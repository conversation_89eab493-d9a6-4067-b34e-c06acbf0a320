import React from 'react'
import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'

export default function Resultat({resultat, setResultat}) {
    return (
        <div>
            <DualContainer>
                <InputText label="Mensuel" value={resultat.salaireMensuel} disabled /> 
                <InputText label="Brut" value={resultat.salaireBrut} disabled />  
            </DualContainer>
            <DualContainer>
                <InputText label="Masse" value={resultat.masseSalariale} disabled /> 
                <InputText label="Total Prorata Grat" value={resultat.totalProrataGrap} disabled /> 
            </DualContainer>          
            <DualContainer>
                <InputText label="Net" value={resultat.netAPayer} disabled /> 
            </DualContainer>  
        </div>
    )
}
