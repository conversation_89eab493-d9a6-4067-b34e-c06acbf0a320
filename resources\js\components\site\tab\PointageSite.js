import React, { useEffect, useState } from 'react'
import InputMonth from '../../input/InputMonth'
import moment from 'moment'
import InputYear from '../../input/InputYear'

import useToken from '../../util/useToken'
import matricule from '../../util/matricule';
import LoadingPage from '../../loading/LoadingPage'

export default function PointageSite({value}) {
    const [month, setMonth] = useState((moment().format("M")).padStart(2, "0"))
    const [year, setYear] = useState(moment().format("YYYY"))
    const [isLoading, toggleLoading] = useState(false)
    const [label, setLabel] = useState("")
    const [pointages, setPointages] = useState([])

    useEffect(() => {
        let isMounted = true;
        toggleLoading(true)
        axios.get('/api/pointage/site/' + value + '?year=' + year + '&month=' + month, useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else if(res.data.pointages){
                    setLabel(
                        "Du " + moment(res.data.begin).format("DD")
                        + (moment(res.data.begin).format("M") != moment(res.data.end).format("M") ? moment(res.data.begin).format(" MMM") : "")
                        + " au " + moment(res.data.end).format("DD MMM YYYY")
                    )
                    setPointages(res.data.pointages)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }, [month, year]);

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <>
                    <div className='line-container'>
                        <div className='header-pointage'>
                            <h3>
                                {label}
                            </h3>
                            <div className='search-pointage'>
                                <div style={{width: 150}}>
                                    <InputMonth selected={month} setSelected={setMonth}/>
                                </div>
                                <div style={{width: 100}}>
                                    <InputYear selected={year} setSelected={setYear}/>
                                </div>
                            </div>
                        </div>
                    </div>
                    {
                        pointages.map(p => 
                            <div key={p.id} className='line-container'>
                                <div>
                                    {
                                        moment(p.date_pointage).format("DD MMM YYYY") 
                                        + " " + (moment(p.date_pointage).format("HH:mm:ss") == "07:00:00" ? "JOUR" : "NUIT")
                                    }
                                </div>
                                <div className='secondary'>
                                    {matricule(p) + ' ' + p.nom}
                                </div>
                            </div>
                        )
                    }
                </>
        }
    </>
}