import axios from 'axios';
import React, { useEffect, useState } from 'react';
import {Link} from 'react-router-dom'
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';

export default function Room() {
    const [nbConfirmation, setNbConfirmation] = useState()

    useEffect(() => {
        let isMounted = true
        axios.get("/api/room", useToken())
        .then((res) => {
            if(isMounted)
                setNbConfirmation(res.data.nb_confirmation)
        })
        return () => {
            isMounted = false
        };
    }, []);

    return <MenuView title="">
        <h3 className='sub-title-menu'>controlroom</h3>
        <div className='palette-container'>
            {
                nbConfirmation > 0 &&
                <div className='palette-item space-between'>
                    <Link className='link-no-style' to="/sanction?status=confirmation">Confirmation sanction</Link>
                    <span className='badge-outline'>{nbConfirmation}</span>
                </div>
            }
            <div className='palette-item'>
                <Link className='link-no-style' to="/sanction/add">Demande de sanction</Link>
            </div>
            <div className='palette-item'>
                <Link className='link-no-style' to="/sav/add">Demande de SAV</Link>
            </div>
            <div className='palette-item'>
                <Link className='link-no-style' to="/fait-marquant">Fait marquant</Link>
            </div>
        </div>
    </MenuView>;
}