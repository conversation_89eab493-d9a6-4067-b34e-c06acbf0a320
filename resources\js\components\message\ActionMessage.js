import React, { useEffect, useState } from 'react';

import { Link, useLocation } from 'react-router-dom';
import NoteModal from '../input/NoteModal';
import axios from 'axios';
import useToken from '../util/useToken';

export default function ActionMessage({ auth, sent, unread, message, updateData, numberFollow, printMessage }) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [currentAction, setAction] = useState(null)
    const [toDone, setToDone] = useState(false);
    const [toRepplyAll, toggleRepplyAll] = useState(false)
    const lastParams = (new URLSearchParams(useLocation().search)).toString()
    
    const handleSeen = () => {
        axios.post('/api/message/seen/' + message.message_id, null, useToken())
        .then((res) => {
            if(res.data.success)
                updateData()
        })
    }
    const unFollow = () => {
        axios.post('/api/message/unfollow/' + message.id, null, useToken())
        .then((res) => {
            if(res.data.success)
                updateData()
        })
    }
    const conditionReplyAll = () => {
        if (sent) {
            if ((numberFollow.nbCopy > 0 && numberFollow.nbReceivers > 0) || numberFollow.nbCopy > 1 || numberFollow.nbReceivers > 1)
                toggleRepplyAll(true);
        }
        else if (message.reply_all)
            toggleRepplyAll(true);
    }

    const handleToDo = (id) => { 
        axios.post('/api/message/handle_to_do', { note_id: message.id, sender_id:message.user_id }, useToken())
        .then((res) => {
            if(res.data.success)
                updateData()
        })
    }
    useEffect(() =>  conditionReplyAll(), [])
    
    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction}
                updateData={() => updateData()}
                closeModal={() => toggleNoteModal(false)}
                toDone={toDone}
                setToDone={setToDone}
            />
        }
        <div className='action-container' style={{textAlign:'end'}}>
            {!message.seen && !sent &&
                <span>
                    <span onClick={()=>handleSeen()} >Bien reçue</span>
                </span>
            }
            <span>
                <Link to={"/message/reply/" +  message.id + '?' + (sent ? "sent=1" : "") + (unread ? "&unread=1" : "") + ((sent ? "&": "") + "last_params=" + encodeURIComponent(lastParams))} >
                    Répondre
                </Link>
            </span>
            {
                toRepplyAll &&
                <span>
                    <Link to={"/message/reply/" + message.id + '?' + 'reply-all=1' + (sent ? "&sent=1" : "") + (unread ? "&unread=1" : "") + ("&last_params=" + encodeURIComponent(lastParams))} >
                        Répondre à tous
                    </Link>
                </span>
            }
            {
                (sent && message.follow) &&
                <span onClick={() => unFollow()}>
                    Ne plus suivre
                </span>
            }
            {
                (!sent) &&
                <span onClick={() => handleToDo(message.id)}>{!message.to_do ? "A faire" : "Effectué"}</span>
            }
            {
                <span onClick={() => printMessage()}>Imprimer</span>
            }
        </div>
    </div>
}