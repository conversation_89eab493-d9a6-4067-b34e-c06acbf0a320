import moment from 'moment';
import React from 'react';
import { CgClose } from 'react-icons/cg' 
import { IoMdArrowBack } from 'react-icons/io';

export default function ShowYearTypeHeader({size, label, data, closeDetail}) {
    return <div className='padding-container'>
        {
            size == "sm" ? 
                <div className='vertical-align'>
                    <div style={{paddingRight: "10px"}} onClick={() => closeDetail()}>
                        <IoMdArrowBack className='secondary' size={26}/>
                    </div>
                    <h2>
                        {label} <span className='secondary'> : {data.type_mouvement == "sortie" ? "OUT" : "IN"}-{moment(data.date_mouvement).format("YYYY")}/{("00000" + data.ref).slice(-6)}</span>
                    </h2>
                </div>
            :
                <div className='space-between'>
                    <h2>
                        <span>Ref : {data.type_mouvement == "sortie" ? "OUT" : "IN"}-{moment(data.date_mouvement).format("YYYY")}/{("00000" + data.ref).slice(-6)}</span>
                    </h2>
                    <CgClose className='secondary' size={30} onClick={() => closeDetail()}/>
                </div>
        }
    </div>
}