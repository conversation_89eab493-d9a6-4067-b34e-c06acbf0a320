const moment = require('moment')
const mysql = require('mysql2')
const nodemailer = require("nodemailer")
const hbs = require('nodemailer-express-handlebars')
const path = require('path')
const Excel = require("exceljs")
const {email_config, sendMail} = require("../auth")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == "task")
const destination_vg = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

const sqlSelectLastVisiteMensuelExport = "SELECT value FROM params p WHERE p.key = 'last_visite_mensuel_export'"

const sqlSelectSuperviseur = "SELECT u.id, u.name, coalesce(ur.email, u.email) as 'email' FROM users u "
    + "left join users ur on ur.id = u.real_email_id "
    + "where u.role in ('superviseur', 'resp_sup') and (u.blocked is null or u.blocked = 0)"

function sqlSelectVisitePoste(dateString){
    const begin = moment(dateString).subtract(1, "month").format('YYYY-MM-DD') + " 06:00:00"
    const end = dateString + " 06:00:00"
	return "SELECT vp.id, vp.user_id, vp.site_id FROM visite_postes vp where vp.date_visite > '" + begin +"' and vp.date_visite <= '" + end +"' "
}
function sqlSelectFaitMarquant(dateString) {
    const begin = moment(dateString).subtract(1, "month").format('YYYY-MM-DD') + " 06:00:00"
    const end = dateString + " 06:00:00"
	return "SELECT f.id, f.user_id, f.site_id FROM fait_marquants f where f.created_at > '" + begin +"' and f.created_at <= '" + end +"' "
}

function sqlSelectSanction(dateString){
    const begin = moment(dateString).subtract(1, "month").format('YYYY-MM-DD') + " 06:00:00"
    const end = dateString + " 06:00:00"
	return "SELECT sanc.id, sanc.user_id, sanc.superviseur_id FROM sanctions sanc "
     + "where sanc.status != 'draft' and sanc.created_at > '" + begin +"' and sanc.created_at <= '" + end +"' "
}

const sqlUpdateLastVisitePosteExport = "UPDATE params p SET p.value = DATE_FORMAT(NOW(), '%Y-%m-%d') " +
		"WHERE p.key = 'last_visite_mensuel_export'"

function generateSuperviseurExcelFile(workbook, header, superviseurs, diff){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }

	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }
    const worksheet = workbook.addWorksheet("Site")


    worksheet.getColumn('A').width = 40
    worksheet.getColumn('B').width = 15
    worksheet.getColumn('C').width = 15
    worksheet.getColumn('D').width = 15
    worksheet.getColumn('E').width = 15
    worksheet.getColumn('F').width = 15
    worksheet.getColumn('G').width = 15

    worksheet.getCell('A1').value = header + " (" + superviseurs.length + " superviseurs)"
    worksheet.getCell('A1').font = fontHeader
    worksheet.mergeCells('A1:F1')

    worksheet.getCell('A2').value = "Nombre de sanctions demandées par les superviseurs : " + superviseurs.map(sup => sup.nb_sanction).reduce((a, b) => a + b, 0)
    worksheet.mergeCells('A2:F2')

    worksheet.getCell('A3').value =  "Nombre de sanctions faites par le RH et controlroom : " + superviseurs.map(sup => sup.nb_sanction_other).reduce((a, b) => a + b, 0)
    worksheet.mergeCells('A3:F3')


    
    worksheet.getCell('A5').value = "Superviseur"
    worksheet.getCell('A5').border = borderStyle
    worksheet.getCell('A5').font = fontBold
    worksheet.getCell('A5').alignment = alignmentStyle
    worksheet.mergeCells('A5:A6')
    
    worksheet.getCell('B5').value = "Nb site"
    worksheet.getCell('B5').border = borderStyle
    worksheet.getCell('B5').font = fontBold
    worksheet.getCell('B5').alignment = alignmentStyle
    worksheet.mergeCells('B5:B6')

    worksheet.getCell('C5').value = "Nb visite"
    worksheet.getCell('C5').border = borderStyle
    worksheet.getCell('C5').font = fontBold
    worksheet.getCell('C5').alignment = alignmentStyle
    worksheet.mergeCells('C5:D5')

    worksheet.getCell('C6').value = "Total"
    worksheet.getCell('C6').border = borderStyle
    worksheet.getCell('C6').font = fontBold
    worksheet.getCell('C6').alignment = alignmentStyle
    worksheet.getCell('D6').value = "Par jour"
    worksheet.getCell('D6').border = borderStyle
    worksheet.getCell('D6').font = fontBold
    worksheet.getCell('D6').alignment = alignmentStyle
    
    worksheet.getCell('E5').value = "Nb sanction agent"
    worksheet.getCell('E5').border = borderStyle
    worksheet.getCell('E5').font = fontBold
    worksheet.getCell('E5').alignment = alignmentStyle
    worksheet.mergeCells('E5:F5')

    worksheet.getCell('E6').value = "Par lui même"
    worksheet.getCell('E6').border = borderStyle
    worksheet.getCell('E6').font = fontBold
    worksheet.getCell('E6').alignment = alignmentStyle

    worksheet.getCell('F6').value = "Par d'autre"
    worksheet.getCell('F6').border = borderStyle
    worksheet.getCell('F6').font = fontBold
    worksheet.getCell('F6').alignment = alignmentStyle

    worksheet.getCell('G5').value = "Fait marquant"
    worksheet.getCell('G5').border = borderStyle
    worksheet.getCell('G5').font = fontBold
    worksheet.getCell('G5').alignment = alignmentStyle
    worksheet.mergeCells('G5:G6')

    let line = 6
    superviseurs.forEach(sup => {
        line++
        worksheet.getCell('A' + line).value = sup.name + " <" + sup.email + ">"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('B' + line).value = sup.nb_site
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).alignment = alignmentStyle
        worksheet.getCell('C' + line).value = sup.nb_visite
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).alignment = alignmentStyle
        worksheet.getCell('D' + line).value = sup.nb_visite ? (sup.nb_visite/diff).toFixed(2) : "0.00"
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).alignment = alignmentStyle
        worksheet.getCell('E' + line).value = sup.nb_sanction ? sup.nb_sanction : "0"
        worksheet.getCell('E' + line).border = borderStyle
        worksheet.getCell('E' + line).alignment = alignmentStyle
        worksheet.getCell('F' + line).value = sup.nb_sanction_other ? sup.nb_sanction_other : ""
        worksheet.getCell('F' + line).border = borderStyle
        worksheet.getCell('F' + line).alignment = alignmentStyle
        worksheet.getCell('G' + line).value = sup.nb_fait
        worksheet.getCell('G' + line).border = borderStyle
        worksheet.getCell('G' + line).alignment = alignmentStyle
    });
}

function doVisitePosteExport(dateString){
	console.log("do visite mensuel")
    pool.query(sqlSelectSuperviseur, [], async (err, superviseurs) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb superviseurs: " + superviseurs.length)
            pool.query(sqlSelectVisitePoste(dateString), [], async (err, visitePostes) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb visite_poste: " + visitePostes.length)
                    pool.query(sqlSelectFaitMarquant(dateString), [], async (err, faitMarquants) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Nb fait_marquant: " + faitMarquants.length)
                            pool.query(sqlSelectSanction(dateString), [], async (err, sanctions) => {
                                if(err)
                                    console.error(err)
                                else {
                                    console.log("Nb sanctions: " + sanctions.length)
                                    superviseurs.forEach(sup => {
                                        sup.nb_visite = 0
                                        sup.nb_fait = 0
                                        sup.nb_sanction = 0
                                        sup.nb_sanction_other = 0
                                        const site_ids = []
                                        visitePostes.forEach(vst => {
                                            if(vst.user_id == sup.id){
                                                sup.nb_visite ++
                                                if(!site_ids.includes(vst.site_id))
                                                    site_ids.push(vst.site_id)
                                            }
                                        })
                                        sup.nb_site = site_ids.length
                                        sanctions.forEach(sanc => {
                                            if(sanc.superviseur_id == sup.id){
                                                if(sanc.user_id == sanc.superviseur_id)
                                                    sup.nb_sanction ++
                                                else
                                                    sup.nb_sanction_other ++
                                            }
                                        })
                                        faitMarquants.forEach(f => {
                                            if(f.user_id == sup.id){
                                                sup.nb_fait ++
                                            }
                                        })
                                    })
                                    superviseurs.sort((a, b) => (a.nb_visite - b.nb_visite))
                                    const workbookSuperviseur = new Excel.Workbook()
                                    const header = "Visite mensuel du "+ moment(dateString).subtract(1, "month").format("DD MMM") + " au " + moment(dateString).format("DD MMM YYYY")
                                    const diff = moment(dateString).diff(moment(dateString).subtract(1, "month"), "days")
                                    generateSuperviseurExcelFile(workbookSuperviseur, header, superviseurs, diff)
                                    const superviseurBuffer = await workbookSuperviseur.xlsx.writeBuffer()
                                    sendMail(
                                        pool,
                                        isTask ? destination_vg : destination_test,
                                        header, 
                                        "Veuillez recevoir le rapport mensuel des activités des superviseurs du "
                                        + moment(dateString).subtract(1, "month").format("DD MMM YYYY") + " au " + moment(dateString).format("DD MMM YYYY") ,
                                        [
                                            {
                                                filename: header + ".xlsx",
                                                content: superviseurBuffer
                                            },
                                        ],
                                        (response) => {
                                            if(response && isTask){
                                                pool.query(sqlUpdateLastVisitePosteExport, [], (e, r) =>{
                                                    if(e)
                                                        console.error(e)
                                                    else
                                                        console.log("update last visite mensuel export: " + r)
                                                    process.exit(1)
                                                })
                                            }
                                            else
                                                process.exit(1)
                                        },
                                        isTask
                                    )
                                }
                            })
                        }
                    })
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2])){
    console.log(process.argv[2])
    doVisitePosteExport(process.argv[2])
}
else if(isTask){
    if(moment().format("DD") == 20 && moment().isAfter(moment().set({hour: 6, minute: 30}))){
        const dateString = moment().format("YYYY-MM-DD")
        pool.query(sqlSelectLastVisiteMensuelExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && moment().format("YYYY-MM-DD") == result[0].value){
                console.log("export visite mensuel already done!")
                process.exit()
            }
            else {
                console.log("exporting ...")
                doVisitePosteExport(dateString)
            }
        })
    }
    else {
        console.log("Not 20 of month, skip export visite mensuel.")
    }
}
else
    console.log("please specify command!")