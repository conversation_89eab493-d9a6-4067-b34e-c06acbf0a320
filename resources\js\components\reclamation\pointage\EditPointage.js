import React, { useEffect, useState } from 'react';
import useToken from '../../util/useToken';
import InputDate from '../../input/InputDate';
import InputSelect from '../../input/InputSelect';
import InputSite from '../../input/InputSite';
import moment from 'moment';
export default function EditPointage({currentPointage, currentReclamId, pointages, setPointages, closeModal, updateData}) {
    const [datePointage, setDatePointage] = useState("")
    const [service, setService] = useState(null)
    const [site, setSite] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(true)

    const handleCancel = (e) => {
        closeModal()
    }

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)

        var formData = new FormData();
        if(datePointage && service && service.value)
            formData.append("date_pointage", moment(datePointage).format("YYYY-MM-DD") + " " + service.value)
        if(site && site.id)
            formData.append("site_id", site.id)
        if(currentReclamId){
            if(currentPointage){
                axios.post("/api/pointage_reclamation/update/" + currentPointage.id, formData, useToken())
                .then((res) => {
                    disableSubmit(false)
                    if(res.data.success){
                        updateData()
                        closeModal()
                    }                
                    else if(res.data.error == "EACCES")
                        setError("Une erreur est survenue.")
                    else if(res.data.error)
                        setError(res.data.error)
                })
                .catch((e) => {
                    console.error(e)
                    setError("Erreur d'envoie, réessayez.")
                    disableSubmit(false)
                })
            }
            else {
                formData.append("reclamation_id", currentReclamId)
                axios.post("/api/pointage_reclamation/add", formData, useToken())
                .then((res) => {
                    disableSubmit(false)
                    if(res.data.success){
                        updateData()
                        closeModal()
                    }                
                    else if(res.data.error == "EACCES")
                        setError("Une erreur est survenue.")
                    else if(res.data.error)
                        setError(res.data.error)
                })
                .catch((e) => {
                    console.error(e)
                    setError("Erreur d'envoie, réessayez.")
                    disableSubmit(false)
                })
            }
        }
        else {
            const newPointage = {
                date_pointage : moment(datePointage).format("YYYY-MM-DD") + " " + service.value,
                site_id : site.id,
                site: site.nom,
            }
            if(currentPointage){
                const newItems = []
                pointages.forEach(item => {
                    if(item.index == currentPointage.index)
                        newItems.push(newPointage)
                    else
                        newItems.push(item)
                });
                setPointages(newItems)
                closeModal()
            }
            else if(!pointages.map(ptg => moment(ptg.date_pointage).format("YYYY-MM-DD HH:mm:ss")).includes(newPointage.date_pointage)){
                const newItems = [...pointages]
                newItems.unshift(newPointage)
                setPointages(newItems)
                closeModal()
            }
            else {
                setError("Date de service déjà sur la liste")
                disableSubmit(false)
            }
        }
    }

    useEffect(() => {
        if(currentPointage){
            setDatePointage(currentPointage.date_pointage ? moment(currentPointage.date_pointage).toDate() : '')
            setService(
                currentPointage.date_pointage ? 
                    {
                        label: moment(currentPointage.date_pointage).format("HH:mm:ss") == "18:00:00" ? "Nuit" : "Jour",
                        value: moment(currentPointage.date_pointage).format("HH:mm:ss")
                    } 
                :
                    null
                )
            setSite({
                id: currentPointage.site_id,
                nom: currentPointage.site
            })
        }
    }, [])

    useEffect(() => {
        disableSubmit(!(site && datePointage && service))

    }, [site, datePointage, service]);

    return (
        <div className='modal'>
            <div style={{maxHeight: 'none'}}>
                <div className="title-container" >
                    <h2>{(currentPointage && currentPointage.index) ? "Modifer le pointage" : "Ajouter une pointage"}</h2>
                </div>
                <form onSubmit={handleSubmit}>
                    <div className='form-body'>
                    <InputDate
                        required
                        value={datePointage}
                        onChange={setDatePointage}/>
                    <InputSelect
                        required
                        label="Service"
                        selected={service} setSelected={setService}
                        options = {[
                            {label: "Jour", value: "07:00:00"},
                            {label: "Nuit", value: "18:00:00"},
                        ]}/>
                    <InputSite
                        required
                        value={site} 
                        onChange={setSite}/>
                    {
                        error &&
                        <div className='container-error'>
                            {error}
                        </div>
                    }
                    </div>
                    <div className="form-button-container">
                        <button className='primary' disabled={submitDisabled} type="submit"> {(currentPointage && currentPointage.index) ? "Modifier" : "Ajouter" }</button>
                        <button className='secondary' onClick={handleCancel}> Annuler </button>
                    </div>
                </form>
            </div>
        </div>
    )
}
