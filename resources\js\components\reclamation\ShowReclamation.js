import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import useToken from '../util/useToken'; 
import matricule from '../util/matricule';        
import Tab from '../layout/Tab';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';
import ActionReclamation from './ActionReclamation';
import moment from 'moment';

export default function ShowReclamation({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [reclamation, setReclamation] = useState()
    const [isLoading, toggleLoading] = useState(false)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/reclamation/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setReclamation(res.data)
                const newUser = []
                if (auth.id != res.data.user_id) {
                    newUser.unshift({ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom })
                }
                setDefautUsers(newUser)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(reclamation)
    }, [reclamation])

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                reclamation &&
                <>
                    <ShowHeader size={size} label="Reclamation" id={reclamation.id} closeDetail={() => setCurrentId()}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + reclamation.status_color}>
                                    {reclamation.status_description}
                                </span> {
                                    reclamation.nb_pj > 0 &&
                                    <span className='badge-outline'>
                                        Pièce jointe : {reclamation.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {reclamation.employe_id ?  (matricule(reclamation) + ' ' + reclamation.employe): "SM - " + reclamation.agent_not_registered} {} 
                        </h3>

                        <p>
                            Service : <span className='text'>{moment(reclamation.date_pointage).format('DD MMM YYYY') + ' ' + (moment(reclamation.date_pointage).format('HH:mm') == "18:00" ? "NUIT" : "JOUR")}</span>
                        </p>
                        <p>
                            Type : <span className='text'>{reclamation.type == 'sm' ? "Agent non enregistré" : reclamation.type == "service24" ? "Manque de demande service 24" : reclamation.type == "archive" ? "Agent archivé" : reclamation.type == "mis_a_pied" ? "Mis à pied" : ""}</span>
                        </p>
                        <div>
                            Superviseur : <span className='text'> 
                                {reclamation.user_nom} {' <' + reclamation.user_email + '>'}
                            </span>
                        </div>
                        <div className='card-action'>
                            <ActionReclamation auth={auth} reclamation={reclamation} updateData={updateData} toggleLoading={toggleLoading}/>
                        </div>
                    </div>
                        <Tab auth={auth} data={reclamation} name="reclamation_id" value={reclamation.id} updateData={updateData} defautUsers={defautUsers} />
                </>
            }
        </div>
    }</>
}