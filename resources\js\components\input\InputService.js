import {useState,useRef, useEffect} from 'react'

import useClickOutside from '../util/useClickOutside'
import axios from 'axios'
import useToken from '../util/useToken'

export default function InputService({currentSelect, setCurrentSelect,required}) {
    const [showSelect, toggleSelect] = useState(false)
    const [label, setLabel] = useState("")
    const [services, setServices] = useState([]);
    const selectRef = useRef(null)

    useClickOutside(selectRef, () => {
        toggleSelect(false)
        if(currentSelect && currentSelect.designation)
            setLabel(currentSelect.designation)
        else
            setLabel("")
    })

    useEffect(() => {
        setLabel(currentSelect ? currentSelect.designation: "")
    }, [currentSelect])
    
    useEffect(() => {
        let isMounted = true
        axios.get("/api/service", useToken())
        .then((res) => {
            if(isMounted){
                setServices(res.data)
            }
        })
        return () => {isMounted = false}
    }, [])

    return (
        <div ref={selectRef} className='input-container'>
            <label>Service {required && <span className='danger'>*</span>}</label>
            <input className='select-search' 
                onClick={() => toggleSelect(!showSelect)} 
                value={label} 
                onChange={(e) => {
                    setLabel(e.target.value)
                    toggleSelect(true)
                    if(!e.target.value) setCurrentSelect(null)
                }}/>
            <div className='input-select-relative'>
                {
                    showSelect && 
                    <div className='select-list'>
                        {
                            services.filter(sc => (new RegExp(label.toLowerCase(), 'g')).test(sc.designation.toLowerCase()))
                            .map((sc, index) => (
                                <div 
                                    key={index}
                                    className="select-item"
                                    onClick={(e) => {
                                        setCurrentSelect(sc)
                                        toggleSelect(false)
                                        setLabel(sc.designation)
                                    }}
                                >
                                    {sc.designation}
                                </div>
                        ))
                        }
                        
                    </div>
                }
            </div>
        </div>
    )
}
