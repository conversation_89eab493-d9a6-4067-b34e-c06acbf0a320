import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Link, Navigate } from 'react-router-dom';
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';
import LoadingPage from '../loading/LoadingPage';

export default function JuridiqueHome({auth}) {
    const [nbData, setData] = useState(null) 
    const [isLoading, toggleLoading] = useState(false);

    useEffect(() => {
        toggleLoading(true) 
        let isMounted = true
        axios.get('/api/juridique_role', useToken())
        .then(res => {
            setData(res.data)
            toggleLoading(false)
        })
        .catch(err => {
            console.error(err)
        })
        return () => { isMounted = false }
    }, [])
    return (
        auth.role == "juridique" ?
            (
                isLoading ?
                    <LoadingPage/>
                :
                    <MenuView title="">
                        {
                            nbData && 
                            (
                                (nbData.nb_plainte > 0 || nbData.nb_recouvrement) ?
                                    <div>
                                        <h3 className='sub-title-menu'>JURIDIQUE</h3>
                                        <div>
                                            <div className='palette-container'>                                
                                                {
                                                    nbData.nb_plainte != 0 &&
                                                    <div className='palette-item'>
                                                        <Link className='link-no-style' to="/plainte/unread">Plainte non lu</Link>
                                                        <span className='badge-outline'>{nbData.nb_plainte}</span>
                                                    </div>
                                                }   
                                                {
                                                    nbData.nb_recouvrement != 0 &&
                                                    <div className='palette-item'>
                                                        <Link className='link-no-style' to="/recouvrement/unread">Recouvrement non lu</Link>
                                                        <span className='badge-outline'>{nbData.nb_recouvrement}</span>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                :
                                    <div className='center secondary'>
                                        <br/>
                                        <br/>
                                        <h3>Aucun nouvel élément pour l'instant</h3>
                                    </div>
                            )
                        }
                    </MenuView>
            )
        :
            <Navigate to="/"/>
    );
}