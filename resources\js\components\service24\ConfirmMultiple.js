import React, { useEffect, useState } from 'react'
import axios from 'axios'
import useToken from '../util/useToken'
import LoadingPage from '../loading/LoadingPage'
import { useLocation } from 'react-router-dom'

export default function ConfirmMultiple({ closeModal, updateData }) {
    const [service24s, setService24s] = useState([])
    const [doneButton, setDoneButton] = useState(false)
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const locationSearch = useLocation().search
    const [error, setError] = useState('')

    const onSearch = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true);
            setDataLoaded(true);
            params.set("offset", 0);
        } else params.set("offset", service24s.length);
        axios.get('/api/service24/get_data_validation?' + params, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.error)
                        console.error(res.data.error);
                    else {
                        if (initial)
                            setService24s(res.data.services);
                        else {
                            const list = service24s.slice().concat(res.data.services);
                            setService24s(list);
                        }
                        setDataLoaded(res.data.services.length < 30);
                    }
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e);
            })
        return () => {
            isMounted = false;
        }
           }

    useEffect(() => {
        service24s.length > 0 ? setDoneButton(true) : setDoneButton(false)
    }, [service24s])
    
    
    const onConfirm = () => { 
        if (service24s.length > 0) {
            setError("")
            
            // const ids = service24s.map(s => s.id)
            const data = new FormData();
            // data.append("ids", ids)
            data.append("note", "Ok")
            axios.post("/api/service24/done_multiple", data, useToken())
            .then((res) => {
                console.log(res.data)
                if (res.data.success) {
                    closeModal()
                    updateData(true)
                }
                else if (res.data.error) {
                    setError(res.data.error)
                }
            })
            .catch((e) => {
                console.error(e) 
                setError("Erreur d'envoie, réessayez.")
            })
        }
    }

    useEffect(() => {
        onSearch(true)
    }, [])

    const fetchMoreData = () => { 
        setTimeout(() => onSearch(), 300);
    }

    return (
        <div className='modal'>
            <div>
                <h2>Confirmation par lot</h2>
                {isLoading ?
                    <LoadingPage />
                :
                    service24s.length == 0 ?
                        <h3 className="center secondary">Aucun données trouvé</h3>
                    :
                        <h3 className="secondary">{service24s.length} services 24 à valider</h3>
                        // <div id="scrollableList">
                        //     <InfiniteScroll
                        //         dataLength={service24s.length}
                        //         next={fetchMoreData}
                        //         hasMore={!allDataLoaded}
                        //         loader={<LoadingPage />}
                        //         scrollableTarget="scrollableList"
                        //     >
                        //         <div className='list-container'>
                        //             <ul>
                        //                 {service24s.map(pv => {
                        //                     return (
                        //                         <li key={pv.id}>
                        //                             <span className='secondary'>
                        //                                 {"[ " + matricule(pv)  + " ] - " +  " " + pv.employe}
                        //                             </span>
                        //                         </li>
                        //                     )
                        //                 })}
                        //             </ul>
                        //         </div>
                        //     </InfiniteScroll>
                        // </div>
                }
                {
                    error &&
                    <div className='container-error'>
                        {error}
                    </div>
                }
                <div className='form-button-container'>
                    {doneButton && <button className='btn btn-primary' onClick={()=> onConfirm()} >Confirmer</button>}
                    <button className='btn' onClick={() => closeModal()}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
