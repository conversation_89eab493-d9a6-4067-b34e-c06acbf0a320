import React,{useEffect, useState} from 'react';
import ExcelJS from 'exceljs';
import moment from 'moment';
import { upperCase } from 'lodash';
import matricule from '../../util/matricule';

export default function ExcelPaie({paie, datePaie}) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Paie');
    const [dataPaie, setDataPaie] = useState(null);
    workbook.creator = 'ADMIN';

    useEffect(()=>{
        reformeData();
    },[paie]);
    const reformeData = () => {
        let dataCurrent = [];
        for (let i = 0; i < paie.length; i++) {
            let currentPaie = paie[i];
            let matr = matricule(currentPaie)
            let numeroCompte = '';
            if (currentPaie.code_banque && currentPaie.code_guichet && currentPaie.numero_compte && currentPaie.rib) {
                numeroCompte = (currentPaie.code_banque).toString() + '' + currentPaie.code_guichet.toString() + '' + currentPaie.numero_compte.toString() + '' + currentPaie.rib.toString();
            }
            dataCurrent.push({
                "MAT": matr,
                "Nom": currentPaie.employe,
                "Fonction": currentPaie.fonction_designation,
                "Heures contrat": currentPaie.nb_heure_contrat,
                "Heure convenue": currentPaie.nb_heure_convenu,
                "Heure Travaillé": currentPaie.nb_heure_travaille,
                "Diff HC/HT": 0, "Class": currentPaie.categorie,
                "AGC": currentPaie.agence,
                "Date d'embauche": currentPaie.date_embauche,
                "SalBase et Majorations": currentPaie.sal_base,
                "Sal Mens": currentPaie.salaire_mensuel,
                "HSFR": 0,
                "HS30%": 0,
                "MH.S 30%": 0,
                "HS50%": 0,
                "MH.S 50%": 0,
                "H Ferié": 0,
                "Mmajferié": 0,
                "HMdim": 0,
                "MMajdim": 0,
                "Hmaj": 0,
                "Mmaj": 0,
                "Tot Maj": 0,
                "Prime Exceptionnelle": currentPaie.prime_exceptionnelle,
                "Prime except prorata": 0, 
                "Prime div": currentPaie.prime_div,
                "Prime div prorata": 0,
                "Idm. Depl": currentPaie.idm_depl,
                "Idm. Depl prorata": 0,
                "Prime Assid": currentPaie.prime_assid,
                "Prime Assid prorata": 0,
                "Prime Resp.": currentPaie.prime_resp,
                "prime resp. prorata": 0,
                "Prime Entret": currentPaie.prime_entret,
                "Prime entret prorata": 0,
                "Prime d'ancienneté": currentPaie.prime_anc,
                "Prime d'ancienneté prorata": 0,
                "Tot.Pr&Grat": 0, "Rappel": currentPaie.rappel,
                "Mbrut": currentPaie.mbrut, 
                "SCongé": currentPaie.s_conge,
                "Cpayé": 0,
                "Nprv deduct": currentPaie.nprv_preavis_deductible,
                "Preavis deductible": 0,
                "Nprv previs payer": currentPaie.nprv_preavis_payer,
                "Previs payer": 0,
                "Nprv licenciement": currentPaie.nprv_licenciement,
                "Indemnite de licenciement": 0,
                "Salaire brut": currentPaie.salaire_brut,
                "SALFA": currentPaie.salfa,
                "CNAPS": currentPaie.cnaps,
                "Net Imp": currentPaie.net_imposable,
                "IRSA": currentPaie.irsa,
                "DED IMPOT": currentPaie.ded_impot ? currentPaie.ded_impot : 0,
                "Retenue pour la formation": currentPaie.retenue_formation,
                "Autre deduction": currentPaie.autre_deduction,
                "Avance Special": currentPaie.avance_special,
                "Avance 15e": currentPaie.avance_15e,
                "Preavis Moins": currentPaie.preavis_moins,
                "All Fam Cnaps": currentPaie.all_fam_cnaps,
                "Remb frais fixe": currentPaie.remb_frais_fixe,
                "Avance speciale embauche": currentPaie.avance_speciale_embauche, 
                "Prime": currentPaie.prime,
                "Perdiem": currentPaie.perdiem,
                "Perdiem prorata": 0,
                "Part Variable": currentPaie.part_variable, 
                "NET A PAYER": currentPaie.net_a_payer,
                "Salfa Patronal": currentPaie.salfa_pat,
                "CNAPS PATRONAL": currentPaie.cnaps_pat,
                "IRSA PAT": currentPaie.irsa_pat,
                "MASSE SALARIALE": currentPaie.masse_salariale,
                "MODE DE PAIEMENT": currentPaie.mode_payement,
                "banque": currentPaie.banque,
                "Numero Tel": currentPaie.numero_tel,
                "Num de compte": numeroCompte,
                "Code Banque": currentPaie.code_banque,
                "Code Guichet": currentPaie.code_guichet,
                "Numero de compte": currentPaie.numero_compte,
                "Rib": currentPaie.rib,
                "CIN": currentPaie.cin            
            })
          
        }
        setDataPaie(dataCurrent);
    }

    const excel = () => {
        const headers = Object.keys(dataPaie[0]);
        worksheet.addRow(headers);
        if (paie){ dataPaie.forEach((data) => {
            const values = headers.map((header) => data[header]);
            worksheet.addRow(values);
        });
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        worksheet.getColumn('G').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1)  {
                const contratCell = worksheet.getCell(`D${rowNumber}`);
                const travailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = { formula: `${travailleCell.address} - ${contratCell.address}` };
            }
        });
        worksheet.getColumn('L').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const diffHCHTCell = worksheet.getCell(`G${rowNumber}`);
                const salBaseCell = worksheet.getCell(`K${rowNumber}`);
                const heureTravailleCell = worksheet.getCell(`F${rowNumber}`);
                const heureContratCell = worksheet.getCell(`D${rowNumber}`);
                cell.value = { formula: `ROUND(IF(${diffHCHTCell.address} = -12, ${salBaseCell.address}, IF(${heureTravailleCell.address} > ${heureContratCell.address}, ${salBaseCell.address}, (${salBaseCell.address} / ${heureContratCell.address} * ${heureTravailleCell.address}))) ,2)` };
            }
        })
        worksheet.getColumn('M').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const heureTravailleCell = worksheet.getCell(`F${rowNumber}`);
                const heureContratCell = worksheet.getCell(`D${rowNumber}`);
                cell.value = { formula: `IF(${heureTravailleCell.address} > ${heureContratCell.address}, ${heureTravailleCell.address} - ${heureContratCell.address}, 0)` };
            }
        })
        worksheet.getColumn('M').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const heureTravailleCell = worksheet.getCell(`F${rowNumber}`);
                const heureContratCell = worksheet.getCell(`D${rowNumber}`);
                cell.value = { formula: `IF(${heureTravailleCell.address} > ${heureContratCell.address}, ${heureTravailleCell.address} - ${heureContratCell.address}, 0)` };
            }
        })
        worksheet.getColumn('N').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const HsfrCell = worksheet.getCell(`M${rowNumber}`);
                cell.value = { formula: `IF(${HsfrCell.address} > 33.6, 33.6,${HsfrCell.address} )` };
            }
        })
        worksheet.getColumn('O').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const heureContratCell = worksheet.getCell(`D${rowNumber}`);
                const Hs30Cell = worksheet.getCell(`N${rowNumber}`);
                const salBaseCell = worksheet.getCell(`K${rowNumber}`);
                cell.value = { formula: `ROUND((${salBaseCell.address}/ ${heureContratCell.address} * ${Hs30Cell.address} *0.3) ,2)` };
            }
        })
        worksheet.getColumn('P').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const HsfrCell = worksheet.getCell(`M${rowNumber}`);
                const Hs30Cell = worksheet.getCell(`N${rowNumber}`);
                cell.value = { formula: `(${HsfrCell.address}-${Hs30Cell.address})` };
            }
        })
        worksheet.getColumn('Q').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const salBaseCell = worksheet.getCell(`K${rowNumber}`);
                const heureContratCell = worksheet.getCell(`D${rowNumber}`);
                const Hs50Cell = worksheet.getCell(`P${rowNumber}`);
                cell.value = { formula: `ROUND((${salBaseCell.address}/${heureContratCell.address}*${Hs50Cell.address}*0.5) ,2)` };
            }
        })
        worksheet.getColumn('S').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const salBaseCell = worksheet.getCell(`K${rowNumber}`);
                const heureContratCell = worksheet.getCell(`D${rowNumber}`);
                const hFerieCell = worksheet.getCell(`R${rowNumber}`);
                cell.value = { formula: `(${salBaseCell.address}/${heureContratCell.address}*${hFerieCell.address}*1)` };
            }
        })
        worksheet.getColumn('U').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const salBaseCell = worksheet.getCell(`K${rowNumber}`);
                const heureContratCell = worksheet.getCell(`D${rowNumber}`);
                const hmDim = worksheet.getCell(`T${rowNumber}`);
                cell.value = { formula: `(${salBaseCell.address}/${heureContratCell.address}*${hmDim.address}*0.4)` };
            }
        })
        worksheet.getColumn('W').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const hMajCell = worksheet.getCell(`V${rowNumber}`);
                const heureContratCell = worksheet.getCell(`D${rowNumber}`);
                const heureTravailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = { formula: `(${hMajCell.address}/${heureContratCell.address}*${heureTravailleCell.address})` };
            }
        })
        worksheet.getColumn('X').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const mhs30Cell = worksheet.getCell(`O${rowNumber}`);
                const mhs50Cell = worksheet.getCell(`Q${rowNumber}`);
                const mmajDimCell = worksheet.getCell(`U${rowNumber}`);
                const mmajCell = worksheet.getCell(`W${rowNumber}`);
                const mmajFerieCell = worksheet.getCell(`W${rowNumber}`);
                cell.value = { formula: `ROUND((${mhs30Cell.address}+${mhs50Cell.address}+${mmajDimCell.address}+${mmajCell.address}+${mmajFerieCell.address}) ,2)` };
            }
        })
        worksheet.getColumn('Z').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber!= 1)  {
                const diffHCHTCell = worksheet.getCell(`G${rowNumber}`);
                const prExceptionnelleCell = worksheet.getCell(`Y${rowNumber}`);
                const contratCell = worksheet.getCell(`D${rowNumber}`);
                const travailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = { formula: `ROUND(IF(${diffHCHTCell.address}=0,${prExceptionnelleCell.address},IF(${travailleCell.address}>${contratCell.address},${prExceptionnelleCell.address},IF(${diffHCHTCell.address}=-12, ${prExceptionnelleCell.address},( ${prExceptionnelleCell.address}/${contratCell.address}*${travailleCell.address})))),2)` };
            }
        })
        worksheet.getColumn('AB').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const diffHCHTCell = worksheet.getCell(`G${rowNumber}`);
                const prDivCell = worksheet.getCell(`AA${rowNumber}`);
                const contratCell = worksheet.getCell(`D${rowNumber}`);
                const travailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = {
                    formula: `ROUND(IF(${diffHCHTCell.address}=0,${prDivCell.address},IF(${travailleCell.address}>${contratCell.address},${prDivCell.address},IF(${diffHCHTCell.address}=-12, ${prDivCell.address},( ${prDivCell.address}/${contratCell.address}*${travailleCell.address})))),2)`
                };
            }
        })
        worksheet.getColumn('AD').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const diffHCHTCell = worksheet.getCell(`G${rowNumber}`);
                const idmDeplCell = worksheet.getCell(`AC${rowNumber}`);
                const contratCell = worksheet.getCell(`D${rowNumber}`);
                const travailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = {
                    formula: `ROUND(IF(${diffHCHTCell.address}=0,${idmDeplCell.address},IF(${travailleCell.address}>${contratCell.address},${idmDeplCell.address},IF(${diffHCHTCell.address}=-12, ${idmDeplCell.address},( ${idmDeplCell.address}/${contratCell.address}*${travailleCell.address})))),2)`
                };
            }
        })
        worksheet.getColumn('AF').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const diffHCHTCell = worksheet.getCell(`G${rowNumber}`);
                const prAssidCell = worksheet.getCell(`AE${rowNumber}`);
                const contratCell = worksheet.getCell(`D${rowNumber}`);
                const travailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = {
                    formula: `ROUND(IF(${diffHCHTCell.address}=0,${prAssidCell.address},IF(${travailleCell.address}>${contratCell.address},${prAssidCell.address},IF(${diffHCHTCell.address}=-12, ${prAssidCell.address},( ${prAssidCell.address}/${contratCell.address}*${travailleCell.address})))),2)`
                };
            }
        })
        worksheet.getColumn('AH').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const diffHCHTCell = worksheet.getCell(`G${rowNumber}`);
                const prRespCell = worksheet.getCell(`AG${rowNumber}`);
                const contratCell = worksheet.getCell(`D${rowNumber}`);
                const travailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = {
                    formula: `ROUND(IF(${diffHCHTCell.address}=0,${prRespCell.address},IF(${travailleCell.address}>${contratCell.address},${prRespCell.address},IF(${diffHCHTCell.address}=-12, ${prRespCell.address},( ${prRespCell.address}/${contratCell.address}*${travailleCell.address})))),2)`
                };
            }
        })
        worksheet.getColumn('AJ').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const diffHCHTCell = worksheet.getCell(`G${rowNumber}`);
                const prEntretCell = worksheet.getCell(`AI${rowNumber}`);
                const contratCell = worksheet.getCell(`D${rowNumber}`);
                const travailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = {
                    formula: `ROUND(IF(${diffHCHTCell.address}=0,${prEntretCell.address},IF(${contratCell.address}>${travailleCell.address}, ${prEntretCell.address},IF(${diffHCHTCell.address}=-12, ${prEntretCell.address},( ${prEntretCell.address}/${contratCell.address}*${travailleCell.address})))),2)`
                };
            }
        })
        worksheet.getColumn('AL').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const diffHCHTCell = worksheet.getCell(`G${rowNumber}`);
                const prAncienneteCell = worksheet.getCell(`AK${rowNumber}`);
                const contratCell = worksheet.getCell(`D${rowNumber}`);
                const travailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = {
                    formula: `ROUND(IF(${diffHCHTCell.address}=0,${prAncienneteCell.address},IF(${travailleCell.address}>${contratCell.address},${prAncienneteCell.address},IF(${diffHCHTCell.address}=-12, ${prAncienneteCell.address},( ${prAncienneteCell.address}/${contratCell.address}*${travailleCell.address})))),2)`
                };
            }
        })
        worksheet.getColumn('AM').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const exceptionnelProrataCell = worksheet.getCell(`Z${rowNumber}`);
                const divProrataCell = worksheet.getCell(`AB${rowNumber}`);
                const deplProrataCell = worksheet.getCell(`AD${rowNumber}`);
                const AssidProrataCell = worksheet.getCell(`AF${rowNumber}`);
                const RespProrataCell = worksheet.getCell(`AH${rowNumber}`);
                const EntretProrataCell = worksheet.getCell(`AJ${rowNumber}`);
                const AncProrataCell = worksheet.getCell(`AL${rowNumber}`);
                cell.value = {
                    formula: `ROUND((${exceptionnelProrataCell.address}+${divProrataCell.address}+${deplProrataCell.address}+${AssidProrataCell.address}+${RespProrataCell.address}+${EntretProrataCell.address}+${AncProrataCell.address}),2)`
                };
            }
        })
        worksheet.getColumn('AQ').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const ScongeCell = worksheet.getCell(`AP${rowNumber}`);
                const SalBaseCell = worksheet.getCell(`K${rowNumber}`);
                cell.value = {
                    formula: `ROUND((${SalBaseCell.address}/30)*${ScongeCell.address}, 2)`
                };
            }
        })
        worksheet.getColumn('AS').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const nprvCell = worksheet.getCell(`AR${rowNumber}`);
                const SalBaseCell = worksheet.getCell(`K${rowNumber}`);
                cell.value = {
                    formula: `(${SalBaseCell.address}/30)*${nprvCell.address}`
                };
            }
        })
        worksheet.getColumn('AU').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const nprvCell = worksheet.getCell(`AT${rowNumber}`);
                const SalBaseCell = worksheet.getCell(`K${rowNumber}`);
                cell.value = {
                    formula: `(${SalBaseCell.address}/30)*${nprvCell.address}`
                };
            }
        })
        worksheet.getColumn('AW').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const nprvCell = worksheet.getCell(`AV${rowNumber}`);
                const SalBaseCell = worksheet.getCell(`K${rowNumber}`);
                cell.value = {
                    formula: `(${SalBaseCell.address}/30)*${nprvCell.address}`
                };
            }
        })
        worksheet.getColumn('AX').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const salMens = worksheet.getCell(`L${rowNumber}`);
                const totMaj = worksheet.getCell(`X${rowNumber}`);
                const totProrataGrat = worksheet.getCell(`AM${rowNumber}`);
                const rappel = worksheet.getCell(`AN${rowNumber}`);
                const cPaye = worksheet.getCell(`AQ${rowNumber}`);
                const prDeductible = worksheet.getCell(`AS${rowNumber}`);
                const prPayer = worksheet.getCell(`AU${rowNumber}`);
                const prLicenciement = worksheet.getCell(`AW${rowNumber}`);
                cell.value = {
                    formula: `ROUND((+${salMens.address}+${totMaj.address}+${totProrataGrat.address}+${rappel.address}+${cPaye.address}-${prDeductible.address}+${prPayer.address}+${prLicenciement.address}), 2)`
                };
            }
        })
        worksheet.getColumn('AY').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const salBrut = worksheet.getCell(`AX${rowNumber}`);
                cell.value = {
                    formula: `ROUND((${salBrut.address})*1/100 ,2)`
                };
            }
        })
        worksheet.getColumn('AZ').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const salBrut = worksheet.getCell(`AX${rowNumber}`);
                cell.value = {
                    formula: `ROUND((${salBrut.address})*1/100, 2)`
                };
            }
        })
        worksheet.getColumn('BA').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const salBrut = worksheet.getCell(`AX${rowNumber}`);
                const salfa = worksheet.getCell(`AY${rowNumber}`);
                const cnaps = worksheet.getCell(`AZ${rowNumber}`);
                cell.value = {
                    formula: `ROUND(${salBrut.address}-${salfa.address}-${cnaps.address} ,2)`
                };
            }
        })
        worksheet.getColumn('AJ').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const diffHCHTCell = worksheet.getCell(`G${rowNumber}`);
                const entret = worksheet.getCell(`AI${rowNumber}`);
                const contratCell = worksheet.getCell(`D${rowNumber}`);
                const travailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = {
                    formula: `ROUND(IF(${diffHCHTCell.address}=0,${entret.address},IF(${travailleCell.address}>${contratCell.address}, ${entret.address},IF(${diffHCHTCell.address}=-12, ${entret.address},( ${entret.address}/${contratCell.address}*${travailleCell.address})))) ,2)`
                };
            }
        })
        worksheet.getColumn('BP').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const netImp = worksheet.getCell(`BA${rowNumber}`);
                const irsa = worksheet.getCell(`BB${rowNumber}`);
                const retenueFormation = worksheet.getCell(`BD${rowNumber}`);
                const autreDeduction = worksheet.getCell(`BE${rowNumber}`);
                const avanceSpecial = worksheet.getCell(`BF${rowNumber}`);
                const avance15 = worksheet.getCell(`BG${rowNumber}`);
                const prMoins = worksheet.getCell(`BH${rowNumber}`);
                const allFamCnaps = worksheet.getCell(`BI${rowNumber}`);
                const rembFraisFixe = worksheet.getCell(`BJ${rowNumber}`);
                const avcSpecAmbauche = worksheet.getCell(`BK${rowNumber}`);
                const prime = worksheet.getCell(`BL${rowNumber}`);
                const perdiemProrata = worksheet.getCell(`BN${rowNumber}`);
                const partVariable = worksheet.getCell(`BO${rowNumber}`);

                cell.value = {
                    formula: `ROUND(${netImp.address}-${irsa.address}-${retenueFormation.address}-${autreDeduction.address}-${avanceSpecial.address}-${avance15.address}-${prMoins.address}+${allFamCnaps.address}+${rembFraisFixe.address}-${avcSpecAmbauche.address}+${prime.address}+${perdiemProrata.address}+${partVariable.address} ,2)`
                };
            }
        })
        worksheet.getColumn('BN').eachCell({ includeEmpty: false }, (cell, rowNumber) => { 
            if (rowNumber != 1) {
                const diffHCHTCell = worksheet.getCell(`G${rowNumber}`);
                const perdiemCell = worksheet.getCell(`BM${rowNumber}`);
                const contratCell = worksheet.getCell(`D${rowNumber}`);
                const travailleCell = worksheet.getCell(`F${rowNumber}`);
                cell.value = {
                    formula: `ROUND(IF(${diffHCHTCell.address}=0,${perdiemCell.address},IF(${travailleCell.address}>${contratCell.address}, ${perdiemCell.address},IF(${diffHCHTCell.address}=-12, ${perdiemCell.address},( ${perdiemCell.address}/${contratCell.address}*${travailleCell.address})))) ,2)`
                };
            }
        })
        worksheet.getColumn('BQ').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const netPayer = worksheet.getCell(`BP${rowNumber}`);
                const avance15e = worksheet.getCell(`BG${rowNumber}`);
                cell.value = {
                    formula: `ROUND((${netPayer.address}+${avance15e.address})*5/100 ,2)`
                };
            }
        })
        worksheet.getColumn('BR').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const netPayer = worksheet.getCell(`BP${rowNumber}`);
                const avance15e = worksheet.getCell(`BG${rowNumber}`);
                cell.value = {
                    formula: `ROUND((${netPayer.address}+${avance15e.address})*13/100, 2)`
                };
            }
        })
        
        worksheet.getColumn('BT').eachCell({ includeEmpty: false }, (cell, rowNumber) => {
            if (rowNumber != 1) {
                const netPayer = worksheet.getCell(`BP${rowNumber}`);
                const salfaPat = worksheet.getCell(`BQ${rowNumber}`);
                const cnapsPat = worksheet.getCell(`BR${rowNumber}`);
                const irsaPat = worksheet.getCell(`BS${rowNumber}`);
                const salfa = worksheet.getCell(`AY${rowNumber}`);
                const cnaps = worksheet.getCell(`AZ${rowNumber}`);
                const irsa = worksheet.getCell(`BB${rowNumber}`);
                const avance15 = worksheet.getCell(`BG${rowNumber}`);
                cell.value = {
                    formula: `ROUND((${netPayer.address}+${salfaPat.address}+${cnapsPat.address}+${irsaPat.address}+${salfa.address}+${cnaps.address}+${irsa.address}+${avance15.address}) ,2)`
                };
            }
        })

        worksheet.columns.forEach((column) => {
            column.width = 15;
        });
        worksheet.getColumn('A').width=16
        worksheet.getColumn('B').width=60
        worksheet.getColumn('C').width=20
        worksheet.getColumn('BT').width=30
        worksheet.views = [
            {
                state: 'frozen',
                xSplit: 1,
                ySplit: 1,
            },
        ];
        const backGroudColor1 ='FF8cb812';
        const backGroudColor2 ='FF177331';

        const defineBgColors = {
            O:backGroudColor1,
            Q:backGroudColor1,
            S:backGroudColor1,
            U:backGroudColor1,
            W:backGroudColor1,
            X:backGroudColor2,
            AM:backGroudColor2,
            AQ:backGroudColor2,
            AS:backGroudColor2,
            AU:backGroudColor2,
            AW:backGroudColor2,
        }
        worksheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
            Object.keys(defineBgColors).forEach((column) => {
                const cell = row.getCell(column);
                const backgroundColor = defineBgColors[column];
                if (backgroundColor) {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: backgroundColor },
                    };
                }
            });
        });

        }

    }

    const onSubmit = () => {
        workbook.xlsx.writeBuffer().then((buffer) => {
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = "PAIE_"+upperCase(moment(datePaie).format("MMM YYYY"))+'.xlsx';
            a.click();
    });}

    return <button className="btn btn-primary" onClick={() => {excel(), onSubmit()}}>
            Exporter
        </button>
}
