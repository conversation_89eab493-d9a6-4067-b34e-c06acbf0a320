import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import LoadingPage from '../loading/LoadingPage';
import InfiniteScroll from 'react-infinite-scroll-component';
import StatusLabel from '../input/StatusLabel';

export default function User({auth, users, setUsers, currentId, setCurrentId}) {
    const locationSearch = useLocation().search;
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'Utilisateur', name: 'account', type:'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Type d\'utilisateur', name: 'type_user', type:'string'},
        {label: 'Matricule ', name: 'matricule', type:'number'},
    ]

    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            params.set("offset", 0)
        }
        else
            params.set("offset", users.length)
        axios.get('/api/user'
            + '?' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setUsers(res.data.users)
                    else {
                        const list = users.slice().concat(res.data.users)
                        setUsers(list)
                    }
                    setDataLoaded(res.data.users.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {updateData(true)}, [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>
                    Utilisateurs
                </h2>
                {   
                    ['admin'].includes(auth.role) && 
                    <Link className='btn btn-primary' to="/user/add">Nouveau utilisateur</Link>
                }
            </div>
            <SearchBar listItems={searchItems}/>
            {
                users.length == 0 ?
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                    <InfiniteScroll
                        dataLength={users.length}
                        next={fetchMoreData}
                        hasMore={!allDataLoaded}
                        loader={<LoadingPage/>}
                    >
                        <div className="line-container ">
                            <div className='row-employe'>
                                <b className='line-cell-lg'>Nom</b>
                                <b className='line-cell-sm'>Rôle</b>
                                <b className="status-line"><StatusLabel color="grey"/></b>
                                <b>Email</b>
                            </div>
                        </div>
                        {
                            users.map((u) => (
                                <div className={`line-container ${currentId && currentId == u.id ? 'selected' : ''}`} key={u.id}>
                                    <div className='row-employe'  onClick={() => setCurrentId(u.id)}>
                                        <span className='line-cell-lg'>{u.name}</span>
                                        <span className='line-cell-sm'>{u.role}</span>
                                        <span className="status-line">
                                            <StatusLabel color={u.blocked ? "pink" : u.type == "fictif" ? "purple" : u.type == "unique" ? "green" : "grey"}/>
                                        </span>
                                        <span>{u.email}</span>
                                    </div>
                                </div>
                            ))
                        }
                    </InfiniteScroll>
            }
        </div>
    } </>
}