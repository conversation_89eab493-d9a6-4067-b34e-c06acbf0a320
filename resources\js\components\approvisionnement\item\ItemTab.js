import { useEffect, useState } from 'react';
import LoadingPage from '../../loading/LoadingPage';
import useToken from '../../util/useToken';
import Item from './Item';

export default function ItemTab({approId, auth, updateData}) {
    const [isLoading, toggleLoading] = useState(false)
    const [items, setItems] = useState([])
    const [currentAppro, setCurrentAppro] = useState(null)

    useEffect(() => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/approvisionnement/article/' + approId, useToken())
        .then((res) => {
            if(isMounted){
                setCurrentAppro(res.data.approvisionnement)
                setItems(res.data.da_items)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }, [approId])

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <Item auth={auth} currentAppro={currentAppro} items={items} setItems={setItems} updateData={updateData}/>
        }
    </>
}