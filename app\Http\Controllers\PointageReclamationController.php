<?php

namespace App\Http\Controllers;

use App\Models\Pointage;
use App\Models\PointageReclamation;
use App\Models\Reclamation;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class PointageReclamationController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public static function index($reclamation_id){
        $pointages = DB::select("SELECT p.id, p.reclamation_id, p.date_pointage, p.site_id, s.nom as 'site'
            FROM pointage_reclamations p
            LEFT JOIN sites s on s.idsite = p.site_id
            WHERE p.reclamation_id = ?
            order by p.date_pointage desc", [$reclamation_id]);
        return response()->json($pointages);
    }

    public static function store(Request $request){
        $reclamation = Reclamation::find($request->reclamation_id);
        if(
            (in_array($request->user()->role, ["rh", "resp_rh"]) && in_array($reclamation->status, ["demande", "traite"]))
            || ($request->user()->id == $reclamation->user_id && $reclamation->status == "draft")
        ){
            $pointage = new PointageReclamation();
            $pointage->reclamation_id = $request->reclamation_id;
            $pointage->employe_id = $reclamation->employe_id;
            $pointage->site_id = $request->site_id;
            $pointage->date_pointage = $request->date_pointage;
            $pointage->created_at = new \Datetime();
            $pointage->updated_at = new \Datetime();
            $pointage->save();
            return response(["success" => "Pointage enregistré", "id" => $pointage->id]);
        }
        return response(["error" => "EACCES"]);
    }

    public static function update($id, Request $request){
        $pointage = PointageReclamation::find($id);
        $reclamation = Reclamation::find($pointage->reclamation_id);
        if(
            (in_array($request->user()->role, ["rh", "resp_rh"]) && in_array($reclamation->status, ["demande", "traite"]))
            || ($request->user()->id == $reclamation->user_id && $reclamation->status == "draft")
        ){

            if(Pointage::where('employe_id', $reclamation->employe_id)
                ->where('date_pointage', $request->date_pointage)
                ->where(function ($query) {
                    return $query->whereNull('soft_delete')->orWhere('soft_delete', 0);
                })
                ->first() != null
            ) {
                return response(["error" => "Le pointage de la date mentionné est déjà enregistré dans TLS"]);
            }
            if(PointageReclamation::where('id', '<>', $pointage->id)
                ->where('employe_id', $reclamation->employe_id)
                ->where('date_pointage', $request->date_pointage)
                ->first() != null
            ) {
                return response(["error" => "Le pointage de la date mentionné est déjà enregistré dans une reclamation"]);
            }

            $pointage->site_id = $request->site_id;
            $pointage->date_pointage = $request->date_pointage;
            $pointage->created_at = new \Datetime();
            $pointage->updated_at = new \Datetime();
            $pointage->save();
            return response(["success" => "Pointage modifié", "id" => $pointage->id]);
        }
        return response(["error" => "EACCES"]);
    }

    public static function delete($id, Request $request){
        $pointage = PointageReclamation::find($id);
        $reclamation = Reclamation::find($pointage->reclamation_id);
        $nb_pointage = count(PointageReclamation::where('reclamation_id', $pointage->reclamation_id)->get());
        if(
            (in_array($request->user()->role, ["rh", "resp_rh"]) && in_array($reclamation->status, ["demande", "traite"]))
            || ($request->user()->id == $reclamation->user_id && $reclamation->status == "draft")
        ){
            if($nb_pointage > 1) {
                $pointage->delete();
                return response(["success" => "Pointage reclamé supprimé", "id" => $reclamation->id]);
            }
            else {
                return response(["error" => "Une reclamation doit contenir au moin un pointage"]);
            }
        }
        return response(["error" => "EACCES"]);
    }
}
