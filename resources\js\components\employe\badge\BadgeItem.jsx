import React from 'react'
import './badge.css';
import Logo from './dirickx_guard.jpg'
import QRCode from 'react-qr-code'

export default function BadgeItem({ employe }) {
    return (
        <div>
            <div className='badge-header'>
                <div className='badge-left-content'>
                    <img className='logo' src={Logo} />
                    <div className="badge-content">
                        <span className='matricule'>{employe.matricule}</span> <br />
                        <span className='employe-name'>{employe.nom}</span> <br />
                        <span className='fonction'><b>{employe.fonction}</b></span>
                    </div>
                </div>
                <div>
                    <div className='badge-profil'></div>
                    <div className='qr-code'>
                        <QRCode
                            size={256}
                            style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                            value={"Matricule: " + employe.matricule + "\nNom: " + employe.nom + "\nFonction: " + employe.fonction}
                            viewBox={`0 0 256 256`}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}
