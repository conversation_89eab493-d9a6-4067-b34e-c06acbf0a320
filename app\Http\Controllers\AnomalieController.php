<?php

namespace App\Http\Controllers;

use App\Models\Employe;
use App\Models\JourFerie;
use App\Models\Planning;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AnomalieController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    function getDayOrNightExport() {
        $now = new \DateTime();
        $beginDay = (clone $now)->setTime(7, 10, 0);
        $endDay = (clone $now)->setTime(19, 10, 0);
    
        if ($now > $beginDay && $now < $endDay) {
            return $now->sub(new \DateInterval('P1D'))->format('Y-m-d') . " 18:00:00";
        } else {
            if ($now < $beginDay) {
                return $now->sub(new \DateInterval('P1D'))->format('Y-m-d') . " 06:00:00";
            }
            return $now->format('Y-m-d') . " 06:00:00";
        }
    }
    
    public function getDayOrNightDate(){
        if (
            new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if (new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 00, 0)->format('Y-m-d H:i:s');
    }

    public static function search(Request $request){
        $searchArray = [];
        if($request->type_anomalie){
            if($request->type_anomalie == "manque")
                $searchArray[] = " COUNT(DISTINCT ptg.id) < COUNT(DISTINCT pl_ptg.id) ";
            else if($request->type_anomalie == "surplus")
                $searchArray[] = " COUNT(DISTINCT ptg.id) > COUNT(DISTINCT pl_ptg.id) ";
            else if($request->type_anomalie == "incoherence")
                $searchArray[] = " COUNT(DISTINCT pl_ptg.id) = COUNT(DISTINCT ptg.id) ";
        }
        if (count($searchArray) > 0) {
            return " AND " . implode(" and ", $searchArray) . "";
        }
        return "";
    }

    public static function string_to_array($data) {
        if (empty($data) || trim($data) === "") {
            return [];
        }
        $ptgAgents = array_map('intval', explode(',', $data));
        return $ptgAgents;
    }

    public static function merge_string($data1, $data2) {
        $array1 = explode(',', $data1);
        $array2 = explode(',', $data2);
        return implode(',', array_unique(array_merge($array1, $array2)));
    }

    public static function index(Request $request){
        $auth = $request->user();
        if (in_array($auth->role, ['resp_op', 'resp_sup', 'validateur', 'room', 'access', 'admin'])) {
            $date = $request->date_service ? new \DateTime($request->date_service) : new \DateTime(AnomalieController::getDayOrNightExport());
            if ($request->date_service) {
                if(new \DateTime($request->date_service) > (new \DateTime())){
                    return response()->json(['error' => 'Choisir une date dans le passé']);
                }
            }
            $condition = "";
            if($request->site_id){
                $condition .= " AND st.idsite = " . $request->site_id;
            }

            $anomalies = DB::select("SELECT  COALESCE(st.group_planning_id, st.idsite) AS idsite_group, 
                    parent_site.nom AS site, parent_site.group_planning_id AS parent_group_id, COUNT(DISTINCT ptg.id) AS ptgCount,  
                    COUNT(DISTINCT pl_ptg.id) AS agent_pl, GROUP_CONCAT(DISTINCT ptg.employe_id) AS 'pointages', 
                    cm_pl.comment, GROUP_CONCAT(DISTINCT pl_ptg.agent_id) AS 'pl_pointages', parent_user.id AS 'superviseur_id',
                    parent_user.name AS 'superviseur', parent_user.email AS 'superviseur_email',
                    parent_user.flotte AS 'superviseur_flotte',
                    parent_resp.id AS 'resp_sup_id',
                    parent_resp.name AS 'resp_sup',
                    parent_resp.email AS 'resp_sup_email'
                FROM sites st
                LEFT JOIN sites parent_site ON parent_site.idsite = COALESCE(st.group_planning_id, st.idsite)
                LEFT JOIN pointages ptg ON ptg.site_id = st.idsite AND (ptg.soft_delete IS NULL OR ptg.soft_delete = 0) AND ptg.date_pointage = ?
                LEFT JOIN plannings pl ON pl.site_id = st.idsite AND pl.date_planning = ?
                LEFT JOIN planning_pointages pl_ptg ON pl_ptg.planning_id = pl.id AND pl_ptg.date_pointage = ?
                LEFT JOIN comment_plannings cm_pl ON cm_pl.id = (
                        SELECT MAX(id) FROM comment_plannings 
                        WHERE planning_id = pl.id AND date_pointage = ?
                    )
                LEFT JOIN users parent_user ON parent_user.id = parent_site.superviseur_id
                LEFT JOIN users parent_resp ON parent_resp.id = parent_site.resp_sup_id
                WHERE 
                    (st.soft_delete = 0 or st.soft_delete is null)
                    AND st.pointage = 1  
                    AND (st.group_planning_id IS NULL OR st.group_planning_id = parent_site.idsite) 
                    " . $condition . "
                GROUP BY  
                    idsite_group, cm_pl.id, parent_site.nom, parent_site.group_planning_id, parent_user.id,
                    parent_user.name, parent_user.email, parent_user.flotte, parent_resp.id, parent_resp.name ,
                    parent_resp.email
                HAVING 
                    IFNULL(GROUP_CONCAT(DISTINCT ptg.employe_id ORDER BY ptg.employe_id ASC), '') 
                    != IFNULL(GROUP_CONCAT(DISTINCT pl_ptg.agent_id ORDER BY pl_ptg.agent_id ASC), '') 
                    " . AnomalieController::search($request) . " 
                ORDER BY 
                    parent_site.nom ASC 
                " . (($request->offset || $request->offset > -1 ) ? " LIMIT $request->offset, 30" : ""), 
                [
                    ($date->format('H:i:s') == "18:00:00" ? $date->format('Y-m-d H:i:s') : $date->modify('+1 hour')->format('Y-m-d H:i:s')),
                    $date->format('Y-m'),
                    $date->format('Y-m-d H:i:s'),
                    $date->format('Y-m-d H:i:s')
                ]
            );
            $init_anomalies = collect($anomalies);
            $anomalies = collect();
            $parents = [];
            foreach ($init_anomalies as $anomalie) {
                $site_parent = null;
                // if ($anomalie->idsite_group && $anomalie->idsite_group != $anomalie->idsite) {
                //     // Recherchez l'anomalie ayant `idsite` égal à `group_planning_id`
                //     $site_parent = $init_anomalies->firstWhere('idsite', $anomalie->idsite_group);
            
                //     if ($site_parent) {
                //         // Vérifiez si le parent est déjà dans la liste des parents mis à jour
                //         $parent_id = $site_parent->idsite;
                //         if (!isset($parents[$parent_id])) {
                //             // Initialisez les propriétés cumulatives
                //             $site_parent->ptg_ids = AnomalieController::string_to_array($site_parent->pointages);
                //             $site_parent->pl_ptg_ids = AnomalieController::string_to_array($site_parent->pl_pointages);
                //             $parents[$parent_id] = $site_parent; // Enregistrez le parent dans la table
                //         }

                //         // Fusionnez les données de l'enfant dans le parent (cumulatif)
                //         $parents[$parent_id]->ptg_ids = array_merge(
                //             $parents[$parent_id]->ptg_ids,
                //             AnomalieController::string_to_array($anomalie->pointages)
                //         );
                //         $parents[$parent_id]->pl_ptg_ids = array_merge(
                //             $parents[$parent_id]->pl_ptg_ids,
                //             AnomalieController::string_to_array($anomalie->pl_pointages)
                //         );

                //         $parents[$parent_id]->pl_origin_test = $parents[$parent_id]->pl_ptg_ids;
                //         $parents[$parent_id]->ptg_origin_test = $parents[$parent_id]->ptg_ids;
                //         $parents[$parent_id]->pl_ptg_test = AnomalieController::string_to_array($anomalie->pl_pointages);
                //         $parents[$parent_id]->ptg_test = AnomalieController::string_to_array($anomalie->pointages);
                //         // Recalcul des propriétés cumulatives
                //         $parents[$parent_id]->non_planning = count(array_diff(
                //             $parents[$parent_id]->ptg_ids,
                //             $parents[$parent_id]->pl_ptg_ids
                //         ));
                //         $parents[$parent_id]->non_pointes = count(array_diff(
                //             $parents[$parent_id]->pl_ptg_ids,
                //             $parents[$parent_id]->ptg_ids
                //         ));

                //         $count_pl_agent = count($parents[$parent_id]->pl_ptg_ids);
                //         $count_ptg_agent = count($parents[$parent_id]->ptg_ids);

                //         $parents[$parent_id]->manque = ($count_pl_agent - $count_ptg_agent) > 0
                //             ? $count_pl_agent - $count_ptg_agent
                //             : 0;
                //         $parents[$parent_id]->surplus = ($count_ptg_agent - $count_pl_agent) > 0
                //             ? $count_ptg_agent - $count_pl_agent
                //             : 0;

                //         $parents[$parent_id]->incoherence = max(
                //             $parents[$parent_id]->non_planning,
                //             $parents[$parent_id]->non_pointes
                //         );
                //         $parents[$parent_id]->test = 1;
                //         continue;
                //     }
                // }
                
                
                $anomalie->ptg_ids = AnomalieController::string_to_array($anomalie->pointages);
                $anomalie->pl_ptg_ids = AnomalieController::string_to_array($anomalie->pl_pointages);
                $anomalie->non_planning = count(array_diff($anomalie->ptg_ids, $anomalie->pl_ptg_ids));
                $anomalie->non_pointes = count(array_diff($anomalie->pl_ptg_ids, $anomalie->ptg_ids));

                $count_pl_agent = count( $anomalie->pl_ptg_ids);
                $count_ptg_agent = count($anomalie->ptg_ids);

                $anomalie->manque = ($count_pl_agent - $count_ptg_agent) > 0 ? $count_pl_agent - $count_ptg_agent : 0;
                $anomalie->surplus = ($count_ptg_agent - $count_pl_agent) > 0 ? $count_ptg_agent - $count_pl_agent : 0;
                // $incoherence = 0;
                $pl_ptg_agent_counts = array_count_values($anomalie->pl_ptg_ids);
                $ptg_agent_counts = array_count_values($anomalie->ptg_ids);
                $anomalie->incoherence = $anomalie->non_planning > $anomalie->non_pointes ? $anomalie->non_planning : $anomalie->non_pointes;
                $anomalies->push($anomalie);
            }
            return response(compact('anomalies'));
        }
        return response(["error" => "EACCES"]);
    }

    public static function show(Request $request, $id) {
        $date = $request->date_service ? new \DateTime($request->date_service) : AnomalieController::getDayOrNightExport();
        if ($request->date_service) {
            if(new \DateTime($request->date_service) > (new \DateTime())){
                return response()->json(['error' => 'Choisir une date dans le passé']);
            }
        }

        $anomalies = DB::select("SELECT st.idsite,  st.nom AS site,  COUNT(DISTINCT ptg.id) AS ptgCount, 
            COUNT(DISTINCT pl_ptg.id) AS agent_pl, GROUP_CONCAT(DISTINCT ptg.employe_id) AS 'pointages', cm_pl.comment,
            GROUP_CONCAT(DISTINCT pl_ptg.agent_id) AS 'pl_pointages', 
            u.id as 'superviseur_id',u.name as 'superviseur', u.email as 'superviseur_email', u.flotte as 'superviseur_flotte', 
            resp.id as 'resp_sup_id', resp.name as 'resp_sup', resp.email as 'resp_sup_email' 
            FROM  sites st 
                LEFT JOIN  pointages ptg ON ptg.site_id = st.idsite AND (ptg.soft_delete IS NULL OR ptg.soft_delete = 0) 
                    AND ptg.date_pointage = ?
                LEFT JOIN  plannings pl ON pl.site_id = st.idsite AND pl.date_planning = ?
                LEFT JOIN  planning_pointages pl_ptg ON pl_ptg.planning_id = pl.id AND pl_ptg.date_pointage = ?
                LEFT JOIN comment_plannings cm_pl ON cm_pl.planning_id = pl.id and cm_pl.date_pointage = ?
                LEFT JOIN users u ON u.id = st.superviseur_id 
                LEFT JOIN users resp ON resp.id = st.resp_sup_id 
            WHERE st.pointage = 1 AND (st.idsite = ? or st.group_planning_id = ?)
                GROUP BY  st.idsite, cm_pl.id 
            HAVING IFNULL(GROUP_CONCAT(DISTINCT ptg.employe_id), '') != IFNULL(GROUP_CONCAT(DISTINCT pl_ptg.agent_id), '') "
            ,[
                ($date->format('H:i:s') == "18:00:00" ? $date->format('Y-m-d H:i:s') : $date->modify('+1 hour')->format('Y-m-d H:i:s')),
                $date->format('Y-m'),
                $date->format('Y-m-d H:i:s'),
                $date->format('Y-m-d H:i:s'),
                $request->id,
                $request->id,
            ]
        );

        $site= DB::select("SELECT s.idsite, s.nom, s.adresse,s.nb_agent_night, s.nb_agent_day,
            hr.nom as 'horaire', u.name as 'superviseur', u.email as 'superviseur_email', total_hour,
            resp.name as 'resp_sup', resp.email as 'resp_sup_email',resp.id as 'resp_sup_id', u.id as 'superviseur_id'
            FROM sites s
            LEFT JOIN horaires hr ON hr.id = s.horaire_pointage_id
            LEFT JOIN users u ON u.id = s.superviseur_id 
            LEFT JOIN users resp ON resp.id = s.resp_sup_id 
            WHERE s.idsite = ?", [$request->id])[0];

        if (count($anomalies) > 0) {
            $parent = collect($anomalies)->firstWhere('idsite', $id);
            $anomalie = new \stdClass();
            $anomalie->idsite = $parent->idsite;
            $anomalie->site = $parent->site;
            $anomalie->ptgCount = $parent->ptgCount;
            $anomalie->agent_pl = $parent->agent_pl;
            $anomalie->pointages = $parent->pointages;
            $anomalie->comment = $parent->comment;
            $anomalie->pl_pointages = $parent->pl_pointages;
            $anomalie->superviseur_id = $parent->superviseur_id;
            $anomalie->superviseur = $parent->superviseur;
            $anomalie->superviseur_email = $parent->superviseur_email;
            $anomalie->superviseur_flotte = $parent->superviseur_flotte;
            $anomalie->resp_sup_id = $parent->resp_sup_id;
            $anomalie->resp_sup = $parent->resp_sup;
            $anomalie->resp_sup_email = $parent->resp_sup_email;

            foreach($anomalies as $an){
                if($an->idsite != $anomalie->idsite){
                    $anomalie->pointages = AnomalieController::merge_string($an->pointages, $anomalie->pointages);
                    $anomalie->pl_pointages = AnomalieController::merge_string($an->pl_pointages, $anomalie->pl_pointages);
                }
            }

            $pl_ptg_agents = AnomalieController::string_to_array($anomalie->pl_pointages);
            $ptg_agents = AnomalieController::string_to_array($anomalie->pointages);

            $employeIds  = array_merge($ptg_agents, $pl_ptg_agents);
            
            $employeIdsString = implode(',', array_map('intval', $employeIds));
            $employes  = DB::select("SELECT id as 'employe_id', societe_id, numero_stagiaire, numero_employe, num_emp_soit,
                num_emp_saoi, nom as 'employe', soft_delete, date_sortie
                FROM employes 
                WHERE id IN (" . $employeIdsString .")", []);
            $employes = json_decode(json_encode($employes), true);
            $temp_ptg_agents = [];
            foreach ($ptg_agents as $agent) {
                foreach($employes as $employe){
                    if($employe['employe_id'] == $agent){
                        $temp_ptg_agents[] = [
                            'employe_id' => $employe['employe_id'],
                            'societe_id' => $employe['societe_id'],
                            'numero_stagiaire' => $employe['numero_stagiaire'],
                            'numero_employe' => $employe['numero_employe'],
                            'num_emp_soit' => $employe['num_emp_soit'],
                            'num_emp_saoi' => $employe['num_emp_saoi'],
                            'employe' => $employe['employe']
                        ];
                    }
                }
            }
            $temp_pl_agents = [];
            foreach ($pl_ptg_agents as $agent) {
                foreach($employes as $employe){
                    if($employe['employe_id'] == $agent){
                        $temp_pl_agents[] = [
                            'employe_id' => $employe['employe_id'],
                            'societe_id' => $employe['societe_id'],
                            'numero_stagiaire' => $employe['numero_stagiaire'],
                            'numero_employe' => $employe['numero_employe'],
                            'num_emp_soit' => $employe['num_emp_soit'],
                            'num_emp_saoi' => $employe['num_emp_saoi'],
                            'employe' => $employe['employe'],
                        ];
                    }
                }
            }

            $ptg_agents = json_decode(json_encode($temp_ptg_agents), true);
            $pl_ptg_agents = json_decode(json_encode($temp_pl_agents), true);

            $ptg_agents = array_values($ptg_agents);
            $pl_ptg_agents = array_values($pl_ptg_agents);
            
            $anomalie->pl_ptg_agents = $pl_ptg_agents;
            $anomalie->ptg_agents = $ptg_agents;
            $anomalie->ptg_ids = array_column($ptg_agents, 'employe_id');
            $anomalie->pl_ptg_ids = array_column($pl_ptg_agents, 'employe_id');
            $anomalie->non_planning = count(array_diff($anomalie->ptg_ids, $anomalie->pl_ptg_ids));
            $anomalie->non_pointes = count(array_diff($anomalie->pl_ptg_ids, $anomalie->ptg_ids));
            $anomalie->manque = 0;
            $anomalie->surplus = 0;
            $count_pl_agent = count($pl_ptg_agents);
            $count_ptg_agent = count($ptg_agents);
            $anomalie->manque = ($count_pl_agent - $count_ptg_agent) > 0 ? $count_pl_agent - $count_ptg_agent : 0;
            $anomalie->surplus = ($count_ptg_agent - $count_pl_agent) > 0 ? $count_ptg_agent - $count_pl_agent : 0;
            // $incoherence = 0;
            // $pl_ptg_agent_counts = array_count_values($anomalie->pl_ptg_ids);
            // $ptg_agent_counts = array_count_values($anomalie->ptg_ids);
            // foreach ($pl_ptg_agent_counts as $id => $count) {
            //     if (isset($ptg_agent_counts[$id])) {
            //         $difference = $count - $ptg_agent_counts[$id];
            //         if ($difference > 0) {
            //             $incoherence += $difference;
            //         }
            //     } else {
            //         $incoherence += $count;
            //     }
            // }
            // foreach ($ptg_agent_counts as $id => $count) {
            //     if (isset($pl_ptg_agent_counts[$id])) {

            //         $difference = $count - $pl_ptg_agent_counts[$id];
            //         if ($difference > 0) {
            //             $incoherence += $difference;
            //         }
            //     } else {
            //         $incoherence += $count;
            //     }
            // }
            // $anomalie->incoherence = $incoherence;
            $anomalie->incoherence = $anomalie->non_planning > $anomalie->non_pointes ? $anomalie->non_planning : $anomalie->non_pointes;
        }
        $planning = Planning::where('site_id', $id)->where('date_planning', $date->format('Y-m'))->first();
        return response(compact('anomalie', 'site', 'planning'));
    }

    public static function index_pointage_effectif(Request $request){
        $horaire = 'day';
        $auth = $request->user();
        $date = $request->date_service ? (new \DateTime($request->date_service))->format('Y-m-d H:i:s') : AnomalieController::getDayOrNightDate();
        if((new \DateTime($date))->format('H:i:s') == "06:00:00")
            $date = (new \DateTime($date))->format('Y-m-d') . " 07:00:00";
        if((new \DateTime($date))->format('H:i:s') == "18:00:00") 
            $horaire = 'night';
        $jour_ferie = JourFerie::where('date', (new \DateTime($date))->format('Y-m-d'))->first();
        $field = $horaire . '_' . (new \DateTime($date))->format('w');
        if($jour_ferie){
            $field = $horaire . '_ferie';
        }
        $cond = '';
        if($request->site_id){
            $cond = " AND st.idsite = " . $request->site_id;
        }
        $sql = "SELECT st.idsite, st.nom as 'site', he.day_1, he.day_2, he.day_3, he.day_4, he.day_5,
            he.day_6, he.day_0, he.day_ferie, he.night_1, he.night_2, he.night_3, he.night_4, he.night_5, 
            he.night_6, he.night_0, he.night_ferie, count(ptg.id) as 'nb_ptg', he.{$field} as 'current_effectif', u.name as 'resp_sup', u.email as 'resp_sup_email', st.resp_sup_id
            FROM sites st 
            LEFT JOIN users u on u.id = st.resp_sup_id
            LEFT JOIN horaire_effectifs he ON he.site_id = st.idsite
            LEFT JOIN pointages ptg ON ptg.site_id = st.idsite AND ptg.date_pointage = ?
            WHERE (st.soft_delete = 0 or st.soft_delete is null) AND st.pointage = 1 AND (st.group_planning_id is null or st.group_planning_id = st.idsite) {$cond} GROUP BY st.idsite, st.nom ";

        if($request->type_anomalie){
            if($request->type_anomalie == "manque")
                $sql .= "HAVING count(ptg.id) < he.{$field} ";
            else if($request->type_anomalie == "surplus")
                $sql .= "HAVING count(ptg.id) > he.{$field} ";
        }
        else $sql .= "HAVING count(ptg.id) <> he.{$field} ";
        $anomalies = DB::select($sql . " ORDER BY st.idsite desc limit ?, 30", [$date, $request->offset]);
        $nb_res = count($anomalies);
        return response(compact('anomalies', 'nb_res'));
    }
    
    public static function show_pointage_effectif(Request $request, $id){
        $horaire = 'day';
        $auth = $request->user();
        $date = $request->date_service ? (new \DateTime($request->date_service))->format('Y-m-d H:i:s') : AnomalieController::getDayOrNightDate();
        if((new \DateTime($date))->format('H:i:s') == "06:00:00")
            $date = (new \DateTime($date))->format('Y-m-d') . " 07:00:00";
        if((new \DateTime($date))->format('H:i:s') == "18:00:00") 
            $horaire = 'night';
        $jour_ferie = JourFerie::where('date', (new \DateTime($date))->format('Y-m-d'))->first();
        $field = $horaire . '_' . (new \DateTime($date))->format('w');
        if($jour_ferie){
            $field = $horaire . '_ferie';
        }
        $sql = "SELECT st.idsite, st.nom as 'site', count(ptg.id) as 'nb_ptg', he.{$field}, u.name as 'resp_sup', u.email as 'resp_sup_email', st.resp_sup_id, u.flotte
            FROM sites st 
            LEFT JOIN users u on u.id = st.resp_sup_id
            LEFT JOIN horaire_effectifs he ON he.site_id = st.idsite
            LEFT JOIN pointages ptg ON ptg.site_id = st.idsite AND ptg.date_pointage = ?
            WHERE st.pointage = 1 AND (st.group_planning_id is null or st.group_planning_id = st.idsite) and st.idsite = ? GROUP BY st.idsite, st.nom ";

        $anomalie = DB::selectOne($sql . " ORDER BY st.idsite", [$date, $id]);
        $anomalie->current_date = $field;
        return response(compact('anomalie'));
    }
    
    public static function index_planning_effectif(Request $request){
        $horaire = 'day';
        $auth = $request->user();
        $date = $request->date_service ? (new \DateTime($request->date_service))->format('Y-m-d H:i:s') : AnomalieController::getDayOrNightDate();
        if((new \DateTime($date))->format('H:i:s') == "06:00:00")
            $date = (new \DateTime($date))->format('Y-m-d') . " 07:00:00";
        if((new \DateTime($date))->format('H:i:s') == "18:00:00") 
            $horaire = 'night';
        $jour_ferie = JourFerie::where('date', (new \DateTime($date))->format('Y-m-d'))->first();
        $field = $horaire . '_' . (new \DateTime($date))->format('w');
        if($jour_ferie){
            $field = $horaire . '_ferie';
        }
        $cond = '';
        if($request->site_id){
            $cond = " AND st.idsite = " . $request->site_id;
        }
        $sql = "SELECT st.idsite, st.nom as 'site', he.day_1, he.day_2, he.day_3, he.day_4, he.day_5,
            he.day_6, he.day_0, he.day_ferie, he.night_1, he.night_2, he.night_3, he.night_4, he.night_5, 
            he.night_6, he.night_0, he.night_ferie, count(pl_ptg.id) as 'nb_ptg', he.{$field} as 'current_effectif', u.name as 'resp_sup', u.email as 'resp_sup_email', st.resp_sup_id
            FROM sites st 
            LEFT JOIN users u on u.id = st.resp_sup_id
            LEFT JOIN horaire_effectifs he ON he.site_id = st.idsite
            LEFT JOIN plannings pl on pl.site_id = st.idsite AND pl.date_planning = ?
            LEFT JOIN planning_pointages pl_ptg ON pl_ptg.planning_id = pl.id AND pl_ptg.date_pointage = ?
            WHERE (st.soft_delete = 0 or st.soft_delete is null) AND st.pointage = 1 AND (st.group_planning_id is null or st.group_planning_id = st.idsite)  {$cond} GROUP BY st.idsite, st.nom ";

        if($request->type_anomalie){
            if($request->type_anomalie == "manque")
                $sql .= "HAVING count(pl_ptg.id) < he.{$field} ";
            else if($request->type_anomalie == "surplus")
                $sql .= "HAVING count(pl_ptg.id) > he.{$field} ";
        }
        else $sql .= "HAVING count(pl_ptg.id) <> he.{$field} ";
        $anomalies = DB::select($sql . " ORDER BY st.idsite desc limit ?, 30", [(new \DateTime($date))->format('Y-m'), $date, $request->offset]);
        $nb_res = count($anomalies);
        return response(compact('anomalies', 'nb_res'));
    }

    public static function show_planning_effectif(Request $request, $id) {
        $horaire = 'day';
        $date = $request->date_service ? (new \DateTime($request->date_service))->format('Y-m-d H:i:s') : AnomalieController::getDayOrNightDate();
        if((new \DateTime($date))->format('H:i:s') == "06:00:00")
            $date = (new \DateTime($date))->format('Y-m-d') . " 07:00:00";
        if((new \DateTime($date))->format('H:i:s') == "18:00:00") 
            $horaire = 'night';
        $jour_ferie = JourFerie::where('date', (new \DateTime($date))->format('Y-m-d'))->first();
        $field = $horaire . '_' . (new \DateTime($date))->format('w');
        if($jour_ferie){
            $field = $horaire . '_ferie';
        }
        $sql = "SELECT st.idsite, st.nom as 'site', he.{$field},  count(pl_ptg.id) as 'nb_ptg', pl.id as 'has_planning', u.name as 'resp_sup', u.email as 'resp_sup_email', st.resp_sup_id, u.flotte
            FROM sites st 
            LEFT JOIN users u on u.id = st.resp_sup_id
            LEFT JOIN horaire_effectifs he ON he.site_id = st.idsite
            LEFT JOIN plannings pl on pl.site_id = st.idsite AND pl.date_planning = ?
            LEFT JOIN planning_pointages pl_ptg ON pl_ptg.planning_id = pl.id AND pl_ptg.date_pointage = ?
            WHERE st.pointage = 1 AND (st.group_planning_id is null or st.group_planning_id = st.idsite) and st.idsite = ? 
            GROUP BY st.idsite, st.nom, pl.id ";
        $anomalie = DB::selectOne($sql, [(new \DateTime($date))->format('Y-m'), $date, $id]);
        $anomalie->current_date = $field;
        return response(compact('anomalie'));
    }
}
