const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const matricule = require("../resources/js/components/util/matricule")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)
const {sendMail} = require("../auth")

const isTask = (process.argv[2] == "task")
const destination_vg = ["ogros<PERSON><EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectLastRecrutementExport = "SELECT value FROM params p WHERE p.key = 'last_recrutement_export'"

const sqlSelectRecrutement = `SELECT r.id, r.nom, r.cin_text, r.employe_id, r.created_at
    , e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit
    , e.date_embauche, e.date_confirmation, e.date_conf_soit, s.nom as 'site', u.name as 'user_nom', u.email as 'user_email'
    FROM recrutements r 
    LEFT JOIN employes e ON e.id = r.employe_id
    LEFT JOIN sites s ON s.idsite = e.real_site_id
    LEFT JOIN users u ON u.id = r.user_id
    WHERE r.created_at > ? and r.created_at < ?`

const sqlSelectPointage = (ids) => `SELECT p.employe_id, count(p.id) as 'nb' FROM pointages p 
    where (p.soft_delete is null or p.soft_delete = 0) and p.employe_id in (${ids.join(',')})
    group by p.employe_id`

const sqlSelectFormation = (ids) => `SELECT f.numero_stagiaire, f.date_pointage
    FROM pointage_formations f 
    where f.numero_stagiaire in (${ids.join(',')})`

const sqlSelectManqueVigilance = (ids) => `SELECT mq.numero_stagiaire, mq.date_service
FROM manque_vigilances mq 
where mq.numero_stagiaire in (${ids.join(',')})`

function sqlUpdateLastRecrutementExport(dateString){
    return "UPDATE params p SET p.value = '" + dateString + "' " +
        "WHERE p.key = 'last_recrutement_export'"
}

function generateRecrutementExcelFile(workbook, header, recrutements){
    const borderStyle = {
        top: {style:'thin'},
        left: {style:'thin'},
        bottom: {style:'thin'},
        right: {style:'thin'}
    }
    const fontBold = {
        bold: true
    }

    const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
    const fontHeader = { size: 16, bold: true }
    let currentNiveau = null
    let currentWorksheet = null
    let line = 0

    recrutements.forEach((row, index) => {
        if(currentNiveau != row.niveau || index==0){
            line = 3
            currentNiveau = row.niveau
            currentWorksheet = workbook.addWorksheet(currentNiveau ? "Niveau " + currentNiveau : "Sans formation")

            currentWorksheet.getColumn('A').width = 15
            currentWorksheet.getColumn('B').width = 50
            currentWorksheet.getColumn('C').width = 20
            currentWorksheet.getColumn('D').width = 15
            currentWorksheet.getColumn('E').width = 10
            currentWorksheet.getColumn('F').width = 10
            currentWorksheet.getColumn('G').width = 50
            currentWorksheet.getCell('A1').value = (currentNiveau ? "Niveau " + currentNiveau : "Sans formation") + " : " + recrutements.filter(r => r.niveau == currentNiveau).length
            currentWorksheet.getCell('A1').font = fontHeader
            currentWorksheet.mergeCells('A1:F1')
            
            currentWorksheet.getCell('A' + line).value = "Matricule"
            currentWorksheet.getCell('A' + line).border = borderStyle
            currentWorksheet.getCell('A' + line).font = fontBold
            currentWorksheet.getCell('B' + line).value = "Nom"
            currentWorksheet.getCell('B' + line).border = borderStyle
            currentWorksheet.getCell('B' + line).font = fontBold
            currentWorksheet.getCell('C' + line).value = "CIN"
            currentWorksheet.getCell('C' + line).border = borderStyle
            currentWorksheet.getCell('C' + line).font = fontBold
            currentWorksheet.getCell('C' + line).alignment = alignmentStyle
            currentWorksheet.getCell('D' + line).value = "Embauche"
            currentWorksheet.getCell('D' + line).border = borderStyle
            currentWorksheet.getCell('D' + line).alignment = alignmentStyle
            currentWorksheet.getCell('D' + line).font = fontBold
            currentWorksheet.getCell('E' + line).value = "Formation"
            currentWorksheet.getCell('E' + line).border = borderStyle
            currentWorksheet.getCell('E' + line).font = fontBold
            currentWorksheet.getCell('F' + line).value = "Pointage"
            currentWorksheet.getCell('F' + line).border = borderStyle
            currentWorksheet.getCell('F' + line).font = fontBold
            currentWorksheet.getCell('G' + line).value = "Site"
            currentWorksheet.getCell('G' + line).border = borderStyle
            currentWorksheet.getCell('G' + line).font = fontBold
            line++
        }
        
        currentWorksheet.getCell('A' + line).border = borderStyle
        currentWorksheet.getCell('A' + line).value = matricule(row)
        currentWorksheet.getCell('B' + line).border = borderStyle
        currentWorksheet.getCell('B' + line).value = row.nom
        currentWorksheet.getCell('C' + line).border = borderStyle
        currentWorksheet.getCell('C' + line).value = row.cin_text
        currentWorksheet.getCell('D' + line).border = borderStyle
        currentWorksheet.getCell('D' + line).value = moment(row.created_at).format("DD/MM/YY")
        currentWorksheet.getCell('E' + line).border = borderStyle
        currentWorksheet.getCell('E' + line).value = (row.nb_formation-row.nb_manque) + "/" + row.nb_formation
        currentWorksheet.getCell('F' + line).border = borderStyle
        currentWorksheet.getCell('F' + line).value = row.nb_pointage
        currentWorksheet.getCell('G' + line).border = borderStyle
        currentWorksheet.getCell('G' + line).value = row.site
        line++
    })
}

function doRecrutementExport(dateString){
    console.log("doRecrutementExport")
    const begin = moment(dateString).format("YYYY-MM-DD") + " 00:00:00"
    const end = moment(dateString).add(1, "month").format("YYYY-MM-DD")  + " 00:00:00"
    pool.query(sqlSelectRecrutement, [begin, end], async (err, recrutements) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb recrutement: " + recrutements.length)
            pool.query(sqlSelectFormation(recrutements.map(r => r.id)), [], async (err, formations) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb formation: " + formations.length)
                    pool.query(sqlSelectManqueVigilance(recrutements.map(r => r.id)), [], async (err, manques) => {
                        if(err)
                            console.error(err)
                        else {
                            let recrutes = recrutements.filter(r => r.employe_id)
                            pool.query(sqlSelectPointage(recrutes.map(r => r.employe_id)), [], async (err, pointages) => {
                                if(err)
                                    console.error(err)
                                else {
                                    console.log("Nb manque: " + manques.length)
                                    const dateService = moment(dateString).format("MMMM YYYY")
                                    const objet = "Recrutement " + dateService
                                    let niveau = {}
                                    let niveauArray = []
                                    recrutes.forEach(r => {
                                        pointages.forEach(p => {
                                            if(r.employe_id == p.employe_id)
                                                r.nb_pointage = p.nb
                                        })
                                    })
                                    recrutes.forEach(r => {
                                        r.nb_formation = formations.filter(f => (r.id == f.numero_stagiaire)).length
                                        r.nb_manque = manques.filter(m => (r.id == m.numero_stagiaire)).length
                                        r.niveau = r.nb_formation - r.nb_manque
                                        if(r.niveau && !niveauArray.includes(r.niveau)){
                                            niveau[r.niveau + " jour" + (r.niveau > 1 ? "s" : "")] = 0
                                            niveauArray.push(r.niveau)
                                        }
                                    })
                                    recrutes.sort((a, b) => a.niveau - b.niveau).forEach(r => {
                                        if(r.niveau)
                                            niveau[r.niveau + " jour" + (r.niveau > 1 ? "s" : "")]++
                                    })
                                    console.log(niveau)

                                    const attachements = []
                                    const workbookRecrutement = new Excel.Workbook()
                                    const recrutementHeader = "Recrutement " + dateService
                                    generateRecrutementExcelFile(workbookRecrutement, recrutementHeader, recrutes)
                                    const recrutementBuffer = await workbookRecrutement.xlsx.writeBuffer()
                                    attachements.push({
                                        filename: recrutementHeader + ".xlsx",
                                        content: recrutementBuffer
                                    })
                                    sendMail(
                                        pool,
                                        isTask ? destination_vg : destination_test,
                                        objet, 
                                        `<p>Veuillez trouver ci-joint le rapport des dossiers de recrutements du mois de ${moment(dateString).format("MMMM YYYY")} </p>
                                        <ul>
                                            <li>Recruté : ${recrutes.length} / ${recrutements.length} dossiers</li>
                                            <li>Sans formation : ${recrutes.filter(r => (r.niveau == 0)).length} / ${recrutes.length} recrutés</li>
                                            <li>Avec formation biométrique: ${recrutes.filter(r => (r.niveau)).length} / ${recrutes.length} recrutés</li>
                                        </ul>
                                        <p>Niveau de formation : </p>
                                        <ul>
                                            ${Object.keys(niveau).sort().map(key => 
                                                `<li>${key} : ${niveau[key]}</li>`
                                            ).join('')}
                                        </ul>`
                                        ,
                                        attachements
                                        ,
                                        (response) => {
                                            if(response && isTask){
                                                pool.query(sqlUpdateLastRecrutementExport(dateString), [], (e, r) =>{
                                                    if(e)
                                                        console.error(e)
                                                    else
                                                        console.log("update last diag export: " + r)
                                                    process.exit(1)
                                                })
                                            }
                                            else
                                                process.exit(1)
                                        },
                                        isTask
                                    )
                                }
                            })
                        }
                    })
                }
            })
        }
    })
}

const year = process.argv[2]
const month = process.argv[3]

if(/^\d{4}$/.test(year) && /^\d{2}$/.test(month)){
    console.log("send test...")
    doRecrutementExport(`${year}-${month}-01`)
}
else if(isTask){
    const dateString = moment().subtract(1, "month").format("YYYY-MM") + '-1'
    pool.query(sqlSelectLastRecrutementExport, [], (err, result) => {
        if(err)
            console.error(err)
        else if(result && result[0].value == dateString) {
            console.log("export list recrutement already done!")
            process.exit(1)
        }
        else {
            console.log("exporting ...")
            doRecrutementExport(dateString)
        }
    })
}
else
    console.log("please specify command!")