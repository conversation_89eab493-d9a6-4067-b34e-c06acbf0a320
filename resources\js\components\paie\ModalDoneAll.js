import React, { useEffect, useState } from 'react'
import InputMonthYear from '../input/InputMonthYear';
import axios from 'axios';
import useToken from '../util/useToken';
import moment from 'moment';

export default function ModalDoneAll({ closeModal, updateData }) {
    const [datePaie, setDatePaie] = useState({
        month: "",
        year: ""
    })
    const [paies, setPaies] = useState();
    const [disabledSearch, setDisabledSearch] = useState(false);
    const [numberPaieInit, setNumberPaieInit] = useState(0)
    const [showElement, setShowElement] = useState(false);
    const [searchButton, toggleSearchButton] = useState(false);
    const [confirmButton, toggleConfirmButton] = useState(false);
    const [employeIds, setEmployeIds] = useState([]);
    const [paieIds, setPaieIds] = useState([])

    useEffect(() => {
        if (datePaie.year.toString().trim() && datePaie.month.trim()) {
            toggleSearchButton(true);
            toggleConfirmButton(false);
        }
        else if (paies && paies.length > 0) {
            toggleConfirmButton(true);
            toggleSearchButton(false);
        }
        setDisabledSearch(!(datePaie.year.toString().trim() && datePaie.month.trim()))
    }, [datePaie])

    useEffect(() => {
        if(moment().isBefore(moment().set('date', 20))){
            setDatePaie({
                month: (moment().subtract(1, 'month')).format('MM'),
                year: (moment().subtract(1, 'month')).format('YYYY')
            })
        }
        else{
            setDatePaie({
                month: moment().format('MM'),
                year: moment().format('YYYY')
            })
        }
    }, [])

    const onSearch = () => {
        let date_paie = datePaie.year + '-' + datePaie.month + '-20';
        let emp_id = []
        let paie_id = []
        axios.get('/api/paie/get_to_done_all?date_paie=' + date_paie, useToken())
            .then((res) => {
                for (let i = 0; i < res.data.paies.length; i++) {
                    emp_id.push((res.data.paies[i].employe_id).toString());
                    paie_id.push((res.data.paies[i].id).toString());
                }
                setEmployeIds(emp_id);
                setPaieIds(paie_id);
                setPaies(res.data.paies)
                setNumberPaieInit(res.data.paies.length)
                setShowElement(true)
                if (res.data.paies.length > 0) {
                    toggleConfirmButton(true)
                    toggleSearchButton(false)
                }
            })
    }

    useEffect(() => {
        if (datePaie.year.toString().trim() && datePaie.month.trim()) 
            onSearch()
    },[datePaie])

    const handleDone = () => { 
        let data = {};
        data.ids_emp = employeIds;
        data.ids_paie = paieIds;
        axios.post('/api/paie/save_done_multiple',data, useToken())
            .then((res) => { 
                if (res.data.success) {
                    updateData(true);
                    closeModal();
                }
            })
    }
    
    return (
        <div className='modal'>
            <div>
                <h2>Terminé</h2>
                <div style={{ marginBottom: 50 }}>
                    <InputMonthYear label="Date Paie" value={datePaie} onChange={setDatePaie} required />
                </div>
                <div>
                    {showElement &&
                        <div >
                            {numberPaieInit + " élement(s) trouvé(s)"}
                        </div>
                    }
                </div>

                <div className='form-button-container'>
                    {/* {searchButton && <button onClick={() => onSearch()} className='btn btn-primary' disabled={disabledSearch}>Chercher</button>} */}
                    {confirmButton&&
                        <button type='button' className='btn btn-primary' onClick={() => handleDone()}>
                            Confirmer
                        </button>
                    }
                    <button type='button' onClick={() => closeModal()}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
