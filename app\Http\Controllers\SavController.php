<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sav;
use App\Models\User;
use App\Models\TypeSav;
use App\Models\Notification;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class SavController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }
    
    public function show($id){
        $sav = DB::select("SELECT sav.id, sav.type_sav, sav.user_id, sav.superviseur_id, sav.site_id, sav.status, sav.created_at, sav.motif, 
            sav.mesure, sav.date_sav, sav.technicien, st.nom as 'site', 
            stat.description as 'status_description', stat.color as 'status_color',
            us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
            FROM sav
            LEFT JOIN sites st on st.idsite = sav.site_id
            LEFT JOIN users us on us.id = sav.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN users sup on sup.id = sav.superviseur_id
            LEFT JOIN `status` stat on stat.name = sav.status
            where sav.id = ?", [$id])[0];
        $type = TypeSav::find($sav->type_sav);
        $sav->type = $type;
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj
            WHERE pj.sav_id = ?
            order by pj.created_at desc", [$id]);
        $sav->nb_pj = count($pieces);
        return response()->json($sav);
    }
    
    public function detail($id){
        $sav = DB::select("SELECT sav.id, sav.type_sav, sav.user_id, sav.superviseur_id, sav.site_id, sav.status, sav.created_at, sav.motif, 
            sav.mesure, stat.description as 'status_description', stat.color as 'status_color',
            st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
            FROM sav
            LEFT JOIN sites st on st.idsite = sav.site_id
            LEFT JOIN users us on us.id = sav.user_id
            LEFT JOIN users sup on sup.id = sav.superviseur_id
            LEFT JOIN `status` stat on stat.name = sav.status
            where sav.id = ?", [$id])[0];
        $type = TypeSav::find($sav->type_sav);
        $sav->type = $type;
        $pieces = DB::select("SELECT pj.id, pj.nature, pj.path, pj.user_id, pj.created_at, pj.updated_at
            FROM piece_jointes pj
            WHERE pj.sav_id = ?
            order by pj.created_at desc", [$id]);
        $historiques = DB::select("SELECT h.id, h.objet, h.note, h.detail, h.created_at, h.user
            FROM historiques h
            where h.sav_id = ?
            order by h.created_at desc
            limit 10", [$id]);
        return response()->json(compact('sav', 'pieces', 'historiques'));
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = "sav.id = '". $request->id ."'";
        else {
            if($request->status)
                $searchArray[] = "sav.status = '". $request->status ."'";
            if($request->created_at)
                $searchArray[] = " sav.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "sav.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " sav.user_id = " . $request->user_id . " ";
            if($request->site_id)
                $searchArray[] = " sav.site_id = " . $request->site_id . " ";
            if($request->superviseur_id)
                $searchArray[] = " sav.superviseur_id = " . $request->superviseur_id . " ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        
        $query_where = $query_where . " order by sav.id desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by sav.id desc limit ". $request->offset . ", 30";
        return compact('query_where', 'query_and');
    }

    function getDateLimit() {
        $current_date = new \DateTime();
        if(new \DateTime >= (new \DateTime)->setTime(6, 0, 0) &&
                new \DateTime < (new \DateTime)->setTime(18, 00, 0)){
            $limit_date = (new \DateTime)->setTime(6, 00, 0)->format('Y-m-d H:i:s');
        }
        else {
            if(new \DateTime < (new \DateTime)->setTime(06, 0,0)){
                $limit_date = (new \DateTime)->sub(new \DateInterval('P1D'))->setTime(18, 0, 0)->format('Y-m-d H:i:s');
            }
            else
                $limit_date = (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
        }
        return $limit_date;
    }

    public function index(Request $request, $type){
        $search = $this->search($request);
        if(in_array($request->user()->role, ['tech', 'electronique', 'validateur', 'access', 'daf'])){
            $sav = DB::select("SELECT sav.id, sav.user_id, sav.site_id, sav.motif, sav.status, sav.created_at,
                stat.description as 'status_description', stat.color as 'status_color',
                (SELECT COUNT(*) FROM piece_jointes pj WHERE pj.sav_id = sav.id) as nb_pj,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email'
                FROM sav
                LEFT JOIN sites st on st.idsite = sav.site_id
                LEFT JOIN users us on us.id = sav.user_id
                LEFT JOIN `status` stat on stat.name = sav.status 
                WHERE sav.type_sav = ? " . $search['query_and'], [$type]);
        }
        else if(in_array($request->user()->role, ['superviseur','resp_sup','resp_op'])){
            $sav = DB::select("SELECT sav.id, sav.user_id, sav.site_id, sav.motif, sav.status, sav.created_at,
                stat.description as 'status_description', stat.color as 'status_color',
                (SELECT COUNT(*) FROM piece_jointes pj WHERE pj.sav_id = sav.id) as nb_pj,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email'
                FROM sav
                LEFT JOIN sites st on st.idsite = sav.site_id
                LEFT JOIN users us on us.id = sav.user_id
                LEFT JOIN `status` stat on stat.name = sav.status
                where sav.type_sav = ? and sav.user_id = ? " . $search['query_and']
                , [$type, $request->user()->id]);
        }
        else if(in_array($request->user()->role, ['room'])){
            $sav = DB::select("SELECT sav.id, sav.user_id, sav.site_id, sav.motif, sav.status, sav.created_at,
                stat.description as 'status_description', stat.color as 'status_color',
                (SELECT COUNT(*) FROM piece_jointes pj WHERE pj.sav_id = sav.id) as nb_pj,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email'
                FROM sav
                LEFT JOIN sites st on st.idsite = sav.site_id
                LEFT JOIN users us on us.id = sav.user_id
                LEFT JOIN `status` stat on stat.name = sav.status
                where sav.type_sav = ? and us.role = 'room' and sav.created_at >= ? " . $search['query_and']
                , [$type, $this->getDateLimit()]);
        }
        else 
            return response(["error" => "EACCES"]);
        return response(compact('sav'));
    }
    
    protected function validateAndSetSav($request, $sav){
        $auth = $request->user();
        if(in_array($request->user()->role, ['superviseur', 'resp_sup', 'resp_op'])){
            $validator = Validator::make($request->all(), [
                'site_id' => 'required',
                'motif' => 'required',
            ]);
            $sav->superviseur_id = $auth->id;
        }
        else if($auth->role == "room"){
            $validator = Validator::make($request->all(), [
                'site_id' => 'required',
                'motif' => 'required',
                'mesure' => 'required',
            ]);
            $sav->mesure = $request->mesure;
            $sav->superviseur_id = $request->superviseur_id;
        }
        else {
            $validator = Validator::make($request->all(), [
                'site_id' => 'required',
                'motif' => 'required',
                'date_sav' => 'required',
                'technicien' => 'required',
            ]);
            $sav->date_sav = $request->date_sav;
            $sav->technicien = $request->technicien;
            if(!$sav->id == null || $auth->id == $sav->user_id)
                $sav->superviseur_id = $request->superviseur_id;
        }

        if ($validator->fails())
            return ['error' => $validator->errors()->first()];

        if($sav->id == null || $auth->id == $sav->user_id){
            $sav->site_id = $request->site_id;
            $sav->motif = $request->motif;
        }

        if(!in_array($request->type_sav, ["tag", "biometrique", "autre"]))
            return ["error" => "Type de sav incorrect"];

        return ['error' => ''];
    }

    public function store(Request $request){
        $auth = $request->user();
        if(in_array($auth->role, ["tech", "electronique", "superviseur", "resp_sup", "resp_op","room"])){
            $sav = new Sav();
            $sav->type_sav = $request->type_sav;
            $validator = $this->validateAndSetSav($request, $sav);
            if($validator['error'])
                return response($validator);

            $sav->user_id = $auth->id;
            if(in_array($auth->role, ["tech", "electronique"]))
                $sav->status = "traite";
            else
                $sav->status = "demande";

            $sav->created_at = new \DateTime();
            $sav->updated_at = new \DateTime();

            if($sav->save()){
                HistoriqueController::new_sav($request, $sav->id);
                if(in_array($auth->role, ["superviseur", "resp_sup", "resp_op"])){
                    if($sav->type_sav == "biometrique"){
                        $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                            from users us
                            left join users ur on ur.id = us.real_email_id 
                            where us.role in ('electronique', 'tech', 'room') 
                            and us.email not like '%@controlroom.mg'", []);
                    }
                    else {
                        $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                            from users us
                            left join users ur on ur.id = us.real_email_id 
                            where us.role in ('tech', 'room') 
                            and us.email not like '%@controlroom.mg'", []);
                    }
                    
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                    //MailController::sav($request, $sav->id, TypeSav::find($request->type_sav)->description, $emails);
                }
                else if(in_array($auth->role, ["tech", "electronique"])){
                    $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                        from users us
                        left join users ur on ur.id = us.real_email_id 
                        where us.role in ('room') 
                        and us.email not like '%@controlroom.mg'", []);
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                    //MailController::sav($request, $sav->id, "Demande de SAV", $emails);
                }
                return response(["success" => "SAV bien envoyée", "id" => $sav->id]);
            }

            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function do_traite(Request $request, $id){
        $sav = Sav::find($id);
        $old_sav = clone $sav;
        $auth = $request->user();
        if(in_array($auth->role, ["tech", "electronique"]) && $sav->status == "demande"){
            $validator = $this->validateAndSetSav($request, $sav);
            if($validator['error'])
                return response($validator);
            
            $sav->status = "traite";
            $sav->updated_at = new \DateTime();

            if($sav->save()){
                HistoriqueController::update_sav($request, $old_sav, "SAV en cours de traitement");
                return response(["success" => "SAV bien envoyée", "id" => $sav->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function edit(Request $request, $id){
        $sav = Sav::find($id);
        $old_sav = clone $sav;
        $auth = $request->user();
        if(in_array($auth->role, ["tech", "electronique"]) && $sav->status == "traite"){
            $validator = $this->validateAndSetSav($request, $sav);
            if($validator['error'])
                return response($validator);
                
            $sav->updated_at = new \DateTime();

            if($sav->save()){
                HistoriqueController::update_sav($request, $old_sav, "SAV modifié");
                return response(["success" => "SAV modifié", "id" => $sav->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_sav(Request $request, $id){
        $sav = Sav::find($id);
        $auth = $request->user();
        if(
            ($request->user()->id == $sav->user_id && in_array($request->user()->role, ['superviseur', 'resp_sup', "resp_op", 'room']) && in_array($sav->status, ['demande'])) || 
            (in_array($auth->role, ["tech", "electronique"]) && in_array($sav->status, ['demande', 'traite']))
        ){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sav->date_sav = null;
            $sav->technicien = null;
            $sav->status = 'draft';
            $sav->updated_at = new \DateTime();

            $sav->note_id = HistoriqueController::action_sav($request, "SAV annulé", $id);
            if($sav->save()){
                if($sav->superviseur_id && in_array($request->user()->role, ["tech","electronique"])){
                    $superviseur =  DB::select("SELECT u.name, coalesce(u2.email, u.email) as 'email' from users u
                        left join users u2 on u2.id = u.real_email_id
                        WHERE u.id = ?", [$sav->superviseur_id])[0];
                    //MailController::sav($request, $sav->id, "SAV annulé"
                    //    , [["address" => $superviseur->email, "name" => $superviseur->name]]);
                }
                return response(["success" => "SAV annulé", "id" => $sav->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function request_validation(Request $request, $id){
        $sav = Sav::find($id);
        $auth = $request->user();
        if(in_array($auth->role, ["tech", "electronique"]) && in_array($sav->status, ['traite'])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sav->status = 'validation';
            $sav->updated_at = new \DateTime();

            $sav->note_id = HistoriqueController::action_sav($request, "Requête de validation", $id);
            if($sav->save()){
                return response(["success" => "Demande de validation envoyé", "id" => $sav->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function reply_validation(Request $request, $id){
        $sav = Sav::find($id);
        if($request->user()->role == 'validateur' && $sav->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sav->status = 'traite';
            $sav->updated_at = new \DateTime();

            $sav->note_id = HistoriqueController::action_sav($request, "Réponse à la demande", $id);
            if($sav->save()){
                if($sav->type_sav == "biometrique")
                    $users = DB::select("SELECT id from users where role in ('tech', 'electronique')", []);
                else
                    $users = DB::select("SELECT id from users where role = 'tech'", []);
                
                foreach ($users as $u) {
                    Notification::create([
                        'historique_id' => $sav->note_id,
                        'receiver_id' => $u->id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                return response(["success" => "Réponse de la demande envoyé", "id" => $sav->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function cancel_validation(Request $request, $id){
        $sav = Sav::find($id);
        $auth = $request->user();
        if(in_array($auth->role, ["tech", "electronique"]) && $sav->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sav->status = 'traite';
            $sav->updated_at = new \DateTime();

            $sav->note_id = HistoriqueController::action_sav($request, "Demande de validation annulé", $id);
            if($sav->save()){
                return response(["success" => "Demande de validation annulé", "id" => $sav->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $sav = Sav::find($id);
        $old_sav = clone $sav;
        $auth = $request->user();
        if($request->user()->id == $sav->user_id && $sav->status == "draft"){
            $validator = $this->validateAndSetSav($request, $sav);
            if($validator['error'])
                return response($validator);
            if(in_array($auth->role, ["tech", "electronique"]))
                $sav->status = "traite";
            else
                $sav->status = "demande";
            $sav->updated_at = new \DateTime();

            if($sav->save()){
                HistoriqueController::update_sav($request, $old_sav, "Renvoie de la demande");
                if(in_array($request->user()->role, ["superviseur","resp_sup","resp_op"])){
                    if($sav->type_sav == "biometrique"){
                        $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                            from users us
                            left join users ur on ur.id = us.real_email_id 
                            where us.role in ('electronique', 'tech')", []);
                    } else {
                        $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                            from users us
                            left join users ur on ur.id = us.real_email_id 
                            where us.role in ('tech')", []);
                    }
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                    //MailController::sav($request, $sav->id, "Renvoie de la demande de SAV", $emails);
                }
                return response(["success" => "Renvoie de la demande", "id" => $sav->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function last_sav($id){
        $sav = DB::select("SELECT sav.id, sav.user_id, sav.superviseur_id, sav.site_id, sav.status, sav.created_at, sav.motif, 
        sav.mesure, sav.date_sav, sav.technicien, 
        stat.description as 'status_description', stat.color as 'status_color',
        st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
        FROM sav
        LEFT JOIN sites st on st.idsite = sav.site_id
        LEFT JOIN users us on us.id = sav.user_id
        LEFT JOIN users sup on sup.id = sav.superviseur_id
        LEFT JOIN `status` stat on stat.name = sav.status
        where st.idsite = ? and sav.status != 'draft'
        order by sav.created_at DESC LIMIT 3", [$id]);
        return response($sav);
    }

    public function save_done(Request $request, $id){
        $sav = Sav::find($id);
        $old_sav = clone $sav;
        $auth = $request->user();
        if(in_array($auth->role, ["tech", "electronique"]) && $sav->status == "traite"){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            
            $sav->status = "done";
            $sav->updated_at = new \DateTime();
            if($sav->save()){
                HistoriqueController::action_sav($request, "SAV terminé", $id);
                if(in_array($request->user()->role, ["tech", "electronique"])){
                    if($sav->type_sav == "biometrique"){
                        $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                            from users us
                            left join users ur on ur.id = us.real_email_id 
                            where us.role in ('electronique', 'tech')", []);
                    } else {
                        $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                            from users us
                            left join users ur on ur.id = us.real_email_id 
                            where us.role in ('tech')", []);
                    }
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                    //MailController::sav($request, $sav->id, "SAV terminé", $emails);
                }
                else if($sav->superviseur_id){
                    $superviseur =  DB::select("SELECT u.name, coalesce(u2.email, u.email) as 'email' from users u
                        left join users u2 on u2.id = u.real_email_id
                        WHERE u.id = ?", [$sav->superviseur_id])[0];
                    //MailController::sav($request, $sav->id, "SAV terminé"
                    //    , [["address" => $superviseur->email, "name" => $superviseur->name]]);
                }
                return response(["success" => "SAV Terminé", "id" => $sav->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }
}
