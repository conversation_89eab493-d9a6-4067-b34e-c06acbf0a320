import axios from 'axios'
import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import { useLocation, useNavigate } from 'react-router-dom';

export default function EtapeModal({useLink, updateData, closeModal, juridique}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [etapes, setEtapes] = useState([])
    const [error, setError] = useState("")
    
    const handleSelectStatus = (stat) => {
        if(useLink) {
            let params = new URLSearchParams(location.search)
            params.set("etape_id", stat.id)
            navigate(location.pathname + "?" + params)
        }
        else {
            setError("")
            if(juridique.order < stat.order){
                const data = new FormData() 
                data.append("etape_id", stat.id)
                axios.post("/api/juridique/update_status/" + juridique.id, data, useToken())
                .then((res) => {
                    updateData()
                    closeModal()
                })
                .catch((err) => {
                    console.error(err)
                })
            }
            else {
                setTimeout(() => {
                    setError("Etape antérieur, il faut faire une demande spécifique.")
                }, 500)
            }
        }
    }

    useEffect(() => {
        let isMounted = true
        axios.get('/api/etape_contentieux', useToken())
        .then((res) => {
            if(isMounted) setEtapes(res.data)
        })
        return () => {isMounted = false}
    }, [])

    return <div className='modal'>
        <div>
            <h2>Status</h2>
            <div className='list-container'>
                <ul>
                    {
                        etapes.map(stat => {
                            return <li key={stat.id} onClick={() => handleSelectStatus(stat)}>
                                <span className={(juridique && stat.order <= juridique.order) ? "secondary" : ""}>
                                    {stat.nom}
                                </span> <span className='secondary'>
                                    - {stat.duration} j
                                </span>
                            </li>
                        })
                    }
                </ul>
            </div>
            {
                error &&
                <div className='container-error'>
                    {error}
                </div>
            }
            <div className='form-button-container'>
                <button onClick={closeModal}>Annuler</button>
            </div>
        </div>
    </div>
}