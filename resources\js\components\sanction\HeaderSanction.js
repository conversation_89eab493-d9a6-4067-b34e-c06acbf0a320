import moment from 'moment';
import React from 'react';
import matricule from '../util/matricule';

export default function HeaderSanction({auth, data}) {
    return (
        <>
            <h3>
                {matricule(data)} {data.employe} 
            </h3>
            Site : <span className='text'>{data.site}</span>
            <p style={{whiteSpace: "pre-line"}}>
                Motif : <span className='text'>{data.motif}</span>
            </p>
            <div className='card-footer'>
                <span>
                    <span>Demandeur : </span>
                    <span className='text'>{data.user_nom} {" <" + data.user_email + ">"}</span>
                </span>
                <span className='secondary'>{moment(data.created_at).from(auth.datetime)}</span>
            </div>
        </>
    )
}