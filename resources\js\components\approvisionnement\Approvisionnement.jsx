import React, { useEffect, useState } from 'react';
import { Link,useLocation  } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import { FiMoreVertical } from 'react-icons/fi';
import HeaderApprovisionnement from './HeaderApprovisionnement';

export default function Approvisionnement({auth, appros, setAppros, currentId, setCurrentId}) {
    const locationSearch = useLocation().search;
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [isLoading, toggleLoading] = useState(false)

    let searchItems = [
        {label: 'Réference', name: 'reference', type:'number'},
        {label: 'Status', name: 'status', type:'string'},
        {label: 'Objet', name: 'objet', type:'string'},
        {label: 'Article', name: 'da_item', type:'string'},
        {label: 'Réf<PERSON>rence paiement', name: 'da_ref', type:'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
    ]
    if(['validateur', 'daf', 'compta', 'achat'].includes(auth.role)){
        searchItems.push({label: 'Demandeur', name: 'user_id', type:'number'})
    }
        
    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            params.set("offset", 0)
        }
        else
            params.set("offset", appros.length)
        axios.get('/api/approvisionnement' + '?' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setAppros(res.data.approvisionnements)
                    else {
                        const list = appros.slice().concat(res.data.approvisionnements)
                        setAppros(list)
                    }
                    setDataLoaded(res.data.approvisionnements.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {updateData(true)}, [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>Approvisionnement</h2>
                <Link className='btn btn-primary' to="/da/add">Nouveau</Link>
            </div>
            <SearchBar listItems={searchItems}/>
            {
                appros.length == 0 ?
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                    <InfiniteScroll
                        dataLength={appros.length}
                        next={fetchMoreData}
                        hasMore={!allDataLoaded}
                        loader={<LoadingPage/>}
                    >
                        {
                            ((new URLSearchParams(locationSearch)).get("da_item") || (new URLSearchParams(locationSearch)).get("da_ref")) ? (
                                appros.map((ap, index) => (
                                    <div className={`card-container ${currentId && currentId == ap.id ? 'selected' : ''}`} key={index}>
                                        <div className='badge-container'>
                                            <span>
                                                <span className={'badge-outline badge-outline-' + ap.status_color}>
                                                    {ap.status_description}
                                                </span> {
                                                    ap.nb_pj > 0 &&
                                                    <span className='badge-outline'>
                                                        Pièce jointe : {ap.nb_pj}
                                                    </span>
                                                }
                                            </span>
                                            <span className='pointer' onClick={() => setCurrentId(ap.id)}>
                                                <FiMoreVertical size={20} color="#888"/>
                                            </span>
                                        </div>
                                        <HeaderApprovisionnement auth={auth} data={ap}/>
                                    </div>
                                ))
                            ) 
                            
                            : (
                                appros.map((ap, index) => (
                                    <div className={`card-container ${currentId && currentId == ap.id ? 'selected' : ''}`} key={index}>
                                        <div className='badge-container'>
                                            <span>
                                                <span className={'badge-outline badge-outline-' + ap.status_color}>
                                                    {ap.status_description}
                                                </span> {
                                                    ap.nb_pj > 0 &&
                                                    <span className='badge-outline'>
                                                        Pièce jointe : {ap.nb_pj}
                                                    </span>
                                                }
                                            </span>
                                            <span className='pointer' onClick={() => setCurrentId(ap.id)}>
                                                <FiMoreVertical size={20} color="#888"/>
                                            </span>
                                        </div>
                                        <HeaderApprovisionnement auth={auth} data={ap}/>
                                    </div>
                                ))
                            ) 
                        }
                    </InfiniteScroll>
            }
        </div>
    } </>
}