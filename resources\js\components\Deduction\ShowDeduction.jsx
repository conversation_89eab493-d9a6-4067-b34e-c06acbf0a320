import React, { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import LoadingPage from '../loading/LoadingPage';
import useToken from '../util/useToken';
import ShowHeader from '../view/ShowHeader';

import numberUtil from "../util/numberUtil";
import moment from 'moment/moment';
import ActionDeduction from './ActionDeduction';
import Tab from '../layout/Tab';
import matricule from '../util/matricule';

export default function ShowDeduction({ auth, currentId, setCurrentId, setCurrentItem, size }) {
    const params = useParams()
    const [deduction, setDeduction] = useState();
    const [isLoading, toggleLoading] = useState(false);
    const [defautUsers, setDefautUsers] = useState([])
    const updateData = () => {
        toggleLoading(true);
        let isMounted = true;
            axios.get("/api/deduction/show/" + (currentId ? currentId : params.id), useToken()).then((res) => {
                if (isMounted) {
                    setDeduction(res.data)
                    if (auth.id != res.data.user_id)
                        setDefautUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
                    else setDefautUsers([])
                    toggleLoading(false);
                }
            }).catch((e) => {
                console.error(e)
            })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if (setCurrentItem) setCurrentItem(deduction);
    }, [deduction]);

    useEffect(() => updateData(), [currentId]);

    return (
        <>
            {
                (isLoading || !deduction) ? 
                    <LoadingPage /> 
                : 
                    <>
                        <ShowHeader label="Déduction" id={deduction.id} closeDetail={()=>setCurrentId()} size={size}/>
                        <div className="card-container">
                            <div className="badge-container">
                                <span>
                                    {
                                        deduction.paie_id ?
                                            <span className="badge-outline badge-outline-green">Déduit</span>
                                        :
                                            <span className={"badge-outline badge-outline-" + (deduction.status_color)}>
                                                {deduction.status_description}
                                            </span>
                                    } {
                                        deduction.nb_pj > 0 &&
                                        <span className="badge-outline">
                                            Pièce jointe : {deduction.nb_pj}
                                        </span>
                                    }
                                </span>
                            </div>
                            <h3>
                                {matricule(deduction)} {deduction.employe}
                            </h3>
                            {
                                (deduction.status == "done" && deduction.date_paie) &&
                                <div>
                                    Fiche de paie : <span className='text capitalize'>{moment(deduction.date_paie).format("MMMM YYYY")}</span>
                                </div>
                            }
                            <div>
                                Montant : <span className='text'>{numberUtil(deduction.montant)}</span>
                            </div>
                            <p>
                                Motif : <span className='text'>{deduction.motif}</span>
                            </p>
                            <div>
                                Demandeur : <span className='text'> 
                                    {deduction.user_nom} {' <' + deduction.user_email + '>'}
                                </span>
                            </div>
                            <div className='card-action'>
                                <ActionDeduction auth={auth} deduction={deduction} updateData={updateData} />
                            </div>
                        </div>
                        <Tab auth={auth} name="deduction_id" value={deduction.id} data={deduction} updateData={updateData} defautUsers={defautUsers} />
                    </>
            }
        </>
    )
}

