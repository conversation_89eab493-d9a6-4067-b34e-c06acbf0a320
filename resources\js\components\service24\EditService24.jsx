import React, { useEffect, useState } from 'react'
import Notification from '../notification/Notification';
import InputAgent from '../input/InputAgent';
import Textarea from '../input/Textarea';
import ButtonSubmit from '../input/ButtonSubmit';
import InputDatePointage from '../input/InputDatePointage';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import moment from 'moment';
import { useParams } from 'react-router-dom';
import InputText from '../input/InputText';
// import DualContainer from '../container/DualContainer';;
moment.locale('fr')

export default function EditService24({action, title, auth }) {
    const [notification, setNotification] = useState()
    const [employe, setEmploye] = useState(null)
    const [error, setError] = useState('')
    const [submitDisabled, disableSubmit] = useState(false)
    const [motif, setMotif] = useState("")
    // const [superviseur, setSuperviseur] = useState(null);
    const [datePointage, setDatePointage] = useState()
    // const [beginService, setBeginService] = useState()
    // const [endService, setEndService] = useState()
    const params = useParams()
    
    useEffect(() => {
        let isMounted = true;
        if (params.id) { 
            axios.get('/api/service24/show/' + params.id, useToken()).then((res) => { 
                if (isMounted) {
                    if (res.data) {
                        let service = res.data;
                        setEmploye({
                            id: service.employe_id,
                            nom: service.employe,
                            matricule: matricule(service)
                        })
                        setMotif(service.motif)
                        setDatePointage(service.date_pointage)
                        // setEndService(service.end_pointage)
                    }  
                }
            })
        }
    
      return () => {isMounted = false;}
    }, [])
    
    const handleSubmit = (e) => {
        e.preventDefault();
        disableSubmit(true);
        const data = {
            employe_id: (employe ? employe.id : null),
            motif: motif,
            date_pointage: datePointage,
            // begin_pointage: beginService,
            // end_pointage: endService,
        }
        axios.post(action + (params.id ? ("" + params.id) : ''), data, useToken())
        .then((res) => {
            disableSubmit(false);
            if (res.data.error) 
                setError(res.data.error)
            else if (res.data.success) {
                setNotification(res.data)
            }
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
        
    }
    return (
        <div id='content'>
            <div>
                {
                    notification ? 
                        <Notification next={notification.id ? "/service24?id=" + notification.id : "/service24"} message={notification.success} />
                    :
                        <form onSubmit={handleSubmit}>
                            <div className='title-container'>
                                <h2>{title}</h2>
                            </div>
                            <InputAgent
                                required
                                value={employe}
                                onChange={(value) => setEmploye(value)}
                            />
                            <InputDatePointage
                                required
                                label="Date de service"
                                value={datePointage}
                                onChange={(value) => setDatePointage(value)}
                            />
                            <InputText label="Motif" value={motif} onChange={(value) => setMotif(value)} maxLength={20} required />
                            <div>
                                {
                                    error && <div className="container-error">{error}</div>
                                }
                            </div>
                            <ButtonSubmit label="Enregistrer" disabled={submitDisabled} />
                        </form>    
                }
            </div>
        </div>
    )
}
