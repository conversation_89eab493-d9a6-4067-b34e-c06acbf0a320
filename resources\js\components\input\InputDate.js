import React from 'react';
import DatePicker, { registerLocale } from "react-datepicker";
import fr from "date-fns/locale/fr"; // the locale you want
registerLocale("fr", fr); // register it with the name you want
import moment from 'moment';

import "react-datepicker/dist/react-datepicker.css";

export default function InputDate({label, noLabel, value, onChange, required, isDateNaiss, format}) {

    const getMinYear = () => {
        const retraite = 60
        const minYear = moment().year() - retraite
        return minYear
    }

    const getMaxYear = () => {
        const majeur = 18
        const maxYear = moment().year() - majeur
        return maxYear
    }

    return <div className='input-container'>
        {!noLabel && <label>{label ? label : "Date"} {required && <span className='danger'>*</span>}</label>}
        <DatePicker
            selected={value}
            onChange={(date) => onChange(date)}
            locale="fr"
            dateFormat={format ? format : "dd/MM/yyyy"}
            className='input-date'
            showYearDropdown = {isDateNaiss ? isDateNaiss : false}
            showMonthDropdown = {isDateNaiss ? isDateNaiss : false}
            dropdownMode="select"
            minDate={isDateNaiss ? new Date(getMinYear(), 0, 1) : undefined}
            maxDate={isDateNaiss ? new Date(getMaxYear(), 11, 31) : undefined}
        />
    </div>
}