import axios from 'axios'
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useToken from '../util/useToken';

export default function ServiceModal({onChange, closeModal, useLink}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [services, setServices] = useState([])

    
    const handleSelectService = (sv) => {
        if(useLink) {
            let params = new URLSearchParams(location.search)
            params.set("service_id", sv.id)
            navigate(location.pathname + "?" + params)
        }
        onChange(sv)
        closeModal()
    }

    useEffect(() => {
        let isMounted = true
        axios.get('/api/service', useToken())
        .then((res) => {
            if(isMounted) setServices(res.data)
        })
        return () => {isMounted = false}
    }, [])

    return <div className='modal'>
        <div>
            <h2>Status</h2>
            <div className='list-container'>
                {
                    <ul>
                        {
                            services.map(sv => {
                                return <li key={sv.id} onClick={() => handleSelectService(sv)}>
                                    {sv.designation}<br/>
                                </li>
                            })
                        }
                    </ul>
                }
            </div>
            <div className='form-button-container'>
                <button onClick={closeModal}>Annuler</button>
            </div>
        </div>
    </div>
}