import React from "react";
import LoadingPage from "../loading/LoadingPage";
import { useState, useEffect } from "react";
import axios from "axios";
import useToken from "../util/useToken";
import moment from "moment";
import './paie.css';

function Salaire({ type, id, updateData, auth }) {
    const [isLoading, toggleLoading] = useState(true);
    const [paie, setPaie] = useState([]);
    const [pointageReclamation, setPointageReclamation] = useState([]);
    const [totalHeure, setTotalHeure] = useState(0);
    const [detectPrime, setDetectPrime] = useState(0);

    const getPaie = () => {
        toggleLoading(true)
        axios.get('/api/paie?id=' + id + "&offset=0", useToken()).then((response) => {
            if (response.data.paies) {
                let res = response.data.paies[0]
                setDetectPrime(res.partVariable + res.perdiem + res.idmDepl +
                    res.primeAnc + res.prime_exceptionnelle + res.prime_div +
                    res.prime_assid + res.prime_resp + res.prime_entret + res.idm_licenciement +
                    res.prime)
                setPaie(response.data.paies[0])
            }
            toggleLoading(false)
        })
    }

    const getReclamation = () => {
        axios.get('/api/paie/get_reclamation/' + id, useToken()).then((res) => {
            if (res.data.error)
            console.error(res.data.error)
        else if (res.data) {
                setPointageReclamation(res.data)
                let total = 0
                res.data.map(p => {
                    total += 12
                })
                setTotalHeure(total)
            }
        })
    }

    useEffect(() => {
        getPaie();
        getReclamation();
    }, [id]);

    const NbWithSpace = (number = 0) => {
        try {
            if (number == null) {
                return "0 Ar";
            }
            return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ") + " Ar";
        } catch (error) {
            console.error("Error");
        }
    } 
    return (
        <>
            {isLoading ? (
                <LoadingPage />
            ) : (
                <div>
                    {paie && type == "salaire" && (
                        <div className="salaire">
                            <div >
                                <span>Salaire de base</span>
                                <span>{NbWithSpace(paie.sal_base)}</span>
                            </div>
                            <div>
                                <span>Net a payer</span>
                                <span>{NbWithSpace(paie.net_a_payer.toFixed(Number.isInteger(paie.net_a_payer) ? 0 : 2))}</span>
                            </div>
                            <div >
                                <span>Salaire Mensuel</span>
                                <span>{NbWithSpace(paie.salaire_mensuel.toFixed(Number.isInteger(paie.salaire_mensuel) ? 0 : 2))}</span>
                            </div>
                            <div >
                                <span>Salaire brut</span>
                                <span>{NbWithSpace(paie.salaire_brut.toFixed(Number.isInteger(paie.salaire_brut)?0:2))}</span>
                            </div>
                            <div className="">
                                <span>Masse Salariale</span>
                                <span>{NbWithSpace(paie.masse_salariale.toFixed(Number.isInteger(paie.masse_salariale)?0:2))}</span>
                            </div>
                        </div>
                    )}
                    {paie && type == "prime" && (
                        <div className="salaire">
                            {paie.partVariable != (null || 0) && 
                                <div>
                                    <span>Part Variable</span>
                                    <span>{NbWithSpace(paie.partVariable)}</span>
                                </div>
                            }       
                            {(paie.perdiem != null && paie.perdiem != 0) && 
                                <div>
                                    <span>Perdiem</span>
                                    <span>{NbWithSpace(paie.perdiem)}</span>
                                </div>
                            }
                                    
                            {(paie.idmDepl != null && paie.idmDepl != 0) && (
                                <div >
                                    <span>Indemnite de deplacement</span>
                                    <span>{NbWithSpace(paie.idmDepl)}</span>
                                </div>
                            )}
                            
                            {(paie.primeAnc != null && paie.primeAnc != 0) && 
                                <div >
                                    <span>Prime Ancienneté</span>
                                    <span>{NbWithSpace(paie.primeAnc)}</span>
                                </div>
                            }
                            {(paie.prime_exceptionnelle != null &&
                                paie.prime_exceptionnelle != 0) && 
                                    <div >
                                        <span>Exceptionnelle</span>
                                        <span>{NbWithSpace(paie.prime_exceptionnelle)}</span>
                                    </div>
                            }
                            
                            {(paie.prime_div != null && paie.prime_div != 0) && (
                                <div >
                                    <span>Prime div</span>
                                    <span>{NbWithSpace(paie.prime_div)}</span>
                                </div>
                            )}
                                    
                            {(paie.prime_assid != null &&
                                paie.prime_assid != 0) && (
                                    <div >
                                        <span>Prime Assid</span>
                                        <span>{NbWithSpace(paie.prime_assid)}</span>
                                    </div>
                            )}
                                    
                            {(paie.prime_resp != null &&
                                paie.prime_resp != 0) && (
                                    <div >
                                        <span>Responsabilité</span>
                                        <span>{NbWithSpace(paie.prime_resp)}</span>
                                    </div>
                            )}
                            
                            {(paie.prime_entret != null &&
                                paie.prime_entret != 0) && (
                                    <div >
                                        <span>Entretien</span>
                                        <span>{NbWithSpace(paie.prime_entret)}</span>
                                    </div>
                            )}
                                
                            {(paie.idm_licenciement != null &&
                                paie.idm_licenciement != 0) && (
                                    <div >
                                        <span>Licenciement</span>
                                        <span>{NbWithSpace(paie.idm_licenciement)}</span>
                                    </div>
                            )}
                            {(paie.prime != null && paie.prime != 0) && (
                                <div >
                                    <span>Prime</span>
                                    <span>{NbWithSpace(paie.prime)}</span>
                                </div>
                            )}
                                
                            {(detectPrime == 0 )&& (
                                <div className="card-container secondary">
                                    Aucune prime                                    
                                </div>
                            )}
                        </div>
                        )}
                        {paie && type == "deduction" && (
                            <div className="salaire">
                                <div >
                                    <span>Deduction</span>
                                    <span>{ NbWithSpace(paie.autre_deduction) }</span>
                                </div>    
                                   
                                <div >
                                    <span>Retenue formation</span>
                                    <span>{ NbWithSpace(paie.retenue_formation) }</span>
                                </div>    
                            </div>   
                        )}
                        {paie && type == "avance" && (
                            <div className="salaire">
                                <div>
                                    <span>Avance 15é</span>
                                    <span>{NbWithSpace(paie.avance_15e)}</span>
                                </div>
                                <div>
                                    <span>Avance spéciale</span>
                                    <span>{NbWithSpace(paie.avance_special)}</span>
                                </div>
                                <div>
                                    <span>Avance Embauche</span>
                                    <span>{NbWithSpace(paie.avance_speciale_embauche)}</span>
                                </div> 
                            </div>
                        )}
                        {pointageReclamation && type == "reclamation" && (
                            <div>

                                <div className='line-container'>
                                    <div className='header-pointage'>
                                        <h3>
                                            Heure réclamé : {totalHeure}
                                        </h3>

                                    </div>
                                </div>
                                {pointageReclamation.map(p => <div key={p.id} className='line-container'>
                                    <div className='pointage-container'>
                                        <div>
                                            {
                                                moment(p.date_pointage).format("DD MMM YYYY") 
                                                + " " + (moment(p.date_pointage).format("HH:mm:ss") == "07:00:00" ? "JOUR" : "NUIT")
                                            }<br/>
                                            <span className='secondary'>
                                                {p.site}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                )}  
                            </div>
                        )}                      
                </div>
            )}
        </>
    );
}

export default Salaire;
