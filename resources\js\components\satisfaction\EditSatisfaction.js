import React, { useEffect, useState } from 'react'
import Textarea from '../input/Textarea'
import InputSite from '../input/InputSite'
import ButtonSubmit from '../input/ButtonSubmit'
import axios from 'axios'
import Notification from '../notification/Notification'
import { useParams } from 'react-router-dom'
import useToken from '../util/useToken'
import LoadingPage from '../loading/LoadingPage'
import InputMultipleFile from '../input/InputMultipleFile'

export default function EditSatisfaction({ title, action }) {
    const [isLoading, toggleLoading] = useState(false)
    const [comment, setComment] = useState('')
    const [site, setSite] = useState()
    const [error, setError] = useState('')
    const [disabledSubmit, setDisabledSubmit] = useState(false)
    const [notification, setNotification] = useState(null)
    const [files, setFiles] = useState([])
    const params = useParams()

    const handleSubmit = (e) => {
        e.preventDefault()
        setDisabledSubmit(true)
        toggleLoading(true);
        let data = new FormData()
        if (site) data.append("site_id", site.id)
        data.append("comment", comment)
        files.forEach((file, index) => {
            data.append(`files[${index}]`, file);
        });
        axios.post(action + (params.id ? params.id : ''), data, useToken())
        .then((res) => {
            if (res.data.success) {
                setNotification(res.data)    
            }
            else if (res.data.error) {
                setError(res.data.error)
            }
            setDisabledSubmit(false)
        })
        .finally(() => { 
            setDisabledSubmit(false)
        })
        toggleLoading(false);
    }

    const getSatisfaction = () => { 
        toggleLoading(true)
        let isMounted = true;
        axios.get("/api/satisfaction/show/" + params.id, useToken()).then((res) => {
            if (isMounted) {
                if (res.data.satisfaction) {
                    let satisfaction = res.data.satisfaction;
                    setSite({
                        id: satisfaction.site_id,
                        adresse: satisfaction.site_adresse,
                        nom: satisfaction.site_nom
                    })
                    setComment(satisfaction.comment)
                }
            }
            toggleLoading(false)
        })
    }

    useEffect(() => params.id && getSatisfaction(), [])

    return (
        <div id='content'>
            {
                isLoading ?
                    <LoadingPage />
                :
                    <div>
                        {
                            notification ?
                                <Notification next={notification.id ? "/satisfaction?id=" + notification.id : "/satisfaction"} message={notification.success} />
                            :
                                <form onSubmit={handleSubmit} encType="multipart/form-data">
                                    <div className='title-container'>
                                        <h2>{title}</h2>
                                    </div>
                                    <InputSite withoutDelete label="Site" value={site} onChange={setSite} required />
                                    <Textarea label="Commentaire" value={comment} onChange={(value) => setComment(value)} required/>
                                    <InputMultipleFile files={files} setFiles={setFiles} />
                                    <div>
                                        {
                                            error && <div className='container-error'>{error}</div>
                                        }
                                    </div>
                                    <ButtonSubmit label="Envoyer" disabled={disabledSubmit} />
                                </form>
                        }
                    </div>
            }
        </div>
    )
}
