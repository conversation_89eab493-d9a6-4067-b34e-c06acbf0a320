const axios = require('axios');
const FormData = require('form-data');
const moment = require('moment');
const { performance } = require('perf_hooks');
const mysql = require('mysql');

moment.locale('fr');

const db_config = {
	host: "127.0.0.1",
	port: "3306",
	user: "root",
	database: "admin",
	password: ""
}
const pool = mysql.createPool(db_config)

console.log("Generate paie");
const tokenAdminTest = "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
let all_time_spent = 0;
const startTimeExecution = performance.now();
const date_paie = moment().format('YYYY-MM') + "-20";

date_begin = moment().subtract(1, 'M').format('YYYY-MM') + "-20 00:00:00"
date_end = moment().format('YYYY-MM') + "-19 23:59:59"
const sqlSelectPointage = "SELECT id, date_pointage, site_id, employe_id FROM pointages ptg "
    + " WHERE (ptg.soft_delete is null or ptg.soft_delete = 0) "
    + " AND (ptg.date_pointage  >='" + date_begin + "' and ptg.date_pointage <= '" + date_end + "')";

const sqlSelectEmploye = "SELECT emp.id as 'employe_id', emp.* FROM employes emp";

const sqlSelectLastPaie = "SELECT employe_id, date_paie FROM paies where date_paie = '" + date_paie + "'";

const sqlSelectAvance = "SELECT employe_id, SUM(montant) as 'montant', avc.type_avance_id, type_avc.name " 
    + " FROM avances avc " 
    + " LEFT JOIN type_avances type_avc on type_avc.id = avc.type_avance_id "
    + " WHERE avc.status = 'done' "
    + " AND date_paie = '" + date_paie + "'"
    + " GROUP BY employe_id, avc.type_avance_id";

const sqlSelectPartVariable = "SELECT cm.id, cm.part_variable_id, cm.montant, pv.employe_id " 
    + " FROM critere_mensuels cm "
    + " LEFT JOIN part_variables pv ON pv.id = cm.part_variable_id "
    + " WHERE pv.status = 'done' "
    + " AND pv.date_paie = '" + date_paie + "'";

const sqlSelectDeduction = "SELECT employe_id, SUM(montant) as total_montant "
    + " FROM deductions "
    + " WHERE date_paie = '" + date_paie + "'" 
    + " AND status = 'done' "
    + " GROUP BY employe_id";

const sqlSelectPrime = "SELECT employe_id, SUM(montant) as total_montant "
    + " FROM primes "
    + " WHERE date_paie = '" + date_paie + "'" 
    + " AND status = 'done' "
    + " GROUP BY employe_id";

const sqlSelectCongePaye = "SELECT id, type_absence, employe_id, depart, retour, date_paie, status FROM absences "
    + " WHERE (type_absence = 'conge' or type_absence = 'permission') "
    + " AND date_paie = '" + date_paie + "'"
    + " GROUP BY employe_id, depart, retour";

const sqlCongeDone = "SELECT id, employe_id, depart, retour, type_absence "
    + " FROM absences ab "
    + " WHERE type_absence = 'conge' AND ab.status = 'done'";

function groupingData(datas){
    const result = datas.reduce((acc, item) => {
        if (!item.employe_id) {
            console.log("employe_id not defined")
        }
        const employeId = item.employe_id;
        if(!acc[employeId]) {
          acc[employeId] = [];
        }
        acc[employeId].push(item);
        return acc;
    }, {});
    return result;
}

function groupPointage(pointages){
    const result = pointages.reduce((acc, pointage) => {
        const employeId = pointage.employe_id;
        acc[employeId] = (acc[employeId] || 0) + 1;
        return acc;
    }, {});
    return result;
}

function groupData(pointages, avances, parts, deductions, primes, conges, congeDones, lastPaies, employes) {
    const pointageCounts = groupPointage(pointages);
    const groupAvances = groupingData(avances);
    const groupParts = groupingData(parts);
    const groupDeductions = groupingData(deductions);
    const groupPrimes = groupingData(primes);
    const groupConges = groupingData(conges);
    const groupCongeDone = groupingData(congeDones);
    const groupLastPaie = groupingData(lastPaies);
    // const groupEmployes = groupingData(employes)
    const groupedData = {};

    Object.keys(pointageCounts).forEach(employeId => {
        groupedData[employeId] = {
            pointages: pointageCounts[employeId],
            avances: [],
            parts: [],
            deductions: [],
            primes: [],
            conges: [],
            congeDones: [],
            // lastPaie: [],
            employe: [],
            paie: []
            
        };
    });

    const dataSources = {
        avances: groupAvances,
        parts: groupParts,
        deductions: groupDeductions,
        primes: groupPrimes,
        conges: groupConges,
        congeDones: groupCongeDone,
        // lastPaies: groupLastPaie,
        // employes: groupEmployes
    };

    Object.keys(dataSources).forEach(category => {
        const groupedCategory = dataSources[category];
        Object.keys(groupedCategory).forEach(employeId => {
            if (!groupedData[employeId]) {
                groupedData[employeId] = {
                    pointages: 0,
                    avances: [],
                    parts: [],
                    deductions: [],
                    primes: [],
                    conges: [],
                    congeDones: [],
                    // lastPaies: [],
                    // employes : []
                };
            }
            // if(employeId == 4401)
            if(!groupedData[employeId]['employe'] || groupedData[employeId]['employe'].length == 0){
                groupedData[employeId]['employe'] = (employes.filter(emp => parseInt(emp.id) == parseInt(employeId)));
            }
            if(!groupedData[employeId]['paie'] || groupedData[employeId]['paie'].length == 0){
                groupedData[employeId]['paie'] = (lastPaies.filter(p => parseInt(p.employe_id) == parseInt(employeId)));
            }
            groupedData[employeId][category] = groupedCategory[employeId];
            
        });
    });

    // Object.keys(groupEmployes).forEach(employeId => {
    //     if (groupedData[employeId]) {
    //         groupedData[employeId]['employes'] = groupEmployes[employeId];
    //     }
    // });
    // console.log(groupedData)
    return groupedData;
}



// function groupingData(datas, acc = {}, index = 0) {
//     if (index >= datas.length) {
//         return acc;
//     }
//     const employeId = datas[index].employe_id;
//     const item = datas[index];
//     if (!acc[employeId]) {
//         acc[employeId] = [];
//     }
//     acc[employeId].push(item);
//     return groupingData(datas, acc, index + 1);
// }

// function groupPointage(pointages, acc = {}, index = 0) {
//     if (index >= pointages.length) {
//         return acc;
//     }
//     const employeId = pointages[index].employe_id;
//     acc[employeId] = (acc[employeId] || 0) + 1;
//     return groupPointage(pointages, acc, index + 1);
// }

function generatePaie(){
    pool.query(sqlSelectPointage, [], async (err, pointages) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb pointage: " + pointages.length)
            pool.query(sqlSelectAvance, [], async (err, avances) => {
                if(err)
                    console.error(err)
                else {
                    pool.query(sqlSelectEmploye, [], async (err, employes) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Nb employe: " + employes.length)
                            pool.query(sqlSelectLastPaie, [], async (err, lastPaies) => {
                                if(err)
                                    console.error(err)
                                else {
                                    console.log("Nb last_paie: " + lastPaies.length)
                                    pool.query(sqlSelectPartVariable, [], async (err, partVariables) => {
                                        if(err)
                                            console.error(err)
                                        else {
                                            console.log("Nb part variable: " + partVariables.length)
                                            pool.query(sqlSelectDeduction, [], async (err, deductions) => {
                                                if(err)
                                                    console.error(err)
                                                else {
                                                    console.log("Nb deduction: " + deductions.length)
                                                    pool.query(sqlSelectPrime, [], async (err, primes) => {
                                                        if(err)
                                                            console.error(err)
                                                        else {
                                                            console.log("Nb prime: " + primes.length)
                                                            pool.query(sqlSelectCongePaye, [], async (err, congePayes) => {
                                                                if(err)
                                                                    console.error(err)
                                                                else {    
                                                                    console.log("Nb conge paye: " + congePayes.length)
                                                                    pool.query(sqlCongeDone, [], async (err, congeDones) => {
                                                                        if(err)
                                                                            console.error(err)
                                                                        else {
                                                                            console.log("Nb conge done: " + congeDones.length)
                                                                            const data = new FormData();
                                                                            const datas = groupData(pointages, avances, partVariables, deductions, primes, congePayes, congeDones, lastPaies, employes);
                                                                            // console.log('datas',datas)
                                                                            data.append("datas", JSON.stringify(datas));
                                                                            data.append("date_paie", date_paie);
                                                                            console.log('loading paie...');
                                                                            const startTime = performance.now();
                                                                            axios.post("http://127.0.0.1:8000/api/paie/generate_paie", data, {
                                                                                headers:
                                                                                    {
                                                                                        ...data.getHeaders(),
                                                                                        'Authorization': 'Bearer ' + tokenAdminTest
                                                                                    }
                                                                            }).then((res) => {
                                                                                const endTime = performance.now();
                                                                                console.log('Response', res);
                                                                                const executionTime = endTime - startTime;
                                                                                all_time_spent += executionTime;
                                                                                console.log(`Query execution time: ${executionTime.toFixed(2)} ms`);
                                                                                console.log(`Time passes: ${((endTime - startTimeExecution)/60000).toFixed(2)} minutes`);
                                                                                if(res.data.success){
                                                                                    console.log('...Payroll generated...');
                                                                                }
                                                                            }).catch((err) => {
                                                                                console.error('Error:', err);
                                                                            });
                                                                        }
                                                                    });
                                                                }
                                                            });
                                                        }
                                                    });
                                                }
                                            });
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
            });
        }
    });
}

generatePaie()


// public function generate_test(Request $request){
//     if ($request->user()->role == 'resp_rh'|| $request->user()->id == '191') {
//         $datas = $request->input('datas');
//         $datePaie = $request->input('date_paie');
//         $datas = json_decode($datas, true);

//         foreach ($datas as $key => $value) {
//             $employeId = $key;
//             $paie = $value["paie"];
//             $employe = $value["employe"];
//             if(!$paie && $employeId){
//                 if(count($employe) == 0)
//                     return $value;
//              if ($employe[0]["employe_id"] != $employeId){
//                     return $employe;
//                 }
//             }
//         }
//         foreach ($datas as $key => $value) {
//             if (is_null($key)) {
//                 return "La clé est nulle";
//             }
//             $employeId = $key;
//             $avances = $value["avances"];
//             $deductions = $value["deductions"];
//             $primes = $value["primes"];
//             $part_variables = $value["parts"];
//             $conge_dones = $value["congeDones"];
//             $paie = $value["paie"];
//             // $paie = Paie::where('employe_id', $employeId)->where('date_paie', $datePaie)->first();
//             if(!$paie && $employeId){
//                 $employe = $value["employe"];
//                 if(count($employe) == 0 || $employe[0]["employe_id"] != $employeId){
//                     $employe = Employe::find($employeId);
//                 }
//                 else {
//                     $employe = $employe[0];
//                 }
//                 $values[$employeId]["avance15"] = 0;
//                 $values[$employeId]["avc_embauche"] = 0;
//                 $values[$employeId]["avc_speciale"] = 0;
//                 if(count($avances) > 0){
//                     foreach ($avances as $avc) {
//                         if($avc["name"] == "avance15" ) $values[$employeId]["avance15"] = $avc["montant"];
//                         if($avc["name"] == "avance_embauche" ) $values[$employeId]["avc_embauche"] = $avc["montant"];
//                         if($avc["name"] == "avance_speciale" ) $values[$employeId]["avc_speciale"] = $avc["montant"];
//                     }
//                 }
//                 $ded_amount = 0;
//                 if(count($deductions) > 0 ){
//                     foreach($deductions as $ded)
//                         $ded_amount += $ded["montant"];
//                 }
//                 $prime_amount = 0;
//                 if(count($primes) > 0 ){
//                     foreach($primes as $prime)
//                         $prime_amount += $prime["total_montant"];
//                 }
//                 $part_amount = 0;
//                 if(count($part_variables) > 0 ){
//                     foreach($part_variables as $part)
//                         $part_amount += $part["montant"];
//                 }
//                 $values[$employeId]["part_part_variable"] = $part_amount;
//                 $values[$employeId]["deduction"] = $ded_amount;
//                 $values[$employeId]["nb_heure_travaille"] = $value["pointages"] * 12;
//                 $values[$employeId]["prime"] = $prime_amount;
//                 if(isset($employe["mode_paiment"])){
//                     if($employe["mode_paiment"] == "MOB"){
//                         $values[$employeId]["numero_tel"] = $employe["numero_mobile"];
//                     } else if($employe["mode_paiment"] == "VIR"){
//                         $values[$employeId]["banque"] = $employe["nom_paiement"];
//                     }
//                 }
//                 $values[$employeId]["cin"] = isset($employe->cin_text) ? $employe->cin_text : null;
//                 $nbConge = json_decode($this->calculAbsencePaye($value["conges"]), true);
//                 $values[$employeId]["conge_paye"] = $nbConge['congePaye'];
//                 $values[$employeId]["conge_pris"] = $nbConge['congePris'];
//                 if (count($conge_dones) > 0) {
//                     if (!isset($employe->societe_id) || !$employe->societe_id) {
//                         $values[$employeId]["conge_reste"] = 0;
//                     } else {
//                         $conge_reste = json_decode($this->conge_done($employeId, $employe, $value["congeDones"]), true);
//                         $values[$employeId]["conge_reste"] = $conge_reste['droit'] ?? 0;
//                     }
//                 } 
//                 else
//                     $values[$employeId]["conge_reste"] = 0;
//                 $values[$employeId]["date_paie"] = $datePaie;
//                 // $values[$employeId]["mode_payement"] = $employe->mode_paiement;
//                 $values[$employeId]["date_paie"] = $datePaie;
//                 $values[$employeId]["mode_payement"] = is_array($employe) ? ($employe['mode_paiement'] ?? null) : ($employe->mode_paiement ?? null);
//                 if (is_array($employe)) {
//                     $values[$employeId] = array_merge($values[$employeId], $employe);
//                 } elseif (is_object($employe) && method_exists($employe, 'toArray')) {
//                     $values[$employeId] = array_merge($values[$employeId], $employe->toArray());
//                 } else {
//                     throw new \Exception('Invalid $employe type: must be an array or an object with toArray() method.');
//                 }
//                 $values[$employeId]["status"] = "demande";
//                 $values[$employeId]["user_id"] = $request->user()->id;
//                 $values[$employeId]["created_at"] =  (new \DateTime())->format('Y-m-d H:i:s');
//                 $values[$employeId]["updated_at"] =  (new \DateTime())->format('Y-m-d H:i:s');
//                 $values[$employeId]["employe_id"] = $employeId;
//                 $values[$employeId]["id"] = $employeId;
//                 if (!$employeId) {
//                     return "EmployeId is null";
//                 }
//                 // return $values[$employeId];
//                 // return $values;
//                 $result = $this->calc_all($values[$employeId]);
//                 if (!isset($result["error"])) {
//                     $values[$employeId] = $result;
//                 }
//                 else{
//                     unset($values[$employeId]);
//                 }
//             }
//         }
//         $convertedDataToArray = array_values($values);
//         $convertedData = [];
//         foreach ($convertedDataToArray as $key => $entry) {
//             if (is_object($entry)) {
//                 $entry = (array) $entry;
//             }
//             if (isset($entry['error'])) {
//                 return $entry;
//             }
//             if (true) {
//                 if (!isset($entry['societe_id'])) {
//                     return $entry;
//                 }
//             }
//         }

//         foreach ($convertedDataToArray as $key => $entry) {
//             if (is_object($entry)) {
//                 $entry = (array) $entry;
//             }
//             if (isset($entry['error'])) {
//                 return $entry;
//             }
//             if (true) {
//                 $convertedData[] = [
//                     "masse_salariale" => $entry["masse_salariale"] ?? 0,                   
//                     "net_a_payer"=> $entry["net_a_payer"] ?? 0,                       
//                     "salaire_brut"=> $entry["salaire_brut"] ?? 0,                      
//                     "cnaps"=> $entry["cnaps"] ?? 0,
//                     "cnaps_pat"=> $entry["cnaps_pat"] ?? 0,
//                     "irsa"=> $entry["irsa"] ?? 0,
//                     "salfa_pat"=> $entry["salfa_pat"] ?? 0,
//                     "salfa"=> $entry["salfa"] ?? 0,
//                     "irsa_pat"=> $entry["irsa_pat"] ?? 0,
//                     "nb_heure_travaille"=> $entry["nb_heure_travaille"] ?? 0,
//                     "nb_heure_contrat"=> $entry["nb_heure_contrat"] ?? 0,
//                     "sal_base"=> $entry["sal_base"] ?? 0,   
//                     "prime"=> $entry["prime"] ?? 0,
//                     "prime_anc"=> $entry["prime_anc"] ?? 0,                                 
//                     "prime_assid"=> $entry["prime_assid"] ?? 0,                               
//                     "prime_exceptionnelle"=> $entry["prime_exceptionnelle"] ?? 0,                      
//                     "prime_entret"=> $entry["prime_entret"] ?? 0,                              
//                     "prime_resp"=> $entry["prime_resp"] ?? 0,                                
//                     "prime_div"=> $entry["prime_div"] ?? 0,                                 
//                     "part_variable"=> $entry["part_variable"] ?? 0,                             
//                     "net_imposable"=> $entry["net_imposable"] ?? 0,                     
//                     "salaire_mensuel"=> $entry["salaire_mensuel"] ?? 0,                   
//                     // "diff_HcHt"=> $entry["diff_HcHt"] ?? 0,                       
//                     "employe_id"=> $entry["employe_id"],                             
//                     "autre_deduction"=> $entry["autre_deduction"] ?? 0,                           
//                     "s_conge"=> $entry["s_conge"] ?? 0,                                   
//                     "perdiem"=> $entry["perdiem"] ?? 0,                                   
//                     "mode_payement"=> $entry["mode_payement"],                          
//                     "numero_tel"=> $entry["numero_tel"],                             
//                     "banque"=> $entry["banque"],                                 
//                     "numero_compte"=> $entry["numero_compte"],                          
//                     "code_banque"=> $entry["code_banque"] ,                            
//                     "code_guichet"=> $entry["code_guichet"],                           
//                     "rib"=> $entry["rib"],                                    
//                     "nom"=> $entry["nom"] ,
//                     "date_paie"=> $entry["date_paie"],                      
//                     "numero_employe"=> $entry["numero_employe"],                         
//                     "num_emp_soit"=> $entry["num_emp_soit"],                           
//                     "numero_stagiaire"=> $entry["numero_stagiaire"],                       
//                     "societe_id"=> $entry["societe_id"],                                
//                     "agence_id"=> $entry["agence_id"],                                
//                     "site_id"=> $entry["site_id"],                                
//                     "real_site_id"=> $entry["real_site_id"],                           
//                     "date_embauche"=> $entry["date_embauche"],                  
//                     "idm_depl"=> $entry["idm_depl"] ?? 0,                                  
//                     "nb_heure_convenu"=>$entry["nb_heure_convenu"],                          
//                     "fonction_id"=> $entry["fonction_id"],                               
//                     "status" => 'demande',
//                     "user_id" => $request->user()->id,
//                     "created_at" => (new \DateTime())->format('Y-m-d H:i:s'),
//                     "updated_at" => (new \DateTime())->format('Y-m-d H:i:s'),
//                 ];
//             }
//         }

//         // return response(compact('convertedData'));
//         $chunkSize = 1000;
//         $chunks = array_chunk($convertedData, $chunkSize);
//         foreach ($chunks as $chunk) {
//             DB::table('paies')->insert($chunk);
//         }
//         usleep(500000);
//         return response()->json(["success" => "Paie bien enregistré"]);
//         // return response(compact('convertedData'));
        
//     }
//     return response(["error" => "EACCES"]);
// }
