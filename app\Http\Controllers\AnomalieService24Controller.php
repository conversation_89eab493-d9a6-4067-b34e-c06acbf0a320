<?php

namespace App\Http\Controllers;

use App\Models\Employe;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AnomalieService24Controller extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function search(Request $request) {
        $auth = $request->user();
        $searchArray = [];
        if ($auth->role == 'superviseur'){
            $searchArray[] = " st.superviseur_id = " . $auth->id . " ";
        }
        else if ($auth->role == 'resp_sup'){
            $searchArray[] = " (st.resp_sup_id = " . $auth->id . " or pl.user_id = " . $auth->id . ") ";
        }
        if ($request->site_id) {
            $searchArray[] =  " pl.site_id = $request->site_id ";
        }
        if ($request->date_planning) {
            $searchArray[] = " pl.date_planning = '$request->date_planning' ";
        }
        if ($request->user_id) {
            $searchArray[] = " pl.user_id = '$request->user_id' ";
        }
        if ($request->resp_sup_id) {
            $searchArray[] = " st.resp_sup_id = '$request->resp_sup_id' ";
        }
        $query_where = "";
        $query_and = "";
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " ";
        }
        return compact("query_where", "query_and");
    }

    public function index(Request $request) {
        $auth = $request->user();
        $date = (new \DateTime())->format('Y-m-d H:i:s');
        $query_and = $this->search($request)['query_and'];
        if (in_array($auth->role, ['resp_sup', 'superviseur', 'resp_op', 'validateur'])) {
            $anomalies = DB::select("WITH 
                pointages_consecutifs AS (
                    SELECT p.agent_id, p.date_pointage,
                        LAG(p.date_pointage) OVER (PARTITION BY p.agent_id ORDER BY p.date_pointage) AS prev_pointage,
                        LEAD(p.date_pointage) OVER (PARTITION BY p.agent_id ORDER BY p.date_pointage) AS next_pointage,
                        CASE 
                            WHEN LAG(p.date_pointage) OVER (PARTITION BY p.agent_id ORDER BY p.date_pointage) IS NULL
                                OR TIMESTAMPDIFF(HOUR, LAG(p.date_pointage) OVER (PARTITION BY p.agent_id ORDER BY p.date_pointage), p.date_pointage) > 12 THEN 1
                            ELSE 0 
                        END AS debut_sequence
                    FROM planning_pointages p
                    LEFT JOIN plannings pl on pl.id = p.planning_id
                    LEFT JOIN sites st on st.idsite = pl.site_id
                    LEFT JOIN users u on u.id = pl.user_id
                    WHERE p.date_pointage >= ? $query_and
                ),
                sequences AS ( 
                    SELECT agent_id, date_pointage,
                        SUM(debut_sequence) OVER (PARTITION BY agent_id ORDER BY date_pointage) AS sequence_id
                    FROM pointages_consecutifs
                ),
                sequences_groupees AS (
                    SELECT s.agent_id, e.nom AS employe, e.societe_id, e.numero_employe, e.num_emp_saoi, e.num_emp_soit, e.numero_stagiaire,
                        MIN(s.date_pointage) AS debut_sequence,
                        MAX(s.date_pointage) AS fin_sequence,
                        COUNT(*) AS nombre_pointages
                    FROM sequences s
                    JOIN employes e ON s.agent_id = e.id
                    GROUP BY s.agent_id, e.nom, s.sequence_id
                    HAVING COUNT(*) > 1
                )
                SELECT 
                    agent_id as 'id', employe, societe_id, numero_employe, num_emp_saoi, num_emp_soit, numero_stagiaire, COUNT(*) AS service_consecutives
                FROM sequences_groupees
                GROUP BY agent_id
                ORDER BY service_consecutives DESC, agent_id LIMIT ?, 30",[(new \DateTime())->format('Y-m-d H:i:s'), $request->offset]);
            return response(compact('anomalies'));
        }
        return response(["error" => "EACCES"]);
    }

    public function show(Request $request, $id) {
        $auth = $request->user();
        $query_and = $this->search($request)['query_and'];
        $anomalies = DB::select(
            "WITH
                pointages_consecutifs AS (
                    SELECT agent_id,date_pointage,
                        LAG(date_pointage) OVER (PARTITION BY agent_id ORDER BY date_pointage) AS prev_pointage,
                        LEAD(date_pointage) OVER (PARTITION BY agent_id ORDER BY date_pointage) AS next_pointage,
                        CASE
                            WHEN LAG(date_pointage) OVER (PARTITION BY agent_id ORDER BY date_pointage) IS NULL
                                OR TIMESTAMPDIFF(HOUR, LAG(date_pointage) OVER (PARTITION BY agent_id ORDER BY date_pointage), date_pointage) > 12 THEN 1
                            ELSE 0
                        END AS debut_sequence
                    FROM planning_pointages p
                    LEFT JOIN plannings pl on pl.id = p.planning_id
                    LEFT JOIN sites st on st.idsite = pl.site_id
                    WHERE date_pointage >= ? AND agent_id = ? $query_and
                ),
                sequences AS (
                    SELECT  agent_id, date_pointage, SUM(debut_sequence) OVER (PARTITION BY agent_id ORDER BY date_pointage) AS sequence_id 
                    FROM pointages_consecutifs
                ),
                sequences_info AS (
                    SELECT s.agent_id, s.sequence_id,MIN(s.date_pointage) AS debut_sequence, MAX(s.date_pointage) AS fin_sequence
                        -- COUNT(*) AS nombre_pointages
                    FROM sequences s
                    JOIN employes e ON e.id = s.agent_id
                    GROUP BY s.agent_id, s.sequence_id
                    HAVING COUNT(*) > 1
                )
            SELECT p.agent_id, p.id as ptg_id, p.date_pointage, pl.id as planning_id, st.nom as 'site', st.idsite
            FROM planning_pointages p
            JOIN sequences_info si ON p.agent_id = si.agent_id 
                AND p.date_pointage BETWEEN si.debut_sequence AND si.fin_sequence
            LEFT JOIN plannings pl on pl.id = p.planning_id
            LEFT JOIN sites st on st.idsite = pl.site_id
            WHERE p.date_pointage >= ? AND p.agent_id = ?
            ORDER BY p.agent_id, p.date_pointage",
            [
                (new \DateTime())->format('Y-m-d H:i:s'),
                $id,
                (new \DateTime())->format('Y-m-d H:i:s'), 
                $id
            ]);

        $employe = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
            st.nom as 'site', st.idsite, u.name as 'superviseur', u.email as 'superviseur_email'
            FROM employes e
            LEFT JOIN sites st on st.idsite  = e.site_id
            LEFT JOIN users u on u.id = st.superviseur_id
            WHERE e.id = ?",
            [$id])[0];
        return response(compact('anomalies', 'employe'));
    }
}
