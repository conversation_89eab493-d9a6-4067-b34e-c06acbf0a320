import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import InputText from '../input/InputText';
import DualContainer from '../container/DualContainer';

export default function UpdateStockModal({action, data, closeModal, updateData}) {
    const [stockExistant, setStockExistant] = useState(data.nbStock)
    const [stockReel, setStockReel] = useState()
    const [error, setError] = useState()

    useEffect(() => {
        if (stockReel) {
            if (data.nbStock < Number(stockReel)) {
                setStockExistant(data.nbStock + (stockReel - data.nbStock))
            } else if (data.nbStock > Number(stockReel)) {
                setStockExistant(data.nbStock - (data.nbStock - stockReel))
            } else {
                setStockExistant(stockReel)
            }
        } else {
            setStockExistant(data.nbStock)
        }
    }, [stockReel])

    const handleOk = () => {
        const payload = {
            stockExistant: stockExistant,
            stockReel: stockReel
        }
        axios.post(action.request, payload, useToken())
        .then((res) => {
            if (res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if (res.data.error)
                setError(res.data.error)
            else {
                if (closeModal) closeModal()
                if (updateData) updateData(true)
            }
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    return (
        <div className="modal">
            <div>
                <h2>Mettre à jour le stock</h2>
                <DualContainer>
                    <InputText
                        disabled
                        label="Stock existant"
                        value={stockExistant}
                    />
                    <InputText
                        disabled={false}
                        label="Stock réel"
                        value={stockReel}
                        onChange={setStockReel}
                    />
                </DualContainer>
                <div className='form-button-container'>
                    <button className='btn-primary' onClick={handleOk}>Envoyer</button>
                    <button type='button' onClick={closeModal}>Annuler</button>
                </div>
            </div>
        </div>
    )
}