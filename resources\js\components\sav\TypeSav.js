import axios from 'axios';
import React, { useEffect, useState } from 'react';
import {Link} from 'react-router-dom'
import useToken from '../util/useToken';
import { MdOutlineConstruction } from 'react-icons/md';

export default function TypeSav() {
    const [typeSav, setTypeSav] = useState(null)
    useEffect(() => {
        let isMounted = true
        axios.get("/api/type_sav", useToken())
        .then((res) => {
            if(isMounted) {
                setTypeSav(res.data)
            }
        })
        .catch( e => console.error(e))
        return () => {
            isMounted = false
        }
    }, [])
    return <div id="wrapper">
        <div>
            <div className='menu-card'>
                <h3 className='sub-title-menu'>
                    <MdOutlineConstruction className='menu-icon'/>
                    <span>SAV</span>
                </h3>
                <div className='palette-container'>
                    {
                        typeSav && typeSav.map(item => (
                            <div key={item.name} className='palette-item'>
                                <Link className='link-no-style' to={"/sav/add/" + item.name}>{item.description}</Link>
                            </div>
                        ))
                    }
                </div>
            </div>
        </div>
    </div>;
}