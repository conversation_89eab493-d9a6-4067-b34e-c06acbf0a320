import React, { useEffect, useState } from 'react';
import InputText from './InputText';
import InputCheckBox from './InputCheckBox';

export default function InputDaItem({items, setItems, toggleModal,id,setId, editItem,setEditItem}) {
    const [designation, setDesignation] = useState("")
    const [unite, setUnite] = useState("")
    const [quantite, setQuantite] = useState("")
    const [prix, setPrix] = useState(0)
    const [price_only,setPriceOnly] = useState(false)
    const [submitDisabled, disableSubmit] = useState(true)

    useEffect(() => {
        if(editItem.length != 0){
            setDesignation(editItem.designation)
            setUnite(editItem.unite)
            setQuantite(editItem.quantite)
            setPrix(editItem.prix)
            setPriceOnly(editItem.price_only)
        }
    }, [editItem])

    const handleCancel = () => {
        setEditItem([])
        toggleModal(false)
    }

    const handleSubmit = (e) => {
        e.preventDefault()
        setId(parseInt(id) + 1)
        
        setItems([...items, { 
            id: id,
            designation: designation, 
            unite: unite,
            quantite: price_only ? 1 : quantite,
            prix : prix,
            price_only : price_only
        }]);
        setDesignation("")
        setUnite("")
        setQuantite("")
        setPrix("")
        setPriceOnly(false)
        toggleModal(false)
    }

    const handleUpdate = (e) => {
        e.preventDefault()
        const editedItems = items.map((item) => {
            if(editItem.id === item.id){
                return {
                    ...item, 
                    designation: designation,
                    unite: unite,
                    quantite: price_only ? 1 : quantite,
                    prix : prix,
                    price_only : price_only
                }
            }
            return item
        })
        setItems(editedItems)
        setEditItem([])
        setDesignation("")
        setUnite("")
        setQuantite("")
        setPrix("")
        setPriceOnly(false)
        toggleModal(false)
    }

    useEffect(() => {
        disableSubmit(
            (!price_only && (!designation.trim() || !unite.trim() || !quantite.trim())) 
            || (price_only && (!designation.trim() || !prix.trim()))
        )
    }, [
        designation,
        unite,
        quantite,
        prix,
        price_only
    ])

    return (
        <div className='modal'>
            <div>
                <div className="title-container" >
                    <h2>Ajouter un article</h2>
                </div>
                <form onSubmit={editItem.length != 0 ? handleUpdate : handleSubmit} encType="multipart/form-data">
                    
                    <InputText 
                        required
                        label="Désignation"
                        value={designation} 
                        onChange={setDesignation}/>
                    <div className='field-container'>
                        <InputCheckBox label="Prix seulement" checked={price_only} onChange={setPriceOnly}/>
                    </div>
                    {
                        !price_only &&
                        <InputText 
                            type="number"
                            min="0"
                            required
                            label="Quantité"
                            value={quantite} 
                            onChange={setQuantite}/>
                    }
                    {
                        !price_only &&
                        <InputText 
                            required
                            label="Unité (pcs, packet, litre... )"
                            value={unite} 
                            onChange={setUnite}/>
                    }
                    <InputText 
                        type="number"
                        min="0"
                        required={price_only ? true : false}
                        label={(price_only ? "Prix" : "Prix unitaire") + " (Ar)"}
                        value={prix} 
                        onChange={setPrix}/>
                    
                    <div className="form-button-container">
                        <button className='primary' disabled={submitDisabled} type="submit"> {editItem.length != 0 ? "Modifier" : "Ajouter" }</button>
                        <button className='secondary' onClick={handleCancel}> Annuler </button>
                    </div>
                </form>
            </div>
        </div>
    )
}