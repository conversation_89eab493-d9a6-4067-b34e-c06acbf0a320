<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\RegionUsers;
use Illuminate\Support\Facades\DB;

class RegionUsersController extends Controller
{
    public function show(Request $request, $id){
        $regions = DB::select("SELECT GROUP_CONCAT(region_id ORDER BY region_id SEPARATOR ', ') AS regions FROM region_users WHERE user_id = ?", [$id]);
        return response()->json($regions);
    }

    public function specify(Request $request, $id) {
        $existing_region_users = RegionUsers::where("user_id", $id)->get();
    
        if ($existing_region_users->isEmpty()) {
            $region_users = [];
    
            if ($request->isTana) {
                $region_users[] = ['user_id' => $id, 'region_id' => 1];
            }
            if ($request->isProvince) {
                $region_users[] = ['user_id' => $id, 'region_id' => 2];
            }
            if ($request->isTamatave) {
                $region_users[] = ['user_id' => $id, 'region_id' => 3];
            }
    
            if (!empty($region_users)) {
                RegionUsers::insert($region_users);
            }
    
            return response()->json(["success" => "Région spécifiée"]);
        } else {
            foreach ($existing_region_users as $eru) {
                if ($eru->region_id == 1 && !$request->isTana) {
                    RegionUsers::where('user_id', $id)->where('region_id', 1)->delete();
                }
                if ($eru->region_id == 2 && !$request->isProvince) {
                    RegionUsers::where('user_id', $id)->where('region_id', 2)->delete();
                }
                if ($eru->region_id == 3 && !$request->isTamatave) {
                    RegionUsers::where('user_id', $id)->where('region_id', 3)->delete();
                }
            }
    
            if ($request->isTana && !$existing_region_users->contains('region_id', 1)) {
                RegionUsers::create(['user_id' => $id, 'region_id' => 1]);
            }
            if ($request->isProvince && !$existing_region_users->contains('region_id', 2)) {
                RegionUsers::create(['user_id' => $id, 'region_id' => 2]);
            }
            if ($request->isTamatave && !$existing_region_users->contains('region_id', 3)) {
                RegionUsers::create(['user_id' => $id, 'region_id' => 3]);
            }
        
            return response()->json(["success" => "Région spécifiée"]);
        }
    }

    public static function getRegions(Request $request) {
        if ($request->user()->region != null) {
            $auth_region = explode(',', $request->user()->region);
            if (is_array($auth_region) && count($auth_region) > 0) {
                foreach($auth_region as $key => $value) {
                    if ($value == 1) {
                        $auth_region[$key] = 3;
                    }
                    if ($value == 2) {
                        $auth_region[$key] = 1;
                    }
                    if ($value == 3) {
                        $auth_region[$key] = 2;
                    }
                }
            } else {
                $auth_region = [1, 2, 3];
            }
        } else {
            $auth_region = [1, 2, 3];
        }

        $auth_region_str = implode(',', $auth_region);
        return $auth_region_str;
    }
    
}
