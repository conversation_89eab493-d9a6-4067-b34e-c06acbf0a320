import React from 'react';
import DatePicker, { registerLocale } from "react-datepicker";
import fr from "date-fns/locale/fr"; // the locale you want
registerLocale("fr", fr); // register it with the name you want
import { RiRefreshLine } from "react-icons/ri";

import "react-datepicker/dist/react-datepicker.css";

export default function InputDateTime({label, value, onChange, required, canRefresh, setRefresh}) {
    return <div className='input-container'>
        {
            canRefresh ? (
                <div style={{ marginBottom: '4px' }} className='space-between'>
                    <label>{label} {required && <span className='danger'>*</span>}</label>
                    <RiRefreshLine color='#666' size={20} cursor="pointer" onClick={() => setRefresh(true)}/>
                </div>
            )
            : (
                <label>{label} {required && <span className='danger'>*</span>}</label>
            )
        }
        <DatePicker
            selected={value}
            onChange={(date) => onChange(date)}
            locale="fr"
            showTimeSelect
            timeIntervals={15}
            dateFormat="dd/MM/yyyy HH:mm"
            className='input-date'
            />
    </div>
}