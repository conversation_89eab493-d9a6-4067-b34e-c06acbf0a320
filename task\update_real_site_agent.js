const moment = require('moment')
const mysql = require('mysql2')

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const sqlSelectAgent = `SELECT e.id, e.real_site_id FROM employes e 
	WHERE (e.soft_delete is null or e.soft_delete = 0)`

const sqlSelectAgentFromPointage = `SELECT e.id, e.real_site_id FROM pointages p 
	LEFT JOIN employes e ON e.id = p.employe_id 
	WHERE (e.soft_delete is null or e.soft_delete = 0) 
	and (p.soft_delete is null or p.soft_delete = 0) 
	and date_pointage = ?`
const sqlUpdateAgent = `UPDATE employes SET real_site_id = ? WHERE id = ?`
const sqlSelectPointage = `SELECT id, site_id, employe_id, date_pointage FROM pointages 
	WHERE (soft_delete = 0 or soft_delete is null) and employe_id = ? ORDER BY id DESC LIMIT 26`
const sqlSelectLastRealSiteExport = "SELECT value FROM params p WHERE p.key = 'last_update_real_site'"
const sqlUpdateLastRealSiteExport = `UPDATE params p SET p.value = ? WHERE p.key = 'last_update_real_site'`

const getDayOrNightExport = () => {
	let beginDay = moment().set({hour:6, minute:10, second:0})
	let endDay = moment().set({hour:18, minute:10, second:0})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
	else {
		if(moment().isBefore(beginDay))
			return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 06:00:00"
		return moment().format("YYYY-MM-DD") + " 06:00:00"
	}
}
const dateString = getDayOrNightExport()
const isAll = (process.argv[3] == "all")
const isTask = (process.argv[2] == "task")

const updateData = (employes, index) => {
	console.log("----")
	if(index < employes.length){
		const currentEmp = employes[index]
		pool.query(sqlSelectPointage, [currentEmp.id], async (err, pointages) => {
			if(err){
				console.error(err)
				process.exit(1)
			}
			else if(pointages.length){
				console.log("Nb pointage: " + pointages.length)
				let sites = {}
				function onlyUnique(value, index, array) {
					return array.indexOf(value) === index;
				}
				pointages.map(p => p.site_id).filter(onlyUnique).forEach(id => {
					sites[id] = 0
				});
				pointages.forEach(p => {
					sites[p.site_id]++
				})
				console.log(sites)
				const newSiteId = Object.entries(sites).reduce((max, current) =>
					current[1] > max[1] ? current : max
				)[0];
				console.log(newSiteId)
				if(!currentEmp.real_site_id || newSiteId != currentEmp.real_site_id){
					pool.query(sqlUpdateAgent, [newSiteId, currentEmp.id], async (err, r) => {
						if(err){
							console.error(err)
							process.exit(1)
						}
						else {
							console.log("ID employe to update: " + currentEmp.id + ", new site id : " + newSiteId)
							setTimeout(() => {
								updateData(employes, index+1)
							}, 200)
						}
					})
				}
				else {
					setTimeout(() => {
						updateData(employes, index+1)
					}, 200)
				}
			}
			else {
				setTimeout(() => {
					updateData(employes, index+1)
				}, 200)
			}
		})
	}
	else if(isTask && !isAll){
		pool.query(sqlUpdateLastRealSiteExport, [dateString], (err, result) => {
			if(err){
				console.error(err)
				process.exit(1)
			}
			else {
				console.log("update done")
				process.exit(0)
			}
		})
	}
	else {
		console.log("update done")
		process.exit(0)
	}
}

const doUpdateRealSite = () => {
	pool.query((isAll ? sqlSelectAgent : sqlSelectAgentFromPointage), isAll ? [] : [getDayOrNightExport()], async (err, employes) => {
		if(err){
			console.error(err)
			process.exit(1)
		}
		else {
			if(employes.length > 0) {
				console.log("Nb employe: " + employes.length)
				if(employes.length > 0){
					updateData(employes, 0)
				}
				else {
					console.log("no update")
					process.exit(1)
				}
			}
			else {
				console.log("no employe")
				process.exit(1)
			}
		}
	})
}

const waitDoUpdateRealSite = () => {
	setTimeout(() => {
		doUpdateRealSite()
	}, 60000)
}

if(!isTask){
	console.log("update real site test")
	doUpdateRealSite()
}
else {
	console.log("update real site task")
	pool.query(sqlSelectLastRealSiteExport, [], (err, params) => {
		if(err){
			console.error(err)
			process.exit(1)
		}
		else if(params.length && params[0].value != dateString){
			doUpdateRealSite()
		}
		else  {
			console.log("update real site already done!")
			process.exit()
		}
	})
}
