import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import StatusLabel from '../input/StatusLabel';

export default function Prime({auth, primes, setPrimes, currentId, setCurrentId}) {
    const locationSearch = useLocation().search
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'Référence', name: 'id', type:'number'},
        {label: 'Status', name: 'status', type: 'number'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Utilisateur', name: 'user_id', type:'number'},
        {label: 'Employe', name: 'employe_id', type:'number'},
        {label: 'Site', name: 'site_id', type:'number'},
    ]
    if(auth.role != 'superviseur')
        searchItems.push({label: 'Superviseur', name: 'superviseur_id', type:'number'})
        
    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            params.set("offset", 0)
        }
        else
            params.set("offset", primes.length)
        axios.get('/api/prime?' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setPrimes(res.data.primes)
                    else {
                        const list = primes.slice().concat(res.data.primes)
                        setPrimes(list)
                    }
                    setDataLoaded(res.data.primes.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>Prime</h2>
                {   
                    ['superviseur','resp_sup', 'resp_op','validateur','resp_rh','rh'].includes(auth.role) && 
                    <Link className='btn btn-primary' to="/prime/add">Nouveau</Link>
                }
            </div>
            <SearchBar listItems={searchItems}/>
            {
                primes.length == 0 ? 
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                    <InfiniteScroll
                        dataLength={primes.length}
                        next={fetchMoreData}
                        hasMore={!allDataLoaded}
                        loader={<LoadingPage/>}
                    >
                        <div className="line-container">
                            <div className="row-list">
                                <b className="line-cell-lg">Employé</b>
                                <b className="status-line">
                                    <StatusLabel color="grey"/>
                                </b>
                                <b>Motif</b>
                            </div>
                        </div>
                        {
                            primes.map((pr) => (
                                <div key={pr.id}
                                    className={`line-container ${currentId && currentId == pr.id ? 'selected' : ''}`}
                                    onClick={() => setCurrentId(pr.id)}
                                >
                                    <div className='row-list'>
                                        <span className='line-cell-lg'>
                                            {matricule(pr)} {pr.employe} 
                                        </span>
                                        <span className="status-line">
                                            <StatusLabel color={pr.status_color} />
                                        </span>
                                        <span>
                                            {pr.motif}
                                        </span>
                                    </div>
                                </div>
                            ))
                        }
                    </InfiniteScroll>
            }
        </div>
    } </>
}