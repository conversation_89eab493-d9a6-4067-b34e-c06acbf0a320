import React, { useEffect, useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import LoadingPage from '../loading/LoadingPage'
import SearchBar from '../input/SearchBar'
import InfiniteScroll from 'react-infinite-scroll-component'
import moment from 'moment'
import useToken from '../util/useToken'

export default function MessageSent({auth, messages, setMessages, setCurrentId, currentId}) {
    const locationSearch = useLocation().search
    const params = new URLSearchParams(locationSearch)
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'Objet', name: 'objet', type:'string'},
        {label: 'Destinataire', name: 'user_id', type:'number'},
        {label: 'Service', name: 'service_id', type:'integer'},
        {label: 'Date', name: 'created_at', type: 'date' },
    ]

    // if(auth.role == "admin"){
    //     searchItems.push({label: 'Expediteur', name: 'sender_id', type:'number'},)
    // }
    const updateData = (initial) => {
        let isMounted = true;
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            params.set("offset", 0)
        }
        else
            params.set("offset", messages.length)
        axios.get('/api/message_sent_unread?unread=1&' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setMessages(res.data.messages)
                    else {
                        const list = messages.slice().concat(res.data.messages)
                        setMessages(list)
                    }
                    setDataLoaded(res.data.messages.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
        setTimeout(() => {updateData()}, 300);
    };

    return (
        <>
            {
                isLoading ?
                    <LoadingPage/>
                :
                <div>
                    <div className="padding-container space-between">
                        <h2>Envoyé non lu</h2>
                        <Link className='btn btn-primary' to="/message/add">Nouveau message</Link>
                    </div>
                    <SearchBar listItems={searchItems}/>
                    {
                        messages.length == 0 ? 
                            <h3 className='center secondary'>Aucun données trouvé</h3>
                        :
                            <InfiniteScroll
                                dataLength={messages.length}
                                next={fetchMoreData}
                                hasMore={!allDataLoaded}
                                loader={<LoadingPage/>}
                            >
                                <div className="line-container">
                                    <div className="row-list">
                                        {auth.role != 'admin' && <b className="line-cell-lg">Dernier objet</b>}
                                        <b className='line-cell-xs'>Nb</b>
                                        {auth.role != 'admin' && <b className="line-cell-sm">Date</b>}
                                        <b>Déstinataire</b>
                                    </div>
                                </div> 
                                {
                                    messages.map((ms, index) => (
                                        ms.nb > 0 && (
                                            <div className={`line-container ${currentId && currentId == ms.user_id ? "selected" : ""}`} 
                                                key={index} 
                                                onClick={()=>setCurrentId(ms.user_id)}
                                            >
                                                <div className="row-list secondary">
                                                    {auth.role != 'admin' && <span className={"line-cell-lg " + ((ms.seen) ? "secondary" : "")}>{ms.objet}</span>}
                                                    <span className='line-cell-xs'>{ms.nb}</span>
                                                    {auth.role != 'admin' && <span className="line-cell-sm secondary">{moment(ms.created_at).format("DD/MM/YY HH:mm")}</span>}
                                                    <span className="secondary">
                                                        { ms.name + " <" + ms.email + ">" }
                                                    </span>
                                                </div>
                                            </div>
                                        )
                                    ))
                                }
                            </InfiniteScroll>
                    }
                </div>
            }
        </>
    )
}
