import React from 'react';

import moment from 'moment';

export default function LastFlotte({flotte}) {
    return <div className="story-container">
        
        <h4 className='capitalize text'>
        <span className={'badge-outline badge-outline-' + flotte.status_color}>
            {flotte.status_description}
        </span>
        </h4>
        <p className='secondary'>
            <span className='text'>Motif : </span> 
            {flotte.objet}<br/>
            <span className='text'>Commentaire : </span> 
            {flotte.commentaire}<br/>
            <span className='text'>Demandé le: </span>
            { moment(flotte.created_at).format("dddd DD MMM YYYY") } <br/>
            <span className='text'>Demandeur : </span> 
            {flotte.user_nom}  {'<' + flotte.user_email + '>'} <br/>
        </p>
    </div>
}