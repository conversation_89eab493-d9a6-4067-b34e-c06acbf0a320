const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs");
const { sendMail } = require("../auth")
const matricule = require("../resources/js/components/util/matricule")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == 'task')
const destination_vg = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectLastEquipementExport = "SELECT value FROM params p WHERE p.key = 'last_equipement_export'"

const sqlSelectStatus = "SELECT name, description FROM status order by ordre"

const sqlSelectArticle = "SELECT name, designation FROM articles order by name"

function sqlSelectEquipement(dateString){
    const begin = moment(dateString).subtract(7, "day").format("YYYY-MM-DD") + " 06:00:00"
    const end = moment(dateString).format("YYYY-MM-DD") + " 06:00:00"
	return "SELECT eq.id, eq.demande, eq.motif, eq.detail, eq.status, eq.created_at, " +
        "u.name as 'user_nom', u.email as 'user_email', " +
        "s.nom as 'site', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.nom as 'employe' " +
        "from equipements eq " +
        "left join employes a on a.id = eq.employe_id " +
        "left join sites s on s.idsite = eq.site_id " +
        "left join users u on u.id = eq.user_id " +
        "where (eq.status not in ('done', 'draft') " + 
        "or (eq.status in ('done', 'draft') and eq.updated_at > '" + begin +"' and eq.updated_at <= '" + end +"')) " +
        "order by eq.created_at"
}
function sqlSelectArticleEquipement(ids){
	return "SELECT aeq.equipement_id, aeq.article, aeq.done, " +
        "ac.designation, eq.demande, eq.motif, eq.detail, eq.created_at, " +
        "u.name as 'user_nom', u.email as 'user_email', " +
        "s.nom as 'site', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.nom as 'employe' " +
        "from article_equipements aeq " +
        "left join articles ac on ac.name = aeq.article " +
        "left join equipements eq on eq.id = aeq.equipement_id " +
        "left join users u on u.id = eq.user_id " +
        "left join sites s on s.idsite = eq.site_id " +
        "left join employes a on a.id = eq.employe_id " +
        "where aeq.equipement_id in (" + ids.join(",") + ") " +
        "order by eq.created_at"
}

function sqlUpdateLastEquipementExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_equipement_export'"
}

function generateEquipementArticleExcelFile(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    data.forEach(ac => {
        const worksheet = workbook.addWorksheet(ac.designation)
        worksheet.getColumn('A').width = 50
        worksheet.getColumn('B').width = 45
        worksheet.getColumn('C').width = 45
        worksheet.getColumn('D').width = 45
        worksheet.getColumn('E').width = 20
        worksheet.getCell('A1').value = ac.designation + " (" + ac.equipements.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:E1')

        let line = 3
        
        worksheet.getCell('A' + line).value = "Site"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontBold
        if(ac.equipements[0].demande || ac.equipements[0].detail){
            if(ac.demande)
                worksheet.getCell('B' + line).value = "Demande"
            else
                worksheet.getCell('B' + line).value = "Détail"
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('B' + line).font = fontBold
            worksheet.getCell('C' + line).value = "Motif"
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('C' + line).font = fontBold
            worksheet.getCell('D' + line).value = "Demandeur"
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('D' + line).font = fontBold
        }
        else {
            worksheet.getCell('B' + line).value = "Employe"
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('B' + line).font = fontBold
            worksheet.getCell('C' + line).value = "Motif"
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('C' + line).font = fontBold
            worksheet.getCell('D' + line).value = "Demandeur"
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('D' + line).font = fontBold
        }
        worksheet.getCell('E' + line).value = "Créé"
        worksheet.getCell('E' + line).border = borderStyle
        worksheet.getCell('E' + line).font = fontBold
        worksheet.getCell('E' + line).alignment = alignmentStyle
        
        worksheet.getCell('F' + line).value = "Ref"
        worksheet.getCell('F' + line).border = borderStyle
        worksheet.getCell('F' + line).font = fontBold
        worksheet.getCell('F' + line).alignment = alignmentStyle
        line++

        ac.equipements.forEach(eq => {
            worksheet.getCell('A' + line).value = eq.site ? capitalizeFirstLetter(eq.site) : ""
            worksheet.getCell('A' + line).border = borderStyle
            if(eq.demande || eq.detail){
                if(eq.demande)
                    worksheet.getCell('B' + line).value = eq.demande
                else
                    worksheet.getCell('B' + line).value = eq.detail
                worksheet.getCell('B' + line).border = borderStyle
                worksheet.getCell('C' + line).value = eq.motif
                worksheet.getCell('C' + line).border = borderStyle
                worksheet.getCell('D' + line).value = eq.user_nom + " <" + eq.user_email + ">"
                worksheet.getCell('D' + line).border = borderStyle
            }
            else {
                worksheet.getCell('B' + line).value = eq.employe ? (
                    (
                        matricule(eq)
                    ) + " " + eq.employe
                ) : ""
                worksheet.getCell('B' + line).border = borderStyle
                worksheet.getCell('C' + line).value = eq.motif
                worksheet.getCell('C' + line).border = borderStyle
                worksheet.getCell('D' + line).value = eq.user_nom + " <" + eq.user_email + ">"
                worksheet.getCell('D' + line).border = borderStyle
            }
            worksheet.getCell('E' + line).value = moment(eq.created_at).fromNow()
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('E' + line).alignment = alignmentStyle
            
            worksheet.getCell('F' + line).value = eq.equipement_id ?? eq.id
            worksheet.getCell('F' + line).border = borderStyle
            worksheet.getCell('F' + line).alignment = alignmentStyle
            line++
        })
    })
}

function generateEquipementStatusExcelFile(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    data.forEach(stat => {
        const worksheet = workbook.addWorksheet(stat.description)
        worksheet.getColumn('A').width = 50
        worksheet.getColumn('B').width = 50
        worksheet.getColumn('C').width = 40
        worksheet.getColumn('D').width = 40
        worksheet.getColumn('E').width = 50
        worksheet.getColumn('F').width = 20
        worksheet.getCell('A1').value = stat.description + " (" + stat.equipements.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:F1')

        let line = 3
        
        worksheet.getCell('A' + line).value = "Site"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontBold
        worksheet.getCell('B' + line).value = "Employe"
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).font = fontBold
        worksheet.getCell('C' + line).value = "Demande"
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).font = fontBold
        worksheet.getCell('D' + line).value = "Motif"
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).font = fontBold
        worksheet.getCell('E' + line).value = "Demandeur"
        worksheet.getCell('E' + line).border = borderStyle
        worksheet.getCell('E' + line).font = fontBold
        worksheet.getCell('F' + line).value = "Créé"
        worksheet.getCell('F' + line).border = borderStyle
        worksheet.getCell('F' + line).font = fontBold
        worksheet.getCell('F' + line).alignment = alignmentStyle
        worksheet.getCell('G' + line).value = "Ref"
        worksheet.getCell('G' + line).border = borderStyle
        worksheet.getCell('G' + line).font = fontBold
        worksheet.getCell('G' + line).alignment = alignmentStyle
        line++

        stat.equipements.forEach(eq => {
            worksheet.getCell('A' + line).value = eq.site ? capitalizeFirstLetter(eq.site) : ""
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = eq.employe ? (
                (
                    matricule(eq)
                ) + " " + eq.employe
            ) : ""
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = (eq.demande ? eq.demande : eq.articles.join(', ')) + (eq.detail ? " (" + eq.detail + ")" : "")
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('D' + line).value = eq.motif
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('E' + line).value = eq.user_nom + " <" + eq.user_email + ">"
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('F' + line).value = moment(eq.created_at).fromNow()
            worksheet.getCell('F' + line).border = borderStyle
            worksheet.getCell('F' + line).alignment = alignmentStyle
            worksheet.getCell('G' + line).value = eq.equipement_id ?? eq.id
            worksheet.getCell('G' + line).border = borderStyle
            worksheet.getCell('G' + line).alignment = alignmentStyle
            line++
        })
    })
}

function doEquipementExport(dateString){
	console.log("doEquipementExport")
    pool.query(sqlSelectStatus, [], async (err, status) => {
        if(err)
            console.error(err)
        else {
            pool.query(sqlSelectArticle, [], async (err, articles) => {
                if(err)
                    console.error(err)
                else {
                    pool.query(sqlSelectEquipement(dateString), [], async (err, equipements) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Nb equipement: " + equipements.length)
                            if(equipements.length > 0){
                                pool.query(sqlSelectArticleEquipement(equipements.map(eq => eq.id)), [], async (err, articleEquipements) => {
                                    if(err)
                                        console.error(err)
                                    else {
                                        const equipementByStatus = []
                                        status.map(stat => {
                                            stat.equipements = []
                                            equipements.map(eq => {
                                                eq.articles = []
                                                articleEquipements.map(aeq => {
                                                    if(eq.id == aeq.equipement_id)
                                                        eq.articles.push(aeq.designation)
                                                })
                                                if(stat.name == eq.status)
                                                    stat.equipements.push(eq)
                                            })
                                            if(stat.equipements.length > 0){
                                                equipementByStatus.push(stat)
                                            }
                                        })
                                        const equipementByArticle = []
                                        articles.map(ac => {
                                            ac.equipements = []
                                            articleEquipements.map(aeq => {
                                                if(aeq.done === null &&  ac.name == aeq.article){
                                                    ac.equipements.push(aeq)
                                                }
                                            })
                                            if(ac.equipements.length > 0)
                                                equipementByArticle.push(ac)
                                        })
                                        const dateService = moment(dateString).format("DD MMMM YYYY")
                                        const header = "Equipement " + dateService
                                        const workbookEquipementStatus = new Excel.Workbook()
                                        generateEquipementStatusExcelFile(workbookEquipementStatus, header, equipementByStatus)
                                        const equipementStatusBuffer = await workbookEquipementStatus.xlsx.writeBuffer()
                                        const headerArticle = "Demande par article " + dateService
                                        const workbookEquipementArticle = new Excel.Workbook()
                                        generateEquipementArticleExcelFile(workbookEquipementArticle, headerArticle, equipementByArticle)
                                        const equipementArticleBuffer = await workbookEquipementArticle.xlsx.writeBuffer()
                                        sendMail(
                                            pool,
                                            isTask ? destination_vg : destination_test,
                                            header, 
                                            "Ci-joint le rapport des demandes d'équipements  du " + moment(dateString).subtract(7, "days").format("DD MMMM YYYY") + " au " + moment(dateString).format("DD MMMM YYYY")
                                            + "<ul>"
                                                + (equipementByStatus.map(stat => "<li>" + stat.description + ": " + stat.equipements.length + "</li>").join(""))
                                            + "</ul>" +
                                            "<br/>Demande par article: "
                                            + "<ul>"
                                                + (equipementByArticle.map(ac => "<li>" + ac.designation + ": " + ac.equipements.length + "</li>").join(""))
                                            + "</ul>"  
                                            ,
                                            [
                                                {
                                                    filename: header + ".xlsx",
                                                    content: equipementStatusBuffer
                                                },
                                                {
                                                    filename: headerArticle + ".xlsx",
                                                    content: equipementArticleBuffer
                                                },
                                            ],
                                            (response) => {
                                                if(response && isTask){
                                                    pool.query(sqlUpdateLastEquipementExport(dateString), [], (e, r) =>{
                                                        if(e)
                                                            console.error(e)
                                                        else
                                                            console.log("update last diag export: " + r)
                                                        process.exit(1)
                                                    })
                                                }
                                                else
                                                    process.exit(1)
                                            },
                                            isTask
                                        )
                                    }
                                })
                            }
                        }
                    })
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2])){
    console.log("send test...")
    doEquipementExport(process.argv[2])
}
else if(isTask){
    if(moment().day() == 3 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
        let date_vigilance = moment().format("YYYY-MM-DD")
        pool.query(sqlSelectLastEquipementExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && result[0].value == date_vigilance) {
                console.log("export list equipement already done!")
                process.exit(1)
            }
            else {
                console.log("exporting ...")
                doEquipementExport(date_vigilance)
            }
        })
    }
    else {
        console.log("Not wednesday, skip Equipement")
    }
}
else
    console.log("please specify command!")