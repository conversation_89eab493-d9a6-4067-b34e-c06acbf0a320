import axios from 'axios';
import React, { useEffect, useState } from 'react';
import {Link, useLocation} from 'react-router-dom'
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';

export default function TypeAbsence() {
    const me = (useLocation().search == "?me=1" ? "?me=1" : "")
    const [typeAbsences, setTypeAbsences] = useState(null)
    useEffect(() => {
        let isMounted = true
        axios.get("/api/type_absence", useToken())
        .then((res) => {
            if(isMounted) {
                setTypeAbsences(res.data)
            }
        })
        .catch( e => console.error(e))
        return () => {
            isMounted = false
        }
    }, [])
    return <div id="content"> 
        <MenuView title="">
            <h3 className='sub-title-menu'>absence</h3>
            <div className='palette-container'>
                {
                    typeAbsences && typeAbsences.map(item => (
                        <div key={item.name} className='palette-item'>
                            <Link className='link-no-style' to={"/absence/add/" + item.name + me}>{item.description}</Link>
                        </div>
                    ))
                }
            </div>
        </MenuView>
    </div>;
}