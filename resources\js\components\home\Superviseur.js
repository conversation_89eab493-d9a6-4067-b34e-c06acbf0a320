import React, { useEffect, useState } from 'react';
import {Link} from 'react-router-dom'
import MenuView from '../view/MenuView';
import useToken from '../util/useToken';

export default function Superviseur() {
    const [data, setData] = useState(null);
    
    useEffect(() => {
        let isMounted = true
        axios.get("/api/superviseur", useToken())
        .then((res) => {
            if(isMounted)
                setData(res.data)
        })
        return () => { isMounted = false };
    }, [])
    
    return <MenuView title="">
        <h3 className='sub-title-menu'>rh</h3>
        <div className='palette-container'>
            <div className='palette-item'>
                <Link className='link-no-style' to="/sanction/add">Demande de sanction</Link>
            </div>
            <div className='palette-item'>
                <Link className='link-no-style' to="/prime/add">Demande de prime</Link>
            </div>
            <div className='palette-item'>
                <Link className='link-no-style' to="/absence/add">Demande d'absence</Link>
            </div>
        </div>
        <h3 className='sub-title-menu'>SITE</h3>
        <div className='palette-container'>
            <div className='palette-item'>
                <Link className='link-no-style' to="/sav/add">Demande de SAV</Link>
            </div>
            <div className='palette-item'>
                <Link className='link-no-style' to="/visite-poste/add">Visite de poste</Link>
            </div>
            <div className='palette-item'>
                <Link className='link-no-style' to="/fait-marquant/add">Fait marquant</Link>
            </div>
        </div>
    </MenuView>;
}