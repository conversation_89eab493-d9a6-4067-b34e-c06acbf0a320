.badge{
    width: 385px;
    height: 270px;
    border: 1px solid black;
    padding: 10px;
    margin-top: 5px;
    margin-right: 10px;
    margin-left: 5px;
}
.badge-header{
    display: flex;
    justify-content: flex-end;
}
.badge-profil{
    width: 120px;
    height: 120px;
    border: 1px solid black;
    border-radius: 10px;
}
.container-badge-profil{
    margin-left: 2px;
    display: grid;
    grid-template-columns:392px 392px;
}
.fonction{
    display: flex;
    justify-content: flex-start;
    font-size: 18px;
    text-transform: uppercase;
    position: absolute;
    bottom: 10px;
}
.badge-content{
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: left;
}
.logo{
    width: 240px;
    height: 50px;
    margin-right: 5px;
}
.badge-left-content{
    width: 250px;
    position: relative;
}
.qr-code{
    margin-left: 15px;
    height: 90px;
    margin-top: 25px;
    max-width: 90px;
    width: "100%";
}
.employe-name{
    width: 240px;
    text-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
    max-height: 50px;
    font-size: 18px;
}
.matricule{
    text-align: center;
}
