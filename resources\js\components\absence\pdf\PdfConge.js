import {useEffect,useState} from 'react'
import {Page,Text,Image,View,Document,StyleSheet,Path} from '@react-pdf/renderer'
import Logo1 from './soit.png'
import Logo2 from './dirickx_guard.jpg'
import useNumberToWords from '../../util/useNumberToWords'
import moment from 'moment'
import matricule from '../../util/matricule'
const getMatricule = matricule;

export default function PdfConge({ absence, societeType, droit, lieu, adresse, phone }) {
    const [societe, setSociete] = useState("")
    const [code, setCode] = useState("")
    const [Logo, setLogo] = useState("")
    const [logoNB, setLogoNb] = useState("")
    const [fonction, setFonction] = useState("")
    const [matricule, setMatricule] = useState("")
     
    useEffect(() => {
        setMatricule(getMatricule(absence))
        switch (absence.societe_id) {
            case 1:
                setLogo(Logo2);
                setLogoNb(2);
                break;
            case 2:
                setLogo(Logo1);
                setLogoNb(1);
                break;
            case 3:
                if (societeType === 'SOIT TANA' || societeType === 'SOIT TAMATAVE') {
                    setLogo(Logo1);
                    setLogoNb(1);
                }
                break;
            default:
                break;
        }

        switch(absence.fonction_id) {
            case 1:
                return setFonction("Agent de sécurité");
            case 2:
                return setFonction("Chef de poste");
            case 3:
                return setFonction("Conducteur de chien");
            case 4:
                return setFonction("Superviseur de site");
            case 5:
                return setFonction("Intervention");
            case 6:
                return setFonction("Motard");
            case 7:
                return setFonction("Contrôleur");
            case 8:
                return setFonction("Chauffeur");
            case 9:
                return setFonction("Femme de ménage");
            default:
                return setFonction("");
        }

    }, [])

    const styles = StyleSheet.create({
        body: {
            paddingTop: 35,
            paddingBottom: 65,
            paddingHorizontal: 60,  
        },
        image: {
            width: '260px'
        },
        image2: {
            width: '160px'
        },
        title: {
            fontFamily: 'Times-Bold',
            textAlign: 'left',
            fontSize: 12,
            marginTop: 8
        },
        border: {
            border: '1px dotted #000',
            paddingVertical: 3,
            paddingHorizontal: 10,
            fontSize: 13,
        },
        container: {
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-around'
        },
        text: {
            fontFamily: 'Times-Roman',
            textAlign: 'left',
            fontSize: 11,
            paddingVertical: 3,
        },
        textBold: {
            marginRight: 10
        },
        textUppercase: {
            textDecoration: 'Capitalize'
        },
        underline: {
            textDecoration: 'underline',
            textDecorationThickness: 1,
        },
        date: {
            textTransform: 'capitalize',
            fontFamily: 'Times-Roman',
            textAlign: 'center',
            fontSize: 10,
            paddingVertical: 10,
        },
        paddingVertical: {
            paddingVertical: 8
        },
        lineContainer: {
            flex: 1,
            flexDirection: 'row',
        },
        line: {
            flex: 1,
            borderBottomWidth: 1,
            borderBottomColor: 'black',
            marginTop: 70,
            marginBottom: 15,
        },
    })

    return (
        <Document>
            <Page size='A4' style={styles.body}>
                <View style={styles.container}>
                    <Image style={logoNB == 1 ? styles.image : styles.image2} src={Logo} />
                </View>                
                <View style={styles.container}>
                    <Text style={styles.title}>Fiche N° :  {("00000" + absence.id).slice(-6)}</Text>
                </View>
                <View style={[styles.container,styles.paddingVertical]}>
                    <Text style={[styles.text, styles.border]}>{absence.type_absence == "conge" ? "DEMANDE DE CONGE" : "DEMANDE DE PERMISSION"}</Text>
                </View>
                <Text style={styles.text}><Text style={styles.textBold}>Nom et Prénoms :  </Text> {absence.employe}</Text>
                <Text style={styles.text}><Text style={styles.textBold}>Matricule :  </Text> {matricule},  <Text style={styles.textBold}>Fonction :  </Text> {fonction}</Text>
                <Text style={styles.text}><Text style={styles.textBold}>Date d'embauche :  </Text> {moment(absence.date_embauche).format('DD MMMM YYYY')}</Text>
                <Text style={styles.text}><Text style={styles.textBold}>Lieu de travail :  </Text> {absence.site},  <Text style={styles.textBold}>Droits :  </Text> {droit + ' jour(s)'}</Text>
                <Text style={styles.text}>
                    <Text style={styles.textBold}>Nombre de jours demandés {'(en chiffre et en lettre) :  '}</Text>
                    {"("+moment(absence.retour).diff(moment(absence.depart), 'hours')/24+") " + useNumberToWords(moment(absence.retour).diff(moment(absence.depart), 'hours')/24)}
                </Text>                
                <Text style={styles.text}><Text style={styles.textBold}>Date de départ :  </Text> {moment(absence.depart).format('DD/MM/YYYY')},  <Text style={styles.textBold}>Date de retour :  </Text>{moment(absence.retour).format('DD/MM/YYYY')}</Text>
                <Text style={styles.text}><Text style={styles.textBold}>Motif / Observation :  </Text> {absence.motif}</Text>
                <Text style={styles.text}>
                    <Text style={styles.textBold}>Remplacement pendant l'absence :</Text>
                    {absence.employe_temporaire
                        ? '  ' +absence.employe_temporaire
                        : absence.employe_remplacant_id
                        ? '  ' + (
                            getMatricule(absence, true)
                        ) + ' ' + absence.arp_employe
                        : ''
                    }
                </Text>
                <Text style={styles.text}><Text style={styles.textBold}>Adresse et téléphone :  {adresse + ' / ' + phone}</Text> </Text>

                <Text style={styles.text}><Text style={styles.textBold}>Fait à :  </Text> <Text style={[styles.text,styles.textUppercase]}>{lieu}</Text>, le {moment().format('DD MMMM YYYY')}</Text>

                <View style={[styles.container,styles.paddingVertical]}>
                    <Text style={[styles.text,styles.underline]}>{absence.societe_id == 2 || absence.societe_id == 1 ? 'L\'employé' : 'Le Stagiaire'}</Text>
                    <Text style={[styles.text,styles.underline]}>Superviseur/Contrôleur</Text>
                    <Text style={[styles.text,styles.underline]}>Chef de service</Text>
                    <Text style={[styles.text,styles.underline]}>La Direction</Text>
                </View>

                <View style={styles.container}>
                    <View style={styles.line} />
                </View>

                <View style={styles.container}>
                    <Image style={logoNB == 1 ? styles.image : styles.image2} src={Logo} />
                </View>                
                <View style={styles.container}>
                    <Text style={styles.title}>Fiche N° :  {("00000" + absence.id).slice(-6)}</Text>
                </View>
                <View style={[styles.container,styles.paddingVertical]}>
                    <Text style={[styles.text, styles.border]}>{absence.type_absence == "conge" ? "DEMANDE DE CONGE" : "DEMANDE DE PERMISSION"}</Text>
                </View>
                <Text style={styles.text}><Text style={styles.textBold}>Nom et Prénoms :  </Text> {absence.employe}</Text>
                <Text style={styles.text}><Text style={styles.textBold}>Matricule :  </Text> {matricule},  <Text style={styles.textBold}>Fonction :  </Text> {fonction}</Text>
                <Text style={styles.text}><Text style={styles.textBold}>Date d'embauche :  </Text> {moment(absence.date_embauche).format('DD MMMM YYYY')}</Text>
                <Text style={styles.text}><Text style={styles.textBold}>Lieu de travail :  </Text> {absence.site},  <Text style={styles.textBold}>Droits :  </Text> {droit + ' jour(s)'}</Text>
                <Text style={styles.text}>
                    <Text style={styles.textBold}>Nombre de jours demandés {'(en chiffre et en lettre) :  '}</Text>
                    {"("+moment(absence.retour).diff(moment(absence.depart), 'hours')/24+") " + useNumberToWords(moment(absence.retour).diff(moment(absence.depart), 'hours')/24)}
                </Text>                
                <Text style={styles.text}><Text style={styles.textBold}>Date de départ :  </Text> {moment(absence.depart).format('DD/MM/YYYY')},  <Text style={styles.textBold}>Date de retour :  </Text>{moment(absence.retour).format('DD/MM/YYYY')}</Text>
                <Text style={styles.text}><Text style={styles.textBold}>Motif / Observation :  </Text> {absence.motif}</Text>
                <Text style={styles.text}>
                    <Text style={styles.textBold}>Remplacement pendant l'absence :</Text>
                    {absence.employe_temporaire
                        ? '  ' +absence.employe_temporaire
                        : absence.employe_remplacant_id
                        ? '  ' + (
                            getMatricule(absence, true)
                        ) + ' ' + absence.arp_employe
                        : ''
                    }
                </Text>
                <Text style={styles.text}><Text style={styles.textBold}>Adresse et téléphone :  {adresse + ' / ' + phone}</Text> </Text>

                <Text style={styles.text}><Text style={styles.textBold}>Fait à :  </Text> <Text style={[styles.text,styles.textUppercase]}>{lieu}</Text>, le {moment().format('DD MMMM YYYY')}</Text>

                <View style={[styles.container,styles.paddingVertical]}>
                    <Text style={[styles.text,styles.underline]}>{absence.societe_id == 2 || absence.societe_id == 1 ? 'L\'employé' : 'Le Stagiaire'}</Text>
                    <Text style={[styles.text,styles.underline]}>Superviseur/Contrôleur</Text>
                    <Text style={[styles.text,styles.underline]}>Chef de service</Text>
                    <Text style={[styles.text,styles.underline]}>La Direction</Text>
                </View>
            </Page>
        </Document>
    )
}
