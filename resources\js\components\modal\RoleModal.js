import axios from 'axios'
import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';

export default function RoleModal({role, onChange, closeModal}) {
    const [searchRole, setSearchRole] = useState("")
    const [roles, setRoles] = useState(null)
    
    const handleSelectRole = (ro) => {
        onChange(ro)
        closeModal()
    }

    useEffect(() => {
        let isMounted = true
        axios.get('/api/role', useToken())
        .then((res) => {
            if(isMounted) setRoles(res.data.roles)
        })
        return () => {isMounted = false}
    }, [])

    return <div className='modal'>
        <div>
            <h2>Role</h2>
            <div className='input-container'>
                <input
                    type="text"
                    value={searchRole}
                    placeholder="Role"
                    onChange={e => setSearchRole(e.target.value)}
                    />
            </div>
            {
                roles &&
                <div className='list-container'>
                    {
                        <ul>
                            {
                                roles.filter(ro => {
                                    const roleFound = new RegExp(searchRole.toLowerCase()).test(ro.name.toLowerCase())
                                    return roleFound
                                }).map((ro,index) => {
                                    return <li key={index} onClick={() => handleSelectRole(ro)}>
                                        <span className='secondary'>{ro.name}</span>
                                    </li>
                                })
                            }
                        </ul>
                    }
                </div>
            }
            <div className='form-button-container'>
                <button onClick={closeModal}>Annuler</button>
            </div>
        </div>
    </div>
}