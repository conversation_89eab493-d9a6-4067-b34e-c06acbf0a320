import React, { useEffect, useState } from 'react'
import SearchBar from '../../input/SearchBar';
import LoadingPage from '../../loading/LoadingPage';
import InfiniteScroll from 'react-infinite-scroll-component';
import useToken from '../../util/useToken';
import { useLocation, useNavigate } from 'react-router-dom';
import ModalExportAnomalie from './ModalExportAnomalie';
import moment from 'moment';
import './anomalie.css';

export default function Anomalie({ anomalies, setAnomalies, auth, currentId, setCurrentId }) {
    const [isLoading, toggleLoading] = useState(false);
    const [allDataLoaded, setDataLoaded] = useState(false);
    const [exportModal, toggleExport] = useState(false);
    const navigate = useNavigate()
    const location = useLocation()
    const locationSearch = location.search;
    const urlParams = new URLSearchParams(locationSearch)

    function getServiceTime() {
        const now = moment();
        let serviceDate;

        if (now.hour() >= 18) {
            serviceDate = now.set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        } else if (now.hour() >= 6) {
            serviceDate = now.set({ hour: 6, minute: 0, second: 0, millisecond: 0 });
        } else {
            serviceDate = now.subtract(1, 'days').set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        }
        return serviceDate.format('YYYY-MM-DD HH:mm:ss');
    }

    const searchItems = [
        { label: "Service", name: "date_service", type: "date_service" },
        { label: 'Site', name: 'site_id', type: 'number' },
        { label: 'Type', name: 'type_anomalie', type: 'select' },
    ]

    const updateData = (initial) => {
        if (!urlParams.get('date_service') || moment(urlParams.get('date_service')) > moment()) {
            urlParams.set('date_service', getServiceTime())
            navigate(location.pathname + "?" + urlParams)
        }
        else {
            let isMounted = true;
            if (initial) {
                toggleLoading(true)
                setDataLoaded(true)
                setCurrentId(null)
                urlParams.set("offset", 0)
            }
            else
                urlParams.set("offset", anomalies.length)
            axios.get("/api/anomalie?" + urlParams, useToken())
                .then((res) => {
                    if (isMounted) {
                        if (res.data.error)
                            console.error(res.data.error)
                        else {
                            if (initial) {
                                setAnomalies(res.data.anomalies)
                            }
                            else {
                                const list = anomalies.slice().concat(res.data.anomalies)
                                setAnomalies(list)
                            }
                            setDataLoaded(res.data.anomalies.length < 30)
                        }
                        toggleLoading(false)
                    }
                })
                .catch((e) => {
                    console.error(e)
                })
            return () => { isMounted = false };
        }
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
        setTimeout(() => updateData(), 300);
    };
    return (
        <div>
            {
                isLoading ?
                    <LoadingPage />
                    :
                    <div>
                        <div className="padding-container space-between">
                            <h2>Anomalie</h2>
                        </div>
                        {
                            exportModal &&
                            <ModalExportAnomalie closeModal={() => toggleExport(false)} />
                        }
                        <SearchBar listItems={searchItems} />
                        {
                            anomalies.length == 0 ?
                                <h3 className="center secondary">Aucun données trouvé</h3>
                                :
                                <div>
                                    <InfiniteScroll dataLength={anomalies.length}
                                        next={fetchMoreData}
                                        hasMore={!allDataLoaded}
                                        loader={<LoadingPage />}
                                    >
                                        <div className="line-container">
                                            <div className="row-list">
                                                <b className='line-cell-lg'>Site</b>
                                                <b className="line-cell-sm">M/S</b>
                                                <b>Manager</b>
                                            </div>
                                        </div>
                                        {
                                            anomalies.map((ano) => (
                                                <div onClick={() => setCurrentId(ano.idsite_group)}
                                                    className={`line-container ${currentId && currentId == ano.idsite_group ? "selected" : ""}`}
                                                    key={ano.idsite_group}
                                                >
                                                    <div className="row-list">
                                                        <span className='line-cell-lg'>{ano.site}</span>
                                                        <span className={`line-cell-sm ${ano.manque > 0 ? 'color-pink' : ano.surplus > 0 ? 'color-purple' : 'color-orange'}`}>
                                                            {ano.manque > 0 ? "Manque:" + ano.manque : ano.surplus > 0 ? "Surplus:" + ano.surplus : "Incoherence:" + ano.incoherence}
                                                        </span>
                                                        {
                                                            ano.resp_sup &&
                                                            <span>{ano.resp_sup + " <" + ano.resp_sup_email + ">"}</span>
                                                        }
                                                    </div>
                                                </div>
                                            ))
                                        }
                                    </InfiniteScroll>
                                </div>
                        }
                    </div>
            }
        </div>
    )
}
