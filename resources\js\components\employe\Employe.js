import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import NotificationEmploye from './NotificationEmploye';

import matricule from '../util/matricule';

export default function Employe({auth, employes, setEmployes, currentId, setCurrentId}) {
    const locationSearch = useLocation().search;
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'Actif', name: "actif", type:"number"},
        {label: 'Archive', name: "archive", type:"number"},
        {label: "Sal. forfait", name: "sal_forfait", type:"number"},
        {label: 'Réference', name: "id", type:"number"},
        {label: 'Nom', name: 'nom', type:'string'},
        {label: 'CIN', name: 'cin_text', type:'string'},
        {label: 'Matricule ', name: 'matricule', type:'number'},
        {label: 'Site', name: 'site_id', type:'number'},
        {label: 'Date de création', name: 'created_at', type:'date'},
    ]

    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            params.set("offset", 0)
        }
        else
            params.set("offset", employes.length)
        axios.get('/api/employe' + '?' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setEmployes(res.data.employes)
                    else {
                        const list = employes.slice().concat(res.data.employes)
                        setEmployes(list)
                    }
                    setDataLoaded(res.data.employes.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {updateData(true)}, [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        :
            <div>
                <div className="padding-container space-between">
                    <h2>
                        Employé
                    </h2>
                    {
                        ['admin', 'rh', "resp_rh"].includes(auth.role) &&
                        <Link className='btn btn-primary' to="/employe/add">Nouveau</Link>
                    }
                </div>
                <SearchBar listItems={searchItems}/>
                {
                    employes.length == 0 ?
                        <h3 className='center secondary'>Aucun données trouvé</h3>
                    :
                        <div>
                            <InfiniteScroll
                                dataLength={employes.length}
                                next={fetchMoreData}
                                hasMore={!allDataLoaded}
                                loader={<LoadingPage/>}
                            >
                                <div className="line-container ">
                                    <div className='row-employe'>
                                        <b className='matricule-employe'>#</b>
                                        <b className='nom-employe'>Nom</b>
                                        <b className='site-employe'>Poste</b>
                                    </div>
                                </div>
                                {
                                    employes.map((a) => (
                                        <div className={`table line-container ${currentId && currentId == a.id ? 'selected' : ''}`} key={a.id}>
                                            <div className={"row-employe " + (a.soft_delete ? "brown" : a.sal_forfait ? "cyan" : "")} onClick={() => setCurrentId(a.id)}>
                                                <span className='matricule-employe'>
                                                    {matricule(a)}
                                                </span>
                                                <span className='nom-employe'>{a.nom}</span>
                                                <span className='site-employe'>{a.site ? a.site : a.nom_agence}</span>
                                            </div>
                                        </div>
                                    ))
                                }
                            </InfiniteScroll>
                        </div>
                }
                <NotificationEmploye clickItem={setCurrentId}/>
            </div>
    } </>
}
