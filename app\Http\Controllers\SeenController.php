<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Seen;
use App\Models\FaitMarquant;
use App\Models\VisitePoste;
use App\Models\SuiviJuridique;

class SeenController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function fait_marquant($id, Request $request){
        $auth = $request->user();
        if(in_array($auth->role, ['validateur', 'access', 'resp_sup', "resp_op", 'daf'])){
            $fait = FaitMarquant::find($id);
            if($fait != null && $fait->user_id != $auth->id
            && Seen::where('user_id', $auth->id)->where('fait_marquant_id', $id)->first() == null){
                $seen = new Seen();
                $seen->user_id = $auth->id;
                $seen->fait_marquant_id = $id;
                $seen->created_at =  new \DateTime();
                if($seen->save())
                    return response(["success" => "Fait marquant lu"]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    public function fait_marquant_all(Request $request){
        $auth = $request->user();
        $faits = [];
        if(count($request->ids) > 0 && in_array($auth->role, ['validateur', 'resp_sup', 'access', 'resp_op', 'daf'])){
            $faits = DB::select("SELECT f.id
            FROM fait_marquants f
            LEFT JOIN (SELECT s.id, s.fait_marquant_id FROM seen s WHERE s.user_id = ?) y ON y.fait_marquant_id = f.id
            where y.id is null and f.id in ( ". implode(",", $request->ids) . ") " .
            "order by f.created_at desc", [$request->user()->id]);
            $new_insert_array = array();
            foreach($faits as $fait){
                $new_insert_array[] = array(
                    'user_id'=>$auth->id, 
                    'fait_marquant_id'=>$fait->id,
                    'created_at'=>now());
            }
            Seen::insert($new_insert_array);
            return response(["success" => "Tout les faits marquant sont lu"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function visite_poste($id, Request $request){
        $auth = $request->user();
        if(in_array($auth->role, ['validateur', 'resp_sup', 'access', 'resp_op', 'daf'])){
            $visite = VisitePoste::find($id);
            if($visite != null && $visite->user_id != $auth->id
            && Seen::where('user_id', $auth->id)->where('visite_poste_id', $id)->first() == null){
                $seen = new Seen();
                $seen->user_id = $auth->id;
                $seen->visite_poste_id = $id;
                $seen->created_at = new \DateTime();
                if($seen->save())
                    return response(["success" => "Visite de poste lu"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function visite_poste_all(Request $request){
        $auth = $request->user();
        $visites = [];
        if(in_array($auth->role, ['su', 'validateur', 'access', 'resp_sup', 'resp_op','daf'])){
            $visites = DB::select("SELECT v.id
            FROM visite_postes v
            LEFT JOIN (SELECT s.id, s.visite_poste_id FROM seen s WHERE s.user_id = ?) y ON y.visite_poste_id = v.id
            WHERE y.id IS null
            order by v.created_at desc
            limit 1000", [$request->user()->id]);
            $new_insert_array = array();
            foreach($visites as $visite){
                $new_insert_array[] = array(
                    'user_id'=>$auth->id, 
                    'visite_poste_id'=>$visite->id,
                    'created_at'=>now());
            }
            Seen::insert($new_insert_array);
            return response(["success" => "Tout les faits marquant sont lu"]);
        }
        return response(["error" => "EACCES"]);
    }
    
    public function juridique($id, Request $request){
        $auth = $request->user();
        if(in_array($auth->role, ['validateur', 'juridique'])){
            $suivi = SuiviJuridique::find($id);
            if($suivi != null && $suivi->user_id != $auth->id
            && Seen::where('user_id', $auth->id)->where('juridique_id', $id)->first() == null){
                $seen = new Seen();
                $seen->user_id = $auth->id;
                $seen->juridique_id = $id;
                $seen->created_at = new \DateTime();
                if($seen->save())
                    return response(["success" => "Visite de poste lu"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function juridique_all(Request $request){
        $auth = $request->user();
        $suivis = [];
        if(in_array($auth->role, ['validateur', 'juridique'])){
            $suivis = DB::select("SELECT sj.id
                FROM suivi_juridiques sj
                LEFT JOIN juridiques j on j.id = sj.juridique_id    
                LEFT JOIN (SELECT s.id, s.juridique_id FROM seen s WHERE s.user_id = ?) y ON y.juridique_id = sj.id
                WHERE y.id IS null and sj.juridique_id is not null and sj.id <= ?
                order by sj.created_at desc", [$request->user()->id, $request->id]);
            $new_insert_array = array();
            foreach($suivis as $suivi){
                $new_insert_array[] = array(
                    'user_id'=>$auth->id, 
                    'juridique_id'=>$suivi->id,
                    'created_at'=>now());
            }
            Seen::insert($new_insert_array);
            return response(["success" => "Tout les faits marquant sont lu"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function plainte_all(Request $request){
        $auth = $request->user();
        $suivis = [];
        if(in_array($auth->role, ['validateur', 'juridique'])){
            $suivis = DB::select("SELECT sj.id
                FROM suivi_juridiques sj
                LEFT JOIN juridiques j on j.id = sj.juridique_id    
                LEFT JOIN (SELECT s.id, s.juridique_id FROM seen s WHERE s.user_id = ?) y ON y.juridique_id = sj.id
                WHERE y.id IS null and sj.plainte_id is not null and sj.id <= ?
                order by sj.created_at desc", [$request->user()->id, $request->id]);
            $new_insert_array = array();
            foreach($suivis as $suivi){
                $new_insert_array[] = array(
                    'user_id'=>$auth->id, 
                    'juridique_id'=>$suivi->id,
                    'created_at'=>now());
            }
            Seen::insert($new_insert_array);
            return response(["success" => "Tout les faits marquant sont lu"]);
        }
        return response(["error" => "EACCES"]);
    }
}
