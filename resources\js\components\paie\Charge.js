import React from 'react'
import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'

export default function Charge({charge}) {
    return (
        <div>
            <DualContainer>
                <InputText type="number" label="Salfa" value={charge.salfa} disabled />
                <InputText type="number" label="salfa Patronale" value={charge.salfaPatronale} disabled />
            </DualContainer>
            <DualContainer>
                <InputText type="number" label="Cnaps" value={charge.cnaps} disabled />
                <InputText type="number" label="Cnaps Patronale" value={charge.cnapsPatronale} disabled />
            </DualContainer>
            <DualContainer>      
                <InputText type="number" label="Irsa" value={charge.irsa} disabled />
                <InputText type="number" label="Irsa Patronale" value={charge.irsaPatronale} disabled />
            </DualContainer>
        </div>
  )
}
