const moment = require('moment');
const mysql2 = require('mysql2');
const mysql = require('mysql');

moment.locale('fr');

const db_config_admin = require("../auth").db_config_admin;
const pool_admin = mysql2.createPool(db_config_admin);

const db_tls_formation = require("../auth").db_config_tls_formation;
const pool_tlsf = mysql.createPool(db_tls_formation);

const selectRecrutement = `
    SELECT id, nom, societe_id, numero_stagiaire, soft_delete, updated_at FROM recrutements
    WHERE updated_at > synchronized_at OR synchronized_at IS NULL
`;

const insertAgent = `
    INSERT INTO agents (id, nom, societe_id, numero_stagiaire, soft_delete, last_update)
    VALUES (?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
        nom = VALUES(nom),
        societe_id = VALUES(societe_id),
        numero_stagiaire = VALUES(numero_stagiaire),
        soft_delete = VALUES(soft_delete),
        last_update = VALUES(last_update)
`;

const updateSynchronizedAt = `
    UPDATE recrutements SET synchronized_at = ? WHERE id = ?
`;

const insertRow = async (recrutements, index) => {
    if (index >= recrutements.length) {
        syncDataTimeout();
        return;
    }

    const currentRecrutement = recrutements[index];
    const currentDate = moment().format('YYYY-MM-DD HH:mm:ss');

    pool_tlsf.query(
        insertAgent,
        [
            currentRecrutement.id,
            currentRecrutement.nom,
            currentRecrutement.societe_id,
            currentRecrutement.numero_stagiaire,
            currentRecrutement.soft_delete,
            currentRecrutement.updated_at,
        ],
        (err) => {
            if (err) {
                console.error(`Erreur d'insertion pour ID ${currentRecrutement.id}:`, err);
                insertRow(recrutements, index + 1);
            } else {
                pool_admin.query(
                    updateSynchronizedAt,
                    [currentDate, currentRecrutement.id],
                    (updateErr) => {
                        if (updateErr) {
                            console.error(`Erreur lors de la mise à jour de synchronized_at pour ID ${currentRecrutement.id}:`, updateErr);
                        }
                        insertRow(recrutements, index + 1);
                    }
                );
            }
        }
    );
};

const syncDataTimeout = () => {
    setTimeout(() => syncDataToTlsf(), 5000);
};

const syncDataToTlsf = async () => {
    console.log("---------\n", moment().format("YY-MM-DD HH:mm:ss"));
    console.log("Sélection des données à insérer");

    pool_admin.query(selectRecrutement, (err, results) => {
        if (err) {
            console.error("Erreur lors de la récupération des recrutements:", err);
            syncDataTimeout();
            return;
        }

        if (results.length > 0) {
            console.log(`${results.length} recrutements trouvés.`);
            insertRow(results, 0);
        } else {
            console.log("Aucun recrutement à synchroniser.");
            syncDataTimeout();
        }
    });
};

syncDataToTlsf();
