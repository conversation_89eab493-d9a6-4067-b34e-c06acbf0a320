import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import './dotation.css'
import moment from 'moment';
import StatusLabel from '../input/StatusLabel';
import { ImEnter, ImExit } from "react-icons/im";
moment.locale('fr')

export default function Dotation({auth, dotations, setDotations, currentId, setCurrentId}) {
    const locationSearch = useLocation().search;
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'À récupérer', name: "a_recuperer", type:"number"},
        {label: 'Réference', name: "id", type:"number"},
        {label: 'Nom', name: 'nom', type:'string'},
        {label: 'Matricule ', name: 'matricule', type:'number'},
        {label: 'Date de mouvement', name: "created_at", type:"date"},
        {label: 'Type', name: 'type_mvt', type:'select'},
    ]

    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true);
            setDataLoaded(true);
            setCurrentId(null);
            params.set("offset", 0);
        } else params.set("offset", dotations.length);

        axios.get('/api/mouvement_article?' + params, useToken())
        .then((res) => {
            if (isMounted) {
                if (res.data.error)
                    console.error(res.data.error);
                else {
                    if (initial) {
                        setDotations(res.data.mouvement_equipement);
                    }
                    else {
                        const list = dotations.slice().concat(res.data.mouvement_equipement);
                        setDotations(list);
                    }
                    setDataLoaded(res.data.mouvement_equipement.length < 30);
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e);
        })
        return () => {
            isMounted = false;
        }
    }

    useEffect(() => {
        let isMounted = true;
        if (isMounted) updateData(true);
        return () => {
            isMounted = false;
        };
    }, [locationSearch]);

    const fetchMoreData = () => {
        setTimeout(() => {
            updateData();
        }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div>
                <div className='padding-container space-between'>
                        <h2>
                            Dotation
                        </h2>
                    {
                        ["tenue"].includes(auth.role) &&
                        <Link className='btn btn-primary' to="/dotation/add/tenue">Nouveau</Link>
                    }
                </div>
            </div>
            <SearchBar listItems={searchItems}/>
            {
                (dotations && dotations.length == 0) ?
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                    <InfiniteScroll dataLength={dotations.length}
                        next={fetchMoreData}
                        hasMore={!allDataLoaded}
                        loader={<LoadingPage />}
                    >
                        <div className='line-container'>
                            <div className="row-dotation">
                                <b className='line-cell-md'>Agent/Site</b>
                                <b className="status-line"><StatusLabel color="grey" /></b>
                                <b className="date-mvt">Date</b>
                                <b className='site'>Site</b>
                            </div>
                        </div>
                        {dotations.map((dt) => (
                            <div key={dt.id}
                                className={`table line-container ${currentId && currentId == dt.id ? 'selected' : ""}`}
                                onClick={()=>setCurrentId(dt.id)}
                            >
                                <div>
                                    <div className={"row-dotation " + (dt.type_mouvement === "sortie" && dt.soft_delete && dt.status !== "termine" ? "brown" : "")}>
                                        <span className="line-cell-md">{dt.forSite == null && matricule(dt)} {!dt.forSite ? dt.nom : dt.forSite}</span>
                                        <span className="status-line">
                                            {dt.type_mouvement === "entré" ? (
                                                <ImEnter className="status-icon-enter" />
                                            ) : dt.type_mouvement === "sortie" && (
                                                <ImExit className="status-icon-exit"/>
                                            )}
                                        </span>
                                        <span className="date-mvt">{moment(dt.date_mouvement).format("DD/MM/YY")}</span>
                                        <span className='site'>{dt.site}</span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </InfiniteScroll>
            }
        </div>
    }
    </>
}
