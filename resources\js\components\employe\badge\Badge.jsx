import React, { useEffect, useRef, useState } from 'react'
import useToken from '../../util/useToken';
import axios from 'axios';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../../loading/LoadingPage';
import matricule from '../../util/matricule';
import { IoMdClose } from 'react-icons/io';
import { Link, useLocation } from 'react-router-dom';
import '../../input/modal.css'
import BadgeItem from './BadgeItem';
import { useReactToPrint } from 'react-to-print';
import moment from 'moment';

export default function Badge({ setEmpPrints, empPrints, setCurrentId, auth }) {
    const locationSearch = useLocation().search;
    const [employes, setEmployes] = useState([])
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [isLoading, toggleLoading] = useState(false)
    const [dataModal, toggleDataModal] = useState(false)
    const [searchValue, setSearchValue] = useState('')

    const handleRemove = (employe) => {
        const newEmployes = empPrints.filter(ag => ag.id != employe.id)
        setEmpPrints(newEmployes)
    }

    const handleRemoveAll = () => {
        const newEmployes = [...employes]
        newEmployes.forEach(ag => ag.is_add = false)
        setEmployes(newEmployes)
        setEmpPrints([]);
        setCurrentId();
    }

    const handleSelectEmploye = (ag) => {
        const newEmpPrints = [...empPrints]
        let currentEmp = newEmpPrints.find(emp => emp.id === ag.id)
        if (!currentEmp) { 
            newEmpPrints.push(ag)
            setEmpPrints(newEmpPrints)
            toggleDataModal(false)
        } 
    }

    const getEmploye = (initial) => {
        let isMounted = true
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true)
            setDataLoaded(true)
            params.set('offset', 0)
        }
        else
            params.set('offset', employes.length);
        if (searchValue)
            params.set('value', searchValue)
        axios.get('/api/employe/search?' + params, useToken())
        .then((res) => {
            if (isMounted){
                if (res.data.error)
                    console.error(res.data.error)
                else {
                    let dataList = [...employes]
                    if (initial) {
                        dataList = [];
                    }
                    res.data.employes.forEach(ag => {
                        dataList.push({
                            id: ag.id,
                            matricule: matricule(ag),
                            date_embauche: ag.date_embauche,
                            nom: ag.nom,
                            fonction: ag.titre ?? ag.fonction,
                            is_add: false,
                        })
                    })
                    setEmployes(dataList)
                    setDataLoaded(res.data.employes.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => getEmploye(true), [locationSearch]);

    const fetchMoreData = () => {
        setTimeout(() => {
            getEmploye()
        }, 300);
    }

    useEffect(() => {
        if (empPrints.length <= 0) {
            setCurrentId();
        }
    }, [empPrints])

    const componentPDF = useRef()
    const generatePdf = useReactToPrint({
        content: () => componentPDF.current,
        documentTitle: "Badge-" + moment().format('D-M-Y H:m'),
    })

    return <>{["rh", "resp_rh"].includes(auth.role) &&
            <div>  
                <div className='padding-container space-between'>
                    <h2>Badge</h2>
                    <Link className='btn btn-primary' to="#" onClick={() => toggleDataModal(true)}>Ajouter</Link>
                </div>
                {empPrints && empPrints.length > 0 && ["rh", "resp_rh"].includes(auth.role) &&
                    <div className='action-container'>
                        <span onClick={() => { setCurrentId('badge') }}>Aperçue</span>
                        <span onClick={() => generatePdf()}>Exporter</span>
                        <span onClick={() => handleRemoveAll()}>Vider</span>
                    </div>
                }
                {dataModal &&
                    <div className='modal'>
                        <div>
                            <h2>Employe</h2>
                            <div className='search-container'>
                                <input type="text" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} placeholder="Nom de l'employe ou matricule" />
                                <button onClick={getEmploye}>Rechercher</button>
                            </div>
                            {isLoading ?<LoadingPage/>
                            :
                                <div>
                                    { employes.length == 0 ?
                                        <h3 className='center secondary'>Aucun données trouvé</h3>
                                    :
                                        <div id='scrollablelist' style={{ maxHeight: '40vh', overflowY:'auto' }}>
                                            <InfiniteScroll
                                                dataLength={employes.length}
                                                next={fetchMoreData}
                                                hasMore={!allDataLoaded}
                                                loader={<LoadingPage />}
                                                scrollableTarget="scrollableList"
                                            >
                                                {employes.map((ag) =>
                                                    <div className='table line-container' key={ag.id} onClick={() => handleSelectEmploye(ag)}>
                                                        <div className="row-employe">
                                                            <span>
                                                                {ag.matricule + '-' + ag.nom}
                                                            </span>
                                                        </div>
                                                    </div>)
                                                }
                                            </InfiniteScroll>
                                        </div>
                                    }
                                </div>
                            }
                            <div className='form-button-container'>
                                <button onClick={() => toggleDataModal(false)}>Annuler</button>
                            </div>
                        </div>
                    </div>
                }
                {empPrints.length == 0 ?
                    <h3 className='center secondary line-container'>Veuillez ajouter des employés</h3>
                    :
                    <div>     
                        {empPrints.map((ag) =>
                            <div className='table line-container' key={ag.id} >
                                <div className="row-employe space-between">
                                    <span>
                                        {[ag.matricule] + ' ' + ag.nom}
                                    </span>
                                    <span onClick={() => handleRemove(ag)}>
                                        <IoMdClose size={20} />
                                    </span>
                                </div>
                            </div>)
                        }
                    </div>  
                } 
                <div className='badge-container' style={{display:"none"}}>
                    <div ref={componentPDF} className='container-badge-profil'>
                        {empPrints &&
                            empPrints.map((emp) => (
                                <div key={emp.id} className='badge'>
                                    <BadgeItem employe={emp} />
                                </div>
                            ))
                        }
                    </div>
                </div>
            </div>
    }
    </>
}
