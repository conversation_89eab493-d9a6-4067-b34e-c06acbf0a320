import React, { useEffect, useState } from 'react'
import {useParams} from 'react-router-dom'
import axios from 'axios'

import useToken from '../util/useToken';
import ActionFlotte from './ActionFlotte';
import ShowHeader from '../view/ShowHeader';
import Tab from '../layout/Tab';
import LoadingPage from '../loading/LoadingPage';
import moment from 'moment';

export default function ShowFlotte({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [flotte, setFlotte] = useState(null)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/flotte/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setFlotte(res.data)
                const newUser = []
                if (auth.id != res.data.user_id)
                    newUser.unshift({ id: res.data.user_id, address: res.data.user_email, name: res.data.user_name })
                setDefautUsers(newUser)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(flotte)
    }, [flotte]);

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                flotte &&
                <>
                    <ShowHeader label="Flotte" id={flotte.id} closeDetail={() => setCurrentId()} size={size}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + flotte.status_color}>
                                    {flotte.status_description}
                                </span> {
                                    flotte.nb_pj > 0 &&
                                    <span className="badge-outline">
                                        Pièce jointe : {flotte.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {
                                flotte.site ? 
                                    flotte.site 
                                : 
                                <div> 
                                    {flotte.user_nom} <span className='secondary'> {" <" + flotte.user_email + ">"} </span> 
                                </div>
                            }
                        </h3>   
                        {
                            flotte.objet &&
                            <div>
                                Objet : <span className='text'>{flotte.objet}</span>
                            </div>
                        }
                        {
                            flotte.commentaire &&
                            <div>
                                Commentaire : <span className='text'>{flotte.commentaire}</span>
                            </div>
                        }
                        {
                            flotte.site && 
                            <div>
                                Demandeur : <span className='text'> 
                                    {flotte.user_nom} {' <' + flotte.user_email + '>'}
                                </span>
                            </div>
                        }
                        <div>
                            Le : <span className='text'> 
                                {moment(flotte.created_at).format("DD MMMM YYYY")}
                            </span>
                        </div>
                        <div className='card-action'>
                            <ActionFlotte auth={auth} flotte={flotte} updateData={updateData} toggleLoading={toggleLoading}/>
                        </div>
                    </div>
                    <Tab auth={auth} name="flotte_id" value={flotte.id} updateData={updateData} defautUsers={defautUsers}/>
                </>
            }
        </div>
    } </>
}