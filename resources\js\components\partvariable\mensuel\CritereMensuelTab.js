import React, { useEffect, useState } from 'react'

import useToken from '../../util/useToken'
import LoadingPage from '../../loading/LoadingPage'
import { AiTwotoneEdit } from 'react-icons/ai'
import showAmount from '../../util/numberUtil'
import EditMensuelModal from './EditMensuelModal'

export default function CritereMensuelTab({partVariable, name, auth, updateData}) {
    const [isLoading, toggleLoading] = useState(true)
    const [criteres, setCriteres] = useState([])
    const [montantTotal, setMontantTotal] = useState(0)
    const [montantMaximum, setMontantMaximum] = useState(0)
    const [currentCritere, setCurrentCritere] = useState(null)
    const [showEditModal, toggleEditModal] = useState(false)

    const handleEditCritere = (critere, index) => {
        critere.index = index
        setCurrentCritere(critere)
        toggleEditModal(true)
    }

    useEffect(() => {
        let isMounted = true;
        toggleLoading(true)
        axios.get('/api/critere_mensuel/part_variable/' + partVariable.id,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else if(res.data.part_mensuels){
                    setCriteres(res.data.part_mensuels)
                    const total = res.data.part_mensuels.reduce(
                        (accumulator, currentValue) => accumulator + currentValue.montant,
                        0,
                    )
                    setMontantTotal(total)
                    const maximum = res.data.part_mensuels.reduce(
                        (accumulator, currentValue) => accumulator + currentValue.maximum,
                        0,
                    )
                    setMontantMaximum(maximum)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }, []);
    
    const showEditAction = (
        (auth.role == "validateur" && ["validation"].includes(partVariable.status))
        || (auth.id == partVariable.user_id && partVariable.status == "draft")
    )

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <>
                    {
                        showEditModal &&
                        <EditMensuelModal
                            currentCritere={currentCritere}
                            updateData={updateData}
                            closeModal={() => toggleEditModal(false)}/> 
                    }
                    <div className='line-container'>
                        <div className='header-pointage'>
                            <div className='space-between'>
                                <h3>
                                    Attribué : {showAmount(montantTotal)}
                                </h3>
                                {
                                    montantTotal < montantMaximum &&
                                    <b className='pink'>
                                        {"-" + showAmount(montantMaximum - montantTotal)}
                                    </b>
                                }
                            </div>
                        </div>
                    </div>
                    {
                        criteres.map(cr => 
                            <div key={cr.id} className='line-container'>
                                <div className='pointage-container'>
                                    <div>
                                        {showAmount(cr.montant)}
                                        <br/>
                                        <span className='secondary'>
                                            {cr.designation} {cr.montant != cr.maximum ? ' (Max : ' + showAmount(cr.maximum) + ')' : ''}
                                        </span>
                                    </div>
                                    {
                                        showEditAction &&
                                        <div style={{width:25, minWidth:25, maxWidth:25}}>
                                            <span>
                                                <AiTwotoneEdit onClick={() => handleEditCritere(cr)} size={20}/>
                                            </span>
                                        </div>
                                    }
                                </div>
                            </div>
                        )
                    }
                </>
        }
    </>
}