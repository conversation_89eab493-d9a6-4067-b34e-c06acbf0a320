import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Link, Navigate } from 'react-router-dom';
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';

export default function RespSup({auth}) {
    const [nbData, setData] = useState(null) 

    useEffect(() => {        
        let isMounted = true
        axios.get('/api/access', useToken())
        .then(res => {
            setData(res.data)
        })
        .catch(err => {
            console.error(err)
        })
        return () => { isMounted = false }
    }, [])

    return (
        ["resp_sup","resp_op"].includes(auth.role) ?
            <MenuView title="">
                {
                    nbData && 
                    <div>
                        {
                            (nbData.nb_fait != 0 || nbData.nb_visite != 0) &&
                            <>
                                <h3 className='sub-title-menu'>RAPPORT</h3>
                                <div>
                                    <div className='palette-container'>
                                        {
                                            (nbData.nb_fait != 0) &&
                                            <div className='palette-item'>
                                                <Link className='link-no-style' to="/fait-marquant?unread=1">Fait marquant non lu</Link>
                                                <span className='badge-outline'>{nbData.nb_fait}</span>
                                            </div>
                                        }
                                        {
                                            (nbData.nb_visite != 0) &&
                                            <div className='palette-item'>
                                                <Link className='link-no-style' to="/visite-poste?unread=1">Visite de poste non lu</Link>
                                                <span className='badge-outline'>{nbData.nb_visite}</span>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </>
                        }
                        <h3 className='sub-title-menu'>équipement</h3>
                        <div>
                            <div className='palette-container'>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/equipement/add">Demande d'équipement ou tenue</Link>
                                </div>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/flotte/add">Demande ou problème de flotte</Link>
                                </div>
                            </div>
                        </div>
                        <h3 className='sub-title-menu'>RH</h3>
                        <div>
                            <div className='palette-container'>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/sanction/add">Demande de sanction</Link>
                                </div>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/prime/add">Demande de prime</Link>
                                </div>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/absence/add">Demande d'absence</Link>
                                </div>
                            </div>
                        </div>
                        <h3 className='sub-title-menu'>TECH</h3>
                        <div>
                            <div className='palette-container'>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/sav/add">Demande de SAV</Link>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </MenuView>
        :
            <Navigate to="/"/>
    );
}