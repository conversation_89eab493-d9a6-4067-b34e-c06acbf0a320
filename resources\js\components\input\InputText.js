import React from 'react';

export default function InputText({label,name, value, onChange, onEnter, type, disabled, required, placeholder, accept,min,max, multiple}) {

    return <div className='input-container'>
        <label>{label} {required && <span className='danger'>*</span>}</label>
        <input
            multiple={multiple}
            name={name}
            placeholder={placeholder}
            type={type ? type : "text"} 
            value={value} 
            onChange={(e) => {type == 'file' ? onChange(e.target.files[0]) : onChange(e.target.value)}}
            onKeyDown={(e) => {if(e.key === 'Enter') onEnter()}}
            disabled={disabled}
            accept={accept}
            min={min}
            max={max}
            step="any"
            />
    </div>;
}