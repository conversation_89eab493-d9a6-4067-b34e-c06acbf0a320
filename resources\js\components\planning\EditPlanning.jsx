import React, { useEffect, useState } from 'react'
import InputEmploye from '../input/InputEmploye'
import InputSite from '../input/InputSite'
import InputMonthYear from '../input/InputMonthYear'
import ButtonSubmit from '../input/ButtonSubmit'
import useToken from '../util/useToken'
import matricule from '../util/matricule'
import LoadingPage from '../loading/LoadingPage'
import Notification from '../notification/Notification'
import CalendarView from './CalendarView'
import ListView from './ListView'
import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'
import ModalWarning from './ModalWarning'
import { useLocation, useParams } from 'react-router-dom'
import moment from 'moment'
import "./planning.css";
import LoadingScreen from '../loading/LoadingScreen'
import ViewHoraire from './ViewHoraire'

export default function EditPlanning({auth, isEdit, toDuplicate}) {
    const [plannings, setPlannings] = useState([])
    const [site, setSite] = useState({})
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const date = queryParams.get('date');
    const paramsSiteId = queryParams.get('site_id');
    const [datePlanning, setDatePlanning] = useState({year: moment(queryParams.get('date')).format('YYYY'), month: moment(queryParams.get('date')).format('MM')})
    const defaultDate = date ? (moment(date + '-01')) : (moment().add(1, "month"))
    const [user, setUser] = useState()
    const [showEmploye, setShowEmploye] = useState(false)
    const [currentPlId, setCurrentPlId] = useState(null)
    const [error, setError] = useState('')
    const [isLoading, toggleLoading] = useState(false)
    const [notification, setNotification] = useState(null)
    const [disableSubmit, setDisableSubmit] = useState(false)
    const [calendar, toggleCalendar] = useState(false)
    const [currentUserToAdd, setCurrentUserToAdd] = useState(null)
    const [addMultipleCase, setAddMultipleCase] = useState(false)
    const [contrat, setContrat] = useState({agent_day: 0, agent_night: 0})
    const [showWarning, setShowWarning] = useState(false)
    const [warningMessage, setWarningMessage] = useState("")
    const [titleMessage, setTitleMessage] = useState("")
    const [currentDatePtg, setCurrentDatePtg] = useState(null)
    const [countHours, setCountHours] = useState(0)
    const [horaires, setHoraires] = useState({title: '', day: [], night: [], total_hours:0})
    const [replaceAgent, toggleReplaceAgent] = useState(false)
    const [showReplaceAgentButton, toggleShowReplaceAgentButton] = useState(false)
    const [alreadyHavePlanning, setAlreadyHavePlanning] = useState(false)
    const [initPointageInEdit, setInitPointageInEdit] = useState([])
    const params = useParams()
    const firstService = queryParams.get('firstService')
    const add12Hours = queryParams.get('add12');
    const lastParams = queryParams.get('last_params') ? decodeURIComponent(queryParams.get('last_params')) : null;

    useEffect(()=>{
        if(paramsSiteId){
            axios.get("/api/site/show/" + paramsSiteId, useToken()).then((res) => {
                setSite(res.data)
            })
        }
    }, [])

    useEffect(()=>{
        if(toDuplicate) toggleCalendar(false)
    }, [])

    const handleSubmit = (e) => {
        e?.preventDefault()
        let isMounted = true
        setDisableSubmit(true)
        let new_pointages = []
        plannings.forEach((pointage) => {
            let current_planning = {
                id: pointage.id,
                day: pointage.day,
                nom: pointage.nom,
                service: pointage.service,
                comment: { id: pointage.comment?.id ? pointage.comment.id : null, content: pointage.comment.comment_content},
                employes: (pointage.employes).map(item => item.id),
            }
            new_pointages.push(current_planning)
        })
        const data = {
            site_id: site?.id,
            date_planning: datePlanning,
            pointages: new_pointages,
            duplicate: toDuplicate ? 1 : 0,
            edit: isEdit ? 1 : 0
        }
        if (isEdit) {
            data.id = params.id
        }
        if (site) {
            axios.post("/api/planning/add", data, useToken()).then((res) => {
                if (isMounted) {
                    if (res.data.error) {
                        console.error(res.data.error);
                        setError(res.data.error)
                    }
                    else if (res.data.success)
                        setNotification(res.data);
                    setDisableSubmit(false)
                }
            })
            .catch((e) => {
                console.error(e)
                setError("Erreur d'envoie, réessayez.")
                setDisableSubmit(false)
            })
        }
        else {
            setError("Veuillez choisir un site.")
            setDisableSubmit(false)
        }
        return () => {isMounted = false}
    }

    const initFormPlanning = (dtPl) => {
        if (dtPl && dtPl?.year && dtPl?.month) {
            const currentNumberDays = moment(`${dtPl.year}-${dtPl.month}`, 'YYYY-MM').daysInMonth()
            const planningTemps = []
            for (let i = 1; i <= currentNumberDays; i++) {
                const date = moment(`${dtPl.year}-${dtPl.month}-${i}`, 'YYYY-MM-DD');
                planningTemps.push({
                    id: i + "J",
                    day: i,
                    nom: date.format('ddd'),
                    service: (date.clone().set({ hour: 6, minute: 0, second: 0 })).format('YYYY-MM-DD HH:mm:ss'),
                    comment: {edition:false, comment_content: '' },
                    employes: [],
                })
                planningTemps.push({
                    id: i + "N",
                    day: i,
                    nom: date.format('ddd'),
                    service: (date.clone().set({ hour: 18, minute: 0, second: 0 })).format('YYYY-MM-DD HH:mm:ss'),
                    comment: { edition: false, comment_content: '' },
                    employes: [],
                })
            }
            return planningTemps
        }
    }
    
    useEffect(() => {
        const init = initFormPlanning(datePlanning)
        if (!params.id && init) {
            setPlannings(init);
        }
    }, [datePlanning])

    const showMessage = (currentPlan, message, title) => {
        if(!title) setTitleMessage(title)
        setWarningMessage(
            message ? 
                message 
            : 
                ("Le nombre d'agent a dépasser le nombre dans le contrat " + 
                (currentPlan ? 
                    ( moment(currentPlan.service).format('HH:mm') == '06:00' ? 
                            "JOUR :" + contrat.agent_day 
                        :
                            "NUIT :" + contrat.agent_night
                    ) 
                    : 
                        "")
                )
        )
        setShowWarning(true)
    }

    const handleAddPlan = () => {
        if (user && site?.id) {
            const newPlannings = [];
            const currentPlan = [...plannings.filter(p => p.id == currentPlId)][0]
            // const otherPlanInSameDay = [...plannings.filter(p => (p.day == currentPlan.day && p.id != currentPlan.id))][0]
            const existingUser = currentPlan?.employes?.find(p => p.id === user.id)
            let canAdd = true
            if(user.warning == 'danger') {
                setTitleMessage("Agent")
                showMessage(currentPlan, "Cet agent est déjà occupé dans d'autre planning.")
                canAdd = false
            }
            if(existingUser) {
                setTitleMessage("Agent")
                showMessage(currentPlan, "Cet agent est déjà ajouté dans ce planning.")
                canAdd = false
            }
            if(moment(currentPlan.service).isBefore(moment())){
                showMessage(null, "La date doit etre après aujourd'hui.", "Date depassée")
                canAdd = false
            }
            let currentContrat = moment(currentPlan.service).format('HH:mm') == '06:00' ? contrat.agent_day : contrat.agent_night
            if(!existingUser){
                if (canAdd) {
                    currentPlan?.employes?.push(user)
                    plannings.forEach((item) => {
                        if (item.id == currentPlId)
                            newPlannings.push(currentPlan)
                        else
                            newPlannings.push(item)
                    })
                    setPlannings(newPlannings)
                }
            }
            setUser()
            setError("")
        }
        // if(!site?.id) {
        //     setTitleMessage("Site non defini")
        //     showMessage(null, "Veuillez choisir un site.")
        // }
    }

    const handleAddComment = (id, comment) => { 
        if(site?.id){
            const newPlannings = [];
            let currentPlan = [...plannings.filter(p => p.id == id)][0]
            let currentComment = currentPlan.comment
            currentComment.comment_content = comment
            currentPlan.comment = currentComment
            plannings.forEach((item) => {
                if (item.id == id)
                    newPlannings.push(currentPlan)
                else
                    newPlannings.push(item)
            })
            setPlannings(newPlannings)
            setError("")
        }
    }
    
    useEffect(() => {
        if(site?.id){
            site.soft_delete && site.soft_delete == 1 ?
                setError("Site archivé, veuillez selectionner une autre site") 
                :
                setError("")
        }
        else 
            setError("Veuillez choisir un site.")
    }, [site])

    useEffect(() => {
        handleAddPlan()
    }, [currentPlId, user])
    
    const handleDeleteEmploye = (employe, id, setShowAgent) => {
        const newPlannings = [];
        let currentPlan = [...plannings.filter(p => p.id == id)][0]
        if(moment(currentPlan.service).isBefore(moment())){
            showMessage(null, "Impossible de modifier le planning depassé.", "Date depassée")
            if(setShowAgent) setShowAgent(false)
        }
        else {
            currentPlan.employes = currentPlan?.employes?.filter(e => e.id != employe.id)
            plannings.forEach((item) => {
                if (item.id == id)
                    newPlannings.push(currentPlan)
                else
                    newPlannings.push(item)
            })
            setPlannings(newPlannings)
        }
    }

    const getPlanning = (id) => {
        toggleLoading(true)
        setShowWarning(false)
        const urlParams = new URLSearchParams()
        if (id)
            urlParams.set('id', id)
        else {
            urlParams.set('site_id', site.id)
            urlParams.set('date_planning', datePlanning.year + '-' + datePlanning.month)
        }
        axios.get("/api/planning/get_planning?" + urlParams, useToken())
        .then((res) => {
            const resPointanges = []
            const resSite = res.data.sites
            // if (resSite.horaire || resSite.day_1 || resSite.day_2 || resSite.day_3 || resSite.day_4 || resSite.day_5 || resSite.day_6 || resSite.day_0 || resSite.day_ferie) {
                setHoraires({
                    title: resSite.horaire ?? "",
                    day:[
                        resSite.day_1 ?? 0,
                        resSite.day_2 ?? 0,
                        resSite.day_3 ?? 0,
                        resSite.day_4 ?? 0,
                        resSite.day_5 ?? 0,
                        resSite.day_6 ?? 0,
                        resSite.day_0 ?? 0,
                        resSite.day_ferie ?? 0,
                    ], 
                    night:[
                        resSite.night_1 ?? 0,
                        resSite.night_2 ?? 0,
                        resSite.night_3 ?? 0,
                        resSite.night_4 ?? 0,
                        resSite.night_5 ?? 0,
                        resSite.night_6 ?? 0,
                        resSite.night_0 ?? 0,
                        resSite.night_ferie ?? 0,
                    ],
                    total_hours: resSite.total_hour
                })
            // } else setHoraires({title: '', day: [], night: [], total_hours: 0})
            setContrat({
                agent_day: resSite.nb_agent_day_planning, 
                agent_night: resSite.nb_agent_night_planning
            })
            if (res.data.planning_pointages?.length > 0) {
                let dateRes = moment(res.data.planning.date_planning)
                dateRes = { year: dateRes.format('YYYY'), month: dateRes.format('MM') }
                let initForm = initFormPlanning(dateRes)

                if (params.id) {
                    if (site?.id != res.data.planning.site_id) {
                        setDatePlanning(dateRes)
                        setSite({
                            id: resSite.idsite,
                            nom: resSite.nom,
                            adresse: resSite.adresse
                        })
                    }
                }

                res.data.planning_pointages.forEach((pointage) => {
                    const date = moment(pointage.date_pointage, 'YYYY-MM-DD HH:mm:ss');
                    const existing = resPointanges.find((p) => p.service == date.format('YYYY-MM-DD HH:mm:ss'));
                    if (existing) {
                        if (pointage.agent_id) {
                            existing.employes.push({
                                id: pointage.agent_id,
                                matricule: matricule(pointage),
                                nom: pointage.nom,
                                soft_delete: pointage.soft_delete
                            })
                        }
                    }
                    else {
                        let curentPointage = {
                            id: pointage.id,
                            day: date.format('D'),
                            nom: date.format('ddd'),
                            service: date.format('YYYY-MM-DD HH:mm:ss'),
                            comment: { id: pointage.comment_id, comment_content: pointage.comment_content ? pointage.comment_content : "" },
                            employes: []
                        }
                        if (pointage.agent_id){
                            curentPointage.employes = [{
                                id: pointage.agent_id,
                                matricule: matricule(pointage),
                                nom: pointage.nom,
                                soft_delete: pointage.soft_delete
                            }]
                        }
                        resPointanges.push(curentPointage)
                    }
                })
                
                initForm?.forEach(pl => {
                    const existInRes = resPointanges.find((res) =>
                        moment(res.service).format('YYYY-MM-DD HH:mm:ss') == moment(pl.service).format('YYYY-MM-DD HH:mm:ss'))
                    if (!existInRes) {
                        resPointanges.push(pl)
                    }
                });
                setPlannings(sortArray(resPointanges, 'service'));
                setInitPointageInEdit(sortArray(resPointanges, 'service'));
                toggleLoading(false)
            }
            else {
                setPlannings(initFormPlanning(datePlanning))
                if (!site) {
                    setSite({
                        id: resSite.idsite,
                        nom: resSite.nom,
                        adresse: resSite.adresse
                    })
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            toggleLoading(false)
            console.error(e)
            setError("Erreur innatendue, réessayez.")
        })
    }
    
    const sortArray = (array, key) => {
        return array.sort((a, b) => {
            if (a[key] < b[key]) return -1;
            if (a[key] > b[key]) return 1;
            return 0;
        });
    }

    const handleToggleSubmit = () => {
        const hasAgents = plannings.some(pl => pl?.employes.length > 0);
        setDisableSubmit(!(site?.id && hasAgents));
        toggleShowReplaceAgentButton((site?.id && hasAgents))
    }
    useEffect(() => {
        handleToggleSubmit()
    }, [plannings, site]);

    const handleCountHours = () => { 
        if (plannings.length > 0) {
            let coutEmployes = 0
            plannings.forEach(pl => {
                if (pl.employes.length > 0) coutEmployes += pl.employes.length
            })
            setCountHours(coutEmployes * 12)
        }
    }
    useEffect(() =>  handleCountHours(),[plannings])

    useEffect(() => { 
        if(site?.id && datePlanning && !toDuplicate && !isEdit)
            getPlanning() 
    }, [site, datePlanning])

    useEffect(() => { 
        if(params.id && !toDuplicate) 
            getPlanning(params.id) 
    }, [])

    const duplicatePointage = (pointages) => {
        let dateDecalage = queryParams.get('date');
        let ptg = initFormPlanning(datePlanning);
        let nb_decalage = moment(dateDecalage).date() - 1;
        let numberDays = moment(dateDecalage, 'YYYY-MM').daysInMonth(); 
        pointages.forEach((pointage) => {
            let date = moment(pointage.date_pointage, 'YYYY-MM-DD HH:mm:ss');
            if (!date.isValid() || date.date() > numberDays) {
                return; // Ignorer ce pointage si la date est invalide
            }
            date.set('month', moment(dateDecalage).month());
            let currentDay = date.date();
            let adjustedDay = currentDay + nb_decalage + (add12Hours ? 0.5 : 0);
            if (adjustedDay <= numberDays) {
                let newDay = date.clone().add(nb_decalage, 'days');
                if (add12Hours) {
                    newDay = newDay.add(12, 'hours');
                }
                const existing = ptg.find((p) => moment(p.service).format('DD HH:mm:ss') === newDay.format('DD HH:mm:ss'));
                if (existing && !existing.employes.find(e => e.id == pointage.agent_id)) {
                    if(newDay.isAfter(moment())){
                        existing.employes.push({
                            id: pointage.agent_id,
                            matricule: matricule(pointage),
                            nom: pointage.nom,
                            soft_delete: pointage.soft_delete
                        });
                    }
                }
            }
        });

        return ptg;
    };    

    useEffect(() => { 
        if(toDuplicate && params.id) {
            let dateDecalage = queryParams.get('date');
            setDatePlanning({year: moment(dateDecalage).format('YYYY'), month: moment(dateDecalage).format('MM')})
        }
    },[])
    
    useEffect(() => { 
        if(toDuplicate && params.id && datePlanning) {
            getDataToDuplicate()
        }
    }, [toDuplicate, datePlanning])

    const getDataToDuplicate = async() =>{
        axios.get("/api/planning/show/" + params.id, useToken())
        .then((res) => {
            const pl = res.data.planning
            const pointages = duplicatePointage(pointageCustomFirstPointage(res.data.pointages))

            setPlannings(sortArray(pointages, 'service'));
            const checking = checkPlanning(pl.idsite)
            if(checking){
                setError("Ce planning existe déjà.")
                showMessage(null, "Ce planning existe déjà.", "Ce planning existe déjà")
                setDisableSubmit(true)
                setAlreadyHavePlanning(true)
            }
            else
                setAlreadyHavePlanning(false)
            setSite({id:pl.idsite, nom:pl.site})
        })
    }

    const checkPlanning = (idsite) => {
        const urlParams = new URLSearchParams()
        urlParams.set('site_id', site?.id ?? idsite)
        urlParams.set('date_planning', datePlanning.year + '-' + datePlanning.month)
        axios.get("/api/planning/get_planning?" + urlParams, useToken())
        .then((res) => {            
            if (res.data.planning_pointages?.length > 0) {
                return res.data
            }
            else 
                return null
        })
    }

    const editDatePl = () => {
        let ptg = []
        ptg = initFormPlanning(datePlanning)
        initPointageInEdit.forEach((pointage) => {
            const date = moment(pointage.service , 'YYYY-MM-DD HH:mm:ss');
            const existing = ptg.find((p) => moment(p.service).format('DD HH:mm:ss') === date.format('DD HH:mm:ss'));  
            if (existing) {
                existing.employes = pointage.employes
            }
        })
        setPlannings(sortArray(ptg, 'service'));        
    }

    useEffect(() => {
        if(isEdit && params.id && initPointageInEdit.length > 0)
            editDatePl()
        if(site?.id && datePlanning.year && datePlanning.month && datePlanning.year.trim() && datePlanning.month.trim()){
            const checking = checkPlanning(site?.id)
            if(checking){
                setError("Ce planning existe déjà.")
                showMessage(null, "Ce planning existe déjà.", "Ce planning existe déjà")
                setDisableSubmit(true)
                setAlreadyHavePlanning(true)
            } 
            else
                setAlreadyHavePlanning(false)
            axios.get("/api/planning/site_detail/" + site?.id, useToken())
            .then((res) => {
                const res_site = res.data.site
                setContrat({agent_day: res_site.nb_agent_day_planning, agent_night: res_site.nb_agent_night_planning})
                setHoraires({
                    ...horaires, title: res_site.horaire ?? "Non defini", 
                    day: [
                        res_site.day_1 ?? 0,
                        res_site.day_2 ?? 0,
                        res_site.day_3 ?? 0,
                        res_site.day_4 ?? 0,
                        res_site.day_5 ?? 0,
                        res_site.day_6 ?? 0,
                        res_site.day_0 ?? 0,
                        res_site.day_ferie ?? 0,
                    ], 
                    night:[
                        res_site.night_1 ?? 0,
                        res_site.night_2 ?? 0,
                        res_site.night_3 ?? 0,
                        res_site.night_4 ?? 0,
                        res_site.night_5 ?? 0,
                        res_site.night_6 ?? 0,
                        res_site.night_0 ?? 0,
                        res_site.night_ferie ?? 0,
                    ],
                    total_hours: res_site.total_hour
                })
            })
        }
    }, [datePlanning, site])

    const pointageCustomFirstPointage = (pointages) => {
        const firstDateOfMonth = moment(datePlanning.year + '-' + datePlanning.month).date(1).format('YYYY-MM-DD');
        const tempPtgs = [];
        const diffHourBetweenFirstPointageAndFirstService =  moment(firstService).diff(moment(firstDateOfMonth + " 06:00:00"), 'hours', true);
        pointages.forEach((ptg) => {
            let dateServiceMinusDiff = moment(ptg.date_pointage).subtract(diffHourBetweenFirstPointageAndFirstService, 'hours');
            if (moment(dateServiceMinusDiff).format('YYYY-MM') == moment(firstDateOfMonth).format('YYYY-MM')) {
                tempPtgs.push({
                    agent_id: ptg.agent_id,
                    date_pointage: moment(dateServiceMinusDiff).format('YYYY-MM-DD HH:mm:ss'),
                    id: ptg.id,
                    nom: ptg.nom,
                    num_emp_saoi: ptg.num_emp_soit,
                    num_emp_soit: ptg.num_emp_saoi,
                    numero_employe: ptg.numero_employe,
                    numero_stagiaire: ptg.numero_stagiaire,
                    societe_id: ptg.societe_id,
                    soft_delete: ptg.soft_delete,
                })
            }
        });
        return tempPtgs.sort((a, b) => a.date_pointage.localeCompare(b.date_pointage)); 
    }
    
    return (
        <div id="content">
            <div>
                {
                    notification ?
                        <Notification next={lastParams ? lastParams : notification.id ? "/planning?id=" + notification.id : "/planning"}
                            message={notification.success}
                        />
                    :
                        <form onSubmit={handleSubmit}>
                            <div className="title-container">
                                <h2>Planning</h2>
                            </div>
                            {showWarning && <ModalWarning message={warningMessage} title={titleMessage} closeModal={() => setShowWarning(false)} duration={1.5} />}
                            {
                                (isEdit || toDuplicate) ? 
                                    (
                                        site?.id ?
                                        <>
                                            <InputMonthYear value={datePlanning} onChange={setDatePlanning} defaultDate={moment(datePlanning.year + '-' + datePlanning.month)} required disable={isEdit}/>
                                            <InputSite withoutDelete actionUrl={['resp_op'].includes(auth.role) ? "/api/site/search_site_planning" :"/api/site/search_by_resp_sup"} value={site} onChange={setSite} required />
                                        </>
                                        :
                                        <LoadingScreen />
                                    )
                                :
                                    <>
                                        <InputMonthYear value={datePlanning} onChange={setDatePlanning} defaultDate={defaultDate} required/>
                                        <InputSite withoutDelete actionUrl={['resp_op'].includes(auth.role) ? "/api/site/search_site_planning" :"/api/site/search_by_resp_sup"} value={site} onChange={setSite} required />
                                    </>
                            }
                            {
                                site?.id && ( !site.soft_delete || site.soft_delete == 0 ) &&
                                <>
                                    {/* <DualContainer>
                                        <InputText label="Nombre Agent jour" value={contrat.agent_day} disabled/>
                                        <InputText label="Nombre Agent nuit" value={contrat.agent_night} disabled/>
                                    </DualContainer> */}
                                    <ViewHoraire horaires={horaires}/>
                                    <DualContainer>
                                        <InputText label="Heure planifiée" value={countHours + ' heures'} disabled/>
                                        <InputText label="Heure facturée" value={horaires.total_hours ? (horaires.total_hours + ' heures') : 'Non défini'} disabled/>
                                    </DualContainer>
                                    <InputText label="Horaire" value={horaires.title} disabled/>
                                    <div className="progress-bar">
                                        <div
                                            className="progress-bar-fill" 
                                            style={{ width: `${(countHours / (horaires.total_hours > 0 ? horaires.total_hours : 1)) * 100}%`, 
                                                backgroundColor: countHours > horaires.total_hours ? '#ee6363' : '#073570' }}
                                        />
                                    </div>
                                </>
                            }
                            {
                                isLoading ?
                                    <LoadingPage />
                                :
                                    <>
                                    {  
                                        (!(alreadyHavePlanning && duplicatePointage)) && site?.id && ( !site.soft_delete || site.soft_delete == 0 ) &&
                                        <div>
                                            <div className="action-container" style={{ marginTop: 25, marginBottom: 10 }}>
                                                <span onClick={()=> toggleCalendar(false)}> Liste </span>
                                                <span onClick={() => {toggleCalendar(true), setAddMultipleCase(false)}}> Calendrier </span>
                                                {/* <span onClick={() => {toggleCalendar(true), setAddMultipleCase(true)}}>Agent</span>
                                                {
                                                    showReplaceAgentButton &&
                                                    <span onClick={()=>toggleReplaceAgent(true)}>Remplacer agent</span>
                                                } */}
                                            </div>
                                            {/* {replaceAgent &&
                                                <span>
                                                    <ReplaceAgentModal plannings={plannings} setPlannings={setPlannings} closeModal={() => toggleReplaceAgent(false)} />
                                                </span>
                                            } */}
                                            <div>
                                                {
                                                    calendar ?
                                                        <CalendarView plannings={plannings}
                                                            datePlanning={datePlanning}
                                                            addUser={setCurrentPlId}
                                                            setShowEmploye={setShowEmploye}
                                                            handleDeleteEmploye={handleDeleteEmploye}
                                                            handleAddComment={handleAddComment}
                                                            addMultipleCase={addMultipleCase}
                                                            setAddMultipleCase={setAddMultipleCase}
                                                            currentUserToAdd={currentUserToAdd}
                                                            setCurrentUserToAdd={setCurrentUserToAdd}
                                                            contrat={contrat}
                                                            horaires={horaires}
                                                            showMessage={showMessage}
                                                            site={site}
                                                            handleCountHours={handleCountHours}
                                                            setCurrentDatePtg={setCurrentDatePtg}
                                                            handleToggleSubmit={handleToggleSubmit}
                                                        />
                                                    :
                                                        <ListView plannings={plannings}
                                                            handleDeleteEmploye={handleDeleteEmploye}
                                                            setShowEmploye={setShowEmploye}
                                                            addUser={setCurrentPlId}
                                                            setCurrentPlId={setCurrentPlId}
                                                            handleAddComment={handleAddComment}
                                                            setCurrentDatePtg={setCurrentDatePtg}
                                                            contrat={contrat}
                                                            toDuplicate={toDuplicate}
                                                            horaires={horaires}
                                                            datePlanning={datePlanning}
                                                            addMultipleCase={addMultipleCase}
                                                            setAddMultipleCase={setAddMultipleCase}
                                                            currentUserToAdd={currentUserToAdd}
                                                            setCurrentUserToAdd={setCurrentUserToAdd}
                                                            showMessage={showMessage}
                                                            site={site}
                                                            handleCountHours={handleCountHours}
                                                            handleToggleSubmit={handleToggleSubmit}
                                                            setPlannings={setPlannings}
                                                        />
                                                }
                                            </div>
                                        </div>
                                        }
                                    </>
                            }
                            {
                                showEmploye &&
                                <InputEmploye value={user}
                                    onChange={setUser}
                                    urlSearch="/api/employe/get_employe_planning"
                                    urlParams={(datePlanning ? ("?date_pointage=" + currentDatePtg + (site?.id ?"&site_id=" + site?.id : "")) : "")}
                                    closeModal={()=>setShowEmploye(false)}
                                    hideInput 
                                />
                            }
                            {
                                error &&
                                <div className='container-error'>
                                    {error}
                                </div>
                            }
                            <ButtonSubmit disabled={disableSubmit}/>
                        </form>
                }
            </div>
        </div>
  )
}
