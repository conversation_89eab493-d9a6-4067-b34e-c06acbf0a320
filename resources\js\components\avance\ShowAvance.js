import React, { useEffect, useState } from 'react'
import LoadingPage from '../loading/LoadingPage';
import ShowHeader from '../view/ShowHeader';
import { useParams } from 'react-router-dom';
import useToken from '../util/useToken';
import numberUtil from "../util/numberUtil";
import moment from 'moment/moment';
import Tab from '../layout/Tab';
import ActionAvance from './ActionAvance';
import matricule from '../util/matricule';

export default function ShowAvance({ auth, currentId, setCurrentId,setCurrentItem, size }) {
    const [isLoading, toggleLoading] = useState(false);
    const [avance, setAvance] = useState();
    const [defautUsers, setDefautUsers] = useState();
    const params = useParams()

    const updateData = () => {
        toggleLoading(true);
        let isMounted = true;
        axios.get("/api/avance/show/" + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if (isMounted) {
                setAvance(res.data)
                toggleLoading(false);
                setDefautUsers([{id: res.data.user_id, address:res.data.user_email, name:res.data.user_nom}])
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if (setCurrentItem) setCurrentItem(avance);
    }, [avance]);

    useEffect(() => updateData(), [currentId]);
    return <>
        {
            (isLoading || !avance) ? 
                <LoadingPage /> 
            :
                <div>
                    <ShowHeader label={"Avance"} id={avance.id} closeDetail={() => setCurrentId()} size={size} />
                    <div className="card-container">
                        <div className="badge-container"> 
                            <span>
                                {
                                    avance.paie_id ?
                                        <span className="badge-outline badge-outline-green">Déduit</span>
                                    :
                                        <span className={"badge-outline badge-outline-" + (avance.status_color)}>
                                            {avance.status_description}
                                        </span>
                                } {
                                    avance.nb_pj > 0 && 
                                    <span className='badge-outline'>
                                        Pièce jointe : {avance.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            <div>
                                {matricule(avance)} {avance.employe}
                            </div>
                        </h3>
                        {
                            (avance.status != "draft" && avance.date_paie) &&
                                <div>
                                    Fiche de paie : <span className='text capitalize'>{moment(avance.date_paie).format("MMMM YYYY")}</span>
                                </div>
                        }
                        <div>
                            Type : <span className='text'>{avance.type_description}</span>
                        </div>
                        <div>
                            Montant : <span className='text'>{numberUtil(avance.montant)}</span>
                        </div>
                        {avance.motif &&
                            <div>
                                Motif : <span className='text'>{avance.motif}</span>
                            </div>
                        }
                        <div>
                            Demandeur : <span className='text'>
                                {avance.user_nom} {' <' + avance.user_email + '>'}
                            </span>
                        </div>
                        <div className="card-action">
                            <ActionAvance auth={auth} avance={avance} updateData={updateData} />
                        </div>
                    </div>
                    <Tab auth={auth} name="avance_id" value={avance.id} updateData={updateData} defautUsers={defautUsers} />
            </div>
            
        }
    </>
}
