@if ($note != null)
<p style="white-space: pre-line;">
    {{$note}}
</p>
@endif
<h3>
    @if($juridique->recouvrement == 1)
        Recouvrement
    @else
        Plainte
    @endif
</h3>

<p style="white-space: pre-line;">
    @if($juridique->recouvrement == 1)
        Débiteur : {{$juridique->debiteur}} <br/>
        Contrat : {{$juridique->contrat}}<br/>
        Facture: {{$juridique->facture}} <br/>
        Montant : {{$juridique->montant}} Ar<br/>
    @else
        Site : {{$juridique->site}} <br/>
        Police ou gendarme compétent : {{$juridique->police}}<br/>
        Agent(s) concerné: {{$juridique->agent}} <br/>
        Rappelle des faits : {{$juridique->fait}} Ar<br/>

    @endif
    @if($juridique->reference)
        Réference : {{$juridique->reference}} <br/>
    @endif
    Utilisateur: {{$juridique->user_nom . ' <' . $juridique->user_email . '>'}} <br/>
</p>

@if(count($suivis) > 0)
    <h4>Suivi</h4>
@endif

@foreach ($suivis as $sv)
    {{$sv->commentaire}}<br/>
    <br/>
@endforeach
