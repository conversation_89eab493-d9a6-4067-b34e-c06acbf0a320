import React, { useEffect, useState } from 'react'
import useToken from '../util/useToken';
import LoadingPage from '../loading/LoadingPage';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useLocation } from 'react-router-dom';
import matricule from '../util/matricule';

export default function AnomaliePlanning({auth, setCurrentId, currentId, anomalies, setAnomalies}) {
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [exportModal, toggleExport] = useState(false);
    const locationSearch = useLocation().search;
    const searchItems = [
        { label: "Date d'anomalie", name: "created_at", type:"date"},
        { label: 'Site', name: 'site_id', type:'number'},
    ]

    const updateData = (initial) => {
        let isMounted = true;
        const urlParams = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            urlParams.set("offset", 0)
        }
        else
            urlParams.set("offset", anomalies.length)
        axios.get("/api/anomalie_planning?" + urlParams ,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial) {
                        setAnomalies(res.data.anomalies)
                    }
                    else {
                        const list = anomalies.slice().concat(res.data.anomalies)
                        setAnomalies(list)
                    }
                    setDataLoaded(res.data.anomalies.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => updateData(), 300);
    };
    
    return (
        <div>
            {
                isLoading ?
                    <LoadingPage />
                :
                    <div>
                        <div className="padding-container space-between">
                            <h2>Agent archivé planifié</h2>
                        </div>
                        <SearchBar listItems={searchItems} />
                        {
                            anomalies.length == 0 ?
                                <h3 className="center secondary">Aucun données trouvé</h3> 
                            :
                                <div>
                                    <InfiniteScroll dataLength={anomalies.length} 
                                        next={fetchMoreData} 
                                        hasMore={!allDataLoaded} 
                                        loader={<LoadingPage />}
                                    >
                                        <div className="line-container">
                                            <div className="row-list">
                                                <b className='line-cell-lg'>Employé</b>
                                                <b className="line-cell-xs">Nb</b>
                                                <b className="">Manager</b>
                                            </div>
                                        </div>
                                        {
                                            anomalies.map((ano) => (
                                                <React.Fragment key={ano.id}>
                                                    {   
                                                        ano.nb > 0 &&
                                                        <div onClick={() => setCurrentId(ano.id)}
                                                            className={`line-container ${currentId && currentId == ano.id ? "selected" : ""}`}
                                                        >
                                                            <div className="row-list">
                                                                <span className='line-cell-lg'>{matricule(ano) + ' ' + ano.nom}</span>
                                                                <span className='line-cell-xs'>{ano.nb}</span>
                                                                <span className=''>{ano.resp + " <" + ano.resp_email + ">"}</span>
                                                            </div>
                                                        </div>
                                                    }
                                                </React.Fragment>
                                            ))
                                        }
                                    </InfiniteScroll>
                                </div>
                }
                    </div>
            }
        </div>
    )
}
