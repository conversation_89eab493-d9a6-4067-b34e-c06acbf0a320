{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"@babel/preset-react": "^7.13.13", "@popperjs/core": "^2.10.2", "@react-pdf/renderer": "^3.0.0", "axios": "^0.21.4", "bootstrap": "^5.1.3", "exceljs": "^4.3.0", "laravel-mix": "^6.0.6", "lodash": "^4.17.19", "moment": "^2.29.3", "mysql": "^2.18.1", "nodemailer": "^6.7.8", "nodemailer-express-handlebars": "^5.0.0", "pdf-creator-node": "^2.3.5", "postcss": "^8.1.14", "react": "^17.0.2", "react-datepicker": "^4.8.0", "react-dom": "^17.0.2", "react-icons": "^4.12.0", "react-infinite-scroll-component": "^6.1.0", "react-router-dom": "^6.3.0", "resolve-url-loader": "^5.0.0", "sass": "^1.32.11", "sass-loader": "^11.0.1"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/material": "^6.1.4", "@mui/x-charts": "^7.21.0", "@react-google-maps/api": "^2.20.6", "@tinymce/tinymce-react": "^4.3.2", "dompurify": "^3.1.6", "form-data": "^4.0.0", "html-react-parser": "^5.1.9", "mysql2": "^3.14.1", "phosphor-react": "^1.4.1", "react-qr-code": "^2.0.12", "react-to-print": "^2.15.1", "tinymce": "^7.0.0"}}