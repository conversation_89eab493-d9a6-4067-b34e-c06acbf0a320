import React, { useEffect, useState } from 'react'
import ExcelJS from 'exceljs'
import matricule from '../util/matricule';
import { upperCase } from 'lodash';
import moment from 'moment';

export default function AvanceExcel({ avances, datePaie }) {
    const [dataAvances, setDataAvances] = useState(null)
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Avance');
    workbook.creator = "ADMIN DIRICKX";

    useEffect(() => {
        reformeData()
    }, [avances]);
    
    const reformeData = () => {
        let dataCurrent = [];
        for (let i = 0; i < avances.length; i++) {
            const currentAvance = avances[i];
            let matr = matricule(currentAvance)
            let dataTemp = {}
            var societe = matr.match(/[a-zA-Z]+|\d+/g)[0];
            var num_matr = matr.match(/\d+/) ? matr.match(/\d+/)[0]: "Ndf";
            if (currentAvance.group_id) {
                if (currentAvance.group_id == currentAvance.id) {
                    dataTemp = {
                        "ID": currentAvance.id,
                        "Societe": societe,
                        "Num Matricule": num_matr,
                        "Employe": currentAvance.employe,
                        "Montant": currentAvance.montant_total,
                        "Type": currentAvance.type_description,
                        "Demandeur": currentAvance.user_nom + ' < ' + currentAvance.user_email + '>'
                    }
                }
            }
            else {
                dataTemp = {
                    "ID": currentAvance.id,
                    "Societe": societe,
                    "Num Matricule": num_matr,
                    "Employe": currentAvance.employe,
                    "Montant": currentAvance.montant,
                    "Type": currentAvance.type_description,
                    "Demandeur": currentAvance.user_nom +' <'+ currentAvance.user_email + '>'
                }
            }
            dataCurrent.push(dataTemp);
        }
        dataCurrent.sort(function (a, b) {
            var compareSociete = a.Societe.localeCompare(b.Societe);
            if (compareSociete !== 0) {
                return compareSociete;
            }
            var numA = isNaN(parseInt(a['Num Matricule'])) ? 0 : parseInt(a['Num Matricule']);
            var numB = isNaN(parseInt(b['Num Matricule'])) ? 0 : parseInt(b['Num Matricule']);
            return numA - numB;
        });
        setDataAvances(dataCurrent)
    }
    const excel = () => {
        const border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
        }
        const cols =['A', 'B', 'C', 'D', 'E', 'F', 'G']
        const headers = Object.keys(dataAvances[0]);
        const headerTitle = "Avance " + moment(datePaie).format("MMM YYYY");
        worksheet.mergeCells('A1:G1');
        worksheet.getCell('A1').value = headerTitle.toUpperCase();
        worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.getCell('A1').font = { bold: true, size: 14, color: { argb: "FFFFFFFF" } }
        worksheet.getCell('A1').fill = { 
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: "327666" } 
        };
        worksheet.addRow(headers);
        if (avances) {
            let i = 2;
            dataAvances.forEach((data) => {
                const values = headers.map((header) => data[header]);
                const row =worksheet.addRow(values);
                cols.forEach((col) => {
                    row.getCell(col).border = border;
                });
                if(i % 2 != 0){
                    cols.forEach((col) => {
                        row.getCell(col).fill = {
                            type: 'pattern',
                            pattern: 'solid',
                            fgColor: { argb: "eaeaea" }
                        };
                    });
                }
                i++
            });
        }
        worksheet.columns.forEach((column) => {
            column.width = 15;
        });
        const headerRow = worksheet.getRow(2);
        headerRow.font = { bold: true, size:12 };

        worksheet.getColumn('D').width = 50
        worksheet.getColumn('F').width = 30
        worksheet.getColumn('G').width = 50
        worksheet.views = [
            {
                state: 'frozen',
                xSplit: 1,
                ySplit: 2,
            },
        ];
        worksheet.getColumn('F').eachCell((cell) => {
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.getColumn('B').eachCell((cell) => {
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
    }

    const onSubmit = () => { 
        workbook.xlsx.writeBuffer().then((buffer) => {
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = "AVANCE_" + upperCase(moment(datePaie).format("MMM YYYY")) + '.xlsx';
            a.click();
        });
    }

    return <button className="btn btn-primary" onClick={() => { excel(), onSubmit() }}>
        Exporter
    </button>
}
