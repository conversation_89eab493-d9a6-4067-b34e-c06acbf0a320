import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import InputSelect from '../input/InputSelect';

export default function StatusRecrutementModal({ closeModal, useLink }) {
    const navigate = useNavigate();
    const location = useLocation();

    const [selectType, setSelectType] = useState();

    const handleOk = () => {
        if (useLink && selectType.value) {
            let params = new URLSearchParams(location.search);
            params.set("status_recrutement", selectType.value);
            navigate(location.pathname + "?" + params);
        }
        closeModal();
    };

    return (
        <div className="modal">
            <div>
                <h3>Status du recrutement</h3>
                <InputSelect 
                    selected={selectType} 
                    setSelected={setSelectType}
                    options={[
                        { label: "En cours", value: "En cours" },
                        { label: "<PERSON><PERSON><PERSON><PERSON>", value: "<PERSON><PERSON>ru<PERSON>" },
                    ]}
                />
                <div className="form-button-container" style={{marginTop: "40px"}}>
                    <button className="btn-primary" onClick={handleOk}>OK</button>
                    <button onClick={closeModal}>Annuler</button>
                </div>
            </div>
        </div>
    );
}