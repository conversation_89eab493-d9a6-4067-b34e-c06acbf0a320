import React, { useEffect, useState } from 'react'
import LoadingPage from '../loading/LoadingPage';
import useToken from '../util/useToken';
import moment from 'moment';
import AddSuiviModal from './AddSuiviModal';

export default function SuiviPlainteTab({name, id}) {
    const [isLoading, toggleLoading] = useState(true)
    const [suivis, setSuivis] = useState([])
    const [showNoteModal, toggleNoteModal] = useState(false)

    const updateData = () => {
        let isMounted = true
        const params = new URLSearchParams()
        params.append(name, id)
        params.append("offset", 0)
        axios.get("/api/suivi_juridique?" + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else
                    setSuivis(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }
    useEffect(() => {
        updateData()
    }, []);

    return <>
        {
            showNoteModal &&
            <AddSuiviModal required name={name} id={id} closeModal={() => toggleNoteModal(false)} updateData={updateData}/>
        } 
        {
            isLoading ?
                <LoadingPage/>
            : <>
                <div className='tab-list-action'>
                    <div className='action-container'>
                        <span>
                            <span onClick={() => toggleNoteModal(true)}>Ajouté un élément</span>
                        </span>
                    </div>
                </div>
                {
                    suivis.length > 0 ?
                        suivis.map((sui) => (
                            <div key={sui.id} className='story-container'>
                                {
                                    sui.commentaire && 
                                    <p className='story-note'>
                                        {sui.commentaire}
                                    </p>
                                }
                                <div className='story-footer'>
                                    <span></span>
                                    <span>{sui.user}, {moment(sui.created_at).format("DD MMM YY")}</span>
                                </div>
                            </div>
                        ))
                    :
                        <div className="card-container secondary">
                            Aucune suivi
                        </div>
                }
            </>
        } </>
}
