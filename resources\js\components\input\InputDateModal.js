import React, { useState } from 'react';
import DatePicker, { registerLocale } from "react-datepicker";
import fr from "date-fns/locale/fr"; // the locale you want
registerLocale("fr", fr); // register it with the name you want

import "react-datepicker/dist/react-datepicker.css";
import { useLocation, useNavigate } from 'react-router-dom';
import moment from 'moment';

export default function InputDateModal({onChange, closeModal, useLink}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [selectedDate, setDate] = useState()

    const handleOk = () => {
        if(useLink) {
            let params = new URLSearchParams(location.search)
            params.set("created_at", moment(selectedDate).format("YYYY-MM-DD"))
            navigate(location.pathname + "?" + params)
        }
        onChange(selectedDate)
        closeModal()
    }

    return <div className='modal'>
        <div>
            <h2>Date</h2>
            <div className='input-container'>
                <DatePicker 
                    selected={selectedDate}
                    onChange={(date) => setDate(date)}
                    dateFormat="dd/MM/yyyy"
                    className='input-date'/>
            </div>
            <div className='form-button-container'>
                <button disabled={!selectedDate} className='btn-primary' onClick={handleOk}>OK</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
}