<?php

namespace App\Http\Util;

use App\GroupPointageSite;
use App\JourFerie;
use App\Pointage;
use App\Fonction;
use App\Agence;
use Illuminate\Support\Facades\DB;

class PaieUtil
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }
    
    static function isConfirmable($day, $month, $year){
        $current_month = (new \DateTime())->format('n');
        $current_year = (new \DateTime())->format('Y');
        if($day == 20){
            if((new \DateTime())->format('j') < 20){
                $end = (\DateTime::createFromFormat("Y-m-d H:i:s", $current_year ."-". $current_month ."-". $day . " 00:00:00"))
                    ->sub(new \DateInterval('P1D'));
            }
            else {
                $end = (\DateTime::createFromFormat("Y-m-d H:i:s", $current_year ."-". $current_month ."-". $day . " 00:00:00"))
                    ->add(new \DateInterval('P1M'))
                    ->sub(new \DateInterval('P1D'));
            }
            $end->sub(new \DateInterval('P1M'));
        }
        else {
            $end = (\DateTime::createFromFormat("Y-m-d H:i:s", $current_year ."-". $current_month ."-". $day . " 00:00:00"))
                ->sub(new \DateInterval('P1D'));
        }
        return ($end->format('n') == $month && $end->format('Y') == $year);
    }
    
    static function isAfterConfirmable($day, $month, $year){
        $current_month = (new \DateTime())->format('n');
        $current_year = (new \DateTime())->format('Y');
        if($day == 20){
            if((new \DateTime())->format('j') < 20){
                $end = (\DateTime::createFromFormat("Y-m-d H:i:s", $current_year ."-". $current_month ."-". $day . " 00:00:00"))
                    ->sub(new \DateInterval('P1D'));
            }
            else {
                $end = (\DateTime::createFromFormat("Y-m-d H:i:s", $current_year ."-". $current_month ."-". $day . " 00:00:00"))
                    ->add(new \DateInterval('P1M'))
                    ->sub(new \DateInterval('P1D'));
            }
        }
        else {
            $end = (\DateTime::createFromFormat("Y-m-d H:i:s", $current_year ."-". $current_month ."-". $day . " 00:00:00"))
                ->add(new \DateInterval('P1M'))
                ->sub(new \DateInterval('P1D'));
        }
        return ($end->format('n') == $month && $end->format('Y') == $year);
    }

    static function getIntervalByAgent($year, $month){
        $day = 20;
        if($month == 0 && $year == 0){
            if((new \DateTime())->format('j') < 20){
                $begin = (\DateTime::createFromFormat("Y-m-d H:i:s", $year ."-". $month ."-". $day . " 00:00:00"))->sub(new \DateInterval('P1M'));
                $end = (clone $begin)->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
            }
            else {
                $begin = \DateTime::createFromFormat("Y-m-d H:i:s", $year ."-". $month ."-". $day . " 00:00:00");
                $end = (clone $begin)->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
            }
        }
        else{
            $begin = (\DateTime::createFromFormat("Y-m-d H:i:s", $year ."-". $month ."-". $day . " 00:00:00"))->sub(new \DateInterval('P1M'));
            $end = (clone $begin)->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
        }
        return compact('begin', 'end');
    }

    static function getIntervalByGroup($req_group, $req_year, $req_month){
        $group = 1;
        $month = (new \DateTime())->format('n');
        $year = (new \DateTime())->format('Y');
        if($req_group != 0)
            $group = $req_group;
        $group = GroupPointageSite::find($group);
        if($req_month != 0)
            $month = $req_month;
        if($req_year != 0)
            $year = $req_year;
        if($group->day == 20){
            if($req_month == 0 && $req_year == 0){
                if((new \DateTime())->format('j') < 20){
                    $begin = (\DateTime::createFromFormat("Y-m-d H:i:s", $year ."-". $month ."-". $group->day . " 00:00:00"))->sub(new \DateInterval('P1M'));
                    $end = (clone $begin)->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
                }
                else {
                    $begin = \DateTime::createFromFormat("Y-m-d H:i:s", $year ."-". $month ."-". $group->day . " 00:00:00");
                    $end = (clone $begin)->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
                }
            }
            else {
                $begin = (\DateTime::createFromFormat("Y-m-d H:i:s", $year ."-". $month ."-". $group->day . " 00:00:00"))->sub(new \DateInterval('P1M'));
                $end = (clone $begin)->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
            }
        }
        else {
            $begin = \DateTime::createFromFormat("Y-m-d H:i:s", $year ."-". $month ."-". $group->day . " 00:00:00");
            $end = (clone $begin)->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
        }
        if($req_month == 0 && $req_year == 0){
            $begin->sub(new \DateInterval('P1M')); 
            $end = (clone $begin)->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
        }
        return compact('begin', 'end', 'group');
    }

    static function allRequest($type, $request, $query_paie, $query_agent, $query_agent_without_paie){
        $interval = PaieUtil::getIntervalByGroup($request->group, $request->year, $request->month);
        $begin = $interval['begin'];
        $end = $interval['end'];
        $group = $interval['group'];
        $month = $end->format('n');
        $year = $end->format('Y');
        
        $enable_confirm = PaieUtil::isConfirmable($group->day, $month, $year);
        $is_after_confirmable = PaieUtil::isAfterConfirmable($group->day, $month, $year);

        $group = $group->id;
        $begin_date = $begin->format('Y-m-d H:i:s');
        if($end > (new \DateTime())){
            $end = (new \DateTime())->sub(new \DateInterval('P1D'))->setTime(23, 0);
        }
        $end_date = $end->format('Y-m-d') . ' 23:00:00';
        if($begin > (new \DateTime())){
            $message = 'Encore indisponible';
            return compact('message', 'begin_date', 'end_date', 'group', 'month', 'year');
        }
        $array_jour_feries = JourFerie::select('date')->where('date', '>=', $begin)->where('date', '<=', $end)->get();
        $jour_feries = [];
        foreach($array_jour_feries as $jr){
            $jour_feries[] = $jr->date;
        }

        $paies = DB::select($query_paie, [$month, $year, $group]);
        
        $ids = [];
        foreach ($paies as $p) {
            $ids[] = $p->employe;
        }
        
        $agents = [];
        if(count($ids) > 0){
            $agents = DB::select($query_agent . " (". implode(",", $ids) .")");
            if($enable_confirm || $is_after_confirmable){
                $agents_without_paie = DB::select($query_agent_without_paie . 
                    " and a.id not in  (". implode(",", $ids) .")", [$end_date, $end_date, $end_date, $group]);
            }
        }
        else if($enable_confirm || $is_after_confirmable){
            $agents_without_paie =  DB::select($query_agent_without_paie, [$end_date, $end_date, $end_date, $group]);
        }
        
        $reclamations = [];
        $pointages = [];
        $primes = [];
        $sanctions = [];
        if($enable_confirm || $is_after_confirmable){
            $w_ids = array_column($agents_without_paie, 'id');
            if(count($w_ids) > 0){
                $only_paies = DB::select("SELECT p.id, p.employe_id, p.site_id, p.confirm_hour, p.confirm, p.societe_id, 
                        p.heure_trav, p.heure_dim, p.heure_nuit, p.heure_ferie, p.heure_reclam, s.nom as 'site'
                    from paies p 
                    LEFT JOIN sites s on s.idsite = p.site_id
                    WHERE p.month = ? and p.year = ?
                    and p.employe_id in  (". implode(",", $w_ids) .")", [$month, $year]);
                if(count($only_paies) > 0)
                    foreach($agents_without_paie as $a){
                        $notFoundPaie = true;
                        foreach($only_paies as $p){
                            if($p->employe_id == $a->id){
                                if(($type=="hour" && !$p->confirm_hour) || ($type=="paie" && !$p->confirm)){
                                    $paies[] = $p;
                                    $agents[] = $a;
                                }
                                $notFoundPaie = false;
                            }
                        }
                        if($notFoundPaie)
                            $agents[] = $a;
                    }
                else
                    foreach($agents_without_paie as $a){
                        $agents[] = $a;
                    }
            }
            $not_confirm_hour_ids = [];
            foreach($agents as $a){
                $notConfirm = true;
                foreach($paies as $p){
                    if($p->employe_id == $a->id && (($type=="hour" && $p->confirm_hour) || ($type=="paie" && $p->confirm)))
                        $notConfirm = false;
                }
                if($notConfirm)
                    $not_confirm_hour_ids[] = $a->id;
            }
            if(count($not_confirm_hour_ids) > 0){
                $pointages = Pointage::select('date_pointage', 'site_id', 'employe_id')
                    ->whereIn('employe_id', $not_confirm_hour_ids)
                    ->where('date_pointage', '>', $begin_date)
                    ->where('date_pointage', '<', $end_date)
                    ->whereNull('paie_id')
                    ->whereNull('reclamation_id')
                    ->get();
                if($type == 'paie') {
                    $primes = DB::select("SELECT p.employe_id, p.montant, p.type_id, t.code as 'type' FROM primes p 
                        LEFT JOIN type_primes t ON t.id = p.type_id
                        WHERE p.paie_id is null and p.month = ? and p.year = ? and 
                        p.employe_id IN (". implode(',', $not_confirm_hour_ids) .")", [$month, $year]);
                    $sanctions = DB::select("SELECT s.employe_id, s.montant, s.motif FROM sanctions s 
                        WHERE s.paie_id is null and s.month = ? and s.year = ? and 
                        s.employe_id IN (". implode(',', $not_confirm_hour_ids) .")", [$month, $year]);
                }

                if($enable_confirm){
                    $reclamations = Pointage::select('date_pointage', 'site_id', 'employe_id')
                        ->whereIn('employe_id', $not_confirm_hour_ids)
                        ->where('date_pointage', '>', (clone $begin)->sub(new \DateInterval('P1M'))->format('Y-m-d H:i:s'))
                        ->where('date_pointage', '<', $begin_date)
                        ->whereNull('paie_id')
                        ->whereNull('reclamation_id')
                        ->get();
                }
                else if($is_after_confirmable){
                    $interval_confirmed = PaieUtil::getIntervalByGroup($group, 0, 0);
                    $begin_confirmed = $interval_confirmed['begin']->sub(new \DateInterval("P1M"));
                    $end_confirmed = $interval_confirmed['end']->sub(new \DateInterval("P1M"));
                    $month_confirmed = $end_confirmed->format("n");
                    $year_confirmed = $end_confirmed->format("Y");
                    $paie_confirmed = DB::select("SELECT p.employe_id FROM paies p 
                        WHERE p.confirm_hour = 1 and p.month = ? and p.year = ?"
                        , [$month_confirmed, $year_confirmed]);
                    $ids_confirmed = array_column($paie_confirmed, 'employe_id');
                    $reclamations = Pointage::select('date_pointage', 'site_id', 'employe_id')
                        ->whereIn('employe_id', $ids_confirmed)
                        ->where('date_pointage', '>', (clone $begin)->sub(new \DateInterval('P1M'))->format('Y-m-d H:i:s'))
                        ->where('date_pointage', '<', $begin_date)
                        ->whereNull('paie_id')
                        ->whereNull('reclamation_id')
                        ->get();
                }
            }
        }
        if($type == 'paie')
            return compact('enable_confirm', 'begin_date', 'end_date', 'group', 'month', 'year', 
                'jour_feries', 'paies', 'agents', 'reclamations', 'pointages', 'primes', 'sanctions');
        else if($type == 'hour'){
            $hours = $paies;
            return compact('enable_confirm', 'begin_date', 'end_date', 'group', 'month', 'year', 
                'jour_feries', 'hours', 'agents', 'reclamations', 'pointages');
        }
        return false;
    }


    static function showRequest($type, $employe_id, $year, $month, $request){
        $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, 
                a.nb_heure_contrat, a.nb_heure_convenu, a.date_embauche, a.date_confirmation, a.date_conf_soit, a.sal_forfait, a.sal_base, 
                a.societe_id, s.idsite as 'site_id', s.nom as 'site', f.libelle as 'fonction', agc.libelle as 'agence',
                s.group_pointage_id as 'group_id', g.day as 'group_day', a.perdiem, a.part_variable, a.idm_depl, a.prime_anc
            FROM employes a
            LEFT JOIN agences agc ON agc.id = a.agence_id
            LEFT JOIN sites s on s.idsite = a.real_site_id
            LEFT JOIN group_pointage_sites g ON g.id = s.group_pointage_id 
            LEFT JOIN fonctions f on f.id = a.fonction_id
            WHERE a.id = ?", [$employe_id]);
        $agent = null;
        if($agents != null)
            $agent = $agents[0];
        
        $paies = DB::select("SELECT p.id, p.site_id, p.employe_id, p.month, p.year, p.group_id, p.confirm_hour, p.confirm,
            p.societe_id, p.sal_forfait, p.sal_base, p.nb_heure_contrat, p.nb_heure_convenu, p.perdiem, p.part_variable, p.idm_depl, p.edited, 
            p.prime_ex, p.prime_div, p.prime_ass, p.prime_resp, p.prime_entr, p.prime_anc, 
            p.heure_trav, p.heure_dim, p.heure_nuit, p.heure_ferie, p.heure_reclam, s.nom as 'site'
            from paies p 
            left join sites s on s.idsite = p.site_id
            WHERE p.month = ? and p.year = ? and p.employe_id = ?", [$month, $year, $employe_id]);
        $paie = null;
        if($paies != null)
            $paie = $paies[0];
        
        if($agent != null){
            if($agent->group_day == 20){
                $begin = (\DateTime::createFromFormat("Y-m-d H:i:s", $year ."-". $month ."-". $agent->group_day . " 00:00:00"))->sub(new \DateInterval('P1M'));
                $end = (clone $begin)->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
            }
            else {
                $begin = \DateTime::createFromFormat("Y-m-d H:i:s", $year ."-". $month ."-". $agent->group_day . " 00:00:00");
                $end = (clone $begin)->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
            }
            if($end > new \DateTime())
                $end = (new \DateTime())->sub(new \DateInterval('P1D'));
            $begin_date = $begin->format('Y-m-d');
            $end_date = $end->format('Y-m-d');

            $pointages = [];
            $reclamations = [];
            $jour_feries = [];
            $reclamable = true;
            $is_confirmable = PaieUtil::isConfirmable($agent->group_day, $month, $year);
            $is_after_confirmable = PaieUtil::isAfterConfirmable($agent->group_day, $month, $year);
            if($paie && $paie->confirm_hour){
                $pointages = DB::select("SELECT p.id, p.date_pointage, p.editable, s.idsite, s.nom as 'site', p.soft_delete
                    FROM pointages p
                    LEFT JOIN sites s ON s.idsite = p.site_id
                    WHERE employe_id = ? and paie_id  = ?
                    ORDER BY date_pointage desc"
                    , [$employe_id, $paie->id]);
                $reclamations = DB::select("SELECT p.id, p.date_pointage, p.editable, s.idsite, s.nom as 'site' , p.soft_delete
                    FROM pointages p
                    LEFT JOIN sites s ON s.idsite = p.site_id
                    WHERE employe_id = ? and reclamation_id  = ?
                    ORDER BY date_pointage desc"
                    , [$employe_id, $paie->id]);
            }
            else if($is_confirmable || $is_after_confirmable){
                $array_jour_feries = JourFerie::select('date')->where('date', '>=', $begin)->where('date', '<=', $end)->get();
                foreach($array_jour_feries as $jr){
                    $jour_feries[] = $jr->date;
                }
                $pointages = DB::select("SELECT p.id, p.date_pointage, p.editable, s.idsite, s.nom as 'site' , p.soft_delete
                    FROM pointages p
                    LEFT JOIN sites s ON s.idsite = p.site_id
                    WHERE paie_id is null and reclamation_id is null and 
                    employe_id = ? and date_pointage > ? and date_pointage < ?
                    ORDER BY date_pointage desc"
                , [$employe_id, $begin->format("Y-m-d H:i:s"), $end->format("Y-m-d") . " 23:00:00"]);
                
                if($is_confirmable){
                    $reclamations = DB::select("SELECT p.id, p.date_pointage, p.editable, s.idsite, s.nom as 'site' , p.soft_delete
                        FROM pointages p
                        LEFT JOIN sites s ON s.idsite = p.site_id
                        WHERE paie_id is null and reclamation_id is null and 
                        employe_id = ? and date_pointage > ? and date_pointage < ?
                        ORDER BY date_pointage desc"
                        , [$employe_id, $begin->sub(new \DateInterval('P1M'))->format("Y-m-d H:i:s"), $end->sub(new \DateInterval('P1M'))->format("Y-m-d") . " 23:00:00"]);
                }
                else {
                    $interval_confirmed = PaieUtil::getIntervalByAgent(0, 0);
                    $begin_confirmed = $interval_confirmed['begin']->sub(new \DateInterval("P1M"));
                    $end_confirmed = $interval_confirmed['end']->sub(new \DateInterval("P1M"));
                    $month_confirmed = $end_confirmed->format("n");
                    $year_confirmed = $end_confirmed->format("Y");
                    $paie_confirmed = DB::select("SELECT p.id FROM paies p 
                        WHERE p.confirm_hour = 1 and p.month = ? and p.year = ? and p.employe_id = ?"
                        , [$month_confirmed, $year_confirmed, $employe_id]);
                    if($paie_confirmed != null){
                        $reclamations = DB::select("SELECT p.id, p.date_pointage, p.editable, s.idsite, s.nom as 'site' , p.soft_delete
                            FROM pointages p
                            LEFT JOIN sites s ON s.idsite = p.site_id
                            WHERE paie_id is null and reclamation_id is null and 
                            employe_id = ? and date_pointage > ? and date_pointage < ?
                            ORDER BY date_pointage desc"
                            , [$employe_id, $begin->sub(new \DateInterval('P1M'))->format("Y-m-d H:i:s"), $end->sub(new \DateInterval('P1M'))->format("Y-m-d") . " 23:00:00"]);
                    }
                    else $reclamable = false;
                }
            }
            if($type == "paie"){
                $primes = DB::select("SELECT p.employe_id, p.montant, p.type_id, t.code as 'type' FROM primes p 
                    LEFT JOIN type_primes t ON t.id = p.type_id
                    WHERE p.month = ? and p.year = ? and p.employe_id = ?", [$month, $year, $employe_id]);
                $sanctions = DB::select("SELECT s.employe_id, s.montant, s.motif FROM sanctions s
                    WHERE s.month = ? and s.year = ? and s.employe_id = ?", [$month, $year, $employe_id]);
            return compact(
                    'is_confirmable', 'is_after_confirmable', 'reclamable', 'begin_date', 'end_date',
                    'agent', 'paie', 'reclamations', 'pointages', 'jour_feries', 'primes', 'sanctions'
                );
            }
            else if($type == "hour"){
                $hour = $paie;
                return compact(
                    'is_after_confirmable', 'reclamable', 'begin_date', 'end_date',
                    'agent', 'hour', 'reclamations', 'pointages', 'jour_feries'
                );
            }
        }
    }
}
