import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import useToken from '../util/useToken';
import ActionVisitePoste from './ActionVisitePoste';
import ShowHeader from '../view/ShowHeader';
import Tab from '../layout/Tab';
import LoadingPage from '../loading/LoadingPage';
import moment from 'moment';

export default function ShowVisitePoste({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [visite, setVisite] = useState(null)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/visite_poste/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setVisite(res.data)
                const newUser = []
                if (auth.id != res.data.user_id)
                    newUser.unshift({ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom })
                setDefautUsers(newUser)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(visite)
    }, [visite]);

    useEffect(updateData, [currentId])

    const stayOnSite = (start, end) => {
        if (start == null || end == null)
            return ""
        const diffMinutes = moment(end).diff(moment(start), 'minutes')
        if (diffMinutes < 60) {
            return `${diffMinutes} minutes`
        } else {
            const hours = Math.floor(diffMinutes / 60)
            const minutes = diffMinutes % 60
            if (minutes == 0) {
                return `${hours} heure`
            } else if (minutes == 1) {
                return (`${hours} heure ${minutes} minute`)
            } else {
                return (`${hours} heure ${minutes} minutes`)
            }
        }
    }

    return <>
        {
            (isLoading || !visite) ?
                <LoadingPage/>
            :
                <>
                    <ShowHeader label="Visite de poste" id={visite.id} closeDetail={() => setCurrentId()} size={size}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                {
                                    (auth.role != 'superviseur' && auth.id != visite.user_id) &&
                                    <span className={"badge-outline badge-outline-" + (visite.send_email ? "cyan" : visite.seen ? "green": "purple")}>
                                        { visite.send_email ? "Email envoyé" : visite.seen ? "Lu" : "Non lu" }
                                    </span>
                                }
                                {
                                    visite.start != null && visite.end != null &&
                                    <span className='badge-outline badge-outline-green'>
                                        {stayOnSite(visite.start, visite.end)}
                                    </span>
                                }
                                {
                                    visite.pointage == 1 && visite.pointeuse == 1 && 
                                    ((visite.start == null && visite.end == null) || 
                                    (visite.start == null && visite.end != null) || 
                                    (visite.start != null && visite.end == null)) &&
                                    <span className='badge-outline badge-outline-red'>
                                        ndf : {visite.start == null && visite.end == null ? "0/2" : "1/2"}
                                    </span>
                                } 
                                {
                                    visite.nb_pj > 0 &&
                                    <span className="badge-outline">
                                        Pièce jointe : {visite.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {visite.site}
                        </h3>
                        <p style={{whiteSpace: "pre-line"}}>
                            Compte rendu : <span className='text'>{visite.compte_rendu}</span>
                        </p>
                        <div>
                            Superviseur : <span className='text'>{visite.user_nom} {" <" + visite.user_email + ">"}</span>
                        </div>
                        <span>
                            {visite.date_visite &&
                                <span>Visite du : </span>
                            }
                            <span className='text'>
                                {visite.date_visite && moment(visite.date_visite).format('LL')}
                                {visite.start && visite.end && (
                                    <> de {moment(visite.start).format('HH[h]mm')} à {moment(visite.end).format('HH[h]mm')}</>
                                )}
                                {/* {visite.start && !visite.end && <> de {moment(visite.start).format('HH[h]mm')}</>}
                                {!visite.start && visite.end && <> à {moment(visite.end).format('HH[h]mm')}</>} */}
                            </span>
                                {visite.start != null && visite.end != null &&
                                    <span>  ({visite.start != null && visite.end != null && stayOnSite(visite.start, visite.end)})</span>
                                }
                        </span>
                        <div>
                            Le : <span className='text'>{moment(visite.created_at).format("DD MMMM YYYY")}</span>
                        </div>
                        <div className='card-action'>
                            <ActionVisitePoste auth={auth} visite={visite} updateData={updateData}/>
                        </div>  
                    </div>
                    <Tab auth={auth} name="visite_poste_id" value={visite.id} updateData={updateData} defautUsers={defautUsers}/>
                </>
        }
    </>
}