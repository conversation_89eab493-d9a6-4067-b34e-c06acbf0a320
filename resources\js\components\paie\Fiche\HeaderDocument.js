import React from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";
import moment from "moment";

const styles = StyleSheet.create({
    headerContainer: {
        display: "flex",
        flexDirection: "row", // Afficher les éléments côte à côte
        marginTop: -11,
    },
    billTo: {
        marginTop: -11,
        paddingBottom: 1,
        // fontFamily: "Calibri",
        width: "50%", // Largeur de chaque colonne (49% pour deux colonnes)
    },
    billToText: {
        margin: 2,
    },
});

const HeaderDocument = ({ item }) => (
    <View style={styles.headerContainer}>
        {/* Première colonne */}
        <View style={styles.billTo}>
            <Text style={styles.billToText}>Bulletin de paie</Text>
            <Text style={styles.billToText}>
                L’unité monétaire est l’ariary
            </Text>
            <Text style={styles.billToText}>
                Paye du mois{" "}
                {moment(item.date_paie).format("MMM YYYY")}
            </Text>
        </View>

        {/* Deuxième colonne */}
        <View style={styles.billTo}>
            <Text style={styles.billToText}>SURETE OCEAN INDIEN Tamatave</Text>
            <Text style={styles.billToText}>
                Activité de vente et d'installation de solution de sureté
            </Text>
            <Text style={styles.billToText}>
                Immeuble NEAMAT Boulevard Ratsimilaho
            </Text>
        </View>
    </View>
);

export default HeaderDocument;
