import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom';
import axios from 'axios';

import useToken from '../util/useToken';
import Tab from '../layout/Tab';
import LoadingPage from '../loading/LoadingPage';
import ActionStock from './ActionStock';
import ShowTextHeader from '../view/ShowTextHeader';

export default function ShowStock({auth, currentName, setCurrentName, setCurrentItem, size}) {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [stock, setStock] = useState(null)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true;
        toggleLoading(true);
        axios.get('/api/article/show_detail/' + currentName, useToken())
        .then((res) => {
            if (isMounted) {
                setStock(res.data.article)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if (setCurrentItem)
            setCurrentItem(stock)
    }, [stock]);

    useEffect(updateData, [currentName])

    return <>{
        isLoading ?
            <LoadingPage/>
        :
            <div>
                {
                    stock &&
                    <>
                        <ShowTextHeader label="Stock" texte={stock.name} closeDetail={() => setCurrentName()} size={size}/>
                        <div className='card-container'>
                            <p style={{ whiteSpace: "pre-line" }}>
                                Article : <span className='text'>{stock.designation}</span>
                            </p>
                            <p style={{ whiteSpace: "pre-line" }}>
                                Stock existant : <span className='text'>{stock.nbStock}</span>
                            </p>
                            <div className='card-action'>
                                <ActionStock auth={auth} stock={stock} updateData={updateData}/>
                            </div>
                        </div>
                        <Tab auth={auth} name="stock_id" value={stock.name} updateData={updateData} defautUsers={defautUsers}/>
                    </>
                }
            </div>
    }</>
}
