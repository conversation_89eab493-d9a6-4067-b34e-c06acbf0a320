import axios from 'axios'
import React, { useEffect, useState } from 'react';
import { IoMdClose } from 'react-icons/io';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';

export default function InputAgentMultiple({employes, setEmployes, required}) {
    const [modalOpen, toggleModal] = useState(false)
    const [data, setData] = useState([])
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [searchValue, setSearchValue] = useState("")

    const handleShowModal = (e) => {
        e.preventDefault()
        toggleModal(true)
    }

    const handleRemoveEmploye = (employe) => {
        const newEmployes = employes.filter(ag => ag.id!= employe.id)
        setEmployes(newEmployes)
    }

    const handleSelectEmploye = (ag) => {
        if(!employes.map(a => a.id).includes(ag.id)){
            toggleModal(false)
            const newEmployes = [...employes]
            newEmployes.unshift(ag)
            setEmployes(newEmployes)
            setData([])
            setSearchValue("")
        }
    }

    const handleCloseModal = () => {
        toggleModal(false)
    }
    
    const handleSearch = (initial, e) => {
        if(e) e.preventDefault()
        const params = new URLSearchParams()
        if(initial){
            setDataLoaded(true)
            setData([])
            params.set("offset", 0)
        }
        else
            params.set("offset", data.length)
        params.set("value", searchValue)

        axios.get('/api/employe/search?' + params, useToken())
        .then((res) => {
            if(res.data){
                let dataList = []
                if(res.data.employes){
                    res.data.employes.forEach(ag => {
                        dataList.push({
                            id: ag.id,
                            matricule: matricule(ag),
                            date_embauche: ag.date_embauche,
                            nom: ag.nom,
                            site: {
                                id: ag.site_id,
                                nom: ag.site,
                            }
                        })
                    });
                }
                if(initial)
                    setData(dataList)
                else {
                    const list = data.slice().concat(dataList)
                    setData(list)
                }
                setDataLoaded(res.data.employes.length < 30)
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }

    useEffect(() => {
        handleSearch(true)
    }, []);
    
    const fetchMoreData = () => {
        setTimeout(() => {
            handleSearch()
        }, 300);
    }
    return <div>
        {
            employes.length == 0 ?
                <div className='input-container'>
                    <label>Agent {required && <span className='danger'>*</span>}</label>
                    <input
                        type="text" 
                        readOnly
                        onClick={handleShowModal}
                        />
                </div>
            :
            <div className="item-header">
                <h3>Employe <span className='danger'>*</span></h3>
                <button onClick={handleShowModal} className="btn">
                    Ajouter
                </button>
            </div>
        }
        {
            employes.map(ag => (
                <div key={ag.id} className="card-container">
                    <div className='item-container'>
                        <span>
                            {ag.matricule} 
                            <span className='secondary'>
                                {' ' + ag.nom}
                            </span>
                        </span>
                        <div>
                            <span onClick={() => handleRemoveEmploye(ag)}>
                                <IoMdClose size={20}/>
                            </span>
                        </div>
                    </div>
                </div>
            ))
        }
        {
            modalOpen &&
            <div className='modal'>
                <div>
                    <h2>Employe</h2>
                    <div className='search-container'>
                        <input type="text" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} placeholder="Nom de l'employe ou matricule"/>
                        <button onClick={(e) => handleSearch(true, e)}>Rechercher</button>
                    </div>
                    <div id="scrollableList">
                        <InfiniteScroll
                            dataLength={data.length}
                            next={fetchMoreData}
                            hasMore={!allDataLoaded}
                            loader={<LoadingPage/>}
                            scrollableTarget="scrollableList"
                        >
                            <div className='list-container'>
                                <ul>
                                    {
                                        data.map(ag => {
                                            return <li key={ag.id} className={data.map(a => a.id).includes(ag.id) ? "danger" : ""}  onClick={() => handleSelectEmploye(ag)}>
                                                {ag.matricule} {ag.nom}<br/>
                                                <span className='secondary'>{ag.site.nom}</span>
                                            </li>
                                        })
                                    }
                                </ul>
                            </div>
                        </InfiniteScroll>
                    </div>
                    <div className='form-button-container'>
                        <button onClick={handleCloseModal}>Annuler</button>
                    </div>
                </div>
            </div>
        }
    </div>;
}