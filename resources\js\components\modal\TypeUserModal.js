import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import InputSelect from '../input/InputSelect';

export default function TypeUserModal({label, name, closeModal}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [type, setType] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    const typeList = ["unique", "fictif", "parent", "désactivé"]

    const handleOk = () => { 
        let params = new URLSearchParams(location.search)
        params.set("type_user", type)
        navigate(location.pathname + "?" + params)
        closeModal()
    }
    useEffect(() => {
        disableSubmit(!(type.trim()))
    }, [type])
    
    return <div className='modal'>
        <div>
            <div className='input-container' style={{ marginBottom:50 }}>
                <InputSelect
                    label={label}
                    selected={type}
                    setSelected={setType}
                    options={typeList}
                    required
                />
            </div>
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk} >Ok</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
}
