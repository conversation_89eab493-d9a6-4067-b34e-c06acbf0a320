import React from 'react'
import parse from 'html-react-parser';
import moment from 'moment';

const MessageItem = React.forwardRef(({ ms, downloadAllFiles, handlePrint }, ref) => (
		<div ref={ref} className="card-container message-log">
			
			<div className='message-user'>
				<span>
					{ms.name + " <" + ms.email + ">"}
				</span>
				<span>{moment(ms.created_at).format("DD MMM YY à HH:mm")} {handlePrint}</span>
			</div>
			<div className="message-content">
				{parse(ms.content)}
			</div>
			{
				ms.pieces.length > 0 &&
				<div className='message-pj no-print'>
					{
						ms.pieces.map(pj => (
						<span key={pj.id} className='pj-link'>
							<a className='link-no-style' target="_blank" href={"/uploads/" + pj.path} key={pj.id}>
								{pj.nature}
							</a><span> </span>
							</span> 
						))
					}
				</div>
			}
			{ms.pieces?.length > 1 && 
				<div className='action-container no-print'>
					<span onClick={() => downloadAllFiles(ms.pieces)}>Télécharger tous</span>
				</div>
			}
			
		</div>
	)
)

export default MessageItem;
