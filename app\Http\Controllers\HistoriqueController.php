<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Historique;
use App\Models\Employe;
use App\Models\Site;
use App\Models\Agence;
use App\Models\Immatriculation;
use App\Models\EtapeContentieux;
use App\Models\Fonction;
use Illuminate\Support\Facades\DB;
use Hash;
use Psy\Command\HistoryCommand;

class HistoriqueController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    protected static function getImmatriculation($id){
        $societe = Immatriculation::find($id);
        if($societe == null) return 'Null';
        return $societe->designation;
    }

    protected static function getFonction($id){
        $fonction = Fonction::find($id);
        if($fonction == null) return 'Null';
        return $fonction->libelle;
    }

    protected static function getEmploye($id){
        if(!$id) return 'Null';
        $employe = Employe::select('societe_id', 'numero_stagiaire', 'numero_employe', 'num_emp_soit', 'num_emp_saoi', 'nom')
            ->find($id);
        if($employe->societe_id == 1)
            $matricule = 'DGM-' . $employe->numero_employe;
        else if($employe->societe_id == 2)
            $matricule = 'SOIT-' . $employe->num_emp_soit;
        else if($employe->societe_id == 3)
            $matricule = 'ST-' . $employe->numero_stagiaire;
        else if($employe->societe_id == 4)
            $matricule = 'SM';
        else if($employe->societe_id == 5)
            $matricule = 'TMP-';
        else if($employe->societe_id == 6)
            $matricule = 'SAOI-' . $employe->num_emp_saoi;
        else
            $matricule = 'Ndf';
        return $matricule . ' ' . $employe->nom;
    }

    protected static function getAvanceType($id){
        if (!$id) return "Null";
        $type = DB::select("select * from type_avances where id = '$id'")[0];
        if ($type) {
            return $type->description;
        }
        return "Null";
    }

    protected static function getSite($id){
        if(!$id) return 'Null';
        $site = Site::select('nom')->find($id);
        return $site->nom;
    }

    protected static function getAgence($id){
        if(!$id) return 'Null';
        $agence = Agence::select('nom')->find($id);
        return $agence->nom;
    }

    protected static function getEtape($id){
        if(!$id) return 'Null';
        $etape = EtapeContentieux::select('nom')->find($id);
        return $etape->nom;
    }

    protected static function getUser($id){
        if(!$id) return 'Null';
        $user = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'
            from users us
            left join users ur on ur.id=us.real_email_id
            where us.id = ?", [$id])[0];
        return $user->name . ' <' . $user->email . '>';
    }

    protected static function getService($date_pointage){
        if(!$date_pointage) return 'Null';
        $dateTime = new \DateTime($date_pointage);
        $horaire = 'JOUR';
        if($dateTime->format('H:i:s') == '18:00:00')
            $horaire = 'NUIT';
        return $dateTime->format('d-m-Y') . ' ' . $horaire;
    }

    protected static function getDateTime($date){
        if(!$date) return 'Null';
        $dateTime = new \DateTime($date);
        return $dateTime->format('d-m-Y H:i');
    }

    protected static function getDate($date){
        if(!$date) return 'Null';
        $dateTime = new \DateTime($date);
        return $dateTime->format('d-m-Y');
    }
    protected static function getBoolean($value){
        if($value) return 'Oui';
        return 'Non';
    }

    public static function getMontant($nb){
        if($nb) return $nb . ' Ar';
        return "0 Ar";
    }

    protected static function getString($text){
        if($text) return $text;
        return "Null";
    }

    protected static function addNewDetail(&$detail, $label, $new){
        $detail = $detail . $label . ': ' . $new . '\n';
    }

    public static function addUpdateDetail(&$detail, $label, $old, $new){
        $detail = $detail . $label . ': ' . $old . ' -> ' . $new . '\n';
    }
    protected static function getServiceUser($id){
        if (!$id) return 'Null';
        $userService = DB::select("SELECT sc.designation
            from services sc where sc.id = ?", [$id])[0];
        return $userService->designation;
    }

    public static function action_fait_marquant(Request $request, $objet, $fait_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->fait_marquant_id = $fait_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function action_visite(Request $request, $objet, $visite_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->visite_poste_id = $visite_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_employe(Request $request, $employe){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->employe_id = $employe->id;
        $historique->objet = "Nouveau employé";
        $historique->created_at = new \DateTime();
        $detail = '';
        HistoriqueController::addNewDetail($detail, "Nom", $request->nom);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_employe(Request $request, $employe, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->employe_id = $employe->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';

        if($request->nom != $employe->nom){
            $old_nom = HistoriqueController::getString($employe->nom);
            $new_nom = HistoriqueController::getString($request->nom);
            HistoriqueController::addUpdateDetail($detail, "Nom", $old_nom, $new_nom);
        }
        if($request->societe_id != $employe->societe_id){
            $old_societe = HistoriqueController::getImmatriculation($request->societe_id);
            $new_societe = HistoriqueController::getImmatriculation($employe->societe_id);
            HistoriqueController::addUpdateDetail($detail, "Matricule", $old_societe, $new_societe);
        }
        if($request->numero_stagiaire != $employe->numero_stagiaire){
            $old_numero_stagiaire = HistoriqueController::getString($employe->numero_stagiaire);
            $new_numero_stagiaire = HistoriqueController::getString($request->numero_stagiaire);
            HistoriqueController::addUpdateDetail($detail, "Num stagiaire", $old_numero_stagiaire, $new_numero_stagiaire);
        }
        if($request->numero_employe != $employe->numero_employe){
            $old_numero_employe = HistoriqueController::getString($employe->numero_employe);
            $new_numero_employe = HistoriqueController::getString($request->numero_employe);
            HistoriqueController::addUpdateDetail($detail, "Num DGM", $old_numero_employe, $new_numero_employe);
        }
        if($request->num_emp_soit != $employe->num_emp_soit){
            $old_num_emp_soit = HistoriqueController::getString($employe->num_emp_soit);
            $new_num_emp_soit = HistoriqueController::getString($request->num_emp_soit);
            HistoriqueController::addUpdateDetail($detail, "Num SOIT", $old_num_emp_soit, $new_num_emp_soit);
        }
        if($request->date_embauche != $employe->date_embauche){
            $old_date_embauche = HistoriqueController::getDate($employe->date_embauche);
            $new_date_embauche = HistoriqueController::getDate($request->date_embauche);
            HistoriqueController::addUpdateDetail($detail, "Date embauche stagiaire", $old_date_embauche, $new_date_embauche);
        }
        if($request->date_confirmation != $employe->date_confirmation){
            $old_date_confirmation = HistoriqueController::getDate($employe->date_confirmation);
            $new_date_confirmation = HistoriqueController::getDate($request->date_confirmation);
            HistoriqueController::addUpdateDetail($detail, "Date embauche DGM", $old_date_confirmation, $new_date_confirmation);
        }
        if($request->date_conf_soit != $employe->date_conf_soit){
            $old_date_conf_soit = HistoriqueController::getDate($employe->date_conf_soit);
            $new_date_conf_soit = HistoriqueController::getDate($request->date_conf_soit);
            HistoriqueController::addUpdateDetail($detail, "Date embauche SOIT", $old_date_conf_soit, $new_date_conf_soit);
        }
        if($request->sal_base != $employe->sal_base){
            $old_sal_base = HistoriqueController::getMontant($employe->sal_base);
            $new_sal_base = HistoriqueController::getMontant($request->sal_base);
            HistoriqueController::addUpdateDetail($detail, "Salaire de base", $old_sal_base, $new_sal_base);
        }
        if($request->idm_depl != $employe->idm_depl){
            $old_idm_depl = HistoriqueController::getMontant($employe->idm_depl);
            $new_idm_depl = HistoriqueController::getMontant($request->idm_depl);
            HistoriqueController::addUpdateDetail($detail, "Salaire de base", $old_idm_depl, $new_idm_depl);
        }
        if($request->perdiem != $employe->perdiem){
            $old_perdiem = HistoriqueController::getMontant($employe->perdiem);
            $new_perdiem = HistoriqueController::getMontant($request->perdiem);
            HistoriqueController::addUpdateDetail($detail, "Perdiem", $old_perdiem, $new_perdiem);
        }
        if($request->part_variable != $employe->part_variable){
            $old_part_variable = $employe->part_variable;
            $new_part_variable = $request->part_variable;
            HistoriqueController::addUpdateDetail($detail, "Part variable", $old_part_variable, $new_part_variable);
        }
        if($request->prime_anc != $employe->prime_anc){
            $old_prime_anc = HistoriqueController::getMontant($employe->prime_anc);
            $new_prime_anc = HistoriqueController::getMontant($request->prime_anc);
            HistoriqueController::addUpdateDetail($detail, "Prime ancienneté", $old_prime_anc, $new_prime_anc);
        }
        if($request->cin != $employe->cin){
            $new_cin = HistoriqueController::getBoolean($request->cin);
            HistoriqueController::addNewDetail($detail, "CIN", $new_cin);
        }
        if($request->cv != $employe->cv){
            $new_cv = HistoriqueController::getBoolean($request->cv);
            HistoriqueController::addNewDetail($detail, "CV", $new_cv);
        }
        if($request->photo != $employe->photo){
            $new_photo = HistoriqueController::getBoolean($request->photo);
            HistoriqueController::addNewDetail($detail, "Photo", $new_photo);
        }
        if($request->residence != $employe->residence){
            $new_residence = HistoriqueController::getBoolean($request->residence);
            HistoriqueController::addNewDetail($detail, "Résidence", $new_residence);
        }
        if($request->plan_reperage != $employe->plan_reperage){
            $new_plan_reperage = HistoriqueController::getBoolean($request->plan_reperage);
            HistoriqueController::addNewDetail($detail, "Plan de repérage", $new_plan_reperage);
        }
        if($request->bulletin_n3 != $employe->bulletin_n3){
            $new_bulletin_n3 = HistoriqueController::getBoolean($request->bulletin_n3);
            HistoriqueController::addNewDetail($detail, "Bulletin N3", $new_bulletin_n3);
        }
        if($request->bonne_conduite != $employe->bonne_conduite){
            $new_bonne_conduite = HistoriqueController::getBoolean($request->bonne_conduite);
            HistoriqueController::addNewDetail($detail, "Cert. bonne conduite", $new_bonne_conduite);
        }
        if ($request->num_emp_saoi != $employe->num_emp_saoi) {
            $old_num_emp_saoi = $employe->num_emp_saoi;
            $new_num_emp_saoi = $request->num_emp_saoi;
            HistoriqueController::addUpdateDetail($detail, "Num SAOI", $old_num_emp_saoi, $new_num_emp_saoi);
        }
        if ($request->date_conf_saoi != $employe->date_conf_saoi) {
            $old_date_conf_saoi = HistoriqueController::getDate($employe->date_conf_saoi);
            $new_date_conf_saoi = HistoriqueController::getDate($request->date_conf_saoi);
            HistoriqueController::addUpdateDetail($detail, "Date embauche SAOI", $old_date_conf_saoi, $new_date_conf_saoi);
        }
        if($request->resp_part_id != $employe->resp_part_id){
            $new_resp = HistoriqueController::getEmploye($request->resp_part_id);
            $old_resp = HistoriqueController::getEmploye($employe->resp_part_id);
            HistoriqueController::addUpdateDetail($detail, "Responsable part variable", $old_resp, $new_resp);
        }
        if($request->fonction_id != $employe->fonction_id){
            $new_fonction = HistoriqueController::getFonction($request->fonction_id);
            $old_fonction = HistoriqueController::getFonction($employe->fonction_id);
            HistoriqueController::addUpdateDetail($detail, "Fonction", $old_fonction, $new_fonction);
        }
        if($request->agence_id != $employe->agence_id){
            $new_agence = HistoriqueController::getAgence($request->agence_id);
            $old_agence = HistoriqueController::getAgence($employe->agence_id);
            HistoriqueController::addUpdateDetail($detail, "Agence", $old_agence, $new_agence);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_employe(Request $request, $objet, $employe_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->employe_id = $employe_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_absence(Request $request, $absence){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->absence_id = $absence->id;
        $historique->objet = "Nouveau congé";
        if ($absence->type_absence == "mis_a_pied") {
            $historique->objet = "Nouvelle mise à pied";
        }
        if ($absence->type_absence == "permission") {
            $historique->objet = "Nouvelle permission";
        }
        $historique->created_at = new \DateTime();
        $detail = '';
        $new_employe = HistoriqueController::getEmploye($request->employe_id);
        $arp_employe = HistoriqueController::getEmploye($request->employe_remplacant_id);
        if($request->employe_id){
            $historique->employe_id = $request->employe_id;
            HistoriqueController::addNewDetail($detail, "Employe", $new_employe);
            $new_site = HistoriqueController::getSite($absence->site_id);
            $historique->site_id = $absence->site_id;
            HistoriqueController::addNewDetail($detail, "Site", $new_site);
        }
        if($request->is_tmp){
            HistoriqueController::addNewDetail($detail, "Employe Remplacant Temporaire", $request->employe_temporaire);
        }else{
            HistoriqueController::addNewDetail($detail, "Employe Remplacant", $arp_employe);
        }
        $depart = HistoriqueController::getService($request->depart);
        if ($request->type_absence == "mis_a_pied") {
            HistoriqueController::addNewDetail($detail, "Nombre de jours", $request->nb_jour);
            $superviseur = HistoriqueController::getUser($request->superviseur_id);
            HistoriqueController::addNewDetail($detail, "Superviseur", $superviseur);
        }
        HistoriqueController::addNewDetail($detail, "Départ", $depart);
        $retour = HistoriqueController::getService($request->retour);
        HistoriqueController::addNewDetail($detail, "Retour", $retour);
        HistoriqueController::addNewDetail($detail, "Motif", $request->motif);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_absence(Request $request, $absence, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->absence_id = $absence->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($absence->employe_id != $request->employe_id){
            $historique->employe_id = $request->employe_id;
            $old_employe = HistoriqueController::getEmploye($absence->employe_id);
            $new_employe = HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addUpdateDetail($detail, "Employe", $old_employe, $new_employe);
        }

        if($absence->employe_temporaire != $request->employe_temporaire){
            $old_employe_temporaire = HistoriqueController::getString($absence->employe_temporaire);
            $new_employe_temporaire = HistoriqueController::getString($request->employe_temporaire);
            HistoriqueController::addUpdateDetail($detail, "Employe Remplacant Temporaire", $old_employe_temporaire, $new_employe_temporaire);
        }
        if($absence->employe_remplacant_id != $request->employe_remplacant_id){
            $old_employe_remplacant = HistoriqueController::getEmploye($absence->employe_remplacant_id);;
            $new_employe_remplacant = HistoriqueController::getEmploye($request->employe_remplacant_id);
            HistoriqueController::addUpdateDetail($detail, "Employe Remplacant", $old_employe_remplacant, $new_employe_remplacant);
        }
        if($absence->site_id != $request->site_id){
            $historique->site_id = $request->site_id;
            $old_site = HistoriqueController::getSite($absence->site_id);
            $new_site = HistoriqueController::getSite($request->site_id);
            HistoriqueController::addUpdateDetail($detail, "Site", $old_site, $new_site);
        }
        if($absence->depart != $request->depart){
            $old_depart = HistoriqueController::getService($absence->depart);
            $new_depart = HistoriqueController::getService($request->depart);
            HistoriqueController::addUpdateDetail($detail, "Service", $old_depart, $new_depart);
        }
        if($absence->retour != $request->retour){
            $old_retour = HistoriqueController::getService($absence->retour);
            $new_retour = HistoriqueController::getService($request->retour);
            HistoriqueController::addUpdateDetail($detail, "Service", $old_retour, $new_retour);
        }
        if($absence->motif != $request->motif){
            $old_motif = HistoriqueController::getString($absence->motif . "\n");
            $new_motif = HistoriqueController::getString($request->motif);
            HistoriqueController::addUpdateDetail($detail, "Motif", $old_motif, $new_motif);
        }
        if ($absence->superviseur_id != $request->superviseur_id) {
            $old_superviseur = HistoriqueController::getUser($absence->superviseur_id);
            $new_superviseur = HistoriqueController::getUser($request->superviseur_id);
            $detail = $detail . 'Superviseur: ' . $old_superviseur . ' -> ' . $new_superviseur . '\n';
        }
        if($absence->date_pointage != $request->date_pointage) {
            $old_pointage = $absence->date_pointage;
            $new_pointage = $request->date_pointage;
            HistoriqueController::addUpdateDetail($detail, "Date de pointage", $old_pointage, $new_pointage);
        }
        if ($absence->nb_jour != $request->nb_jour) {
            $old_jour = $absence->nb_jour;
            $new_jour = $request->nb_jour;
            HistoriqueController::addUpdateDetail($detail, "Nombre de jour", $old_jour, $new_jour);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_absence(Request $request, $objet, $absence_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->absence_id = $absence_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        if($request->date_paie){
            $detail = $request->date_paie;
            $historique->detail = HistoriqueController::addNewDetail($detail,"Date de payement","");
        }
        $historique->save();
        return $historique->id;
    }

    public static function new_absence_fictif(Request $request, $absence){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name.'<'. $request->user()->email. '>';
        $historique->absence_id = $absence->id;
        $historique->objet = "Fictif";
        $historique->created_at = new \DateTime();
        $detail = '';
        HistoriqueController::addNewDetail($detail, "Nombre de jours", $request->nb_jour);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function new_equipement(Request $request, $equipement){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->equipement_id = $equipement->id;
        $historique->objet = "Ajout de la demande";
        $historique->created_at = new \DateTime();
        $detail = '';
        if($request->demande)
            HistoriqueController::addNewDetail($detail, "Demande", $request->demande);
        else{
            $articles = DB::select("SELECT ac.designation
                FROM article_equipements aeq
                LEFT JOIN articles ac ON ac.name = aeq.article
                where aeq.equipement_id = ?", [$equipement->id]);
            HistoriqueController::addNewDetail($detail, "Demande", implode(', ', array_column($articles, 'designation')));
        }
        if($request->recruiting){
            HistoriqueController::addNewDetail($detail, "Employe en cous de recrutement", "");
        }
        if($request->employe_id){
            $historique->employe_id = $request->employe_id;
            $new_employe = HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addNewDetail($detail, "Employe", $new_employe);
        }
        if($equipement->site_id){
            $historique->site_id = $equipement->site_id;
            $new_site = HistoriqueController::getSite($equipement->site_id);
            HistoriqueController::addNewDetail($detail, "Site", $new_site);
        }
        if($request->detail)
            HistoriqueController::addNewDetail($detail, "Détail", $request->detail);
        HistoriqueController::addNewDetail($detail, "Motif", $request->motif);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_equipement(Request $request, $equipement, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->equipement_id = $equipement->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($equipement->demande != $request->demande){
            $old_demande = HistoriqueController::getString($equipement->demande);
            $new_demande = HistoriqueController::getString($request->demande);
            HistoriqueController::addUpdateDetail($detail, "Demande", $old_demande, $new_demande);
        }
        else{
            $articles = DB::select("SELECT ac.designation
                FROM article_equipements aeq
                LEFT JOIN articles ac ON ac.name = aeq.article
                where aeq.equipement_id = ?", [$equipement->id]);
            HistoriqueController::addNewDetail($detail, "Demande", implode(', ', array_column($articles, 'designation')));
        }
        if($equipement->employe_id != $request->employe_id){
            $historique->employe_id = $request->employe_id;
            $old_employe = HistoriqueController::getEmploye($equipement->employe_id);
            $new_employe = HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addUpdateDetail($detail, "Employe", $old_employe, $new_employe);
        }
        if($equipement->site_id != $request->site_id){
            $historique->site_id = $request->site_id;
            $old_site = HistoriqueController::getSite($equipement->site_id);
            $new_site = HistoriqueController::getSite($request->site_id);
            HistoriqueController::addUpdateDetail($detail, "Site", $old_site, $new_site);
        }
        if($equipement->detail != $request->detail){
            $old_detail = HistoriqueController::getString($equipement->detail);
            $new_detail = HistoriqueController::getString($request->detail);
            HistoriqueController::addUpdateDetail($detail, "Détail", $old_detail, $new_detail);
        }
        if($equipement->motif != $request->motif){
            $old_motif = HistoriqueController::getString($equipement->motif . "\n");
            $new_motif = HistoriqueController::getString($request->motif);
            HistoriqueController::addUpdateDetail($detail, "Motif", $old_motif, $new_motif);
        }
        if($equipement->technicien != $request->technicien){
            $old_tech = HistoriqueController::getString($equipement->technicien);
            $new_tech = HistoriqueController::getString($request->technicien);
            HistoriqueController::addUpdateDetail($detail, "Technicien", $old_tech, $new_tech);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_equipement(Request $request, $objet, $equipement_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->equipement_id = $equipement_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_sav(Request $request, $sav_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->sav_id = $sav_id;
        $historique->objet = $request->is_tranfert ? "Transfert d'une demande d'équipement en une demande de SAV" : "Nouveau SAV";
        $historique->created_at = new \DateTime();
        $detail = '';
        $historique->site_id = $request->site_id;
        $new_site = HistoriqueController::getSite($request->site_id);
        HistoriqueController::addNewDetail($detail, "Site", $new_site);
        HistoriqueController::addNewDetail($detail, "Motif", $request->motif);
        if($request->date_sav)
            HistoriqueController::addNewDetail($detail, "Date prévu", $request->date_sav);
        if($request->technicien)
            HistoriqueController::addNewDetail($detail, "Technicien", $request->technicien);
        if($request->superviseur_id){
            $new_superviseur = HistoriqueController::getUser($request->superviseur_id);
            HistoriqueController::addNewDetail($detail, "Superviseur", $new_superviseur);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_sav(Request $request, $sav, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->sav_id = $sav->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($sav->site_id != $request->site_id){
            $historique->site_id = $request->site_id;
            $old_site = HistoriqueController::getSite($sav->site_id);
            $new_site = HistoriqueController::getSite($request->site_id);
            HistoriqueController::addUpdateDetail($detail, "Site", $old_site, $new_site);
        }
        if($sav->motif != $request->motif){
            $old_motif = HistoriqueController::getString($sav->motif . "\n");
            $new_motif = HistoriqueController::getString($request->motif);
            HistoriqueController::addUpdateDetail($detail, "Motif", $old_motif, $new_motif);
        }
        if($sav->date_sav != $request->date_sav){
            $old_date = HistoriqueController::getDate($sav->date_sav);
            $new_date = HistoriqueController::getDate($request->date_sav);
            HistoriqueController::addUpdateDetail($detail, "Date prévu", $old_date, $new_date);
        }
        if($sav->technicien != $request->technicien){
            $old_tech = HistoriqueController::getString($sav->technicien);
            $new_tech = HistoriqueController::getString($request->technicien);
            HistoriqueController::addUpdateDetail($detail, "Technicien", $old_tech, $new_tech);
        }
        if($sav->superviseur_id != $request->superviseur_id){
            $old_superviseur = HistoriqueController::getUser($sav->superviseur_id);
            $new_superviseur = HistoriqueController::getUser($request->superviseur_id);
            $detail = $detail . 'Superviseur: ' . $old_superviseur . ' -> ' . $new_superviseur . '\n';
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_sav(Request $request, $objet, $sav_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->sav_id = $sav_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_sanction(Request $request, $sanction){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->sanction_id = $sanction->id;
        $historique->objet = "Nouvelle sanction";
        $historique->created_at = new \DateTime();
        $detail = '';
        $new_employe = HistoriqueController::getEmploye($request->employe_id);
        $historique->employe_id = $request->employe_id;
        HistoriqueController::addNewDetail($detail, "Employe", $new_employe);
        $historique->site_id = $sanction->site_id;
        $new_site = HistoriqueController::getSite($sanction->site_id);
        HistoriqueController::addNewDetail($detail, "Site", $new_site);
        $service = HistoriqueController::getService($sanction->date_pointage);
        HistoriqueController::addNewDetail($detail, "Service", $service);
        if($request->objet)
            HistoriqueController::addNewDetail($detail, "Objet", $request->objet);
        if($request->montant)
            HistoriqueController::addNewDetail($detail, "Montant", $request->montant . " Ar");
        HistoriqueController::addNewDetail($detail, "Motif", $request->motif);
        if($request->superviseur_id){
            $new_superviseur = HistoriqueController::getUser($request->superviseur_id);
            HistoriqueController::addNewDetail($detail, "Superviseur", $new_superviseur);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_sanction(Request $request, $sanction, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->sanction_id = $sanction->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($sanction->employe_id != $request->employe_id){
            $historique->employe_id = $request->employe_id;
            $old_employe = HistoriqueController::getEmploye($sanction->employe_id);
            $new_employe = HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addUpdateDetail($detail, "Employe", $old_employe, $new_employe);
        }
        if($sanction->site_id != $request->site_id){
            $historique->site_id = $request->site_id;
            $old_site = HistoriqueController::getSite($sanction->site_id);
            $new_site = HistoriqueController::getSite($request->site_id);
            HistoriqueController::addUpdateDetail($detail, "Site", $old_site, $new_site);
        }
        if($sanction->date_pointage != $request->date_pointage){
            $old_service = HistoriqueController::getService($sanction->date_pointage);
            $new_service = HistoriqueController::getService($request->date_pointage);
            HistoriqueController::addUpdateDetail($detail, "Service", $old_service, $new_service);
        }
        if($sanction->objet != $request->objet){
            $old_objet = HistoriqueController::getString($sanction->objet);
            $new_objet = HistoriqueController::getString($request->objet);
            HistoriqueController::addUpdateDetail($detail, "Objet", $old_objet, $new_objet);
        }
        if($sanction->montant != $request->montant){
            $old_montant = HistoriqueController::getMontant($sanction->montant);
            $new_montant = HistoriqueController::getMontant($request->montant);
            HistoriqueController::addUpdateDetail($detail, "Montant", $old_montant, $new_montant);
        }
        if($sanction->motif != $request->motif){
            $old_motif = HistoriqueController::getString($sanction->motif . "\n");
            $new_motif = HistoriqueController::getString($request->motif);
            HistoriqueController::addUpdateDetail($detail, "Motif", $old_motif, $new_motif);
        }
        if($sanction->superviseur_id != $request->superviseur_id){
            $old_superviseur = HistoriqueController::getUser($sanction->superviseur_id);
            $new_superviseur = HistoriqueController::getUser($request->superviseur_id);
            $detail = $detail . 'Superviseur: ' . $old_superviseur . ' -> ' . $new_superviseur . '\n';
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_sanction(Request $request, $objet, $sanction_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->sanction_id = $sanction_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_flotte(Request $request, $flotte_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->flotte_id = $flotte_id;
        $historique->objet = $request->is_transfert ? "Transfert d'une demande d'équipement en une demande de Flotte" : "Nouveau Flotte";
        $historique->created_at = new \DateTime();
        $detail = '';
        $historique->site_id = $request->site_id;
        $new_site = HistoriqueController::getSite($request->site_id);
        HistoriqueController::addNewDetail($detail, "Site", $new_site);
        HistoriqueController::addNewDetail($detail, "Objet", $request->objet);
        HistoriqueController::addNewDetail($detail, "Commentaire", $request->commentaire);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_flotte(Request $request, $flotte, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->flotte_id = $flotte->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($flotte->site_id != $request->site_id){
            $historique->site_id = $request->site_id;
            $old_site = HistoriqueFlotteController::getSite($flotte->site_id);
            $new_site = HistoriqueFlotteController::getSite($request->site_id);
            HistoriqueFlotteController::addUpdateDetail($detail, "Site", $old_site, $new_site);
        }
        if($flotte->objet != $request->objet){
            $old_objet = HistoriqueFlotteController::getString($flotte->objet . "\n");
            $new_objet = HistoriqueFlotteController::getString($request->objet);
            HistoriqueFlotteController::addUpdateDetail($detail, "objet", $old_objet, $new_objet);
        }
        if($flotte->commentaire != $request->commentaire){
            $old_commentaire = HistoriqueFlotteController::getString($flotte->commentaire);
            $new_commentaire = HistoriqueFlotteController::getString($request->commentaire);
            HistoriqueFlotteController::addUpdateDetail($detail, "commentaire", $old_commentaire, $new_commentaire);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_flotte(Request $request, $objet, $flotte_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->flotte_id = $flotte_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_reclamation(Request $request, $reclamation){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->reclamation_id = $reclamation->id;
        $historique->objet = "Nouvelle reclamation";
        $historique->created_at = new \DateTime();
        $detail = '';
        $new_employe = HistoriqueController::getEmploye($request->employe_id);
        $historique->employe_id = $request->employe_id;
        HistoriqueController::addNewDetail($detail, "Employe", $new_employe);
        HistoriqueController::addNewDetail($detail, "Motif", $request->motif);
        $historique->detail = $detail;
        return $historique->save();
    }
    
    public static function action_horaire_effectif(Request $request, $horaire, $status, $old){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->site_id = $horaire->site_id;
        $historique->created_at = new \DateTime();
        $detail = '';
        $jours = [
            "Lundi" => ["day_1", "night_1"],
            "Mardi" => ["day_2", "night_2"],
            "Mercredi" => ["day_3", "night_3"],
            "Jeudi" => ["day_4", "night_4"],
            "Vendredi" => ["day_5", "night_5"],
            "Samedi" => ["day_6", "night_6"],
            "Dimanche" => ["day_0", "night_0"],
            "Férié" => ["day_ferie", "night_ferie"],
        ];
        if($status == "add"){
            $historique->objet = "Ajout horaire effectif";
            foreach ($jours as $jour => [$dayKey, $nightKey]) {
                HistoriqueController::addNewDetail($detail, "$jour JOUR", $horaire->$dayKey);
                HistoriqueController::addNewDetail($detail, "$jour NUIT", $horaire->$nightKey);
            }
        }
        else{
            $historique->objet = "Mise à jour horaire effectif";
            foreach ($jours as $jour => [$dayKey, $nightKey]) {
                if($old->$dayKey != $horaire->$dayKey){
                    HistoriqueController::addUpdateDetail($detail, "$jour JOUR", $old->$dayKey, $horaire->$dayKey);
                }
                if($old->$nightKey != $horaire->$nightKey){
                    HistoriqueController::addUpdateDetail($detail, "$jour NUIT", $old->$nightKey, $horaire->$nightKey);
                }
            }
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_reclamation(Request $request, $reclamation, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->reclamation_id = $reclamation->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($reclamation->employe_id != $request->employe_id){
            $historique->employe_id = $request->employe_id;
            $old_employe = HistoriqueController::getEmploye($reclamation->employe_id);
            $new_employe = HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addUpdateDetail($detail, "Employe", $old_employe, $new_employe);
        }
        if($reclamation->motif != $request->motif){
            $old_motif = HistoriqueController::getString($reclamation->motif . "\n");
            $new_motif = HistoriqueController::getString($request->motif);
            HistoriqueController::addUpdateDetail($detail, "Motif", $old_motif, $new_motif);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_reclamation(Request $request, $objet, $reclamation_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->reclamation_id = $reclamation_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_part_variable(Request $request, $part_variable, $critere_save){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->part_variable_id = $part_variable->id;
        $historique->objet = "Nouvelle part variable";
        $historique->created_at = new \DateTime();
        $detail = '';
        $new_employe = HistoriqueController::getEmploye($part_variable->employe_id);
        $historique->employe_id = $part_variable->employe_id;
        HistoriqueController::addNewDetail($detail, "Employé", $new_employe);
        $paie = HistoriqueController::getDate($part_variable->date_paie);
        HistoriqueController::addNewDetail($detail, "Date paie", $paie);
        foreach ($critere_save as $key => $value){
            $amount = HistoriqueController::getMontant($value['montant']);
            HistoriqueController::addNewDetail($detail, $value['critere'], $value['montant']);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_part_variable(Request $request, $part_variable, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->part_variable_id = $part_variable->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($part_variable->employe_id != $request->employe_id){
            $historique->employe_id = $request->employe_id;
            $old_employe = HistoriqueController::getEmploye($part_variable->employe_id);
            $new_employe = HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addUpdateDetail($detail, "Employé", $old_employe, $new_employe);
        }
        if($request->date_paie && $part_variable->date_paie != $request->date_paie){
            $old_date_paie = HistoriqueController::getDate($part_variable->date_paie);
            $new_date_paie = HistoriqueController::getDate($request->date_paie);
            HistoriqueController::addUpdateDetail($detail, "Date paie", $old_date_paie, $new_date_paie);
        }
        if($request->commentaire && $part_variable->commentaire != $request->commentaire){
            $old_commentaire = HistoriqueController::getString($part_variable->commentaire);
            $new_commentaire = HistoriqueController::getString($request->commentaire);
            HistoriqueController::addUpdateDetail($detail, "Commentaire", $old_commentaire, $new_commentaire);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_part_variable(Request $request, $objet, $part_variable_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->part_variable_id = $part_variable_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_critere_part(Request $request, $critere_part){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->employe_id = $critere_part->employe_id;
        $historique->objet = "Nouveau critère part variable";
        $historique->created_at = new \DateTime();
        $detail = '';
        HistoriqueController::addNewDetail($detail, "Designation: ", $critere_part->designation);
        // HistoriqueController::addNewDetail($detail, "Montant: ", $critere_part->montant);
        $historique->detail = $detail;
        $historique->save();
    }
    
    public static function update_critere_part(Request $request, $old_critere_part, $critere_part, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->employe_id = $critere_part->employe_id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($old_critere_part->designation != $critere_part->designation){
            $old_designation = HistoriqueController::getString($old_critere_part->designation);
            $new_designation = HistoriqueController::getString($critere_part->designation);
            HistoriqueController::addUpdateDetail($detail, "Designation: ", $old_designation, $new_designation);
        }
        // if($old_critere_part->montant != $critere_part->montant){
        //     if($old_critere_part->designation == $critere_part->designation){
        //         HistoriqueController::addNewDetail($detail, "Designation: ", $critere_part->designation);
        //     }
        //     $old_montant = HistoriqueController::getMontant($old_critere_part->montant);
        //     $new_montant = HistoriqueController::getMontant($critere_part->montant);
        //     HistoriqueController::addUpdateDetail($detail, "Montant: ", $old_montant, $new_montant);
        // }
        $historique->detail = $detail;
        $historique->save();
    }

    public static function soft_delete_critere_part(Request $request,$critere_part){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' .$request->user()->email . '>';
        $historique->employe_id = $critere_part->employe_id;
        $historique->objet = "Suppression de critère part";
        $historique->created_at = new \DateTime();
        $detail = '';
        HistoriqueController::addNewDetail($detail, "Designation: ", $critere_part->designation);
        HistoriqueController::addNewDetail($detail, "Montant: ", $critere_part->montant);
        $historique->detail = $detail;
        $historique->save();
    }

    public static function new_prime(Request $request, $prime){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->prime_id = $prime->id;
        $historique->objet = "Nouvelle prime";
        $historique->created_at = new \DateTime();
        $detail = '';
        $new_employe = HistoriqueController::getEmploye($request->employe_id);
        $historique->employe_id = $request->employe_id;
        HistoriqueController::addNewDetail($detail, "Employe", $new_employe);
        $historique->site_id = $prime->site_id;
        $new_site = HistoriqueController::getSite($prime->site_id);
        HistoriqueController::addNewDetail($detail, "Site", $new_site);
        $service = HistoriqueController::getService($prime->date_pointage);
        HistoriqueController::addNewDetail($detail, "Service", $service);
        if($request->objet)
            HistoriqueController::addNewDetail($detail, "Objet", $request->objet);
        if($request->montant)
            HistoriqueController::addNewDetail($detail, "Montant", $request->montant . " Ar");
        HistoriqueController::addNewDetail($detail, "Motif", $request->motif);
        if($request->superviseur_id){
            $new_superviseur = HistoriqueController::getUser($request->superviseur_id);
            HistoriqueController::addNewDetail($detail, "Superviseur", $new_superviseur);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_prime(Request $request, $prime, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->prime_id = $prime->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($prime->employe_id != $request->employe_id){
            $historique->employe_id = $request->employe_id;
            $old_employe = HistoriqueController::getEmploye($prime->employe_id);
            $new_employe = HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addUpdateDetail($detail, "Employe", $old_employe, $new_employe);
        }
        if($prime->site_id != $request->site_id){
            $historique->site_id = $request->site_id;
            $old_site = HistoriqueController::getSite($prime->site_id);
            $new_site = HistoriqueController::getSite($request->site_id);
            HistoriqueController::addUpdateDetail($detail, "Site", $old_site, $new_site);
        }
        if($request->date_pointage && $prime->date_pointage != $request->date_pointage){
            $old_service = HistoriqueController::getService($prime->date_pointage);
            $new_service = HistoriqueController::getService($request->date_pointage);
            HistoriqueController::addUpdateDetail($detail, "Service", $old_service, $new_service);
        }
        if($prime->objet != $request->objet){
            $old_objet = HistoriqueController::getString($prime->objet);
            $new_objet = HistoriqueController::getString($request->objet);
            HistoriqueController::addUpdateDetail($detail, "Objet", $old_objet, $new_objet);
        }
        if($prime->montant != $request->montant){
            $old_montant = HistoriqueController::getMontant($prime->montant);
            $new_montant = HistoriqueController::getMontant($request->montant);
            HistoriqueController::addUpdateDetail($detail, "Montant", $old_montant, $new_montant);
        }
        if($prime->motif != $request->motif){
            $old_motif = HistoriqueController::getString($prime->motif . "\n");
            $new_motif = HistoriqueController::getString($request->motif);
            HistoriqueController::addUpdateDetail($detail, "Motif", $old_motif, $new_motif);
        }
        if($prime->superviseur_id != $request->superviseur_id){
            $old_superviseur = HistoriqueController::getUser($prime->superviseur_id);
            $new_superviseur = HistoriqueController::getUser($request->superviseur_id);
            $detail = $detail . 'Superviseur: ' . $old_superviseur . ' -> ' . $new_superviseur . '\n';
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_prime(Request $request, $objet, $prime_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->prime_id = $prime_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function action_montant_prime(Request $request, $objet, $prime_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->prime_id = $prime_id;
        $historique->objet = $objet;
        $historique->note = "Montant : " . $request->montant . " Ar";
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_user(Request $request, $user){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->user_id = $user->id;
        $historique->objet = "Nouvel Utilisateur";
        $historique->created_at = new \DateTime();
        $detail = '';
        HistoriqueController::addNewDetail($detail, "Name", $request->name);
        HistoriqueController::addNewDetail($detail, "Email", $request->email);
        HistoriqueController::addNewDetail($detail, "Role", $request->role);
        HistoriqueController::addNewDetail($detail, "Email password", $request->email_password);
        HistoriqueController::addNewDetail($detail, "Real email id", $request->real_email_id);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_user(Request $request, $user, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->user_id = $user->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($user->name != $request->name){
            $old_name = $user->name;
            $new_name = $request->name;
            HistoriqueController::addUpdateDetail($detail, "Name", $old_name, $new_name);
        }
        if($user->email != $request->email){
            $old_email = $user->email;
            $new_email = $request->email;
            HistoriqueController::addUpdateDetail($detail, "Email", $old_email, $new_email);
        }
        if($user->email_password != $request->email_password){
            $old_email_password = $user->email_password;
            $new_email_password = $request->email_password;
            HistoriqueController::addUpdateDetail($detail, "Email password", $old_email_password, $new_email_password);
        }
        if($user->role != $request->role){
            $old_role = $user->role;
            $new_role = $request->role;
            HistoriqueController::addUpdateDetail($detail, "Role", $old_role, $new_role);
        }
        if($user->must_change_password != $request->must_change_password){
            $old_must_change_password = $user->must_change_password;
            $new_must_change_password = $request->must_change_password;
            HistoriqueController::addUpdateDetail($detail, "Must change password", $old_must_change_password, $new_must_change_password);
        }
        if($user->real_email_id != $request->real_email_id){
            $old_real_email_id = $user->real_email_id;
            $new_real_email_id = $request->real_email_id;
            HistoriqueController::addUpdateDetail($detail, "Real email id", $old_real_email_id, $new_real_email_id);
        }
        if ($user->service_id != $request->service_id) {
            $old_service = HistoriqueController::getServiceUser($user->service_id);
            $new_service = HistoriqueController::getServiceUser($request->service_id);
            HistoriqueController::addUpdateDetail($detail, "Service", $old_service, $new_service);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_user(Request $request, $objet, $user_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->user_id = $user_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_approvisionnement(Request $request, $approvisionnement_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->approvisionnement_id = $approvisionnement_id;
        $historique->objet = "Nouvelle DA";
        $historique->created_at = new \DateTime();
        $detail = '';

        HistoriqueController::addNewDetail($detail, "Objet", $request->objet);
        foreach($request->items as $item){
            if($item['price_only']){
                HistoriqueController::addNewDetail($detail, "designation", $item['designation']);
                HistoriqueController::addNewDetail($detail, "unite", $item['unite']);
                HistoriqueController::addNewDetail($detail, "quantite", 1);
                HistoriqueController::addNewDetail($detail, "prix", $item['prix']);
            }else {
                HistoriqueController::addNewDetail($detail, "designation", $item['designation']);
                HistoriqueController::addNewDetail($detail, "unite", $item['unite']);
                HistoriqueController::addNewDetail($detail, "quantite", $item['quantite']);
                HistoriqueController::addNewDetail($detail, "prix", $item['prix']);
            }
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function new_approvisionnement_item(Request $request, $approvisionnement_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->approvisionnement_id = $approvisionnement_id;
        $historique->objet = "Nouvelle Article ";
        $historique->created_at = new \DateTime();
        $detail = '';

        if($request->price_only){
            HistoriqueController::addNewDetail($detail, "designation", $request->designation);
            HistoriqueController::addNewDetail($detail, "prix", $request->prix);
        } else {
            HistoriqueController::addNewDetail($detail, "designation", $request->designation);
            HistoriqueController::addNewDetail($detail, "unite", $request->unite);
            HistoriqueController::addNewDetail($detail, "quantite", $request->quantite);
            HistoriqueController::addNewDetail($detail, "prix", $request->prix);
        }

        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_approvisionnement(Request $request, $approvisionnement, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->approvisionnement_id = $approvisionnement->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($approvisionnement->objet != $request->objet){
            $old_objet = $approvisionnement->objet;
            $new_objet = $request->objet;
            HistoriqueController::addUpdateDetail($detail, "Objet", $old_objet, $new_objet);
        }

        $historique->detail = $detail;
        return $historique->save();
    }
    public static function update_approvisionnement_item(Request $request, $item, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->approvisionnement_id = $item->approvisionnement_id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($item->designation != $request->designation){
            $old_designation = $item->designation;
            $new_designation = $request->designation;
            HistoriqueController::addUpdateDetail($detail, "Designation", $old_designation, $new_designation);
        }
        if($item->unite != $request->unite){
            $old_unite = $item->unite;
            $new_unite = $request->unite;
            HistoriqueController::addUpdateDetail($detail, "Unite", $old_unite, $new_unite);
        }
        if($item->quantite != $request->quantite){
            $old_quantite = $item->quantite;
            if($request->price_only == 1){
                $new_quantite = 1;
            }else{
                $new_quantite = $request->quantite;
            }
            HistoriqueController::addUpdateDetail($detail, "Quantite", $old_quantite, $new_quantite);
        }
        if($item->prix != $request->prix){
            $old_prix = $item->prix;
            $new_prix = $request->prix;
            HistoriqueController::addUpdateDetail($detail, "Prix", $old_prix, $new_prix);
        }

        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_approvisionnement(Request $request, $objet, $approvisionnement_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->approvisionnement_id = $approvisionnement_id;
        $historique->objet = $objet;

        $note = '';
        if($request->note)
            $note = $request->note;
        if($request->cout)
            $note = $note . "\n". ' Coût : ' . $request->cout. ' Ar' ;
        if($request->total)
            $note = $note . 'Total : ' . $request->total. ' Ar' ;
        if($note)
            $historique->note = $note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_fait_marquant(Request $request, $fait_marquant_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->fait_marquant_id = $fait_marquant_id;
        $historique->objet = "Nouveau fait marquant";
        $historique->created_at = new \DateTime();
        $detail = '';
        $historique->site_id = $request->site_id;
        $new_site = HistoriqueController::getSite($request->site_id);
        HistoriqueController::addNewDetail($detail, "Site", $new_site);
        HistoriqueController::addNewDetail($detail, "Objet", $request->objet);
        HistoriqueController::addNewDetail($detail, "Commentaire", $request->commentaire);
        HistoriqueController::addNewDetail($detail, "Date visite", $request->date_visite);
        HistoriqueController::addNewDetail($detail, "Début", $request->start);
        HistoriqueController::addNewDetail($detail, "Fin", $request->end);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function new_visite_poste(Request $request, $visite_poste_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->visite_poste_id = $visite_poste_id;
        $historique->objet = "Nouvelle visite de poste";
        $historique->created_at = new \DateTime();
        $detail = '';
        $historique->site_id = $request->site_id;
        $new_site = HistoriqueController::getSite($request->site_id);
        HistoriqueController::addNewDetail($detail, "Site", $new_site);
        HistoriqueController::addNewDetail($detail, "Date de visite", $request->date_visite);
        HistoriqueController::addNewDetail($detail, "Debut du visite", $request->start);
        HistoriqueController::addNewDetail($detail, "Fin du visite", $request->end);
        HistoriqueController::addNewDetail($detail, "Compte rendu", $request->compte_rendu);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function new_juridique(Request $request, $juridique){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->juridique_id = $juridique->id;
        $historique->objet = "Nouveau dossier juridique";
        $historique->created_at = new \DateTime();
        $detail = '';
        HistoriqueController::addNewDetail($detail, "Référence", $request->reference);
        HistoriqueController::addNewDetail($detail, "Agence", HistoriqueController::getAgence($request->agence_id));
        $historique->detail = $detail;
        return $historique->save();
    }
    public static function update_juridique(Request $request, $juridique, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->juridique_id = $juridique->juridique_id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($juridique->etape_id != $request->etape_id){
            $old_etape = self::getEtape($juridique->etape_id);
            $new_etape = self::getEtape($request->etape_id);
            HistoriqueController::addUpdateDetail($detail, "Status", $old_etape, $new_etape);
        }
        if($juridique->reference != $request->reference){
            $old_reference = $juridique->reference;
            $new_reference = $request->reference;
            HistoriqueController::addUpdateDetail($detail, "Référence", $old_reference, $new_reference);
        }
        if($juridique->agence_id != $request->agence_id){
            $old_agence = HistoriqueController::getAgence($juridique->agence_id);
            $new_agence = HistoriqueController::getAgence($request->agence_id);
            HistoriqueController::addUpdateDetail($detail, "Agence", $old_agence, $new_agence);
        }
        if($juridique->debiteur != $request->debiteur){
            $old_debiteur = $juridique->debiteur;
            $new_debiteur = $request->debiteur;
            HistoriqueController::addUpdateDetail($detail, "Débiteur", $old_debiteur, $new_debiteur);
        }
        if($juridique->contrat != $request->contrat){
            $old_contrat = $juridique->contrat;
            $new_contrat = $request->contrat;
            HistoriqueController::addUpdateDetail($detail, "Contrat", $old_contrat, $new_contrat);
        }
        if($juridique->facture != $request->facture){
            $old_facture = $juridique->facture;
            $new_facture = $request->facture;
            HistoriqueController::addUpdateDetail($detail, "Facture(s)", $old_facture, $new_facture);
        }
        if($juridique->montant != $request->montant){
            $old_montant = HistoriqueController::getMontant($juridique->montant);
            $new_montant = HistoriqueController::getMontant($request->montant);
            HistoriqueController::addUpdateDetail($detail, "Montant", $old_montant, $new_montant);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_juridique(Request $request, $objet, $juridique_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->juridique_id = $juridique_id;
        $historique->objet = $objet;

        $note = '';
        if($request->commentaire)
            $note = $request->commentaire;
        else if($request->note)
            $note = $request->note;
        if($note)
            $historique->note = $note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_plainte(Request $request, $juridique){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->plainte_id = $juridique->id;
        $historique->objet = "Nouveau dossier juridique";
        $historique->created_at = new \DateTime();
        $detail = '';
        HistoriqueController::addNewDetail($detail, "Référence", $request->reference);
        HistoriqueController::addNewDetail($detail, "Agence", HistoriqueController::getAgence($request->agence_id));
        $historique->detail = $detail;
        return $historique->save();
    }
    public static function update_plainte(Request $request, $juridique, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->plainte_id = $juridique->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($juridique->reference != $request->reference){
            $old_reference = $juridique->reference;
            $new_reference = $request->reference;
            HistoriqueController::addUpdateDetail($detail, "Référence", $old_reference, $new_reference);
        }
        if($juridique->agence_id != $request->agence_id){
            $old_agence = HistoriqueController::getAgence($juridique->agence_id);
            $new_agence = HistoriqueController::getAgence($request->agence_id);
            HistoriqueController::addUpdateDetail($detail, "Agence", $old_agence, $new_agence);
        }
        if($juridique->police != $request->police){
            $old_police = $juridique->police;
            $new_police = $request->police;
            HistoriqueController::addUpdateDetail($detail, "Police/Gendarme compétent", $old_police, $new_police);
        }
        if($juridique->agent != $request->agent){
            $old_agent = $juridique->agent;
            $new_agent = $request->agent;
            HistoriqueController::addUpdateDetail($detail, "Agent(s) concerné", $old_agent, $new_agent);
        }
        if($juridique->fait != $request->fait){
            $old_fait =$juridique->fait;
            $new_fait = $request->fait;
            HistoriqueController::addUpdateDetail($detail, "Rappelle des faits", $old_fait, $new_fait);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_plainte(Request $request, $objet, $plainte_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->plainte_id = $plainte_id;
        $historique->objet = $objet;

        $note = '';
        if($request->commentaire)
            $note = $request->commentaire;
        else if($request->note)
            $note = $request->note;
        if($note)
            $historique->note = $note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    protected function search(Request $request){
        if(!isset($request->offset))
            return null;

        $query = "SELECT h.id, h.objet, h.note, h.detail, h.created_at, h.user
            FROM historiques h";
        if($request->employe_id){
            $query = $query . " where h.employe_id = ?";
            $id = $request->employe_id;
        }
        else if($request->fait_marquant_id){
            $query = $query . " where h.fait_marquant_id = ?";
            $id = $request->fait_marquant_id;
        }
        else if($request->visite_poste_id){
            $query = $query . " where h.visite_poste_id = ?";
            $id = $request->visite_poste_id;
        }
        else if($request->approvisionnement_id){
            $query = $query . " where h.approvisionnement_id = ?";
            $id = $request->approvisionnement_id;
        }
        else if($request->flotte_id){
            $query = $query . " where h.flotte_id = ?";
            $id = $request->flotte_id;
        }
        else if($request->sav_id){
            $query = $query . " where h.sav_id = ?";
            $id = $request->sav_id;
        }
        else if($request->absence_id){
            $query = $query . " where h.absence_id = ?";
            $id = $request->absence_id;
        }
        else if($request->prime_id){
            $query = $query . " where h.prime_id = ?";
            $id = $request->prime_id;
        }
        else if($request->sanction_id){
            $query = $query . " where h.sanction_id = ?";
            $id = $request->sanction_id;
        }
        else if($request->equipement_id){
            $query = $query . " where h.equipement_id = ?";
            $id = $request->equipement_id;
        }
        else if($request->user_id){
            $query = $query . " where h.user_id = ?";
            $id = $request->user_id;
        }
        else if($request->juridique_id){
            $query = $query . " where h.juridique_id = ?";
            $id = $request->juridique_id;
        }
        else if($request->plainte_id){
            $query = $query . " where h.plainte_id = ?";
            $id = $request->plainte_id;
        }
        else if($request->reclamation_id){
            $query = $query . " where h.reclamation_id = ? AND h.created_at > '2025-02-10 00:00:00'";
            $id = $request->reclamation_id;
        }
        else if($request->paie_id){
            $query = $query . " where h.paie_id = ?";
            $id = $request->paie_id;
        }
        else if ($request->deduction_id) {
            $query = $query . " where h.deduction_id = ?";
            $id = $request->deduction_id;
        }
        else if ($request->avance_id) {
            $query = $query . " where h.avance_id = ?";
            $id = $request->avance_id;
        }
        else if ($request->part_variable_id) {
            $query = $query . " where h.part_variable_id = ?";
            $id = $request->part_variable_id;
        }
        else if ($request->service24_id) {
            $query = $query. " where h.service24_id =?";
            $id = $request->service24_id;
        }
        else if($request->satisfaction_id) {
            $query = $query. " where h.satisfaction_id =?";
            $id = $request->satisfaction_id;
        }
        else if($request->site_id) {
            $query = $query. " where h.site_id =?";
            $id = $request->site_id;
        }
        else if($request->planning_id) {
            $query = $query. " where h.planning_id =?";
            $id = $request->planning_id;
        }
        else if($request->dotation_id) {
            $query = $query. " where h.dotation_id =?";
            $id = $request->dotation_id;
        }
        else if($request->stock_id) {
            $query = $query. " where h.stock_id =?";
            $id = $request->stock_id;
        }
        else if($request->recrutement_id) {
            $query = $query. " where h.recrutement_id =?";
            $id = $request->recrutement_id;
        }
        else
            return null;

        $query = $query . " order by h.created_at desc, h.id desc limit ". $request->offset . ", 30";
        return compact('query', 'id');
    }

    public function tab(Request $request){
        $search = $this->search($request);
        if($search != null)
            return response()->json(DB::select($search['query'], [$search['id']]));
        else
            return response(["error" => "EACCES"]);
    }

    public static function new_paie($request, $paie){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->paie_id = $paie->id;
        $historique->objet = "Nouvelle paie";
        $historique->created_at = new \DateTime();
        $detail='';
        $employe = HistoriqueController::getEmploye($paie->employe_id);
        $historique->employe_id = $paie->employe_id;
        HistoriqueController::addNewDetail($detail, "Employe", $employe);
        $historique->site_id = $paie->site_id;
        $new_site = HistoriqueController::getSite($paie->site_id);
        HistoriqueController::addNewDetail($detail, "Site", $new_site);
        $netPayer = $request->net_a_payer;
        HistoriqueController::addNewDetail($detail, "Net à payer", $netPayer . ' Ar');
        HistoriqueController::addNewDetail($detail, "Salaire de base", $request->sal_base . ' Ar');
        HistoriqueController::addNewDetail($detail, "Heure travaille", $request->nb_heure_travaille );
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function new_deduction($request, $deduction){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->deduction_id = $deduction->id;
        $historique->objet = "Nouvelle deduction";
        $historique->created_at = new \DateTime();
        $detail='';
        $historique->employe_id = $deduction->employe_id;
        $employe = HistoriqueController::getEmploye($deduction->employe_id);
        HistoriqueController::addNewDetail($detail, "Employe", $employe);
        HistoriqueController::addNewDetail($detail, "Montant", $deduction->montant);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function new_repartition_deduction($request, $deduction){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->deduction_id = $deduction->id;
        $historique->objet = "Déduction issue de la répartition Ref: " . $deduction->group_id;
        $historique->created_at = new \DateTime();
        $detail = '';
        $historique->employe_id = $deduction->employe_id;
        $employe = HistoriqueController::getEmploye($deduction->employe_id);
        HistoriqueController::addNewDetail($detail, "Employe", $employe);
        HistoriqueController::addNewDetail($detail, "Montant", $deduction->montant);
        HistoriqueController::addNewDetail($detail, "Montant Total", $deduction->montant_total);
        // HistoriqueController::addNewDetail($detail, "Référence de déduction repartitionnée",$deduction->group_id);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function new_repartition_absence($request, $absence){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name.'<'. $request->user()->email. '>';
        $historique->absence_id = $absence->id;
        $historique->objet = "Absence issue de la répartition Ref: ". $absence->group_id;
        $historique->created_at = new \DateTime();
        $detail = '';
        $historique->employe_id = $absence->employe_id;
        $employe = HistoriqueController::getEmploye($absence->employe_id);
        HistoriqueController::addNewDetail($detail, "Employe", $employe);
        $historique->detail = $detail;
        return $historique->save();

    }

    public static function new_avance($request, $avance){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->avance_id = $avance->id;
        $historique->objet = "Nouvelle avance";
        $historique->created_at = new \DateTime();
        $detail = '';
        $historique->employe_id = $avance->employe_id;
        $employe = HistoriqueController::getEmploye($avance->employe_id);
        $type = HistoriqueController::getAvanceType($avance->type_avance_id);
        HistoriqueController::addNewDetail($detail, "Employe", $employe);
        HistoriqueController::addNewDetail($detail, "Montant", $avance->montant);
        HistoriqueController::addNewDetail($detail, "Type", $type);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function new_repartition_avance($request, $avance){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->avance_id = $avance->id;
        $historique->objet = "Avance issue de la répartition Ref: " . $avance->group_id;
        $historique->created_at = new \DateTime();
        $detail = '';
        $historique->employe_id = $avance->employe_id;
        $employe = HistoriqueController::getEmploye($avance->employe_id);
        HistoriqueController::addNewDetail($detail, "Employe", $employe);
        HistoriqueController::addNewDetail($detail, "Montant", $avance->montant);
        HistoriqueController::addNewDetail($detail, "Montant Total", $avance->montant_total);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_avance(Request $request, $old_avance, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->avance_id = $old_avance->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if ($old_avance->employe_id != $request->employe_id) {
            $historique->employe_id = $request->employe_id;
            $old_employe = HistoriqueController::getEmploye($old_avance->employe_id);
            $new_employe =  HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addUpdateDetail($detail, "Employe", $old_employe, $new_employe);
        }
        if ($old_avance->montant != $request->montant) {
            $old_montant = $old_avance->montant;
            $new_montant = $request->montant;
            HistoriqueController::addUpdateDetail($detail, "montant", $old_montant, $new_montant);
        }

        if ($old_avance->type_avance_id != $request->type_avance_id) {
            $old_type = HistoriqueController::getAvanceType($old_avance->type_avance_id);
            $new_type = HistoriqueController::getAvanceType($request->type_avance_id);
            HistoriqueController::addUpdateDetail($detail, "Type avance", $old_type, $new_type);
        }
        if ($old_avance->motif != $request->motif) {
            $old_motif = $old_avance->motif;
            $new_motif = $request->motif;
            HistoriqueController::addUpdateDetail($detail, "Motif", $old_motif, $new_motif);
        }
        $historique->detail = $detail;
        return $historique->save();

    }

    public static function update_paie(Request $request, $paie ,$objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->paie_id = $paie->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail ='';
        if($paie->employe_id != $request->employe_id){
            $historique->employe_id = $request->employe_id;
            $old_employe = HistoriqueController::getEmploye($paie->employe_id);
            $new_employe =  HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addUpdateDetail($detail, "Employe", $old_employe, $new_employe);
        }
        if($request->mode_payement != $paie->mode_payement) {
            $old_payement = $paie->mode_payement;
            $new_payement = $request->mode_payement;
            HistoriqueController::addUpdateDetail($detail, "Payement", $old_payement, $new_payement);
            if(strtolower($request->mode_payement) == "virement banquaire") {
                HistoriqueController::addNewDetail($detail,"Num banque", $request->banque.":".$request->code_banque." ".$request->code_guichet." ".$request->numero_compte." ".$request->rib);
            }
            elseif(strtolower($request->mode_payement) == "mobile money"){
                HistoriqueController::addNewDetail($detail, "Payement", $request->numero_tel);
            }
        }
        if($request->sal_base != $paie->sal_base){
            $old_sal_base = $paie->sal_base;
            $new_sal_base = $request->sal_base;
            HistoriqueController::addUpdateDetail($detail, "Salaire de base", $old_sal_base, $new_sal_base);
        }
        if($request->nb_heure_travaille != $paie->nb_heure_travaille) {
            $old_heure_travaille = $paie->nb_heure_travaille;
            $new_heure_travaille = $request->nb_heure_travaille;
            HistoriqueController::addUpdateDetail($detail, "Heure travaillé", $old_heure_travaille, $new_heure_travaille);
        }
        if($request->prime != $paie->prime){
            $old_prime = $paie->prime;
            $new_prime = $request->prime;
            HistoriqueController::addUpdateDetail($detail, "Prime", $old_prime, $new_prime);
        }
        if($request->prime_exceptionnelle != $paie->prime_exceptionnelle){
            $old_prime_exceptionnelle = $paie->prime_exceptionnelle;
            $new_prime_exceptionnelle = $request->prime_exceptionnelle;
            HistoriqueController::addUpdateDetail($detail, "Prime exceptionnelle", $old_prime_exceptionnelle, $new_prime_exceptionnelle);
        }
        if ($request->prime_anc != $paie->prime_anc) {
            $old_prime_anc = $paie->prime_anc;
            $new_prime_anc =$request->prime_anc;
            HistoriqueController::addUpdateDetail($detail,"Prime Ancienneté",$old_prime_anc, $new_prime_anc);
        }
        if($request->prime_entret != $paie->prime_entret) {
            $old_entret = $paie->prime_entret;
            $new_entret = $request->prime_entret;
            HistoriqueController::addUpdateDetail($detail, "Prime entretien", $old_entret, $new_entret);
        }
        if($request->prime_resp != $paie->prime_resp) {
            $old_resp = $paie->prime_resp;
            $new_resp = $request->prime_resp;
            HistoriqueController::addUpdateDetail($detail, "Prime de Responsabilité", $old_resp, $new_resp);
        }
        if($request->idm_depl != $paie->idm_depl) {
            $old_idm_depl = $paie->idm_depl;
            $new_idm_depl = $request->idm_depl;
            HistoriqueController::addUpdateDetail($detail, "Idm de deplacement", $old_idm_depl, $new_idm_depl);
        }
        if($request->prime_assid != $paie->prime_assid) {
            $old_prime_assid = $paie->prime_assid;
            $new_prime_assid = $request->prime_assid;
            HistoriqueController::addUpdateDetail($detail, "Prime assid", $old_prime_assid, $new_prime_assid);
        }
        if($request->prime_div != $paie->prime_div) {
            $old_prime_div = $paie->prime_div;
            $new_prime_div = $request->prime_div;
            HistoriqueController::addUpdateDetail($detail, "Prime div", $old_prime_div, $new_prime_div);
        }
        if($request->part_variable != $paie->part_variable) {
            $old_part_variable = $paie->part_variable;
            $new_part_variable = $request->part_variable;
            HistoriqueController::addUpdateDetail($detail, "Part variable", $old_part_variable, $new_part_variable);
        }
        if($request->perdiem != $paie->perdiem){
            $old_perdiem = $paie->perdiem;
            $new_perdiem = $request->perdiem;
            HistoriqueController::addUpdateDetail($detail, "Perdiem", $old_perdiem, $new_perdiem);
        }
        if ($request->retenue_formation != $paie->retenue_formation) {
            $old_retenue_formation = $paie->retenue_formation;
            $new_retenue_formation = $request->retenue_formation;
            HistoriqueController::addUpdateDetail($detail, "Retenue Formation", $old_retenue_formation, $new_retenue_formation);
        }
        if ($request->autre_deduction != $paie->autre_deduction) {
            $old_autre_deduction =$paie->autre_deduction;
            $new_autre_deduction = $request->autre_deduction;
            HistoriqueController::addUpdateDetail($detail, "Autre deduction", $old_autre_deduction, $new_autre_deduction);
        }
        if($request->avance_15e != $paie->avance_15e) {
            $old_avance_15 = $paie->avance_15e;
            $new_avance_15 =$request->avance_15e;
            HistoriqueController::addUpdateDetail($detail, "Avance 15e", $old_avance_15, $new_avance_15);
        }
        if($request->avance_special != $paie->avance_special){
            $old_avance_special = $paie->avance_special;
            $new_avance_special = $request->avance_special;
            HistoriqueController::addUpdateDetail($detail, "Avance Speciale", $old_avance_special, $new_avance_special);
        }
        if ($request->s_conge != $paie->s_conge) {
            $old_s_conge = $paie->s_conge;
            $new_s_conge = $request->s_conge;
            HistoriqueController::addUpdateDetail($detail, "Solde congé payé", $old_s_conge, $new_s_conge);
        }
        if($request->nprv_preavis_payer != $paie->nprv_preavis_payer){
            $old_nprv_preavis_payer = $paie->nprv_preavis_payer;
            $new_nprv_preavis_payer = $request->nprv_preavis_payer;
            HistoriqueController::addUpdateDetail($detail, 'Nprv preavis payer', $old_nprv_preavis_payer, $new_nprv_preavis_payer);
        }
        if($request->nprv_preavis_deductible != $paie->nprv_preavis_deductible){
            $old_nprv_preavis_deductible = $paie->nprv_preavis_deductible;
            $new_nprv_preavis_deductible = $request->nprv_preavis_deductible;
            HistoriqueController::addUpdateDetail($detail, 'Nprv preavis deductible', $old_nprv_preavis_deductible, $new_nprv_preavis_deductible);
        }
        if($request->nprv_licenciement != $paie->nprv_licenciement){
            $old_nprv_licenciement = $paie->nprv_licenciement;
            $new_nprv_licenciement = $request->nprv_licenciement;
            HistoriqueController::addUpdateDetail($detail, 'Nprv licenciement', $old_nprv_licenciement, $new_nprv_licenciement);
        }
        if($request->heure_ferie != $paie->heure_ferie){
            $old_h_ferie = $paie->heure_ferie;
            $new_h_ferie = $request->heure_ferie;
            HistoriqueController::addUpdateDetail($detail, 'Heure ferié', $old_h_ferie, $new_h_ferie);
        }
        if ($request->hm_dim != $paie->hm_dim) {
            $old_hm_dim = $paie->hm_dim;
            $new_hm_dim = $request->hm_dim;
            HistoriqueController::addUpdateDetail($detail, 'Heure maj Dimanche',$old_hm_dim, $new_hm_dim);
        }
        if($request->h_maj != $paie->h_maj) {
            $old_h_maj = $paie->h_maj;
            $new_h_maj = $request->h_maj;
            HistoriqueController::addUpdateDetail($detail, 'Heure maj', $old_h_maj, $new_h_maj);
        }
        if ($request->nb_heure_contrat != $paie->nb_heure_contrat) {
            $old_heure_contrat = $paie->nb_heure_contrat;
            $new_heure_contrat = $request->nb_heure_contrat;
            HistoriqueController::addUpdateDetail($detail, 'Heure Contrat', $old_heure_contrat, $new_heure_contrat);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_paie(Request $request, $objet, $paie_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' < ' . $request->user()->email . '>';
        $historique->paie_id = $paie_id;
        $historique->objet = $objet;
        if($request->note) $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function action_deduction(Request $request, $objet, $deduction_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' < ' . $request->user()->email . '>';
        $historique->deduction_id = $deduction_id;
        $historique->objet = $objet;
        if ($request->note) $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function action_avance(Request $request, $objet, $avance_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' < ' . $request->user()->email . '>';
        $detail ='';
        $historique->avance_id = $avance_id;
        $historique->objet = $objet;
        if ($request->note) $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function update_deduction(Request $request, $deduction, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->deduction_id = $deduction->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if ($deduction->employe_id != $request->employe_id) {
            $historique->employe_id = $request->employe_id;
            $old_employe = HistoriqueController::getEmploye($deduction->employe_id);
            $new_employe = HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addUpdateDetail($detail, "Employe", $old_employe, $new_employe);
        }
        if ($deduction->montant != $request->montant) {
            $old_montant = $deduction->montant;
            $new_montant = $request->montant;
            HistoriqueController::addUpdateDetail($detail, "montant", $old_montant, $new_montant);
        }
        if ($deduction->motif != $request->motif) {
            $old_motif = $deduction->motif;
            $new_motif = $request->motif;
            HistoriqueController::addUpdateDetail($detail, "motif", $old_motif, $new_motif);
        }

        $historique->detail =$detail;
        return $historique->save();
    }

    public static function update_site(Request $request, $site, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->site_id = $site->idsite;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if($site->superviseur_id != $request->superviseur_id){
            $old_superviseur = HistoriqueController::getUser($site->superviseur_id);
            $new_superviseur = HistoriqueController::getUser($request->superviseur_id);
            $detail = $detail . 'Superviseur: ' . $old_superviseur . ' -> ' . $new_superviseur . '\n';
        }
        if($site->resp_sup_id != $request->resp_sup_id){
            $resp_sup = HistoriqueController::getUser($site->resp_sup_id);
            $new_resp_sup = HistoriqueController::getUser($request->resp_sup_id);
            $detail = $detail . 'Responsable superviseur: ' . $resp_sup . ' -> ' . $new_resp_sup . '\n';
        }
        if($request->parent_site_id != $site->group_planning_id){
            $parent_site = Site::find($request->parent_site_id);
            $old_parent = Site::find($site->group_planning_id);
            $detail = $detail . 'Groupe planning: ' . ($old_parent ? $site->nom : "") . ' -> ' . ($parent_site ? $parent_site->nom : "Null") . '\n';
        }
        if($request->nb_agent_night_planning != $site->nb_agent_night_planning){
            $detail = $detail . 'Nb agents nuit planning: ' . $site->nb_agent_night_planning . ' -> ' . $request->nb_agent_night_planning . '\n';
        }
        if($request->nb_agent_day_planning != $site->nb_agent_day_planning){
            $detail = $detail . 'Nb agents jour planning: ' . $site->nb_agent_day_planning . ' -> ' . $request->nb_agent_day_planning . '\n';
        }
        $historique->detail =$detail;
        $historique->save();
        return $historique;
    }

    public static function seen_planning(Request $request, $planning_id, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->planning_id = $planning_id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $historique->save();
    }

    public static function groupPointage($data){
        $result =  $data
                ->sortBy('date_pointage')
                ->groupBy('date_pointage')
                ->map(function ($group) {
                    return [
                        'date_pointage' => $group->first()['date_pointage'],
                        'agents' => $group->pluck('agent_id')->all(),
                    ];
            })->values()->all();
        return $result;
    }

    public static function new_planning(Request $request, $planning){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->planning_id = $planning->id;
        $date_planning = (new \DateTime($planning->date_planning))->format('M Y');
        $site = HistoriqueController::getSite($planning->site_id);
        $historique->objet = "Màj du planning: $site [$date_planning]";
        $historique->created_at = new \DateTime();
        $detail = '';
        $heure_pointe = 0;
        $data = $planning->pointages;
        $group_pointages = self::groupPointage($data);

        if($planning->pointages){
            $heure_pointe = count($planning->pointages) * 12;
            HistoriqueController::addNewDetail($detail, "Site", $site);
            HistoriqueController::addNewDetail($detail, "Date planning", $date_planning);
            HistoriqueController::addNewDetail($detail, "Heures pointées", $heure_pointe);
            foreach($group_pointages as $group){
                $agents = '\n';
                $label = '*' . (new \DateTime($group['date_pointage']))->format('d-m-Y');
                if ((new \DateTime($group['date_pointage']))->format('H:i:s') == '18:00:00')
                    $label .= ' NUIT';
                else
                    $label .= ' JOUR';
                foreach($group['agents'] as $agent){
                    $agent = HistoriqueController::getEmploye($agent);
                    $agents .=  ' -' . $agent . '\n';
                }
                HistoriqueController::addNewDetail($detail, $label, $agents);
            }
        }
        $historique->detail = $detail;
        $historique->save();
        return $historique->detail;
    }

    public static function update_planning(Request $request, $planning, $old_planning, $old_pointage){
        $new_pointages = self::groupPointage($planning->pointages);
        $old_pointages = self::groupPointage($old_pointage);

        $history = [];

        $new_pointages_assoc = [];
        $old_pointages_assoc = [];

        foreach ($new_pointages as $pointage) {
            $new_pointages_assoc[$pointage['date_pointage']] = $pointage['agents'];
        }

        foreach ($old_pointages as $pointage) {
            $old_pointages_assoc[$pointage['date_pointage']] = $pointage['agents'];
        }

        $all_dates = array_unique(array_merge(array_keys($new_pointages_assoc), array_keys($old_pointages_assoc)));
        foreach ($all_dates as $date) {
            $new_agents = $new_pointages_assoc[$date] ?? [];
            $old_agents = $old_pointages_assoc[$date] ?? [];

            $added_agents = array_diff($new_agents, $old_agents);
            $removed_agents = array_diff($old_agents, $new_agents);

            // Check date_pointage if already exist and merge added/removed agents
            if (isset($history[$date])) {
                $history[$date]['agent_added'] = array_values(array_unique(array_merge($history[$date]['agent_added'], $added_agents)));
                $history[$date]['agent_removed'] = array_values(array_unique(array_merge($history[$date]['agent_removed'], $removed_agents)));
            } else {
                //add new entry
                $history[$date] = [
                    "date_pointage" => $date,
                    "agent_added" => array_values($added_agents),
                    "agent_removed" => array_values($removed_agents),
                ];
            }
        }
        $history = array_values($history);
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->planning_id = $planning->id;
        $historique->objet = "Mise à jour du planning";
        $historique->created_at = new \DateTime();
        $detail = '';
        if($planning->date_planning != $old_planning->date_planning){
            $old_date_planning = $old_planning->date_planning;
            $new_date_planning = $planning->date_planning;
            HistoriqueController::addUpdateDetail($detail, "Date planning", $old_date_planning, $new_date_planning);
        }
        $site = HistoriqueController::getSite($planning->site_id);
        $date_planning = (new \DateTime($planning->date_planning))->format('M Y');
        HistoriqueController::addNewDetail($detail, "Site", $site);
        HistoriqueController::addNewDetail($detail, "Date planning", $date_planning);

        $new_hour_pointage = count($planning->pointages) * 12;
        $old_hour_pointage = count($old_pointage) * 12;
        if($new_hour_pointage != $old_hour_pointage)
            HistoriqueController::addUpdateDetail($detail, "Heures pointées", $old_hour_pointage . 'h' , $new_hour_pointage . 'h \n');

        foreach($history as $h){
            $agents = '\n';
            $label = '*' . (new \DateTime($h['date_pointage']))->format('d-m-Y');
            if ((new \DateTime($h['date_pointage']))->format('H:i:s') == '18:00:00')
                $label .= ' NUIT';
            else
                $label .= ' JOUR';

            if(count($h['agent_added']) > 0){
                $agents .= 'Ajout agents : \n';
                foreach($h['agent_added'] as $agent){
                    $agent = HistoriqueController::getEmploye($agent);
                    $agents .=  ' -' . $agent . '\n';
                }
            }
            if(count($h['agent_removed']) > 0){
                $agents .= 'Suppression agents : \n';
                foreach($h['agent_removed'] as $agent){
                    $agent = HistoriqueController::getEmploye($agent);
                    $agents .=  ' -' . $agent . '\n';
                }
            }
            if(count($h['agent_removed']) > 0 || count($h['agent_added']) > 0)
                HistoriqueController::addNewDetail($detail, $label, $agents);
        }
        $historique->detail = $detail;
        $historique->save();
        return $historique->detail;
    }

    public static function assign_planning(Request $request, $planning, $old_employe, $new_employe)
    {
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->planning_id = $planning->id;
        $historique->objet = "Assigné un agent";
        $historique->created_at = new \DateTime();
        $detail = '';

        // $historique->employe_id = $planning->employe_id;
        $old_employe = HistoriqueController::getEmploye($old_employe);
        $new_employe =  HistoriqueController::getEmploye($new_employe);
        HistoriqueController::addUpdateDetail($detail, "Employe", $old_employe, $new_employe);
        HistoriqueController::addNewDetail($detail, "Date planning",  (new \DateTime($planning->date_planning))->format('M Y'));

        $pointages = $planning->pointages;
        $ptg_detail = '';
        foreach ($pointages as $ptg) {
            $label = '*' . (new \DateTime($ptg->date_pointage))->format('d-m-Y');
                if ((new \DateTime($ptg->date_pointage))->format('H:i:s') == '18:00:00')
                    $label .= ' NUIT';
                else
                    $label .= ' JOUR';
            $ptg_detail .= '\n' . $label;
        }
        HistoriqueController::addNewDetail($detail, "Pointages", $ptg_detail);
        $historique->detail = $detail;
        $historique->save();
        return $historique->detail;
    }

    public static function assign_one_pointage(Request $request, $planning, $old_pointage, $new_agent_id)
    {
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->planning_id = $planning->id;
        $historique->objet = "Assigné un agent";
        $historique->created_at = new \DateTime();
        $detail = '';
        $old_employe = HistoriqueController::getEmploye($old_pointage->agent_id);
        $new_employe =  HistoriqueController::getEmploye($new_agent_id);
        $detail .= 'Site : ' . HistoriqueController::getSite($planning->site_id) . '\n';
        $detail .= 'Date de service : ' . HistoriqueController::getService($old_pointage->date_pointage) .'\n';
        HistoriqueController::addUpdateDetail($detail, "Agent", $old_employe, $new_employe);
        $historique->detail = $detail;
        $historique->save();
        return $detail;
    }

    public static function new_service24(Request $request, $service)
    {
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->employe_id = $service->employe_id;
        $historique->service24_id = $service->id;
        $historique->objet = "Nouveau service 24";
        $historique->created_at = new \DateTime();
        $detail = '';
        $employe = HistoriqueController::getEmploye($service->employe_id);
        HistoriqueController::addNewDetail($detail, "Employé", $employe);
        HistoriqueController::addNewDetail($detail, "Début service", HistoriqueController::getService($service->begin_pointage));
        HistoriqueController::addNewDetail($detail, "Fin service", HistoriqueController::getService($service->end_pointage));
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_service24(Request $request, $objet, $service24_id)
    {
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' < ' . $request->user()->email . '>';
        $historique->service24_id = $service24_id;
        $historique->objet = $objet;
        if ($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function update_service24(Request $request, $old_service24, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->service24_id = $old_service24->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if ($request->date_pointage != $old_service24->date_pointage) {
            $old_pointage = HistoriqueController::getService($old_service24->date_pointage);
            $new_pointage = HistoriqueController::getService($request->date_pointage);
            HistoriqueController::addUpdateDetail($detail, "Date de service", $old_pointage, $new_pointage);
        }
        if ($old_service24->employe_id != $request->employe_id) {
            $historique->employe_id = $request->employe_id;
            $old_employe = HistoriqueController::getEmploye($old_service24->employe_id);
            $new_employe =  HistoriqueController::getEmploye($request->employe_id);
            HistoriqueController::addUpdateDetail($detail, "Employe", $old_employe, $new_employe);
        }
        if ($old_service24->motif != $request->motif) {
            $old_motif = $old_service24->motif;
            $new_motif = $request->motif;
            HistoriqueController::addUpdateDetail($detail, "Motif", $old_motif, $new_motif);
        }
        $historique->detail = $detail;
        $historique->save();
        return $historique;
    }

    public static function new_satisfaction(Request $request, $satisfaction, $pjName){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name.'<'. $request->user()->email. '>';
        $historique->site_id = $request->site_id;
        $historique->objet = "Nouvelle satisfaction";
        $historique->satisfaction_id = $satisfaction->id;
        $historique->created_at = new \DateTime();
        $detail = '';
        $new_site = HistoriqueController::getSite($satisfaction->site_id);
        HistoriqueController::addNewDetail($detail, "Site", $new_site);
        HistoriqueController::addNewDetail($detail, "PJ", $pjName);
        $historique->detail = $detail;
        $historique->save();
    }

    public static function update_satisfaction(Request $request, $old_satisfaction, $objet)
    {
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->satisfaction_id = $old_satisfaction->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        if ($old_satisfaction->comment != $request->comment) {
            $old_comment = $old_satisfaction->comment;
            $new_comment = $request->comment;
            HistoriqueController::addUpdateDetail($detail, "Commentaire", $old_comment, $new_comment);
        }
        if ($old_satisfaction->site_id != $request->site_id) {
            $old_site = HistoriqueController::getSite($old_satisfaction->site_id);
            $new_site = HistoriqueController::getSite($request->site_id);
            HistoriqueController::addUpdateDetail($detail, "Site", $old_site, $new_site);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_satisfaction(Request $request, $objet, $satisfaction_id)
    {
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->satisfaction_id = $satisfaction_id;
        $historique->objet = $objet;
        if ($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function action_planning(Request $request, $objet, $planning_id)
    {
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->planning_id = $planning_id;
        $historique->objet = $objet;
        if ($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_dotation($dotation)
    {
        $historique = new Historique();
        $historique->user_id = $dotation['user_id'];
        $historique->user = HistoriqueController::getUser($dotation['user_id']);
        $historique->dotation_id = $dotation['id'];
        $detail = "";
        if ($dotation['articles']) {
            $selected_articles = implode(', ', $dotation['articles']);
            HistoriqueController::addNewDetail($detail, "Articles", $selected_articles);
        }
        if ($dotation['employe_id']) {
            $historique->employe_id = $dotation['employe_id'];
            $new_employe = HistoriqueController::getEmploye($dotation['employe_id']);
            HistoriqueController::addNewDetail($detail, "Employe", $new_employe);
        }
        if ($dotation['employe_id']) {
            $employe = DB::select("SELECT site_id FROM employes WHERE id = ?", [$dotation['employe_id']])[0];
            $new_site = HistoriqueController::getSite($employe->site_id);
            HistoriqueController::addNewDetail($detail, "Site", $new_site);
        }
        $historique->objet = "Nouvelle dotation";
        $historique->created_at = new \DateTime();
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_dotation(Request $request, $objet, $dotation_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->dotation_id = $dotation_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function action_stock(Request $request, $objet, $variationStock, $stock_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->stock_id = $stock_id;
        $historique->objet = $objet;
        $detail = "";
        if ($objet == "Mise à jour du stock") {
            if ($variationStock) {
                HistoriqueController::addNewDetail($detail, "Stock existant avant", $variationStock["nbStockBefore"]);
                HistoriqueController::addNewDetail($detail, "Stock réel", $variationStock["stockReel"]);
                HistoriqueController::addNewDetail($detail, "Stock Existant actuel", $variationStock["nbStockAfter"]);
            }
        } else {
            if ($variationStock) {
                HistoriqueController::addNewDetail($detail, "Stock existant avant", $variationStock["nbStockBefore"]);
                HistoriqueController::addNewDetail($detail, "Qte ajouté", $variationStock["nbStockAfter"] - $variationStock["nbStockBefore"]);
                HistoriqueController::addNewDetail($detail, "Stock existant actuel", $variationStock["nbStockAfter"]);
            }
        }
        if($request->note)
            $historique->note = $request->note;
        $historique->detail = $detail;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }

    public static function new_recrutement(Request $request, $recrutement) {
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->recrutement_id = $recrutement->id;
        $historique->objet = "Nouveau recrutement";
        $historique->created_at = new \DateTime();
        $detail = '';
        HistoriqueController::addNewDetail($detail, "Nom", $request->nom);
        HistoriqueController::addNewDetail($detail, "Cin", $request->cin_text);
        HistoriqueController::addNewDetail($detail, "Date de naissance", $request->date_naiss);
        HistoriqueController::addNewDetail($detail, "Sexe", $request->sexe);
        HistoriqueController::addNewDetail($detail, "Adresse", $request->adresse);
        HistoriqueController::addNewDetail($detail, "Téléphone", $request->telephone);
        if ($request->email) {
            HistoriqueController::addNewDetail($detail, "Email", $request->email);
        }
        HistoriqueController::addNewDetail($detail, "Compétence en français", $request->francais);
        HistoriqueController::addNewDetail($detail, "Compétence en anglais", $request->anglais);
        HistoriqueController::addNewDetail($detail, "Experience en gardiennage", $request->xp);
        HistoriqueController::addNewDetail($detail, "Taille", $request->taille);
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function update_recrutement(Request $request, $recrutement, $objet){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->recrutement_id = $recrutement->id;
        $historique->objet = $objet;
        $historique->created_at = new \DateTime();
        $detail = '';
        
        if($request->nom != $recrutement->nom){
            $old_nom = HistoriqueController::getString($recrutement->nom);
            $new_nom = HistoriqueController::getString($request->nom);
            HistoriqueController::addUpdateDetail($detail, "Nom", $old_nom, $new_nom);
        }
        if($request->cin_text != $recrutement->cin_text){
            $old_cin_text = HistoriqueController::getString($recrutement->cin_text);
            $new_cin_text = HistoriqueController::getString($request->cin_text);
            HistoriqueController::addUpdateDetail($detail, "CIN", $old_cin_text, $new_cin_text);
        }
        if($request->date_naiss != $recrutement->date_naiss){
            $old_date_naiss = HistoriqueController::getDate($recrutement->date_naiss);
            $new_date_naiss = HistoriqueController::getDate($request->date_naiss);
            HistoriqueController::addUpdateDetail($detail, "Date de naissance", $old_date_naiss, $new_date_naiss);
        }
        if($request->sexe != $recrutement->sexe){
            $old_sexe = HistoriqueController::getString($recrutement->sexe);
            $new_sexe = HistoriqueController::getString($request->sexe);
            HistoriqueController::addUpdateDetail($detail, "Sexe", $old_sexe, $new_sexe);
        }
        if($request->adresse != $recrutement->adresse){
            $old_adresse = HistoriqueController::getString($recrutement->adresse);
            $new_adresse = HistoriqueController::getString($request->adresse);
            HistoriqueController::addUpdateDetail($detail, "Adresse", $old_adresse, $new_adresse);
        }
        if($request->latitude != $recrutement->latitude){
            $old_latitude = HistoriqueController::getString($recrutement->latitude);
            $new_latitude = HistoriqueController::getString($request->latitude);
            HistoriqueController::addUpdateDetail($detail, "Latitude", $old_latitude, $new_latitude);
        }
        if($request->longitude != $recrutement->longitude){
            $old_longitude = HistoriqueController::getString($recrutement->longitude);
            $new_longitude = HistoriqueController::getString($request->longitude);
            HistoriqueController::addUpdateDetail($detail, "Longitude", $old_longitude, $new_longitude);
        }
        if($request->telephone != $recrutement->telephone){
            $old_telephone = HistoriqueController::getString($recrutement->telephone);
            $new_telephone = HistoriqueController::getString($request->telephone);
            HistoriqueController::addUpdateDetail($detail, "Téléphone", $old_telephone, $new_telephone);
        }
        if($request->email != $recrutement->email){
            $old_email = HistoriqueController::getString($recrutement->email);
            $new_email = HistoriqueController::getString($request->email);
            HistoriqueController::addUpdateDetail($detail, "Email", $old_email, $new_email);
        }
        if($request->francais != $recrutement->francais){
            $old_francais = HistoriqueController::getString($recrutement->francais);
            $new_francais = HistoriqueController::getString($request->francais);
            HistoriqueController::addUpdateDetail($detail, "Compétence en français", $old_francais, $new_francais);
        }
        if($request->anglais != $recrutement->anglais){
            $old_anglais = HistoriqueController::getString($recrutement->anglais);
            $new_anglais = HistoriqueController::getString($request->anglais);
            HistoriqueController::addUpdateDetail($detail, "Compétence en anglais", $old_anglais, $new_anglais);
        }
        if($request->xp != $recrutement->xp){
            $old_xp = HistoriqueController::getString($recrutement->xp);
            $new_xp = HistoriqueController::getString($request->xp);            
            HistoriqueController::addUpdateDetail($detail, "Experience en gardiennage", $old_xp, $new_xp);
        }
        if($request->taille != $recrutement->taille){
            $old_taille = HistoriqueController::getString($recrutement->taille);
            $new_taille = HistoriqueController::getString($request->taille);
            HistoriqueController::addUpdateDetail($detail, "Taille", $old_taille, $new_taille);
        }
        $historique->detail = $detail;
        return $historique->save();
    }

    public static function action_recrutement(Request $request, $objet, $recrutement_id){
        $historique = new Historique();
        $historique->user_id = $request->user()->id;
        $historique->user = $request->user()->name . ' <' . $request->user()->email . '>';
        $historique->recrutement_id = $recrutement_id;
        $historique->objet = $objet;
        if($request->note)
            $historique->note = $request->note;
        $historique->created_at = new \DateTime();
        $historique->save();
        return $historique->id;
    }
}
