import React from 'react';
import useToken from '../util/useToken';

export default function ActionVisitePoste({auth, visite, updateData}) {
    const handleSeen = () => {
        axios.post('/api/seen/visite_poste/' + visite.id , null, useToken())
        .then((res) => {
            if(res.data.success)
                updateData()
        })
        .catch((e) => {
            console.error(e)
        })
    }

    return <div>
        <div className='action-container'>
            {
                (auth.id != visite.user_id && !visite.seen) && 
                <span onClick={handleSeen}>Marquer comme lu</span>
            }
        </div>
    </div>
}