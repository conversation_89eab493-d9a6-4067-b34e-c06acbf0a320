import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import useToken from '../util/useToken';
import moment from 'moment';
import ActionAbsence from './ActionAbsence';
import ShowHeader from '../view/ShowHeader';
import Tab from '../layout/Tab';
import LoadingPage from '../loading/LoadingPage';
import matricule from '../util/matricule';

export default function ShowAbsence({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [absence, setAbsence] = useState(null)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/absence/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setAbsence(res.data)
                const newUsers = []
                if (res.data.type_absence == 'mis_a_pied') {
                    newUsers.unshift({ id: res.data.superviseur_id, address: res.data.sup_email, name: res.data.sup_nom })
                }
                if (auth.id != res.data.user_id)
                    newUsers.unshift({ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom })
                setDefautUsers(newUsers)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(absence)
    }, [absence]);

    useEffect(updateData, [currentId])

    const formatNumber = (nb) => {
        const roundedNumber = Math.round(nb * 10) / 10;
        const formattedNumber = roundedNumber % 1 === 0 ? roundedNumber.toFixed(0) : roundedNumber.toFixed(1);
        return formattedNumber;
    }

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div> {
            absence && 
            <>
                <ShowHeader size={size} 
                    label={
                        absence.type_absence == "conge" ? "Congé" : 
                        absence.type_absence == "mis_a_pied" ? "Mise à pied": 
                        "Permission"
                    }
                    id={ absence.id } 
                    closeDetail={() => setCurrentId()}
                />
                <div className="card-container">
                    <div className='badge-container'>
                        <span>
                            <span className={'badge-outline badge-outline-' + absence.status_color}>
                                {absence.status_description}
                            </span> {
                                absence.nb_pj > 0 &&
                                <span className='badge-outline'>
                                    Pièce jointe : {absence.nb_pj}
                                </span>
                            }
                        </span>
                    </div>
                    <h3>
                        {
                            absence.employe ? (matricule(absence) + ' ' + absence.employe) 
                            :
                            <div>
                                {absence.user_nom} <span className='secondary'>{' <' + absence.user_email + '>'}</span>
                            </div>
                        } 
                    </h3>
                    {
                        absence.employe &&
                        <p style={{whiteSpace: "pre-line"}}>
                            {
                                absence.site ?
                                <>
                                    Site: <span className='text'>
                                        {absence.site}
                                    </span>
                                </>
                                :
                                <>
                                    Fonction: <span className='text'>
                                        {absence.fonction}
                                    </span>
                                </>
                            }
                        </p>
                    }
                    { absence.depart && absence.retour &&
                        <p style={{whiteSpace: "pre-line"}}>
                            Du: <span className='text capitalize'>
                                {absence.type_absence == "mis_a_pied" ?
                                    moment(absence.depart).format("DD MMM YYYY")
                                    :    
                                    moment(absence.depart).format("DD MMM YYYY") + (moment(absence.depart).format("HH:mm:ss") == "06:00:00" ? " Jour" : " Nuit")
                                }
                            </span>
                            <span className='text'> au </span>
                            <span className='text capitalize'>
                                {absence.type_absence == "mis_a_pied" ?
                                    moment(absence.retour).subtract(1, 'days').format("DD MMM YYYY")
                                    : 
                                    moment(absence.retour).format("DD MMM YYYY") + (moment(absence.retour).format("HH:mm:ss") == "06:00:00" ? " Jour" : " Nuit")
                                }
                            </span>
                        </p>
                    }
                    <p style={{whiteSpace: "pre-line"}}>
                        Durée: <span className='text'>
                            {
                                (absence.depart && absence.retour) ? (formatNumber(moment(absence.retour).diff(moment(absence.depart), 'hours')/24) + " jour(s)") : 
                                absence.nb_jour ? absence.nb_jour + " jour(s)" :
                                null
                            }
                        </span>
                    </p>
                    <p style={{whiteSpace: "pre-line"}}>
                        Motif: <span className='text'>{absence.motif}</span>
                    </p>
                    <p style={{whiteSpace: "pre-line"}}>
                        {absence.employe_temporaire &&
                            <>Remplaçant: 
                                <span className='text'> {absence.employe_temporaire}</span>
                            </>
                        }
                        {absence.employe_remplacant_id &&
                            <>
                                Remplaçant:     
                                <span className='text'> 
                                    {' '+ matricule(absence, true)+' ' + absence.arp_employe}
                                </span>
                            </>
                        }
                    </p>
                    {
                        absence.sup_email && 
                        <div>
                            Superviseur : <span className='text'>
                                {absence.sup_nom + ' <' + absence.sup_email + '>'}
                            </span>       
                        </div>    
                    }    
                    {
                        absence.employe &&
                        <div>
                            Demandeur : <span className='text'> 
                                {absence.user_nom} {' <' + absence.user_email + '>'}
                            </span>
                        </div>
                    }
                    <div className='card-action'>
                        <ActionAbsence auth={auth} absence={absence} updateData={updateData} toggleLoading={toggleLoading} setCurrentId={setCurrentId}/>
                    </div>
                </div>
                <Tab auth={auth} name={"absence_id"} data={absence} value={absence.id} updateData={updateData} defautUsers={defautUsers}/>
            </>
        } </div>
    } </>
}