import React, { useEffect, useState } from 'react'
import useToken from '../util/useToken'
import { useLocation, useParams } from 'react-router-dom'
import ShowHeader from '../view/ShowHeader'
import matricule from '../util/matricule'
import LoadingPage from '../loading/LoadingPage'
import moment from 'moment'
import "../layout/tab.css"
import InputEmploye from '../input/InputEmploye'

export default function ShowAnomalieService24({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const [isLoading, toggleLoading] = useState(false)
    const [anomalie, setAnomalie] = useState()
    const [employe, setEmploye] = useState()
    const [modalAssign, toggleAssignModal] = useState(false)
    const [agentToAssign, setAgentToAssign] = useState()
    const [currentParams, setCurrentParams] = useState()
    const [currentPtgId, setCurrentPtgId] = useState(null)
    const [initData, setInitData] = useState()
    const [error, setError] = useState('')
    const params = useParams()
    const locationSearch = new URLSearchParams(useLocation().search); 
    
    const reformatData = (data) => {
        const sortedData = data.sort((a, b) => moment(a.date_pointage).diff(moment(b.date_pointage)));
        const groupedData = [];
        let group = [];
        for (let i = 0; i < sortedData.length; i++) {
            if (group.length === 0) {
                group.push(sortedData[i]);
            } 
            else {
                const prevDate = moment(group[group.length - 1].date_pointage);
                const currDate = moment(sortedData[i].date_pointage);
                const hoursDiff = currDate.diff(prevDate, 'hours');

                if (hoursDiff === 12) {
                    group.push(sortedData[i]);
                } else {
                    groupedData.push(group);
                    group = [sortedData[i]];
                }
            }
        }
        if (group.length > 0) {
            groupedData.push(group);
        }
        return groupedData;
    }

    const updateData = () => { 
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/anomalie_service24/show/' + (currentId ? currentId : params.id) + '?' + locationSearch.toString(), useToken())
        .then((res) => {
            if (isMounted) {
                if(!res.data)
                    setCurrentId()
                else{
                    let listAnomalies = res.data.anomalies
                    let reformatAno = reformatData(listAnomalies)
                    setAnomalie(reformatAno)
                    setInitData(listAnomalies)
                    let emp = res.data.employe
                    setEmploye(emp)
                    setCurrentItem({
                        id: emp.id,
                        employe: emp.nom,
                        societe_id: emp.societe_id,
                        numero_employe: emp.numero_employe,
                        num_emp_saoi: emp.num_emp_saoi,
                        num_emp_soit: emp.num_emp_soit,
                        numero_stagiaire: emp.numero_stagiaire,
                        service_consecutives: reformatAno.length
                    })
                    if (reformatAno.length == 0){
                        setCurrentId()
                    }
                    
                }
            }
            toggleLoading(false)
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
    }

    useEffect(() => {
        updateData()
    }, [currentId])

    const createParamsEmploye = (id) => {
        let currentPtg = initData.find(d => d.ptg_id === id);
        if (currentPtg) {
            setCurrentPtgId(id);
            setCurrentParams(`?date_pointage=${currentPtg.date_pointage}&site_id=${currentPtg.idsite}`);
            toggleAssignModal(true);
        }
    }

    const handleAssign = () => {
        axios.post('/api/anomalie_planning/assign/' + currentPtgId, { remplacant_id: agentToAssign.id, service24 : 1 }, useToken())
        .then((res) => {
            if(res.data.success){
                setAgentToAssign()
                toggleAssignModal(false)
                updateData()
                setError('')
            }
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
        })
    }

    return (
        <div>
            <div>
                {
                    isLoading ?
                        <LoadingPage />
                    :
                        <>
                            {anomalie &&
                                <div>
                                    <ShowHeader size={size} label="Anomalie" id={currentId} closeDetail={() => setCurrentId()} />
                                    <div className="card-container">
                                        <h3>
                                            <div>{matricule(employe) + ' ' + employe.nom}</div>
                                        </h3>
                                        {employe.idsite &&
                                            <p style={{ whiteSpace: "pre-line" }}>
                                                Site: <span className='text'>{employe.site}</span>
                                            </p>
                                        }
                                        {
                                            employe?.superviseur && 
                                                <p style={{ whiteSpace: "pre-line" }}>
                                                    Superviseur: <span className='text'>{employe.superviseur + " <" + employe.superviseur_email + ">"}</span>
                                                </p>
                                        }

                                        <div className='tab-container'>
                                            <div className="tab-list">
                                                { anomalie.length > 0 &&
                                                    <div className='active'>Pointage</div>
                                                }
                                            </div>
                                            <div className="tab-content">
                                                {
                                                    anomalie.map((ano, index) => 
                                                        <div key={index} className="line-container">
                                                            <div>
                                                                {ano.map(ptg =>{
                                                                    const ptgDate = moment(ptg.date_pointage);
                                                                    const service = ptgDate.format('HH') === '06' ? 'JOUR' : 'NUIT';
                                                                    return (
                                                                        <div style={{ marginBottom: 10 }} key={ptg.ptg_id} className='space-between on-hover'>
                                                                            <span>
                                                                                <span>{ptgDate.format('DD MMM-YYYY')} - {service}</span><br/>
                                                                                <span className='secondary'>{ptg.site}</span><br/>
                                                                            </span>
                                                                            <div className="action-container">
                                                                                <span onClick={() => {createParamsEmploye(ptg.ptg_id)}}>Remplacer</span>
                                                                            </div>
                                                                        </div>
                                                                    )})
                                                                }
                                                            </div>
                                                        </div>
                                                    )
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                            {
                                modalAssign &&
                                <div className='modal'>
                                    <div>
                                        <InputEmploye value={agentToAssign}
                                            onChange={setAgentToAssign}
                                            urlSearch="/api/employe/get_employe_planning"
                                            urlParams={currentParams}
                                            // closeModal={()=>setShowEmploye(false)}
                                            // hideInput 
                                        />
                                        {
                                            error &&
                                            <div className='container-error'>
                                                {error}
                                            </div>
                                        }
                                        <div className='form-button-container'>
                                            <button type='button' className='btn-primary' onClick={handleAssign}>Confirmer</button>
                                            <button type='button' className='btn' onClick={()=>toggleAssignModal(false)}>Fermer</button>
                                        </div>
                                    </div>
                                </div>
                            }
                        </>
                }
            </div>
        </div>
    )
}
