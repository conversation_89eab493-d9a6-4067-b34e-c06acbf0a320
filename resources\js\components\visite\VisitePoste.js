import React, { useEffect, useState } from 'react';
import { Link, useLocation  } from "react-router-dom";
import moment from 'moment'
moment.locale('fr')

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import HeaderVisitePoste from './HeaderVisitePoste';
import { FiMoreVertical } from 'react-icons/fi';

export default function VisitePoste({auth, visites, setVisites, currentId, setCurrentId}) {
    const locationSearch = useLocation().search;
    const params = new URLSearchParams(locationSearch)
    const [isLoading, toggleLoading] = useState(true)
    const [allDataLoaded, setDataLoaded] = useState(false)
    let searchItems = [
        {label: 'Réf<PERSON>rence', name: 'id', type:'number'},
    ]
    if(!params.get("unread"))
        searchItems.push({label: 'Non lu', name: 'unread', type:'string'})
    searchItems = searchItems.concat([
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Site', name: 'site_id', type:'number'},
        {label: 'Date de visite', name: 'date_visite', type:'date'},
    ])
    if(auth.role != 'superviseur')
        searchItems.push({label: 'Utilisateur', name: 'user_id', type:'number'})

    const handleSeenAll = () => {
        toggleLoading(true)
        axios.post('/api/seen/visite_poste_all/', null, useToken())
        .then((res) => {
            if(res.data.success){
                updateData(true)                
            }
            else 
                toggleLoading(false)
        })
        .catch(() => {
            toggleLoading(false)
        })
    }

    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            params.set("offset", 0)
        }
        else
            params.set("offset", visites.length)
        axios.get('/api/visite_poste?' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setVisites(res.data.visites)
                    else {
                        const list = visites.slice().concat(res.data.visites)
                        setVisites(list)
                    }
                    setDataLoaded(res.data.visites.length < 10)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    const stayOnSite = (start, end) => {
        if (start == null || end == null)
            return ""
        const diffMinutes = moment(end).diff(moment(start), 'minutes')
        if (diffMinutes < 60) {
            return `${diffMinutes} minutes`
        } else {
            const hours = Math.floor(diffMinutes / 60)
            const minutes = diffMinutes % 60
            if (minutes == 0) {
                return `${hours} heure`
            } else if (minutes == 1) {
                return (`${hours} heure ${minutes} minute`)
            } else {
                return (`${hours} heure ${minutes} minutes`)
            }
        }
    }

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>
                    Visite de poste
                </h2>
                <div>
                    {/*
                        (auth.role !='superviseur' && params.get("unread") && visites.length > 0) && 
                        <span className='btn btn-outline-secondary pointer' onClick={handleSeenAll}>Marquer tout comme lu</span>
                    */}
                    {
                        ['superviseur', 'resp_sup', 'resp_op', 'su'].includes(auth.role) && 
                        <Link className='btn btn-primary' to="/visite-poste/add">Nouvelle visite</Link>
                    }
                </div>
            </div>
            <SearchBar listItems={searchItems}/>
            <div>
                {
                    visites.length == 0 ? 
                        <h3 className='center secondary'>Aucun données trouvé</h3>
                    :
                        <InfiniteScroll
                            dataLength={visites.length}
                            next={fetchMoreData}
                            hasMore={!allDataLoaded}
                            loader={<LoadingPage/>}
                        >
                            {
                                visites.length == 0 ? 
                                    <h3 className='center secondary'>Aucun données trouvé</h3>
                                :
                                    visites.map((vst) => (
                                        <div className={`card-container ${currentId && currentId == vst.id ? 'selected' : ''}`} key={vst.id}>
                                            <div className='badge-container'>
                                                <span>
                                                    {   
                                                        <div>
                                                            {
                                                                (auth.role != 'superviseur' && auth.id != vst.user_id) && (
                                                                    <span className={"badge-outline badge-outline-" + (vst.send_email ? "cyan" : vst.seen ? "green": "purple")}>
                                                                        { vst.send_email ? "Email envoyé" : vst.seen ? "Lu" : "Non lu" }
                                                                    </span>
                                                                )
                                                            }
                                                            {
                                                                vst.start != null && vst.end != null &&
                                                                <span className='badge-outline badge-outline-green'>
                                                                    {stayOnSite(vst.start, vst.end)}
                                                                </span>
                                                            }
                                                            {
                                                                vst.pointage == 1 && vst.pointeuse == 1 && 
                                                                ((vst.start == null && vst.end == null) || 
                                                                (vst.start == null && vst.end != null) || 
                                                                (vst.start != null && vst.end == null)) &&
                                                                <span className='badge-outline badge-outline-red'>
                                                                    ndf : {vst.start == null && vst.end == null ? "0/2" : "1/2"}
                                                                </span>
                                                            }
                                                            {
                                                                vst.nb_pj > 0 &&
                                                                <span className="badge-outline">
                                                                    Pièce jointe : {vst.nb_pj}
                                                                </span>
                                                            }
                                                        </div>
                                                    } 
                                                </span>
                                                <span className='pointer' onClick={() => setCurrentId(vst.id)}>
                                                    <FiMoreVertical size={20} color="#888"/>
                                                </span>
                                            </div>
                                            <HeaderVisitePoste auth={auth} data={vst}/>
                                        </div>
                                    ))
                            }
                        </InfiniteScroll>
                }
            </div>
        </div>
    } </>
}