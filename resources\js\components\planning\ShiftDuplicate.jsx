import React, { useEffect, useState } from 'react'
import InputDate from '../input/InputDate'
import { useLocation, useNavigate } from 'react-router-dom'
import moment from 'moment'
import axios from 'axios'
import useToken from '../util/useToken'
import DualContainer from '../container/DualContainer'
import InputSelect from '../input/InputSelect'
import matricule from '../util/matricule'
import PreviewDuplicate from './PreviewDuplicate'

export default function ShiftDuplicate({planning, pointages, auth, defaultDate, firstPointage, setFirstPointage, closeModal}) {
    const [date, setDate] = useState()
    const [dateFirstSevice, setDateFirstService] = useState(moment(planning.date_planning).date(1).toDate())
    const [error, setError] = useState('')
    const [firstService, setFirstService] = useState({label: "JOUR", value: "06:00:00"})
    const [service, setService] = useState({label: "JOUR", value: "06:00:00"})
    const [calendar, toggleCalendar] = useState(true)
    const lastParams = (new URLSearchParams(useLocation().search)).toString()
    const isNonFait = window.location.href.includes('planning-non-fait');
    useEffect(() => {
        if(defaultDate){
            setDate(moment(defaultDate + '-01').toDate())
        }
        else if(moment(planning.date_planning).isAfter(moment()))
            setDate(moment(planning.date_planning + '-01').add(1, 'M').toDate())
        else{
            setDate(moment().date(1).add(1, 'M').toDate())
        }
    }, [])

    useEffect(() => {
        if(moment(planning.date_planning).month() != moment(dateFirstSevice).month() || moment(planning.date_planning).year() != moment(dateFirstSevice).year()){
            setDateFirstService(moment(planning.date_planning).date(1).toDate())
            setError("La date de premier service doit être dans le mois " + moment(planning.date_planning).add(1, 'M').format('MMMM') + ' et de l\'année ' + moment(planning.date_planning).year())
        }
        if(moment().isAfter(moment(date))){
            setError("La date de décalage doit être dans le futur")
        }
    }, [dateFirstSevice, date])

    useEffect(() => {
        setFirstPointage(pointages.filter(ptg => moment(ptg.date_pointage).format('DD/MM/YYYY HH:mm:ss') == (moment(dateFirstSevice).format('DD/MM/YYYY') + ' ' + firstService.value)))
    }, [dateFirstSevice, firstService])

    const navigate = useNavigate()

    const handleCheck = () => {
        axios.get("/api/planning/check_planning/" + planning.id + "?date_planning=" + moment(date).format('YYYY-MM'), useToken())
        .then((res) => {
            if(res.data.error){
                setError(res.data.error)
                console.error(res.data.error)
            }   
            else
                navigate("/planning/duplicate/" + planning.id + "?date=" + moment(date).format('YYYY-MM-DD') + (service?.value == "18:00:00" ? "&add12=1" : "") + "&firstService=" + moment(dateFirstSevice).format('YYYY-MM-DD ') + firstService.value  +  (isNonFait ? "&last_params=" + encodeURIComponent( "/planning-non-fait?" + lastParams) : ""))
        })
    }

    return (
        <>
            <div className='modal'>
                <div>
                    <h2>Dupliquer</h2>
                    <div>
                        <DualContainer>
                            <InputDate label="Date de premier service" value={dateFirstSevice} onChange={setDateFirstService} format={"EE dd/MM/yyyy"}/>
                            <InputSelect
                                required
                                label="Service"
                                selected={firstService} 
                                setSelected={setFirstService}
                                options = {[
                                    {label: "JOUR", value: "06:00:00"},
                                    {label: "NUIT", value: "18:00:00"},
                                ]}
                            />
                        </DualContainer>
                        {firstPointage.length > 0 &&
                            <div className="card-container secondary">
                                {
                                    firstPointage.map((ptg) =>
                                        <div key={ptg.id} style={{ padding : 5 }}>
                                            {("[" + matricule(ptg) + "] " + ptg.nom) }
                                        </div>
                                    )
                                }
                            </div>
                        }
                    </div>
                    <div className="">
                        <DualContainer>
                            <InputDate label="Date de décalage" value={date} onChange={setDate} format={"EE dd/MM/yyyy"} required/>
                            <InputSelect
                                required
                                label="Service"
                                selected={service} 
                                setSelected={setService}
                                options = {[
                                    {label: "JOUR", value: "06:00:00"},
                                    {label: "NUIT", value: "18:00:00"},
                                ]}
                            />
                        </DualContainer>
                    </div>
                    <div className="action-container" style={{marginTop: 25, marginBottom: 10}}>
                        <span onClick={() => {toggleCalendar(true)}}> Calendrier </span>
                        <span onClick={()=> toggleCalendar(false)}> Liste </span>
                    </div>
                    
                    <PreviewDuplicate planning={planning}
                        calendar={calendar}
                        pointages={pointages}
                        auth={auth}
                        defaultDate={defaultDate}
                        firstPointage={firstPointage}
                        dateDuplication={date}
                        dateFirstSevice={dateFirstSevice}
                        service={service}
                        firstService={firstService}/>
                    {error && <div className='container-error'>{error}</div>}
                    <div className="form-button-container">
                        <button type='button' className='btn-primary' onClick={() => handleCheck()}>
                            Suivant
                        </button>
                        <button type='button' className='btn' onClick={closeModal}>
                            Annuler
                        </button>
                    </div>
                </div>
            </div>
        </>
    )
}
