import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import Tab from '../layout/Tab';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';
import useToken from '../util/useToken';
import ActionSuiviJuridique from './ActionSuiviJuridique';
import showAmount from '../util/numberUtil';

export default function ShowSuiviJuridique({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [juridique, setJuridique] = useState()
    const [isLoading, toggleLoading] = useState(false)


    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/suivi_juridique/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setJuridique(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(juridique)
    }, [juridique])

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                juridique &&
                <>
                    <ShowHeader size={size} label="Juridique" id={juridique.juridique_id ? juridique.juridique_id : juridique.plainte_id} closeDetail={() => setCurrentId()}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + juridique.status_color}>
                                    {juridique.status_description}
                                </span> {
                                    juridique.nb_pj > 0 &&
                                    <span className='badge-outline'>
                                        Pièce jointe : {juridique.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {juridique.juridique_id ? juridique.debiteur : juridique.site}
                        </h3>
                        <div>
                            Référence : <span className='text'>{juridique.reference}</span>
                        </div>
                        <div>
                            Agence : <span className='text'>{juridique.agence}</span>
                        </div>
                        {
                            juridique.juridique_id ?
                                <>
                                    <div>
                                        Débiteur : <span className='text'>{juridique.debiteur}</span>
                                    </div>
                                    <div>
                                        Contrat : <span className='text'>{juridique.contrat}</span>
                                    </div>
                                    <div>
                                        Facture(s) : <span className='text'>{juridique.facture}</span>
                                    </div>
                                    <div>
                                        Montant : <span className='text'>{showAmount(juridique.montant)}</span>
                                    </div>
                                </>
                            :
                                <>
                                    <div>
                                        Police : <span className='text'>{juridique.police}</span>
                                    </div>
                                    <div>
                                        Agent(s) concerné : <span className='text'>{juridique.agent}</span>
                                    </div>
                                    <div>
                                        <span className='text'>{juridique.fait}</span>
                                    </div>
                                </>
                        }
                        {
                            !juridique.seen &&
                            <div className='card-action'>
                                <ActionSuiviJuridique auth={auth} juridique={juridique} updateData={updateData} toggleLoading={toggleLoading}/>
                            </div>
                        }
                    </div>
                    {

                    }
                    <Tab auth={auth} 
                        name={juridique.juridique_id ? "juridique_id" : "plainte_id"} 
                        data={juridique} 
                        value={juridique.juridique_id ? juridique.juridique_id : juridique.plainte_id} 
                        updateData={updateData}/>
                </>
            }
        </div>
    }</>
}