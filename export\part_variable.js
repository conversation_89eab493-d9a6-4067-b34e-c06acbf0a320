const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const matricule = require("../resources/js/components/util/matricule")

moment.locale('fr')

const {db_config_admin, sendMail} = require("../auth")
const pool = mysql.createPool(db_config_admin)
const isTask = (process.argv[2] == "task")

const destination_vg = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

const destination_test = ["<EMAIL>", "<EMAIL>"]

const sqlSelectEmploye = "SELECT e.id, e.nom, e.societe_id, e.num_emp_soit, num_emp_saoi, e.numero_employe, e.numero_stagiaire, e.resp_part_id, " +
    "e.fonction_id, f.libelle as 'fonction', e.titre, ac.nom as 'agence' " + 
    "FROM employes e " +
    "LEFT JOIN fonctions f ON f.id = e.fonction_id " +
    "LEFT JOIN agences ac ON ac.id = e.agence_id " +
	"where e.part_variable = 1 and (e.soft_delete is null or e.soft_delete = 0) "
const sqlSelectCritere = (ids) => "SELECT cm.id, cm.employe_id, cm.designation, cm.montant FROM critere_parts cm " +
	"where (cm.soft_delete is null or cm.soft_delete = 0) and cm.employe_id in (" + ids.join(",") + ")"
const sqlSelectUser = (ids)=> "SELECT u.id, u.name, u.email, u.employe_id FROM users u " +
	"where u.employe_id in (" + ids.join(',') + ")"
const sqlSelectPartVariableExport = "SELECT value FROM params p WHERE p.key = 'last_part_variable'"
const sqlUpdatePartVariableExport = "UPDATE params p SET p.value = DATE_FORMAT(NOW(), '%Y-%m-%d') " +
	"WHERE p.key = 'last_part_variable'"

function doExportPart(){
	console.log("export rh report...")
	pool.query(sqlSelectEmploye, [], async (err, employes) => {
		if(err)
			console.error(err)
		else if(employes){
            const responsableIds = []
            employes.forEach(emp => {
                if(!responsableIds.includes(emp.resp_part_id))
                    responsableIds.push(emp.resp_part_id)
            });
            console.log("Nb employe: " + employes.length)
            pool.query(sqlSelectUser(responsableIds), [], async (err, users) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb user: " + users.length)
                    pool.query(sqlSelectCritere(employes.map(e => e.id)), [], async (err, criteres) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Nb critere: " + criteres.length)
                            users.forEach( u => {
                                u.employes = []
                                employes.forEach(emp => {
                                    if(emp.resp_part_id == u.employe_id){
                                        emp.criteres = []
                                        criteres.forEach(cr => {
                                            if(cr.employe_id == emp.id)
                                                emp.criteres.push(cr)
                                        })
                                        u.employes.push(emp)
                                    }
                                })
                            })
                            
                            const workbook = new Excel.Workbook()
                            generateAgentExcelFile(workbook, users)
                            const archiveAgentBuffer = await workbook.xlsx.writeBuffer()
                            sendMail(
                                pool,
                                isTask ? destination_vg : destination_test,
                                "Part variable", 
                                "Veuillez trouver ci-joint la liste des parts variables groupés par responsable.",
                                [
                                    {
                                        filename: "Part variable.xlsx",
                                        content: archiveAgentBuffer
                                    }
                                ],
                                (response) => {
                                    if(response && isTask){
                                        pool.query(sqlUpdatePartVariableExport, [], (e, r) =>{
                                            if(e)
                                                console.error(e)
                                            else
                                                console.log("update last part export: " + r)
                                            process.exit(1)
                                        })
                                    }
                                    else
                                        process.exit(1)
                                },
                                isTask
                            )
                        }
                    })

                }
            })
		}
	})
}

function generateAgentExcelFile(workbook, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = {
		size: 16,
		bold: true
	}
	const fontBold = {
		bold: true
	}
    data.forEach(u => {
        const worksheet = workbook.addWorksheet(u.name)
        worksheet.getColumn('A').width = 10
        worksheet.getColumn('B').width = 40
        worksheet.getColumn('C').width = 30
        worksheet.getColumn('D').width = 30
        worksheet.getColumn('E').width = 20
        worksheet.getColumn('F').width = 50
        worksheet.getColumn('G').width = 20
        worksheet.mergeCells('F1:G1')
        worksheet.getCell('A1').value = u.name + " <" + u.email + ">"
        worksheet.getCell('A1').font = fontHeader
        worksheet.getCell('A2').value = "Matricule"
        worksheet.getCell('A2').border = borderStyle
        worksheet.getCell('A2').font = fontBold
        worksheet.getCell('B2').value = "Nom"
        worksheet.getCell('B2').border = borderStyle
        worksheet.getCell('B2').font = fontBold
        worksheet.getCell('C2').value = "Fonction"
        worksheet.getCell('C2').border = borderStyle
        worksheet.getCell('C2').font = fontBold
        worksheet.getCell('D2').value = "Agence"
        worksheet.getCell('D2').border = borderStyle
        worksheet.getCell('D2').font = fontBold
        worksheet.getCell('E2').value = "Montant total"
        worksheet.getCell('E2').border = borderStyle
        worksheet.getCell('E2').font = fontBold
        worksheet.getCell('F2').value = "Critères"
        worksheet.getCell('F2').border = borderStyle
        worksheet.getCell('F2').font = fontBold
        worksheet.mergeCells('F2:G2')
        let line = 3
        u.employes.forEach(emp => {
            worksheet.getCell('A' + line).value = matricule(emp)
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = emp.nom
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = (emp.fonction_id == 12 ? emp.titre : emp.fonction)
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('D' + line).value = emp.agence
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('E' + line).value = emp.criteres.reduce(function (accumulateur, valeurCourante) {
                return accumulateur + valeurCourante.montant;
            }, 0);
            worksheet.getCell('E' + line).border = borderStyle
            emp.criteres.forEach(cr => {
                worksheet.getCell('A' + line).border = borderStyle
                worksheet.getCell('B' + line).border = borderStyle
                worksheet.getCell('C' + line).border = borderStyle
                worksheet.getCell('D' + line).border = borderStyle
                worksheet.getCell('E' + line).border = borderStyle
                worksheet.getCell('F' + line).value = cr.designation
                worksheet.getCell('F' + line).border = borderStyle
                worksheet.getCell('G' + line).value = cr.montant
                worksheet.getCell('G' + line).border = borderStyle
                line++
            })
            line+=2
        })
    });
}



if(process.argv[2] == "test"){
    console.log("export test")
    doExportPart()
}

else if(isTask){
    if(moment().format("DD") == 19 && moment().isAfter(moment().set({hour: 6, minute: 30}))){
        pool.query(sqlSelectPartVariableExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && moment().format("YYYY-MM-DD") == result[0].value){
                console.log("export part already done!")
                process.exit()
            }
            else {
                console.log("exporting ...")
                doExportPart()
            }
        })
    }
    else {
        console.log("Not 19 of month, skip export critere part employe.")
    }
}
else
    console.log("please specify command!")