import React, { useEffect, useState } from 'react';
import {useLocation, useParams} from 'react-router-dom'
import axios from 'axios';

import useToken from '../util/useToken';
import ActionSite from './ActionSite';
import Tab from '../layout/Tab';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';

export default function ShowSite({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [site, setSite] = useState()
    const [isLoading, toggleLoading] = useState(false)
    const locationSearch = new URLSearchParams(useLocation().search);
    const isAlarm = locationSearch.get('date-alarm')

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/site/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setSite(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(site)
    }, [site])

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                site &&
                <>
                    <ShowHeader size={size} label="Site" id={site.id} closeDetail={() => setCurrentId()}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + (site.soft_delete ? "pink" : site.pointeuse ? "cyan" : "grey")}>
                                    {site.soft_delete ? "En archive" : site.pointeuse ? "Pointeuse" : "Pointage"}
                                </span> {
                                    site.nb_pj > 0 &&
                                    <span className='badge-outline'>
                                        Pièce jointe : {site.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {site.nom} 
                        </h3>
                        {
                            site.adresse &&
                            <div>
                                Adresse : <span className='text'>{site.adresse}</span>
                            </div>
                        }
                        {
                            site.resp_sup_id &&
                            <div>
                                Manager : <span className='text'>{site.resp_sup_name} {' <' + site.resp_sup_email + '>'}</span>
                            </div>
                        }
                        {
                            site.user &&
                            <div>
                                Superviseur : <span className='text'> 
                                    {site.user} {' <' + site.email + '>'}
                                </span>
                            </div>
                        }
                        <div className='card-action'>
                            <ActionSite auth={auth} site={site} updateData={updateData} toggleLoading={toggleLoading}/>
                        </div>
                    </div>
                    <Tab auth={auth} name="site_id" value={site.id} data={{...site, isAlarm: isAlarm}} updateData={updateData} currentNature={site.nature} setCurrentNature={setCurrentItem}/>
                </>
            }
        </div>
    }</>
}