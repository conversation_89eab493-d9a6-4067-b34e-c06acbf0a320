const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const ids = []

const sqlSelectSite = () => {
    if(ids.length > 0)
        return "SELECT s.idsite, s.nom FROM sites s WHERE (s.soft_delete is null or s.soft_delete = 0) and s.superviseur_id is null " +
            "and s.idsite not in (" + ids.join(',') + ") limit 1"
    return "SELECT s.idsite, s.nom FROM sites s WHERE (s.soft_delete is null or s.soft_delete = 0) and s.superviseur_id is null limit 1"
} 

const sqlUpdateSite = "UPDATE sites SET superviseur_id = ? WHERE idsite = ?"

const sqlSelectFaitMarquant = "SELECT f.id, f.user_id FROM fait_marquants f " +
        "left join users u on u.id = f.user_id " +
        "WHERE f.site_id = ? and u.role = 'superviseur' " +
        "ORDER BY f.created_at DESC limit 1"

function updateSuperviseurSite() {
    pool.query(sqlSelectSite(), [], async (err, sites) => {
        if(err)
            console.error(err)
        else {
            if(sites.length > 0) {
                const currentSite = sites[0]
                console.log("--------\n", currentSite.idsite + " " + currentSite.nom)
                pool.query(sqlSelectFaitMarquant, [currentSite.idsite], async (err, faits) => {
                    if(err)
                        console.error(err)
                    else {
                        if(faits.length > 0) {
                            const currentFait = faits[0]
                            console.log("user_id : " , currentFait.user_id)
                            pool.query(sqlUpdateSite, [currentFait.user_id, currentSite.idsite], async (err) => {
                                if(err)
                                    console.error(err)
                                else {
                                    console.log("update_site")
                                    setTimeout(() => updateSuperviseurSite(), 200)
                                }
                            })
                        }
                        else {
                            ids.push(currentSite.idsite)
                            setTimeout(() => updateSuperviseurSite(), 200)
                        }
                    }
                })
            }
            else {
                console.log("all site updated")
                process.exit(1)
            }
        }
    })
}

updateSuperviseurSite()