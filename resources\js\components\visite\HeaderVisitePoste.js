import React from 'react';

import moment from 'moment';

export default function HeaderVisitePoste({auth, data}) {
    
    return (
        <div>
            <h3>
                {data.site}
            </h3>
            <p style={{whiteSpace: "pre-line"}}>
                Compte rendu : <span className='text'>{data.compte_rendu}</span>
            </p>
            <span>
                <span>Superviseur : </span>
                <span className='text'>{data.user_nom} {" <" + data.user_email + ">"}</span>
            </span>
            <div className='card-footer'>
                <span>
                    {
                        data.date_visite &&
                        <span>Visite du : </span>
                    }
                    <span className='text'>
                        {data.date_visite && moment(data.date_visite).format('LL')}
                        {data.start && data.end && (
                            <> de {moment(data.start).format('HH[h]mm')} à {moment(data.end).format('HH[h]mm')}</>
                        )}
                    </span>
                </span>
                <span className='secondary'>{moment(data.created_at).from(auth.datetime)}</span>
            </div>
        </div>
    )
}