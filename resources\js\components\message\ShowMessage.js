import React, { useEffect, useRef, useState } from 'react';
import {useLocation, useParams} from 'react-router-dom'
import parse from 'html-react-parser';
import axios from 'axios';
import { FaCaretDown, FaCaretUp } from "react-icons/fa";

import useToken from '../util/useToken';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';
import ActionMessage from './ActionMessage';
import "./message.css"
import moment from 'moment';
import useClickOutside from '../util/useClickOutside';
import ReactToPrint, { useReactToPrint } from 'react-to-print';
import MessageItem from './MessageItem';
import { RiMailDownloadLine } from 'react-icons/ri';

export default function ShowMessage({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const locationSearch = useLocation().search
    const urlParams = new URLSearchParams(locationSearch)
    const sent = urlParams.get("sent")
    const unread = urlParams.get("unread")
    const [messages, setMessages] = useState([])
    const [note, setNote] = useState(null)
    const [isLoading, toggleLoading] = useState(false)
    const [numberFollow, setNumberFollow] = useState(0)
    const [showCc, toggleCc] = useState(false)
    const [showAllReceveirs, toggleAllReceveirs] = useState(false)
    const [showAllCc, toggleAllCc] = useState(false)

    const itemMessageRefs = useRef([]);
    const allMessageRef = useRef();
    const firstMessageRef = useRef(null)
    const receiverRef = useRef(null)
    const ccRef = useRef(null)
    
    const printFirstMessage = useReactToPrint({
        content: () => firstMessageRef.current,
        documentTitle: note ? note.objet : "Message",
    })
    
    const generateAllMessagePdf = useReactToPrint({
        content: () => allMessageRef.current,
        documentTitle: note ? note.objet : "Message",
    })

    useClickOutside(receiverRef, () => {
        toggleAllReceveirs(false)
    })
    useClickOutside(ccRef, () => {
        toggleAllCc(false)
    })

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        let paramData = new URLSearchParams()
        if(sent)
            paramData.set("sent", 1)
        if(unread)
            paramData.set("unread", 1)
        axios.get('/api/message/show/' + (currentId ? currentId : params.id) + "?" + paramData, useToken())
        .then((res) => {
            if(isMounted){
                if(!sent){
                    res.data.note.copies = res.data.copies
                }
                setMessages(res.data.messages)
                setNote(res.data.note)
                let copies = res.data.copies;
                if (copies) {
                    let nbReceivers = 0;
                    let nbCopy = 0
                    copies.forEach(copy => {
                        if (copy.follow == 1) {
                            nbReceivers++;
                        }
                        else 
                            nbCopy++;
                    });
                    setNumberFollow({'nbCopy': nbCopy, 'nbReceivers': nbReceivers});
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(note)
    }, [note])


    useEffect(updateData, [currentId])

    const downloadAllFiles = (pjs) => {
        pjs.forEach(pj => {
            const link = document.createElement('a');
            link.href = "/uploads/" + pj.path;
            link.setAttribute('download', pj.nature); // This is optional, but it sets the file name
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                (note) &&
                <>
                    <ShowHeader size={size} label="Message" id={note.id} closeDetail={() => setCurrentId()}/>
                    <div className="card-container">
                        <h3><span >{note.objet}</span></h3>
                        {
                            (note.to && note.to.length > 0) &&
                            <div className='display-flex text nowrap'>
                                Pour : <span className="text nowrap">
                                        {
                                            note.to.length == 1 ?
                                                note.to[0].name + " <" + note.to[0].email + ">"
                                            :
                                                <div>
                                                    {showAllReceveirs ?
                                                        <div ref={receiverRef} className='text-wrap'>
                                                            {(note.to.map(u => u.name).join(", "))}
                                                        </div>
                                                    :
                                                        note.to.slice(0, 5).map(u => u.name).join(", ")   
                                                    }
                                                    {
                                                        note.to.length > 5 &&
                                                        <span className='action-container'>
                                                                <span className={!showAllReceveirs ? "margin-left-10" : ""} onClick={() => toggleAllReceveirs(true)}>{showAllReceveirs ? null : (" +" + (note.to.length - 5))}</span>
                                                        </span>
                                                    }
                                                </div>
                                        }
                                </span>
                            </div>
                        }
                        {
                            (note.cc && note.cc.length > 0) &&
                                <div className='display-flex text nowrap'>
                                Cc : <span className="text nowrap">
                                    {
                                        note.cc.length == 1 ? 
                                            note.cc[0].name + " <" + note.cc[0].email + ">" 
                                        : 
                                            <div> 
                                                {
                                                    showAllCc ? 
                                                        <div ref={ccRef} className="text-wrap">
                                                            {(note.cc.map(u => u.name).join(", "))}
                                                        </div>
                                                    :
                                                        (note.cc.slice(0, 5).map(u => u.name).join(", "))
                                                }
                                                {
                                                    note.cc.length > 5 &&
                                                        <span className='action-container'>
                                                            <span onClick={() => toggleAllCc(true)} className={!showAllCc ? "margin-left-10" : ""}> {showAllCc ? null : (" +" + (note.cc.length - 5)) }</span>
                                                        </span>
                                                }
                                            </div>
                                    }
                                </span>
                            </div>
                        }
                        <ActionMessage sent={sent ? true : false}
                            unread={unread ? true : false}  
                            auth={auth} 
                            message={note} 
                            numberFollow = {numberFollow}
                            updateData={updateData} 
                            toggleLoading={toggleLoading}
                            printMessage={generateAllMessagePdf}/>
                    </div>
                    <div ref={allMessageRef}>
                        <h3 className="print-only" style={{margin: 10 ,display: 'none'}}><span>{note.objet}</span></h3>
                        <div ref={firstMessageRef} className="card-container message-log">
                            
                            <div className='message-user'>
                                <span>
                                    <span>
                                        {(sent && unread) ? (note.sender_name + " <" + note.sender_email + ">") : note.name + " <" + note.email + ">" }
                                        {
                                            (!sent && note.copies && note.copies.length > 0) &&
                                            <span className='pointer no-print'>
                                                {
                                                    showCc ?
                                                        <FaCaretUp onClick={() => toggleCc(false)} size={15} color="#888"/>
                                                    :
                                                        <FaCaretDown onClick={() => toggleCc(true)} size={15} color="#888"/>
                                                }
                                            </span>
                                        }
                                    </span>
                                    {
                                        showCc &&
                                        <>
                                            <br/>
                                            <span className='secondary'>
                                                {note.copies.map(c => c.user_name).join(", ")}
                                            </span>
                                        </>
                                    }
                                </span>
                                <span style={{verticalAlign: "middle"}}>
                                    {moment(note.created_at).format("DD MMM YY à HH:mm")} 
                                    <span className='no-print' style={{marginLeft:5, verticalAlign: "middle"}} onClick={printFirstMessage}>
                                        <RiMailDownloadLine size={15}/>
                                    </span>
                                </span>
                            </div>
                            <div className="message-content">
                                {parse(note.content)}
                            </div>
                            {
                                note.pieces.length > 0 &&
                                <div className='message-pj no-print'>
                                    {
                                        note.pieces.map(pj => (
                                            <span key={pj.id} className='pj-link'>
                                                <a className='link-no-style' target="_blank" href={"/uploads/" + pj.path} key={pj.id}>
                                                    {pj.nature}
                                                </a><span> </span> 
                                            </span>
                                        ))
                                    }
                                </div>
                            }
                            {note.pieces?.length > 1 && 
                                <div className='action-container no-print'>
                                    <span onClick={() => downloadAllFiles(note.pieces)}>Télécharger tous</span>
                                </div>
                            }
                        </div>
                        {
                            messages.map((ms, index) => (
                                <div key={ms.id}>
                                    <MessageItem ms={ms} 
                                        ref={(el) => (itemMessageRefs.current[index] = el)} 
                                        downloadAllFiles={downloadAllFiles}  
                                        handlePrint = {
                                            <ReactToPrint
                                                trigger={() => <span className='no-print' style={{marginLeft:5}}><RiMailDownloadLine size={15}/></span>} 
                                                content={() => itemMessageRefs.current[index]}
                                            />
                                        }
                                        />
                                </div>
                            ))
                        }
                    </div>
                </>
            }
        </div>
    }</>
}