<?php

namespace App\Http\Controllers;

use App\Models\Seen;
use App\Models\Notification;
use App\Models\Historique;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = "n.id = '". $request->id ."'";
        else {  
            if($request->consigne){
                $searchArray[] = " n.user_id  = "  . $request->user()->id . " and n.follow = 1";
            }
            else 
                $searchArray[] = " n.receiver_id = " . $request->user()->id . " ";

            if($request->status)
                $searchArray[] = "n.status = '". $request->status ."'";
            if($request->created_at)
                $searchArray[] = " n.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "n.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->unread)
                $searchArray[] = " (n.seen is null or n.seen = 0) ";
            
            if($request->type_note == "sanction")
                $searchArray[] = " h.sanction_id is not null ";
            else if($request->type_note == "prime")
                $searchArray[] = " h.prime_id is not null ";
            else if($request->type_note == "absence")
                $searchArray[] = " h.absence_id is not null ";
            else if($request->type_note == "paie")
                $searchArray[] = " h.paie_id is not null ";
            else if($request->type_note == "deduction")
                $searchArray[] = " h.deduction_id is not null ";
            else if($request->type_note == "da")
                $searchArray[] = " h.approvisionnement_id is not null ";
            else if($request->type_note == "equipement")
                $searchArray[] = " h.equipement_id is not null ";
            else if($request->type_note == "sav")
                $searchArray[] = " h.sav_id is not null ";
            else if($request->type_note == "flotte")
                $searchArray[] = " h.flotte_id is not null ";
            else if($request->type_note == "visite_poste")
                $searchArray[] = " h.visite_poste_id is not null ";
            else if($request->type_note == "fait_marquant")
                $searchArray[] = " h.fait_marquant_id is not null ";
            else if($request->type_note == "employe")
                $searchArray[] = " h.employe_id is not null ";
            else if($request->type_note == "avance")
                $searchArray[] = " h.avance_id is not null ";
            else if($request->type_note == "part_variable")
                $searchArray[] = " h.part_variable_id is not null ";
            else if($request->type_note == "service")
                $searchArray[] = " h.service24_id is not null ";
            else if($request->type_note == "reclamation")
                $searchArray[] = " h.reclamation_id is not null ";
            else if($request->type_note == "juridique")
                $searchArray[] = " h.juridique_id is not null ";
            else if($request->type_note == "site")
                $searchArray[] = " h.site_id is not null ";
            else if($request->type_note == "satisfaction")
                $searchArray[] = " h.satisfaction_id is not null ";
            else if($request->type_note == "dotation")
                $searchArray[] = " h.dotation_id is not null ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by n.id desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by n.id desc limit ". $request->offset . ", 30";

        return compact('query_where', 'query_and');
    }

    public function index(Request $request){
        $search = $this->search($request);
        $notifications = DB::select("SELECT n.id, h.objet, h.note, h.equipement_id, h.sanction_id, h.prime_id, h.absence_id, 
            h.sav_id, h.flotte_id, h.approvisionnement_id, da.reference as 'da_reference', da.created_at as 'da_created_at', 
            h.deduction_id, h.paie_id, h.avance_id, h.visite_poste_id, h.fait_marquant_id, h.employe_id, h.service24_id, h.site_id, h.satisfaction_id,
            h.reclamation_id, h.part_variable_id, h.juridique_id, h.satisfaction_id, n.created_at, n.historique_id, 
            n.user_id, u.name as 'expediteur_nom', coalesce(ur.email, u.email) as 'expediteur_email',
            n.receiver_id, rec.name as 'destinataire_nom', coalesce(urr.email, rec.email) as 'destinataire_email'
            FROM notifications n
            LEFT JOIN historiques h on h.id = n.historique_id
            LEFT JOIN users u ON u.id = n.user_id
            LEFT JOIN users ur on ur.id = u.real_email_id
            LEFT JOIN users rec on rec.id = n.receiver_id
            LEFT JOIN users urr on urr.id = rec.real_email_id
            LEFT JOIN approvisionnements da on da.id = h.approvisionnement_id
            " . $search['query_where'], []);
        $ids = array_column($notifications, 'historique_id');
        foreach ($notifications as $note) {
            $note->users = [];
        }
        //dd(count($notifications));
        if (count($notifications) > 0) {
            Notification::whereNull('seen')->where('receiver_id', $request->user()->id)->where('id', '<=', $notifications[0]->id)->update(["seen" => 1]);
        }
        if(!$request->consigne && count($ids) > 0) {
            $users = DB::select("SELECT n.historique_id, us.id, us.name, coalesce(ur.email, us.email) as 'address'
                FROM notifications n
                LEFT JOIN users us on us.id = n.receiver_id
                LEFT JOIN users ur on ur.id = us.real_email_id
                WHERE n.receiver_id != ? and n.historique_id in (" . implode(", ", $ids) . ")", [$request->user()->id]);
            foreach ($notifications as $note) {
                foreach ($users as $u) {
                    if($u->historique_id == $note->historique_id
                    && !in_array($u->id, array_column($note->users, "id"))){
                        $note->users[] = $u;
                    }
                }
            }
        }
        return response()->json($notifications);
    }

    public function seen(Request $request){
        $ids = json_decode($request->ids, true);
        if(count($ids) > 0){
            $notes = DB::select("SELECT n.id, h.note, u.name as 'user_nom', u.email as 'user_email'
                FROM notifications n
                LEFT JOIN historiques h ON h.id = n.historique_id
                LEFT JOIN users u ON u.id = n.user_id
                WHERE n.receiver_id = ? and (n.seen is null or n.seen = 0)
                and n.id in (" . implode(",", $ids) . ")", [$request->user()->id]);
            
            foreach ($notes as $n) {
                $historique = new Historique();
                $historique->objet = "Accusé de réception";
                $historique->note = "Note: " . mb_substr($n->note, 0, 20) . (strlen ($n->note) > 20 ? " ..." : "") . ", De: " . $n->user_nom;
                $historique->user_id = $request->user()->id;
                $historique->user = $request->user()->name . " <" . $request->user()->email . ">";
                $historique->created_at = now();
                $historique->save();
            }
            Notification::where('receiver_id', $request->user()->id)
                ->whereIn('id', $ids)
                ->where(function ($q) {
                    return $q->whereNull('seen')->orWhere('seen', 0);
                })
                ->update(['seen' => 1, 'updated_at' => now()]);
            return ["success" => "Marqué comme lu"];
        }
        return ["error" => "EACCES"];
    }

    public function remove_seen($id, Request $request){
        Notification::where('id', $id)
            ->update(['follow' => null, 'updated_at' => now()]);
        return ["success" => "Suivi consigne supprimé"];
    }

    public function store(Request $request){
        $validator = Validator::make($request->all(), [
            'note' => 'required',
        ]);
        if ($validator->fails())
            return response(['error' => $validator->errors()->first()]);

        $historique = new Historique();
        if($request->note_id){
            Notification::where('id', $request->note_id)->update(["seen" => 1]);
            $historique->objet = "Réponse";
        }
        else
            $historique->objet = "Note";
        $historique->note = $request->note;
        $user = DB::select("SELECT us.id, us.name, coalesce(ur.email, us.email) as 'email'  
            from users us left join users ur on ur.id=us.real_email_id 
            where us.id = ?", [$request->user()->id])[0];
        $historique->user_id = $request->user()->id;
        $historique->user = $user->name . " <" . $user->email . ">";
        $historique->created_at = now();
        
        $historique->equipement_id = $request->equipement_id;
        $historique->sanction_id = $request->sanction_id;
        $historique->prime_id = $request->prime_id;
        $historique->absence_id = $request->absence_id;
        $historique->sav_id = $request->sav_id;
        $historique->flotte_id = $request->flotte_id;
        $historique->approvisionnement_id = $request->approvisionnement_id;
        $historique->visite_poste_id = $request->visite_poste_id;
        $historique->fait_marquant_id = $request->fait_marquant_id;
        $historique->employe_id = $request->employe_id;
        $historique->juridique_id = $request->juridique_id;
        $historique->paie_id = $request->paie_id;
        $historique->deduction_id = $request->deduction_id;
        $historique->avance_id = $request->avance_id;
        $historique->service24_id = $request->service24_id;
        $historique->reclamation_id = $request->reclamation_id;
        $historique->site_id = $request->site_id;
        $historique->part_variable_id = $request->part_variable_id;
        $historique->satisfaction_id = $request->satisfaction_id;
        $historique->dotation_id = $request->dotation_id;

        $user_ids = json_decode($request->user_ids, true);
        if($historique->save()){
            if($user_ids && count($user_ids) > 0){
                if($request->fait_marquant_id){
                    $seen = Seen::where('fait_marquant_id', $request->fait_marquant_id)->where('user_id', $request->user()->id)->first();
                    if($seen == null){
                        $seen = new Seen();
                        $seen->fait_marquant_id = $request->fait_marquant_id;
                        $seen->user_id = $request->user()->id;
                        $seen->created_at = new \DateTime();
                    }
                    $seen->send_email = true;
                    $seen->save();
                }
                foreach ($user_ids as $id) {
                    if($id != $request->user()->id){
                        $notif = Notification::create([
                            'historique_id' => $historique->id,
                            'receiver_id' => $id,
                            'user_id' => $request->user()->id,
                            'follow' => $request->follow,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }
            return response(["success" => "Notification bien envoyé"]);
            
        }
    }

}
