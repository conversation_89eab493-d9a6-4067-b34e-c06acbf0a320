[2025-04-07 13:17:38] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:18:38] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:19:38] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:20:39] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:21:39] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:22:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:23:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:24:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:25:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:26:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:27:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:28:43] production.ERROR: file_get_contents(D:\laravel\DRX\admin\.env): failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): file_get_contents(D:\\laravel\\DRX\\admin\\.env): failed to open stream: No such file or directory at D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\KeyGenerateCommand.php:96)
[stacktrace]
#0 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'file_get_conten...', 'D:\\\\laravel\\\\DRX\\\\...', 96, Array)
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\KeyGenerateCommand.php(96): file_get_contents('D:\\\\laravel\\\\DRX\\\\...')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\KeyGenerateCommand.php(80): Illuminate\\Foundation\\Console\\KeyGenerateCommand->writeNewEnvironmentFileWith('base64:gJxr70tf...')
#3 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\KeyGenerateCommand.php(45): Illuminate\\Foundation\\Console\\KeyGenerateCommand->setKeyInEnvironmentFile('base64:gJxr70tf...')
#4 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\KeyGenerateCommand->handle()
#5 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#10 D:\\laravel\\DRX\\admin\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#11 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 D:\\laravel\\DRX\\admin\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 D:\\laravel\\DRX\\admin\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\KeyGenerateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 D:\\laravel\\DRX\\admin\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\laravel\\DRX\\admin\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 {main}
"} 
[2025-04-07 13:28:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:29:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:30:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:31:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:32:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:33:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:34:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:35:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:36:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:37:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:38:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:39:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:40:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:41:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:42:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:43:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:44:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:45:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:46:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:47:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:48:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:49:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:50:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:51:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:52:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:53:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:54:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:55:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-04-07 13:56:52] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:48:54] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:49:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:50:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:51:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:52:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:53:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:54:34] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:55:34] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:56:48] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:57:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:58:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 13:59:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 14:00:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 14:01:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 14:02:35] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:36:20] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:37:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:38:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:39:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:40:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:41:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:42:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:43:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:44:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:45:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:46:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:47:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:48:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:49:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:50:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:51:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:52:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:53:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:54:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:55:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:56:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:57:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-19 16:58:18] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:20:34] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:21:08] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:22:08] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:23:08] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:24:08] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:25:08] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:26:54] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:27:53] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:28:53] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:29:53] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:30:53] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:31:53] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:32:53] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:33:53] production.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:34:54] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:35:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:36:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:37:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:38:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:39:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:40:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:41:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:42:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:43:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:44:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:45:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:46:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:47:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:48:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:49:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:50:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:51:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:52:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:53:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:54:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:55:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:56:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:57:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:58:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 05:59:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:00:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:01:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:02:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:03:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 09:08:50] local.ERROR: Invalid key supplied {"userId":5,"exception":"[object] (LogicException(code: 0): Invalid key supplied at D:\\laravel\\DRX\\admin\\vendor\\league\\oauth2-server\\src\\CryptKey.php:67)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(275): League\\OAuth2\\Server\\CryptKey->__construct('file://D:\\\\larav...', NULL, false)
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(214): Laravel\\Passport\\PassportServiceProvider->makeCryptKey('private')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(104): Laravel\\Passport\\PassportServiceProvider->makeAuthorizationServer()
#3 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#4 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#5 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('League\\\\OAuth2\\\\S...', Array, true)
#6 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('League\\\\OAuth2\\\\S...', Array)
#7 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('League\\\\OAuth2\\\\S...', Array)
#8 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('League\\\\OAuth2\\\\S...')
#9 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#10 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#11 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Laravel\\\\Passpor...')
#12 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('Laravel\\\\Passpor...', Array, true)
#13 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Laravel\\\\Passpor...', Array)
#14 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('Laravel\\\\Passpor...', Array)
#15 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\HasApiTokens.php(66): Illuminate\\Foundation\\Application->make('Laravel\\\\Passpor...')
#16 D:\\laravel\\DRX\\admin\\app\\Http\\Controllers\\Auth\\UserAuthController.php(165): App\\Models\\User->createToken('API Token')
#17 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\UserAuthController->login(Object(Illuminate\\Http\\Request))
#18 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('login', Array)
#19 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\UserAuthController), 'login')
#20 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#21 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#22 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(103): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#27 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#28 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#29 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laravel\\DRX\\admin\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 D:\\laravel\\DRX\\admin\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 D:\\laravel\\DRX\\admin\\server.php(21): require_once('D:\\\\laravel\\\\DRX\\\\...')
#55 {main}
"} 
[2025-05-20 06:08:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 09:08:57] local.ERROR: Invalid key supplied {"userId":5,"exception":"[object] (LogicException(code: 0): Invalid key supplied at D:\\laravel\\DRX\\admin\\vendor\\league\\oauth2-server\\src\\CryptKey.php:67)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(275): League\\OAuth2\\Server\\CryptKey->__construct('file://D:\\\\larav...', NULL, false)
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(214): Laravel\\Passport\\PassportServiceProvider->makeCryptKey('private')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(104): Laravel\\Passport\\PassportServiceProvider->makeAuthorizationServer()
#3 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#4 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#5 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('League\\\\OAuth2\\\\S...', Array, true)
#6 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('League\\\\OAuth2\\\\S...', Array)
#7 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('League\\\\OAuth2\\\\S...', Array)
#8 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('League\\\\OAuth2\\\\S...')
#9 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#10 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#11 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Laravel\\\\Passpor...')
#12 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('Laravel\\\\Passpor...', Array, true)
#13 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Laravel\\\\Passpor...', Array)
#14 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('Laravel\\\\Passpor...', Array)
#15 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\HasApiTokens.php(66): Illuminate\\Foundation\\Application->make('Laravel\\\\Passpor...')
#16 D:\\laravel\\DRX\\admin\\app\\Http\\Controllers\\Auth\\UserAuthController.php(165): App\\Models\\User->createToken('API Token')
#17 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\UserAuthController->login(Object(Illuminate\\Http\\Request))
#18 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('login', Array)
#19 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\UserAuthController), 'login')
#20 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#21 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#22 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(103): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#27 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#28 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#29 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laravel\\DRX\\admin\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 D:\\laravel\\DRX\\admin\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 D:\\laravel\\DRX\\admin\\server.php(21): require_once('D:\\\\laravel\\\\DRX\\\\...')
#55 {main}
"} 
[2025-05-20 09:09:02] local.ERROR: Invalid key supplied {"userId":5,"exception":"[object] (LogicException(code: 0): Invalid key supplied at D:\\laravel\\DRX\\admin\\vendor\\league\\oauth2-server\\src\\CryptKey.php:67)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(275): League\\OAuth2\\Server\\CryptKey->__construct('file://D:\\\\larav...', NULL, false)
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(214): Laravel\\Passport\\PassportServiceProvider->makeCryptKey('private')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(104): Laravel\\Passport\\PassportServiceProvider->makeAuthorizationServer()
#3 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#4 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#5 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('League\\\\OAuth2\\\\S...', Array, true)
#6 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('League\\\\OAuth2\\\\S...', Array)
#7 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('League\\\\OAuth2\\\\S...', Array)
#8 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('League\\\\OAuth2\\\\S...')
#9 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#10 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#11 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Laravel\\\\Passpor...')
#12 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('Laravel\\\\Passpor...', Array, true)
#13 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Laravel\\\\Passpor...', Array)
#14 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('Laravel\\\\Passpor...', Array)
#15 D:\\laravel\\DRX\\admin\\vendor\\laravel\\passport\\src\\HasApiTokens.php(66): Illuminate\\Foundation\\Application->make('Laravel\\\\Passpor...')
#16 D:\\laravel\\DRX\\admin\\app\\Http\\Controllers\\Auth\\UserAuthController.php(165): App\\Models\\User->createToken('API Token')
#17 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\UserAuthController->login(Object(Illuminate\\Http\\Request))
#18 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('login', Array)
#19 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\UserAuthController), 'login')
#20 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#21 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#22 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(103): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#27 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#28 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#29 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laravel\\DRX\\admin\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 D:\\laravel\\DRX\\admin\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 D:\\laravel\\DRX\\admin\\server.php(21): require_once('D:\\\\laravel\\\\DRX\\\\...')
#55 {main}
"} 
[2025-05-20 06:09:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:10:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:11:53] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:16:50] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:17:50] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:18:50] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:19:50] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:20:50] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 06:21:50] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:06:28] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:07:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:08:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:09:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:10:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:11:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:12:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:13:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:14:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:15:39] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:16:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:17:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:18:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:19:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-20 07:20:27] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:27:59] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:28:22] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:29:22] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:30:22] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:31:22] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:32:35] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:33:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:34:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:35:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:36:36] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:37:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:38:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:39:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:40:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:41:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:42:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:43:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:44:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:45:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:46:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:47:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:48:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:49:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:50:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:51:23] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:54:58] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:55:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:56:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:57:55] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:58:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 05:59:55] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:00:55] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:01:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:02:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:03:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:04:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:05:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:06:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:07:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:08:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:09:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:10:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:11:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:12:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:13:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:14:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:15:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:16:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:17:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:18:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:19:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:20:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:21:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:22:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:23:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:24:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:25:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:26:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:27:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:28:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:29:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:30:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:31:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:32:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:33:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:34:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:35:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:36:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:37:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:38:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:39:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:40:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:41:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:42:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 06:43:56] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:06:41] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:07:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:08:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:09:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:10:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:11:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:12:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:13:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:14:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:15:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:16:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:17:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:18:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:19:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-30 11:20:08] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Models\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Models\\\\Mode...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Uncaught Refle...', '0', '['type' => 1, '...', '0', '???', '???')
#1 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
