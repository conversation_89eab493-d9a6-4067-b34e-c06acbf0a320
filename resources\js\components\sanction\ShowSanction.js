import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';
import numberUtil from '../util/numberUtil'

import useToken from '../util/useToken';
import matricule from '../util/matricule';
import moment from 'moment';
import ActionSanction from './ActionSanction';
import Tab from '../layout/Tab';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';


export default function ShowSanction({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [sanction, setSanction] = useState(null)
    const [isLoading, toggleLoading] = useState(true)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/sanction/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setSanction(res.data)
                const newUser = []
                if (auth.id != res.data.user_id) {
                    newUser.unshift({ id: res.data.superviseur_id, address: res.data.sup_email, name: res.data.sup_nom })
                }
                setDefautUsers(newUser)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(sanction)
    }, [sanction]);

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                sanction &&
                <>
                    <ShowHeader size={size} label="Sanction" id={sanction.id} closeDetail={() => setCurrentId()}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + sanction.status_color}>
                                    {sanction.status_description}
                                </span> {
                                    sanction.nb_pj > 0 &&
                                    <span className='badge-outline'>
                                        Pièce jointe : {sanction.nb_pj}
                                    </span>
                                }
                            </span>
                            <div style={{display:'flex',alignItems: 'center'}}>
                            </div>                        
                        </div>
                        <h3>
                            {matricule(sanction)} {sanction.employe} 
                        </h3>
                        <div>
                            Site : <span className='text'>{sanction.site}</span>
                        </div>
                        {
                            !sanction.absence &&
                            <div>
                                Date du service : <span className='text'>
                                    {
                                        moment(sanction.date_pointage).format("dddd DD MMM YYYY") 
                                        + " " + (moment(sanction.date_pointage).format("HH:mm:ss") == "07:00:00" ? " JOUR" : " NUIT")}
                                </span>
                            </div>
                        }
                        {
                            sanction.objet &&
                            <div>
                                Objet : <span className='text'>{sanction.objet}</span>
                            </div>
                        }
                        {
                            sanction.montant > 0 &&
                            <div>
                                Montant : <span className='text'>{numberUtil(sanction.montant)}</span>
                            </div>
                        }
                        <p>
                            Motif : <span className='text'>{sanction.motif}</span>
                        </p>
                        {
                            sanction.mesure &&
                            <p>
                                Mesure prise : <span className='text'>{sanction.mesure}</span>
                            </p>
                        }
                        {
                            sanction.user_id != sanction.superviseur_id &&
                            <div>
                                Superviseur responsable : <span className='text'> 
                                    {sanction.sup_nom} {' <' + sanction.sup_email + '>'}
                                </span>
                            </div>
                        }
                        <div>
                            Demandeur : <span className='text'> 
                                {sanction.user_nom} {' <' + sanction.user_email + '>'}
                            </span>
                        </div>
                        <div>
                            Le : <span className='text'>{moment(sanction.created_at).format("DD MMMM YYYY")}</span>
                        </div>
                        <div className='card-action'>
                            <ActionSanction auth={auth} sanction={sanction} updateData={updateData} toggleLoading={toggleLoading}/>
                        </div>
                    </div>
                    <Tab auth={auth} name="sanction_id" value={sanction.id} updateData={updateData} defautUsers={defautUsers}/>
                </>
            }
        </div>
    } </>
}