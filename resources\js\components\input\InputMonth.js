import {useState,useRef} from 'react'

import useClickOutside from '../util/useClickOutside'

export default function InputMonth({selected, setSelected, label, required, disable}) {
    const [isActive, setIsActive] = useState(false)
    const selectRef = useRef(null)
    const options = [
        "Janvier",
        "Février",
        "Mars", 
        "Avril",
        "Mai", 
        "Juin", 
        "Juillet", 
        "Aout", 
        "Sept<PERSON>bre", 
        "Octobre", 
        "Novembre", 
        "Décembre", 
    ]
    useClickOutside(selectRef, () => setIsActive(false))

    return (
        <div className='input-container'>
            {label && <label>{label} {required && <span className='danger'>*</span>}</label>}
            <select id="month-select" style={{height: "44px"}} value={selected} onChange={(e) => setSelected(e.target.value)} disabled={disable}>
                <option></option>
                {options.map((option, index) => (
                    <option key={index} value={(index + 1).toString().padStart(2, "0")}>
                        {option}
                    </option>
                ))}
            </select>
            {/* <div className='select-container'>
                <div className={'select-input '} ref={selectRef}>
                    <div className={"select-placeholder " + (isActive ? "active-select" : "") }
                        onClick={(e) => setIsActive(!isActive)}>
                        <span>{selected > 0 ? options[selected-1] : ''}</span>
                        <span>{isActive ? <IoMdArrowDropup/> : <IoMdArrowDropdown/>}</span>
                    </div>
                    {isActive && 
                        <div className="select-content">
                            {options.map((option,index) => (
                                <div 
                                    key={index}
                                    onClick={(e) => {
                                        setSelected(index+1)
                                        setIsActive(false)
                                    }}
                                    className="select-option"
                                >
                                    {option.label ? option.label : option}
                                </div>
                            ))}
                            
                        </div>
                    }
                </div>
            </div> */}
        </div>
    )
}
