import {IoMdClose} from 'react-icons/io';
import {AiTwotoneEdit} from 'react-icons/ai'
import { useEffect, useState } from 'react';
import axios from 'axios';
import EditCritereModal from './EditCritereModal';
import ConfirmModal from '../../../modal/ConfirmModal';
import useToken from '../../../util/useToken';
import showAmount from '../../../util/numberUtil';

export default function CriterePartVariable({auth, value}) {
    const [showDeleteModal, toggleDeleteModal] = useState(false) 
    const [showAddModal, toggleAddModal] = useState(false) 
    const [showEditModal, toggleEditModal] = useState(false) 
    const [currentCritere, setCurrentCritere] = useState(null)
    const [criteres, setCriteres] = useState([])

    const handleConfirmDelete = () => {
            axios.post('/api/critere_part/delete/' + currentCritere.id, [], useToken())
            .then((res) => {
                if(res.data.success){
                    if(updateData) updateData()
                    toggleDeleteModal(false)
                }                
                else if(res.data.error == "EACCES")
                    setError("Une erreur est survenue.")
                else if(res.data.error)
                    setError(res.data.error)
            })
            .catch((e) => {
                console.error(e)
                setError("Erreur d'envoie, réessayez.")
            })
    }

    const handleDeleteCritere = (critere) => {
        setCurrentCritere(critere)
        toggleDeleteModal(true)
    }

    const handleEditCritere = (critere) => {
        setCurrentCritere(critere)
        toggleEditModal(true)
    }

    const handleAddCritere = () => {
        setCurrentCritere(null)
        toggleAddModal(true)
    }

    const updateData = (isMounted) => {
        if(isMounted) {
            axios.get("/api/critere_part/employe/" + value, useToken())
            .then((res) => {
                if(res.data.error)
                    console.error(res.data.error)
                else if(res.data.criteres)
                    setCriteres(res.data.criteres)
            })
        }
    }

    useEffect(() => {
        let isMounted = true
        if(isMounted)
            updateData(isMounted)
        return () => { isMounted = false };
    }, []);

    return (
        <>
            {
                showAddModal &&
                <EditCritereModal 
                    currentEmployeId={value}
                    action="/api/critere_part/add"
                    updateData={updateData}
                    closeModal={() => toggleAddModal(false)} 
                />
            }
            {
                showEditModal &&
                <EditCritereModal 
                    currentCritere={currentCritere}
                    action={"/api/critere_part/update/" + currentCritere.id}
                    updateData={updateData}
                    closeModal={() => toggleEditModal(false)} 
                />
            }
            {
                showDeleteModal &&
                <ConfirmModal
                    msg="Supprimé ce critère ?"
                    confirmAction={handleConfirmDelete}
                    closeModal={() => toggleDeleteModal(false)} 
                />
            }
            <div>
                {
                    criteres.map((cr, index) => (
                        <div key={index} className="card-container">
                            <div className='pointage-container'>
                                <div>
                                    {cr.designation}
                                    <br/>
                                    <span className='secondary'>
                                        {showAmount(cr.montant)}
                                    </span>
                                </div>
                                {/*
                                    ["admin", "rh"].includes(auth.role) &&
                                    <div>
                                        <span onClick={() => handleEditCritere(cr)}>
                                            <AiTwotoneEdit size={20}/>
                                        </span>
                                        <span onClick={() => handleDeleteCritere(cr)}>
                                            <IoMdClose size={20}/>
                                        </span>
                                    </div>
                                */}
                            </div>
                        </div>
                    ))
                }
            </div>
        </>
    )
}
