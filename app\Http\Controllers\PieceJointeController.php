<?php

namespace App\Http\Controllers;

use App\Http\Controllers\MailController;
use Illuminate\Http\Request;
use App\Models\PieceJointe;
use App\Models\Employe;
use App\Models\Recrutement;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Sanction;
use App\Models\Prime;
use App\Models\Sav;
use App\Models\Approvisionnement;
use App\Models\Equipement;
use App\Models\Absence;
use App\Models\Article;
use App\Models\Site;
use App\Models\Flotte;
use App\Models\VisitePoste;
use App\Models\FaitMarquant;
use App\Models\Notification;
use Storage;
use File;

class PieceJointeController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }
    
    protected static function getType($type){
        if($request->equipement_id) {
            $type_id = "equipement_id";
            $value = $request->equipement_id;
            $historique_action = "action_equipement";
            $historique_new = "new_equipement";
            $mail = "equipement";
        }
        else if($request->sanction_id){
            $type_id = "sanction_id";
            $historique_action = "action_sanction";
            $historique_new = "new_sanction";
            $mail = "sanction";
        }
        else if($request->prime_id) {
            $type_id = "prime_id";
            $historique_action = "action_prime";
            $historique_new = "new_prime";
            $mail = "prime";
        }
        else if($request->absence_id) {
            $type_id = "absence_id";
            $historique_action = "action_absence";
            $historique_new = "new_absence";
            $mail = "absence";
        }
        else if($request->sav_id) {
            $type_id = "sav_id";
            $historique_action = "action_sav";
            $historique_new = "new_sav";
            $mail = "sav";
        }
        else if($request->flotte_id) {
            $type_id = "flotte_id";
            $historique_action = "action_flotte";
            $historique_new = "new_flotte";
            $mail = "flotte";
        }
        else if($request->approvisionnement_id) {
            $type_id = "approvisionnement_id";
            $historique_action = "action_approvisionnement";
            $historique_new = "new_approvisionnement";
            $mail = "approvisionnement";
        }
        else if($request->visite_poste_id) {
            $type_id = "visite_poste_id";
            $historique_action = "action_visite";
            $historique_new = "new_visite";
            $mail = "visite";
        }
        else if($request->fait_marquant_id) {
            $type_id = "fait_marquant_id";
            $historique_action = "action_fait_marquant";
            $historique_new = "new_visite";
            $mail = "fait";
        }
        else if($request->article_id) {
            $type_id = "article_id";
            $historique_action = "action_article";
            $historique_new = "new_article";
            $mail = "article";
        }
        else if($request->employe_id) {
            $type_id = "employe_id";
            $historique_action = "action_employe";
            $historique_new = "new_employe";
            $mail = "employe";
        }
        else if($request->juridique_id) {
            $type_id = "juridique_id";
            $historique_action = "action_juridique";
            $historique_new = "new_juridique";
            $mail = "juridique";
        }
        else if($request->site_id) {
            $type_id = "site_id";
            $historique_action = "action_site";
            $historique_new = "new_site";
            $mail = "site";
        }
        else if($request->deduction_id){
            $type_id = "deduction_id";
            $historique_action = "action_deduction";
            $historique_new = "new_deduction";
            $mail = "deduction";
        }
        else if ($request->paie_id) {
            $type_id = "paie_id";
            $historique_action = "action_paie";
            $historique_new = "new_paie";
            $mail = "paie";
        }
        else if($request->avance_id) {
            $type_id="avance_id";
            $historique_action = "avance_action";
            $historique_new = "new_avance";
            $mail = "avance";
        }
        else if($request->part_variable_id) {
            $type_id="part_variable_id";
            $historique_action = "part_variable_action";
            $historique_new = "new_part_variable";
            $mail = "part_variable";
        }
        else if($request->service24_id) {
            $type_id= "service24_id";
            $historique_action = "service24_action";
            $historique_new = "new_service24";
            $mail = "service24";
        }
        else if ($request->reclamation_id) {
            $type_id="reclamation_id";
            $historique_action = "action_reclamation";
            $historique_new = "new_reclamation";
            $mail = "reclamation";
        }
        else if ($request->satisfaction_id) {
            $type_id = "satisfaction_id";
            $historique_action = "action_satisfaction";
            $historique_new = "new_satisfaction";
            $mail = "satisfaction";
        }
        else if ($request->planning_id) {
            $type_id = "planning_id";
            $historique_action = "action_planning";
            $historique_new = "new_planning";
            $mail = "planning";
        }
        else if ($request->dotation_id) {
            $type_id = "dotation_id";
            $historique_action = "action_dotation";
            $historique_new = "new_dotation";
            $mail = "dotation";
        }
        else if ($request->recrutement_id) {
            $type_id = "recrutement_id";
            $historique_action = "action_recrutement";
            $historique_new = "new_recrutement";
            $mail = "recrutement";
        }
        else {
            $type_id = "";
            $historique_action = "";
            $historique_new = "";
            $mail = "";
        }
        return [$type_id, $historique_action, $historique_new, $mail];
    }
    
    protected static function getNature($nature){
        switch($nature){
            case "cin" :
                $pj_nature = "cin";
                break;
            case "cv" :
                $pj_nature = "cv";
                break;
            case "photo" :
                $pj_nature = "photo";
                break;
            case "residence" : 
                $pj_nature = "residence";
                break;
            case "plan_reperage" : 
                $pj_nature = "plan_reperage";
                break;
            case "bulletin_n3" : 
                $pj_nature = "bulletin_n3";
                break;
            case "bonne_conduite" : 
                $pj_nature = "bonne_conduite";
                break;
            default :
                $pj_nature = "";
        }
        return [$pj_nature];
    }

    protected static function getSearch($request){
        $search = "";
        if($request->sanction_id)
            $search = "sanction_id = '". $request->sanction_id ."'";
        else if($request->prime_id)
            $search = "prime_id = '". $request->prime_id ."'";
        else if($request->sav_id)
            $search = "sav_id = '". $request->sav_id ."'";
        else if($request->approvisionnement_id)
            $search = "approvisionnement_id = '". $request->approvisionnement_id ."'";
        else if($request->equipement_id)
            $search = "equipement_id = '". $request->equipement_id ."'";
        else if($request->absence_id)
            $search = "absence_id = '". $request->absence_id ."'";
        else if($request->article_id)
            $search = "article_id = '". $request->article_id ."'";
        else if($request->employe_id)
            $search = "employe_id = '". $request->employe_id ."'";
        else if($request->site_id)
            $search = "site_id = '". $request->site_id ."'";
        else if($request->flotte_id)
            $search = "flotte_id = '". $request->flotte_id ."'";
        else if($request->visite_poste_id)
            $search = "visite_poste_id = '". $request->visite_poste_id ."'";
        else if($request->fait_marquant_id)
            $search = "fait_marquant_id = '". $request->fait_marquant_id ."'";
        else if($request->juridique_id)
            $search = "juridique_id = '". $request->juridique_id ."'";
        else if($request->deduction_id)
            $search = "deduction_id = '". $request->deduction_id ."'";	
        else if($request->paie_id)
            $search = "paie_id = '". $request->paie_id ."'";
        else if($request->avance_id)
            $search = "avance_id = '". $request->avance_id ."'";
        else if($request->part_variable_id)
            $search = "part_variable_id = '". $request->part_variable_id ."'";
        else if ($request->service24_id)
            $search = "service24_id = '". $request->service24_id."'";
        else if ($request->reclamation_id)
            $search = "reclamation_id = '". $request->reclamation_id;
        else if ($request->satisfaction_id) 
            $search = "satisfaction_id = '". $request->satisfaction_id;
        else if ($request->planning_id)
            $search = "planning_id = '". $request->planning_id;
        else if ($request->dotation_id)
            $search = "dotation_id = '". $request->dotation_id;
        else if ($request->recrutement_id)
            $search = "recrutement_id = '". $request->recrutement_id;
        if($search)
            return " WHERE " . $search . " ";
        return "";
    }

    public function index(Request $request){
        $search = $this->getSearch($request);
        if($search){         
            $type_id = PieceJointeController::getSearch($request)[0];
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path, pj.user_id, pj.created_at, pj.updated_at
                FROM piece_jointes pj " .
                $search . "
                order by pj.created_at desc", []);
            return response($piece_jointes);
        }
        return response(["error" => "EACCES"]);
    }

    public static function store_and_join(Request $request){
        $auth = $request->user();
        $validator = Validator::make($request->all(), [
            'nature' => 'required|max:15',
            'pj' => 'required',
        ]);
        if ($validator->fails())
            return response(['error' => $validator->errors()->first()]);
        $piece_jointe = new PieceJointe();
        $pj_date = date("Y-m-d_His",time());
        $file_ext = $request->file('pj')->extension();   
        
        if($request->equipement_id) {
            $fileName = 'equipement_' . $request->equipement_id . '_' . $pj_date . '.' . $file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            HistoriqueController::action_equipement($request, "Ajout pièce jointe", $request->equipement_id);
            $piece_jointe->equipement_id = $request->equipement_id;
        }
        else if($request->sanction_id){
            $fileName = 'sanction_' . $request->sanction_id . '_' . $pj_date . '.' . $file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            HistoriqueController::action_sanction($request, "Ajout pièce jointe", $request->sanction_id);
            $piece_jointe->sanction_id = $request->sanction_id;
        }
        else if($request->prime_id) {
            $fileName = 'prime_' . $request->prime_id . '_' . $pj_date . '.' . $file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            HistoriqueController::action_prime($request, "Ajout pièce jointe", $request->prime_id);
            $piece_jointe->prime_id = $request->prime_id;
        }
        else if($request->absence_id) {
            $fileName = 'absence_' . $request->absence_id . '_' . $pj_date . '.' . $file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            HistoriqueController::action_absence($request, "Ajout pièce jointe", $request->absence_id);
            $piece_jointe->absence_id = $request->absence_id;
        }
        else if($request->sav_id) {
            $fileName = 'sav_' . $request->sav_id . '_' . $pj_date . '.' . $file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            HistoriqueController::action_sav($request, "Ajout pièce jointe", $request->sav_id);
            $piece_jointe->sav_id = $request->sav_id;
        }
        else if($request->flotte_id) {
            $fileName = 'flotte_' . $request->flotte_id . '_' . $pj_date . '.' . $file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            HistoriqueController::action_flotte($request, "Ajout pièce jointe", $request->flotte_id);
            $piece_jointe->flotte_id = $request->flotte_id;
        }
        else if($request->approvisionnement_id) {
            $fileName = 'da_' . $request->approvisionnement_id . '_' . $pj_date . '.' . $file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            $note_id = HistoriqueController::action_approvisionnement($request, "Ajout pièce jointe", $request->approvisionnement_id);
            $piece_jointe->approvisionnement_id = $request->approvisionnement_id;
            if($request->user()->role != "compta"){
                $users = DB::select("SELECT us.id from users us where us.role = 'compta'", []);
                foreach ($users as $u) {
                    Notification::create([
                        'historique_id' => $note_id,
                        'receiver_id' => $u->id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
        else if($request->visite_poste_id) {
            $fileName = 'visite_' . $request->visite_poste_id . '_' . $pj_date . '.' . $file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            HistoriqueController::action_visite($request, "Ajout pièce jointe", $request->visite_poste_id);
            $piece_jointe->visite_poste_id = $request->visite_poste_id;
        }
        else if($request->fait_marquant_id) {
            $fileName = 'fait_' . $request->fait_marquant_id . '_' . $pj_date . '.' . $file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            HistoriqueController::action_fait_marquant($request, "Ajout pièce jointe", $request->fait_marquant_id);
            $piece_jointe->fait_marquant_id = $request->fait_marquant_id;
        }
        else if($request->juridique_id) {
            $fileName = 'juridique_' . $request->juridique_id . '_' . $pj_date . '.' . $file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            HistoriqueController::action_juridique($request, "Ajout pièce jointe", $request->juridique_id);
            $piece_jointe->juridique_id = $request->juridique_id;
        }
        else if($request->employe_id) {
            $fileName = 'employe_' . $request->employe_id . '_'. $request->nature . '_'. $pj_date. '.' .$file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            HistoriqueController::action_employe($request, "Ajout pièce jointe", $request->employe_id);
            $piece_jointe->employe_id = $request->employe_id;
            $nature = PieceJointeController::getNature($request->nature)[0];
            if($nature){
                $employe = Employe::find($request->employe_id);
                $employe_pj = $nature;
                $employe->$employe_pj = 1;
                $employe->last_update = new \DateTime();
                $employe->save();
            }
        }
        else if($request->avance_id){
            $fileName = 'avance_' . $request->avance_id . '_' . $pj_date . '.' . $file_ext;
            $request->note =$request->nature . " <".$fileName . ">";
            HistoriqueController::action_avance($request, 'Ajout pièce jointe', $request->avance_id);
            $piece_jointe->avance_id = $request->avance_id;
        }
        else if($request->deduction_id){
            $fileName = 'deduction_' . $request->deduction_id . '_' . $pj_date . '.' . $file_ext;
            $request->note =$request->nature . " <".$fileName . ">";
            HistoriqueController::action_deduction($request, 'Ajout pièce jointe', $request->deduction_id);
            $piece_jointe->deduction_id = $request->deduction_id;
        }
        else if($request->paie_id){
            $fileName = 'paie_' . $request->paie_id . '_' . $pj_date . '.' . $file_ext;
            $request->note =$request->nature . " <".$fileName . ">";
            HistoriqueController::action_paie($request, 'Ajout pièce jointe', $request->paie_id);
            $piece_jointe->paie_id = $request->paie_id;

        }
        else if ($request->service24_id) {
            $fileName = 'service24_' . $request->service24_id . '_' . $pj_date. '.' .$file_ext;
            $request->note = $request->nature. " <". $fileName. ">";
            HistoriqueController::action_service24($request, "Ajout pièce jointe", $request->service24_id);
            $piece_jointe->service24_id = $request->service24_id;
        }
        else if($request->reclamation_id){
            $fileName ='reclamation_'. $request->reclamation_id. '_'. $pj_date. '.'. $file_ext;
            $request->note = $request->nature. " <". $fileName. ">";
            HistoriqueController::action_reclamation($request, "Ajout pièce jointe", $request->reclamation_id);
            $piece_jointe->reclamation_id = $request->reclamation_id;
        }
        else if ($request->part_variable_id) {
            $fileName = 'part_variable_' . $request->part_variable_id . '_'. $pj_date. '.'. $file_ext;
            $request->note = $request->nature . " <" . $fileName . ">";
            HistoriqueController::action_part_variable($request, "Ajout pièce jointe", $request->part_variable_id);
            $piece_jointe->part_variable_id = $request->part_variable_id;
        }
        else if ($request->satisfaction_id) {
            $fileName = 'satisfaction_'.$request->satisfaction_id . '_' . $pj_date. '.'. $file_ext;
            $request->note = $request->nature. " <". $fileName. ">";
            HistoriqueController::action_satisfaction($request, "Ajout pièce jointe", $request->satisfaction_id);
            $piece_jointe->satisfaction_id = $request->satisfaction_id;
        }
        else if ($request->planning_id) {
            $fileName = 'planning_'.$request->planning_id . '_' . $pj_date. '.'. $file_ext;
            $request->note = $request->nature. " <". $fileName. ">";
            HistoriqueController::action_planning($request, "Ajout pièce jointe", $request->planning_id);
            $piece_jointe->planning_id = $request->planning_id;
        }
        else if ($request->dotation_id) {
            $fileName = 'dotation_'.$request->dotation_id . '_' . $pj_date. '.'. $file_ext;
            $request->note = $request->nature. " <". $fileName. ">";
            // HistoriqueController::action_dotation($request, "Ajout pièce jointe", $request->dotation_id);
            $piece_jointe->dotation_id = $request->dotation_id;
        }
        else if($request->recrutement_id) {
            $fileName = 'recrutement' . $request->recrutement_id . '_'. $request->nature . '_'. $pj_date. '.' .$file_ext;
            $request->note = $request->nature . " <" . $fileName .">";
            // HistoriqueController::action_recrutement($request, "Ajout pièce jointe", $request->recrutement_id);
            $piece_jointe->recrutement_id = $request->recrutement_id;
            $nature = PieceJointeController::getNature($request->nature)[0];
            if($nature){
                $recrutement = Recrutement::find($request->recrutement_id);
                $recrutement_pj = $nature;
                $recrutement->$recrutement_pj = 1;
                // $recrutement->last_update = new \DateTime();
                $recrutement->save();
            }
        }
        $request->file('pj')->storeAs(
            'uploads',
            $fileName,
            'public'
        );
        $piece_jointe->nature = $request->nature;
        $piece_jointe->path = $fileName;
        $piece_jointe->user_id = $auth->id;
        $piece_jointe->created_at = new \DateTime();
        if($piece_jointe->save())
            return response(["success" => "Pièce jointe bien ajouté", "id" => $piece_jointe->id]);
        return response(["error" => "Erreur d'envoi, réessayez"]);
    }

    public static function store_and_show(Request $request, $type){
        $auth = $request->user();
        $validator = Validator::make($request->all(), [
            'nature' => 'required',
            'pj' => 'required',
        ]);
        if ($validator->fails())
            return response(['error' => $validator->errors()->first()]);
        $piece_jointe = new PieceJointe();

        $pj_date = date("Y-m-d_His",time());
        $file_ext = $request->file('pj')->extension();
        $fileName = $type. '_'. $pj_date. '.' .$file_ext;                     
        $type_id = PieceJointeController::getType($type)[0];
        $historique = PieceJointeController::getType($type)[2];
        $request->file('pj')->storeAs(
            'uploads',
            $fileName,
            'public'
        );
        $piece_jointe->nature = $request->nature;                
        $piece_jointe->path = $fileName;                
        $piece_jointe->user_id = $auth->id;      
        $piece_jointe->created_at = new \DateTime();
        $piece_jointe->save();

        return response()->json($piece_jointe);    
    }

    public function edit(Request $request,$type,$id){
        $auth = $request->user();
        if(in_array($auth->role, ["rh", "resp_rh"])){
            $validator = Validator::make($request->all(), [
                'nature' => 'required',
                'pj' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $pj_id = DB::select("SELECT id FROM piece_jointes WHERE $type = ? AND nature = ?",[$id,$request->nature])[0];
            $piece_jointe = PieceJointe::find($pj_id->id);
            $path = public_path().'/uploads/'.$piece_jointe->path; 
    
            $pj_date = date("Y-m-d_His",time());
            $file_ext = $request->file('pj')->extension();                             
            //$type_id = PieceJointeController::getType($type)[0];
            $fileName = $type .'_' . $id . '_'. $request->nature . '_'. $pj_date. '.' .$file_ext;
            /*if($type_id == "employe_id"){
                $fileName = $type .'_' . $id . '_'. $request->nature . '_'. $pj_date. '.' .$file_ext;
            }else{
                $fileName = $type .'_' . $id . '_' . $pj_date . '.' . $file_ext;
            }*/
            //$historique = PieceJointeController::getType($type)[1];
            $request->file('pj')->storeAs(
                'uploads',
                $fileName,
                'public'
            );              
            $piece_jointe->path = $fileName;                
            $piece_jointe->user_id = $auth->id;
            //$piece_jointe->$type_id = $id;
            $piece_jointe->$type = $id;                            
            $piece_jointe->updated_at = new \DateTime();
            if($piece_jointe->save()){
                File::delete($path);
                if ($type == "employe_id") {
                    HistoriqueController::action_employe($request, "Modification pièce jointe : <".$fileName.">", $id);
                } else if ($type == "recrutement_id") {
                    HistoriqueController::action_recrutement($request, "Modification pièce jointe : <".$fileName.">", $id);
                }
                /*if($type_id == "employe_id"){
                    HistoriqueController::action_employe($request, "Modification pièce jointe : <".$fileName.">", $id);
                } else{
                    HistoriqueController::$historique($request, "Modification pièce jointe : <".$fileName.">", $id);
                }*/
                
                return response(["success" => "Pièce jointe bien modifié", "id" => $piece_jointe->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        else 
            return response(["error" => "EACCES"]);
    }


    public function destroy(Request $request, $type, $id, $historique_id){
        $auth = $request->user();
        $historique = PieceJointeController::getType($type)[1];
        $piece_jointe = PieceJointe::find($id);
        if($auth->id == $piece_jointe->user_id) {           
            $path = public_path().'/uploads/'.$piece_jointe->path;            
            File::delete($path);
            HistoriqueController::$historique($request, "Suppression pièce jointe : <".$piece_jointe->path.">", $historique_id);
            return $piece_jointe->delete();
        }
        else 
            return response(["error" => "EACCES"]);
    }
    public function tab(Request $request) {
        if($request->equipement_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path FROM piece_jointes pj
                WHERE pj.equipement_id = ? ORDER BY id desc", [$request->equipement_id]);
        }
        else if($request->sanction_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.sanction_id = ?", [$request->sanction_id]);
        }
        else if($request->prime_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.prime_id = ?", [$request->prime_id]);
        }
        else if($request->absence_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.absence_id = ?", [$request->absence_id]);
        }
        else if($request->sav_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.sav_id = ?", [$request->sav_id]);
        }
        else if($request->flotte_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.flotte_id = ?", [$request->flotte_id]);
        }
        else if($request->approvisionnement_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.approvisionnement_id = ?", [$request->approvisionnement_id]);
        }
        else if($request->visite_poste_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.visite_poste_id = ?", [$request->visite_poste_id]);
        }
        else if($request->fait_marquant_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.fait_marquant_id = ?", [$request->fait_marquant_id]);
        }
        else if($request->juridique_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.juridique_id = ?", [$request->juridique_id]);
        }
        else if($request->employe_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.employe_id = ?", [$request->employe_id]);
        }
        else if($request->user_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.user_id = ?", [$request->user_id]);
        }
        else if($request->deduction_id){
            $piece_jointes = DB::select( "SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.deduction_id = ?", [$request->deduction_id]);
        }
        else if($request->paie_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.paie_id = ?", [$request->paie_id]);
        }
        else if($request->avance_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.avance_id = ?", [$request->avance_id]);
        }
        else if($request->part_variable_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.part_variable_id = ?", [$request->part_variable_id]);
        }
        else if ($request->service24_id) {
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.service24_id =?", [$request->service24_id]);
        }
        else if ($request->reclamation_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.reclamation_id =? and pj.created_at >= '2025-02-10 00:00:00'", [$request->reclamation_id]);
        }
        else if($request->satisfaction_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.satisfaction_id =?", [$request->satisfaction_id]);
        }
        else if($request->planning_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.planning_id =?", [$request->planning_id]);
        }
        else if($request->dotation_id){
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.dotation_id =?", [$request->dotation_id]);
        }
        else if ($request->recrutement_id) {
            $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
                FROM piece_jointes pj
                WHERE pj.recrutement_id =?", [$request->recrutement_id]);
        }
        if(isset($piece_jointes))
            return response()->json($piece_jointes);
        else 
            return response(["error" => "EACCES"]);
    }
}
