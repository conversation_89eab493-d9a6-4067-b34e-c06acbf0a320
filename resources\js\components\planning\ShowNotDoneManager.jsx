import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import useToken from '../util/useToken'
import axios from 'axios'
import ShowHeader from '../view/ShowHeader'
import LoadingPage from '../loading/LoadingPage'

export default function ShowNotDoneManager({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const [isLoading, toggleLoading] = useState(true)
    const [sites, setSites] = useState()
    const [manager, setManager] = useState()
    const locationSearch = new URLSearchParams(useLocation().search); 
    const datePlanning = locationSearch.get('date_planning') ? moment(locationSearch.get('date_planning')) : moment()
    const params = useParams()

    const updateData = () => { 
        // "?date_planning=" + datePlanning.format('YYYY-MM')        
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/planning/show_not_done/' + (currentId ? currentId : params.id) + "?" + locationSearch , useToken())
        .then((res) => {
            if (isMounted) {
                if(!res.data)
                    setCurrentId()
                else{
                    setManager(res.data.manager)
                    setSites(res.data.sites)
                }
            }
            toggleLoading(false)
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
    }
    useEffect(() => {
        updateData()
    },[currentId])

    return (
        <div>
            {
                isLoading ?
                    <LoadingPage />
                :
                    <div>
                        <ShowHeader size={size} label="Planning non fait" id={currentId} closeDetail={() => setCurrentId()} />
                        <div className="card-container">
                            <h3><div>{sites.length +" "} Planning non fait</div></h3>
                            <p style={{ whiteSpace: "pre-line" }}>
                                Manager: <span className='text'>{manager.name + " <" + manager.email + ">"}</span><br/>
                            </p>
                            <p style={{ whiteSpace: "pre-line" }}>
                                Agence: <span className='text'>{manager.agence}</span><br/>
                            </p>
                            {
                                manager.flotte &&
                                <p style={{ whiteSpace: "pre-line" }}>
                                    Flotte: <span className='text'>{manager.flotte}</span><br/>
                                </p>
                            }
                            <div className="tab-container">
                                <div className="tab-list">
                                    {sites && <div className='active'>Site</div>}
                                </div>
                                <div className="tab-content">
                                    {
                                        sites.map((site, index) => {
                                            return (
                                                <div key={index} className='card-container'>
                                                    <span>
                                                        <span className="text secondary">{site.nom}</span><br/>
                                                        <span className="text secondary">{site.adresse}</span><br/>
                                                    </span>
                                                </div>
                                            )
                                        })
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
            }
        </div>
    )
}
