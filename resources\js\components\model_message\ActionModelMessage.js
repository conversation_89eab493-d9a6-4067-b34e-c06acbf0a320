import React from 'react'
import { <PERSON> } from 'react-router-dom'

export default function ActionModelMessage({auth, model}) {
    return (
        <div className='action-container'>
            { (auth.id == model.user_id  || (auth.id == 73 && model.access == "service") )&&
                <span><Link to={"/message/model/update/" + model.id}>Editer</Link></span>
            }
        </div>
  )
}
