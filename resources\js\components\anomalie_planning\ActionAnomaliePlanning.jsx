import React, { useState } from 'react'
import InputAgent from '../input/InputAgent'
import useToken from '../util/useToken'

export default function ActionAnomaliePlanning({auth, anomalies, employe, updateData}) {
    const [showAssignModal, toggleAssignModal] = useState(false)
    const [agent, setAgent] = useState()
    const [error, setError] = useState('')

    const handleAssign = () => {
        axios.post('/api/anomalie_planning/assign_all/' + employe.id, { remplacant_id: agent.id }, useToken())
        .then((res) => {
            if(res.data.success){
                toggleAssignModal(false)
                updateData()
            }
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
        })
    }

    return (
        <div>
            <div className="action-container">
                <span onClick={() => toggleAssignModal(true)}>Remplacer tout</span>
            </div>
            {
                showAssignModal &&
                <div className='modal'>
                    <div>
                        <h2>Remplacer tout</h2>
                        <InputAgent label="Agent remplaçant" value={agent} onChange={setAgent} required/>
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                            
                        }
                        <div className='form-button-container'>
                            <button type='button' className='btn-primary' onClick={handleAssign}>Confirmer</button>
                            <button type='button' className='btn' onClick={()=>toggleAssignModal(false)}>Fermer</button>
                        </div>
                    </div>
                </div>
                // <AssignerAgent closeModal={() => toggleAssignModal(false)} oldAgent={employe} agent={agent} setAgent={setAgent}/>
            }
        </div>
  )
}
