import {Google<PERSON><PERSON>,Marker,useLoadScript} from "@react-google-maps/api";
import { useMemo } from "react";

export default function Emplacement({data}) {
    const { isLoaded } = useLoadScript({
        googleMapsApiKey: process.env.MIX_GOOGLE_MAPS_API_KEY
    });

    const mapContainerStyle = {
        width: "100%",
        height: "400px",
        margin: "0 auto"
    };

    const center = useMemo(
        () => ({
            lat: data.latitude ? data.latitude : -18.933333,
            lng: data.longitude ? data.longitude : 47.516667
        }),
        [data.latitude, data.longitude]
    );

    if(!isLoaded) return <div>chargement...</div>
    return (
        <div style={mapContainerStyle}>
            <GoogleMap
                zoom={(data.latitude && data.longitude) ? 18 : 6}
                center={center}
                mapContainerStyle={{
                    width: "100%",
                    height: "100%",
                }}
            >
                {data.latitude && data.longitude ? (
                    <Marker
                        position={{ lat: data.latitude, lng: data.longitude }}
                    />
                ) : null}
            </GoogleMap>
        </div>
    )
}