import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { Outlet } from "react-router-dom";

import './navbar.css'

import MailSidebar from './sidebar/MailSidebar';
import MailSidebarModal from './sidebar/MailSidebarModal';
import useToken from '../util/useToken';
import ResetPassword from '../login/ResetPassword';
import NavBar from './NavBar';

export default function MailRoute({auth, setAuth}) {
    const [showSidebar, toggleSidebar] = useState(false)

    const handleDeconnect = () => {
        axios.post('/api/logout', {}, useToken())
        .then((res) => {
            localStorage.removeItem('token')
            window.location.reload()
        })
    }

    const getWindowSize = () => {
        const {innerWidth} = window;
        return (innerWidth > 1200 ? "lg" : innerWidth > 900 ? "md" : "sm");
    }

    const [size, setSize] = useState(getWindowSize())   

    useEffect(() => {
        function handleWindowResize() {
          setSize(getWindowSize());
        }
    
        window.addEventListener('resize', handleWindowResize);
    
        return () => {
          window.removeEventListener('resize', handleWindowResize);
        }

    }, [size])

    return (
        <div>
            <NavBar auth={auth} toggleSidebar={toggleSidebar} size={size}/>
            {
                ["xg", "lg"].includes(size) ? 
                    <MailSidebar auth={auth} setAuth={setAuth} disconnectUser={handleDeconnect}/>
                : showSidebar ?
                    <MailSidebarModal auth={auth} setAuth={setAuth} closeSidebar={() => toggleSidebar(false)} disconnectUser={handleDeconnect}/>
                : 
                    <></>                
            }
            <div style={{width: '100%', paddingLeft: (["xg", "lg"].includes(size) ? "280px" : "0px")}}>
                {
                    auth.must_change_password ?
                        <ResetPassword auth={auth}/>
                    :
                        <Outlet/>
                }
            </div>
        </div>
    );
}