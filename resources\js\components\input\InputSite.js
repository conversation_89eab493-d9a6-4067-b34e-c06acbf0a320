import axios from 'axios'
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useToken from '../util/useToken';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';

export default function InputSite({value, label, actionUrl, onChange, required, hideInput, closeModal, disabled, useLink, withoutDelete}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [modalOpen, toggleModal] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [sites, setSites] = useState([])
    const [searchValue, setSearchValue] = useState("")

    const handleSelectSite = (st) => {
        if(useLink) {
            let params = new URLSearchParams(location.search)
            params.set("site_id", st.id)
            navigate(location.pathname + "?" + params)
        }
        toggleModal(false)
        onChange(st)
        if(closeModal) closeModal()
    }

    const handleCloseModal = () => {
        toggleModal(false)
        if(closeModal) closeModal()
    }
    
    const handleSearch = (initial, e) => {
        if(e) e.preventDefault()
        const params = new URLSearchParams()
        if(initial){
            setDataLoaded(true)
            setSites([])
            params.set("offset", 0)
        }
        else
            params.set("offset", sites.length)
        if(withoutDelete)
            params.set("without_soft_delete", 1)
        params.set("value", searchValue.replace('+', '%2B'))
        axios.get(( actionUrl ??'/api/site/search') + '?' +params, useToken())
        .then((res) => {
            if(res.data){
                if(initial)
                    setSites(res.data.sites)
                else {
                    const list = sites.slice().concat(res.data.sites)
                    setSites(list)
                }
                setDataLoaded(res.data.sites.length < 30)
            }
        })
    }
    
    const fetchMoreData = () => {
        setTimeout(() => {
            handleSearch()
        }, 300);
    }

    useEffect(() => {
        handleSearch(true)
    }, [])

    return <div>
        {
            !hideInput &&
            <div className='input-container'>
                <label>{label ?? "Site"} {required && <span className='danger'>*</span>}</label>
                <input
                    type="text" 
                    value={value ? value.nom : ''}
                    readOnly
                    disabled={disabled}
                    onClick={() => {toggleModal(true)}}
                    />
            </div>
        }
        {
            (hideInput || modalOpen) &&
            <div className='modal'>
                <div>
                    <h2>Site</h2>
                    <div className='search-container'>
                        <input type="text" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} placeholder="Nom du site"/>
                        <button onClick={(e) => handleSearch(true, e)}>Rechercher</button>
                    </div>
                    <div id="scrollableList"> 
                        <InfiniteScroll
                            dataLength={sites.length}
                            next={fetchMoreData}
                            hasMore={!allDataLoaded}
                            loader={<LoadingPage/>}
                            scrollableTarget="scrollableList"
                        >
                            <div className='list-container'>
                                <ul>
                                    {
                                        sites.map(st => {
                                            return <li className={(st.soft_delete && st.soft_delete == 1) ?'danger': ''} key={st.id} onClick={() => handleSelectSite(st)}>
                                                {st.nom}<br/>
                                                <span className='secondary'>{st.adresse ? st.adresse : " "}</span>
                                            </li>
                                        })
                                    }
                                </ul>
                            </div>
                        </InfiniteScroll>
                    </div>  
                    <div className='form-button-container'>
                        <button onClick={handleCloseModal}>Annuler</button>
                    </div>
                </div>
            </div>
        }
    </div>;
}