<?php

namespace App\Http\Controllers;

use App\Models\PlainteAgent;
use App\Models\SuiviJuridique;
use App\Models\Agence;
use App\Models\Site;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PlainteAgentController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    private static $attributeNames = array(
        'reference' => 'Référence ',
        'agence_id' => "Agence",
        'police' => 'Police/Gendarme compétent',
        'agent' => 'Agent(s) concerné',
        'fait' => 'Rappelle des faits',
        'debiteur' => "Débiteur",
        'contrat' => "Contrat",
        'facture' => "Facture(s)",
        'montant' => "Montant",
        'suivi' => 'Chose faite'
    );

    protected static function search(Request $request){
        $searchArray = [];
        
        if($request->id)
            $searchArray[] = "p.id = '". $request->id ."'";
        else {
            if($request->reference)
                $searchArray[] = "p.reference like '%". $request->reference ."%'";
            if($request->agence_id)
                $searchArray[] = "p.agence_id = ". $request->agence_id;
            if($request->agent)
                $searchArray[] = "p.agent like '%". $request->agent ."%'";
            if($request->site_id)
                $searchArray[] = "p.site_id like '%". $request->site_id ."%'";
            if($request->police)
                $searchArray[] = "p.police like '%". $request->police ."%'";
            if($request->fait)
                $searchArray[] = "p.fait like '%". $request->fait ."%'";
            if($request->status)
                $searchArray[] = "p.status = '". $request->status ."'";
            if($request->created_at)
                $searchArray[] = " p.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "p.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " p.user_id = " . $request->user_id . " ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by p.updated_at desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by p.updated_at desc limit ". $request->offset . ", 30";

        return compact('query_where', 'query_and');
    }

    public static function index(Request $request){
        $search = self::search($request);
        $juridiques = DB::select("SELECT p.id, p.reference, 
            p.agent, p.fait, p.status, p.created_at, stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site'
            FROM plainte_agents p
            LEFT JOIN `status` stat on stat.name = p.status
            LEFT JOIN sites st on st.idsite = p.site_id
            ". $search["query_where"]
            , []);
        return response(compact('juridiques'));
    }

    public static function show($id) {
        $juridique =  DB::select("SELECT p.id, p.reference, 
            p.police, p.agent, p.fait, p.status, p.created_at, p.fait_id,
            stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site',
            (SELECT COUNT(*) FROM piece_jointes pj WHERE pj.juridique_id = p.id) as nb_pj,
            p.user_id, u.name as 'user_nom', u.email as 'user_email', agc.nom as 'agence'
            FROM plainte_agents p
            LEFT JOIN agences agc on agc.id = p.agence_id
            LEFT JOIN sites st on st.idsite = p.site_id
            LEFT JOIN `status` stat on stat.name = p.status
            LEFT JOIN users u on u.id = p.user_id
            WHERE p.id = ?"
            , [$id])[0];
        return response()->json($juridique);
    }

    public static function detail($id) {
        $juridique = PlainteAgent::find($id);
        if($juridique) {
            $juridique->agence = Agence::find($juridique->agence_id);
            $juridique->site = Site::select('idsite', 'nom')->where('idsite', $juridique->site_id)->first();
        }
        return response()->json($juridique);
    }

    protected static function validateAndSetJuridique($request, $juridique){
        $auth = $request->user();
        $juridique->user_id = $auth->id;
        $juridique->reference = $request->reference;
        $juridique->agence_id = $request->agence_id;
        $juridique->status = "traite";
        if($juridique->id){
            $validator = Validator::make($request->all(), [
                'reference' => 'required|unique:juridiques,reference,' . $juridique->id,
                'agence_id' => 'required',
            ]);
        }
        else {
            $validator = Validator::make($request->all(), [
                'reference' => 'required|unique:juridiques',
                'agence_id' => 'required',
                'suivi' => 'required',
            ]);
        }
        if ($validator->fails()){
            return $validator->setAttributeNames(self::$attributeNames);
        }
        $validator = Validator::make($request->all(), [
            'site_id' => 'required',
            'agent' => 'required',
            'police' => 'required',
            'fait' => 'required'
        ]);
        $juridique->site_id = $request->site_id;
        $juridique->agent = $request->agent;
        $juridique->police = $request->police;
        $juridique->fait = $request->fait;
        $juridique->fait_id = $request->fait_id;

        return $validator->setAttributeNames(self::$attributeNames);
    }

    public static function store(Request $request){
        if(in_array($request->user()->role, ['juridique'])){
            $juridique = new PlainteAgent();
            $validator = self::validateAndSetJuridique($request, $juridique);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $juridique->created_at = new \DateTime();
            $juridique->updated_at = new \DateTime();
            
            if($juridique->save()){
                $suivi = new SuiviJuridique();
                $suivi->plainte_id = $juridique->id;
                $suivi->commentaire = $request->suivi;
                $suivi->user_id = $request->user()->id;
                $suivi->created_at = new \DateTime();
                $suivi->updated_at = new \DateTime();
                $suivi->save();
                HistoriqueController::new_juridique($request, $juridique);
                return response(["success" => "Dossier bien enregistré"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public static function edit($id, Request $request){
        if(in_array($request->user()->role, ['juridique'])){
            $juridique = PlainteAgent::find($id);
            $validator = self::validateAndSetJuridique($request, $juridique);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $juridique->updated_at = new \DateTime();
            
            if($juridique->save()){
                HistoriqueController::update_juridique($request, $juridique, "Dossier modifié");
                return response(["success" => "Dossier bien enregistré"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public static function save_done($id, Request $request){
        $juridique = Juridique::find($id);
        if($juridique->status == "traite" && in_array($request->user()->role, ['juridique'])){
            $juridique->status = "done";
            $juridique->save();
            $suivi = new SuiviJuridique();
            $suivi->juridique_id = $juridique->id;
            $suivi->commentaire = $request->note;
            $suivi->user_id = $request->user()->id;
            $suivi->created_at = new \Datetime();
            $suivi->updated_at = new \Datetime();
            $suivi->save();
            HistoriqueController::action_juridique($request, "Dossier terminé", $id);
            return response(["success" => "Dossier terminé"]);
        }
        return response(["error" => "EACCES"]);
    }

    public static function cancel($id, Request $request){
        $juridique = Juridique::find($id);
        if($juridique->status == "traite" && in_array($request->user()->role, ['juridique'])){
            $juridique->status = "draft";
            $juridique->save();
            $suivi = new SuiviJuridique();
            $suivi->juridique_id = $juridique->id;
            $suivi->commentaire = $request->note;
            $suivi->user_id = $request->user()->id;
            $suivi->created_at = new \Datetime();
            $suivi->updated_at = new \Datetime();
            $suivi->save();
            HistoriqueController::action_juridique($request, "Dossier annulé", $id);
            return response(["success" => "Dossier annulé"]);
        }
        return response(["error" => "EACCES"]);
    }
}
