<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Flotte;
use App\Models\User;
use App\Models\Notification;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class FlotteController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function show($id){
        $flotte = DB::select("SELECT flottes.id, flottes.site_id, flottes.user_id, flottes.objet, flottes.commentaire, flottes.status, flottes.note_id, flottes.created_at,
            stat.description as 'status_description', stat.color as 'status_color', 
            st.nom as 'site', us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email'
            FROM flottes
            LEFT JOIN sites st on st.idsite = flottes.site_id
            LEFT JOIN users us on us.id = flottes.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN `status` stat on stat.name = flottes.status
            where flottes.id = ?", [$id])[0];
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj
            WHERE pj.flotte_id = ?
            order by pj.created_at desc", [$id]);
        $flotte->nb_pj = count($pieces);
        return response()->json($flotte);
    }

    public function detail($id){
        $flottes = DB::select("SELECT flottes.id, flottes.user_id, flottes.objet, flottes.commentaire, flottes.status, flottes.note_id, flottes.created_at,
            stat.description as 'status_description', stat.color as 'status_color',
            st.nom as 'site', us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email'
            FROM flottes
            LEFT JOIN sites st on st.idsite = flottes.site_id
            LEFT JOIN users us on us.id = flottes.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN `status` stat on stat.name = flottes.status
            where flottes.id = ?", [$id])[0];
        $pieces = DB::select("SELECT pj.id, pj.nature, pj.path, pj.user_id, pj.created_at, pj.updated_at
            FROM piece_jointes pj
            WHERE pj.flotte_id = ?
            order by pj.created_at desc", [$id]);
        $historiques = DB::select("SELECT h.id, h.objet, h.note, h.detail, h.created_at, h.user
            FROM historiques h
            where h.flotte_id = ?
            order by h.created_at desc
            limit 10", [$id]);
        return response()->json(compact('flottes', 'pieces', 'historiques'));
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = "flottes.id = '". $request->id ."'";
        else {
            if($request->status)
                $searchArray[] = "flottes.status = '". $request->status ."'";
            if($request->created_at)
                $searchArray[] = " flottes.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "flottes.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " flottes.user_id = " . $request->user_id . " ";
            if($request->site_id)
                $searchArray[] = " flottes.site_id = " . $request->site_id . " ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by flottes.id desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by flottes.id desc limit ". $request->offset . ", 30";
        return compact('query_where', 'query_and');
    }

    public function index(Request $request){
        $search = $this->search($request);
        if(in_array($request->user()->role, ['tech','validateur','access','daf'])){
            $flottes = DB::select("SELECT flottes.id, flottes.user_id, flottes.objet, flottes.commentaire, flottes.status, flottes.note_id, flottes.created_at,
                stat.description as 'status_description', stat.color as 'status_color',
                (SELECT COUNT(*) FROM piece_jointes pj WHERE pj.flotte_id = flottes.id) as nb_pj,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email'
                FROM flottes
                LEFT JOIN sites st on st.idsite = flottes.site_id
                LEFT JOIN users us on us.id = flottes.user_id
                LEFT JOIN `status` stat on stat.name = flottes.status
                " . $search['query_where'], []);
        }
        else if(in_array($request->user()->role, ['superviseur','resp_sup', 'resp_op','simple'])){
            $flottes = DB::select("SELECT flottes.id, flottes.user_id, flottes.objet, flottes.commentaire, flottes.status, flottes.note_id, flottes.created_at,
                stat.description as 'status_description', stat.color as 'status_color',
                (SELECT COUNT(*) FROM piece_jointes pj WHERE pj.flotte_id = flottes.id) as nb_pj,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email'
                FROM flottes
                LEFT JOIN sites st on st.idsite = flottes.site_id
                LEFT JOIN users us on us.id = flottes.user_id
                LEFT JOIN `status` stat on stat.name = flottes.status
                where flottes.user_id = ? " . $search['query_and']
                , [$request->user()->id]);
        }
        else 
            return response(["error" => "EACCES"]);
        return response(compact('flottes'));
    }

    protected function validateAndSetFlotte($request, $flotte){
        $auth = $request->user();
        if($request->me == 1){
            $validator = Validator::make($request->all(), [
                'objet' => 'required',
                'commentaire' => 'required'
            ]);
            $flotte->personal = true;
        }
        else if(in_array($auth->role, ['superviseur','resp_sup', 'resp_op', 'tech'])){
            $validator = Validator::make($request->all(), [
                'site_id' => 'required',
                'objet' => 'required',
                'commentaire' => 'required'
            ]);
            $flotte->personal = false;
        }
        $flotte->user_id = $auth->id;
        return $validator;
    }

    public function store(Request $request){
        $auth = $request->user();
        if(/*in_array($auth->role, ["superviseur","resp_sup"])*/ $auth){
            $flotte = new Flotte();

            $validator = $this->validateAndSetFlotte($request, $flotte);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $flotte->site_id = $request->site_id;
            $flotte->objet = $request->objet;
            $flotte->commentaire = $request->commentaire;
            $flotte->user_id = $auth->id;
            $flotte->status = "demande";           

            $flotte->created_at = new \DateTime();
            $flotte->updated_at = new \DateTime();

            if($flotte->save()){
                HistoriqueController::new_flotte($request, $flotte->id);
                if(in_array($auth->role, ["superviseur","resp_sup", "resp_op"])){
                    $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                        from users us
                        left join users ur on ur.id=us.real_email_id 
                        where us.role = 'tech'", []);
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                    //MailController::flotte($request, $flotte->id, "Demande de Flotte", $emails);
                }
                return response(["success" => "Flotte bien envoyée", "id" => $flotte->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done(Request $request, $id){
        $flotte = Flotte::find($id);
        $old_flotte = clone $flotte;
        if($request->user()->role == "tech" && in_array($flotte->status, ["demande","traite"])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            
            $flotte->status = "done";
            $flotte->updated_at = new \DateTime();

            if($flotte->save()){
                HistoriqueController::action_flotte($request, "Traitement flotte terminé", $id);
                return response(["success" => "Flotte bien envoyée", "id" => $flotte->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function edit(Request $request, $id){
        $flotte = Flotte::find($id);
        $old_flotte = clone $flotte;
        $auth = $request->user();
        if($auth->role == "tech" && $flotte->status == "traite"){
            $validator = $this->validateAndSetFlotte($request, $flotte);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
                
            $flotte->updated_at = new \DateTime();

            if($flotte->save()){
                HistoriqueController::update_flotte($request, $old_flotte, "Flotte modifié");
                return response(["success" => "Flotte modifié", "id" => $flotte->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_flotte(Request $request, $id){
        $flotte = Flotte::find($id);
        if(
            ($request->user()->id == $flotte->user_id && in_array($request->user()->role, ['superviseur','resp_sup','resp_op']) && in_array($flotte->status, ['demande'])) || 
            ($request->user()->role == 'tech' && in_array($flotte->status, ['demande']))
        ){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $flotte->status = 'draft';
            $flotte->updated_at = new \DateTime();

            $flotte->note_id = HistoriqueController::action_flotte($request, "Flotte annulé", $id);
            if($flotte->save()){
                if(in_array($request->user()->role, ["superviseur","resp_sup","resp_op"])){
                    $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                        from users us
                        left join users ur on ur.id=us.real_email_id 
                        where us.role = 'tech'", []);
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                    //MailController::flotte($request, $flotte->id, "Demande de flotte annulé", $emails);
                }
                return response(["success" => "Flotte annulé", "id" => $flotte->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function request_validation(Request $request, $id){
        $flotte = Flotte::find($id);
        if($request->user()->role == 'tech' && in_array($flotte->status, ['demande','traite'])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $flotte->status = 'validation';
            $flotte->updated_at = new \DateTime();

            $flotte->note_id = HistoriqueController::action_flotte($request, "Requête de validation", $id);
            if($flotte->save()){
                return response(["success" => "Demande de validation envoyé", "id" => $flotte->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function reply_validation(Request $request, $id){
        $flotte = Flotte::find($id);
        if($request->user()->role == 'validateur' && $flotte->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $flotte->status = 'traite';
            $flotte->updated_at = new \DateTime();

            $flotte->note_id = HistoriqueController::action_flotte($request, "Réponse à la demande", $id);
            if($flotte->save()){
                $users = DB::select("SELECT id from users where role = 'tech'", []);
                foreach ($users as $u) {
                    Notification::create([
                        'historique_id' => $flotte->note_id,
                        'receiver_id' => $u->id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                return response(["success" => "Réponse de la demande envoyé", "id" => $flotte->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_validation(Request $request, $id){
        $flotte = Flotte::find($id);
        if($request->user()->role == 'tech' && $flotte->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $flotte->status = 'traite';
            $flotte->updated_at = new \DateTime();

            $flotte->note_id = HistoriqueController::action_flotte($request, "Demande de validation annulé", $id);
            if($flotte->save()){
                return response(["success" => "Demande de validation annulé", "id" => $flotte->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $flotte = Flotte::find($id);
        $old_flotte = clone $flotte;
        if($request->user()->id == $flotte->user_id && $flotte->status == "draft"){
            $validator = $this->validateAndSetFlotte($request, $flotte);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            if($request->user()->role == "tech")
                $flotte->status = "traite";
            else
                $flotte->status = "demande";
            $flotte->updated_at = new \DateTime();

            if($flotte->save()){
                HistoriqueController::update_flotte($request, $old_flotte, "Renvoie de la demande");
                return response(["success" => "Renvoie de la demande", "id" => $flotte->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function note(Request $request, $id){
        $flotte = Flotte::find($id);
        if(in_array($request->user()->role, ['tech']) && in_array($flotte->status, ['demande'])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            if(in_array($request->user()->role, ['tech']))
                $flotte->status = "traite";
            $flotte->updated_at = new \DateTime();
            $flotte->note_id = HistoriqueController::action_flotte($request, "Note", $id);
            if($flotte->save())
                return response(["success" => "Note ajouté avec succès", "id" => $flotte->id]);
        }
        return response(["error" => "EACCES"]);
    }


    public function last_flotte($id){
        $flottes = DB::select("SELECT flottes.id, flottes.user_id, flottes.objet, flottes.commentaire, flottes.status, flottes.note_id, flottes.created_at,
        stat.description as 'status_description', stat.color as 'status_color',
        st.nom as 'site', us.name as 'user_nom', us.email as 'user_email'
        FROM flottes
        LEFT JOIN sites st on st.idsite = flottes.site_id
        LEFT JOIN users us on us.id = flottes.user_id
        LEFT JOIN `status` stat on stat.name = flottes.status
        where st.idsite = ? and flottes.status != 'draft'
        order by flottes.created_at DESC LIMIT 3", [$id]);
        return response($flottes);
    }
}
