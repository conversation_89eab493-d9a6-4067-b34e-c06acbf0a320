import React from 'react';

export default function ModalChoiceAnomalie({onChange, closeModal}) {
    const types = [
        {name: "Effectif et pointage", value: 'anomalie_pointage_effectif'},
        {name: "Planning et pointage", value: 'anomalie_planning'},
        {name: "Planning et effectif", value: 'anomalie_planning_effectif'},
    ]
    const handleSelectType = (t) => {
        onChange(t.value)
        closeModal()
    }

    return <div className='modal'>
        <div>
            <h2>Type de comparatif</h2>
            <div className='list-container'>
                <ul>
                    {
                        types.map(t => {
                            return <li key={t.value} onClick={() => handleSelectType(t)}>
                                {t.name}<br/>
                            </li>
                        })
                    }
                </ul>
            </div>
            <div className='form-button-container'>
                <button onClick={closeModal}>Annuler</button>
            </div>
        </div>
    </div>
}