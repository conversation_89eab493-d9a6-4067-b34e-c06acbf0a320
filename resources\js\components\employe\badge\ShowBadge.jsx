import React, { useRef } from 'react'
import BadgeItem from './BadgeItem';
import { CgClose } from 'react-icons/cg'
import './badge.css';

export default function ShowBadge({ employes, setCurrentId, size, auth}) {
    const componentPDF = useRef()
    
    return <>{
        ["rh", "resp_rh"].includes(auth.role) &&
        <div>
            {employes && employes.length > 0 &&
                <div className='space-between' style={{marginBottom:20, }}>
                    <CgClose className='secondary' size={30} onClick={() => setCurrentId()} />
                </div>
            }
            <div className='badge-container'>
                <div ref={componentPDF} className='container-badge-profil'>
                    {employes &&
                        employes.map((emp) => (
                            <div key={emp.id} className='badge'>
                                <BadgeItem employe={emp} />
                            </div>
                        ))
                    }
                </div>
            </div>
        </div>
    }</>
}
