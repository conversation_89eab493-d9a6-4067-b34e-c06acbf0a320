import axios from 'axios'
import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import InputText from '../input/InputText';
import InputDate from '../input/InputDate';
import moment from 'moment';

export default function ArchiveRecrutementModal({closeModal, updateData, recrutement}) {
    const [observation, setObservation] = useState("");
    const [dateArchive, setDateArchive] = useState(null);
    const [submitDisabled, setSubmitDisabled] = useState(true);

    const handleOk = () => {
        const data = {
            observation: observation,
            date_archive: moment(dateArchive).format('YYYY-MM-DD')
        }
        axios.post("/api/recrutement/soft_delete/" + recrutement.id, data, useToken())
        .then(res => {
            updateData()
        })
    }
    
    useEffect(() => {
        setSubmitDisabled(!(observation && dateArchive))
    }, [observation, dateArchive]);
    
    return <div className='modal'>
        <div>
            <h2>Mise en archive</h2>
            <InputDate
                required
                label="Date d'archive"
                value={dateArchive} 
                onChange={setDateArchive}/>
            <InputText
                required
                type="text"
                label="Observation"
                value={observation}
                onChange={setObservation}/>
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Enregistrer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>;
}