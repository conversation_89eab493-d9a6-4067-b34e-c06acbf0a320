import { useRef, useState } from 'react';
import InfoModal from '../modal/InfoModal';
import LoadingScreen from '../loading/LoadingScreen';
import { AiOutlineDelete } from "react-icons/ai";

export default function InputSingleFile({ file, setFile }) {
    const fileInputRef = useRef(null);
    const [showInfo, toggleInfo] = useState(false);
    const [showLoading, toggleLoading] = useState(false);

    const handleFileInputClick = () => {
        fileInputRef.current.click();
    };

    const uploadFile = (event) => {
        toggleLoading(true);
        const selectedFile = event.target.files[0];
        if (selectedFile && selectedFile.size < 50 * 1024 * 1024) {
            setFile(selectedFile);
        } else {
            toggleInfo(true);
        }
        setTimeout(() => toggleLoading(false), 200);
    };

    const deleteFile = () => {
        setFile(null);
    };

    return (
        <div className='input-container'>
            {showInfo && <InfoModal msg="Fichier trop volumineux" closeModal={() => toggleInfo(false)} />}
            {showLoading && <LoadingScreen />}
            <input 
                hidden 
                type="file" 
                name="file" 
                ref={fileInputRef} 
                onChange={uploadFile} 
            />
            {
                !file ? (
                    <div style={{ paddingTop: '10px', paddingBottom: '10px' }}>
                        <div className='action-container'>
                            <span onClick={handleFileInputClick}>Ajouter une pièce jointe</span>
                        </div>
                    </div>
                ) : (
                    <div className='action-container'>
                        <div className='pj-file space-between'>
                            <span className='secondary'>{file.name}</span>
                            <span>
                                <AiOutlineDelete onClick={deleteFile} />
                            </span>
                        </div>
                    </div>
                )
            }
        </div>
    );
}
