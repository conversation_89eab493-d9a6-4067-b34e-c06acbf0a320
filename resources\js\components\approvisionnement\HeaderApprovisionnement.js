import moment from 'moment';
import React from 'react';
import useNumberUtil from '../util/useNumberUtil';

export default function HeaderApprovisionnement({auth, data}) {
    return <div>
        <h3>
            {"DA-" + moment(data.created_at).format("YYYY") + "/" + ("00000" + data.reference).slice(-6) } 
        </h3>
        {
            data.article_id ?
            <>
                <div>
                    {data.article} <span className='text'>
                    {
                        " [" +
                        (
                            data.price_only ? 
                                data.prix + " Ar"
                            : data.prix ? 
                                data.prix + " x " + data.quantite + " " + data.unite + " : " + useNumberUtil(data.quantite * data.prix) + ""
                            :
                                data.quantite + " " + data.unite
                        )
                        + ( 
                            (data.cout && data.prix && (data.cout != data.prix*data.quantite)) ? (" -> "  + data.cout + " Ar") : ""
                        )
                        + "]"
                    } </span>
                </div>
                {
                    data.total ?
                        <>Total: <span className='text'>{useNumberUtil(data.total)}</span></>
                    :
                        <>Service: <span className='text'>{data.service}</span></>
                }
                <div className='card-footer'>
                    <span>
                        <span>Demandeur : </span>
                        <span className='text'>{data.user_nom} {" <" + data.user_email + ">"}</span>
                    </span>
                    <span className='secondary'>{moment(data.created_at).from(auth.datetime)}</span>
                </div>
            </>
            :
            <>
                <div>
                    Motif: <span className='text'>{data.objet}</span>
                </div>
                {
                    data.total ?
                        <>Total: <span className='text'>{useNumberUtil(data.total)}</span></>
                    :
                        <>Service: <span className='text'>{data.service}</span></>
                }
                <div className='card-footer'>
                    <span>
                        <span>Demandeur : </span>
                        <span className='text'>{data.user_nom} {" <" + data.user_email + ">"}</span>
                    </span>
                    <span className='secondary'>{moment(data.created_at).from(auth.datetime)}</span>
                </div>
            </>
        }
    </div>
}