import {useEffect,useState} from 'react'
import {Page,Text,Image,View,Document,StyleSheet} from '@react-pdf/renderer'
import Logo1 from './dirickx_madagascar.png'
import Logo2 from './dirickx_guard.jpg'
import moment from 'moment'
import matricule from '../../util/matricule'

const getMatricule = matricule;

export default function PdfAvertAutre({sanction,motif,avertissementGrade,genre,societeSt}) {
    const [societe, setSociete] = useState("")
    const [rcs, setRcs] = useState("")
    const [stat, setStat] = useState("")
    const [adresse, setAdresse] = useState("")
    const [code, setCode] = useState("")
    const [Logo, setLogo] = useState("")
    const [fonction, setFonction] = useState("")
    const [matricule, setMatricule] = useState("")

    useEffect(() => {
        if(sanction.societe_id == 2){
            setSociete('« Sûreté Océan Indien Tamatave Sarl »')
            setRcs('Toamasina 20 11B00100 –')
            setStat('47597  31 2011 0 00658 – NIF 3000538020')
            setAdresse('Villa Herihasina I Derrière cite VohitsaraBetainomby Toamasina –')
            setCode('Tamatave 501 –')
            setLogo(Logo1)
        }else if(societeSt == "SOIT"){
            setSociete('« Sûreté Océan Indien Tamatave Sarl »')
            setRcs('Toamasina 20 11B00100 –')
            setStat('47597  31 2011 0 00658 – NIF 3000538020')
            setAdresse('Villa Herihasina I Derrière cite VohitsaraBetainomby Toamasina –')
            setCode('Tamatave 501 –')
            setLogo(Logo1)
        }else{
            setSociete('DIRICKX GUARD SARL')
            setRcs('Antananarivo 2015 B 00494')
            setStat('80105 11 2015 0 10514 NIF : 4001991253')
            setAdresse('Adresse : Lot 23 Talatamaty')
            setLogo(Logo2)
        }

        setMatricule(getMatricule(sanction))

        switch(sanction.fonction_id) {
            case 1:
                return setFonction("Agent de sécurité");
            case 2:
                return setFonction("Chef de poste");
            case 3:
                return setFonction("Conducteur de chien");
            case 4:
                return setFonction("Superviseur de site");
            case 5:
                return setFonction("Intervention");
            case 6:
                return setFonction("Motard");
            case 7:
                return setFonction("Contrôleur");
            case 8:
                return setFonction("Chauffeur");
            case 9:
                return setFonction("Femme de ménage");
            default:
                return setFonction("");
        }

    }, [])

    const styles = StyleSheet.create({
        body: {
            display: 'flex',
            flexDirection: 'row',         
            justifyContent: 'space-between'   
        },
        bodyLeft: {
            width: '50%',
            padding: 10,
        },
        bodyRight: {
            width: '50%',
            padding: 10,
            borderLeft: "1pt solid black"
        },
        image: {
            margin: 15,
            width: '160px'
        },
        header: {
            marginHorizontal: 15,
            marginVertical: 0,
            fontSize: 8,
            fontWeight: 700,
            fontFamily: 'Times-BoldItalic',
        },
        date: {
            textTransform: 'capitalize',
            fontFamily: 'Times-Roman',
            textAlign: 'center',
            fontSize: 9,
            paddingVertical: 10,
        },
        employe: {
            fontFamily: 'Times-Roman',
            textAlign: 'center',
            fontSize: 9,
            paddingVertical: 3,
        },
        underline: {
            textDecoration: 'underline',
            textDecorationThickness: 1,
            fontFamily: 'Times-Roman',
            fontSize: 9,
        },
        objet: {
            fontFamily: 'Times-Roman',
            textAlign: 'left',
            fontSize: 9,
            marginTop: 15,
            paddingVertical: 8,
            marginHorizontal: 15,
        },
        text: {
            marginHorizontal: 15,
            marginVertical: 3,
            lineHeight: 1.5,
            fontSize: 9,
            textAlign: 'justify',
            fontFamily: 'Times-Roman',
        },
        container: {
            paddingHorizontal: 30,
            paddingVertical: 15,
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between'
        },
        left:{
            fontFamily: 'Times-Roman',
            fontSize: 9,
            textDecoration: 'underline',
            textDecorationThickness: 1,
        },
        right: {
            fontFamily: 'Times-Roman',
            fontSize: 9,
            textDecoration: 'underline',
            textDecorationThickness: 1,
        }
    })

    return (
        <Document>
            <Page size='A4' style={styles.body} orientation="landscape">
                <View style={styles.bodyLeft}>
                    <Image style={styles.image} src={Logo} />
                    <Text style={styles.header}>{societe}</Text>
                    <Text style={styles.header}>RCS {rcs}</Text>
                    <Text style={styles.header}>STAT {stat}</Text>
                    <Text style={styles.header}>{adresse}</Text>
                    <Text style={styles.header}>{code}</Text>     

                    <Text style={styles.date}>Antananarivo, le {moment().format('DD MMMM YYYY')}</Text>
                    <Text style={styles.employe}>{genre}</Text>
                    <Text style={styles.employe}>{sanction.employe}</Text>
                    <Text style={styles.employe}>{fonction}</Text>
                    <Text style={styles.employe}>Matricule {matricule}</Text>

                    <Text style={styles.objet}><Text style={styles.underline}>Objet</Text> : {avertissementGrade}</Text>
                    
                    <Text style={styles.text}>{genre},</Text>
                    <Text style={styles.text}>
                        {motif+'.'}
                    </Text>
                    <Text style={styles.text}>
                        Votre comportement est non professionnel et nous ne pouvons pas tolérer.
                    </Text>
                    <Text style={styles.text}>
                        C’est pourquoi par la présente lettre nous vous infligeons un {avertissementGrade == "01er Avertissement" ? "premier avertissement " : (avertissementGrade == "02ème Avertissement" ? "deuxième avertissement " : (avertissementGrade == "03ème Avertissement" ? "troisième avertissement " : (avertissementGrade == "04ème Avertissement" ? "quatrième avertissement " : " ")))}  
                        conformément au règlement intérieur.
                    </Text>
                    <Text style={styles.text}>
                        Nous attirons votre attention sur le fait qu’en cas de récidive, vous encourez une sanction 
                        plus importante pouvant aller jusqu’à votre licenciement.
                    </Text>
                    <Text style={styles.text}>
                        Nous vous prions d’agréer, {genre}, l’expression de nos salutations les meilleures.
                    </Text>

                    <View style={styles.container}>
                        <Text style={styles.left}>{sanction.societe_id == 2 || sanction.societe_id == 1 ? 'L\'employé' : 'Le Stagiaire'}</Text>
                        <Text style={styles.right}>La Direction</Text>
                    </View>
                </View>
                <View style={styles.bodyRight}>
                    <Image style={styles.image} src={Logo} />
                    <Text style={styles.header}>{societe}</Text>
                    <Text style={styles.header}>RCS {rcs}</Text>
                    <Text style={styles.header}>STAT {stat}</Text>
                    <Text style={styles.header}>{adresse}</Text>
                    <Text style={styles.header}>{code}</Text>     

                    <Text style={styles.date}>Antananarivo, le {moment().format('DD MMMM YYYY')}</Text>
                    <Text style={styles.employe}>{genre}</Text>
                    <Text style={styles.employe}>{sanction.employe}</Text>
                    <Text style={styles.employe}>{fonction}</Text>
                    <Text style={styles.employe}>Matricule {matricule}</Text>

                    <Text style={styles.objet}><Text style={styles.underline}>Objet</Text> : {avertissementGrade}</Text>
                    
                    <Text style={styles.text}>{genre},</Text>
                    <Text style={styles.text}>
                        {motif+'.'}
                    </Text>
                    <Text style={styles.text}>
                        Votre comportement est non professionnel et nous ne pouvons pas tolérer.
                    </Text>
                    <Text style={styles.text}>
                        C’est pourquoi par la présente lettre nous vous infligeons un {avertissementGrade == "01er Avertissement" ? "premier avertissement " : (avertissementGrade == "02ème Avertissement" ? "deuxième avertissement " : (avertissementGrade == "03ème Avertissement" ? "troisième avertissement " : (avertissementGrade == "04ème Avertissement" ? " quatrième avertissement " : " ")))}  
                        conformément au règlement intérieur.
                    </Text>
                    <Text style={styles.text}>
                        Nous attirons votre attention sur le fait qu’en cas de récidive, vous encourez une sanction 
                        plus importante pouvant aller jusqu’à votre licenciement.
                    </Text>
                    <Text style={styles.text}>
                        Nous vous prions d’agréer, {genre}, l’expression de nos salutations les meilleures.
                    </Text>

                    <View style={styles.container}>
                        <Text style={styles.left}>{sanction.societe_id == 2 || sanction.societe_id == 1 ? 'L\'employé' : 'Le Stagiaire'}</Text>
                        <Text style={styles.right}>La Direction</Text>
                    </View>
                </View>
            </Page>
        </Document>
    )
}
