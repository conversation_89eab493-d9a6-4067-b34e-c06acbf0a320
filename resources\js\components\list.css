.checkbox-input-container{
    padding: 20px 0px;
}
.grid-modal-container{
    margin: 20px 0px 20px 0px;
    display: grid;
    grid-template-columns: auto auto auto;
    gap: 10px;
}
.card-container{
    padding: 20px 10px;
    border-top: 1px solid #ccc;
}
.card-container > .badge-container{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.line-container{
    padding: 10px;
    border-top: 1px solid #ccc;
}
.table:hover{
    background-color: #f1f1f1;
}
.row-employe{
    display: flex;
    cursor: pointer;
}
.row-employe > span, .row-employe > b{
    padding: 5px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.row-list{
    display: flex;
    cursor: pointer;
}
.row-list > span, .row-list > b{
    padding: 5px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.dual-container{
    display: flex;
}
.cell{
    width: 50%;
    min-width: 50%;
    max-width: 50%;
}
.cell-left{
    padding-right: 10px;
}
.cell-right{
    padding-left: 10px;
}
.matricule-employe{
    width: 100px;
    min-width: 100px;
    max-width: 100px;
}
.reference-juridique{
    width: 130px;
    min-width: 130px;
    max-width: 130px;
}
.sujet-juridique{
    width: 35%;
    min-width: 35%;
    max-width: 35%;
}
.nom-employe{
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 40%;
    min-width: 40%;
    max-width: 40%;
}
.formation{
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 50%;
    min-width: 50%;
    max-width: 50%; 
}
.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
}
.card-action {
    padding-top: 15px;
}
.card-grey{
    border-color: #9e9e9e;
}
.card-cyan{
    border-color: #00bcd4;
}
.card-lime{
    border-color: #c0ca33;
}
.card-green{
    border-color: #8bc34a;
}
.card-orange{
    border-color: #ff9800;
}
.card-header{
    display: flex;
    justify-items: center;
    justify-content: space-between;
    padding-top: 10px;
}
.header-action{
    padding: 0px 10px 10px 10px;
}
.action-container > span{
    display: inline-block;
    margin-right: 15px;
    text-decoration: underline;
    color: #888;
    cursor: pointer;
}
.action-container > span > a{
    color: inherit;
}
.action-container>span:first-child{
    color: #073570;
}
.article-container{
    padding: 20px 10px;
    border-top: 1px solid #ccc;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}
.anomalie-container{
    padding: 20px 10px;
    border-top: 1px solid #ccc;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}
.story-container{
    padding: 20px 10px;
    border-top: 1px solid #ccc;
}
.story-container > .objet-story {
    color: #666;
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.story-footer{
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #777;
    padding-bottom: 10px;
}
.story-note {
    color: #777;
    padding-bottom: 10px;
    font-style: italic;
    white-space: pre-line;
}
.line-cell-xs {
    width: 50px;
    min-width: 50px;
    max-width: 50px;
}
.line-cell-sm {
    width: 125px;
    min-width: 125px;
    max-width: 125px;
}
.line-cell-md {
    width: 35%;
    min-width: 35%;
    max-width: 35%;
}
.line-cell-lg {
    width: 40%;
    min-width: 40%;
    max-width: 40%;
}
.line-cell-xg {
    width: 50%;
    min-width: 50%;
    max-width: 50%;
}
.date-depart{
    display: inline-block;
    width: 120px;
    min-width: 120px;
    margin-left: 10px;
    margin-right: 10px;
}
.date-service{
    width: 140px;
    min-width: 140px;
    max-width: 140px;
}
.superviseur {
    display: inline-block;
}
.nb-jour{
    width: 50px;
    min-width: 50px;
    display: inline-block;
    text-align: center;
}
.site-employe{
    display: inline-block;
}
