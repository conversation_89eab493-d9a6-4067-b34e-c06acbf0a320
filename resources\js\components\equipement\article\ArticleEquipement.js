import React, { useEffect, useState } from 'react';
import LoadingPage from '../../loading/LoadingPage';
import useToken from '../../util/useToken';
import ActionArticle from './ActionArticle';

export default function ArticleEquipement({auth, id, updateData}) {
    const [isLoading, toggleLoading] = useState(true)
    const [articles, setArticles] = useState([])
    
    useEffect(() => {
        let isMounted = true
        axios.get("/api/equipement/article/" + id ,  useToken())
        .then((res) => {
            if(isMounted) {
                setArticles(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false};
    }, []);
    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <div>
                    {
                        articles.map((ac) => (
                            <div key={ac.article} className="article-container">
                                <span>{ac.designation}</span>
                                {
                                    ac.done ? 
                                        <span className='badge-outline'>Fait</span>
                                    :
                                    ac.done === 0 ?
                                        <span className='badge-outline'>Annulé</span>
                                    :
                                    auth.role == ac.service ?
                                        <ActionArticle auth={auth} article={ac} updateData={updateData}/>
                                    :
                                        <span className='badge-outline'>{ac.service}</span>
                                }
                            </div>
                        ))
                    }
                </div>
        }
    </>
}