import {useEffect,useState} from 'react'
import {Page,Text,Image,View,Document,StyleSheet} from '@react-pdf/renderer'
import Logo1 from './dirickx_madagascar.png'
import Logo2 from './dirickx_guard.jpg'
import moment from 'moment'
import matricule from '../../util/matricule'

const getMatricule = matricule;

export default function PdfLicenciement({sanction,motif,genre,societeSt}) {
    const [societe, setSociete] = useState("")
    const [rcs, setRcs] = useState("")
    const [stat, setStat] = useState("")
    const [adresse, setAdresse] = useState("")
    const [code, setCode] = useState("")
    const [Logo, setLogo] = useState("")
    const [fonction, setFonction] = useState("Agent de sécurité")
    const [matricule, setMatricule] = useState("")

    useEffect(() => {
        if(sanction.societe_id == 2){
            setSociete('« Sûreté Océan Indien Tamatave Sarl »')
            setRcs('Toamasina 20 11B00100 –')
            setStat('47597  31 2011 0 00658 – NIF 3000538020')
            setAdresse('Villa Herihasina I Derrière cite VohitsaraBetainomby Toamasina –')
            setCode('Tamatave 501 –')
            setLogo(Logo1)
        }else if(societeSt == "SOIT"){
            setSociete('« Sûreté Océan Indien Tamatave Sarl »')
            setRcs('Toamasina 20 11B00100 –')
            setStat('47597  31 2011 0 00658 – NIF 3000538020')
            setAdresse('Villa Herihasina I Derrière cite VohitsaraBetainomby Toamasina –')
            setCode('Tamatave 501 –')
            setLogo(Logo1)
        }else{
            setSociete('DIRICKX GUARD SARL')
            setRcs('Antananarivo 2015 B 00494')
            setStat('80105 11 2015 0 10514 NIF : 4001991253')
            setAdresse('Adresse : Lot 23 Talatamaty')
            setLogo(Logo2)
        }

        setMatricule(getMatricule(sanction))

        switch(sanction.fonction_id) {
            case 1:
                return setFonction("Agent de sécurité");
            case 2:
                return setFonction("Chef de poste");
            case 3:
                return setFonction("Conducteur de chien");
            case 4:
                return setFonction("Superviseur de site");
            case 5:
                return setFonction("Intervention");
            case 6:
                return setFonction("Motard");
            case 7:
                return setFonction("Contrôleur");
            case 8:
                return setFonction("Chauffeur");
            case 9:
                return setFonction("Femme de ménage");
            default:
                return setFonction("");
        }

    }, [])

    const styles = StyleSheet.create({
        body: {
            paddingTop: 35,
            paddingBottom: 65,
            paddingHorizontal: 60,
        },
        image: {
            margin: 15,
            width: '45%' 
        },
        header: {
            marginHorizontal: 15,
            marginVertical: 0,
            fontSize: 11,
            fontWeight: 700,
            fontFamily: 'Times-BoldItalic',
        },
        date: {
            fontFamily: 'Times-Roman',
            textAlign: 'center',
            fontSize: 12,
            paddingVertical: 10,
        },
        dateRight: {
            fontFamily: 'Times-Roman',
            textAlign: 'right',
            fontSize: 12,
            paddingVertical: 10,
            marginHorizontal: 15,
        },
        employe: {
            fontFamily: 'Times-Roman',
            textAlign: 'center',
            fontSize: 12,
            paddingVertical: 3,
        },
        underline: {
            textDecoration: 'underline',
            textDecorationThickness: 1,
            fontFamily: 'Times-Roman',
            fontSize: 12,
        },
        objet: {
            fontFamily: 'Times-Roman',
            textAlign: 'left',
            fontSize: 12,
            marginTop: 15,
            paddingVertical: 8,
            marginHorizontal: 15,
        },
        text: {
            marginHorizontal: 15,
            marginVertical: 3,
            lineHeight: 1.5,
            fontSize: 12,
            textAlign: 'justify',
            fontFamily: 'Times-Roman',
        },
        bold: {
            fontSize: 12,
            fontFamily: 'Times-Bold',
        },
        capitalize: {
            textTransform: 'capitalize',
        },
        container: {
            paddingHorizontal: 30,
            paddingVertical: 15,
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between'
        },
        left:{
            fontFamily: 'Times-Roman',
            fontSize: 12,
            textDecoration: 'underline',
            textDecorationThickness: 1,
        },
        right: {
            fontFamily: 'Times-Roman',
            fontSize: 12,
            textDecoration: 'underline',
            textDecorationThickness: 1,
        },
        signatureDirection : {
            textDecoration: 'underline',
            textDecorationThickness: 1,
        },
        marginTop: {
            marginTop: '30px'
        },
        marginTopRegular: {
            marginTop: '20px'
        },
        marginTopSmall: {
            marginTop: '10px'
        },
        title: {
            fontFamily: 'Helvetica-Bold',
            textAlign: 'center',
            fontSize: 20,
            paddingVertical: 3,
            textDecoration: 'underline',
            textDecorationThickness: 1,
        },
        firstBorder:{
            width: '100%',
            padding: '3px',
            border: '3px solid #000'
        },
        secondBorder: {
            width: '100%',
            border: '3px solid #000',
            display: 'flex',
            justifyContent: 'center',
            alignContent: 'center',
            textAlign: 'center'
        },
        secondTitle: {
            fontFamily: 'Helvetica-Bold',
            textAlign: 'center',
            fontSize: 19,
            width: '100%',
            paddingVertical: 2
        }
    })

    return (
        <Document>
            <Page size='A4' style={styles.body}>
                <Image style={styles.image} src={Logo} />
                <Text style={styles.header}>{societe}</Text>
                <Text style={styles.header}>RCS {rcs}</Text>
                <Text style={styles.header}>STAT {stat}</Text>
                <Text style={styles.header}>{adresse}</Text>
                <Text style={styles.header}>{code}</Text>     

                <Text style={styles.date}>Antananarivo, le {moment().format('DD MMMM YYYY')}</Text>
                <Text style={styles.employe}>{genre}</Text>
                <Text style={styles.employe}>{sanction.employe}</Text>
                <Text style={styles.employe}>{fonction}</Text>
                <Text style={styles.employe}>Matricule {matricule}</Text>

                <Text style={styles.objet}><Text style={styles.underline}>Objet</Text> : Notification de licenciement</Text>
                
                <Text style={styles.text}>{genre},</Text>
                <Text style={styles.text}>
                    Nous faisons suite à notre entretien préalable et sommes contraints 
                    de vous notifier votre licenciement pour faute lourde, 
                    compte tenu des éléments suivants : 
                </Text>
                <Text style={styles.text}>
                    {motif}
                </Text>
                <Text style={styles.text}>
                    Vos explications recueillies lors de notre entretien, 
                    qui ne nous ont pas convaincues, ne sont pas de nature à modifier notre décision.
                </Text>
                <Text style={styles.text}>
                    Nous vous prions de restituer sans délais les matériels de la Société 
                    ayant été mis en votre disposition.
                </Text>
                <Text style={styles.text}>
                    Votre Certificat de travail est disponible après notification de la présente.
                </Text>
                <Text style={styles.text}>
                    Votre solde de tout compte sera disponible dans les périodes 
                    de distribution habituelle de salaire. 
                </Text>
                <Text style={styles.text}>
                    Nous vous prions de recevoir {genre} l’expression de nos salutations les meilleures.
                </Text>

                <View style={styles.container}>
                    <Text style={styles.left}>{sanction.societe_id == 2 || sanction.societe_id == 1 ? 'L\'employé' : 'Le Stagiaire'}</Text>
                    <Text style={styles.right}>La Direction</Text>
                </View>
                    
            </Page>
            <Page size='A4' style={styles.body}>
                <Image style={styles.image} src={Logo} />
                <Text style={styles.header}>{societe}</Text>
                <Text style={styles.header}>RCS {rcs}</Text>
                <Text style={styles.header}>STAT {stat}</Text>
                <Text style={styles.header}>{adresse}</Text>
                <Text style={styles.header}>{code}</Text>
                
                <View style={[styles.firstBorder, styles.marginTopRegular]}>
                    <View style={styles.secondBorder}>
                        <Text style={styles.secondTitle}>CERTIFICAT DE TRAVAIL</Text>
                    </View>
                </View>

                <Text style={[styles.text, styles.marginTopRegular]}>
                    Nous soussignés, société <Text style={styles.bold}>{societe == "DIRICKX GUARD SARL" ? "DIRICKX GUARD" : (societe == "« Sûreté Océan Indien Tamatave Sarl »" ? "S.O.I.T" : societe)}</Text>,  certifions par la présente que:
                </Text>
                <Text style={styles.employe}>{genre + ' ' + sanction.employe}</Text>
                <Text style={styles.employe}>Matricule : {matricule}</Text>
                <Text style={styles.employe}>Fonction : {fonction}</Text>
                <Text style={styles.employe}>Catégorie professionnelle : {sanction.categorie}</Text>

                <Text style={[styles.text, styles.marginTopSmall]}>
                    A travaillé au sein de notre Société depuis{" "}
                    <Text style={[styles.bold, styles.capitalize]}>
                        {
                            sanction.societe_id == 1 ? moment(sanction.date_confirmation).format("DD MMMM YYYY") :
                            sanction.societe_id == 2 ? moment(sanction.date_conf_soit).format("DD MMMM YYYY") :
                            ''
                        }
                    </Text> jusqu’au <Text style={[styles.bold, styles.capitalize]}>{moment().format('DD MMMM YYYY')}</Text>.
                </Text>
                <Text style={styles.text}>
                    En foi de quoi, ce certificat lui est délivré pour faire valoir ce que de droit.
                </Text>
                <Text style={styles.text}>
                    Il nous quitte libre de tout engagement.
                </Text>
                <Text style={styles.dateRight}>Fait à Antananarivo le {moment().format('DD MMMM YYYY')}</Text>

                <Text style={[styles.dateRight, styles.signatureDirection]}>La Direction</Text>
                    
            </Page>
        </Document>
    )
}
