import React, { useEffect, useRef, useState, lazy } from 'react'
import "./view.css"
import ShowMessage from '../../message/ShowMessage'
import ModelMessage from '../../model_message/ModelMessage'
import ShowModelMessage from '../../model_message/ShowModelMessage'
import PlainteAgent from '../../plainte/PlainteAgent'
import MessageSent from '../../message/MessageSent'
import ShowMessageSent from '../../message/ShowMessageSent'
import AnomalieService24 from '../../anomalie24h/AnomalieService24'
import ShowAnomalieService24 from '../../anomalie24h/ShowAnomalieService24'
import PlanniningNotDoneManager from '../../planning/PlanniningNotDoneManager'
import ShowNotDoneManager from '../../planning/ShowNotDoneManager'
import ShowAppelle from '../../appelle/ShowAppelle'
import Rappel from '../../rappeler/Rappel'
import { useLocation } from 'react-router-dom'
import ShowAnomaliePlanningEffectif from '../../planning/anomalie/ShowAnomaliePlanningEffectif'
import ShowAnomaliePointageHoraireEffectif from '../../planning/anomalie/ShowAnomaliePointageHoraireEffectif'

const Absence = lazy(() => import('../../absence/Absence'))
const ShowAbsence = lazy(() => import('../../absence/ShowAbsence'))
const Approvisionnement = lazy(() => import('../../approvisionnement/Approvisionnement'))
const ShowApprovisionnement = lazy(() => import('../../approvisionnement/ShowApprovisionnement'))
const Equipement = lazy(() => import('../../equipement/Equipement'))
const ShowEquipement = lazy(() => import('../../equipement/ShowEquipement'))
const FaitMarquant = lazy(() => import('../../fait/FaitMarquant'))
const ShowFaitMarquant = lazy(() => import('../../fait/ShowFaitMarquant'))
const Flotte = lazy(() => import('../../flotte/Flotte'))
const ShowFlotte = lazy(() => import('../../flotte/ShowFlotte'))
const Prime = lazy(() => import('../../prime/Prime'))
const ShowPrime = lazy(() => import('../../prime/ShowPrime'))
const Sanction = lazy(() => import('../../sanction/Sanction'))
const ShowSanction = lazy(() => import('../../sanction/ShowSanction'))
const Sav = lazy(() => import('../../sav/Sav'))
const ShowSav = lazy(() => import('../../sav/ShowSav'))
const ShowVisitePoste = lazy(() => import('../../visite/ShowVisitePoste'))
const VisitePoste = lazy(() => import('../../visite/VisitePoste'))
const Employe = lazy(() => import('../../employe/Employe'))
const ShowEmploye = lazy(() => import('../../employe/ShowEmploye'))
const Recrutement = lazy(() => import('../../recrutement/Recrutement'))
const ShowRecrutement = lazy(() => import('../../recrutement/ShowRecrutement'))
const ShowUser = lazy(() => import('../../user/ShowUser'))
const User = lazy(() => import('../../user/User'))
const Juridique = lazy(() => import('../../juridique/Juridique'))
const ShowJuridique = lazy(() => import('../../juridique/ShowJuridique'))
const ShowPlainte = lazy(() => import('../../plainte/ShowPlainte'))
const SuiviJuridique = lazy(() => import('../../suivi_juridique/SuiviJuridique'))
const ShowSuiviJuridique = lazy(() => import('../../suivi_juridique/ShowSuiviJuridique'))
const Reclamation = lazy(() => import('../../reclamation/Reclamation'))
const ShowReclamation = lazy(() => import('../../reclamation/ShowReclamation'))
const Paie = lazy(() => import('../../paie/Paie'))
const ShowPaie = lazy(() => import('../../paie/ShowPaie'))
const Deduction = lazy(() => import('../../Deduction/Deduction'))
const ShowDeduction = lazy(() => import('../../Deduction/ShowDeduction'))
const ShowAvance = lazy(() => import('../../avance/ShowAvance'))
const Avance = lazy(() => import('../../avance/Avance'))
const PartVariable = lazy(() => import('../../partvariable/PartVariable'))
const ShowPartVariable = lazy(() => import('../../partvariable/ShowPartVariable'))
const Badge = lazy(() => import('../../employe/badge/Badge'))
const ShowBadge = lazy(() => import('../../employe/badge/ShowBadge'))
const Message = lazy(() => import('../../message/Message.js'))
const Appelle = lazy(() => import('../../appelle/Appelle.js'))

const ShowService24 = lazy(() => import('../../service24/ShowService24'))
const Site = lazy(() => import('../../site/Site'))
const ShowSite = lazy(() => import('../../site/ShowSite'))
const Service24 = lazy(() => import('../../service24/Service24'))
const Satisfaction = lazy(() => import('../../satisfaction/Satisfaction'))
const ShowSatisfaction = lazy(() => import('../../satisfaction/ShowSatisfaction'))
const Planning = lazy(() => import('../../planning/Planning'))
const ShowPlanning = lazy(() => import('../../planning/ShowPlanning'))
const PartNonFait = lazy(() => import('../../partvariable_nonfait/PartNonFait'))
const ShowPartNonFait = lazy(() => import('../../partvariable_nonfait/ShowPartNonFait'))
const PlanningNonFait = lazy(() => import('../../planning_non_fait/PlanningNonFait'))
const ShowPlanningNonFait = lazy(() => import('../../planning_non_fait/ShowPlanningNonFait'))
const Anomalie = lazy(() => import('../../planning/anomalie/Anomalie'))
const ShowAnomalie = lazy(() => import('../../planning/anomalie/ShowAnomalie'))
const Dotation = lazy(() => import('../../dotation/Dotation'))
const ShowDotation = lazy(() => import('../../dotation/ShowDotation'))
const Stock = lazy(() => import('../../Stock/Stock'))
const ShowStock = lazy(() => import('../../Stock/ShowStock'))
const AnomaliePlanning = lazy(() => import('../../anomalie_planning/AnomaliePlanning'))
const ShowAnomaliePlanning = lazy(() => import('../../anomalie_planning/ShowAnomaliePlanning'))

export default function View({ auth, name }) {
    const [items, setItems] = useState([])
    const [currentId, setCurrentId] = useState(null)
    const [currentName, setCurrentName] = useState(null)
    const [currentItem, setCurrentItem] = useState(null)
    const [currentNature, setCurrentNature] = useState(null)
    const locationSearch = new URLSearchParams(useLocation().search);
    const detailRef = useRef(null)

    if (document.getElementById('pageTitle')) {
        const isLocalhost = window.location.href.includes('localhost')
        let titlePage = "Admin"
        if (name) {
            if (name == "unread_plainte")
                titlePage = "Plainte"
            else if (name == "unread_recouvrement")
                titlePage = "Recouvrement"
            else if (name == "model-message")
                titlePage = "Model courrier"
            else if (name == "part")
                titlePage = "Part variable"
            else
                titlePage = capitalizeFirstLetter(name);
            if (auth.note_message)
                titlePage = "(" + auth.note_message + ") " + titlePage
        }
        document.getElementById('pageTitle').textContent = titlePage + (isLocalhost ? " - LOCAL" : " - DIRICKX");
    }

    function capitalizeFirstLetter(string) {
        return string.substring(0, 1).toUpperCase() + string.substring(1)
    }

    useEffect(() => {
        setItems([])
    }, [name])

    useEffect(() => {
        if (currentItem && currentId) {
            let newItems = []
            items.forEach((item) => {
                if (currentItem.id == item.id) {
                    newItems.push(currentItem)
                }
                else
                    newItems.push(item)
            })
            setItems(newItems)
        } else if (currentItem && currentName) {
            let newItems = []
            items.forEach((item) => {
                if (currentItem.name == item.name) {
                    newItems.push(currentItem)
                } else
                    newItems.push(item)
            })
            setItems(newItems)
        }
    }, [currentItem]);

    const getWindowSize = () => {
        const { innerWidth } = window;
        return (innerWidth > 1500 ? "xg" : innerWidth > 1200 ? "lg" : innerWidth > 900 ? "md" : "sm");
    }
    const [size, setSize] = useState(getWindowSize())

    useEffect(() => {
        function handleWindowResize() {
            setSize(getWindowSize());
        }

        window.addEventListener('resize', handleWindowResize);

        return () => {
            window.removeEventListener('resize', handleWindowResize);
        }

    }, [size])

    useEffect(() => {
        if (detailRef && detailRef.current) detailRef.current.scrollTo(0, 0)
    }, [detailRef]);

    return <div>
        {
            (size != "sm" || (size == "sm" && !currentId)) && (size != "sm" || (size == "sm" && !currentName)) &&
            <div className='view-container'
                style={
                    (size == "xg") ?
                        { paddingRight: "800px" }
                        :
                        (size != "sm") ?
                            { paddingRight: "600px" }
                            :
                            { paddingRight: "0px" }
                }
            >
                <div className='view-list' style={{ paddingLeft: '10px', paddingRight: '10px' }}>
                    {
                        name == "equipement" &&
                        <Equipement auth={auth} equipements={items} setEquipements={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "sanction" &&
                        <Sanction auth={auth} sanctions={items} setSanctions={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "prime" &&
                        <Prime auth={auth} primes={items} setPrimes={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "absence" &&
                        <Absence auth={auth} absences={items} setAbsences={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "sav" &&
                        <Sav auth={auth} sav={items} setSav={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "flotte" &&
                        <Flotte auth={auth} flottes={items} setFlottes={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "da" &&
                        <Approvisionnement auth={auth} appros={items} setAppros={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "visite" &&
                        <VisitePoste auth={auth} visites={items} setVisites={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "fait" &&
                        <FaitMarquant auth={auth} faits={items} setFaits={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "employe" &&
                        <Employe auth={auth} employes={items} setEmployes={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "recrutement" &&
                        <Recrutement auth={auth} recrutements={items} setRecrutements={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "dotation" &&
                        <Dotation auth={auth} dotations={items} setDotations={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "stock" &&
                        <Stock auth={auth} stocks={items} setStocks={setItems} currentName={currentName} setCurrentName={setCurrentName} />
                    }
                    {
                        name == "user" &&
                        <User auth={auth} users={items} setUsers={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "recouvrement" &&
                        <Juridique auth={auth} juridiques={items} setJuridiques={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "plainte" &&
                        <PlainteAgent auth={auth} juridiques={items} setJuridiques={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "unread_recouvrement" &&
                        <SuiviJuridique recouvrement auth={auth} suivis={items} setSuivis={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "unread_plainte" &&
                        <SuiviJuridique auth={auth} suivis={items} setSuivis={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "reclamation" &&
                        <Reclamation auth={auth} reclamations={items} setReclamations={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "paie" &&
                        <Paie auth={auth} paies={items} setPaies={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "deduction" &&
                        <Deduction auth={auth} deductions={items} setDeductions={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "avance" &&
                        <Avance auth={auth} avances={items} setAvances={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "part" &&
                        <PartVariable auth={auth} partVariables={items} setPartVariables={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "site" &&
                        <Site auth={auth} sites={items} setSites={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "service24" &&
                        <Service24 auth={auth} services={items} setServices={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "badge" &&
                        <Badge auth={auth} empPrints={items} setEmpPrints={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "message" &&
                        <Message auth={auth} messages={items} setMessages={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "appelle" &&
                        <Appelle auth={auth} appelles={items} setAppelles={setItems} currentItem={currentItem} setCurrentItem={setCurrentItem} />
                    }
                    {
                        name == "model-message" &&
                        <ModelMessage auth={auth} models={items} setModels={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "satisfaction" &&
                        <Satisfaction auth={auth} satisfactions={items} setSatisfactions={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "planning" &&
                        <Planning auth={auth} plannings={items} setPlannings={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "part-non-fait" &&
                        <PartNonFait auth={auth} partNonFaits={items} setPartNonFaits={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "planning-non-fait" &&
                        <PlanningNonFait auth={auth} plannings={items} setPlannings={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "planning-non-fait-manager" &&
                        <PlanniningNotDoneManager auth={auth} plannings={items} setPlannings={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "anomalie" &&
                        <Anomalie auth={auth} anomalies={items} setAnomalies={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "anomalie-planning" &&
                        <AnomaliePlanning auth={auth} anomalies={items} setAnomalies={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "message-sent" &&
                        <MessageSent auth={auth} messages={items} setMessages={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "anomalie-service" &&
                        <AnomalieService24 auth={auth} anomalies={items} setAnomalies={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                    {
                        name == "rappel" &&
                        <Rappel auth={auth} calls={items} setCalls={setItems} currentId={currentId} setCurrentId={setCurrentId} />
                    }
                </div>
            </div>
        }
        {
            (size != "sm" || ((size == "sm" && currentId) || (size == "sm" && currentName))) &&
            <div className='view-detail custom-scroll' ref={detailRef}
                style={
                    (size == "xg") ?
                        { width: "800px" }
                        :
                        (size != "sm") ?
                            { width: "600px" }
                            :
                            { width: "100%" }
                }
            >
                {
                    currentId ? <>
                        {
                            name == "equipement" &&
                            <ShowEquipement auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "sanction" &&
                            <ShowSanction auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "prime" &&
                            <ShowPrime auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "absence" &&
                            <ShowAbsence auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "sav" &&
                            <ShowSav auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "flotte" &&
                            <ShowFlotte auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "da" &&
                            <ShowApprovisionnement auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "visite" &&
                            <ShowVisitePoste auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "fait" &&
                            <ShowFaitMarquant auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "employe" &&
                            <ShowEmploye auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} currentNature={currentNature} setCurrentNature={setCurrentNature} />
                        }
                        {
                            name == "recrutement" &&
                            <ShowRecrutement auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} currentNature={currentNature} setCurrentNature={setCurrentNature} />
                        }
                        {
                            name == "dotation" &&
                            <ShowDotation auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "user" &&
                            <ShowUser auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "recouvrement" &&
                            <ShowJuridique auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "plainte" &&
                            <ShowPlainte auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "paie" &&
                            <ShowPaie auth={auth} currentId={currentId} setCurrentId={setCurrentId} setItems={setItems} items={items} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            ["unread_recouvrement", "unread_plainte"].includes(name) &&
                            <ShowSuiviJuridique auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "reclamation" &&
                            <ShowReclamation auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "deduction" &&
                            <ShowDeduction auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "avance" &&
                            <ShowAvance auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "part" &&
                            <ShowPartVariable auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "site" &&
                            <ShowSite auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "service24" &&
                            <ShowService24 auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "badge" &&
                            <ShowBadge auth={auth} employes={items} size={size} setCurrentId={setCurrentId} />
                        }
                        {
                            name == "satisfaction" &&
                            <ShowSatisfaction auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "message" &&
                            <ShowMessage auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "model-message" &&
                            <ShowModelMessage auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "planning" &&
                            (locationSearch.get('type-anomalie') ?
                                (locationSearch.get('type-anomalie') == "anomalie_planning_effectif" ?

                                    <ShowAnomaliePlanningEffectif auth={auth} currentId={currentId} setCurrentId={setCurrentId} size={size} />
                                    :
                                        locationSearch.get('type-anomalie') == "anomalie_pointage_effectif" ?
                                        <ShowAnomaliePointageHoraireEffectif auth={auth} currentId={currentId} setCurrentId={setCurrentId} size={size} />
                                        :
                                        <ShowAnomalie auth={auth} currentId={currentId} setCurrentId={setCurrentId} size={size} /> 
                                )
                                :
                                <ShowPlanning auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                            )

                        }
                        {
                            name == "part-non-fait" &&
                            <ShowPartNonFait auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "planning-non-fait" &&
                            <ShowPlanningNonFait auth={auth} currentId={currentId} setCurrentId={setCurrentId} size={size} />
                        }
                        {
                            name == "anomalie" &&
                            <ShowAnomalie auth={auth} currentId={currentId} setCurrentId={setCurrentId} size={size} />
                        }
                        {
                            name == "anomalie-planning" &&
                            <ShowAnomaliePlanning auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "message-sent" &&
                            <ShowMessageSent auth={auth} messages={items} setMessages={setItems} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "anomalie-service" &&
                            <ShowAnomalieService24 auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                        {
                            name == "planning-non-fait-manager" &&
                            <ShowNotDoneManager auth={auth} currentId={currentId} setCurrentId={setCurrentId} setCurrentItem={setCurrentItem} size={size} />
                        }
                    </>
                        : currentName ?
                            <>
                                {
                                    name == "stock" &&
                                    <ShowStock auth={auth} currentName={currentName} setCurrentName={setCurrentName} setCurrentItem={setCurrentItem} size={size} />
                                }
                            </>
                            : (currentItem && currentItem.data?.length > 0 && size != "sm") ?
                                <>
                                    {
                                        name == "appelle" &&
                                        <ShowAppelle auth={auth} currentItem={currentItem} size={size} />
                                    }
                                </>
                                :
                                <img src='/img/tls_background.svg' width="100%" />
                }
            </div>
        }
    </div>
}
