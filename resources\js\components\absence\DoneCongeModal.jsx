import React, { useEffect, useState } from 'react';

import axios from 'axios';
import useToken from '../util/useToken';
import InputMonthYear from '../input/InputMonthYear';
import Textarea from '../input/Textarea';
import moment from 'moment';
moment.locale("fr")
import LoadingPage from '../loading/LoadingPage';

export default function DoneCongeModal({ absence, updateData, closeModal }) {
    const departMoment = moment(absence.depart)
    const [date, setDate] = useState({month: departMoment.format('MM'), year: departMoment.format('YYYY')})
    const [repartitions, setRepartitions] = useState([])
    const [waitRepartition, setWaitRepartition] = useState(false)
    const [note, setNote] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    useEffect(() => {
        if (date && date.month && date.year) {
            let depart = moment(absence.depart);
            let retour = moment(absence.retour);
            let datePaiement  = moment(date.year+"-"+date.month+"-20").set("hour",6).set("minute",0).set("second",0);
            let segments = [];
            let dateActuelle = moment(depart);

            while (dateActuelle.isBefore(retour)) {
                let prochaineDate20 = moment(dateActuelle).set('date', 20).add(1, 'month').set("hour", 6).set("minute", 0).set("second", 0);
                let prochaineDate = moment.min(moment(retour), prochaineDate20);
                let init20 = moment(depart).set('date', 20).set("hour", 6).set("minute", 0).set("second", 0);
                if (datePaiement > init20) {
                    init20 = moment(datePaiement)
                }
                if (init20 < prochaineDate && init20 > depart && segments.length == 0) {
                    segments.push({ label: init20.format('MMM YYYY'), depart: moment(dateActuelle).format('YYYY-MM-DD HH'), retour: init20.format('YYYY-MM-DD HH'), date_paie: init20.format("YYYY-MM-DD") });
                    dateActuelle = init20
                }
                else {   
                    segments.push({ label: prochaineDate20.format('MMM YYYY'), depart: moment(dateActuelle).format('YYYY-MM-DD HH'), retour: prochaineDate.format('YYYY-MM-DD HH'), date_paie: prochaineDate20.format("YYYY-MM-DD") });
                    dateActuelle = prochaineDate;
                }
            }   
            setRepartitions(segments);
        }
    }, [date])
        
    const handleOk = () => {
        disableSubmit(true)
        setError("")
        let formData = new FormData()
        formData.append("date_paie", date.year + "-" + date.month + "-20")
        formData.append("repartitions", JSON.stringify(repartitions))
        formData.append("note", note)
        axios.post("/api/absence/save_done/" + absence.id, formData, useToken())
            .then((res) => {
                disableSubmit(false)
                if (res.data.success) {
                    closeModal()
                    updateData()
                }
                else if (res.data.error == "EACCES")
                    setError("Une erreur est survenue.")
                else if (res.data.error)
                    setError(res.data.error)
            })
            .catch((e) => {
                console.error(e)
                disableSubmit(false)
                setError("Erreur d'envoie, réessayez.")
            })
    }

    return <div className='modal'>
        <div>
            <h3>Terminer absence</h3>
            <InputMonthYear label="Fiche de paie"
                required
                value={date}
                onChange={setDate} />
            {
                waitRepartition &&
                <LoadingPage />
            }
            {
                repartitions.length > 0 &&
                <>
                    <div className='checkbox-input-container'>
                        <b>Répartition</b>
                    </div>
                    {
                        repartitions.map((rep) => (
                            <div key={rep.label} className='card-container'>
                                {(moment(rep.retour).diff(moment(rep.depart), "hours")/24)} jour(s)<span className='secondary'>({rep.label})</span>
                            </div>
                        ))
                    }
                </>
            }
            <Textarea label="Commentaire"
                value={note}
                onChange={(value) => setNote(value)} />
            {
                error &&
                <div className='container-error'>
                    {error}
                </div>
            }
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Envoyer</button>
                <button onClick={() => { closeModal() }}>Annuler</button>
            </div>
        </div>
    </div>
}

