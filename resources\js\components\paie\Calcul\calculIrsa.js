export function calculIrsa (base_imp) {
    let tranche0 = 350000;
    let tranche1 = 400000;
    let tranche2 = 500000;
    let tranche3 = 600000;
    let irsa_ = 0;
    if (base_imp <= tranche1) {
        irsa_ = (base_imp - tranche0) * 0.05; //5%
    } else if (base_imp <= tranche2) {
        irsa_ = (tranche1 - tranche0) * 0.05; //5%
        irsa_ = irsa_ + (base_imp - tranche1) * 0.1; //10%
    } else if (base_imp <= tranche3) {
        irsa_ = (tranche1 - tranche0) * 0.05; //5%
        irsa_ = irsa_ + (tranche2 - tranche1) * 0.1; //10%
        irsa_ = irsa_ + (base_imp - tranche2) * 0.15; //15%
    } else if (base_imp > tranche3) {
        irsa_ = (tranche1 - tranche0) * 0.05; //5%
        irsa_ = irsa_ + (tranche2 - tranche1) * 0.1; //10%
        irsa_ = irsa_ + (tranche3 - tranche2) * 0.15; //15%
        irsa_ = irsa_ + (base_imp - tranche3) * 0.2; //20%
    }
    if (irsa_ <= 3000) {
        irsa_ = 3000;
    }
    return parseFloat(irsa_.toFixed(2));
};