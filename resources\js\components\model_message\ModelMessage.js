import React, { useEffect, useState } from 'react'
import LoadingPage from '../loading/LoadingPage'
import { Link, useLocation } from 'react-router-dom'
import SearchBar from '../input/SearchBar'
import InfiniteScroll from 'react-infinite-scroll-component'
import useToken from '../util/useToken'

export default function ModelMessage({ auth, setCurrentId, currentId }) {
    const [isLoading, toggleLoading] = useState(false)
    const [models, setModels] = useState([])
    const [allDataLoaded, setDataLoaded] = useState(false)
    const locationSearch = useLocation().search;

    const searchItems = [
        { label: "Reférence", name: "id", type: "number" },
        { label: "Nom", name: "nom", type: "number" },
        { label: "Objet", name: "objet", type: "number" },
        { label: 'Date', name: 'created_at', type: 'date' },
    ]

    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true);
            params.set("offset", 0)
        }
        else
            params.set("offset", models.length)
        axios.get('/api/model_message?' + params, useToken())
        .then((res) => {
            if (isMounted) {
                if (res.data.models) {
                    if (initial)
                        setModels(res.data.models)
                    else {
                        const list = models.slice().concat(res.data.models)
                        setModels(list)
                    }   
                    setDataLoaded(res.data.models.length < 30)
                }
                toggleLoading(false);
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false; }
    }

    useEffect(() => {
      updateData(true)
    }, [locationSearch])
    
    const fetchMoreData = () => {
        setTimeout(() => {
            updateData()
        }, 300);
    }

    return <> {
        isLoading ?
            <LoadingPage />
            :
            <div>
                <div className="padding-container space-between">
                    <h2>Model de courrier</h2>
                    <Link className='btn btn-primary' to="/message/model/add">Nouveau model</Link>
                </div>
                <SearchBar listItems={searchItems} />
                {
                    models.length == 0 ?
                        <h3 className='center secondary'>Aucun données trouvé</h3>
                        :
                        <InfiniteScroll
                            dataLength={models.length}
                            next={fetchMoreData}
                            hasMore={!allDataLoaded}
                            loader={<LoadingPage />}
                        >
                            <div className="line-container">
                                <div className="row-list">
                                    <b className="line-cell-md">Nom</b>
                                    <b className="line-cell-md">Objet</b>
                                    <b className=''>Destinataire</b>
                                </div>
                            </div>
                            {
                                models.map((md) => (
                                    <div className={`line-container secondary ${currentId && currentId == md.id ? "selected" : ""}`}
                                        key={md.id}
                                        onClick={() => setCurrentId(md.id)}
                                    >
                                        <div className="row-list">
                                            <span className={"line-cell-md"}>{md.type}</span>
                                            <span className={"line-cell-md"}>{md.objet}</span>
                                            <span>
                                                {
                                                    md.users.length > 0 &&
                                                    (
                                                        md.users.length == 1 ?
                                                            (md.users[0].user_name + " <" + md.users[0].user_email + ">")
                                                        :
                                                            md.users.map(n => n.user_name).join(', ')
                                                    )
                                                        
                                                }
                                            </span>
                                        </div>
                                    </div>
                                ))
                            }
                        </InfiniteScroll>
                }
            </div>
    } </>
}