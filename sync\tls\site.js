const moment = require('moment')
const mysql = require('mysql2')
moment.locale('fr')
const {db_config_zo, db_config_admin} = require("../../auth")

const pool_tls = mysql.createPool(db_config_zo);
const pool_admin = mysql.createPool(db_config_admin);
const sqlSelectSite = "SELECT st.idsite, st.superviseur_id, st.resp_sup_id, st.group_planning_id " +
    "FROM sites st " +
    "WHERE (st.group_planning_id is not null or st.resp_sup_id is not null or st.superviseur_id is not null) AND st.synchronized_at is null or (st.admin_updated_at is not null and st.synchronized_at <= st.admin_updated_at) ";

const sqlUpdateSite = "UPDATE sites set superviseur_id=?, resp_sup_id=?, group_planning_id=? " +
    "WHERE idsite = ? ";

const sqlUpdateSyncDate = "UPDATE sites SET synchronized_at = now() WHERE idsite = ? ";

function syncSiteById(sites, index) {
    if (index < sites.length) {
        const site = sites[index]
        const params = [site.superviseur_id, site.resp_sup_id, site.group_planning_id, site.idsite]
        pool_tls.query(sqlUpdateSite, params, async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                waitBeforeUpdate()
            }
            else {
                console.log("sync site: " + site.idsite)
                pool_admin.query(sqlUpdateSyncDate, [site.idsite], async (err, res) => {
                    if (err) {
                        console.error(err)
                    }
                    else console.log("synchronized_at updated")
                })
                setTimeout(() => {
                    syncSiteById(sites, index + 1)
                }, 200)
            }
        })
    }
    else
        waitBeforeUpdate()
}

function updateData() {
    pool_admin.query(sqlSelectSite, [], async (err, sites) => {
        if (err) {
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            if (sites.length > 0) {
                console.log("sites to sync: " + sites.length)
                syncSiteById(sites, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
        }
    })
}

let count = 1

function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 30000)
    if (count > 3) count = 1
    else count++
}

updateData()