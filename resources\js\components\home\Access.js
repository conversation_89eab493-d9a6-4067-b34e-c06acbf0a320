import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Link, Navigate } from 'react-router-dom';
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';

export default function Access({auth}) {
    const [nbData, setData] = useState(null) 

    useEffect(() => {        
        let isMounted = true
        axios.get('/api/access', useToken())
        .then(res => {
            setData(res.data)
        })
        .catch(err => {
            console.error(err)
        })
        return () => { isMounted = false }
    }, [])

    return (
        ["access"].includes(auth.role) ?
            <MenuView title="">
                {
                    nbData && 
                    <div>
                        <h3 className='sub-title-menu'>ACCESS</h3>
                        <div>
                            <div className='palette-container'>
                                {
                                    (nbData.nb_visite != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/visite-poste?unread=1">Visite de poste non lu</Link>
                                        <span className='badge-outline'>{nbData.nb_visite}</span>
                                    </div>
                                }
                                {
                                        nbData.nb_fait != 0 &&
                                        <div className='palette-item'>
                                            <Link className='link-no-style' to="/fait-marquant?unread=1">Fait marquant non lu</Link>
                                            <span className='badge-outline'>{nbData.nb_fait}</span>
                                        </div>
                                    }
                                {
                                    (nbData.nb_demande_equipement != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/equipement?status=demande">Demande d'équipement</Link>
                                        <span className='badge-outline'>{nbData.nb_demande_equipement}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_demande_sanction != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sanction?status=demande">Demande de sanction</Link>
                                        <span className='badge-outline'>{nbData.nb_demande_sanction}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_demande_conge != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/absence/conge?status=demande">Demande de congé</Link>
                                        <span className='badge-outline'>{nbData.nb_demande_conge}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_demande_permission != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/absence/permission?status=demande">Demande de permission</Link>
                                        <span className='badge-outline'>{nbData.nb_demande_permission}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_demande_sav != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sav/autre?status=demande">Demande de SAV</Link>
                                        <span className='badge-outline'>{nbData.nb_demande_sav}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_demande_sav_tag != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sav/tag?status=demande">{'Demande de SAV - Tag et Rondier)'}</Link>
                                        <span className='badge-outline'>{nbData.nb_demande_sav_tag}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_demande_flotte != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/flotte?status=demande">Demande de Flotte</Link>
                                        <span className='badge-outline'>{nbData.nb_demande_flotte}</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
            </MenuView>
        :
            <Navigate to="/"/>
    );
}