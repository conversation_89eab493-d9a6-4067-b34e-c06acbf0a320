import React, { useEffect, useState } from 'react';
import axios from 'axios';

import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import { useParams } from 'react-router-dom';
import moment from 'moment';
import InputMonthYear from '../input/InputMonthYear';
import Textarea from '../input/Textarea';
import InputText from '../input/InputText';
import showAmount from '../util/numberUtil';
import matricule from '../util/matricule';
import InputEmploye from '../input/InputEmploye';

export default function EditPartVariable({title, action, auth}) {
    const params = useParams()
    const [employe, setEmploye] = useState(null);
    const [datePaie, setDatePaie] = useState(null);
    const [commentaire, setCommentaire] = useState("")
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    const [criteres, setCriteres] = useState([])
    const defaultDatePv = (moment().isAfter(moment().set("date", 25)) ? moment().add(1, "month").set("date", 20) : moment().set("date", 20))

    const setValueCritere = (critereId, value) => {
        let newCriteres = []
        criteres.forEach((cr) => {
            if(critereId == cr.id)
                cr.value = value
            newCriteres.push(cr)
        })
        setCriteres(newCriteres)
    }

    const handleSubmitAll = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)

        const formData = {
            employe_id: employe ? employe.id : null,
            commentaire: commentaire.trim(),
            date_paie: datePaie ? (datePaie.year + "-" + datePaie.month + "-" + "20") : null,
            criteres: criteres
        }

        axios.post(action + (params.id ? "/" + params.id : ""), formData, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }
    
    useEffect(() => {
        let isMounted = true
        if(params.id){
            axios.get('/api/part_variable/show/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    const partVariable = res.data
                    setDatePaie({
                        month: moment(partVariable.date_paie).format("MM"),
                        year: moment(partVariable.date_paie).format("YYYY")
                    })
                    if(partVariable.employe) setEmploye({
                        id: partVariable.employe_id,
                        nom: partVariable.employe,
                        matricule: matricule(partVariable)
                    })
                    if(partVariable.commentaire) setCommentaire(partVariable.commentaire)
                }
            })
        }
        return () => { isMounted = false };
    }, [])

    useEffect(() => {
        let isMounted = true
        if(!params.id && employe){
            axios.get('/api/critere_mensuel/employe/' + employe.id, useToken())
            .then((res) => {    
                if(res.data.error)
                    console.error(res.data.error)
                else if(res.data.criteres){
                    res.data.criteres.forEach(cr => {
                        cr.value = cr.montant
                    })
                    setCriteres(res.data.criteres)
                }
            })
        }
        else 
            setCriteres([])
        return () => { isMounted = true };
    }, [employe]);

    return (
        <div id="content">
            {
                notification ? 
                    <Notification next={notification.id ? "/part-variable?id=" + notification.id : ("/part-variable?date_paie=" + datePaie.year + "-" + datePaie.month)} message={notification.success}/>
                :
                <div>
                    <div className="title-container" >
                        <h2>{title}</h2>
                    </div>
                    {
                        datePaie &&
                        <InputEmploye required
                            label="Employé"
                            value={employe}
                            onChange={setEmploye}
                            urlSearch="/api/employe/sous_hierarchie"
                            urlParams= {(datePaie ? ("?date_paie=" + datePaie.year + "-" + datePaie.month + "-" + "20") : "")}/>
                    }
                    <InputMonthYear
                        setDefaultDate
                        defaultDate={defaultDatePv}
                        required
                        value={datePaie}
                        onChange={setDatePaie}/>
                    {
                        (!params.id && criteres.length > 0) &&
                        <>
                            {
                                criteres.map(cr => (
                                    <InputText required 
                                        key={cr.id}
                                        label={cr.designation + " : " + showAmount(cr.montant)}
                                        value={cr.value ? cr.value : ''}
                                        onChange={(v) => setValueCritere(cr.id, v)}
                                    />
                                ))
                            }
                        </>
                    }
                    <Textarea
                        label="Commentaire"
                        value={commentaire}
                        onChange={setCommentaire}/>
                    {
                        error &&
                        <div className='container-error'>
                            {error}
                        </div>
                    }
                    <div className="input-container-btn">
                        <button className='primary' disabled={submitDisabled} onClick={handleSubmitAll}>
                            Envoyer
                        </button>
                    </div>
                </div>
            }
        </div>
    )
}