import axios from 'axios'
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useToken from '../util/useToken';

export default function InputArticle({role, value, onChange, required, hideInput, closeModal, useLink}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [modalOpen, toggleModal] = useState(false)
    const [articles, setArticles] = useState(null)
    
    const handleSelectArticle = (article) => {
        if(useLink) {
            let params = new URLSearchParams(location.search)
            params.set("article", article.name)
            navigate(location.pathname + "?" + params)
        }
        toggleModal(false)
        onChange(article)
        if(closeModal) closeModal()
    }

    const handleCloseModal = () => {
        toggleModal(false)
        if(closeModal) closeModal()
    }

    useEffect(() => {
        let isMounted = true
        axios.get('/api/article', useToken())
        .then((res) => {
            if(isMounted) setArticles(res.data)
        })
        return () => {isMounted = false}
    }, [])

    return <div>
        {
            !hideInput &&
            <div className='input-container'>
                <label>Article {required && <span className='danger'>*</span>}</label>
                <input
                    type="text"
                    value={value ? value.description : ""}
                    readOnly
                    onClick={() => {toggleModal(true)}}
                    />
            </div>
        }
        {
            (hideInput || modalOpen) &&
            <div className='modal'>
                <div>
                    <h2>Article</h2>
                    {
                        articles &&
                        <div className='list-container'>
                            {
                                <ul>
                                    {
                                        articles.map(ac => {
                                            return <li key={ac.name} onClick={() => handleSelectArticle(ac)}>
                                                {ac.designation}<br/>
                                            </li>
                                        })
                                    }
                                </ul>
                            }
                        </div>
                    }
                    <div className='form-button-container'>
                        <button onClick={handleCloseModal}>Annuler</button>
                    </div>
                </div>
            </div>
        }
    </div>;
}