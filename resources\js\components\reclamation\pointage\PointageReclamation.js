import {IoMdClose} from 'react-icons/io';
import {AiTwotoneEdit} from 'react-icons/ai'
import { useState } from 'react';
import ConfirmModal from '../../modal/ConfirmModal';
import axios from 'axios';
import useToken from '../../util/useToken'; 
import EditPointage from './EditPointage';
import moment from 'moment';

export default function PointageReclamation({auth, pointages, setPointages, updateData}) {
    const [showDeleteModal, toggleDeleteModal] = useState(false) 
    const [showEditModal, toggleEditModal] = useState(false) 
    const [currentPointage, setCurrentPointage] = useState(null)

    const capitalizeFirstLetter = (text) => {
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
    }

    const handleConfirmDelete = () => {
        if(currentPointage.id){
            axios.post('/api/approvisionnement/destroy_da_pointages/' + currentPointage.id, [], useToken())
            .then((res) => {
                if(res.data.success){
                    if(updateData) updateData()
                    toggleDeleteModal(false)
                }                
                else if(res.data.error == "EACCES")
                    setError("Une erreur est survenue.")
                else if(res.data.error)
                    setError(res.data.error)
            })
            .catch((e) => {
                console.error(e)
                setError("Erreur d'envoie, réessayez.")
            })
        }
        else {
            const newPointages = pointages.filter((ptg, index) => currentPointage.index != index)
            setPointages(newPointages)
            toggleDeleteModal(false)
        }
    }

    const handleDeletePointage = (ptg, index) => {
        ptg.index = index
        setCurrentPointage(ptg)
        toggleDeleteModal(true)
    }

    const handleEditPointage = (ptg, index) => {
        ptg.index = index
        setCurrentPointage(ptg)
        toggleEditModal(true)
    }

    const handleAddPointage = () => {
        setCurrentPointage(null)
        toggleEditModal(true)
    }

    return (
        <>
            {
                showEditModal &&
                <EditPointage 
                    currentPointage={currentPointage}
                    pointages={pointages}
                    setPointages = {setPointages}
                    updateData={updateData}
                    closeModal={() => toggleEditModal(false)} 
                />
            }
            {
                showDeleteModal &&
                <ConfirmModal 
                    msg="Voulez-vous vraiment supprimer cette article ?"
                    confirmAction={handleConfirmDelete}
                    closeModal={() => toggleDeleteModal(false)} 
                />
            }
            <div className='tab-list-action'>
                <div className='action-container'>
                    <span onClick={handleAddPointage}>
                        Ajouter une pointage
                    </span>
                </div>
            </div>
            <div>
                {
                    pointages && pointages.map((ptg, index) => (
                        <div key={index} className="card-container">
                            <div className='pointage-container'>
                                <div>
                                    {
                                        capitalizeFirstLetter(moment(ptg.date_pointage).format("ddd DD MMM YYYY") 
                                        + " " + (moment(ptg.date_pointage).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR"))
                                    }
                                    <br/>
                                    <span className='secondary'>
                                        {ptg.site}
                                    </span>
                                </div>
                                <div>
                                    <span onClick={() => handleEditPointage(ptg, index)}>
                                        <AiTwotoneEdit size={20}/>
                                    </span>
                                    <span onClick={() => handleDeletePointage(ptg, index)}>
                                        <IoMdClose size={20}/>
                                    </span>
                                </div>
                            </div>
                        </div>
                    ))
                }
            </div>
        </>
    )
}
