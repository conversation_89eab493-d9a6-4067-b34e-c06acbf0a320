<?php

namespace App\Http\Controllers;

use App\Models\Employe;
use App\Models\Message;
use App\Models\NoteMessage;
use App\Models\Planning;
use App\Models\PlanningPointage;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AnomaliePlanningController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function search(Request $request) {
        $auth = $request->user();
        $searchArray = [];
        // if ($auth->role == 'superviseur'){
        //     $searchArray[] = " st.superviseur_id = " . $auth->id . " ";
        // }
        if ($auth->role == 'resp_sup'){
            $searchArray[] = " (st.resp_sup_id = " . $auth->id . " or pl.user_id = " . $auth->id . ") ";
        }
        if ($request->site_id) {
            $searchArray[] =  " pl.site_id = $request->site_id ";
        }
        if ($request->created_at) {
            $searchArray[] = " ptg.date_pointage like '$request->created_at%' ";
        }
        if ($request->user_id) {
            $searchArray[] = " pl.user_id = '$request->user_id' ";
        }
        if ($request->resp_sup_id) {
            $searchArray[] = " st.resp_sup_id = '$request->resp_sup_id' ";
        }
        $query_where = "";
        $query_and = "";
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " ";
        }
        return compact("query_where", "query_and");
    }

    public function index(Request $request){
        $auth = $request->user();
        $date_now = new \DateTime();
        $role = $auth->role;
        if (in_array($role, ['resp_sup', 'resp_op', 'validateur'])) {
            // $query_and = '';
            // if($role == 'resp_sup')
            //     $query_and = " AND st.resp_sup_id = $auth->id";
            $query_and = $this->search($request)['query_and'];
            $anomalies = DB::select("SELECT count(ptg.agent_id) as 'nb',
                emp.id, emp.nom, emp.societe_id, emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi,
                st_ag.nom as 'site', resp.name as 'resp', resp.email as 'resp_email'
                FROM planning_pointages ptg 
                LEFT JOIN plannings pl on pl.id=ptg.planning_id
                LEFT JOIN sites st on st.idsite=pl.site_id
                LEFT JOIN employes emp on emp.id = ptg.agent_id
                LEFT JOIN sites st_ag on st_ag.idsite = emp.site_id
                LEFT JOIN users resp on resp.id = st_ag.resp_sup_id
            WHERE ptg.date_pointage > ?
            AND emp.soft_delete = 1 ". $query_and."
            GROUP BY ptg.agent_id
            LIMIT ?, 30", [ $date_now->format('Y-m-d H:i:s'), $request->offset]);
            return response(compact('anomalies'));
        }
        return response(['error' => "EACCES"]);
    }

    public function show(Request $request, $id) {
        $auth = $request->user();
        $date_now = new \DateTime();
        if (in_array($auth->role, ['resp_sup', 'resp_op', 'validateur'])) {
            $employe = DB::select("SELECT emp.id, emp.nom, 
                emp.date_sortie, emp.observation, emp.societe_id, emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi,
                st.idsite, st.nom as 'site', resp.name as 'resp', resp.email as 'resp_email'
                FROM employes emp
                LEFT JOIN sites st on st.idsite = emp.site_id
                LEFT JOIN users resp on resp.id = st.resp_sup_id
                WHERE emp.id = ?", [$id])[0];
            // $query_and = '';
            // if($auth->role == 'resp_sup')
            //     $query_and = " AND st.resp_sup_id = $auth->id";
            $query_and = $this->search($request)['query_and'];
            $anomalie = DB::select("SELECT ptg.id, ptg.date_pointage, st.nom as 'site', st.idsite,
                resp.name as 'resp', resp.email as 'resp_email'
                FROM planning_pointages ptg 
                LEFT JOIN plannings pl on pl.id=ptg.planning_id 
                LEFT JOIN sites st on st.idsite=pl.site_id
                LEFT JOIN employes emp on emp.id = ptg.agent_id
                LEFT JOIN users resp on resp.id = st.resp_sup_id
                WHERE ptg.date_pointage > ?
                AND emp.soft_delete = 1
                AND emp.id = ?
                ". $query_and."
                ORDER BY ptg.date_pointage ASC", [$date_now->format('Y-m-d H:i:s'), $id]);
            return response(compact('anomalie', 'employe'));
        }
        return response(['error' => "EACCES"]);
    }

    public function assign_all(Request $request, $id){
        $auth = $request->user();
        if (in_array($auth->role, ['resp_sup', 'resp_op'])) {
            $remplacant_id = $request->remplacant_id;
            $old_agent = Employe::find($id);
            $new_agent = Employe::find($remplacant_id);
            $date_now = new \DateTime();
            if ($id && $remplacant_id && ($id != $remplacant_id) && in_array($auth->role, ['resp_sup', 'resp_op'])) {
                $query_and = '';
                if($auth->role == 'resp_sup')
                    $query_and = " AND st.resp_sup_id = $auth->id";
                $pointages = DB::select("SELECT ptg.id, ptg.date_pointage, st.idsite
                    FROM planning_pointages ptg
                    LEFT JOIN plannings pl on pl.id = ptg.planning_id
                    LEFT JOIN sites st on st.idsite = pl.site_id
                    WHERE ptg.date_pointage > ?
                    AND ptg.agent_id = ? " . $query_and, [ $date_now->format('Y-m-d H:i:s'), $id]);
    
                $verify = DB::select("SELECT * FROM planning_pointages 
                    WHERE agent_id = ? 
                    AND date_pointage in ('". join("','", array_column($pointages, 'date_pointage')) ."')", 
                    [$remplacant_id]);
                if (count($verify) > 0) {
                    $date_of_pointage = new \DateTime($verify[0]->date_pointage);
                    return response(['error' => "Ce remplacant est déjà affecté à une site le " . $date_of_pointage->format('d/m/Y') . ($date_of_pointage->format('H:i:s') == '06:00:00' ? ' JOUR' : ' NUIT')]);
                };
                $query_ptg_to_update = DB::table('planning_pointages')
                    ->whereIn('id', array_column($pointages, 'id'))
                    ->where('agent_id', $id);
    
                $query_plannings = DB::table('plannings')
                    ->leftJoin('sites as st', 'st.idsite', '=', 'plannings.site_id')
                    ->whereIn('id', function ($query) use ($pointages) {
                        $query->select('planning_id')
                            ->from('planning_pointages')
                            ->whereIn('id', array_column($pointages, 'id'));
                    });
                    
                $ptg_updated = $query_ptg_to_update->get();
                $query_ptg_to_update->update(['agent_id' => $remplacant_id, 'created_at' => new \DateTime()]);
                $query_plannings->update(['updated_at' => new \DateTime(), 'seen_at' => null]);
                $plannings = $query_plannings->get();
                foreach ($plannings as $plan) {
                    $ptgs = [];
                    foreach ($ptg_updated as $ptg) {
                        if ($ptg->planning_id == $plan->id) {
                            $ptgs[] = $ptg;
                        }
                    }
                    $plan->pointages = $ptgs;
                    $details = HistoriqueController::assign_planning($request, $plan, $id, $remplacant_id);
                    if($plan->superviseur_id || ($plan->resp_sup_id && $plan->resp_sup_id == $auth->id))
                        AnomaliePlanningController::message_assign($request, $plan, $old_agent, $new_agent, $details);
                }
                return response(['success' => "Assignation effectuée"]);
            }
        }
        return response(['error' => "EACCES"]);
    }

    protected static function getService($date_pointage){
        if(!$date_pointage) return 'Null';
        $dateTime = new \DateTime($date_pointage);
        $horaire = ($dateTime->format('H:i:s') == '18:00:00') ? 'NUIT' : 'JOUR';
        return $dateTime->format('d-m-Y') . ' ' . $horaire;
    }

    public function assign(Request $request, $id){
        $pointageData = DB::select("SELECT ptg.id, ptg.planning_id, ptg.date_pointage, ptg.agent_id,
            st.resp_sup_id, st.nom as 'site', st.idsite, pl.user_id
            FROM planning_pointages ptg
            LEFT JOIN plannings pl on pl.id = ptg.planning_id
            LEFT JOIN sites st on st.idsite = pl.site_id
            WHERE ptg.id = ?", [$id])[0];
        $pointage = PlanningPointage::find($id);
        $auth = $request->user();
        if ((in_array($auth->role,['resp_sup']) && in_array($auth->id, [$pointageData->user_id, $pointageData->resp_sup_id]))
            || in_array($auth->role, ['resp_op'])) {    
            $old_pointage = clone $pointage;
            $planning =  DB::table('planning_pointages')
                ->leftjoin('plannings as pl', 'pl.id', '=', 'planning_pointages.planning_id')
                ->leftjoin('sites as st', 'st.idsite', '=', 'pl.site_id')
                ->where('planning_pointages.id', $id)
                ->first();

                // $messages = DB::table('note_messages as note')
                // ->leftJoin('messages as ms', 'ms.id', '=', 'note.message_id')
                // ->leftJoin('users as u', 'u.id', '=', 'ms.user_id')
                // ->where('ms.user_id', $request->user()->id)
                // ->where('note.follow', 1)
                // ->where(function ($query) {
                //     $query->where('note.seen', 0)
                //         ->orWhereNull('note.seen');
                // })
                // ->update(["follow"=> null]);
            $date_pointage = new \DateTime($pointage->date_pointage);
            $new_agent = Employe::find($request->remplacant_id);
            $verify = PlanningPointage::where('agent_id', $new_agent->id)->where('date_pointage', $date_pointage->format('Y-m-d H:i:s'))->first();
            if($request->service24){
                $check_service24 =PlanningPointage::where('agent_id', $new_agent->id)->Where(function ($query) use ($date_pointage){
                    return $query->where('date_pointage', (clone $date_pointage)->add(new \DateInterval('PT12H')))
                    ->orWhere('date_pointage', (clone $date_pointage)->sub(new \DateInterval('PT12H')));
                })->get();
                if(count($check_service24) > 0){
                    $error = "Cette agent est déjà affecté à un service le ";
                    foreach($check_service24 as $check){
                        $error .= self::getService($check->date_pointage) . ' ';
                    }
                    return response(['error' =>  $error ]);
                }
            }
            if($verify){
                return response(['error' => "Employe déjà affecté à une site"]);
            }
            if(!$new_agent->soft_delete && ($new_agent->id != $old_pointage->agent_id)){
                $pointage->agent_id = $request->remplacant_id;
                if($pointage->save()){
                    $details = HistoriqueController::assign_one_pointage($request, $planning, $old_pointage, $request->remplacant_id);
                    if ($planning->superviseur_id || ($planning->resp_sup_id && $planning->resp_sup_id == $auth->id)) {
                        AnomaliePlanningController::message_assign($request, $planning, $old_pointage, $new_agent, $details);
                    }
                    return response(['success' => "Assignation effectuée"]);
                }
            }
        }
        return response(['error' => "Erreur d'assignation " . ($date_pointage)->format('d/m/Y') . ($date_pointage->format('H:i:s') == '06:00:00' ? ' JOUR' : ' NUIT')]);
    }

    public static function message_assign(Request $request, $planning, $old_agent, $new_agent, $details, $objet = null){
        $message = new Message();
        $message->objet = $objet ?? 'Remplacer des employés au planning';
        $message->user_id = $request->user()->id;
        $message->created_at = new \DateTime();
        $message->updated_at = new \DateTime();
        $message->content = '<p><b>Planning : '. $planning->nom . '</b></p>';
        $message->content .= '<p><b>Agent remplacé: </b>' . $old_agent->nom . '</p>';
        $message->content .= '<p><b>Agent remplacant: </b>' . $new_agent->nom . '</p>';
        $message->content .= '<p>'. str_replace(['<', '>', '\n'], ['&lt;', '&gt;', '<br/>'], $details) .' </p>';
        $message->content .= '<p>Voir le planning : <a href="'. env('APP_URL') . "/planning?id=" . $planning->id . '">Cliquer ici</a></p>';
        if ($message->save()) {
            $note = new NoteMessage();
            $note->message_id = $message->id;
            $note->user_id = $planning->superviseur_id;
            $note->follow = 1;
            $note->created_at = new \DateTime();
            $note->updated_at = new \DateTime();
            $note->save();
            if($planning->resp_sup_id && $planning->resp_sup_id != $request->user()->id){
                $note = new NoteMessage();
                $note->message_id = $message->id;
                $note->user_id = $planning->resp_sup_id;
                $note->follow = 1;
                $note->created_at = new \DateTime();
                $note->updated_at = new \DateTime();
                $note->save();
            }
        }
    }

}
