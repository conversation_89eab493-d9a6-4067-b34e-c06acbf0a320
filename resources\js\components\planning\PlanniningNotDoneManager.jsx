import axios from 'axios';
import React, { useEffect, useState } from 'react'
import LoadingPage from '../loading/LoadingPage';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useLocation } from 'react-router-dom';
import useToken from '../util/useToken';

export default function PlanniningNotDoneManager({auth, plannings, setPlannings,  currentId ,setCurrentId}) {
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const locationSearch = useLocation().search;

    const searchItems = [
        { label: 'Site', name: 'site_id', type: 'number' },
        { label: 'Date de planning', name: 'date_planning', type: 'dateMonth' },
        { label: 'Manager', name: 'user_id', type: 'number' },
    ]

    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true);
            params.set("offset", 0)
        }
        else
            params.set("offset", plannings.length)
        axios.get('/api/planning/not_done?' + params, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.plannings) {
                        if (initial)
                            setPlannings(res.data.plannings)
                        else {
                            const list = plannings.slice().concat(res.data.plannings)
                            setPlannings(list)
                        }
                        setDataLoaded(res.data.plannings.length < 30)
                    }
                    toggleLoading(false);
                }
            })
            .catch((e) => {
                console.error(e)
            })
        return () => { isMounted = false; }
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
        setTimeout(() => updateData(), 300)
    }
    return (
        <div>
            {
                isLoading ?
                    <LoadingPage /> 
                : 
                    <div>
                        <div className="padding-container space-between">
                            <h2>Planning non fait</h2>
                        </div>
                        <SearchBar listItems={searchItems} />
                        {
                            plannings.length == 0?
                                <h3 className='center secondary'>Aucun données trouvé</h3>
                            :
                            <div>
                                {
                                                
                                    <InfiniteScroll
                                        dataLength={plannings.length}
                                        next={fetchMoreData}
                                        hasMore={!allDataLoaded}
                                        loader={<LoadingPage />}
                                    >
                                        <div className="line-container">
                                            <div className="row-list">
                                                <b className="line-cell-lg">Manager</b>
                                                <b className="line-cell-sm">Nb</b>
                                                <b className="">Agence</b>
                                            </div>
                                        </div>
                                        {
                                            plannings.map((pln, index) => {
                                                return (
                                                    <div onClick={() => setCurrentId(pln.resp_sup_id)}
                                                        className={`line-container ${currentId && currentId == pln.resp_sup_id ? "selected" : ""}`}
                                                        key={index}
                                                    >
                                                        <div className="row-list">
                                                            <span className="line-cell-lg">{pln.resp_sup_name + " <" + pln.resp_sup_email + ">"}</span>
                                                            <span className="line-cell-sm">{pln.nb_sites_non_faits}</span>
                                                            <span className="">{pln.agence}</span>
                                                        </div>
                                                    </div>
                                                )
                                            })
                                        }
                                    </InfiniteScroll>
                                }
                            </div>
                        }
                    </div>
            }
        </div>
    )
}
