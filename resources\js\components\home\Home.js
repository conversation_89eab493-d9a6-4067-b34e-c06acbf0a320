import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import LoadingPage from '../loading/LoadingPage';
import useToken from '../util/useToken';
import Wrapper from './Wrapper';
import { FiUserCheck } from "react-icons/fi";
import { IoIosGitPullRequest } from "react-icons/io";
import { MdOutlineMapsHomeWork } from "react-icons/md";
import { RiAdminLine } from "react-icons/ri";
import { ImAccessibility } from "react-icons/im";
import { RiUserSettingsLine } from "react-icons/ri";
import { GrShieldSecurity } from "react-icons/gr";
import { HiOutlineBriefcase } from "react-icons/hi";
import { GiInjustice } from "react-icons/gi";
import { LiaFunnelDollarSolid, LiaPhoneSlashSolid } from "react-icons/lia";
import { RiShirtLine } from "react-icons/ri";
import { BiPurchaseTag } from "react-icons/bi";
import { MdOutlineConstruction, MdOutlinePersonSearch } from "react-icons/md";
import { LuCalendar } from "react-icons/lu";
import { AiOutlineLike } from "react-icons/ai";
import moment from 'moment';

export default function Home({auth}) {
    const [isLoading, toggleLoading] = useState(false)
    const [nbData, setData] = useState({})
    const role = auth.role

    const isLocalhost = window.location.href.includes('localhost')
    let titlePage = "Admin"
    if (auth.note_message)
        titlePage = "(" + auth.note_message + ") " + titlePage
    document.getElementById('pageTitle').textContent = titlePage + (isLocalhost ? " - LOCAL" : " - DIRICKX");

    useEffect(() => {
        let isMounted = true
        toggleLoading(true)
        axios.get("/api/home", useToken())
        .then((res) => {
            if(isMounted){
                setData(res.data)
                toggleLoading(false)
            }
        })
        return () => { isMounted = false };
    }, [])

    return <>
        {
            isLoading ?
                <div id="content">
                    <LoadingPage/>
                </div>
            :
                <Wrapper>
                    {
                        (nbData.nb_pv > 0  || nbData.nb_pv_validation > 0 || nbData.nb_pv_non_fait > 0) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <LiaFunnelDollarSolid className="menu-icon"/>
                                <span>part variable</span></h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_pv > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/part-variable/add">A faire</Link>
                                        <span className='badge-number-outline'>{nbData.nb_pv}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_pv_validation > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/part-variable?status=validation">{auth.role == "validateur" ? "Demande de validation" : "En attente de validation"}</Link>
                                        <span className='badge-number-outline'>{nbData.nb_pv_validation}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_pv_non_fait > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/part-non-fait">Part variable non fait</Link>
                                        <span className='badge-number-outline'>{nbData.nb_pv_non_fait}</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    {
                        (
                            nbData.nb_da_validation > 0 || nbData.nb_da_valide > 0 || nbData.nb_da_traite > 0 ||
                            nbData.nb_equipement_validation > 0 || nbData.nb_sav_validation > 0 ||
                            nbData.nb_sav_tag_validation > 0 || nbData.nb_flotte_validation > 0
                        ) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <FiUserCheck className='menu-icon'/>
                                <span>validation</span>
                            </h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_da_validation > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/da?status=validation">Demande de validation DA</Link>
                                        <span className='badge-number-outline'>{nbData.nb_da_validation}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_da_valide > 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/da?status=demande">Validé par DG</Link>
                                        <span className='badge-number-outline'>{nbData.nb_da_valide}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_da_traite > 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/da?status=traite">En cours de traitement</Link>
                                        <span className='badge-number-outline'>{nbData.nb_da_traite}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_equipement_validation > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/equipement?status=validation">Demande de validation équipement</Link>
                                        <span className='badge-number-outline'>{nbData.nb_equipement_validation}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_sav_validation > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sav/autre?status=validation">Demande de validation SAV</Link>
                                        <span className='badge-number-outline'>{nbData.nb_sav_validation}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_sav_tag_validation > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sav/tag?status=validation">Demande de validation Tag et Rondier</Link>
                                        <span className='badge-number-outline'>{nbData.nb_sav_tag_validation}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_flotte_validation > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/flotte?status=validation">Demande de validation flotte</Link>
                                        <span className='badge-number-outline'>{nbData.nb_flotte_validation}</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    {
                        (
                            nbData.nb_fait > 0 || nbData.nb_site_ndf > 0 || nbData.sans_employe > 0
                            || nbData.nb_site_sans_manager > 0 || nbData.nb_misapied_demande > 0 || nbData.nb_site_principale_archive > 0
                        ) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <GrShieldSecurity className="menu-icon"/>
                                <span>opération</span>
                            </h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_fait > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/fait-marquant?unread=1">Fait marquant non lu</Link>
                                        <span className='badge-number-outline'>{nbData.nb_fait}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_site_ndf > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/site?sup_ndf=1">Site sans superviseur</Link>
                                        <span className='badge-number-outline'>{nbData.nb_site_ndf}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_site_sans_manager > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/site?resp_sup_ndf=1">Site sans manager</Link>
                                        <span className='badge-number-outline'>{nbData.nb_site_sans_manager}</span>
                                    </div>
                                }
                                { 
                                    nbData.sans_employe > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/equipement?sans_employe=1">Equipement sans employé</Link>
                                            <span className='badge-number-outline'>{nbData.sans_employe}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_site_principale_archive > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/site?site_principale_archive=1">Sites principale en archive</Link>
                                            <span className='badge-number-outline'>{nbData.nb_site_principale_archive}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_misapied_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/absence/mis_a_pied?status=demande">Mis à pied non appliqué</Link>
                                        <span className='badge-number-outline'>{nbData.nb_misapied_demande}</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    {
                        (
                            nbData.nb_planning_non_lu > 0 || nbData.nb_planning_to_do > 0 || nbData.nb_planning_to_do_next > 0
                            || nbData.nb_planning_non_lu_sup > 0 || nbData.nb_planning_non_fait > 0
                            || nbData.nb_anomalie > 0 || nbData.nb_anomalie_24h > 0 || nbData.nb_pl_non_fait_by_manager > 0
                        ) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <LuCalendar className="menu-icon"/>
                                <span>planning</span>
                            </h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_planning_non_lu > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/planning?unread=1">Non lu</Link>
                                        <span className='badge-number-outline'>{nbData.nb_planning_non_lu}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_planning_to_do > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/planning/add">A faire</Link>
                                        <span className='badge-number-outline'>{nbData.nb_planning_to_do}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_planning_to_do_next > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to={"/planning-non-fait?date_planning=" + moment().add(1, 'month').format("YYYY-MM")}>
                                            Non fait
                                            <span className='capitalize secondary'>{" (" + moment().add(1, 'month').format("MMM YYYY") + ")"}</span>
                                        </Link>
                                        <span className='badge-number-outline'>{nbData.nb_planning_to_do_next}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_planning_non_fait > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to={"/planning-non-fait?date_planning=" + moment().format("YYYY-MM")}>
                                            Non fait 
                                            <span className='capitalize secondary'>{" (" + moment().format("MMM YYYY") + ")"}</span>
                                        </Link>
                                        <span className='badge-number-outline'>{nbData.nb_planning_non_fait}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_planning_non_fait_next > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to={"/planning-non-fait?date_planning=" + moment().add(1, 'month').format("YYYY-MM")}>
                                            Non fait 
                                            <span className='capitalize secondary'>{" (" + moment().add(1, 'month').format("MMM YYYY") + ")"}</span>
                                        </Link>
                                        <span className='badge-number-outline'>{nbData.nb_planning_non_fait_next}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_planning_non_lu_sup > 0 && ["resp_sup", "resp_op", "resp_rh"].includes(role) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/planning?unread_sup=1">Non lu par le superviseur</Link>
                                        <span className='badge-number-outline'>{nbData.nb_planning_non_lu_sup}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_anomalie > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/anomalie-planning">Agents archivés encore planifiés</Link>
                                        <span className='badge-number-outline'>{nbData.nb_anomalie}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_anomalie_24h > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/anomalie-service">Service 24 dans le planning</Link>
                                        <span className='badge-number-outline'>{nbData.nb_anomalie_24h}</span>
                                    </div>
                                }
                                {/*
                                    nbData.nb_pl_non_fait_by_manager > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/non-fait-manager">Planning non fait par manager</Link>
                                        <span className='badge-outline'>{nbData.nb_pl_non_fait_by_manager}</span>
                                    </div>
                                */}
                            </div>
                        </div>
                    }
                    {
                        (
                            nbData.nb_sanction_validation > 0 || nbData.nb_prime_validation > 0
                            || nbData.nb_conge_validation > 0 || nbData.nb_permission_validation > 0 || nbData.nb_service24_validation > 0
                        ) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <ImAccessibility className='menu-icon'/>
                                <span>RH</span></h3>
                            <div>
                                <div className='palette-container'>
                                    {
                                        nbData.nb_sanction_validation > 0 &&
                                        <div className='palette-item'>
                                            <Link className='link-no-style' to="/sanction?status=validation">Demande de validation sanction</Link>
                                            <span className='badge-number-outline'>{nbData.nb_sanction_validation}</span>
                                        </div>
                                    }
                                    {
                                        nbData.nb_prime_validation > 0 &&
                                        <div className='palette-item'>
                                            <Link className='link-no-style' to="/prime?status=validation">Demande de validation prime</Link>
                                            <span className='badge-number-outline'>{nbData.nb_prime_validation}</span>
                                        </div>
                                    }
                                    {
                                        nbData.nb_conge_validation > 0 &&
                                        <div className='palette-item'>
                                            <Link className='link-no-style' to="/absence/conge?status=validation">Demande de validation congé</Link>
                                            <span className='badge-number-outline'>{nbData.nb_conge_validation}</span>
                                        </div>
                                    }
                                    {
                                        nbData.nb_permission_validation > 0 &&
                                        <div className='palette-item'>
                                            <Link className='link-no-style' to="/absence/permission?status=validation">Demande de validation permission</Link>
                                            <span className='badge-number-outline'>{nbData.nb_permission_validation}</span>
                                        </div>
                                    }
                                    {
                                        nbData.nb_service24_validation > 0 &&
                                        <div className='palette-item'>
                                            <Link className='link-no-style' to="/service24?status=validation">Demande de validation service 24</Link>
                                            <span className='badge-number-outline'>{nbData.nb_service24_validation}</span>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                    {
                        nbData.nb_recrutement_en_cours > 0 &&
                        <>
                            <div className='menu-card'>
                                <h3 className='sub-title-menu'>
                                    <MdOutlinePersonSearch className='menu-icon' />
                                    <span>RECRUTEMENT</span>
                                </h3>
                                <div className='palette-container'>
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/recrutement">Recrutements en cours</Link>
                                        <span className='badge-number-outline'>{nbData.nb_recrutement_en_cours}</span>
                                    </div>
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="">
                                            Recruté du 
                                            {" " + moment().subtract(7, 'days').clone().isoWeekday(1).startOf('day').format("DD/MM/YY")} au {moment().subtract(7, 'days').clone().isoWeekday(7).startOf('day').format("DD/MM/YY")}
                                        </Link>
                                        <span className='badge-number-outline'>{nbData.nb_recrute_last_week > 0 ? nbData.nb_recrute_last_week : "0"}</span>
                                    </div>
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="">
                                            Recruté du 
                                            {" " + moment().clone().isoWeekday(1).startOf('day').format("DD/MM/YY")} au {moment().clone().isoWeekday(7).startOf('day').format("DD/MM/YY")}
                                        </Link>
                                        <span className='badge-number-outline'>{nbData.nb_recrute_this_week > 0 ? nbData.nb_recrute_this_week : "0"}</span>
                                    </div>
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="">
                                            Recrutement non abouti
                                        </Link>
                                        <span className='badge-number-outline'>{nbData.nb_recrutement_non_abouti}</span>
                                    </div>
                                </div>
                            </div>
                        </>
                    }
                    {
                        (nbData.nb_plainte > 0 || nbData.nb_recouvrement > 0) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <GiInjustice className='menu-icon'/>
                                <span>JURIDIQUE</span>
                            </h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_plainte > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/plainte/unread">Plainte agent non lu</Link>
                                        <span className='badge-number-outline'>{nbData.nb_plainte}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_recouvrement > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/recouvrement/unread">Recouvrement client non lu</Link>
                                        <span className='badge-number-outline'>{nbData.nb_recouvrement}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_recouvrement_late > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/recouvrement?late_date=1">Recouvrement en retard</Link>
                                        <span className='badge-number-outline'>{nbData.nb_recouvrement_late}</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    {
                        (
                            nbData.etapes && nbData.etapes.length > 0
                        ) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <HiOutlineBriefcase className='menu-icon'/>
                                <span>RECOUVREMENT</span>
                            </h3>
                            <div className='palette-container'>
                                {
                                    nbData.etapes.map(e => <div key={e.id} className='palette-item'>
                                        <Link className='link-no-style' to={"/recouvrement?late_date=1&etape_id=" + e.id}>{e.nom}</Link>
                                        <span className='badge-number-outline'>{e.nb_juridique}</span>
                                    </div>)
                                }
                            </div>
                        </div>
                    }
                    {
                        (nbData.nb_equipement_demande > 0) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <BiPurchaseTag className='menu-icon'/>
                                <span>achat</span>
                            </h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_equipement_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/equipement?status=demande">Equipement à traiter</Link>
                                        <span className='badge-number-outline'>{nbData.nb_equipement_demande}</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    {
                        ((nbData.nb_equipement_tenue_demande > 0)  || (nbData.nb_a_recuperer > 0) || (nbData.nb_demande_equipement_pour_validation > 0) || (nbData.demande_valide > 0)) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <RiShirtLine className='menu-icon'/>
                                <span>tenue</span>
                            </h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_equipement_tenue_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/equipement?status=demande">Equipement à traiter</Link>
                                        <span className='badge-number-outline'>{nbData.nb_equipement_tenue_demande}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_a_recuperer > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/dotation?a_recuperer=1">Dotation à récupérer</Link>
                                        <span className='badge-number-outline'>{nbData.nb_a_recuperer}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_demande_equipement_pour_validation > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/equipement?pour_validation=1">Pour validation</Link>
                                        <span className='badge-number-outline'>{nbData.nb_demande_equipement_pour_validation}</span>
                                    </div>
                                }
                                {
                                    nbData.demande_valide > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/equipement?valider=1">Demande validée</Link>
                                        <span className='badge-number-outline'>{nbData.demande_valide}</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    {
                        (nbData.nb_sav_demande > 0 || nbData.nb_sav_tag_demande > 0 || nbData.nb_flotte_demande > 0 || nbData.nb_biometrique_demande > 0) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <MdOutlineConstruction className='menu-icon'/>
                                <span>TECH</span>
                            </h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_biometrique_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sav/biometrique?status=demande">Biométrique à traité</Link>
                                        <span className='badge-number-outline'>{nbData.nb_biometrique_demande}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_sav_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sav/autre?status=demande">SAV à traité</Link>
                                        <span className='badge-number-outline'>{nbData.nb_sav_demande}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_sav_tag_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sav/tag?status=demande">Tag et Rondier à traité</Link>
                                        <span className='badge-number-outline'>{nbData.nb_sav_tag_demande}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_flotte_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/flotte?status=demande">Flotte à traité</Link>
                                        <span className='badge-number-outline'>{nbData.nb_flotte_demande}</span>
                                    </div>
                                }
                                {/*
                                    nbData.nb_reclamation_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to={"/absence/mis_a_pied"}>Demande de reclamation</Link>
                                        <span className='badge-number-outline'>{nbData.nb_reclamation_demande}</span>
                                    </div>
                                */}
                            </div>
                        </div>
                    }
                    {
                        (
                            nbData.nb_sanction_demande > 0 || nbData.nb_conge_demande > 0 || nbData.nb_permission_demande > 0
                            || nbData.nb_avance_demande > 0 || nbData.nb_reclamation_demande > 0
                        ) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <ImAccessibility className='menu-icon'/>
                                <span>RH</span>
                            </h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_sanction_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sanction?status=demande">Demande de sanction</Link>
                                        <span className='badge-number-outline'>{nbData.nb_sanction_demande}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_prime_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/prime?status=demande">Demande de prime</Link>
                                        <span className='badge-number-outline'>{nbData.nb_prime_demande}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_conge_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/absence/conge?status=demande">Demande de congé</Link>
                                        <span className='badge-number-outline'>{nbData.nb_conge_demande}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_permission_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/absence/permission?status=demande">Demande de permission</Link>
                                        <span className='badge-number-outline'>{nbData.nb_permission_demande}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_avance_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/avance?status=demande">Demande d'avance</Link>
                                        <span className='badge-number-outline'>{nbData.nb_avance_demande}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_reclamation_demande > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/reclamation?status=demande">Demande de réclamation d'heure</Link>
                                        <span className='badge-number-outline'>{nbData.nb_reclamation_demande}</span>
                                    </div>
                                }

                            </div>
                        </div>
                    }
                    {
                        (nbData.nb_users > 0 || nbData.nb_all_users > 0) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <RiAdminLine className='menu-icon'/>
                                <span>admin</span>
                            </h3>
                            <div>
                                <div className='palette-container'>
                                    {
                                        nbData.nb_users > 0 &&
                                        <div className='palette-item'>
                                            <Link className='link-no-style' to="/user?status=1">Utilisateurs jamais connecté</Link>
                                            <span className='badge-number-outline'>{nbData.nb_users}</span>
                                        </div>
                                    }
                                    {
                                        nbData.nb_all_users > 0 &&
                                        <div className='palette-item'>
                                            <Link className='link-no-style' to="/user">Tous les utilisateurs</Link>
                                            <span className='badge-number-outline'>{nbData.nb_all_users}</span>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                    {
                        ["superviseur"].includes(auth.role) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <GrShieldSecurity className="menu-icon"/>
                                <span>opération</span>
                            </h3>
                            <div className='palette-container'>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/service24/add">Demande de service 24</Link>
                                </div>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/satisfaction/add">Fiche de satisfaction</Link>
                                </div>
                            </div>
                        </div>
                    }
                    {
                        ["superviseur"].includes(auth.role) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <ImAccessibility className="menu-icon"/>
                                <span>rh</span>
                            </h3>
                            <div className='palette-container'>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/absence/add/conge">Demande de congé</Link>
                                </div>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/absence/add/permission">Demande de permission</Link>
                                </div>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/prime/add">Demande de prime</Link>
                                </div>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/reclamation/add">Réclamation pointage</Link>
                                </div>
                            </div>
                        </div>
                    }
                    <div className='menu-card'>
                        <h3 className='sub-title-menu'>
                            <IoIosGitPullRequest className='menu-icon'/>
                            <span>demande</span>
                        </h3>
                        <div className='palette-container'>
                            <div className='palette-item'>
                                <Link className='link-no-style' to="/da/add">Nouvelle DA</Link>
                            </div>
                            <div className='palette-item'>
                                <Link className='link-no-style' to="/equipement/add">Demande d'équipement ou tenue</Link>
                            </div>
                            <div className='palette-item'>
                                <Link className='link-no-style' to="/flotte/add">Demande ou problème de flotte</Link>
                            </div>
                        </div>
                    </div>
                    {
                        ["superviseur", "resp_sup", "resp_op","room", "access"].includes(role) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <MdOutlineMapsHomeWork className='menu-icon'/>
                                <span>site</span>
                            </h3>
                            <div className='palette-container'>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/sav/add">Demande de SAV</Link>
                                </div>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/visite-poste/add">Nouvelle visite de poste</Link>
                                </div>
                                <div className='palette-item'>
                                    <Link className='link-no-style' to="/fait-marquant/add">Nouveau fait marquant</Link>
                                </div>
                            </div>
                        </div>
                    }
                    {
                        ['room', 'resp_room'].includes(role) && nbData.re_call &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <LiaPhoneSlashSolid className='menu-icon'/>
                                <span>ROOM</span>
                            </h3>
                            <div className='palette-container'>
                                {
                                    nbData.re_call > 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/rappel">Contact à rappeler</Link>
                                        <span className='badge-number-outline'>{nbData.re_call}</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    {
                        (nbData.nb_unread_this_month > 0 || nbData.nb_unread_last_month > 0) &&
                        <div className='menu-card'>
                            <h3 className='sub-title-menu'>
                                <AiOutlineLike className='menu-icon'/>
                                <span>Satisfaction</span>
                            </h3>
                            <div>
                                <div className='palette-container'>
                                    {
                                        nbData.nb_unread_this_month > 0 &&
                                        <div className='palette-item'>
                                            <Link className='link-no-style' to={`/satisfaction?unread=1&created_at=${moment().format('MMMM')}`}><span className='capitalize secondary'>Non lu {moment().format('MMM')}</span></Link>
                                            <span className='badge-number-outline'>{nbData.nb_unread_this_month}</span>
                                        </div>
                                    }
                                    {
                                        nbData.nb_unread_last_month > 0 &&
                                        <div className='palette-item'>
                                            <Link className='link-no-style' to={`/satisfaction?unread=1&created_at=${moment().subtract(1, 'months').format('MMMM')}`}><span className='capitalize secondary'>Non lu {moment().subtract(1, 'months').format('MMM')}</span></Link>
                                            <span className='badge-number-outline'>{nbData.nb_unread_last_month}</span>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </Wrapper>
        }
    </>
}
