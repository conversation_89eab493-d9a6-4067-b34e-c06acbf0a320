import React, { useState } from 'react';
import LoadingScreen from '../loading/LoadingScreen';
import useToken from '../util/useToken';


export default function ActionSuiviJuridique({juridique, updateData}) {
    const [isLoading, toggleLoading] = useState(false);

    const handleSeen = () => {
        toggleLoading(true)
        axios.post('/api/seen/juridique/' + juridique.id , null, useToken())
        .then((res) => {
            toggleLoading(false)
            if(res.data.success)
                updateData()
        })
        .catch((e) => {
            console.error(e)
        })
    }

    return <> {
        isLoading ?
            <LoadingScreen/>
        :
            <div>
                <div className='action-container'>
                    <span onClick={handleSeen}> 
                        Marquer comme lu
                    </span>
                </div>
            </div>
    } </>
}