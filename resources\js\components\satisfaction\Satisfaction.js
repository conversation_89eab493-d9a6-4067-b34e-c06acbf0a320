import React, { useEffect, useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import <PERSON>Bar from '../input/SearchBar'
import useToken from '../util/useToken';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import StatusLabel from '../input/StatusLabel';

export default function Satisfaction({ auth, satisfactions, setSatisfactions, currentId, setCurrentId }) {
    const [isLoading, toggleLoading] = useState(false);
    const [allDataLoaded, setDataLoaded] = useState(false);
    const locationSearch = useLocation().search;
    const params = new URLSearchParams(locationSearch)
    const searchItems = [
        { label: "Date de creation", type: 'date', name: 'created_at' },
        { label: 'Réference', name: "id", type: "number" },
        { label: 'Site', name: 'site_id', type: 'number' },
    ]   
    if (['access', 'validateur'].includes(auth.role)) {
        searchItems.push(
            { label: 'Non lu', name: 'unread', type: 'string' },
            { label: 'Utilisateur', name: 'user_id', type: 'number' }
        )
    }
    const updateData = (initial) => {
        let isMounted = true;
        if (initial) {
            toggleLoading(true);
            setDataLoaded(true);
            params.set('offset', 0)
        }
        else params.set('offset', satisfactions.length);
        axios.get("/api/satisfaction?" + params, useToken())
        .then((res) => {
            if (isMounted) {
                if (res.data.error)
                    console.error("Error: " + res.data.error)
                else {
                    if (initial) setSatisfactions(res.data.satisfactions);
                    else {
                        const list = satisfactions.slice().concat(res.data.satisfactions);
                        setSatisfactions(list);
                    }
                    setDataLoaded(res.data.satisfactions.length < 30);
                }
                toggleLoading(false);
            }    
        }).catch(e => {
            console.error(e);
        })
        return () => isMounted = false;
    }

    const handleSeenAll = () => {
        toggleLoading(true);
        axios.post("/api/satisfaction/seen_all", { ids: satisfactions.map(s => s.id) }, useToken())
        .then((res) => { 
            if (res.data.success) {
                updateData(true)
            }
            else
                toggleLoading(false)
        })
        .catch(() => {
            toggleLoading(false)
        })
    }

    useEffect(() => updateData(true), [locationSearch]);
    
    const fetchMoreData = () => {
        setTimeout(() => updateData(), 300);
    }

    return (
        <>  
            {
                isLoading ?
                    <LoadingPage />
                :
                    <div>
                        <div className='padding-container space-between'>
                            <h2>
                                Satisfaction
                            </h2>
                            {
                                ['validateur', 'access'].includes(auth.role) &&
                                (params.get("unread") && satisfactions.length > 0) &&
                                <span className='btn btn-outline-secondary pointer' onClick={handleSeenAll} >Marquer tout comme lu</span>
                            }
                            {
                                ['superviseur', 'resp_sup', 'resp_op', 'access'].includes(auth.role) &&
                                <Link to="/satisfaction/add" className='btn btn-primary'>Nouveau</Link>
                            }
                        </div>
                        <SearchBar listItems={searchItems} />
                        {
                            satisfactions.length == 0 ?
                                <h3 className='center secondary'>Aucun données trouvé</h3>
                            :
                                <div>
                                    <InfiniteScroll
                                        dataLength={satisfactions.length}
                                        next={fetchMoreData}
                                        hasMore={!allDataLoaded}
                                        loader={<LoadingPage/>}
                                    >
                                        <div className='line-container'>
                                            <div className='row-list'>
                                                <b className='line-cell-md'>Site</b>
                                                <b className="status-line">
                                                    <StatusLabel color={"grey"} />
                                                </b>
                                                <b className='line-cell-xs'>PJ</b>
                                                <b>Utilisateur</b>
                                            </div>
                                        </div>
                                        {
                                            satisfactions.map((stf, index) =>  (
                                                <div onClick={() => setCurrentId(stf.id)}
                                                    className={`line-container ${currentId && currentId == stf.id ? "selected" : ""}`}
                                                    key={index}
                                                >
                                                    <div className="row-list">
                                                        <span className='line-cell-md'>{stf.site_nom}</span>
                                                        <span className="status-line">
                                                            <StatusLabel color={stf.user_id == auth.id ? 'grey' : stf.seen ? 'green' : 'purple'} />
                                                        </span>
                                                        <span className='line-cell-xs'>{stf.nb_pj}</span>
                                                        <span>
                                                            {stf.user_nom + '<' + stf.user_email + '> '}
                                                        </span>
                                                    </div>
                                                </div>
                                            ))
                                        }
                                    </InfiniteScroll>
                                </div>
                        }
                    </div>
            }
        </>
    )
}
