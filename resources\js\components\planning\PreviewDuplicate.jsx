import moment from "moment";
import React, { useState, useEffect  } from "react"
import matricule from "../util/matricule";
import './calendar.css';
import ModalAgent from "./ModalAgent";

export default function PreviewDuplicate({ planning, 
    pointages, 
    dateDuplication,
    dateFirstSevice,
    service,
    firstService,
    calendar
 }) {
    const add12Hours = service.value == "18:00:00" ? true : false
    const [planningDuplicates, setPlanningDuplicates] = useState([]);
    const [days, setDays] = useState([]);
    const [numberOfBlank, setNumberOfBlank] = useState(0)
    const [regroupPlanning, setRegroupPlanning] = useState([])
    const contrat = {agent_day: planning.nb_agent_day_planning, agent_night: planning.nb_agent_night_planning}
    const [showAgent, setShowAgent] = useState(false)
    const [currentPlanning, setCurrentPlanning] = useState([])
    const daysOfWeek = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];

    const initFormPlanning = (dtPl) => {
        if (dtPl && dtPl?.year && dtPl?.month) {
            const currentNumberDays = moment(`${dtPl.year}-${dtPl.month}`, 'YYYY-MM').daysInMonth()
            const planningTemps = []
            for (let i = 1; i <= currentNumberDays; i++) {
                const date = moment(`${dtPl.year}-${dtPl.month}-${i}`, 'YYYY-MM-DD');
                planningTemps.push({
                    id: i + "J",
                    day: i,
                    nom: date.format('ddd'),
                    service: (date.clone().set({ hour: 6, minute: 0, second: 0 })).format('YYYY-MM-DD HH:mm:ss'),
                    comment: {edition:false, comment_content: '' },
                    employes: [],
                })
                planningTemps.push({
                    id: i + "N",
                    day: i,
                    nom: date.format('ddd'),
                    service: (date.clone().set({ hour: 18, minute: 0, second: 0 })).format('YYYY-MM-DD HH:mm:ss'),
                    comment: { edition: false, comment_content: '' },
                    employes: [],
                })
            }
            return planningTemps
        }
    }

    const duplicatePointage = (pointages) => {
        let dateDecalage = moment(moment(dateDuplication).format('YYYY-MM-DD') + ' ' + service.value).format('YYYY-MM-DD HH:mm:ss');
        let ptg = initFormPlanning({year:moment(dateDuplication).format('YYYY'), month:moment(dateDuplication).format('MM')});
        let nb_decalage = moment(dateDecalage).date() - 1;
        let numberDays = moment(dateDecalage, 'YYYY-MM').daysInMonth(); 
        pointages.forEach((pointage) => {
            let date = moment(pointage.date_pointage, 'YYYY-MM-DD HH:mm:ss');
            if (!date.isValid() || date.date() > numberDays) {
                return;
            }
            date.set('month', moment(dateDecalage).month());
            let currentDay = date.date();
            let adjustedDay = currentDay + nb_decalage + (add12Hours ? 0.5 : 0);
            if (adjustedDay <= numberDays) {
                let newDay = date.clone().add(nb_decalage, 'days');
                if (add12Hours) {
                    newDay = newDay.add(12, 'hours');
                }
                const existing = ptg.find((p) => moment(p.service).format('DD HH:mm:ss') === newDay.format('DD HH:mm:ss'));
                if (existing && !existing.employes.find(e => e.id == pointage.agent_id)) {
                    existing.employes.push({
                        id: pointage.agent_id,
                        matricule: matricule(pointage),
                        nom: pointage.nom,
                        soft_delete: pointage.soft_delete
                    });
                }
            }
        });
        return ptg;
    }; 

    const pointageCustomFirstPointage = (pointages) => {
        const firstDateOfMonth = moment(planning.date_planning).date(1).format('YYYY-MM-DD');
        const tempPtgs = [];
        const diffHourBetweenFirstPointageAndFirstService =  moment(moment(dateFirstSevice).format("YYYY-MM-DD") + " " + firstService.value).diff(moment(firstDateOfMonth + " 06:00:00"), 'hours', true);
        pointages.forEach((ptg) => {
            let dateServiceMinusDiff = moment(ptg.date_pointage).subtract(diffHourBetweenFirstPointageAndFirstService, 'hours');
            if (moment(dateServiceMinusDiff).format('YYYY-MM') == moment(firstDateOfMonth).format('YYYY-MM')) {
                tempPtgs.push({
                    agent_id: ptg.agent_id,
                    date_pointage: moment(dateServiceMinusDiff).format('YYYY-MM-DD HH:mm:ss'),
                    id: ptg.id,
                    nom: ptg.nom,
                    num_emp_saoi: ptg.num_emp_soit,
                    num_emp_soit: ptg.num_emp_saoi,
                    numero_employe: ptg.numero_employe,
                    numero_stagiaire: ptg.numero_stagiaire,
                    societe_id: ptg.societe_id,
                    soft_delete: ptg.soft_delete,
                })
            }
        });
        return tempPtgs.sort((a, b) => a.date_pointage.localeCompare(b.date_pointage)); 
    }

    useEffect(() => {
        const today = moment(dateDuplication, 'YYYY-MM');
        const monthStart = today.clone().startOf('month');
        const monthEnd = today.clone().endOf('month');
        const startDate = monthStart.clone().startOf('week');
        const endDate = monthEnd.clone().endOf('week');
        const all_days = [];
        let day = startDate;
        let nbBlank = 0
        let isBefore = true;
        while (day.isBefore(endDate, 'day') || day.isSame(endDate, 'day')) {
            all_days.push({
                date: day.clone(),
                day: day.month() === today.month() ? day.date() : '',
                isCurrentMonth: day.month() === today.month(),
            });
            if (isBefore && day.month() !== today.month()) {
                nbBlank += 1
            }
            else isBefore = false;
            day.add(1, 'day');
        }
        setNumberOfBlank(nbBlank)
        setDays(all_days);
    }, [dateDuplication, service]);
    
    useEffect(() => {
        const reformatPtg = duplicatePointage(pointageCustomFirstPointage(pointages))
        setPlanningDuplicates(reformatPtg)
        let ptg = initFormPlanning({year:moment(dateDuplication).format('YYYY'), month:moment(dateDuplication).format('MM')});
        reformatPtg.map(rfptg =>{
            let currentPtg = ptg.find(p => moment(p.service).format('YYYY-MM-DD HH:mm:ss') == moment(rfptg.service).format('YYYY-MM-DD HH:mm:ss'))
            if(currentPtg && moment(currentPtg.service).isAfter(moment())){
                currentPtg.employes = currentPtg.employes.concat(rfptg.employes)
            }
        })
        setRegroupPlanning(groupByDay(ptg))
    }, [dateDuplication,
        dateFirstSevice,
        service,
        firstService, 
        planning
    ]);

    const groupByDay = (data) =>{
        const grouped = {};
        data.forEach(item => {
            const day = item.day;
            if (!grouped[day]) {
                grouped[day] = [];
            }
            grouped[day].push(item);
        });
        return Object.values(grouped);
    }

    return (
        <div>
            {
                calendar ?
                    <>
                        {
                            showAgent &&
                            <ModalAgent planning={currentPlanning} closeModal={()=>setShowAgent(false)} setShowAgent={setShowAgent} />	
                        }
                        <div className="calendar-header">
                            {daysOfWeek.map((day) => (
                                <div key={day} className="calendar-header-day">
                                    {day}
                                </div>
                            ))}
                        </div>
                        <div className="calendar-body">
                            {days.slice(0, numberOfBlank).map((day, index) => (
                                <div key={index} className={`calendar-day-in-modal`}>
                                    <div className="calendar-day-half calendar-day-half--day"></div>
                                    <div className="calendar-day-half calendar-day-half--night"></div>
                                </div>
                            ))}
                            {regroupPlanning.map((pl, index) => (
                                <div key={index} className={`calendar-day-in-modal`} >
                                    {pl.map((day) =>
                                        <React.Fragment key={day.id}>
                                            {
                                                day?.service &&
                                                <div className={`calendar-day-half 
                                                    ${'calendar-day-half--' + (moment(day?.service).format('HH') == '06' ? 
                                                        ('day' + (day.employes.length < contrat.agent_day ? '-not-enough' : (day.employes.length > contrat.agent_day ? '-surplus' : ''))) 
                                                    :
                                                        ('night' + (day.employes.length < contrat.agent_night ? '-not-enough' :(day.employes.length > contrat.agent_night ? '-surplus' : ''))))}`}
                                                >
                                                    {window.innerWidth <= 370 ? 
                                                        <span className='day secondary comment-add'>
                                                            <span>{moment(day.service).format('D')}</span>
                                                            <span>{(moment(day?.service).format('HH:mm:ss') == '06:00:00' ? 'J' : 'N')}</span>
                                                        </span>
                                                        :
                                                        <span className='day secondary'>
                                                            {moment(day?.service).format('D') + (moment(day?.service).format('HH:mm:ss') == '06:00:00' ? 'J' : 'N')} 
                                                        </span>
                                                    }
                                                    <span className={`number-agent secondary`} >
                                                        {
                                                            day.employes.length > 0 &&
                                                            <b onClick={() => { setShowAgent(true), setCurrentPlanning(day) }}>
                                                                {day.employes.length}
                                                            </b>
                                                        }
                                                    </span>
                                                        
                                                    <span className='day secondary comment-add' style={window.innerWidth >= 425 ? {width:10} : {display:'none'}}>
                                                        {/* {moment().isBefore(moment(day.service)) &&
                                                            <HiOutlineUserAdd size={15} 
                                                                // onClick={() => { setCurrentDatePtg(moment(day.service).format('YYYY-MM-DD HH:mm:ss')), setShowEmploye(true), addUser(day.id) }} 
                                                            />
                                                        } */}
                                                    </span>
                                                </div>
                                            }
                                        </React.Fragment>
                                    )}
                                </div>
                            ))}
                        </div>
                    </>
                :
                    planningDuplicates.map((pl, index) => 
                        <div key={index} className="card-container">
                            <div className="space-between">
                                <span>
                                    {(pl.nom).toUpperCase() + " " + pl.day + " " + (moment(pl.service).format('HH:mm:ss') == "06:00:00" ? " JOUR" : " NUIT")}
                                </span>
                                
                            </div>
                            {pl.employes?.length > 0 &&
                                <div style={{padding: '20px 10px'}} className="secondary" >
                                    {
                                        pl.employes.map((emp, index) => 
                                            <div key={index} style={{ padding : 5 }}>
                                                {emp.id ? ("[" + emp.matricule + "] " + emp.nom) : ""}
                                            </div>
                                        )
                                    }
                                </div>
                            }
                        </div>
                    )
            }
        </div>
    )
}