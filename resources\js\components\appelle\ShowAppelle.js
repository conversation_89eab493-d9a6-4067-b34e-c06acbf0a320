import React, { useEffect, useState } from 'react'
import LoadingPage from '../loading/LoadingPage';
import moment from 'moment';



export default function ShowAppelle({auth, currentItem, size}) {

    const nbOperateur = {
        inbound : {day: 5, night: 8},
        outbound : {day: 8, night: 11}
    }
    const [dataTable, setDataTable] = useState();

    const formatDuration = (secs) => {
        const hours = Number.parseInt(secs/3600)
        const minutes = Number.parseInt((secs%3600)/60)
        const secondes = Number.parseInt((secs%3600)%60)
        return (hours > 0 ? hours + "h" : "") + " " + (minutes > 0 ? minutes + "m" : "") + " " + (secondes > 0 ? secondes + "s" : "")
    }

    useEffect(() => {
        let totalCount = 0
        let inboundCount = 0
        let outboundCount = 0
        let totalDuration = 0
        let inboundDuration = 0
        let outboundDuration = 0
        let arrayCall = []
        currentItem.data.forEach(d => { 
            let nbCall = 0
            if(currentItem.showClearData) {
                d.clearAppelles.forEach(a => {
                    if(a.disposition == "ANSWERED"){
                        totalCount += 1
                        totalDuration += a.duration
                        if(a.calltype == "Inbound"){
                            nbCall +=1
                            inboundCount += 1
                            inboundDuration += a.duration
                        }
                        else if(a.calltype == "Outbound"){
                            nbCall +=1
                            outboundCount += 1
                            outboundDuration += a.duration
                        }
                    }
                })
            }
            else {
                d.appelles.forEach(a => {
                    if(a.disposition == "ANSWERED"){
                        totalCount += 1
                        totalDuration += a.duration
                        if(a.calltype == "Inbound"){
                            nbCall +=1
                            inboundCount += 1
                            inboundDuration += a.duration
                        }
                        else if(a.calltype == "Outbound"){
                            nbCall +=1
                            outboundCount += 1
                            outboundDuration += a.duration
                        }
                    }
                })
            }
            arrayCall.push(nbCall)
        })
        const horaire = (moment(currentItem.selectedService).format("HH:mm:ss") == "06:00:00" ? "day" : "night");
        setDataTable(
            {
                nbCall : {
                    min: (Math.min(...arrayCall) / nbOperateur.inbound[horaire]).toFixed(2),
                    max: (Math.max(...arrayCall) / nbOperateur.inbound[horaire]).toFixed(2),
                    moy: (arrayCall.reduce(
                            (accumulator, currentValue) => accumulator + currentValue,
                            0,
                        ) / arrayCall.length / nbOperateur.inbound[horaire]).toFixed(2)
                },
                horaire : horaire,
                duration : {
                    total : totalDuration,
                    inbound: inboundDuration,
                    outbound: outboundDuration
                },
                count : {
                    total : totalCount,
                    inbound: inboundCount,
                    outbound: outboundCount
                },
            }
        )
    }, [currentItem]);

    return <>
        {
            !dataTable ?
                <LoadingPage/>
            :
                <div>
                    <div className='padding-container'>
                        <h2>
                            <span className='capitalize'>
                                {
                                    moment(currentItem.selectedService).format("ddd DD MMMM YYYY") 
                                    + " " + (moment(currentItem.selectedService).format("HH") == "06" ? "JOUR" : "NUIT")
                                }
                            </span>
                        </h2>
                    </div>

                    <div className="card-container">
                        <h3 className='text'>Nombre d'appelle</h3>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'></span>
                            <span className='secondary center line-cell-sm'>
                                N Op
                            </span>
                            <span className='secondary right line-cell-sm'>
                                Nb/Op
                            </span>
                            <span className='secondary right line-cell-sm'>
                                Total
                            </span>
                        </div>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'>Entrant</span>
                            <span className='secondary center line-cell-sm'>
                                {nbOperateur.inbound[dataTable.horaire]}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {(dataTable.count.inbound / nbOperateur.inbound[dataTable.horaire]).toFixed(2)}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {dataTable.count.inbound}
                            </span>
                        </div>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'>Sortant</span>
                            <span className='secondary center line-cell-sm'>
                                {nbOperateur.outbound[dataTable.horaire]}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {(dataTable.count.outbound / nbOperateur.outbound[dataTable.horaire]).toFixed(2)}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {dataTable.count.outbound}
                            </span>
                        </div>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'>Tous</span>
                            <span className='secondary center line-cell-sm'>
                                {/*(nbOperateur.inbound[dataTable.horaire] + nbOperateur.outbound[dataTable.horaire])/2*/}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {(dataTable.count.inbound / nbOperateur.inbound[dataTable.horaire] + dataTable.count.outbound / nbOperateur.outbound[dataTable.horaire]).toFixed(2)}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {dataTable.count.total}
                            </span>
                        </div>
                    </div>
                    
                    <div className="card-container">
                        <h3 className='text'>Durée d'appelle</h3>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'></span>
                            <span className='secondary center line-cell-sm'>
                                N Op
                            </span>
                            <span className='secondary right line-cell-sm'>
                                Durée/Op
                            </span>
                            <span className='secondary right line-cell-sm'>
                                Total
                            </span>
                        </div>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'>Entrant</span>
                            <span className='secondary center line-cell-sm'>
                                {nbOperateur.inbound[dataTable.horaire]}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {formatDuration(dataTable.duration.inbound / nbOperateur.inbound[dataTable.horaire])}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {formatDuration(dataTable.duration.inbound)}
                            </span>
                        </div>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'>Sortant</span>
                            <span className='secondary center line-cell-sm'>
                                {nbOperateur.outbound[dataTable.horaire]}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {formatDuration(dataTable.duration.outbound / nbOperateur.outbound[dataTable.horaire])}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {formatDuration(dataTable.duration.outbound)}
                            </span>
                        </div>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'>Tous</span>
                            <span className='secondary center line-cell-sm'>
                                {/*(nbOperateur.inbound[dataTable.horaire] + nbOperateur.outbound[dataTable.horaire])/2*/}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {formatDuration(dataTable.duration.inbound / nbOperateur.inbound[dataTable.horaire] + dataTable.duration.outbound / nbOperateur.outbound[dataTable.horaire])}
                            </span>
                            <span className='secondary right line-cell-sm'>
                                {formatDuration(dataTable.duration.total)}
                            </span>
                        </div>
                    </div>
                    
                    <div className="card-container">
                        <h3 className='text'>Nombre d'appelle / Opérateur / {currentItem.interval} min</h3>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'>Minimum</span>
                            <span className='secondary right line-cell-sm'>
                                {dataTable.nbCall.min}
                            </span>
                        </div>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'>Maximum</span>
                            <span className='secondary right line-cell-sm'>
                                {dataTable.nbCall.max}
                            </span>
                        </div>
                    </div>
                    <div className="line-container">
                        <div className="row-list space-between">
                            <span className='text line-cell-sm'>Moyenne</span>
                            <span className='secondary right line-cell-sm'>
                                {dataTable.nbCall.moy}
                            </span>
                        </div>
                    </div>
                </div>
        }
    </>
}
