import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import { FiMoreVertical } from 'react-icons/fi';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import HeaderFlotte from './HeaderFlotte';

export default function Flotte({auth, flottes, setFlottes, currentId, setCurrentId}) {
    const locationSearch = useLocation().search;
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'Réference', name: 'id', type:'number'},
        {label: 'Status', name: 'status', type:'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Demandeur', name: 'user_id', type:'number'},
        {label: 'Site', name: 'site_id', type:'number'},
    ]

    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            params.set("offset", 0)
        }
        else
            params.set("offset", flottes.length)
        axios.get('/api/flotte?' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setFlottes(res.data.flottes)
                    else {
                        const list = flottes.slice().concat(res.data.flottes)
                        setFlottes(list)
                    }
                    setDataLoaded(res.data.flottes.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {updateData(true)}, [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <>{
        isLoading ?
            <LoadingPage/>
        :
            <div>
                <div className="padding-container space-between">
                    <h2>Flotte</h2>
                    {
                        ['superviseur','resp_sup','resp_op'].includes(auth.role) && 
                        <Link className='btn btn-primary' to="/flotte/add">Nouvelle flotte</Link>
                    }
                </div>
                <SearchBar listItems={searchItems}/>
                {
                    flottes.length == 0 ?
                        <h3 className='center secondary'>Aucun données trouvé</h3>
                    :
                        <InfiniteScroll
                            dataLength={flottes.length}
                            next={fetchMoreData}
                            hasMore={!allDataLoaded}
                            loader={<LoadingPage/>}
                        >
                            {
                                flottes.map((f) => (
                                    <div className={`card-container ${currentId && currentId == f.id ? 'selected' : ''}`} key={f.id}>
                                        <div className='badge-container'>
                                            <span>
                                                <span className={'badge-outline badge-outline-' + f.status_color}>
                                                    {f.status_description}
                                                </span> {
                                                    f.nb_pj > 0 &&
                                                    <span className='badge-outline'>
                                                        Pièce jointe : {f.nb_pj}
                                                    </span>
                                                }
                                            </span>
                                            <span className='pointer' onClick={() => setCurrentId(f.id)}>
                                                <FiMoreVertical size={20} color="#888"/>
                                            </span>
                                        </div>
                                        <HeaderFlotte auth={auth} data={f}/>
                                    </div>
                                ))
                            }
                        </InfiniteScroll>
                }
            </div>
    } </>
}