import React, { useEffect, useState } from 'react'
import {useParams} from 'react-router-dom'
import axios from 'axios'
import ShowYearHeader from '../view/ShowYearHeader';

import useToken from '../util/useToken';
import matricule from '../util/matricule';
import moment from 'moment';

import Tab from '../layout/Tab';
import LoadingPage from '../loading/LoadingPage';
import ActionDotation from './ActionDotation';

export default function showDotation({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [dotation, setDotation] = useState(null)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/mouvement_article/show_detail/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setDotation(res.data.dotation)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(dotation)
    }, [dotation]);

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
            <div>
                {
                    dotation &&
                    <>
                        <ShowYearHeader label="MVT" data={dotation} closeDetail={() => setCurrentId()} size={size}/>
                        <div className="card-container">
                            <div className='badge-container'>
                                <span>
                                <span className={`${dotation.type_mouvement == "entré" ? 'badge-outline badge-outline-enter' : dotation.type_mouvement == "sortie" && 'badge-outline badge-outline-exit'}`}>
                                        {dotation.type_mouvement}
                                    </span>
                                </span>
                            </div>
                            <h3>
                                <div>
                                    {dotation.forSite == null && matricule(dotation)} {!dotation.forSite ? dotation.nom : dotation.forSite}
                                </div>
                            </h3>
                            {
                                dotation.site &&
                                <p style={{ whiteSpace: "pre-line" }}>
                                    Site : <span className='text'>{dotation.site}</span>
                                </p>
                            }
                            {
                                dotation.demandeur &&
                                <p style={{ whiteSpace: "pre-line" }}>
                                    Superviseur : <span className='text'>{`${dotation.demandeur} <${dotation.email}>`}</span>
                                </p>
                            }
                            {
                                dotation.soft_delete == 1 &&
                                <p style={{ whiteSpace: "pre-line" }}>
                                    Observation : {dotation.observation} ({moment(dotation.date_sortie).format('LL')})
                                </p>
                            }
                            <p style={{ whiteSpace: "pre-line" }}>
                                Le : <span className='text'>{moment(dotation.date_mouvement).format('LL')}</span>
                            </p>
                            <div className='card-action'>
                                <ActionDotation auth={auth} dotation={dotation} updateData={updateData}/>
                            </div>
                        </div>
                        <Tab auth={auth} name="dotation_id" value={{id : dotation.id, type: dotation.type_mouvement}} updateData={updateData} defautUsers={defautUsers}/>
                    </>
                }
            </div>
    } </>
}
