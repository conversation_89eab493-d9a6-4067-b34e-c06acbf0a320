drop trigger IF EXISTS before_updated_site;

DELIMITER |
CREATE TRIGGER before_updated_site
BEFORE UPDATE
ON sites FOR EACH ROW
BEGIN
    if(COALESCE(NEW.resp_sup_id, 0) != COALESCE(OLD.resp_sup_id, 0) 
        or COALESCE(NEW.superviseur_id, 0) != COALESCE(OLD.superviseur_id, 0) 
        or COALESCE(NEW.group_planning_id, 0) != COALESCE(OLD.group_planning_id, 0)
    ) 
        then
            begin
                set NEW.admin_updated_at = now();
            end;
	end if;
END
| DELIMITER ;