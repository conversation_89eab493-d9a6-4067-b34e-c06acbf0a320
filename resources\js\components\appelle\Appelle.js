import React, { useEffect, useState } from 'react'
import LoadingPage from '../loading/LoadingPage';
import { useLocation } from 'react-router-dom';
import axios from 'axios';
import useToken from '../util/useToken';
import InputDate from '../input/InputDate';
import InputSelect from '../input/InputSelect';

import "./appelle.css";
import { BarChart } from '@mui/x-charts';
import moment from 'moment';
import DualContainer from '../container/DualContainer';

export default function Appelle({ auth, appelles, setAppelles, currentItem, setCurrentItem }) {

    const showClearData = true
    
    const getCurrentService = () => {
        if(moment().isAfter(moment().set({hour: 5, minute: 50, second: 0})) 
            && moment().isBefore(moment().set({hour: 17, minute: 50, second: 0})))
            return { horaire: {label: "NUIT", value: "18:00:00"}, date: moment().subtract(1, "day").toDate()}
        else if(moment().isBefore(moment().set({hour: 23, minute: 59, second: 59})))
            return { horaire: {label: "JOUR", value: "06:00:00"}, date: moment().toDate()}
        else 
            return { horaire: {label: "JOUR", value: "06:00:00"}, date: moment().subtract(1, "day").toDate()}
    }

    const [isLoading, toggleLoading] = useState(false);
    const [interval, setInterval] = useState(60);
    const [typeCall, setTypeCall] = useState("all");
    const [displays, setDisplays] = useState([])
    const [dateService, setDateService] = useState(getCurrentService().date);
    const [horaireService, setHoraireService] = useState(getCurrentService().horaire);
    const locationSearch = useLocation().search;

    useEffect(() => {
        const selectedDate = moment(dateService).format("YYYY-MM-DD") + " " + horaireService.value;
        let currentDate = moment(selectedDate).subtract(10, "minutes");
        const endDate = moment(selectedDate).subtract(10, "minutes").add(12, "hours");
        let listDisplay = []
        while (currentDate.isBefore(endDate)) {
            let currentDisplay = {
                label: moment(currentDate).add(10, "minutes").format("HH:mm"),
                begin: currentDate,
                end: moment(currentDate).add(interval, "minutes"),
                appelles: [],
                clearAppelles: []
            }
            Object.keys(appelles).forEach(key => {
                const dateCall = moment(appelles[key][0].datetime)
                if(dateCall.isAfter(currentDisplay.begin) && dateCall.isBefore(currentDisplay.end)){
                    let duration = 0;
                    appelles[key].every(s => {
                        if (["ANSWERED"].includes(s.disposition)) {
                            duration = s.duration
                            return false;
                        }
                        return true;
                    });
                    currentDisplay.appelles.push({
                        call_id: key, 
                        calltype: appelles[key][0].calltype, 
                        duration: duration, 
                        stories: appelles[key]
                    })
                }
            })
            currentDisplay.nb_call = currentDisplay.appelles.length
            currentDisplay.nb_inbound = 0
            currentDisplay.nb_outbound = 0
            currentDisplay.nb_internal = 0
            currentDisplay.nb_transfer = 0
            currentDisplay.nb_other = 0
            currentDisplay.nb_inbound_success = 0
            currentDisplay.nb_inbound_missing = 0
            currentDisplay.nb_inbound_busy = 0
            currentDisplay.nb_outbound_success = 0
            currentDisplay.nb_outbound_missing = 0
            currentDisplay.nb_outbound_busy = 0

            const inboundNumbers = []
            const outboundNumbers = []
            currentDisplay.appelles.reverse().forEach(a => {
                if(a.calltype == "Inbound"){
                    currentDisplay.nb_inbound += 1
                    if(!inboundNumbers.includes(a.stories[0].src)){
                        inboundNumbers.push(a.stories[0].src)
                        currentDisplay.clearAppelles.push(a)
                    }
                    if(a.stories.map(s => s.disposition).includes("ANSWERED")){
                        a.disposition = "ANSWERED"
                        currentDisplay.nb_inbound_success += 1
                    }
                    else if(a.stories.map(s => s.disposition).includes("NO ANSWER")){
                        a.disposition = "NO ANSWER"
                        currentDisplay.nb_inbound_missing += 1
                    }
                    else {
                        a.disposition = "BUSY"
                        currentDisplay.nb_inbound_busy += 1
                    }
                }
                else if(a.calltype == "Outbound"){
                    currentDisplay.nb_outbound += 1
                    if(!outboundNumbers.includes(a.stories[0].dst)){
                        outboundNumbers.push(a.stories[0].dst)
                        currentDisplay.clearAppelles.push(a)
                    }
                    if(a.stories.map(s => s.disposition).includes("ANSWERED")){
                        a.disposition = "ANSWERED"
                        currentDisplay.nb_outbound_success += 1
                    }
                    else if(a.stories.map(s => s.disposition).includes("NO ANSWER")){
                        a.disposition = "NO ANSWER"
                        currentDisplay.nb_outbound_missing += 1
                    }
                    else {
                        a.disposition = "BUSY"
                        currentDisplay.nb_outbound_busy += 1
                    }
                }
                else if(a.calltype == "Internal")
                    currentDisplay.nb_internal += 1
                else if(a.calltype == "Transfer")
                    currentDisplay.nb_transfer += 1
                else {
                    currentDisplay.nb_other += 1
                }
            })
            
            currentDisplay.nb_call_clear = currentDisplay.clearAppelles.length
            currentDisplay.nb_inbound_clear = 0
            currentDisplay.nb_outbound_clear = 0
            currentDisplay.nb_internal_clear = 0
            currentDisplay.nb_transfer_clear = 0
            currentDisplay.nb_other_clear = 0
            currentDisplay.nb_inbound_success_clear = 0
            currentDisplay.nb_inbound_missing_clear = 0
            currentDisplay.nb_inbound_busy_clear = 0
            currentDisplay.nb_outbound_success_clear = 0
            currentDisplay.nb_outbound_missing_clear = 0
            currentDisplay.nb_outbound_busy_clear = 0

            currentDisplay.clearAppelles.forEach(a => {
                console.log(a)
                if(a.calltype == "Inbound"){
                    currentDisplay.nb_inbound_clear += 1
                    if(a.stories.map(s => s.disposition).includes("ANSWERED")){
                        a.disposition = "ANSWERED"
                        currentDisplay.nb_inbound_success_clear += 1
                    }
                    else if(a.stories.map(s => s.disposition).includes("NO ANSWER")){
                        a.disposition = "NO ANSWER"
                        currentDisplay.nb_inbound_missing_clear += 1
                    }
                    else {
                        a.disposition = "BUSY"
                        currentDisplay.nb_inbound_busy_clear += 1
                    }
                }
                else if(a.calltype == "Outbound"){
                    currentDisplay.nb_outbound_clear += 1
                    if(a.stories.map(s => s.disposition).includes("ANSWERED")){
                        a.disposition = "ANSWERED"
                        currentDisplay.nb_outbound_success_clear += 1
                    }
                    else if(a.stories.map(s => s.disposition).includes("NO ANSWER")){
                        a.disposition = "NO ANSWER"
                        currentDisplay.nb_outbound_missing_clear += 1
                    }
                    else {
                        a.disposition = "BUSY"
                        currentDisplay.nb_outbound_busy_clear += 1
                    }
                }
                else if(a.calltype == "Internal")
                    currentDisplay.nb_internal_clear += 1
                else if(a.calltype == "Transfer")
                    currentDisplay.nb_transfer_clear += 1
                else {
                    currentDisplay.nb_other_clear += 1
                }
            })
            listDisplay.push(currentDisplay)
            currentDate.add(interval, "minutes")
        }
        setDisplays(listDisplay)

        setCurrentItem({
            selectedService : moment(dateService).format("YYYY-MM-DD") + " " + horaireService.value, 
            interval: interval, 
            data: listDisplay,
            showClearData: showClearData,
        })
    }, [interval, appelles])

    useEffect(() => {
        updateData()
    }, [dateService, horaireService])

    const updateData = () => {
        let isMounted = true;
        toggleLoading(true)
        setInterval(horaireService.value == "18:00:00" ? 30 : 60)
        const params = new URLSearchParams(locationSearch);
        const selectedDate = moment(dateService).format("YYYY-MM-DD") + " " + horaireService.value;
        params.set("begin", moment(selectedDate).subtract(10, "minutes").format("YYYY-MM-DD HH:mm:ss"));
        params.set("end", moment(selectedDate).subtract(10, "minutes").add(12, "hours").format("YYYY-MM-DD HH:mm:ss"));
        axios.get("/api/appelle?" + params, useToken()).then((res) => {
            if (isMounted) {
                if (res.data.error)
                    console.error("Error: " + res.data.error);
                else {
                    let appelles = {}
                    res.data.forEach(a => {
                        if(!appelles[a.uniqueid])
                            appelles[a.uniqueid] = []
                        appelles[a.uniqueid].push(a)    
                    });
                    setAppelles(appelles)
                }
                toggleLoading(false)
            }
        }).catch(e => {
            console.error(e);
        });
        return () => {
            isMounted = false;
        }
    }

    const getWindowSize = () => {
        const {innerWidth} = window;
        return (innerWidth > 1500 ? (innerWidth-240-800) * 1.5 : innerWidth > 1200 ? (innerWidth-240-600) * 1.5 : innerWidth > 900 ? (innerWidth-600) * 1.5 : innerWidth * 1.2);
    }   
    const [size, setSize] = useState(getWindowSize())
 
    useEffect(() => {
        function handleWindowResize() {
          setSize(getWindowSize());
        }
        window.addEventListener('resize', handleWindowResize);
        return () => {
          window.removeEventListener('resize', handleWindowResize);
        }
    }, [size])

    const chartSetting = {
        yAxis: [
          {
            label: 'appelle',
          },
        ],
        width: size,
        height: 700,
      };
    
    const translations = {
        nb_inbound: 'Entrant',
        nb_outbound: 'Sortant',
        nb_internal: 'Interne',
        nb_transfer: 'Transferé',
        nb_other: 'Non défini',
        nb_inbound_success: "Abouti",
        nb_inbound_missing: "Manqué",
        nb_inbound_busy: "Occupé",
        nb_outbound_success: "Abouti",
        nb_outbound_missing: "Injoignable",
        nb_outbound_busy: "Occupé",
        
        nb_inbound_clear: 'Entrant',
        nb_outbound_clear: 'Sortant',
        nb_internal_clear: 'Interne',
        nb_transfer_clear: 'Transferé',
        nb_other_clear: 'Non défini',
        nb_inbound_success_clear: "Abouti",
        nb_inbound_missing_clear: "Manqué",
        nb_inbound_busy_clear: "Occupé",
        nb_outbound_success_clear: "Abouti",
        nb_outbound_missing_clear: "Injoignable",
        nb_outbound_busy_clear: "Occupé",
    };
      
    const addLabels = (series) => {
        return series.map((item) => ({
          ...item,
          label: translations[item.dataKey],
          valueFormatter: (v) => (v ? `${v.toLocaleString()}` : '0'),
        }));
    }
    
    return (
        <>
            {
                isLoading ? 
                    <LoadingPage /> 
                : 
                    <div>
                        <div className="padding-container space-between">
                            <h2>Monitoring</h2>
                        </div>
                        <div id="serviceCardContainer">
                            <DualContainer>
                                <InputDate label="Date" value={dateService} onChange={setDateService}/>
                                <InputSelect label="Service" selected={horaireService} setSelected={setHoraireService} options={[
                                    {label: "JOUR", value: "06:00:00"},
                                    {label: "NUIT", value: "18:00:00"},
                                ]}/>
                            </DualContainer>
                        </div>
                        <div className='radio-input-container'>
                            <label>Type</label>
                            <div className='space-between radio-list-container'>
                                <label className="container-radio">
                                    Tous
                                    <input type="radio" name="typeCallRadio" checked={typeCall=="all"} onChange={() => setTypeCall("all")}/>
                                    <span className="checkmark"></span>
                                </label>
                                <label className="container-radio">
                                    Entrant
                                    <input type="radio" name="typeCallRadio" checked={typeCall=="inbound"} onChange={() => setTypeCall("inbound")}/>
                                    <span className="checkmark"></span>
                                </label>
                                <label className="container-radio">
                                    Sortant
                                    <input type="radio" name="typeCallRadio" checked={typeCall=="outbound"} onChange={() => setTypeCall("outbound")}/>
                                    <span className="checkmark"></span>
                                </label>
                            </div>
                        </div>
                        <div className='radio-input-container'>
                            <label>Interval</label>
                            <div className='space-between radio-list-container'>
                                <label className="container-radio">
                                    1 heure
                                    <input type="radio" name="intervalRadio" checked={interval==60} onChange={() => setInterval(60)}/>
                                    <span className="checkmark"></span>
                                </label>
                                <label className="container-radio">
                                    30 min
                                    <input type="radio" name="intervalRadio" checked={interval==30} onChange={() => setInterval(30)}/>
                                    <span className="checkmark"></span>
                                </label>
                                <label className="container-radio">
                                    15 min
                                    <input type="radio" name="intervalRadio" checked={interval==15} onChange={() => setInterval(15)}/>
                                    <span className="checkmark"></span>
                                </label>
                                <label className="container-radio">
                                    5 min
                                    <input type="radio" name="intervalRadio" checked={interval==5} onChange={() => setInterval(5)}/>
                                    <span className="checkmark"></span>
                                </label>
                            </div>
                        </div>
                        <BarChart
                            dataset={displays}
                            xAxis={[{ scaleType: 'band', dataKey: 'label'}]}
                            series={addLabels(
                                typeCall == "all" ? (showClearData ? [
                                        { dataKey:'nb_inbound_clear', stack: 'call' },
                                        { dataKey:'nb_outbound_clear', stack: 'call' },
                                        { dataKey:'nb_internal_clear', stack: 'call' },
                                        { dataKey:'nb_transfer_clear', stack: 'call' },
                                    ] : [
                                        { dataKey:'nb_inbound', stack: 'call' },
                                        { dataKey:'nb_outbound', stack: 'call' },
                                        { dataKey:'nb_internal', stack: 'call' },
                                        { dataKey:'nb_transfer', stack: 'call' },
                                    ]
                                ) : typeCall == "inbound" ? (showClearData ? [
                                        { dataKey:'nb_inbound_success_clear', stack: 'inbound' },
                                        { dataKey:'nb_inbound_missing_clear', stack: 'inbound' },
                                        { dataKey:'nb_inbound_busy_clear', stack: 'inbound' },
                                    ] : [
                                        { dataKey:'nb_inbound_success', stack: 'inbound' },
                                        { dataKey:'nb_inbound_missing', stack: 'inbound' },
                                        { dataKey:'nb_inbound_busy', stack: 'inbound' },
                                    ]
                                ) : (showClearData ? [
                                        { dataKey:'nb_outbound_success_clear', stack: 'inbound' },
                                        { dataKey:'nb_outbound_missing_clear', stack: 'inbound' },
                                        { dataKey:'nb_outbound_busy_clear', stack: 'inbound' },
                                    ] : [
                                        { dataKey:'nb_outbound_success', stack: 'inbound' },
                                        { dataKey:'nb_outbound_missing', stack: 'inbound' },
                                        { dataKey:'nb_outbound_busy', stack: 'inbound' },
                                    ]
                                )
                            )}
                            colors={
                                typeCall == "all" ? 
                                    ['#00bcd4', '#8bc34a', '#ff9800', '#9c27b0']
                                : typeCall == "inbound" ?
                                    ['#8bc34a', '#e91e63', '#ff9800', '#9c27b0']
                                :
                                    ['#8bc34a', '#e91e63', '#ff9800', '#9c27b0']
                                }
                            {...chartSetting}
                        />
                    </div>
            }
        </>
    )
}
