import React, { useEffect, useState } from 'react'
import axios from 'axios'
import useToken from '../util/useToken'
import matricule from '../util/matricule';
import InputCheckBox from '../input/InputCheckBox';

export default function DoublonModal({closeModal, updateData, employe}) {
    const [doublons, setDoublons] = useState([]);

    useEffect(() => {
        axios.get("/api/employe/duplicated_name/" + employe.id +"/"+ employe.nom, useToken())
        .then((res) => {
            setDoublons(res.data)
        })
        .catch((err) => {
            console.error(err)
        })
    }, [])

    const handleCheckboxChange = (id) => {
        setDoublons(doublons.map(d =>
            d.id === id ? { ...d, ignore_name: !d.ignore_name } : d
        ));
    };

    const handleConfirm = () => {
        axios.post("/api/employe/update_ignore_name", { doublons }, useToken())
            .then(() => {
                updateData();
                closeModal();
            })
            .catch((err) => {
                console.error(err);
            });
    };

    return (
        <div>
            <div className='modal'>
                <div>
                    <h2>
                        Liste des doublons
                    </h2>
                    <div className='list-container'>
                        <ul>
                            {
                                doublons && doublons.length > 0 ?
                                    doublons.map((d) => (
                                        <li key={d.id}>
                                            <div className='space-between'>
                                                <div>[{matricule(d)}]{d.nom}</div>
                                                <InputCheckBox 
                                                    checked={d.ignore_name}
                                                    onChange={() => handleCheckboxChange(d.id)}
                                                />
                                            </div>
                                        </li>
                                    ))
                                :
                                    <li>
                                        Aucun doublon trouvé.
                                    </li>
                            }
                        </ul>
                    </div>
                    <div className='form-button-container'>
                        <button className='primary' onClick={handleConfirm}>Confirmer</button>
                        <button onClick={closeModal}>Annuler</button>
                    </div>
                </div>
            </div>
        </div>
    )
}