<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Prime;
use App\Models\Pointage;
use App\Models\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\HistoriqueController;
use App\Http\Controllers\MailController;

class PrimeController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }
    
    public function show($id){
        $prime = DB::select("SELECT pr.id, pr.user_id, pr.employe_id, pr.superviseur_id, pr.site_id, pr.pointage_id,
            pr.status, pr.created_at, pr.objet, pr.montant, pr.motif, pr.date_pointage,
            stat.description as 'status_description', stat.color as 'status_color',
            e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
            st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
            FROM primes pr
            LEFT JOIN employes e on e.id = pr.employe_id
            LEFT JOIN sites st on st.idsite = pr.site_id
            LEFT JOIN users us on us.id = pr.user_id
            LEFT JOIN users sup on sup.id = pr.superviseur_id
            LEFT JOIN `status` stat on stat.name = pr.status
            where pr.id = ?", [$id])[0];
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj
            WHERE pj.prime_id = ?
            order by pj.created_at desc", [$id]);
        $prime->nb_pj = count($pieces);
        return response()->json($prime);
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = "pr.id = '". $request->id ."'";
        else {
            if($request->status)
                $searchArray[] = "pr.status = '". $request->status ."'";
            if($request->created_at)
                $searchArray[] = " pr.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "pr.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " pr.user_id = " . $request->user_id . " ";
            if($request->employe_id)
                $searchArray[] = " pr.employe_id = " . $request->employe_id . " ";
            if($request->superviseur_id)
                $searchArray[] = " pr.superviseur_id = " . $request->superviseur_id . " ";
            if($request->date_pointage)
                $searchArray[] = " pr.date_pointage = '" . $request->date_pointage . "' ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by pr.id desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by pr.id desc limit ". $request->offset . ", 30";

        return compact('query_where', 'query_and');
    }

    public function index(Request $request){
        $search = $this->search($request);
        if(in_array($request->user()->role, ['resp_rh', 'rh', 'validateur', 'access', 'daf'])){
            $primes = DB::select("SELECT pr.id, pr.user_id, pr.employe_id, pr.superviseur_id, pr.site_id, pr.pointage_id,
                pr.status, pr.created_at, pr.objet, pr.montant, pr.motif, pr.date_pointage,
                stat.description as 'status_description', stat.color as 'status_color',
                e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
                FROM primes pr
                LEFT JOIN employes e on e.id = pr.employe_id
                LEFT JOIN sites st on st.idsite = pr.site_id
                LEFT JOIN users us on us.id = pr.user_id
                LEFT JOIN users sup on sup.id = pr.superviseur_id
                LEFT JOIN `status` stat on stat.name = pr.status
                ". $search["query_where"]
                , []);
        }
        else if(in_array($request->user()->role, ['superviseur','resp_sup','resp_op'])){
            $primes = DB::select("SELECT pr.id, pr.user_id, pr.employe_id, pr.superviseur_id, pr.site_id, pr.pointage_id,
                pr.status, pr.created_at, pr.objet, pr.montant, pr.motif, pr.date_pointage,
                stat.description as 'status_description', stat.color as 'status_color',
                e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
                FROM primes pr
                LEFT JOIN employes e on e.id = pr.employe_id
                LEFT JOIN sites st on st.idsite = pr.site_id
                LEFT JOIN users us on us.id = pr.user_id
                LEFT JOIN users sup on sup.id = pr.superviseur_id
                LEFT JOIN `status` stat on stat.name = pr.status
                where pr.user_id = ? " . $search['query_and']
                , [$request->user()->id]);
        }
        else 
            return response(["error" => "EACCES"]);

        if(count($primes)> 0){
            $pieces = DB::select("SELECT prime_id, COUNT(id) as nb FROM piece_jointes pj 
                WHERE pj.prime_id in (" . implode(',', array_column($primes, 'id')) . ")
                GROUP BY pj.prime_id");
            foreach ($primes as $pr) {
                $pr->nb_pj = 0;
                foreach ($pieces as $pj) {
                    if($pr->id == $pj->prime_id)
                        $pr->nb_pj = $pj->nb;
                }
            }
        }
        return response(compact('primes'));
    }
    
    protected function validateAndSetPrime($request, $prime){
        $auth = $request->user();
        $prime->objet = null;
        $prime->montant = null;
        if(in_array($request->user()->role, ['superviseur', 'resp_sup', 'resp_op','resp_rh','rh'])){
            if($request->absence){
                $validator = Validator::make($request->all(), [
                    'employe_id' => 'required',
                    'motif' => 'required',
                ]);
            }
            else {
                $rules = ['employe_id' => 'required'];
                $rules['motif'] = 'required';
                $validator = Validator::make($request->all(), $rules);
            }
            $prime->superviseur_id = $auth->id;
        }
        else if($auth->role == "validateur") {
            $validator = Validator::make($request->all(), [
                'employe_id' => 'required',
                'motif' => 'required',
                'montant' => 'required|numeric|min:1',
            ]);
            $prime->montant = $request->montant;
        }
        if($prime->id == null || $auth->id == $prime->user_id){
            $prime->employe_id = $request->employe_id; 
            $prime->pointage_id = null;
            $prime->site_id = $request->site_id;
            if($request->date_service){
                $prime->date_pointage = $request->date_service;
            }
            // $current_date = new \DateTime();
            // $service = new \DateTime($request->date_service);
            // $validator = Validator::make($request->all(), [
            //     'date_service' => 'required|date_service|before_or_equal:today'
            // ]);
            // if($request->pointage_id){
            //     $prime->pointage_id = $request->pointage_id;
            //     $prime->site_id = Pointage::find($request->pointage_id)->site_id;
            //     $prime->date_pointage = Pointage::find($request->pointage_id)->date_pointage;
            // }

            $prime->motif = $request->motif;
        }
        return $validator;
    }

    public function store(Request $request){
        $auth = $request->user();
        if(in_array($auth->role, ["superviseur", "resp_sup", "resp_op", "resp_rh", "rh", "validateur"])){
            $prime = new Prime();

            $validator = $this->validateAndSetPrime($request, $prime);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $prime->user_id = $auth->id;
            $prime->status = "demande";

            $prime->created_at = new \DateTime();
            $prime->updated_at = new \DateTime();

            if($prime->save()){
                HistoriqueController::new_prime($request, $prime);
                if(in_array($auth->role, ["superviseur", "resp_sup", "resp_op"])){
                    $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                        from users us
                        left join users ur on ur.id = us.real_email_id 
                        where us.role = 'resp_rh'", []);
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                }
                else if(in_array($auth->role, ["resp_rh"])){
                    $superviseur =  DB::select("SELECT u.name, coalesce(u2.email, u.email) as 'email' from users u
                        left join users u2 on u2.id = u.real_email_id
                        WHERE u.id = ?", [$prime->superviseur_id])[0];
                }
                return response(["success" => "Prime bien envoyée", "id" => $prime->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function edit(Request $request, $id){
        $prime = Prime::find($id);
        $old_prime = clone $prime;
        $auth = $request->user();
        if($auth->role == "validateur" && $prime->status == "traite"){
            $validator = $this->validateAndSetPrime($request, $prime);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $prime->montant = $request->montant;
            $prime->updated_at = new \DateTime();

            if($prime->save()){
                HistoriqueController::update_prime($request, $old_prime, "Prime modifié");
                return response(["success" => "Prime modifié", "id" => $prime->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function do_traite(Request $request, $id){
        $prime = Prime::find($id);
        $old_prime = clone $prime;
        if($request->user()->role == "resp_rh" && $prime->status == "demande"){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            
            $prime->status = "traite";
            $prime->updated_at = new \DateTime();

            if($prime->save()){
                HistoriqueController::update_prime($request, $old_prime, "Prime en cours de traitement");
                return response(["success" => "Prime bien envoyée", "id" => $prime->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $prime = Prime::find($id);
        $old_prime = clone $prime;
        $auth = $request->user();
        if(in_array($auth->role, ["superviseur", "resp_sup", "resp_op"])){
            if($request->user()->id == $prime->user_id && $prime->status == "draft"){
                $validator = $this->validateAndSetPrime($request, $prime);
                if ($validator->fails())
                    return response(['error' => $validator->errors()->first()]);

                $prime->status = "demande";
                $prime->updated_at = new \DateTime();

                if($prime->save()){
                    HistoriqueController::update_prime($request, $old_prime, "Renvoie de la demande");

                    $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                        from users us
                        left join users ur on ur.id = us.real_email_id 
                        where us.role = 'resp_rh'", []);
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                    //MailController::prime($request, $prime->id, "Renvoie de la demande de prime", $emails);
                    return response(["success" => "Renvoie de la demande réussi", "id" => $prime->id]);
                }
                return response(["error" => "Erreur d'envoi, réessayez"]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function request_validation(Request $request, $id){
        $prime = Prime::find($id);
        if($request->user()->role == 'resp_rh' && in_array($prime->status, ['demande'])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $prime->status = 'validation';
            $prime->updated_at = new \DateTime();

            $prime->note_id = HistoriqueController::action_prime($request, "Requête de validation", $id);
            if($prime->save()){
                return response(["success" => "Demande de validation envoyé", "id" => $prime->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function reply_validation(Request $request, $id){
        $prime = Prime::find($id);
        $old_prime = clone $prime;
        if($request->user()->role == 'validateur' && $prime->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'montant' => 'required|numeric|gt:0',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $prime->status = 'traite';
            $prime->montant = $request->montant;
            $prime->updated_at = new \DateTime();

            $prime->note_id = HistoriqueController::action_montant_prime($request, "Réponse à la demande", $prime->id);
            if($prime->save()){
                $users = DB::select("SELECT us.id from users us where us.role = 'resp_rh'", []);
                foreach ($users as $u) {
                    Notification::create([
                        'historique_id' => $prime->note_id,
                        'receiver_id' => $u->id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                return response(["success" => "Réponse de la demande envoyé", "id" => $prime->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function cancel_validation(Request $request, $id){
        $prime = Prime::find($id);
        if($request->user()->role == 'resp_rh' && $prime->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $prime->status = 'traite';
            $prime->updated_at = new \DateTime();

            $prime->note_id = HistoriqueController::action_prime($request, "Demande de validation annulé", $id);
            if($prime->save()){
                return response(["success" => "Demande de validation annulé", "id" => $prime->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_prime(Request $request, $id){
        $prime = Prime::find($id);
        if(
            ($request->user()->id == $prime->user_id && in_array($request->user()->role, ['superviseur','resp_sup','resp_op'])  && in_array($prime->status, ['demande'])) || 
            (in_array($request->user()->role, ['resp_rh']) && in_array($prime->status, ['demande'])) ||
            (in_array($request->user()->role, ['validateur']) && in_array($prime->status, ['validation', 'traite']))
        ){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $prime->objet = null;
            $prime->montant = null;
            $prime->status = 'draft';
            $prime->updated_at = new \DateTime();

            $prime->note_id = HistoriqueController::action_prime($request, "Prime annulé", $id);
            if($prime->save()){
                if(in_array($request->user()->role, ["superviseur","resp_sup","resp_op"])){
                    $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                        from users us
                        left join users ur on ur.id = us.real_email_id 
                        where us.role = 'resp_rh'", []);
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                    //MailController::prime($request, $prime->id, "Prime annulé", $emails);
                }
                else if($prime->superviseur_id){
                    $superviseur =  DB::select("SELECT u.name, coalesce(u2.email, u.email) as 'email' from users u
                        left join users u2 on u2.id = u.real_email_id
                        WHERE u.id = ?", [$prime->superviseur_id])[0];
                    //MailController::prime($request, $prime->id, "Prime annulé"
                    //    , [["address" => $superviseur->email, "name" => $superviseur->name]]);
                }
                return response(["success" => "Prime annulé", "id" => $prime->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done(Request $request, $id){
        $prime = Prime::find($id);
        if($request->user()->role == 'resp_rh' && in_array($prime->status, ['traite'])){
            $verifyDatePaie = DB::select("SELECT * FROM paies WHERE date_paie ='$request->date_paie' and  (status = 'traite' or status = 'done') and employe_id = '$request->employ_id'");
            if (count($verifyDatePaie) > 0) {
                return (["error" => "Date de paie invalide"]);
            }
            $validator = Validator::make($request->all(), [
                'note' => 'required',
                'date_paie' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $prime->status = 'done';
            $prime->date_paie = $request->date_paie;
            $prime->updated_at = new \DateTime();

            $prime->note_id = HistoriqueController::action_prime($request, "Prime terminé", $id);
            if($prime->save()){
                $paie = DB::select("SELECT * FROM paies WHERE date_paie = '$prime->date_paie' AND (status = 'demande') AND employe_id = $prime->employe_id");
                if (count($paie) > 0) {
                    $new_request = clone $request;
                    $new_request->employe_id = $prime->employe_id;
                    $new_request->date_paie = $prime->date_paie;
                    $new_request->paie_id = $paie[0]->id;
                    PaieController::RecalculePaie($new_request);
                }
                return response(["success" => "Prime terminé", "id" => $prime->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
}
