import React, { useEffect, useState } from 'react'
import useToken from '../util/useToken'
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import { Link, useLocation } from 'react-router-dom';
import moment from 'moment';
import "./paie.css";
import SearchBar from '../input/SearchBar';
import StatusLabel from '../input/StatusLabel';
import { upperCase } from 'lodash';
import ModalDoneAll from './ModalDoneAll';
import ModalExportExcel from './Fiche/ModalExportExcel';

export default function Paie({ auth, currentId, setCurrentId, paies, setPaies }) {
    const locationSearch = useLocation().search;
    const [isLoading, toggleLoading] = useState(false);
    const [allDataLoaded, setDataLoaded] = useState(false);
    const [showDoneAll, toggleDoneAll] = useState(false);
    const [showExport, toggleExport] = useState(false)

    let searchItems = [
        { label: "Date de paie", name: "date_paie", type: "dateMonth" },
        { label: "Date de création", name: "created_at", type: "date" },
        { label: 'Status', name: 'status', type: 'number' },
        { label: "Réference", name: "id", type: 'number' },
        { label: 'Employé', name: "employe_id", type: "string" },
        { label: "Site", name: "site_id", type: "number" },
        { label: "Agence", name: "agence_id", type: "string" }
    ]
    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true);
            setDataLoaded(true);
            params.set("offset", 0);
        } else params.set("offset", paies.length);
        axios
            .get("/api/paie?" + params, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.error)
                        console.error("ERREUR3", res.data.error);
                    else {
                        if (initial) setPaies(res.data.paies);
                        else {
                            const list = paies.slice().concat(res.data.paies);
                            setPaies(list);
                        }
                        setDataLoaded(res.data.paies.length < 30);
                    }
                    toggleLoading(false);
                }
            })
            .catch((e) => {
                console.error(e);
            });
        return () => {
            isMounted = false;
        };
    };
    useEffect(() => {
        let isMounted = true;
        if (isMounted) updateData(true);
        return () => {
            isMounted = false;
        };
    }, [locationSearch]);
    const fetchMoreData = () => {
        setTimeout(() => {
            updateData();
        }, 300);
    };
    const NbWithSpace = (number) => {
        try {
            return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
        } catch (error) {
            console.error("erreur");
        }
    }
    
    return (
        <>
            {
                isLoading ? <LoadingPage />
                :
                <div>
                    <div className="padding-container space-between">
                        <h2>
                            Paie
                        </h2>
                        {
                            ['resp_rh'].includes(auth.role) &&
                            <Link className="btn btn-primary" to="/paie/add">Nouvelle</Link>
                        }
                    </div>
                    <SearchBar listItems={searchItems} />
                    {paies.length == 0 ?
                        <h3 className="center secondary">Aucun données trouvé</h3>
                    :
                        <div>
                            <div className='action-container'>
                                <span onClick={()=>toggleDoneAll(true)}>Terminer</span>    
                                <span onClick={()=>toggleExport(true)}>Exporter</span>    
                            </div>
                            {showDoneAll && auth.role == 'resp_rh' && <ModalDoneAll updateData={updateData} closeModal={() => toggleDoneAll(false)} />}
                            {showExport && <ModalExportExcel closeModal={() => toggleExport(false)} />}
                            
                            <InfiniteScroll
                                dataLength={paies.length}
                                next={fetchMoreData}
                                hasMore={!allDataLoaded}
                                loader={<LoadingPage />}
                            >
                                <div className="line-container ">
                                    <div className='row-paie'>
                                        <b className='matricule-employe'>Date</b>
                                        <b className='nom-employe' style={{ width: "40%", minWidth: "40%" }}>Employé</b>
                                        <b className="status-line"><StatusLabel color="grey" /></b>
                                        <b className="nombre-heure">H</b>
                                        <b className="salaire-net">Salaire Net</b>
                                        <b className='agence-employe'>Agence</b>
                                    </div>
                                </div>
                                {paies.map((paie) => (
                                    <div key={paie.id} 
                                        className={`line-container ${currentId && currentId == paie.id ? "selected" : ""}`} 
                                        onClick={() => setCurrentId(paie.id)}
                                    >
                                        <div className="row-paie" >
                                            <span className='date-paie'>{upperCase(moment(paie.date_paie).format("MMM YYYY"))}</span>
                                            <span className='nom-employe' style={{ width: "40%", minWidth: "40%" }}>{paie.employe}</span>
                                            <span className="status-line"><StatusLabel color={paie.color} /></span>
                                            <span className='nombre-heure'>{paie.nb_heure_travaille}</span>
                                            <span className='salaire-net'>
                                                {paie.net_a_payer && NbWithSpace(paie.net_a_payer.toFixed(Number.isInteger(paie.net_a_payer) ? 0 : 2))} Ar
                                            </span>
                                            <span className='agence-employe'>{paie.agence}</span>
                                        </div>
                                    </div>
                                ))}
                            </InfiniteScroll>
                        </div>
                    }
                    </div>
            }
        </>
    )
}
