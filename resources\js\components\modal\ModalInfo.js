import { createPortal } from "react-dom";

export default function ModalInfo({dayVisite, nightVisite, closeModal}) {
    return createPortal(
        <div className="modal">
            <div>
                <h3> 
                    <span>Remarque!</span> <br/>
                    {dayVisite && <span>Veuillez envoyer vos visites de poste jours avant 20 heures du soir</span>}
                    {nightVisite && <span>Veuillez envoyer vos visites de poste nuits avant 08 heures du matin</span>}
                </h3>
                <div className="form-button-container">
                    <button onClick={closeModal}>OK</button>
                </div>
            </div>
        </div>,
        document.body
    )
}