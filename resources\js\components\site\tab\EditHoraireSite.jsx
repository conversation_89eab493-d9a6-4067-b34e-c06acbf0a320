import axios from "axios";
import { useEffect, useState } from "react";
import useToken from "../../util/useToken";

export default function EditHoraireSite({closeModal, horaires, site, updateData}) {
    const header = ['Lun', 'Mar', 'Mer', 'Jeu', 'V<PERSON>', '<PERSON>', 'Dim', '<PERSON><PERSON><PERSON><PERSON>'];
    const backgroundColor = ['indigo', 'bg-orange', 'purple', 'green']
    const [disabled, setDisabled] = useState(false)
    const [numberWithColor, setNumberWithColor] = useState([])
    const [horaire, setHoraire] = useState({
        day: horaires?.day ?? Array(8).fill(0),
        night: horaires?.night ?? Array(8).fill(0)
    });

    const soustraireMultipleDe4 = (n) => {
        let multipleDe4 = Math.floor(n / 4) * 4;
        return n - multipleDe4;
    }

    const handleChange = (type, index, value) => {
        setHoraire(prev => ({
            ...prev,
            [type]: prev[type].map((h, i) => (i === index ? (Number(value) > 0 ? Number(value) : 0) : h))
        }));
    };

    const allUniqueNumbers = []

    const isInclude = (value) => {
        const tempNumberColor = []
        if(value > 0 && !allUniqueNumbers.includes(value)) {
            allUniqueNumbers.push(value)
            allUniqueNumbers.sort((a, b) => a - b);
        }
        if(allUniqueNumbers.length <= backgroundColor.length) {
            for(let i = 0; i < allUniqueNumbers.length; i++) {
                if(!tempNumberColor.find(n => n.value == allUniqueNumbers[i]))
                    tempNumberColor.push({color: backgroundColor[i], value: allUniqueNumbers[i]})
            }
            setNumberWithColor(tempNumberColor)
        }

        else if(allUniqueNumbers.length > backgroundColor.length) {
            for(let i = 0; i < allUniqueNumbers.length; i++) {
                if(!tempNumberColor.find(n => n.value == allUniqueNumbers[i])){
                    if(i < backgroundColor.length) {
                        tempNumberColor.push({color: backgroundColor[i], value: allUniqueNumbers[i]})
                    }
                    else {
                        tempNumberColor.push({color: backgroundColor[soustraireMultipleDe4(i)], value: allUniqueNumbers[i]})
                    }
                }

            }
            setNumberWithColor(tempNumberColor)
        }
    }

    useEffect(() => {
        horaire.day.map((h, i) => isInclude(h))
        horaire.night.map((h, i) => isInclude(h))
    }, [horaire])

    const handleSubmit = () =>{
        setDisabled(true)
        let data = {
            day: horaire.day,
            night: horaire.night
        }
        axios.post('/api/site/store_horaire/'+ site.id, data, useToken())
        .then((res) => {
            if(res.data.error){
                console.error(res.data.error)
                setDisabled(false)
            }
            else{
                setDisabled(false)
                updateData()
                closeModal()
            }
        })
        .catch((e) => {
            setDisabled(false)
            console.error(e)
        })
    }
    return (
        <>
            <div className='modal'>
                <div>
                    <h2>Horaire</h2>
                    <div className="card-container">
                        <div className="calendar-header">
                            {header.map((hr) => (
                                <div key={hr} className="calendar-header-day">
                                    {hr}
                                </div>
                            ))}
                        </div>
                        <div className="calendar-header">
                            {horaire.day.map((hr, index) => (
                                <input 
                                    type="number"
                                    key={index} 
                                    className={"horaire-content " + (numberWithColor.find(nc => nc.value === hr)?.color || "white")}
                                    style={{ color: '#fafafa', fontWeight: 'bold', fontSize: '1.7em', width: "20px" }}
                                    value={horaire.day[index]}
                                    onChange={(e) => handleChange("day", index, e.target.value)}
                                />
                            ))}
                        </div>
                        <div className="calendar-header">
                            {horaire.night.map((hr, index) => (
                                <input 
                                    type="number"
                                    key={index} 
                                    className={"horaire-content " + (numberWithColor.find(nc => nc.value === hr)?.color || "white")}
                                    style={{ color: '#fafafa', fontWeight: 'bold', fontSize: '1.7em', width: "20px" }}
                                    value={horaire.night[index]}
                                    onChange={(e) => handleChange("night", index, e.target.value)}
                                />
                            ))}
                        </div>
                    </div>
                    <div className='form-button-container'>
                        <button type='button' className='btn-primary' disabled={disabled} onClick={() => handleSubmit()}>Enregistrer</button>
                        <button type='button' onClick={() => closeModal()}>Annuler</button>
                    </div>
                </div>
            </div>
        </>
    );
};