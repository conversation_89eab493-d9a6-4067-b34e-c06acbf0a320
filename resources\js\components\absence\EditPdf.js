import { useState,useEffect } from 'react'
import PdfConge from './pdf/PdfConge'
import {PDFDownloadLink} from '@react-pdf/renderer'
import InputSelect from '../input/InputSelect'
import InputText from '../input/InputText'
import useToken from '../util/useToken'
import moment from 'moment'
import PdfMisePied from '../sanction/pdf/PdfMisePied'
import PdfMisePiedBouton from '../sanction/pdf/PdfMisePiedBouton'
import PdfAvertTag from '../sanction/pdf/PdfAvertTag'
import PdfAvertSommeilRetard from '../sanction/pdf/PdfAvertSommeilRetard'
import PdfAvertBiometrique from '../sanction/pdf/PdfAvertBiometrique'
import matricule from '../util/matricule';

export default function EditPdf({header,absence,setShowPrintModal}) {
    const [societeType, setSocieteType] = useState("")
    const [enablePrint, setEnablePrint] = useState(false)
    const [isLoading, toggleLoading] = useState(true)
    const [droit, setDroit] = useState(null)
    const [lieu, setLieu] = useState("")
    const [adresse, setAdresse] = useState("")
    const [phone, setPhone] = useState("")
    const [genre, setGenre] = useState("");
    const validPhone = /^(032|034|038)\d{7}$/
    const [nbJour, setNbJour] = useState("");
    const [motif, setMotif] = useState("");
    const [societeSt, setSocieteSt] = useState("")
    const societeList = [
        'SOIT TANA',
        'SOIT TAMATAVE',
        'DIRICKX GUARD'
    ]   

    useEffect(() => {
        if (absence.depart && absence.retour) {
            setNbJour((moment(absence.retour).diff(moment(absence.depart), 'hours') / 24).toString().padStart(2, "0") )
        }
        else setNbJour(absence.nb_jour.toString().padStart(2, "0") )
    }, [])
    
    useEffect(() => {
        let isMounted = true
        axios.get('/api/absence/conge_done/' + absence.id, useToken())
        .then((res) => {
            if(isMounted){
                const data_absence = res.data.employe
                const conges = res.data.conges
                data_absence.droit = (
                    (moment().diff(moment(data_absence.date_embauche), 'days') / 30.5 * 2.5)
                    - (conges.map(c => moment(c.retour).diff(moment(c.depart), 'hours')).reduce((a, b) => a+b, 0) / 24)
                )
                if((data_absence.droit % 1) >= 0.5)
                    data_absence.droit = Math.round(data_absence.droit) + 0.5
                else
                    data_absence.droit = Math.round(data_absence.droit)
                setDroit(data_absence.droit)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })

        return () => { isMounted = false };
    }, []);

    useEffect(() => {
        if (absence.type_absence == "mis_a_pied") {
            setEnablePrint(genre.trim() && (absence.societe_id == 3?societeType.trim():true));
        }
        else if(absence.societe_id == 3){
            setEnablePrint(societeType.trim() && lieu.trim() && adresse.trim() && validPhone.test(phone))
        }else {
            setEnablePrint(lieu.trim() && adresse.trim() && validPhone.test(phone))
        }
        
    },[societeType,lieu,adresse,phone, genre])

    return <div className='modal'>
        <div style={{maxHeight: '60rem',overflowY: 'auto'}}>
            <h3>{header}</h3>
            {absence.type_absence == 'mis_a_pied' ?
                <>
                    <InputSelect label='Genre' selected={genre} setSelected={setGenre} options={["Monsieur", "Madame"]} />
                    {
                        absence.societe_id == 3 &&
                            <InputSelect
                                label='Societe'
                                selected={societeType}
                                setSelected={setSocieteType}
                                options={["SOIT","DIRICKX GUARD"]}
                                required 
                            />
                    }
                    <InputText label="Motif" value={motif} onChange={setMotif} />
                </>
                :
                <>
                    {absence.societe_id == 3 &&
                        <InputSelect 
                            label='Societe'
                            selected={societeType}
                            setSelected={setSocieteType}
                            options={societeList}
                            required
                        />
                    }                  
                    <InputText
                        required
                        label="Lieu de déposition"
                        placeholder='ex: Antananarivo'
                        value={lieu}
                        onChange={setLieu}
                    />         
                    <InputText
                        required
                        label="Adresse de l'employe"
                        placeholder='Lot ...'
                        value={adresse}
                        onChange={setAdresse}
                    />
                    <InputText
                        required
                        label="Téléphone de l'employe"
                        placeholder='ex: 0321100000'
                        value={phone}
                        onChange={setPhone}
                    />
                </>
            }
            <div className='form-button-container'>
                {!enablePrint &&
                    <button className='btn-primary' disabled>Imprimer</button>
                }
                {enablePrint &&
                    (absence.type_absence != "mis_a_pied" ? 
                        <PDFDownloadLink 
                            document={<PdfConge 
                                            absence={absence} 
                                            societeType={societeType} 
                                            droit={droit}
                                            lieu={lieu}
                                            adresse={adresse}
                                            phone={phone}
                                        />} 
                            fileName={"FICHE_" + (absence.type_absence == "conge" ? "CONGE_" : "PERMISSION_") +                            
                                (matricule(absence))
                            }>
                            {({ loading }) =>
                                loading ? (
                                    <span></span>
                                ) : (
                                    <button style={{
                                        display: 'inline-block', 
                                        fontSize: '12pt', 
                                        padding: '10px 15px',
                                        width: '150px',
                                        border: 'none',
                                        color: '#fff',
                                        cursor: 'pointer'}} 
                                        className='btn-primary'
                                        onClick={() => {setShowPrintModal(false);setSocieteType("")}}>
                                            Continuer
                                    </button>
                                )
                            }
                    </PDFDownloadLink> 
                : 
                    <PDFDownloadLink 
                        document={
                            absence.type_mis_a_pied == "BOUTON" ? 
                                <PdfMisePiedBouton absence={absence} 
                                    motif={motif} 
                                    genre={genre} 
                                    nbJour={nbJour ? nbJour : absence.nb_jour} 
                                    societeSt={societeType}
                                />
                            : absence.type_mis_a_pied == "TAG" ?
                                <PdfAvertTag sanction={absence} 
                                    motif={motif} 
                                    avertissementGrade="mis_a_pied"
                                    genre={genre}
                                    nbJour={nbJour ? nbJour: absence.nb_jour} 
                                    societeSt={societeType}
                                />
                            : (absence.type_mis_a_pied == "SOMMEIL" || absence.type_mis_a_pied == "SOMMEIL/RETARD") ?
                                <PdfAvertSommeilRetard sanction={absence} 
                                    genre={genre}
                                    motif={motif}
                                    avertissementGrade={"mis_a_pied"}
                                    nbJour={nbJour}
                                    societeSt={societeType}
                                />
                            : absence.type_mis_a_pied == "BIOMETRIQUE" ?
                                <PdfAvertBiometrique sanction={absence}
                                    genre={genre}
                                    motif={motif}
                                    avertissementGrade={"mis_a_pied"}
                                    nbJour={nbJour}
                                    societeSt={societeType}
                                />
                            :
                            <PdfMisePied absence={absence} 
                                motif={motif} 
                                genre={genre} 
                                jour={nbJour ? nbJour: absence.nb_jour} 
                                societeSt={societeType}
                            /> 
                        }
                        fileName={"MISE_A_PIED_" + absence.type_mis_a_pied + "_" + (matricule(absence)
                        )}>
                        {({ loading }) => loading ? (<span></span>) : (
                            <button style={{
                                display: 'inline-block',
                                fontSize: '12pt',
                                padding: '10px 15px',
                                width: '150px',
                                border: 'none',
                                color: '#fff',
                                cursor: 'pointer'
                            }}
                                className='btn-primary'
                                onClick={() => { setShowPrintModal(false); setSocieteType("") }}>
                                Imprimer
                            </button>
                        )}
                    </PDFDownloadLink>)
                }               
                <button onClick={() => {setShowPrintModal(false);setSocieteType("")}}>Annuler</button>
            </div>
        </div>
    </div>
}
