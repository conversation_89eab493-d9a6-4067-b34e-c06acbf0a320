import React, { useState } from 'react';

import NoteModal from '../input/NoteModal';
import { Link } from 'react-router-dom';

export default function ActionPartVariable({auth, partVariable, updateData}) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [currentAction, setAction] = useState(null)

    const handleResendPartVariable = (id) => {
        setAction({
            header: "Renvoyer la demande de part variable",
            request: "/api/part_variable/send_back/" + id,
        })
        toggleNoteModal(true)
    }

    const handleCancel = (id) => {
        setAction({
            header: "Annuler la part variable",
            request: "/api/part_variable/cancel/" + id
        })
        toggleNoteModal(true)
    }

    const handleDiscard = (id) => {
        setAction({
            header: "Non attribuer la part variable",
            request: "/api/part_variable/discard/" + id
        })
        toggleNoteModal(true)
    }
    
    const handleDone = (id) => {
        setAction({
            header: "Accorder la part variable",
            request: "/api/part_variable/save_done/" + id,
        })
        toggleNoteModal(true)
    }
    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction}
                updateData={() => updateData()}
                closeModal={() => toggleNoteModal(false)}
            /> 
            
        }
        <div className='action-container'>
            {
                (["validation"].includes(partVariable.status) && ["validateur"].includes(auth.role)) && 
                <span onClick={() => handleDone(partVariable.id)}>Confirmer</span>
            }
            {
                (["draft"].includes(partVariable.status) && auth.id == partVariable.user_id) && 
                <span>
                    <Link to={"/part-variable/edit/" + partVariable.id}>Modifier</Link>
                </span>
            }
            {
                (["validation"].includes(partVariable.status) && ["validateur"].includes(auth.role)) && 
                <span onClick={() => handleDiscard(partVariable.id)}>Refuser</span>
            }
            {
                (["validation"].includes(partVariable.status) && auth.id == partVariable.user_id) && 
                <span onClick={() => handleCancel(partVariable.id)}>Annuler</span>
            }
            {
                (
                    ["draft"].includes(partVariable.status) && 
                    (auth.id == partVariable.user_id)
                ) && 
                <span onClick={() => handleResendPartVariable(partVariable.id)}>Renvoyer</span>
            }
        </div>
    </div>
}