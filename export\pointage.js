const moment = require('moment')
const mysql = require('mysql')
const Excel = require("exceljs")

moment.locale('fr')


const {db_config_admin, sendMail } = require("../auth")
const poolAdmin = mysql.createPool(db_config_admin)

const sqlSelectDatePointageExport = "SELECT value FROM params p WHERE p.key = 'last_export_pointage'"
function sqlUpdateLastPointageExport(date) {
    console.log("date: " + date)
    return "UPDATE params p SET p.value = '" + date + "' WHERE p.key = 'last_export_pointage'"
}

const isTask = (process.argv[2] == "task")
const destination_pointage = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

const destination_test = ["<EMAIL>"]

const sqlSelectGroupPointage = "select id, nom FROM group_pointage_sites"

function sqlSelectPointage(interval) {
    return "select p.id, p.employe_id, p.site_id, p.date_pointage, s.nom as 'site', s.heure_contrat FROM pointages p " +
        "left join sites s on s.idsite = p.site_id " +
        "WHERE (p.soft_delete is null or p.soft_delete = 0) " +
        "and p.date_pointage > '" + interval.begin + "' and p.date_pointage < '" + interval.end + "'"
}

function sqlSelectAgent(ids) {
    return `
        SELECT e.id, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.nom,
            s.nom AS 'site', s.heure_contrat, s.group_pointage_id, e.real_site_id AS 'site_id'
        FROM employes e
        LEFT JOIN sites s ON s.idsite = e.real_site_id
        WHERE e.id IN (${ids.join(", ")})
        GROUP BY e.id, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.nom,
                 s.nom, s.group_pointage_id, e.real_site_id
        ORDER BY s.nom, e.nom`;
}

function sqlSelectHeureContrat() {
    return `
        select distinct heure_contrat from sites order by coalesce(heure_contrat, 500);
    `;
}

function getDayExport() {
    let beginDay = moment().set({ hour: 6, minute: 0, second: 0 })
    let endDay = moment().set({ hour: 23, minute: 59, second: 59 })
    if (moment().isAfter(beginDay) && moment().isBefore(endDay))
        return moment().format("YYYY-MM-DD")
    else
        return moment().subtract(1, 'day').format("YYYY-MM-DD")
}

function generateColumnExcel(interval) {
    let currentCol = "F"
    let colsByDate = {}
    let currentDate = moment(interval.begin).set({ hour: 7, minute: 0, second: 0 })
    while (moment(currentDate).isBefore(moment(interval.end).set({ hour: 23 }))) {
        colsByDate[currentDate.format('YYYY-MM-DD HH:mm:ss')] = {
            title: currentDate.format('DD') + ' ' +
                (currentDate.format('HH:mm:ss') == '07:00:00' ? 'J' :
                    currentDate.format('HH:mm:ss') == '18:00:00' ? 'N' : ''),
            col: currentCol
        }
        let currentArray = currentCol.split('')
        if (currentArray[currentArray.length - 1] != "Z")
            currentArray[currentArray.length - 1] = String.fromCharCode(currentArray[currentArray.length - 1].charCodeAt(0) + 1)
        else if (currentArray.length == 1)
            currentArray = ['A', 'A']
        else {
            currentArray[0] = String.fromCharCode(currentArray[0].charCodeAt(0) + 1)
            currentArray[1] = 'A'
        }
        currentCol = currentArray.join('')
        if (currentDate.format('HH:mm:ss') == '07:00:00')
            currentDate.set({ hour: 18, minute: 0, second: 0 })
        else currentDate.add(1, 'day').set({ hour: 7, minute: 0, second: 0 })
    }
    colsByDate.total = {
        title: "Total",
        col: currentCol
    }
    return colsByDate
}

async function generateExcelFile(workbook, groups, interval) {
    const colors = [
        {
            site: '9c27b0',
            line1: 'f3e5f5',
            line2: 'e1bee7'
        },
        {
            site: '2196f3',
            line1: 'e3f2fd',
            line2: 'bbdefb'
        },
        {
            site: 'cddc39',
            line1: 'f9fbe7',
            line2: 'f0f4c3'
        },
        {
            site: '009688',
            line1: 'e0f2f1',
            line2: 'b2dfdb'
        }
    ]
    const borderStyle = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
    }
    const fillTitle = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF607d8b' }
    }
    const fillHoraire = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFb0bec5' }
    }

    groups.forEach(group => {
        console.log(group.agents)
        const colsByDate = generateColumnExcel(interval)
        const worksheet = workbook.addWorksheet(group.nom.toUpperCase(), {
            views: [
                { state: 'frozen', ySplit: 1 },
                //{state: 'frozen', xSplit: 3}
            ]
        })
        worksheet.properties.defaultRowHeight = 24;
        const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
        const alignLeftStyle = { vertical: 'middle' }

        worksheet.getColumn('A').width = 10
        worksheet.getColumn('B').width = 15
        worksheet.getColumn('C').width = 65
        worksheet.getColumn('D').width = 55
        worksheet.getColumn('E').width = 30

        worksheet.getRow(1).height = 20
        worksheet.getCell('A1').value = 'SOCIETE'
        worksheet.getCell('A1').fill = fillTitle
        worksheet.getCell('A1').border = borderStyle
        worksheet.getCell('A1').alignment = alignmentStyle
        worksheet.getCell('B1').value = 'MATRICULE'
        worksheet.getCell('B1').fill = fillTitle
        worksheet.getCell('B1').border = borderStyle
        worksheet.getCell('B1').alignment = alignmentStyle
        worksheet.getCell('C1').value = 'NOM'
        worksheet.getCell('C1').fill = fillTitle
        worksheet.getCell('C1').border = borderStyle
        worksheet.getCell('C1').alignment = alignLeftStyle
        worksheet.getCell('D1').value = 'SITE'
        worksheet.getCell('D1').fill = fillTitle
        worksheet.getCell('D1').border = borderStyle
        worksheet.getCell('D1').alignment = alignmentStyle
        worksheet.getCell('E1').value = 'CONTACT'
        worksheet.getCell('E1').fill = fillTitle
        worksheet.getCell('E1').border = borderStyle
        worksheet.getCell('E1').alignment = alignmentStyle

        Object.keys(colsByDate).map((key) => {
            worksheet.getColumn(colsByDate[key].col).width = 5
            worksheet.getCell(colsByDate[key].col + '1').value = colsByDate[key].title
            worksheet.getCell(colsByDate[key].col + '1').fill = fillHoraire
            worksheet.getCell(colsByDate[key].col + '1').border = borderStyle
            worksheet.getCell(colsByDate[key].col + '1').alignment = alignmentStyle
        })

        //Total
        worksheet.getColumn(colsByDate.total.col).width = 8
        worksheet.getCell(colsByDate.total.col + '1').value = colsByDate.total.title
        worksheet.getCell(colsByDate.total.col + '1').fill = fillHoraire
        worksheet.getCell(colsByDate.total.col + '1').border = borderStyle
        worksheet.getCell(colsByDate.total.col + '1').alignment = alignmentStyle

        let line = 2
        let colorIndex = 0
        let lastSiteId = 0
        group.agents.map((a) => {
            console.log(a)
            if (a.pointages.length > 0) {
                if (lastSiteId && lastSiteId != a.site_id) {
                    if (colorIndex < colors.length - 1)
                        colorIndex++
                    else colorIndex = 0
                }
                const fillLine = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FF' + colors[colorIndex]['line2'] }
                }
                const fillSite = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FF' + colors[colorIndex].site }
                }

                Object.keys(colsByDate).map((key) => {
                    worksheet.getCell(colsByDate[key].col + line).fill = fillLine
                    worksheet.getCell(colsByDate[key].col + line).border = borderStyle
                })
                worksheet.getRow(line).height = 20
                worksheet.getCell('A' + line).border = borderStyle
                worksheet.getCell('A' + line).fill = fillLine
                worksheet.getCell('A' + line).alignment = alignmentStyle
                worksheet.getCell('A' + line).value = (
                    a.societe_id == 1 ? "DGM" :
                        a.societe_id == 2 ? "SOIT" :
                            a.societe_id == 3 ? "ST" :
                                a.societe_id == 4 ? 'SM' :
                                    'Ndf'
                )
                worksheet.getCell('B' + line).border = borderStyle
                worksheet.getCell('B' + line).fill = fillLine
                worksheet.getCell('B' + line).alignment = alignmentStyle
                worksheet.getCell('B' + line).value = (
                    a.societe_id == 1 ? a.numero_employe :
                        a.societe_id == 2 ? a.num_emp_soit :
                            a.societe_id == 3 ? a.numero_stagiaire :
                                a.societe_id == 4 ? 'SM' :
                                    a.numero_employe ? a.numero_employe :
                                        a.numero_stagiaire ? a.numero_stagiaire :
                                            'Ndf'
                )
                worksheet.getCell('C' + line).value = a.nom
                worksheet.getCell('C' + line).border = borderStyle
                worksheet.getCell('C' + line).fill = fillLine
                worksheet.getCell('C' + line).alignment = alignLeftStyle
                worksheet.getCell('D' + line).value = a.site
                worksheet.getCell('D' + line).border = borderStyle
                worksheet.getCell('D' + line).fill = fillSite
                worksheet.getCell('D' + line).alignment = alignmentStyle
                worksheet.getCell('E' + line).value = a.phone_agent
                worksheet.getCell('E' + line).border = borderStyle
                worksheet.getCell('E' + line).fill = fillLine
                worksheet.getCell('E' + line).alignment = alignmentStyle
                let total = 0;
                a.pointages.map((p) => {
                    const datePointage = moment(p.date_pointage).format("YYYY-MM-DD HH:mm:ss")
                    total += 12
                    worksheet.getCell(colsByDate[datePointage].col + line).value = 12
                    worksheet.getCell(colsByDate[datePointage].col + line).fill = fillSite
                    worksheet.getCell(colsByDate[datePointage].col + line).alignment = alignmentStyle
                    if (a.site_id != p.site_id)
                        worksheet.getCell(colsByDate[datePointage].col + line).note = p.site
                })

                worksheet.getCell(colsByDate.total.col + line).value = total
                worksheet.getCell(colsByDate.total.col + line).fill = fillSite
                worksheet.getCell(colsByDate.total.col + line).alignment = alignmentStyle

                lastSiteId = a.site_id
                line++;
            }
        })
    })
}

async function generateDetailExcelFile(workbook, groups, heures, interval) {
    const colors = [
        {
            site: '9c27b0',
            line1: 'f3e5f5',
            line2: 'e1bee7'
        },
        {
            site: '2196f3',
            line1: 'e3f2fd',
            line2: 'bbdefb'
        },
        {
            site: 'cddc39',
            line1: 'f9fbe7',
            line2: 'f0f4c3'
        },
        {
            site: '009688',
            line1: 'e0f2f1',
            line2: 'b2dfdb'
        }
    ]
    const borderStyle = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
    }
    const fillTitle = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF607d8b' }
    }
    const fillHoraire = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFb0bec5' }
    }
    heures.forEach(heure => {
        let worksheet = "";
        if (heure.heure_contrat != null) {
            worksheet = workbook.addWorksheet(heure.heure_contrat)
        } else {
            worksheet = workbook.addWorksheet("Non attribué")
        }
        groups.forEach(group => {
            const colsByDate = generateColumnExcel(interval)
            worksheet.properties.defaultRowHeight = 24;
            const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
            const alignLeftStyle = { vertical: 'middle' }
    
            worksheet.getColumn('A').width = 10
            worksheet.getColumn('B').width = 15
            worksheet.getColumn('C').width = 65
            worksheet.getColumn('D').width = 55
            worksheet.getColumn('E').width = 30
    
            worksheet.getRow(1).height = 20
            worksheet.getCell('A1').value = 'SOCIETE'
            worksheet.getCell('A1').fill = fillTitle
            worksheet.getCell('A1').border = borderStyle
            worksheet.getCell('A1').alignment = alignmentStyle
            worksheet.getCell('B1').value = 'MATRICULE'
            worksheet.getCell('B1').fill = fillTitle
            worksheet.getCell('B1').border = borderStyle
            worksheet.getCell('B1').alignment = alignmentStyle
            worksheet.getCell('C1').value = 'NOM'
            worksheet.getCell('C1').fill = fillTitle
            worksheet.getCell('C1').border = borderStyle
            worksheet.getCell('C1').alignment = alignLeftStyle
            worksheet.getCell('D1').value = 'SITE'
            worksheet.getCell('D1').fill = fillTitle
            worksheet.getCell('D1').border = borderStyle
            worksheet.getCell('D1').alignment = alignmentStyle
            worksheet.getCell('E1').value = 'CONTACT'
            worksheet.getCell('E1').fill = fillTitle
            worksheet.getCell('E1').border = borderStyle
            worksheet.getCell('E1').alignment = alignmentStyle
    
            Object.keys(colsByDate).map((key) => {
                worksheet.getColumn(colsByDate[key].col).width = 5
                worksheet.getCell(colsByDate[key].col + '1').value = colsByDate[key].title
                worksheet.getCell(colsByDate[key].col + '1').fill = fillHoraire
                worksheet.getCell(colsByDate[key].col + '1').border = borderStyle
                worksheet.getCell(colsByDate[key].col + '1').alignment = alignmentStyle
            })
    
            //Total
            worksheet.getColumn(colsByDate.total.col).width = 8
            worksheet.getCell(colsByDate.total.col + '1').value = colsByDate.total.title
            worksheet.getCell(colsByDate.total.col + '1').fill = fillHoraire
            worksheet.getCell(colsByDate.total.col + '1').border = borderStyle
            worksheet.getCell(colsByDate.total.col + '1').alignment = alignmentStyle
    
            let line = 2
            let colorIndex = 0
            let lastSiteId = 0
            group.agents.filter((ga) => ga.heure_contrat == heure.heure_contrat).map((a) => {
                console.log(a)
                if (a.pointages.length > 0) {
                    if (lastSiteId && lastSiteId != a.site_id) {
                        if (colorIndex < colors.length - 1)
                            colorIndex++
                        else colorIndex = 0
                    }
                    const fillLine = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FF' + colors[colorIndex]['line2'] }
                    }
                    const fillSite = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FF' + colors[colorIndex].site }
                    }
    
                    Object.keys(colsByDate).map((key) => {
                        worksheet.getCell(colsByDate[key].col + line).fill = fillLine
                        worksheet.getCell(colsByDate[key].col + line).border = borderStyle
                    })
                    worksheet.getRow(line).height = 20
                    worksheet.getCell('A' + line).border = borderStyle
                    worksheet.getCell('A' + line).fill = fillLine
                    worksheet.getCell('A' + line).alignment = alignmentStyle
                    worksheet.getCell('A' + line).value = (
                        a.societe_id == 1 ? "DGM" :
                            a.societe_id == 2 ? "SOIT" :
                                a.societe_id == 3 ? "ST" :
                                    a.societe_id == 4 ? 'SM' :
                                        'Ndf'
                    )
                    worksheet.getCell('B' + line).border = borderStyle
                    worksheet.getCell('B' + line).fill = fillLine
                    worksheet.getCell('B' + line).alignment = alignmentStyle
                    worksheet.getCell('B' + line).value = (
                        a.societe_id == 1 ? a.numero_employe :
                            a.societe_id == 2 ? a.num_emp_soit :
                                a.societe_id == 3 ? a.numero_stagiaire :
                                    a.societe_id == 4 ? 'SM' :
                                        a.numero_employe ? a.numero_employe :
                                            a.numero_stagiaire ? a.numero_stagiaire :
                                                'Ndf'
                    )
                    worksheet.getCell('C' + line).value = a.nom
                    worksheet.getCell('C' + line).border = borderStyle
                    worksheet.getCell('C' + line).fill = fillLine
                    worksheet.getCell('C' + line).alignment = alignLeftStyle
                    worksheet.getCell('D' + line).value = a.site
                    worksheet.getCell('D' + line).border = borderStyle
                    worksheet.getCell('D' + line).fill = fillSite
                    worksheet.getCell('D' + line).alignment = alignmentStyle
                    worksheet.getCell('E' + line).value = a.phone_agent
                    worksheet.getCell('E' + line).border = borderStyle
                    worksheet.getCell('E' + line).fill = fillLine
                    worksheet.getCell('E' + line).alignment = alignmentStyle
                    let total = 0;
                    a.pointages.map((p) => {
                        const datePointage = moment(p.date_pointage).format("YYYY-MM-DD HH:mm:ss")
                        total += 12
                        worksheet.getCell(colsByDate[datePointage].col + line).value = 12
                        worksheet.getCell(colsByDate[datePointage].col + line).fill = fillSite
                        worksheet.getCell(colsByDate[datePointage].col + line).alignment = alignmentStyle
                        if (a.site_id != p.site_id)
                            worksheet.getCell(colsByDate[datePointage].col + line).note = p.site
                    })
    
                    worksheet.getCell(colsByDate.total.col + line).value = total
                    worksheet.getCell(colsByDate.total.col + line).fill = fillSite
                    worksheet.getCell(colsByDate.total.col + line).alignment = alignmentStyle
    
                    lastSiteId = a.site_id
                    line++;
                }
            })
        })
    })
}

function doPointage(date) {
    console.log("doPointage")
    const interval = getInterval(20, date)
    console.log(interval.begin + " -> " + interval.end)
    poolAdmin.query(sqlSelectGroupPointage, [], async (err, groups) => {
        if (err)
            console.error(err)
        else {
            console.log("Nb group: " + groups.length)
            poolAdmin.query(sqlSelectPointage(interval), [], async (err, pointages) => {
                if (err)
                    console.error(err)
                else {
                    console.log("Nb pointage: " + pointages.length)
                    poolAdmin.query(sqlSelectAgent([...new Set(pointages.map(p => p.employe_id))]), [], async (err, agents) => {
                        if (err)
                            console.error(err)
                        else {
                            console.log("Nb agent: " + agents.length)
                            groups.forEach(g => {
                                g.agents = []
                                agents.forEach(ag => {
                                    ag.pointages = []
                                    pointages.forEach(ptg => {
                                        if (ptg.employe_id == ag.id)
                                            ag.pointages.push(ptg)
                                    })
                                    if (ag.group_pointage_id == g.id) {
                                        g.agents.push(ag)
                                    }
                                })
                            })
                            poolAdmin.query(sqlSelectHeureContrat(), [], async (err, heures) => {
                                if (err)
                                    console.error(err)
                                else {
                                    const workbook = new Excel.Workbook()
                                    const detailsWorkbook = new Excel.Workbook()
                                    await generateExcelFile(workbook, groups, interval)
                                    await generateDetailExcelFile(detailsWorkbook, groups, heures, interval)
                                    const buffer = await workbook.xlsx.writeBuffer()
                                    const bufferDetails = await detailsWorkbook.xlsx.writeBuffer()
                                    const titleParRegion = 'Pointage par region du ' + moment(date).subtract(1, "day").format('DD-MM-YYYY')
                                    const titleParContrat = 'Pointage par contrat du ' + moment(date).subtract(1, "day").format('DD-MM-YYYY')
                                    const arrayFile = {
                                        filename: titleParRegion + ".xlsx",
                                        content: buffer
                                    }
                                    const detailFile = {
                                        filename: titleParContrat + ".xlsx",
                                        content: bufferDetails
                                    }
                                    sendMail(
                                        poolAdmin,
                                        isTask ? destination_pointage : destination_test,
                                        "Rapport pointage du " + moment(date).subtract(1, "day").format('ddd DD MMM YYYY'),
                                        "Veuillez trouver ci-joint joint le rapport pointage du " + moment(interval.begin).format('DD-MM-YYYY') +
                                        (moment(interval.begin).format('DD-MM-YYYY') != moment(interval.end).format('DD-MM-YYYY') ? ' au ' + moment(interval.end).format('DD-MM-YYYY') : ''),
                                        [arrayFile, detailFile],
                                        () => {
                                            if (isTask)
                                                poolAdmin.query(sqlUpdateLastPointageExport(date), [], (err, data) => {
                                                    if (err)
                                                        console.error(err)
                                                    console.log("\n\ndone!!!")
                                                    process.exit(1)
                                                })
                                            else process.exit(1)
                                        },
                                        isTask
                                    )
                                }
                            })
                        }
                    })
                }
            })
        }
    })
}

function getInterval(day, date) {
    let beginDate = null
    let endDate = moment(date).subtract(1, "day")

    if (endDate.isAfter(endDate.clone().set("date", day)))
        beginDate = endDate.clone().set("date", day)
    else
        beginDate = endDate.clone().subtract(1, "month").set("date", day)

    return {
        begin: beginDate.format('YYYY-MM-DD') + " 00:00:00",
        end: endDate.format('YYYY-MM-DD') + " 23:59:59"
    }
}

if (/^\d{4}\-\d{2}\-\d{2}$/.test(process.argv[2])) {
    console.log("test...")
    doPointage(process.argv[2])
}
else if (process.argv[2] == 'task') {
    const date = getDayExport()
    poolAdmin.query(sqlSelectDatePointageExport, [], (err, result) => {
        if (err)
            console.error(err)
        else if (result && date == result[0].value) {
            console.log("export pointage already done!")
            process.exit(1)
        }
        else
            doPointage(date)
    })
}
else {
    console.log("please specify command!")
}
