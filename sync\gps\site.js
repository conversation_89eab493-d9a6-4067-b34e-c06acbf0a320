const moment = require('moment')
const mysql = require('mysql')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth")

const pool_admin = mysql.createPool(auth.db_config_admin)
const pool_gps = mysql.createPool(auth.db_config_gps)

const pathname = 'logs/sync/site/' +  moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
	console.error(err)
})

const sqlSelectSite = "SELECT idsite, nom, soft_delete from sites " +
    "where synchronized_at is null or (gps_updated_at is not null and synchronized_at <= gps_updated_at) " +
    "limit 50"
const sqlInsertOrUpdateSite = "INSERT INTO drx_sites(idsite, nom, soft_delete) " +
    "VALUES (?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE nom=?, soft_delete=?"
const sqlUpdateSite = "UPDATE sites SET synchronized_at = now() WHERE idsite = ?"

function syncSiteById(sites, index){
    if(index < sites.length){
        const site = sites[index]
        pool_gps.query(sqlInsertOrUpdateSite, [site.idsite, site.nom, site.soft_delete
            , site.nom,  site.soft_delete], async (err, res) => {
            if(err){
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if(err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                console.log("sync site: " + site.idsite)
                pool_admin.query(sqlUpdateSite, [site.idsite], async (err, res) => {
                    if(err){
                        fs.appendFile(pathname, err.toString(), (err) => {
                            if(err) console.error(err);
                        })
                        console.error(err)
                    }
                })
                setTimeout(() => {
                    syncSiteById(sites, index+1)
                }, 200)
            }
        })
    }
    else
        waitBeforeUpdate()
}

function updateData(){
    pool_admin.query(sqlSelectSite, [], async (err, sites) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if(err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            if(sites.length > 0){
                console.log("site to sync: " + sites.length)
                syncSiteById(sites, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
        }
    })
}

let count = 1
function waitBeforeUpdate(){
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if(count > 3) count = 1
    else count ++
}

updateData()