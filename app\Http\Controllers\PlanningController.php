<?php

namespace App\Http\Controllers;

use App\Models\CommentPlanning;
use App\Models\Message;
use App\Models\NoteMessage;
use App\Models\Planning;
use App\Models\PlanningPointage;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PlanningController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function search(Request $request){
        $auth = $request->user();
        $searchArray = [];
        if ($request->id) {
            $searchArray[] = " pln.id = " . $request->id . " ";
        } else {
            if ($request->site_id) {
                $searchArray[] =  " pln.site_id = $request->site_id ";
            }
            if ($request->created_at) {
                $searchArray[] =  " pln.created_at > '$request->created_at 00:00:00' and pln.created_at <= '$request->created_at 23:59:59' ";
            }
            if ($request->date_planning) {
                $searchArray[] = " pln.date_planning = '$request->date_planning' ";
            }
            if ($request->user_id && !in_array($auth->role, ['superviseur', 'resp_sup'])) {
                $searchArray[] = " pln.user_id = '$request->user_id' ";
            }
            // if ($request->superviseur_id && $auth->role != 'superviseur') {
            //     $searchArray[] = " pln.user_id = '$request->superviseur_id' ";
            // }
            if ($request->resp_sup_id && !in_array($auth->role, ['superviseur', 'resp_sup'])) {
                $searchArray[] = " st.resp_sup_id = '$request->resp_sup_id' ";
            }
            if ($request->unread){
                $date_planning = $request->date_planning ?? (new \DateTime())->format('Y-m');
                $searchArray[] = " (pln.seen_at is null or pln.seen_at < pln.updated_at)
                    AND pln.date_planning = '$date_planning' and st.superviseur_id = " . $request->user()->id . " ";
            }
            if ($request->unread_sup){
                $date_planning = $request->date_planning ?? (new \DateTime())->format('Y-m');
                $searchArray[] = " (pln.seen_at is null or pln.seen_at < pln.updated_at)
                    AND pln.date_planning = '$date_planning' AND pln.user_id = " . $request->user()->id . " ";
            }
        }
        $query_where = "";
        $query_and = "";
        if ($request->offset || $request->offset > -1) {
            $orderBy = " order by pln.date_planning DESC limit " . $request->offset . ", 30";
        } else {
            $orderBy = " order by pln.date_planning DESC";
        }
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " ";
        }
        if($request->resp_sup_id || in_array($auth->role, ['resp_sup', 'superviseur'])){
            $groupBy = " GROUP BY pln.id ";
        }else{
            if(in_array($auth->role, ["resp_op", "validateur", "access"]))
                $groupBy = " GROUP BY resp.id, resp.name, resp.email";
            else
                $groupBy = " GROUP BY sup.id, sup.name, sup.email ";
            $orderBy = " ORDER BY latest_date_planning DESC";
        }
        return compact("query_where", "query_and", "groupBy", "orderBy");
    }

    public static function hasAgent($request){
        foreach ($request->pointages as $ptg) {
            if(count($ptg['employes']) > 0)
                return true;
        }
        return false;
    }

    public function index(Request $request) {
        $auth = $request->user();
        $orderBy = $this->search($request)['orderBy'];
        $groupBy = $this->search($request)['groupBy'];
        if(in_array($auth->role, ['superviseur', 'resp_sup', 'resp_op', 'validateur', 'access']) || in_array($auth->id, [1])){
            if($request->resp_sup_id || in_array($auth->role, ['resp_sup', 'superviseur'])){
                $sql ="SELECT count(ptg.id) as 'number_ptg', pln.id, st.nom as 'site', st.total_hour,
                    pln.date_planning, pln.status,stat.color as 'status_color'
                    FROM plannings pln 
                    LEFT JOIN sites st ON st.idsite = pln.site_id
                    LEFT JOIN planning_pointages ptg ON ptg.planning_id = pln.id
                    LEFT JOIN `status` stat ON stat.name = pln.status 
                    WHERE ptg.id is not null
                    " . ($this->search($request))['query_and'];
                if(in_array($auth->role, ['superviseur', 'resp_sup'])){
                    $sql .= " AND (pln.user_id = $auth->id or st.resp_sup_id = $auth->id)";
                }
                if(in_array($auth->role, ['resp_op'])){
                    $regions = RegionUsersController::getRegions($request);
                    $sql .= " AND st.group_pointage_id IN ($regions)";
                }
                $plannings = DB::select( $sql . $groupBy . $orderBy, []);
            }
            else if($request->un_finished){
                $date_planning = $request->date_planning ?? (new \DateTime())->format('Y-m');
                if($request->resp_sup_id){
                    $plannings = DB::select("SELECT st.nom as 'site', st.* FROM sites st 
                        LEFT JOIN plannings pln ON pln.site_id = st.idsite AND pln.date_planning = ? 
                        WHERE pln.id IS NULL AND st.resp_sup_id = ? " . ($this->search($request))['query_and'], [$date_planning, $request->resp_sup_id]);
                }
                else{
                    $plannings = DB::select("SELECT u.id AS resp_sup_id, u.name AS resp_sup, u.email as resp_sup_email,
                        COUNT(st.idsite) AS nb_planning, ag.nom AS agence
                        FROM sites st
                        LEFT JOIN plannings pln ON pln.site_id = st.idsite AND pln.date_planning = ?
                        LEFT JOIN users u ON st.resp_sup_id = u.id
                        LEFT JOIN employes emp ON emp.id = u.employe_id
                        LEFT JOIN agences ag ON ag.id = emp.agence_id
                        WHERE pln.id IS NULL and st.resp_sup_id is not null
                        " . ($this->search($request))['query_and'] .
                        "GROUP BY u.id, u.name
                        ORDER BY nb_planning DESC LIMIT ?, 30", [$date_planning, $request->offset ?? 0]);
                }
            }
            else{
                $sql = "SELECT count(pln.id) as 'nb_planning',
                    resp.id as 'resp_sup_id', resp.name as 'resp', resp.email as 'resp_email',
                    MAX(pln.date_planning) as 'latest_date_planning'
                    FROM plannings pln 
                    LEFT JOIN users u ON u.id = pln.user_id
                    LEFT JOIN sites st ON st.idsite = pln.site_id
                    LEFT JOIN users resp ON resp.id = st.resp_sup_id
                    ";
                if(in_array($auth->role, ['superviseur', 'resp_sup'])){
                    $sql .= " WHERE (pln.user_id = $auth->id or st.superviseur_id = $auth->id)" . ($this->search($request))['query_and'];
                } else if(in_array($auth->role, ['resp_op'])){
                    $regions = RegionUsersController::getRegions($request);
                    $sql .= " WHERE st.group_pointage_id IN ($regions)" . ($this->search($request))['query_and'];
                }
                else $sql .=  ($this->search($request))['query_where'];
                $plannings = DB::select($sql . $groupBy . $orderBy, []);
            }
            return response(compact('plannings'));
        }
        return response(['error' => "EACCES"]);
    }
    
    public function store (Request $request){
        $validator = Validator::make($request->all(),[
            'site_id' => 'required',
            'date_planning' => 'required',
        ]);
        $date_planning = $request->date_planning;
        $max_date_planning = (new \DateTime('first day of next month'));
        $date_pl = new \DateTime($date_planning['year'] . '-' . $date_planning['month']);
        if($date_pl > $max_date_planning)
            return ['error' => "La date de planning est limitée au mois suivant."];
        if ($validator->fails())
            return ['error' => $validator->errors()->first()];
        $verify_site = Site::find($request->site_id);
        if(!$verify_site->resp_sup_id)
            return ['error' => "Ce site n'est pas lié à un manager"];
        else if($verify_site->pointage != 1)
            return ['error' => "Ce site n'a pas de pointage"];
        $auth = $request->user();
        $site_id = $request->site_id;
        $site = Site::find($site_id);
        $is_new = false;
        $is_duplicate = $request->duplicate;
        $is_edit = $request->edit;
        if($is_edit){
            $planning = Planning::find($request->id);
            $another_planning = Planning::where('site_id', $site_id)
                ->where('date_planning', $date_planning['year'] . '-' . $date_planning['month'])
                ->where('id', '!=', $request->id)
                ->first();
            if($another_planning)
                return response(["error" => "Ce planning existe déjà dans ce site"]);

            if($planning->date_planning != ($date_planning['year'] . '-' . $date_planning['month'])){
                $pointage_in_old_planning = PlanningPointage::where('planning_id', $planning->id)->get();
                foreach ($pointage_in_old_planning as $ptg) {
                    if((new \DateTime($ptg->date_pointage)) < (new \DateTime())){
                        return response(["error" => "Impossible de modifier la date de planning en raison d'un pointage dépassé."]);
                    }
                }
            }
        }
        else
            $planning = Planning::where('site_id', $site_id)->where('date_planning', $date_planning['year'] . '-' . $date_planning['month'])->first();
        
        if($auth->id == $site->resp_sup_id || ($planning && $planning->user_id == $auth->id) || in_array($auth->role, ['resp_op'])){
            if ($planning) {
                $old_planning = clone $planning;
                if($is_duplicate){
                    return response(["error" => "Impossible de dupliquer un planning existant"]);
                }
                $old_ptgs = PlanningPointage::where('planning_id', $planning->id)->get();
            }
            else {
                if (!$this->hasAgent($request)) {
                    return response(["error" => "Ajouter au moins un employé"]);
                }
                $planning = new Planning();
                $is_new = true;
                $planning->created_at = new \DateTime();
                $old_ptgs = collect();
            }

            $emp_ids = [];
            foreach ($request->pointages as $ptg) {
                if((new \DateTime($ptg['service']))->format('Y-m') != $date_planning['year'] . '-' . $date_planning['month']){
                    return response(["error" => "Erreur de date de service"]);
                }
                if(count($ptg['employes']) > 0 && new \DateTime($ptg['service']) > new \DateTime()){
                    foreach ($ptg['employes'] as $emp_ptg) {
                        $emp_ids[] = $emp_ptg;
                    }
                }
            }

            if(count($emp_ids) > 0){
                $verify_employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, 
                e.num_emp_soit, e.num_emp_saoi, e.soft_delete,
                CASE 
                    WHEN e.societe_id = 1 THEN CONCAT('DGM-', e.numero_employe)
                    WHEN e.societe_id = 2 THEN CONCAT('SOIT-', e.num_emp_soit)
                    WHEN e.societe_id = 3 THEN CONCAT('ST-', e.numero_stagiaire)
                    WHEN e.societe_id = 4 THEN 'SM'
                    WHEN e.societe_id = 5 THEN 'TMP'
                    WHEN e.societe_id = 6 THEN CONCAT('SAOI', e.num_emp_saoi)
                    ELSE 'NDF'
                END AS 'matricule'
                FROM employes e WHERE e.soft_delete = 1 and e.id in (" . implode(",", $emp_ids) . ")");

                if(count($verify_employes) > 0){
                    $first_employe_archive = null;
                    foreach ($emp_ids as $id) {
                        foreach ($verify_employes as $emp) {
                            if($emp->id == $id){
                                $first_employe_archive = $emp;
                                return response(["error" => "L'agent [" . $first_employe_archive->matricule ."] - " . strtoupper($first_employe_archive->nom) ." est en archive"]);
                            }
                        }
                    }
                    
                }
            }
            $planning->user_id = $auth->id;
            $planning->site_id = $site_id;
            $planning->date_planning = ($date_planning['year'] . '-' . $date_planning['month']);
            $planning->updated_at = new \DateTime();
            $planning->status = 'demande';
            if ($planning->save() && $request->pointages) {
                $data_ptgs = [];
                $all_datas = [];
                foreach ($request->pointages as $ptg) {
                    $service = $ptg['service'];
                    if ($ptg['comment']['id']) 
                        $comment = CommentPlanning::find($ptg['comment']['id']);
                    else{
                        $verify_comment = CommentPlanning::where('planning_id', $planning->id)->where('date_pointage', $service)->first();
                        if($verify_comment)
                            $comment = $verify_comment;
                        else
                            $comment = new CommentPlanning();
                    }
                    $comment->comment = $ptg['comment']['content'];
                    $comment->planning_id = $planning->id;
                    $comment->date_pointage = $service;
                    $comment->created_at = new \DateTime();
                    $comment->updated_at = new \DateTime();
                    if (strlen(trim($ptg['comment']['content'])) > 0){
                        $comment->save();
                    }
                    foreach ($ptg['employes'] as $emp_ptg) {
                        $is_not_exist = true;
                        foreach ($old_ptgs as $ptg) {
                            if (($emp_ptg == $ptg['agent_id'] && $service == $ptg['date_pointage']))
                                $is_not_exist = false;
                        }
                        if ($is_not_exist) {
                            if((new \DateTime($service)) < (new \DateTime())){
                                return response(["error" => "Date de pointage " . (new \DateTime($service))->format('Y-m-d') . 
                                    ((new \DateTime($service))->format('H:i:s') == '06:00:00' ? ' JOUR' : ' NUIT') . " déjà passée"]);
                            }
                            $data_ptgs[] = [
                                "planning_id" => $planning->id,
                                "agent_id" => $emp_ptg,
                                "date_pointage" => $service,
                                "created_at" => new \DateTime(),
                                "updated_at" => new \DateTime(),
                            ];
                        }
                        $all_datas[] = [
                            "planning_id" => $planning->id,
                            "agent_id" => $emp_ptg,
                            "date_pointage" => $service,
                        ];
                    }
                }

                if (count($all_datas) > 0) {
                    $tuples = [];
                    foreach ($all_datas as $data) {
                        $tuples[] = "('" . $data["agent_id"] . "', '" . $data["date_pointage"] . "')";
                    }
                    $pointage_in_other = DB::select(
                        "SELECT pl_ptg.id, pl_ptg.date_pointage, pl_ptg.agent_id, pln.site_id, st.nom as 'site', emp.nom as 'agent',
                        CASE 
                            WHEN emp.societe_id = 1 THEN CONCAT('DGM-', emp.numero_employe)
                            WHEN emp.societe_id = 2 THEN CONCAT('SOIT-', emp.num_emp_soit)
                            WHEN emp.societe_id = 3 THEN CONCAT('ST-', emp.numero_stagiaire)
                            WHEN emp.societe_id = 4 THEN 'SM'
                            WHEN emp.societe_id = 5 THEN 'TMP'
                            WHEN emp.societe_id = 6 THEN CONCAT('SAOI', emp.num_emp_saoi)
                            ELSE 'NDF'
                        END AS 'matricule'
                        FROM planning_pointages pl_ptg
                        LEFT JOIN plannings pln ON pln.id = pl_ptg.planning_id
                        LEFT JOIN sites st ON pln.site_id = st.idsite
                        LEFT JOIN employes emp ON emp.id = pl_ptg.agent_id
                        WHERE planning_id != ?
                        AND (pl_ptg.agent_id, pl_ptg.date_pointage) IN (" . implode(", ", $tuples) . ")",
                        [$planning->id]
                    );
                    if(count($pointage_in_other) > 0) {
                        $data = $pointage_in_other[0];
                        $date_ptg = (new \DateTime($data->date_pointage))->format('Y-m-d');
                        $service = (new \DateTime($data->date_pointage))->format('H:i:s') == '06:00:00' ? 'JOUR' : 'NUIT';
                        return response(["error" => "Employé [" . $data->matricule . 
                            " " . $data->agent . "] déjà sur le site " . $data->site . 
                            " le " . $date_ptg ." $service ." ]
                        );
                    }
                    $pointage_to_delete = DB::select("SELECT id, date_pointage FROM planning_pointages 
                        WHERE planning_id= ? 
                        AND (agent_id, date_pointage) NOT IN (" . implode(", ", $tuples) . ")",
                        [$planning->id]
                    );
                    if(count($pointage_to_delete) > 0){
                        foreach ($pointage_to_delete as $ptg) {
                            if(new \DateTime($ptg->date_pointage) < new \DateTime()){
                                return response(["error" => "Date de pointage " . (new \DateTime($ptg->date_pointage))->format('Y-m-d') . 
                                    ((new \DateTime($ptg->date_pointage))->format('H:i:s') == '06:00:00' ? ' JOUR' : ' NUIT') . " déjà passée"]);
                            }
                        }
                    }
                }
                else
                    $pointage_to_delete = clone $old_ptgs;

                $ids = collect($pointage_to_delete)->pluck('id')->toArray();

                if (!empty($ids)) {
                    DB::table('planning_pointages')
                        ->whereIn('id', $ids)
                        ->where('date_pointage', '>', new \DateTime())
                        ->delete();
                }

                if (!empty($data_ptgs))
                    PlanningPointage::insert($data_ptgs);

                $planning->pointages = PlanningPointage::where('planning_id', $planning->id)->get();
                $details = '';
                if($is_new)
                    $details = HistoriqueController::new_planning($request, $planning);
                else
                    $details = HistoriqueController::update_planning($request, $planning, $old_planning, $old_ptgs);
                $site = Site::find($planning->site_id);
                if($site->superviseur_id && $site->superviseur_id != $request->user()->id)
                    $this->message_for_superviseur($request, $planning, $details);
            }
            return response(['success' => "Planning enregistrer avec success", "id" => $planning->id]);
        }
        return response(['error' => "Vous n'avez pas le droit de modifier ce planning"]);
    }

    public static function message_for_superviseur(Request $request, $planning, $details){
        $message = new Message();
        $site = Site::find($planning->site_id);
        if($site->superviseur_id && ($site->superviseur_id != $request->user()->id)){
            $message->objet = "Màj du planning: $site->nom [".(new \DateTime($planning->date_planning))->format('M Y') ."]";
            $message->user_id = $request->user()->id;
            $message->created_at = new \DateTime();
            $message->updated_at = new \DateTime();

            $message->content = '<p><b>Planning : '. $site->nom . '</b></p>';
            $message->content .= '<p>'. str_replace(['<', '>', '\n'], ['&lt;', '&gt;', '<br/>'], $details) .' </p>';
            $message->content .= '<p>Voir le planning : <a href="'. env('APP_URL') . "/planning?id=" . $planning->id . '">Cliquer ici</a></p>';
            if ($message->save()) {
                $note = new NoteMessage();
                $note->message_id = $message->id;
                $note->user_id = $site->superviseur_id;
                $note->follow = 1;
                $note->created_at = new \DateTime();
                $note->updated_at = new \DateTime();
                $note->save();
            }
        }
    }

    public function get_planning(Request $request) {
        $auth = $request->user();
        $auth_role = $auth->role;
        $id = $request->id;
        $site_id = $request->site_id;
        $date_planning = $request->date_planning;
        // $condition = "";
        // if($auth_role == "resp_sup"){
        //     $condition = " AND st.resp_sup_id = $auth->id";
        // }
        // if ($auth_role == "superviseur") {
        //     $condition = " AND st.superviseur_id = $auth->id";
        // }
        $planning = DB::select("SELECT pln.id, pln.site_id, pln.date_planning,
            st.nom, st.adresse, st.nb_agent_night, st.nb_agent_day, total_hour
            from plannings pln 
            LEFT JOIN sites st on st.idsite = pln.site_id 
            WHERE " . ($id ?  " pln.id = $id" : "site_id = $site_id and date_planning = '$date_planning'")
            // . $condition
            , []);
        $sites = DB::select("SELECT st.idsite, st.nom, st.adresse, st.nb_agent_night, st.nb_agent_day, st.total_hour,
            h.nom as 'horaire', hr.day_1, hr.day_2, hr.day_3, hr.day_4, hr.day_5, hr.day_6, hr.day_0, hr.day_ferie,
            hr.night_1, hr.night_2, hr.night_3, hr.night_4, hr.night_5, hr.night_6, hr.night_0, hr.night_ferie,
            st.nb_agent_night_planning, st.nb_agent_day_planning
            FROM sites st
            LEFT JOIN horaires h ON h.id = st.horaire_pointage_id 
            LEFT JOIN horaire_effectifs hr ON hr.site_id = st.idsite
            WHERE st.idsite = ?", [$site_id ?? $planning[0]->site_id])[0];
        if ($planning) {
            $comments = CommentPlanning::where('planning_id', $planning[0]->id)->whereNotNull('date_pointage', )->get();
            $planning_pointages = DB::select(
                "SELECT ptg.id, ptg.agent_id, ptg.date_pointage, emp.nom, emp.societe_id, 
                emp.numero_stagiaire, emp.numero_employe, emp.num_emp_saoi, emp.num_emp_soit, emp.soft_delete
                FROM planning_pointages ptg
                LEFT JOIN employes emp ON emp.id = ptg.agent_id
                WHERE ptg.planning_id = ? ", 
                [$planning[0]->id]);
                foreach ($comments as $comment) {
                    $has_correspondence = false;
                    foreach ($planning_pointages as $pointage) {
                        if (isset($pointage->date_pointage) && isset($comment->date_pointage)) {
                            if ($pointage->date_pointage == $comment->date_pointage) {
                                $has_correspondence = true;
                                $pointage->comment_content = $comment->comment;
                                $pointage->comment_id = $comment->id;
                            }
                        }
                    }
                    if (!$has_correspondence) {
                        $planning_pointages[] = [
                            "id"=> (new \DateTime($comment->date_pointage))->format('d') . ((new \DateTime($comment->date_pointage))->format('H') == '06' ? 'J' : 'N'),
                            "agent_id"=> null,
                            "date_pointage"=> $comment->date_pointage,
                            "nom"=> null,
                            "societe_id"=> null,
                            "numero_stagiaire"=> null,
                            "numero_employe"=> null,
                            "num_emp_saoi"=> null,
                            "num_emp_soit"=> null,
                            "soft_delete" => null,
                            "comment_content"=> $comment->comment,
                            "comment_id"=> $comment->id
                        ];
                    }
                }
            return response(['planning' => $planning[0], 'planning_pointages' => $planning_pointages, 'sites' => $sites]);
        }
        return response(['sites' => $sites]);
    }

    public function show(Request $request, $id) {
        $auth = $request->user();
        $planning = DB::select("SELECT pln.id, pln.seen_at, pln.updated_at, st.nom as 'site', st.idsite,
            st.superviseur_id, sup.name as 'superviseur', sup.email as 'superviseur_email', st.resp_sup_id, st.total_hour, pln.date_planning, 
            st.nb_agent_night, st.nb_agent_day_planning, st.nb_agent_night_planning, st.nb_agent_day, pln.user_id, pln.status,stat.color as 'status_color', 
            stat.description as 'status_description', hr.nom as 'horaire', 
            he.day_1, he.day_2, he.day_3, he.day_4, he.day_5, he.day_6, he.day_0, he.day_ferie,
            he.night_1, he.night_2, he.night_3, he.night_4, he.night_5, he.night_6, he.night_0, he.night_ferie,
            u.name as 'user_name', u.email as 'user_email'
            FROM plannings pln 
            LEFT JOIN sites st ON st.idsite = pln.site_id
            LEFT JOIN horaires hr ON hr.id = st.horaire_pointage_id
            LEFT JOIN horaire_effectifs he ON he.site_id = st.idsite
            LEFT JOIN `status` stat ON stat.name = pln.status 
            LEFT JOIN users u ON u.id = pln.user_id
            LEFT JOIN users sup ON sup.id = st.superviseur_id
            WHERE pln.id=?", [$id]);
        if (count($planning)> 0) {
            $comments = CommentPlanning::where('planning_id', $planning[0]->id)->get();
            $pointages = DB::select("SELECT ptg.id, ptg.agent_id, ptg.date_pointage, 
                emp.nom, emp.societe_id, emp.numero_stagiaire, emp.numero_employe,
                emp.num_emp_saoi, emp.num_emp_soit, emp.soft_delete
                FROM planning_pointages ptg
                LEFT JOIN employes emp ON emp.id = ptg.agent_id
                WHERE ptg.planning_id = ?",
                [$planning[0]->id]);
            foreach ($comments as $comment) {
                $has_correspondence = false;
                foreach ($pointages as $pointage) {
                    if (isset($pointage->date_pointage) && isset($comment->date_pointage)) {
                        if ($pointage->date_pointage == $comment->date_pointage) {
                            $has_correspondence = true;
                            $pointage->comment_content = $comment->comment;
                            $pointage->comment_id = $comment->id;
                        }
                    }
                }
                if (!$has_correspondence) {
                    $pointages[] = [
                        "id"=> (new \DateTime($comment->date_pointage))->format('d') . ((new \DateTime($comment->date_pointage))->format('H') == '06' ? 'J' : 'N'),
                        "agent_id"=> null,
                        "date_pointage"=> $comment->date_pointage,
                        "nom"=> null,
                        "societe_id"=> null,
                        "numero_stagiaire"=> null,
                        "numero_employe"=> null,
                        "num_emp_saoi"=> null,
                        "num_emp_soit"=> null,
                        "soft_delete" => null,
                        "comment_content"=> $comment->comment,
                        "comment_id"=> $comment->id
                    ];
                }
            }
            $data['planning'] = $planning[0];
            $data['pointages'] = $pointages;
            if(($planning[0]->seen_at == null || $planning[0]->seen_at < $planning[0]->updated_at) && 
                ($auth->id == $planning[0]->superviseur_id && $planning[0]->superviseur_id != $planning[0]->user_id))
            {
                Planning::where('id', $planning[0]->id)->update(['seen_at' => new \DateTime()]);
                HistoriqueController::seen_planning($request, $planning[0]->id, "Planning vu par le superviseur");
            }
            else if($planning[0]->superviseur_id == $planning[0]->user_id && $planning[0]->user_id == $auth->id){
                Planning::where('id', $planning[0]->id)->update(['seen_at' => new \DateTime()]);
            }
            $pieces = DB::select("SELECT pj.id FROM piece_jointes pj where pj.planning_id = ?", [$id]);
            $data['planning']->nb_pj = count($pieces);
            return response()->json($data);
        }
        return null;
    }

    public function get_data_to_print(Request $request){
        $auth = $request->user();
        $auth_role = $auth->role;
        $plannings = [];
        $condition = "";
        if ($request->date_planning) {
            $condition .= " WHERE pln.date_planning = '$request->date_planning' ";
        }
        if($auth_role == "resp_sup"){
            $condition .= " AND st.resp_sup_id = $auth->id";
        }
        if ($auth_role == "superviseur") {
            $condition .= " AND st.superviseur_id = $auth->id";
        }
        if($request->site_id){
            $condition .= " AND st.idsite = $request->site_id";
        }
        

        if(in_array($auth_role, ['resp_sup', 'resp_op']) || in_array($request->user()->id, [1, 105])){
            // if(!in_array($request->user()->id, [1, 55, 105])){
            //     $request->merge(['user_id' => $request->user()->id]);
            // }
            if($auth_role == 'resp_sup'){
                $request->merge(['user_id' => $request->user()->id]);
            }
            $plannings = DB::select("SELECT pln.id,st.idsite, st.nom as 'site', pln.date_planning,
                st.nb_agent_night, st.nb_agent_day, st.nb_agent_night_planning, st.nb_agent_day_planning, pln.user_id, hr.nom as 'horaire', u.name as 'superviseur'
                FROM plannings pln 
                LEFT JOIN sites st ON st.idsite = pln.site_id
                LEFT join users u on u.id = st.superviseur_id
                LEFT JOIN horaires hr ON hr.id = st.horaire_pointage_id
                " . $condition . " order by u.id desc", []);
        }
        else if($auth_role == 'superviseur'){
            $plannings = DB::select("SELECT pln.id,st.idsite, st.nom as 'site', pln.date_planning,
                st.nb_agent_night, st.nb_agent_day, pln.user_id, hr.nom as 'horaire', u.name as 'superviseur'
                FROM plannings pln 
                LEFT JOIN sites st ON st.idsite = pln.site_id
                LEFT join users u on u.id = st.superviseur_id
                LEFT JOIN horaires hr ON hr.id = st.horaire_pointage_id
                " . $condition . " order by u.id desc", []);
        }
        $planning_ids =  array_map(function ($resultItem) {
            return (string)$resultItem->id;
        }, $plannings);

        if (count($planning_ids) > 0) {
            $implodeIds = implode(',', $planning_ids);
        } else
            $implodeIds = "\"\"";
        $pointages = DB::select(
            "SELECT ptg.id,ptg.planning_id, ptg.agent_id, ptg.date_pointage, 
            emp.nom, emp.societe_id, emp.numero_stagiaire, emp.numero_employe, 
            emp.num_emp_saoi, emp.num_emp_soit, emp.soft_delete
            FROM planning_pointages ptg 
            LEFT JOIN employes emp ON emp.id = ptg.agent_id
            WHERE ptg.planning_id in (". $implodeIds .")"
        );
        foreach ($plannings as $pl) {
            foreach ($pointages as $ptg) {
                if ($pl->id == $ptg->planning_id) {
                    $pl->pointages[] = $ptg;
                }
            }
        }
        return response(compact('plannings'));
    }
    
    public function planning_non_fait(Request $request){
        $auth = $request->user();
        $date_planning = $request->date_planning ?? (new \DateTime())->format('Y-m');
        $pln = DB::select("SELECT count(st.idsite), u.name as 'resp_sup', u.id as 'resp_sup_id'
            FROM sites st 
            LEFT JOIN users u ON u.id = st.resp_sup_id
            LEFT JOIN plannings pln ON pln.site_id = st.idsite and pln.date_planning = ?
            WHERE (st.soft_delete is null or st.soft_delete = 0) 
            AND pln.id is null
            AND st.superviseur_id is not null group by u.id", [$date_planning]);
        return response($pln);
    }

    public function get_site_detail(Request $request){
        $site = DB::select("SELECT  st.nom as 'site', st.idsite,
            st.superviseur_id, st.resp_sup_id, st.total_hour, hr.day_1, hr.day_2, hr.day_3, hr.day_4, hr.day_5, hr.day_6, hr.day_0, hr.day_ferie,
            hr.night_1, hr.night_2, hr.night_3, hr.night_4, hr.night_5, hr.night_6, hr.night_0, hr.night_ferie,
            st.nb_agent_night, st.nb_agent_day, h.nom as 'horaire', st.nb_agent_night_planning, st.nb_agent_day_planning
            FROM sites st
            LEFT JOIN horaire_effectifs hr ON hr.site_id = st.idsite
            LEFT JOIN horaires h ON h.id = st.horaire_pointage_id
            WHERE st.idsite=?", [$request->site_id])[0];
        return response(compact('site'));
    }

    public function check_planning(Request $request, $id){
        $origin_planning = Planning::find($id);
        $check_planning = Planning::where('site_id', $origin_planning->site_id)->where('date_planning', $request->date_planning)->first();
        if($check_planning)
            return response(['error' => "Ce planning existe déjà, Choisissez une date différente"]);
        return response(['success' => "Ce planning n'existe pas"]);
    }

    public static function planning_not_done_manager(Request $request){
        $auth = $request->user();
        $query_and = '';
        if($request->user_id)
            $query_and .= " AND st.resp_sup_id = " . $request->user_id . " ";
        if($request->site_id)
            $query_and .= " AND st.idsite = " . $request->site_id . " ";
        if (in_array($auth->role, ['validateur', 'resp_op', 'access'])) {
            $date_planning = $request->date_planning ?? (new \DateTime())->format('Y-m');
            $plannings = DB::select("SELECT u.id AS resp_sup_id, u.name AS resp_sup_name, u.email as resp_sup_email,
                COUNT(st.idsite) AS nb_sites_non_faits, ag.nom AS agence
                FROM sites st
                LEFT JOIN plannings pln ON pln.site_id = st.idsite AND pln.date_planning = ?
                LEFT JOIN users u ON st.resp_sup_id = u.id
                LEFT JOIN employes emp ON emp.id = u.employe_id
                LEFT JOIN agences ag ON ag.id = emp.agence_id
                WHERE pln.id IS NULL and st.resp_sup_id is not null
                $query_and
                GROUP BY u.id, u.name
                ORDER BY nb_sites_non_faits DESC LIMIT ?, 30", [$date_planning, $request->offset ?? 0]);
            return response(compact('plannings'));
        }
        return response(['error' => "EACCES"]);
    }

    public static function show_not_done_manager(Request $request, $id){
        $auth = $request->user();
        $query_and = '';
        if($request->user_id)
            $query_and .= " AND st.resp_sup_id = " . $request->user_id . " ";
        if($request->site_id)
            $query_and .= " AND st.idsite = " . $request->site_id . " ";

        $date_planning = $request->date_planning ?? (new \DateTime())->format('Y-m');
        $manager = DB::select("SELECT u.id, u.name, u.email, ag.nom AS agence, u.flotte
            FROM users u
            LEFT JOIN employes emp on emp.id= u.employe_id 
            LEFT JOIN agences ag on ag.id = emp.agence_id
            WHERE u.id = ?", [$id])[0];
        $sites = DB::select("SELECT st.* FROM sites st 
            LEFT JOIN plannings pln ON pln.site_id = st.idsite AND pln.date_planning = ? 
            WHERE pln.id IS NULL AND st.resp_sup_id = ? $query_and", [$date_planning, $id]);
        return response(compact('sites', 'manager'));
    }
}
