<?php

namespace App\Http\Controllers;

use App\Models\CriterePart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CriterePartController extends Controller
{
    public static function employe($id, Request $request){
        $auth = $request->user();
        if (in_array($auth->role, ["resp_rh", "rh", "validateur"])) {
            $criteres = DB::select("SELECT cr.id, cr.designation, cr.montant
                FROM critere_parts cr
                WHERE cr.employe_id = ? and (cr.soft_delete is null or cr.soft_delete = 0)
                order by id desc", [$id]);
            return response(compact('criteres'));
        }
        return response(["error" => "EACCES"]);
    }
    
    protected function validateAndSetCritere($request, $critere){
        $auth = $request->user();
        if(!$critere->id){
            $validator = Validator::make($request->all(), [
                'employe_id' => 'required',
                'designation' => 'required',
                'montant' => 'required|numeric|gt:0',
            ]);
            $critere->employe_id = $request->employe_id;
        }
        else {
            $validator = Validator::make($request->all(), [
                'designation' => 'required',
                'montant' => 'required|numeric|gt:0',
            ]);
        }
        $critere->designation = $request->designation;
        $critere->montant = $request->montant;
        return $validator;
    }

    public static function store(Request $request){
        if(in_array($request->user()->role, ["admin", "rh", "resp_rh"])){
            $critere = new CriterePart();
            $validator = self::validateAndSetCritere($request, $critere);
            if($validator->fails())
                return ['error' => $validator->errors()->first()];
            $critere->save();
            return response(["success" => "Critère ajouté"]);
        }
        return response(["error" => "EACCES"]);
    }

    public static function update($id, Request $request){
        if(in_array($request->user()->role, ["admin", "rh", "resp_rh"])){
            $critere = CriterePart::find($id);
            $validator = self::validateAndSetCritere($request, $critere);
            if($validator->fails())
                return ['error' => $validator->errors()->first()];
            $critere->save();
            return response(["success" => "Critère modifié"]);
        }
        return response(["error" => "EACCES"]);
    }

    public static function delete($id, Request $request){
        if(in_array($request->user()->role, ["admin", "rh", "resp_rh"])){
            $critere = CriterePart::find($id);
            $critere->soft_delete = 1;
            $critere->save();
            return response(["success" => "Critère supprimé"]);
        }
        return response(["error" => "EACCES"]);
    }
}
