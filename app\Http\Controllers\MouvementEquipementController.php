<?php

namespace App\Http\Controllers;

use App\Models\MouvementEquipement;
use App\Models\Site;
use App\Models\Equipement;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\LigneEquipementController;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MouvementEquipementController extends Controller
{
    protected function searchIndex(Request $request) {
        $auth_id = $request->user()->id;
        $role = $request->user()->role;
        $searchArray = [];
        if ($request->a_recuperer) {
            if (!in_array($role, ['superviseur'])) {
                $searchArray[] = " emp.soft_delete = '" . $request->a_recuperer . "' ";
                $searchArray[] = " meq.type_mouvement = 'sortie'";
                $searchArray[] = " (meq.`status` is null OR meq.`status` = 'en cours') ";
            } else {
                $searchArray[] = " emp.soft_delete = '" . $request->a_recuperer . "' ";
                $searchArray[] = " meq.type_mouvement = 'sortie'";
                $searchArray[] = " (meq.`status` is null OR meq.`status` = 'en cours') ";
                $searchArray[] = " meq.superviseur_id = '" . $auth_id . "' ";
            }
        }
        if ($request->id) {
            $searchArray[] = " meq.id = '" . $request->id . "' ";
        }
        if ($request->nom) {
            $searchArray[] = " emp.nom like '%" . $request->nom . "%' ";
        }
        if($request->matricule)
        $searchArray[] = " (emp.numero_stagiaire = " . $request->matricule . 
            " or emp.numero_employe = " . $request->matricule .
            " or emp.num_emp_soit = " . $request->matricule .
            " or emp.num_emp_saoi = " .$request->matricule .
            ") ";
        if ($request->type_mvt) {
            $searchArray[] = " meq.type_mouvement = '" . $request->type_mvt . "' ";
        }
        if ($request->created_at) {
            $searchArray[] = " meq.date_mouvement = '" . $request->created_at . "' ";

        }

        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by meq.id desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by meq.id desc limit ". $request->offset . ", 30";
        return compact('query_where', 'query_and');
    }

    public function index(Request $request) {
        $auth = $request->user();
        $search = $this->searchIndex($request);
        $query_where = $search['query_where'];
        $query_and = $search['query_and'];
        $regions = RegionUsersController::getRegions($request);
        if (in_array($auth->role, ["resp_op"])) {
            $mouvement_equipement = DB::select(
            "SELECT meq.id, meq.employe_id, meq.site_id, meq.ref, meq.type_mouvement, meq.status, emp.nom, emp.societe_id, emp.numero_employe, emp.num_emp_soit, emp.numero_stagiaire, emp.num_emp_saoi, emp.soft_delete, meq.date_mouvement,s.nom as site, fs.nom as forSite
            FROM mouvement_equipement meq
            LEFT JOIN employes emp ON meq.employe_id = emp.id
            LEFT JOIN sites s ON s.idSite = emp.site_id
            LEFT JOIN sites fs ON fs.idSite = meq.site_id
            WHERE s.group_pointage_id in($regions)
            " . $query_and);
        } else if (in_array($auth->role, ["superviseur", "resp_sup"])) {
            $column = $auth->role == "superviseur" ? "s.superviseur_id" : "s.resp_sup_id";
            $mouvement_equipement = DB::select(
            "SELECT meq.id, meq.employe_id, meq.site_id, meq.ref, meq.type_mouvement, meq.status, emp.nom, emp.societe_id, emp.numero_employe, emp.num_emp_soit, emp.numero_stagiaire, emp.num_emp_saoi, emp.soft_delete, meq.date_mouvement,s.nom as site, fs.nom as forSite
            FROM mouvement_equipement meq
            LEFT JOIN employes emp ON meq.employe_id = emp.id
            LEFT JOIN sites s ON s.idSite = emp.site_id
            LEFT JOIN sites fs ON fs.idSite = meq.site_id
            WHERE $column = ?
            " . $query_and , [$auth->id]);
        } else {
            $mouvement_equipement = DB::select(
            "SELECT meq.id, meq.employe_id, meq.site_id, meq.ref, meq.type_mouvement, meq.status, emp.nom, emp.societe_id, emp.numero_employe, emp.num_emp_soit, emp.numero_stagiaire, emp.num_emp_saoi, emp.soft_delete, meq.date_mouvement,s.nom as site, fs.nom as forSite
            FROM mouvement_equipement meq
            LEFT JOIN employes emp ON meq.employe_id = emp.id
            LEFT JOIN sites s ON s.idSite = emp.site_id
            LEFT JOIN sites fs ON fs.idSite = meq.site_id
            " . $search['query_where']);
        }
        return response(compact('mouvement_equipement'));
    }

    public function detail(Request $request, $id){
        $dotation = DB::select(
        "SELECT meq.id, meq.employe_id, meq.site_id, meq.ref, meq.type_mouvement, meq.status, emp.nom, emp.societe_id, emp.numero_employe, emp.num_emp_soit, emp.numero_stagiaire, emp.num_emp_saoi, emp.soft_delete, emp.observation, emp.date_sortie, u.name as demandeur, u.email, s.nom as site, meq.date_mouvement, fs.nom as forSite 
        FROM mouvement_equipement meq
        LEFT JOIN employes emp ON meq.employe_id = emp.id
        LEFT JOIN sites s ON emp.site_id = s.idSite
        LEFT JOIN sites fs ON fs.idSite = meq.site_id
        LEFT JOIN users u ON u.id = meq.superviseur_id
        WHERE meq.id = ?", [$id])[0];

        $ligne_equipements = DB::select("SELECT leq.id, leq.article, leq.mouvement_equipement_id, leq.status, leq.estUsé, leq.estDeduit, ac.name, ac.designation
        FROM ligne_equipement leq
        LEFT JOIN articles ac ON ac.name = leq.article
        WHERE leq.mouvement_equipement_id = ?", [$id]);

        $displays = [];
        foreach ($ligne_equipements as $le) {
            if ($dotation->id == $le->mouvement_equipement_id) {
                $displays[] = [
                    'name' => $le->article,
                    'designation' => $le->designation,
                    'status' => $le->status,
                    'estUsé' => $le->estUsé,
                    'estDeduit' => $le->estDeduit
                ];
            }
        }
        if (count($displays) > 0)
            $dotation->articles = $displays;

        return response(compact('dotation'));
    }

    public function get_actif_agent(Request $request) {
        $agents = DB::select("SELECT emp.id, emp.nom, s.nom AS poste, s.superviseur_id AS superviseur FROM employes emp
        LEFT JOIN sites s ON s.idSite = emp.site_id
        WHERE emp.nom LIKE ? AND emp.soft_delete = 0
        ORDER BY emp.id DESC LIMIT ?, 30",
        ['%' . $request->value . '%', $request->offset]);
        return response(compact('agents'));
    }

    public function do_back(Request $request, $id, $estDirect = false) {
        $selected_articles = $request->input('selectedItems');
        $date = Carbon::parse($request->input('date_mouvement'))->format('Y-m-d');
        $employe_proprio = DB::select("SELECT meq.employe_id as employe, meq.superviseur_id as superviseur FROM mouvement_equipement meq
        WHERE meq.id = ?" ,[$id])[0];
        $site_proprio = DB::select("SELECT meq.site_id as forsite, meq.superviseur_id as superviseur FROM mouvement_equipement meq
        WHERE meq.id = ?" ,[$id])[0];
        if (!$estDirect) {
            if ($employe_proprio->employe) {
                $response = MouvementEquipementController::new_dotation("retourner", "entré", $date, $selected_articles, $employe_proprio->employe, $employe_proprio->superviseur);
            } else {
                if ($site_proprio->forsite) {
                    $response = MouvementEquipementController::new_dotation("retourner", "entré", $date, $selected_articles, $site_proprio->forsite, $site_proprio->superviseur);
                }
            }
        } else {
            if ($employe_proprio->employe) {
                $response = MouvementEquipementController::new_dotation("direct", "entré", $date, $selected_articles, $employe_proprio->employe, $employe_proprio->superviseur, true);
            } else {
                if ($site_proprio->forsite) {
                    $response = MouvementEquipementController::new_dotation("direct", "entré", $date, $selected_articles, $site_proprio->forsite, $site_proprio->superviseur, true);
                }
            }
        }
        if ($response->getStatusCode() == 200) {
            $responseData = json_decode($response->getContent(), true);
            $new_id = $responseData['newId'];
            if (!$estDirect) {
                LigneEquipementController::update_ligne_equipement("retourner", $selected_articles, $id);
            } else {
                LigneEquipementController::update_ligne_equipement("direct", $selected_articles, $id);
            }
            if (is_array($selected_articles) && count($selected_articles) > 0) {
                foreach ($selected_articles as $article) {
                    HistoriqueController::action_dotation($request, $article . " retourné", $id);
                    HistoriqueController::action_dotation($request, $article . " retourné", $new_id);
                }
            }
            $dotation = MouvementEquipement::find($id);
            $nb_article = DB::select('SELECT leq.id FROM ligne_equipement leq WHERE leq.mouvement_equipement_id = ?'
            , [$id]);
            $has_done = DB::select('SELECT leq.id
            FROM ligne_equipement leq
            WHERE (leq.status IN ("retourné", "transferé", "direct")
            OR (leq.status = "attribué" AND leq.estUsé = 1)
            OR (leq.status = "attribué" AND leq.estDeduit = 1))
            AND leq.mouvement_equipement_id = ?', [$id]);
            if (count($has_done) > 0)
                $dotation->status = "en cours";
            if (count($has_done) == count($nb_article)) {
                $dotation->status = "termine";
            }
            $dotation->save();
            return response()->json([
                'newId' => $new_id,
                'message' => 'Dotation créée avec succès'
            ]);
        }
    }

    public function do_transfert(Request $request, $id) {
        $selected_articles = $request->input('selectedItems');
        $selected_employe = $request->input('selectedAgent');
        $employe_id = $selected_employe['id'] ?? null;
        $site = $selected_employe['site'] ?? null;
        
        if ($selected_employe) {
            if ($site) {
                $superviseur = Site::where('idSite', $site['id'])->pluck('superviseur_id')->first();
            } else {
                $superviseur = null;
            }
        } else {
            $superviseur = Site::where('idSite', $request->input('selectedSite')['id'])->pluck('superviseur_id')->first();
        }
        $superviseur_id = $superviseur;

        if ($request->input('type') == "entré") {
            $nbStock = [];
            if (is_array($selected_articles) && count($selected_articles) > 0) {
                foreach ($selected_articles as $article) {
                    if (ArticleController::get_nbStock($article) >= 1) {
                        array_push($nbStock, ArticleController::get_nbStock($article));
                    }
                }
            }
            if ($selected_employe) {
                $response = MouvementEquipementController::new_dotation("transferer", "sortie", Carbon::now()->format('Y-m-d'), $selected_articles, $employe_id, $superviseur_id);
            } else {
                $response = MouvementEquipementController::new_dotation("transferer", "sortie", Carbon::now()->format('Y-m-d'), $selected_articles, $request->input('selectedSite')['id'], $superviseur_id);
            }
            if ($response->getStatusCode() == 200) {
                $responseData = json_decode($response->getContent(), true);
                $new_id = $responseData['newId'];
                LigneEquipementController::update_ligne_equipement("transferer", $selected_articles, $id);
                if (is_array($selected_articles) && count($selected_articles) > 0) {
                    foreach ($selected_articles as $article) {
                        HistoriqueController::action_dotation($request, $article . " transferé", $id);
                        HistoriqueController::action_dotation($request, $article . " transferé", $new_id);
                    }
                }
                $dotation = MouvementEquipement::find($id);
                $nb_article = DB::select('SELECT leq.id FROM ligne_equipement leq WHERE leq.mouvement_equipement_id = ?'
                , [$id]);
                $has_done = DB::select('SELECT leq.id
                FROM ligne_equipement leq
                WHERE leq.mouvement_equipement_id = ?
                AND (
                    leq.status = "transferé"
                    OR (leq.status = "retourné" AND leq.estUsé = 1)
                    OR (leq.status = "retourné" AND leq.estDeduit = 1)
                )', [$id]);

                if (count($has_done) > 0)
                    $dotation->status = "en cours";
                if (count($has_done) == count($nb_article)) {
                    $dotation->status = "termine";
                }
                $dotation->save();
                return response()->json([
                    'newId' => $new_id,
                    'message' => 'Dotation créée avec succès'
                ]);
            }
            // if (count($nbStock) == count($selected_articles)) {
            // } else {
            //     return response(["message" => "Certaine article n'ont pas de stock disponible"]);
            // }
        } else {
            MouvementEquipementController::do_back($request, $id, true);
            $nbStock = [];
            if (is_array($selected_articles) && count($selected_articles) > 0) {
                foreach ($selected_articles as $article) {
                    if (ArticleController::get_nbStock($article) >= 1) {
                        array_push($nbStock, ArticleController::get_nbStock($article));
                    }
                }
            }
            if ($selected_employe) {
                $response = MouvementEquipementController::new_dotation("transferer", "sortie", Carbon::now()->format('Y-m-d'), $selected_articles, $employe_id, $superviseur_id);
            } else {
                $response = MouvementEquipementController::new_dotation("transferer", "sortie", Carbon::now()->format('Y-m-d'), $selected_articles, $request->input('selectedSite')['id'], $superviseur_id);
            }
            if ($response->getStatusCode() == 200) {
                $responseData = json_decode($response->getContent(), true);
                $new_id = $responseData['newId'];
                LigneEquipementController::update_ligne_equipement("direct", $selected_articles, $id);
                if (is_array($selected_articles) && count($selected_articles) > 0) {
                    foreach ($selected_articles as $article) {
                        HistoriqueController::action_dotation($request, $article . " transferé", $id);
                        HistoriqueController::action_dotation($request, $article . " transferé", $new_id);
                    }
                }
                $dotation = MouvementEquipement::find($id);
                $nb_article = DB::select('SELECT leq.id FROM ligne_equipement leq WHERE leq.mouvement_equipement_id = ?'
                , [$id]);
                $has_done = DB::select('SELECT leq.id
                FROM ligne_equipement leq
                WHERE (leq.status IN("direct", "retourné")
                OR (leq.status = "attribué" AND leq.estUsé = 1)
                OR (leq.status = "attribué" AND leq.estDeduit = 1))
                AND leq.mouvement_equipement_id = ?', [$id]);

                if (count($has_done) > 0)
                $dotation->status = "en cours";
                if (count($has_done) == count($nb_article)) {
                $dotation->status = "termine";
                }
                $dotation->save();

                return response()->json([
                    'newId' => $new_id,
                    'message' => 'Dotation créée avec succès'
                ]);
            }
            // if (count($nbStock) == count($selected_articles)) {
            // } else {
            //     return response(["message" => "Certaine article n'ont pas de stock disponible"]);
            // }
        }
    }

    public function declaration_usure(Request $request, $id) {
        $selected_articles = $request->input('selectedItems');
        if (is_array($selected_articles) && count($selected_articles) > 0) {
            LigneEquipementController::update_ligne_equipement("usure", $selected_articles, $id);
            foreach ($selected_articles as $article) {
                HistoriqueController::action_dotation($request, $article . " usée", $id);
            }
        }
    }

    public function deduction(Request $request, $id) {
        $selected_articles = $request->input('selectedItems');
        if (is_array($selected_articles) && count($selected_articles) > 0) {
            LigneEquipementController::update_ligne_equipement("deduction", $selected_articles, $id);
            foreach ($selected_articles as $article) {
                HistoriqueController::action_dotation($request, $article . " deduit", $id);
            }
        }
    }

    public static function new_dotation($action, $type, $date, $selected_articles, $for, $superviseur, $estDirect = false) {
        $formatted_date = Carbon::parse($date)->format('Y-m-d');
        $mouvement = new MouvementEquipement();
        if ($type == "sortie") {
            $maxRef = DB::table('mouvement_equipement')
                ->where('type_mouvement', 'sortie')
                ->max('ref');
            $mouvement->ref = $maxRef ? $maxRef + 1 : 1;
        } else {
            $maxRef = DB::table('mouvement_equipement')
                ->where('type_mouvement', 'entre')
                ->max('ref');
            $mouvement->ref = $maxRef ? $maxRef + 1 : 1;
        }
        $mouvement->type_mouvement = $type;
        $mouvement->date_mouvement = $date;
        if (count($selected_articles) == 1 && $selected_articles[0] == "impermeable") {
            $mouvement->site_id = $for;
        } else {
            $mouvement->employe_id = $for;
        }
        if ($superviseur) {
            $mouvement->superviseur_id = $superviseur;
        } else {
            $mouvement->superviseur_id = null;
        }
        $mouvement->save();
        $new_id = $mouvement->id;
        $status = "attribué";
        $action == "retourner" ? $status = "retourné" : $action == "transferer" && $status = "attribué";
        if ($estDirect) {
            $status = "direct";
        }
        if (is_array($selected_articles) && count($selected_articles) > 0) {
            foreach ($selected_articles as $article) {
                LigneEquipementController::new_ligne_equipement($article, $new_id, $status);
                if ($action == "nouveau" || $action == "transferer") {
                    ArticleController::decrementerStock($article);
                } else if ($action == "retourner") {
                    ArticleController::incrementerStock($article);
                }
            }
        } else {
            return response(["messsage" => "Aucun article sélectionné"]);
        }
        $dotation = [
            "user_id" => Auth::user()->id,
            "id" => $mouvement->id,
            "ref" => $mouvement->ref,
            "type" => $mouvement->type_mouvement,
            "date" => $mouvement->date_mouvement,
            "employe_id" => $mouvement->employe_id,
            "superviseur_id" => $mouvement->superviseur_id,
            "articles" => $selected_articles
        ];
        HistoriqueController::new_dotation($dotation);
        return response()->json([
            'newId' => $new_id,
            'message' => 'Dotation créée avec succès'
        ]);
    }

    public function store(Request $request) {
        $auth = $request->user();
        $action = "nouveau";
        $type = $request->input('type');
        $date = $request->input('dateMouvement');
        $selected_articles = $request->input('articles');
        $employe_id = $request->input('employe.id');
        $site = Site::where('idSite', $request->input('employe.site.id'))->first();
        if ($site && $site->superviseur_id) {
            $superviseur_id = $site->superviseur_id;
        } else {
            $superviseur_id = null;
        }
        $nbStock = [];
        if (is_array($selected_articles) && count($selected_articles) > 0) {
            foreach ($selected_articles as $article) {
                if (ArticleController::get_nbStock($article) >= 1) {
                    array_push($nbStock, ArticleController::get_nbStock($article));
                }
            }
        }
        $response = MouvementEquipementController::new_dotation($action, $type, $date, $selected_articles, $employe_id, $superviseur_id);
        if ($response->getStatusCode() == 200) {
            $responseData = json_decode($response->getContent(), true);
            $new_id = $responseData['newId'];
            return response(["success" => "Dotation bien enregistré", "id" => $new_id]);
        }
        // if (count($nbStock) == count($selected_articles)) {
        // } else {
        //     return response(["failure" => "Stock insuffisant"]);
        // }
    }

    public function dotation_equipement(Request $request, $id) {
        $equipement = Equipement::find($id);
        $employe_id = $equipement->employe_id;
        $ligne_equipement = DB::select("SELECT meq.id, meq.type_mouvement, GROUP_CONCAT(ac.designation ORDER BY leq.id SEPARATOR ', ') AS articles, meq.date_mouvement
        FROM mouvement_equipement meq
        LEFT JOIN ligne_equipement leq ON leq.mouvement_equipement_id = meq.id
        LEFT JOIN articles ac ON ac.name = leq.article
        WHERE meq.employe_id = ?
        AND meq.type_mouvement = 'sortie'
        AND (meq.status is null OR meq.status = 'en cours')
        AND (leq.status = 'attribué')
        GROUP BY meq.id, meq.type_mouvement, meq.date_mouvement", [$employe_id]);
        return response(compact('ligne_equipement'));
    }

    public function dotation_employe(Request $request, $id) {
        $ligne_equipement = DB::select("SELECT meq.id, meq.type_mouvement, GROUP_CONCAT(ac.designation ORDER BY leq.id SEPARATOR ', ') AS articles, meq.date_mouvement
        FROM mouvement_equipement meq
        LEFT JOIN ligne_equipement leq ON leq.mouvement_equipement_id = meq.id
        LEFT JOIN articles ac ON ac.name = leq.article
        WHERE meq.employe_id = ?
        AND meq.type_mouvement = 'sortie'
        AND (meq.status is null OR meq.status = 'en cours')
        AND (leq.status = 'attribué')
        GROUP BY meq.id, meq.type_mouvement, meq.date_mouvement", [$id]);
        return response(compact('ligne_equipement'));
    }
}
