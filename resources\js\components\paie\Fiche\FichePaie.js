import React, { useMemo } from "react";
import {
    PDFDownloadLink,
} from "@react-pdf/renderer";

import useToken from "../../util/useToken";
import { useState } from "react";
import DocumentPDF from "./DocumentPDF";
import { useEffect } from "react";
import moment from "moment";
// import InputText from "../../input/InputText";
const MyDocument = ({ siret, setSiret, data, id, updateData }) => {
    
    // const controllInputSiret = (event) => {
    //     const newVal = event.target.value.replace(/[^0-9]/g, "");
    //     let formattedValue = "";
    //     for (let i = 0; i < newVal.length; i++) {
    //         if (i == 5) formattedValue += " ";
    //         if (i == 7) formattedValue += " ";
    //         if (i == 11) formattedValue += " ";
    //         if (i == 12) formattedValue += " ";
    //         formattedValue += newVal[i];
    //     }
    //     setSiret(formattedValue);
    // }

    const [paie, setPaie] = useState(null);
    const [paies, setPaies] = useState(null);
    // const [siret, setSiret] = useState("51333 31 2011 0 00658");
    // const [showPdf, toggleShowPdf] = useState(false);
    const getPaie = () => {
        if (id) {   
            axios
                .get("/api/paie?offset=0&id=" + id, useToken())
                .then((response) => {
                    if (response.data.paies) setPaie(response.data.paies[0]);
                });
        }
        else if (data) {
            setPaies(data)
            // setPaie(data[0]);
            // let axiosPromises = [];
            // for (let i = 0; i < data[0].length; i++) {
            //     axiosPromises.push(
            //         axios.get("/api/paie?offset=0&id=" + data[i],
            //             useToken()
            //         )
            //     );
            // }

            // Promise.all(axiosPromises)
            //     .then((responses) => {
            //         let datas = [];
            //         responses.forEach((response) => {
            //             if (response.data.paie) {
            //                 datas.push(response.data.paie);
            //             }
            //         });
            //         setPaie(datas[0])
            //         setPaies(datas)
            //     })
            //     .catch((error) => {
            //         console.error("Error fetching data:", error);
            //     });
        }
    };

    useEffect(() => {
        let isMounted = true;
        if (isMounted) {
            getPaie();
        }
        return () => {
            isMounted = false;
        };
    }, [id, data]);
    const documentPdf = useMemo(() => (
         (paies || paie) && <DocumentPDF paie={paie} siret={siret} setSiret={setSiret} paies={paies} />
         ), [paie, siret, setSiret, paies]);

    const fileName = useMemo(() => {
        if (paies && paies.length > 0) {
            return "FichesDesPaies.pdf";
        } else if(paie){
            return `${paie.nom}-${moment(paie.date_paie).format("MMM YYYY")}.pdf`;
        }
    }, [paies, paie]);

    return (
        <div>
            {(paies || paie) && (
                <div>
                    <PDFDownloadLink
                        document={documentPdf} fileName={fileName}
                    >
                            {({loading }) =>
                                loading
                                ? <button className="btn btn-primary" style={{
                                    display: 'inline-block',
                                    fontSize: '12pt',
                                    padding: '10px 15px',
                                    width: '150px',
                                    border: 'none',
                                    color: '#fff',
                                    cursor: 'pointer'
                                }} disabled>Imprimer</button>
                                    : (
                                <button style={{
                                    display: 'inline-block', 
                                    fontSize: '12pt', 
                                    padding: '10px 15px',
                                    width: '150px',
                                    border: 'none',
                                    color: '#fff',
                                    cursor: 'pointer'}} 
                                        className='btn-primary'
                                        onClick={updateData?()=>updateData(true):null}
                                >
                                  Imprimer     
                                        {/* {paie.status == "done"? "Imprimer":"Terminer - Imprimer"} */}
                              
                                 </button>
                            )
                            }
                        </PDFDownloadLink>
                </div>
            )}
        </div>
    );
};

export default MyDocument;
