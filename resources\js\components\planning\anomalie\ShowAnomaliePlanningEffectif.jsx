import React, { useEffect, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import LoadingPage from '../../loading/LoadingPage'
import useToken from '../../util/useToken'
import axios from 'axios'
import ShowHeader from '../../view/ShowHeader'
import moment from 'moment'
import "../../layout/tab.css"

export default function ShowAnomaliePlanningEffectif({ auth, currentId, setCurrentId, size }) {
    const [isLoading, toggleLoading] = useState(false)
    const [anomalie, setAnomalie] = useState()
    const params = useParams()
    const [dayWeek, setDayWeek] = useState()
    const locationSearch = new URLSearchParams(useLocation().search);
    const date_service = locationSearch.get('date_service') ? moment(locationSearch.get('date_service')) : moment().subtract(1, 'days')
    const dayNames = ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'];


    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/anomalie_pl_eff/show/' + (currentId ? currentId : params.id) + "?date_service=" + date_service.format('YYYY-MM-DD HH:mm:ss'), useToken())
            .then((res) => {
                if (isMounted) {
                    if (!res.data)
                        setCurrentId()
                    else {
                        setAnomalie(res.data.anomalie)
                        setDayWeek(res.data.anomalie.current_date.split("_"))
                    }
                }
                toggleLoading(false)
            })
            .catch((e) => {
                console.error(e)
                toggleLoading(false)
            })
    }
    useEffect(() => {
        updateData()
    }, [currentId])

    return (
        <div>
            {
                isLoading ?
                    <LoadingPage />
                    :
                    <>
                        {anomalie &&
                            <div>
                                <ShowHeader size={size} label="Planning et effectif" id={currentId} closeDetail={() => setCurrentId()} />
                                <div className="card-container">
                                    <div className="badge-container"><span>
                                            {
                                                anomalie.nb_ptg > anomalie[anomalie.current_date] ?
                                                    <span className="badge-outline badge-outline-purple">Surplus: { anomalie.nb_ptg - anomalie[anomalie.current_date]}</span>
                                                    :
                                                    <span className={"badge-outline badge-outline-pink"}>
                                                        Manque: { anomalie[anomalie.current_date] - anomalie.nb_ptg}
                                                    </span>
                                                        
                                            }
                                        </span>
                                    </div>
                                    <h3><div>{anomalie.site}</div></h3>
                                            
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Nb agents plannifiés: <span className='text'>{anomalie.nb_ptg}</span>
                                    </p>
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        {`Nb agents ${dayWeek[1] == 'ferie'? "Ferie" : dayNames[dayWeek[1]] }  ${dayWeek[0] == 'night'? "NUIT" : "JOUR"} du site`} : <span className='text'>{anomalie[anomalie.current_date]}</span>
                                    </p>
                                    
                                    {
                                        anomalie.resp_sup_id &&
                                        <>
                                            <p style={{ whiteSpace: "pre-line" }}>
                                                Manager: <span className='text'>{anomalie.resp_sup + " <" + anomalie.resp_sup_email + ">"}</span>
                                            </p>
                                            <p style={{ whiteSpace: "pre-line" }}>
                                                Flotte du manager: <span className='text'>{anomalie.flotte}</span>
                                            </p>
                                        </>
                                    }
                                </div>
                            </div>
                        }
                    </>
            }
        </div>
    )
}
