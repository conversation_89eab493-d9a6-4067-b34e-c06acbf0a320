import React, { useState } from 'react';
import axios from 'axios';

import InputTextLg from '../input/InputTextLg';

import './login.css';
import ButtonSubmit from '../input/ButtonSubmit';

const Login = () => {
    const [error, setError] = useState("")
    const [email, setEmail] = useState("")
    const [password, setPassword] = useState("")

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        axios.post('/api/login', {
            email: email,
            password: password
        })
        .then((res) => {
            if(res.data.error)
                setError(res.data.error)
            else if(res.data.token){
                localStorage.setItem("token", res.data.token)
                window.location.reload()
            }
        })
    }

    return (
        <div className='box'>
            <div>
                <img src='/img/logo_dirickx.jpg' width="100%" height="70"/>
                <div id="loginFormContainer">
                    <center>
                        <div id="logoTlsContainer">
                            <img id="logoTls" src="/img/blue_drx.svg"/>
                        </div>
                    </center>
                    <form onSubmit={handleSubmit}>
                        <InputTextLg 
                            value={email} 
                            onChange={(value) => {setEmail(value)}} 
                            type="email" 
                            placeholder='Email'/>
                        <InputTextLg 
                            value={password} 
                            onChange={(value) => {setPassword(value)}} 
                            type="password" 
                            placeholder='Password'/>
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                        }
                        <ButtonSubmit label="Se connecter"/>
                    </form>
                </div>
            </div>
        </div>
    );
}
export default Login