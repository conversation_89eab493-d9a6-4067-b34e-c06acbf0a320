import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import LoadingPage from '../loading/LoadingPage';
import InfiniteScroll from 'react-infinite-scroll-component';
import StatusLabel from '../input/StatusLabel';
import matricule from '../util/matricule';
import moment from 'moment';

export default function Sanction({auth, sanctions, setSanctions, currentId, setCurrentId}) {
    const locationSearch = useLocation().search
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'Référence', name: 'id', type:'number'},
        {label: 'Status', name: 'status', type:'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Utilisateur', name: 'user_id', type:'number'},
        {label: 'Employe', name: 'employe_id', type:'number'},
        {label: 'Site', name: 'site_id', type:'number'},
    ]
    if(auth.role != 'superviseur')
        searchItems.push({label: 'Superviseur', name: 'superviseur_id', type:'number'})
        
    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            params.set("offset", 0)
        }
        else
            params.set("offset", sanctions.length)

        axios.get("/api/sanction?" + params ,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setSanctions(res.data.sanctions)
                    else {
                        const list = sanctions.slice().concat(res.data.sanctions)
                        setSanctions(list)
                    }
                    setDataLoaded(res.data.sanctions.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }
    
    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>
                    Sanction
                </h2>
                {   
                    ['superviseur', 'resp_sup', 'resp_op', 'rh', 'resp_rh', 'room'].includes(auth.role) && 
                    <Link className='btn btn-primary' to="/sanction/add">Nouveau</Link>
                }
            </div>
            <SearchBar hasEmploye listItems={searchItems}/>
            {
                sanctions.length == 0 ? 
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                    <InfiniteScroll
                        dataLength={sanctions.length}
                        next={fetchMoreData}
                        hasMore={!allDataLoaded}
                        loader={<LoadingPage/>}
                    >
                        <div className="line-container">
                            <div className="row-list">
                                <b className="line-cell-lg">Employé</b>
                                <b className="status-line">
                                    <StatusLabel color="grey"/>
                                </b>
                                <b className='line-cell-sm'>Service</b>
                                <b>Motif</b>
                            </div>
                        </div>
                        {
                            sanctions.map((sanc) => (
                                <div key={sanc.id} 
                                    className={`line-container ${currentId && currentId == sanc.id ? 'selected' : ''}`} 
                                    onClick={() => setCurrentId(sanc.id)}
                                >
                                    <div className="row-list">
                                        <span className='line-cell-lg'>
                                            {matricule(sanc)} {sanc.employe} 
                                        </span>
                                        <span className="status-line">
                                            <StatusLabel color={sanc.status_color} />
                                        </span>
                                        <span className='line-cell-sm'>{sanc.date_pointage ? `${moment(sanc.date_pointage).format("DD/MM/YY")} ${(moment(sanc.date_pointage).format("HH") == "18" ? " NUIT" : " JOUR")}` : ""}</span>
                                        <span>
                                            {sanc.motif}
                                        </span>
                                    </div>
                                </div>
                            ))
                        }
                    </InfiniteScroll>
            }
        </div>
    } </>
}