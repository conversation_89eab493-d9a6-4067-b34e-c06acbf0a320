<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Approvisionnement;
use App\Models\DaItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\HistoriqueController;

class DaItemController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function done(Request $request, $id){
        $validator = Validator::make($request->all(), [
            'cout' => 'required|integer|gt:0',
        ]);
        if ($validator->fails())
            return response(['error' => $validator->errors()->first()]);
        $item = DB::select("SELECT it.id, it.approvisionnement_id, it.designation, it.done
            FROM da_items it 
            WHERE it.id = ?", [$id])[0];
        $approvisionnement = Approvisionnement::find($item->approvisionnement_id);
        if(in_array($request->user()->role, ["compta", "daf"]) && in_array($approvisionnement->status, ["traite"])){
            DaItem::where('id', $id)->update(['reference' => $request->note, 'done' => 1, 'cout' => $request->cout, 'updated_at' => (new \DateTime())]);
            $approvisionnement->note_id = HistoriqueController::action_approvisionnement($request, "Article terminé : " . mb_substr($item->designation, 0, 25), $approvisionnement->id);
            $not_traite = DB::select('SELECT it.id FROM da_items it WHERE it.done is null and it.approvisionnement_id = ?'
            , [$item->approvisionnement_id]);
            if(count($not_traite) == 0){
                $items = DB::select('SELECT it.id, it.cout FROM da_items it WHERE it.done = 1 and it.approvisionnement_id = ?', [$item->approvisionnement_id]);
                $request->note = null;
                $request->cout = null;
                $total = 0;
                foreach($items as $item)
                    $total = $total + $item->cout;
                $request->total = $total;
                $approvisionnement->total = $total;
                $approvisionnement->note_id = HistoriqueController::action_approvisionnement($request, "DA terminé", $approvisionnement->id);
                $approvisionnement->status = 'done';
            }
            $approvisionnement->save();
            return response(["success" => "Approvisionnement bien envoyée", "id" => $approvisionnement->id]);
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel(Request $request, $id){
        $validator = Validator::make($request->all(), [
            'note' => 'required',
        ]);
        if ($validator->fails())
            return response(['error' => $validator->errors()->first()]);

        $item = DB::select("SELECT it.id, it.approvisionnement_id, ac.service, ac.designation, it.done
            FROM da_items it 
            LEFT JOIN articles ac on ac.name = it.item
            WHERE it.id = ?", [$id])[0];
        $approvisionnement = Approvisionnement::find($item->approvisionnement_id);
        if(is_null($item->done) && $item->service == $request->user()->role && in_array($approvisionnement->status, ["demande", "traite"])){
            DaItem::where('id', $id)->update(['done' => 0]);
            $approvisionnement->note_id = HistoriqueController::action_equipement($request, $item->designation . " annulé", $approvisionnement->id);
            $not_traite = DB::select('SELECT it.id FROM da_items it WHERE it.done is null and it.approvisionnement_id = ?'
            , [$item->approvisionnement_id]);
            if(count($not_traite) > 0)
                $approvisionnement->status = 'traite';
            else {
                $has_done = DB::select('SELECT it.id FROM da_items it WHERE it.done = 1 and it.approvisionnement_id = ?'
                , [$item->approvisionnement_id]);
                if(count($has_done) > 0)
                    $approvisionnement->status = 'done';
                else
                    $approvisionnement->status = 'draft';
            }
            $approvisionnement->save();
            return response(["success" => "Approvisionnement bien envoyée", "id" => $approvisionnement->id]);
        }
        return response(["error" => "EACCES"]);
    }
}
