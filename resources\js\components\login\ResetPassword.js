import React, { useState } from 'react';
import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import useToken from '../util/useToken';

export default function ResetPassword({auth}) {
    const [currentPassword, setCurrentPassword] = useState("")
    const [newPassword, setNewPassword] = useState("")
    const [confirmationPassword, setConfirmationPassword] = useState("")
    const [error, setError] = useState()
    
    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        axios.post('/api/change_password', {
            current_password: currentPassword,
            new_password: newPassword,
            new_password_confirmation: newPassword,
        }, useToken())
        .then((res) => {
            if(res.data.error){
                setError(res.data.error)
            }
            else {
                localStorage.removeItem("token")
                window.location.reload()
            }
        })
        .catch(err => console.error(err))
    }

    return (
        <div id="content">
            <form onSubmit={handleSubmit}>
                <div className="title-container">
                    <h2>Changer de mot de passe</h2>
                </div>
                <InputText 
                    value={currentPassword} 
                    onChange={setCurrentPassword} 
                    label="Mot de passe actuel" 
                    type="password" 
                    required/>
                <InputText
                    value={newPassword} 
                    onChange={setNewPassword}  
                    label="Nouveau mot de passe" 
                    type="password" 
                    required/>
                <InputText 
                    value={confirmationPassword} 
                    onChange={setConfirmationPassword} 
                    label="Confirmation" 
                    type="password" 
                    required/>
                {
                    error &&
                    <div className='container-error'>
                        {error}
                    </div>
                }
                <ButtonSubmit />
            </form>
        </div>
    )
}