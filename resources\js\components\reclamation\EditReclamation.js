import React, { useEffect, useState } from 'react';
import axios from 'axios';

import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import InputText from '../input/InputText';
import InputAgent from '../input/InputAgent';
import { useParams } from 'react-router-dom';
import InputDate from '../input/InputDate';
import InputSelect from '../input/InputSelect';
import InputSite from '../input/InputSite';
import moment from 'moment';

export default function EditReclamation({title, action, auth}) {
    const params = useParams()
    const [employe, setEmploye] = useState(null);
    const [motif, setMotif] = useState("")
    const [pointages, setPointages] = useState([])
    const [notification, setNotification] = useState(null)
    const [datePointage, setDatePointage] = useState(null)
    const [service, setService] = useState({label: "Jour", value: "07:00:00"})
    const [site, setSite] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleSubmitAll = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)

        const data = {
            employe_id: employe ? employe.id : null,
            motif: motif,
            date_pointage: moment(datePointage).format("YYYY-MM-DD") + " " + service.value,
            site_id: site.id,
        }
        axios.post(action + (params.id ? "/" + params.id : ""), data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }

    useEffect(() => {
        if(datePointage && service && service.value){
            if(moment(moment(datePointage).format('YYYY-MM-DD ') + service.value).isAfter(moment().subtract(12, 'hours')))
                setError("La date de pointage doit être passée")
            else
                setError("")
        }
    }, [datePointage, service])

    useEffect(() => {
        console.log(site)
        if(!(datePointage && service?.value && employe?.id && site?.id
            && moment(moment(datePointage).format('YYYY-MM-DD ') + service.value).isBefore(moment().subtract(12, 'hours')))
        ){
            disableSubmit(true)
        }
        else
            disableSubmit(false)
    }, [datePointage, employe, datePointage, service, site])

    useEffect(() => {
        let isMounted = true
        if(params.id){
            axios.get('/api/reclamation/show/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    const reclamation = res.data
                    if(reclamation.employe) setEmploye({
                        id: reclamation.employe_id,
                        nom: reclamation.employe,
                        matricule: matricule(reclamation)
                    })
                    if(reclamation.motif) setMotif(reclamation.motif)
                }
            })
        }
        return () => { isMounted = false };
    }, [])

    return (
        <div id="content">
            {
                notification ? 
                    <Notification next={notification.id ? "/reclamation?id=" + notification.id : "/"} message={notification.success}/>
                :
                <div>
                    <div className="title-container" >
                        <h2>{title}</h2>
                    </div>
                    <InputAgent 
                        label="Agent"
                        required
                        value={employe}
                        onChange={setEmploye}/>
                    <InputText
                        required
                        label="Motif"
                        value={motif}
                        onChange={setMotif}/>
                    {/* {
                        !params.id &&
                        <PointageReclamation
                            auth={auth}
                            pointages={pointages}
                            setPointages={setPointages}/>
                    } */}
                    {employe &&
                        // <InputPointage employeId={employe.id} value={pointage} onChange={setPointage} required/>
                        <>
                            <InputDate
                                required
                                value={datePointage}
                                onChange={setDatePointage}/>
                            <InputSelect
                                required
                                label="Service"
                                selected={service} setSelected={setService}
                                options = {[
                                    {label: "Jour", value: "07:00:00"},
                                    {label: "Nuit", value: "18:00:00"},
                                ]}/>
                            <InputSite
                                required
                                value={site} 
                                onChange={setSite}/>
                        </>
                    }
                    {
                        error &&
                        <div className='container-error'>
                            {error}
                        </div>
                    }
                    <div className="input-container-btn">
                        <button className='primary' disabled={submitDisabled} onClick={handleSubmitAll}>
                            Envoyer
                        </button>
                    </div>
                </div>
            }
        </div>
    )
}