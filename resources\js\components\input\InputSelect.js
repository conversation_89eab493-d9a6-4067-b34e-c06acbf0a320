import {useState,useRef} from 'react'

import useClickOutside from '../util/useClickOutside'

export default function InputSelect({label,selected, setSelected, className, options,required}) {
    const [isActive, setIsActive] = useState(false)
    const selectRef = useRef(null)
    useClickOutside(selectRef, () => setIsActive(false))

    const handleChange = (e) => {
        const value = e.target.value;
        if (!value) {
            typeof options[0] === 'object' ? setSelected({label: null, value: null}) : setSelected(null);
            return;
        }
        const selectedOption = typeof options[0] === 'object'
            ? options.find(option => option.value === value)
            : value;
        setSelected(selectedOption);
    };

    return (
        <div className='input-container'>
            <label>{label} {required && <span className='danger'>*</span>}</label>
            <select style={{height: 44}} value={selected?.value || selected} onChange={handleChange}>
                <option></option>
                {
                    options.map((option, index) => (
                        <option 
                            key={index}
                            value={typeof option === 'object' ? option.value : option}
                            // style={(typeof option === 'object' && option.value === selected?.value) || (option === selected) ? { backgroundColor: "#aaa" } : { backgroundColor: "#fff" }}
                        >
                            {typeof option === 'object' ? option.label : option}
                        </option>
                    ))
                }
            </select>

            {/* <div className='select-container'>
                <div className={'select-input ' + className} ref={selectRef}>
                    <div className={isActive ? "select-placeholder active-select" : "select-placeholder" }
                        onClick={(e) => setIsActive(!isActive)}>
                        <span>{selected ? (selected.label ? selected.label : selected) : ''}</span>
                        <span>{isActive ? <IoMdArrowDropup/> : <IoMdArrowDropdown/>}</span>
                    </div>
                    {isActive && 
                        <div className="select-content">
                            {
                                options.map((option, index) => (
                                    <div 
                                        key={index}
                                        onClick={(e) => {
                                            setSelected(option)
                                            setIsActive(false)
                                        }}
                                        className="select-option"
                                    >
                                        {option && (option.label ? option.label : option)}
                                    </div>
                                ))
                            }
                            
                        </div>
                    }
                </div>
            </div> */}
        </div>
    )
}
