
import React, { useState } from 'react';

import axios from 'axios';
import InputCheckBox from '../input/InputCheckBox';
import useToken from '../util/useToken';
import Textarea from '../input/Textarea';
import InputEmploye from '../input/InputEmploye';
import matricule from '../util/matricule';

export default function ResetPasswordModal({id, user, updateData, closeModal}) {
    const [note, setNote] = useState("")
    const [blocked, setBlocked] = useState(false)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    const [employe, setEmploye] = useState(user.employe ? {
        id: user.employe.id,
        nom: user.employe.nom,
        matricule: matricule(user.employe)
    } : null)
    const [showEmploye, toggleEmploye] = useState(true)

    const handleOk = () => {
        disableSubmit(true)
        setError("")
        let formData = new FormData() 
        formData.append("note", note)
        if(blocked)
            formData.append("blocked", 1)
        if(employe)
            formData.append("employe_id", employe.id)
        axios.post("/api/user/reset_password/" + id, formData, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success){
                closeModal()
                if(updateData) updateData()
            }
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    return <div className='modal'>
        <div>
            <h3>
                Réinitialiser
            </h3>
            <div className='input-container'>
                <Textarea 
                    value={note} 
                    label="Commentaire" 
                    onChange={(value) => setNote(value)} 
                    required/>
            </div>
            {!user.blocked &&
                <div className='field-container'>
                    <InputCheckBox 
                        label="Désactivé le compte"
                        checked={blocked} 
                        onChange={(value) => { setBlocked(value); toggleEmploye(!value) }}/>
                </div>
            }
            {showEmploye &&
                <InputEmploye
                    label="Employé"
                    value={employe}
                    onChange={setEmploye}
                    required
                />
            }
            {
                error &&
                <div className='container-error'>
                    {error}
                </div>
            }
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Envoyer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
}