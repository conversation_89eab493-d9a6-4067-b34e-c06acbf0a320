<?php

namespace App\Http\Controllers;

use App\Models\Status;
use Illuminate\Http\Request;

class StatusController extends Controller
{
    public function index(Request $request){
        if($request->has_employe)
            return response(Status::orderBy("ordre")->get());
        else
            return response(Status::whereNotIn("name", ["convocation", "confirmation"])->orderBy("ordre")->get());
    }
    public function show(Request $request, $name){
        return response()->json(Status::find($name));
    }
}