import React, { useState } from 'react';

import { Link } from 'react-router-dom';
import NoteModal from '../input/NoteModal';
import InputAgent from '../input/InputAgent';
import TransferModal from './TransferModal';
import DoneMultipleModal from '../modal/DoneMultipleModal';
import ValidateEquipementModal from './ValidateEquipementModal';
import RefuseEquipementModal from './RefuseEquipementModal';

export default function ActionEquipement({auth, equipement, updateData}) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [showValidateModal, toggleValidateModal] = useState(false)
    const [showRefuseModal, toggleRefuseModal] = useState(false)
    const [currentAction, setAction] = useState(null)
    const [showEmployeModal, toggleEmployeModal] = useState(false)
    const [showTransfer, toggleTransfer] = useState(false)
    const [showDoneMultipleModal, toggleDoneMultipleModal] = useState(false)

    const handleReplyValidation = (id) => {
        setAction({
            header: "Réponse à la demande",
            request: "/api/equipement/reply_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    const handleCancelValidation = (id) => {
        setAction({
            header: "Annuler la demande de validation",
            request: "/api/equipement/cancel_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    const handleRequestValidation = (id) => {
        setAction({
            header: "Demande de validation",
            request: "/api/equipement/request_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleCancelEquipement = (id) => {
        setAction({
            header: "Annuler l'équipement",
            request: "/api/equipement/cancel_equipement/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    const handleNoteEquipement = (id) => {
        setAction({
            header: "Accuser la réception",
            request: "/api/equipement/note/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleDoneAllRequest = (id, type) => {
        setAction({
            header: "Terminer toutes les demandes",
            request: `/api/article_equipement/done_all/${id}?type=${type}`,
            required: true

        })
        toggleDoneMultipleModal(true)
    }

    return <div>
        {
            showNoteModal &&
            <NoteModal
                action={currentAction}
                updateData={() => updateData(true)}
                closeModal={() => toggleNoteModal(false)}
            />
        }
        {
            showEmployeModal &&
            <InputAgent
                hideInput
                useAction={`/api/equipement/set_employe/${equipement.id}?type=${equipement.type}`}
                updateData={() => updateData()}
                closeModal={() => toggleEmployeModal(false)}
                required
            />
        }
        {
            showTransfer &&
            <TransferModal
                equipement={equipement}
                closeModal={() => toggleTransfer(false)}
                updateData={() => updateData(true)}/>
        }
        {
            showDoneMultipleModal &&
            <DoneMultipleModal
                action={currentAction}
                task={"terminer"}
                name={"equipement"}
                data={equipement}
                closeModal={() => toggleDoneMultipleModal(false)}
                updateData={() => updateData(true)}
            />
        }
        {
            showValidateModal &&
            <ValidateEquipementModal
                closeModal={() => toggleValidateModal(false)}
                updateData={() => updateData(true)}
                equipement={equipement}
            />
        }
        {
            showRefuseModal &&
            <RefuseEquipementModal
                closeModal={() => toggleRefuseModal(false)}
                updateData={() => updateData(true)}
                equipement={equipement}
            />
        }
        <div className='action-container'>
            {
                ["validateur"].includes(auth.role) && equipement.isValidated === 0 && equipement.articles.find((article) => article.designation === "Botte de pluie") &&
                <span onClick={() => toggleValidateModal(true)}>Valider</span>
            }
            {
                ["validateur"].includes(auth.role) && equipement.isValidated === 0 && equipement.articles.find((article) => article.designation === "Botte de pluie") &&
                <span onClick={() => toggleRefuseModal(true)}>Refuser</span>
            }
            {
                equipement.type != "tenue" ? (
                    (["demande", "traite"].includes(equipement.status) &&
                    ["tenue"].includes(auth.role)) &&
                    equipement.articles.some((article) => article.done !== 1) &&
                    <span onClick={() => handleDoneAllRequest(equipement.id, equipement.type)}>Terminer</span>
                ) : (
                    (["demande", "traite"].includes(equipement.status)) &&
                    equipement.employe_id !== null && ["tenue"].includes(auth.role) &&
                    equipement.articles.some((article) => article.done !== 1) &&
                    <span onClick={() => handleDoneAllRequest(equipement.id, equipement.type)}>Terminer</span>
                )
            }
            {
                (equipement.recruiting != 0 && auth.id == equipement.user_id && !['guerite','tonfa', 'torche', 'chargeur','impermeable'].includes(equipement.type)) &&
                <span onClick={() => toggleEmployeModal(true)}>
                    Mentionner l'employe
                </span>
            }
            {
                (['demande'].includes(equipement.status) && ["achat", "operation"].includes(auth.role)) &&
                <span onClick={() => handleNoteEquipement(equipement.id)}>
                    Accuser réception
                </span>
            }
            {
                (['demande', 'traite'].includes(equipement.status) && ["achat", "operation"].includes(auth.role)) &&
                <span onClick={() => handleRequestValidation(equipement.id)}>Validation</span>
            }
            {
                (["validation"].includes(equipement.status) && ["validateur"].includes(auth.role)) &&
                <span onClick={() => handleReplyValidation(equipement.id)}>Répondre</span>
            }
            {
                (["validation"].includes(equipement.status) && ["achat", "operation"].includes(auth.role)) &&
                <span onClick={() => handleCancelValidation(equipement.id)}>Annuler la demande de validation</span>
            }
            {/* {
                (
                    (['demande', 'traite'].includes(equipement.status) && ["achat", "operation"].includes(auth.role)) ||
                    (['demande'].includes(equipement.status) && auth.id == equipement.user_id)
                ) &&
                <span onClick={() => handleCancelEquipement(equipement.id)}>Annuler la demande</span>
            } */}
            {
                (["draft"].includes(equipement.status) && auth.id == equipement.user_id) &&
                <span>
                    <Link to={"/equipement/send_back/" + equipement.id}>
                        {["rh", "resp_rh"].includes(auth.role) ? "Refaire le traitement" : "Renvoyer"}
                    </Link>
                </span>
            }
            {["demande", "traite"].includes(equipement.status) && (["achat", "operation"].includes(auth.role) || auth.id == equipement.user_id) &&
                <span onClick={()=>toggleTransfer(true)}>Transférer</span>
            }
        </div>
    </div>
}
