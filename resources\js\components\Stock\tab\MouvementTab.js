import React, { useEffect, useState } from 'react';
import useToken from '../../util/useToken';
import moment from 'moment';
import LoadingPage from '../../loading/LoadingPage';
import matricule from '../../util/matricule';

export default function MouvementTab({auth, name, value, updateData}) {
    const [isLoading, toggleLoading] = useState(true)
    const [mouvements, setMouvements] = useState([])

    useEffect(() => {
        let isMounted = true;
        axios.get('/api/article/mouvement_tab/' + value, useToken())
        .then((res) => {
            if(isMounted) {
                setMouvements(res.data.tenLastMouvement)
                toggleLoading(false)
            }
        })
        .catch((error) => {
            console.error(error)
        })
        return () => { isMounted = false }
    }, [])

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <div>
                    {
                        mouvements.map((mvt) => (
                            <div key={mvt.id} className='article-container'>
                                <span>{matricule(mvt)} {mvt.nom}</span>
                                <span>{moment(mvt.date_mouvement).format("DD/MM/YY")}</span>
                            </div>
                        ))
                    }
                </div>
        }
    </>
}