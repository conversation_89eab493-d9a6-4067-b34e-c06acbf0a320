import React, { useEffect, useState } from 'react'
import ButtonSubmit from '../input/ButtonSubmit'
import useToken from '../util/useToken';
import InputEmploye from '../input/InputEmploye';
import Textarea from '../input/Textarea';
import InputText from '../input/InputText';
import Notification from "../notification/Notification";
import { useParams } from 'react-router-dom';
import axios from 'axios';
import matricule from '../util/matricule';

export default function EditDeduction({ auth, title, action }) {
    const [disabledSubmit, setDisabledSubmit] = useState(false);
    const [notification, setNotification] = useState(null);
    const [error, setError] = useState("");
    const [employe, setEmploye] = useState(null);
    const [motif, setMotif] = useState(null);
    const [montant, setMontant] = useState(0);
    const params = useParams();

    const handleSubmit = (e) => {
        e.preventDefault();
        setError("")
        setDisabledSubmit(true);
        // let formData = new FormData();
        // formData.append("motif", motif);
        // formData.append("montant", montant);
        const data = {
            employe_id: employe ? employe.id : '',
            motif: motif,
            montant: parseInt(montant)
        }

        axios.post(action + (params.id ? params.id : ""), data, useToken())
            .then((res) => {
                    setDisabledSubmit(false);
                    if (res.data.success)
                        setNotification(res.data);
                    else if (res.data.error == "EACCES") {
                        console.error(res.data.error);
                        setError("Une erreur est survenue.");
                    }
                    else if (res.data.error) 
                        setError(res.data.error);
            })
            .finally(() => {
                setDisabledSubmit(false);
            });
    }

    const getDeduction = () => {
        axios.get('/api/deduction?offset=0&id=' + (params.id), useToken()).then((res) => {
            let ded = res.data.deductions[0];
            let emp = {
                id: ded.employe_id,
                nom: ded.employe,
                matricule:matricule(ded)
            };
            setEmploye(emp)
            setMontant(ded.montant);
            setMotif(ded.motif);
        })
    }

    useEffect(() => {
        params.id && getDeduction()
    }, []);

    return (
        <div id="content">
            <div>
                { 
                    notification ? 
                        <Notification message={notification.success}
                            next={notification.id ? "/deduction?id=" + notification.id : "/deduction"} 
                        />
                    : 
                        <form onSubmit={handleSubmit}>
                            <div className="title-container">
                                <h2>{title}</h2>
                            </div>
                            <InputEmploye label="Employé" onChange={setEmploye} value={employe} required />
                            <InputText type="number" onChange={setMontant} value={montant} required label="Montant" />
                            <Textarea label="Motif" value={motif} onChange={setMotif} required/>
                            <div>
                                {
                                    error && <div className="container-error">{error}</div>
                                }
                            </div>
                            <ButtonSubmit label="Enregistrer" disabled={disabledSubmit} />
                        </form>
                }
            </div>
        </div>
    )
}
