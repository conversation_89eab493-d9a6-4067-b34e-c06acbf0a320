import React, { useEffect, useState } from 'react';
import { useParams} from 'react-router-dom'
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import Textarea from '../input/Textarea';
import InputSite from '../input/InputSite';
import InputCheckBox from '../input/InputCheckBox';
import LoadingScreen from '../loading/LoadingScreen';
import InputAgent from '../input/InputAgent';
import InputAgentMultiple from '../input/InputAgentMultiple';

export default function EditEquipement({auth, action}) {
    const params = useParams()
    const [me, setMe] = useState(false)
    const [isLoading, setLoading] = useState(false)
    const [currentType, setCurrentType] = useState(null)
    const [forAgent, setForAgent] = useState(null)
    const [recruiting, setRecruiting] = useState(false)

    const [employe, setEmploye] = useState(null)
    const [employes, setEmployes] = useState([])
    const [nbEmploye, setNbEmploye] = useState("")
    const [site, setSite] = useState(null)
    const [demande, setDemande] = useState("")
    const [detail, setDetail] = useState("")
    const [motif, setMotif] = useState("")
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    const [articles, setArticles] = useState([])
    const [qte, setQte] = useState("")

    const handleChangeCheckbox = (e) => {
        setArticles(
            articles.map(a => {
                if(a.name == e.target.name)
                    a.checked = e.target.checked
                return a
            })
        )
    }

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        let data = {
            type: currentType.name,
            motif: motif,
            demande: demande,
            detail: detail,
            recruiting: recruiting,
            for_agent: forAgent,
            nb_employe: nbEmploye,
        }
        if(me) {
            data.me = 1
            data.employe_id = auth.employe_id
        }
        if(employe)
            data.employe_id = employe.id
        if(employes.length > 0)
            data.employe_ids = employes.map(ag => ag.id)
        if(site)
            data.site_id = site.id
        if(articles.length == 1)
            data[articles[0].name] = true
        if(qte)
            data.qte = qte
        else if(articles.length > 1){
            articles.map(a => {
                if(a.checked) data[a.name] = true
            })
        }
        axios.post(action + (params.id ? "/" + params.id : ""), data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    useEffect(() => {
        if(!me && currentType){
            if(currentType.for_agent === 1) setForAgent(true)
            else if(currentType.for_agent === 0) setForAgent(false)
        } else 
            setForAgent(false)
    }, [currentType, me])

    useEffect(() => {
        let isMounted = true
        setLoading(true)
        if(params.type){
            axios.get('/api/type_equipement/show/' + params.type, useToken())
            .then((res) => {
                if(isMounted){
                    setCurrentType(res.data.type)
                    const articles = res.data.articles.map(a => {
                        a.checked = false
                        return a
                    })
                    setArticles(articles)
                    setLoading(false)
                }
            })
            .catch(e => {
                setLoading(false)
            })
        }
        return () => { isMounted = false };
    }, [])

    useEffect(() => {
        let isMounted = true
        if(params.id){
            setLoading(true)
            axios.get('/api/equipement/show/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    setLoading(false)
                    const articles = res.data.articles
                    setArticles(articles)
                    const equipement = res.data.equipement
                    setCurrentType({
                        name: equipement.type,
                        designation: equipement.type_demande,
                        for_agent: equipement.for_agent,
                    })
                    if(equipement.motif) setMotif(equipement.motif)
                    if(equipement.employe) setEmploye({
                        id: equipement.employe_id,
                        nom: equipement.employe,
                        matricule: matricule(equipement)
                    })
                    if(equipement.site) setSite({
                        id: equipement.site_id,
                        nom: equipement.site
                    })
                    if(equipement.demande)
                        setDemande(equipement.demande)
                    if(equipement.detail)
                        setDetail(equipement.detail)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        return () => { isMounted = false };
    }, [])

    return (
        ((params.id || params.type) && isLoading) ?
            <LoadingScreen/>
        :
            <div id="content">
                {
                    notification ?
                        <Notification
                            next={notification.id ? "/equipement?id=" + notification.id : "/equipement"}
                            message={notification.success}/>
                    : (
                        currentType &&
                        <div>
                            <h2>{currentType.designation}</h2>
                            <form onSubmit={handleSubmit}>
                                {
                                    !currentType.name == "impermeable" &&
                                    <div className='card-container'>
                                        <InputCheckBox label="Pour moi même" checked={me} onChange={setMe}/>
                                    </div>
                                }
                                {
                                    (!me && forAgent !== null && (["resp_sup","resp_op"].includes(auth.role))) &&
                                    <div className='field-container'>
                                        <InputCheckBox
                                            label="Employe en cours de recrutement"
                                            checked={recruiting}
                                            onChange={setRecruiting}/>
                                    </div>
                                }
                                {
                                    recruiting && <>
                                        <div className='warning-container'>
                                            <h3 className='danger'>Ne pas coché si l'employe est déjà déterminé.</h3>
                                            Le non respect de cette consigne entraine une sanction.
                                        </div>
                                        <InputText
                                            value={nbEmploye}
                                            onChange={setNbEmploye}
                                            label="Nombre d'employe"
                                            type="number"
                                            required/>
                                    </>
                                }
                                {
                                    (!me && forAgent !== null) &&
                                    (
                                        (!recruiting && forAgent) ?
                                            (
                                                params.id ?
                                                    <InputAgent required value={employe} onChange={setEmploye}/>
                                                :
                                                    <InputAgentMultiple required employes={employes} setEmployes={setEmployes}/>
                                            )
                                        :
                                            <InputSite required value={site} withoutDelete  onChange={setSite}/>
                                    )
                                }
                                {
                                    articles.length > 1 &&
                                    <div className='input-container'>
                                        <label>Demande <span className='danger'>*</span></label>
                                        <div className='checkbox-form-container'>
                                            {
                                                articles.map(art => (
                                                    <div key={art.name} className='checkbox-card'>
                                                        <label className="checkbox-container">
                                                            {art.designation}
                                                            <input type="checkbox" name={art.name} checked={art.checked} onChange={handleChangeCheckbox}/>
                                                            <span className="checkmark"></span>
                                                        </label>
                                                    </div>
                                                ))
                                            }
                                        </div>
                                    </div>
                                }
                                {
                                    currentType.name == "other" &&
                                    <InputText
                                        placeholder="2 assiettes, 2 Cuillières, 1 Chaise"
                                        value={demande}
                                        onChange={setDemande}
                                        label="Demande"
                                        required/>
                                }
                                {
                                    ['fourniture_bureau', 'fourniture_entretien'].includes(currentType.name) &&
                                    <InputText
                                        value={detail}
                                        placeholder="1 cahier, 2 Stylo, 1 Regle"
                                        onChange={setDetail}
                                        required
                                        label="Détail"/>
                                }
                                {
                                    currentType.name == "impermeable" &&
                                    <InputText
                                        value={qte}
                                        onChange={setQte}
                                        type="number"
                                        required
                                        label="Quantité"
                                    />
                                }
                                <Textarea
                                    required
                                    label="Motif"
                                    value={motif}
                                    onChange={setMotif}/>

                                {
                                    error &&
                                    <div className='container-error'>
                                        {error}
                                    </div>
                                }
                                <ButtonSubmit disabled={submitDisabled}/>
                            </form>
                        </div>
                    )
                }
            </div>
    )
}
