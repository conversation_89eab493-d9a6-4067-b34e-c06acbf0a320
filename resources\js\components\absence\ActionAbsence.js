import React, { useState, lazy } from 'react';
import { Link } from 'react-router-dom';
import NoteModal from '../input/NoteModal';
const EditPdf = lazy(() => import('./EditPdf'))
import DoneCongeModal  from "./DoneCongeModal";
import EditSuperviseurModal from './EditSuperviseurModal';

export default function ActionAbsence({ auth, absence, updateData, setCurrentId }) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [currentAction, setAction] = useState(null)
    const [showPrintModal, setShowPrintModal] = useState(false)
    const [toDone, setToDone] = useState(false);
    const [showDoneModal, setShowDoneModal] = useState(false);
    const [editSup, toggleEditSup] = useState(false);

    const handleNoteAbsence = (id) => {
        setAction({
            header: "Note",
            request: "/api/absence/note/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleCancelValidation = (id) => {
        setAction({
            header: "Annuler la demande de validation",
            request: "/api/absence/cancel_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleRequestValidation = (id) => {
        setAction({
            header: "Demande de validation",
            request: "/api/absence/request_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleReplyValidation = (id) => {
        setAction({
            header: "Réponse à la demande",
            request: "/api/absence/reply_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleCancelAbsence = (id) => {
        setAction({
            header: "Annuler le absence",
            request: "/api/absence/cancel_absence/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    
    const handleDone = (id) => {
        setAction({
            header: "Valider le absence",
            request: "/api/absence/save_done/" + id,
            required: true
        })
        if (absence.type_absence != "mis_a_pied") setToDone(true)
        toggleNoteModal(true)
    }

    // useEffect(() => {
    //     let isMounted = true
    //     if (isMounted && absence.superviseur_id && absence.type_absence == "mis_a_pied") {
    //         axios.get("/api/absence/get_responsable/" + absence.superviseur_id, useToken())
    //         .then((res) => {
    //             if ((res.data.respUserIds).includes(auth.id))
    //                 setResponsable(true);
    //         })
    //     }
    //     return () => { isMounted = false };
    // }, [])

    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction} 
                updateData={() => updateData(true)} 
                closeModal={() => toggleNoteModal(false)}
                toDone={toDone}
                setToDone={setToDone}
            />  
        }
        {
            showDoneModal &&
            <DoneCongeModal absence={absence} updateData={updateData} closeModal={()=>setShowDoneModal(false)}/>
        }
        {
            showPrintModal &&
            <EditPdf 
                header={absence.type_absence == "conge" ? "Impréssion congé" : absence.type_absence =="permission"? "Impréssion permission":"Impression mise à pied"}
                absence={absence}
                setShowPrintModal={setShowPrintModal}/>
        }
        {
            editSup &&
            <EditSuperviseurModal   
                absence={absence}
                updateData={updateData}
                setCurrentId={setCurrentId}
                auth={auth}
                closeModal={() => toggleEditSup(false)}
            />
        }
        <div className='action-container'>
            {
                (["demande"].includes(absence.status) && ["rh", "resp_rh"].includes(auth.role)) && (absence.type_absence != "mis_a_pied") &&
                <span onClick={() => handleNoteAbsence(absence.id)}>Accuser réception</span>
            }
            {
                (['demande', 'traite'].includes(absence.status) && ["rh", "resp_rh"].includes(auth.role)) && (absence.type_absence !="mis_a_pied") &&
                <span onClick={() => handleRequestValidation(absence.id)}>Validation</span>
            }
            {
                (["traite", "done"].includes(absence.status) && ["rh", "resp_rh"].includes(auth.role)) && 
                    absence.type_absence != "mis_a_pied" && 
                    <span onClick={() => setShowPrintModal(true)}>Imprimer</span>
            }
            {
                (absence.status != "draft") && ["rh", "resp_rh"].includes(auth.role) && 
                    absence.type_absence == "mis_a_pied" && 
                    <span onClick={() => setShowPrintModal(true)}>Imprimer</span>
            }
            {
                (["validation"].includes(absence.status) && ["validateur"].includes(auth.role)) &&
                <span onClick={() => handleReplyValidation(absence.id)}>Répondre</span>
            }
            {
                (["validation"].includes(absence.status) && ["rh", "resp_rh"].includes(auth.role)) &&
                <span onClick={() => handleCancelValidation(absence.id)}>Annuler la demande de validation</span>
            }
            {
                (["demande", "traite"].includes(absence.status) && ["rh", "resp_rh"].includes(auth.role)) && 
                (absence.type_absence != "mis_a_pied") &&
                <span onClick={() => setShowDoneModal(true)}>Terminer</span>
            }
            {
                ((["demande"].includes(absence.status) && auth.id == absence.user_id)
                 ||(["demande", "traite"].includes(absence.status) && ["rh", "resp_rh"].includes(auth.role))) && 
                <span onClick={() => handleCancelAbsence(absence.id)}>Annuler</span>
            }
            {
                (["rh", "resp_rh"].includes(auth.role)) &&
                (absence.type_absence == "mis_a_pied") &&
                (["demande"].includes(absence.status)) &&
                <span> <Link to={"/absence/edit/" + absence.id}>Editer</Link> </span>
            }
            {
                (["rh", "resp_rh"].includes(auth.role) && 
                (["demande"].includes(absence.status)) && 
                (absence.type_absence == "mis_a_pied")) &&
                    <span> <Link to={"/absence/apply/" + absence.id}>Appliquer</Link> </span>
            }
            {
                (absence.superviseur_id == auth.id) &&
                (absence.type_absence == "mis_a_pied") &&
                (absence.status == 'demande') &&
                <span>
                    <span onClick={() => toggleEditSup(true)}>Editer</span>
                </span>
            }
            {
                (["draft"].includes(absence.status) && auth.id == absence.user_id) && 
                <span>
                    <Link to={"/absence/send_back/" + absence.id}>Renvoyer la demande</Link>
                </span>
            }
        </div>
    </div>
}