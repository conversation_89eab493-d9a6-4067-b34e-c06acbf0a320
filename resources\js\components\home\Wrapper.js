import React, { Children, useEffect, useState } from "react";

export default function Wrapper({ children }) {
    const count = Children.count(children);
    const countArray = Children.toArray(children).length;

    const getArrayColumn = () => {
		const {innerWidth} = window;
		const widthView = innerWidth > 1200 ? (innerWidth - 280) : innerWidth;
		const nbCol = widthView > 1500 ? 4 : widthView > 1100 ? 3 : widthView > 725 ? 2 : 1
		const arrayCol = []
		for(let i=0; i<nbCol; i++){
			const childrenByCol = []
			let currentIndex = i
			Children.toArray(children).map((el, index) => {
				if(index == currentIndex){
					childrenByCol.push(el)
					currentIndex = currentIndex + nbCol
				}
			})
			arrayCol.push(childrenByCol)
		}
		return arrayCol
    } 
    const [arrayColumn, setArrayColumn] = useState(getArrayColumn())
	  
    useEffect(() => {
        function handleWindowResize() {
            setArrayColumn(getArrayColumn());
        }
        window.addEventListener('resize', handleWindowResize);
        return () => {
            window.removeEventListener('resize', handleWindowResize);
        }
    }, [arrayColumn])

    return <div id="wrapper">
		{
			arrayColumn.map((children, index) => <div key={index}>
				{children}
			</div>)
		}
	</div>;
}
