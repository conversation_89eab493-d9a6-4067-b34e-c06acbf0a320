const ExcelJS = require('exceljs');
const moment = require('moment');
const axios = require('axios');
const mysql = require('mysql')
const {sendMail, tokenAdmin, tokenTest} = require("../auth")

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

moment.locale('fr')

const isTask = (process.argv[2] == "task")
const destination_vg = ["<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", ]

const sqlSelectLastAnomalieExport = "SELECT value FROM params p WHERE p.key = 'last_anomalie_export'"
function sqlUpdateLastAnomalieExport (dateString) {
    return "UPDATE params p  SET p.value = '" + dateString + "' " + 
    "WHERE p.key = 'last_anomalie_export'"
}
const exportDailyAnomalie = async (workbook, date, anomalies) => {
    workbook.creator = "ADMIN";
    const border = {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
    };
    const fontStyle = {
        size: 12,
        color: { argb: 'FFFFFFFF' },
        bold: true
    };
    
    console.log('exporting anomalies for: ', date);

    const groupByRespSup = () => {
        const groupedData = anomalies.reduce((result, item) => {
            const { resp_sup_id, superviseur_id } = item;
            if (!result[resp_sup_id]) {
                result[resp_sup_id] = {};
            }
            if (!result[resp_sup_id][superviseur_id]) {
                result[resp_sup_id][superviseur_id] = [];
            }
            result[resp_sup_id][superviseur_id].push(item);
            return result;
        }, {});

        return Object.entries(groupedData).map(([respSupId, superviseurs]) => ({
            respSupId,
            respSupNom: anomalies.find(a => a.resp_sup_id === Number(respSupId))?.resp_sup || 'Unknown',
            superviseurs: Object.entries(superviseurs).map(([superviseurId, items]) => ({
                superviseurId,
                items,
            })),
        }));
    };

    const reformatedData = groupByRespSup();

    const headers = ['', 'FLOTTE', 'JOUR', 'NUIT', 'MANQUE', 'SURPLUS', 'INCOHÉRENCE'];
    reformatedData.forEach(dataGroup => {
        const worksheet = workbook.addWorksheet(`${dataGroup.respSupNom}`);
        dataGroup.superviseurs.forEach((supGroup, index) => {
            if (index !== 0) {
                worksheet.addRow([]);
            }
            const superviseurRow = worksheet.addRow([
                (supGroup.items[0].superviseur || '').toUpperCase(),
                supGroup.items[0].superviseur_flotte || '',
                supGroup.items[0].superviseur_email || ''
            ]);
            worksheet.mergeCells(`C${superviseurRow.number}:G${superviseurRow.number}`);
            superviseurRow.eachCell((cell) => {
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFFF0000' },
                };
                cell.font = fontStyle;
            });

            const beforeHeader = worksheet.addRow([null, null, 'CONTRAT', '', 'DEPLOYER']);
            worksheet.mergeCells(`C${beforeHeader.number}:D${beforeHeader.number}`);
            worksheet.mergeCells(`E${beforeHeader.number}:F${beforeHeader.number}`);
            beforeHeader.eachCell({ includeEmpty: true }, (cell, colNumber) => {
                if (colNumber > 2) {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFD3D3D3' },
                    };
                    cell.border = border;
                }
            });

            const addHeader = worksheet.addRow(headers);
            addHeader.eachCell({ includeEmpty: true }, (cell) => {
                cell.border = border;
            });

            let numberNight = 0;
            let numberDay = 0;
            let numberIncoherence = 0;
            let numberManque = 0;
            let numberSurplus = 0;
            supGroup.items.forEach(data => {
                numberNight += data.nb_agent_night ?? 0;
                numberDay += data.nb_agent_day ?? 0;
                numberIncoherence += data.incoherence ?? 0;
                numberManque += data.manque ?? 0;
                numberSurplus += data.surplus ?? 0;
                const row = worksheet.addRow([
                    data.site || '',
                    data.superviseur_flotte || '',
                    data.nb_agent_day || '',
                    data.nb_agent_night || '',
                    data.manque || '',
                    data.surplus || '',
                    data.incoherence || ''
                ]);
                row.eachCell({ includeEmpty: true }, (cell) => {
                    cell.border = border;
                });
            });

            worksheet.addRow([
                '',
                '',
                numberDay,
                numberNight,
                numberManque,
                numberSurplus,
                numberIncoherence
            ]);
            ['A', 'B', 'C', 'D', 'E', 'F', 'G'].forEach(col => {
                worksheet.getColumn(col).alignment = { horizontal: col === 'A' ? 'left' : 'center' };
            });
        });
    });

    workbook.eachSheet((worksheet) => {
        worksheet.getColumn('A').width = 30;
        worksheet.getColumn('B').width = 15;
        worksheet.getColumn('C').width = 10;
        worksheet.getColumn('D').width = 10;
        worksheet.getColumn('E').width = 15;
        worksheet.getColumn('F').width = 15;
        worksheet.getColumn('G').width = 20;
    });
    // const buffer = await workbook.xlsx.writeBuffer();
    // const fs = require('fs');
    // fs.writeFileSync(`ANOMALIE_ZOZ_${moment().format('MMM_YYYY').toUpperCase()}.xlsx`, buffer);
};

const queryAsync = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        pool.query(sql, params, (err, result) => {
            if (err) reject(err);
            else resolve(result);
        });
    });
};

const main = async(date) => {
    let all_time_spent = 0;
    const startTime = performance.now();
    const startTimeExecution = performance.now();
    try {
        const result = await queryAsync(sqlSelectLastAnomalieExport, []);
        if (result && result[0].value == date) {
            console.log("export list anomalies already done!");
            process.exit(1);
        } else {
            const res = await axios.get("http://" + (isTask ? "app.dirickx.mg:8001" : "localhost:8000")  + `/api/anomalie?created_at=${moment(date).format('YYYY-MM-DD')}`, { 
                headers: {
                    'Authorization': 'Bearer ' + (isTask ? tokenAdmin : tokenTest)
                }
            });

            const endTime = performance.now();
            const executionTime = endTime - startTime;
            all_time_spent += executionTime;
            console.log(`Time passes: ${((endTime - startTimeExecution)/60000).toFixed(2)} minutes`);

            if(res.data.anomalies){
                const anomalies = res.data.anomalies || [];
                const workbook = new ExcelJS.Workbook();
                await exportDailyAnomalie(workbook, new Date(date), anomalies);
                const savBuffer = await workbook.xlsx.writeBuffer();
                const header = "ANOMALIE JOURNALIERE " + moment(date).format("DD MMMM YYYY");
                sendMail(pool, 
                    isTask ? destination_vg : destination_test,
                    header, 
                    "Veuillez trouver ci-joint le rapport des Anomalies du " + moment(date).format("DD MMMM YYYY") + "<br/>",
                    [
                        {
                            filename: header + ".xlsx",
                            content: savBuffer
                        },
                    ],
                    (response) => {
                        if(response){
                            pool.query(sqlUpdateLastAnomalieExport(moment(date).format("YYYY-MM-DD")), [], (e, r) =>{
                                if(e)
                                    console.error(e)
                                else
                                    console.log("update last diag export: " + r)
                                process.exit(1)
                            })
                        }
                        else
                            process.exit(1)
                    },
                    isTask
                );
            }
        }

    } catch (err) {
        console.error('Error:', err);
    }
};

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2])){
    console.log("send test...")
    main(process.argv[2])
}
else if(isTask){
    let date = moment().subtract(1, 'days').format('YYYY-MM-DD')
    main(date)
}
