import React from 'react';
import { Page, Document, StyleSheet, View } from '@react-pdf/renderer';
import HeaderDocument from './HeaderDocument'
import ItemsTable from './ItemsTable'
import FooterDocument from './FooterDocument'

const styles = StyleSheet.create({
    page: {
        fontFamily: 'Helvetica',
        fontSize: 7,
        paddingTop: 30,
        paddingLeft:5,
        paddingRight:60,
        lineHeight: 1,
        flexDirection: 'column',
        flexDirection: "row",
        justifyContent: "space-between",
    }, 
    logo: {
        width: 74,
        height: 66,
        marginLeft: 'auto',
        marginRight: 'auto',
        bottom:2,
        
  },
  pageSider: {
    marginLeft: 33,
  }    
});


const DocumentPDF = ({ paie, siret,setSiret,paies }) => {
    const Content = ({ styles, p }) => (
        <View style={styles ? styles : null}>
            <HeaderDocument item={p} />
            <ItemsTable paie={p} siret={siret} setSiret={setSiret} />
            <FooterDocument />
        </View>
    );


    const Pages = () => (
        <>
            {paies.map((p) =>
                <Page size="A4" style={styles.page} orientation="landscape" key={p.id}>
                    <Content p={p} />
                    <Content styles={styles.pageSider} p={p} />
                </Page>
            )}
        </>)
        return (
            <Document>
                {(paies && paies.length > 0) ? <Pages />
                    : paie &&
                    <Page size="A4" style={styles.page} orientation="landscape">
                        <Content p={paie}/>
                        <Content styles={ styles.pageSider } p={paie}/>
                    
                    </Page>
                }
            </Document>
        );
}
  
export default DocumentPDF