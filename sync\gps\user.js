const moment = require('moment')
const mysql = require('mysql')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth")

const pool_admin = mysql.createPool(auth.db_config_admin)
const pool_gps = mysql.createPool(auth.db_config_gps)

const pathname = 'logs/sync/user/' +  moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
	console.error(err)
})

const sqlSelectUser = "SELECT id, name, email from users " +
    "where synchronized_at is null or (gps_updated_at is not null and synchronized_at <= gps_updated_at) " +
    "limit 50"
const sqlInsertOrUpdateUser = "INSERT INTO drx_users(id, name, email) " +
    "VALUES (?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE name=?, email=?"
const sqlUpdateUser = "UPDATE users SET synchronized_at = now() WHERE id = ?"

function syncUserById(users, index){
    if(index < users.length){
        const user = users[index]
        pool_gps.query(sqlInsertOrUpdateUser, [user.id, user.name, user.email
            , user.name,  user.email], async (err, res) => {
            if(err){
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if(err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                console.log("sync user: " + user.id)
                pool_admin.query(sqlUpdateUser, [user.id], async (err, res) => {
                    if(err){
                        fs.appendFile(pathname, err.toString(), (err) => {
                            if(err) console.error(err);
                        })
                        console.error(err)
                    }
                })
                setTimeout(() => {
                    syncUserById(users, index+1)
                }, 200)
            }
        })
    }
    else
        waitBeforeUpdate()
}

function updateData(){
    pool_admin.query(sqlSelectUser, [], async (err, users) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if(err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            if(users.length > 0){
                console.log("user to sync: " + users.length)
                syncUserById(users, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
        }
    })
}

let count = 1
function waitBeforeUpdate(){
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if(count > 3) count = 1
    else count ++
}

updateData()