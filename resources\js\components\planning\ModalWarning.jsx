import React, { useEffect, useRef } from 'react'

export default function ModalWarning({ message, duration, title, closeModal }) {
    const timeoutRef = useRef(null);
    const isHovered = useRef(false);

    useEffect(() => {
        if (!duration) return;
        const setCloseTimeout = () => {
            if (isHovered.current) return;
            timeoutRef.current = setTimeout(() => {
                closeModal(false);
            }, duration * 1000);
        };
        setCloseTimeout();
        return () => {
            clearTimeout(timeoutRef.current);
        };
    }, [duration, closeModal]);

    const handleMouseEnter = () => {
        isHovered.current = true;
        clearTimeout(timeoutRef.current);
    };
    
    const handleMouseLeave = () => {
        isHovered.current = false;
        timeoutRef.current = setTimeout(() => {
            closeModal(false);
        }, duration * 1000);
    };

    return (
        <div className="modal">
            <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
                <div>
                    <h2>{title ? title : 'Attention'}</h2>
                    <p>{message}</p>
                </div>
                <div className='form-button-container'>
                    <button type='button' onClick={() => closeModal()}>Fermer</button>
                </div>
            </div>
        </div>
    )
}
