import React, { useEffect, useState } from 'react';
import { Link, useLocation, useNavigate } from "react-router-dom"

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import StatusLabel from '../input/StatusLabel';
import matricule from '../util/matricule';
import { RiTShirtLine } from 'react-icons/ri';
import { MdHouseSiding } from 'react-icons/md';
import { GiBaseballBat, GiNotebook } from 'react-icons/gi';
import { IoFlashlightSharp } from 'react-icons/io5';
import { AiOutlineSafety } from 'react-icons/ai';
import { FaHandHolding, FaSolarPanel, FaSprayCan } from 'react-icons/fa';
import moment from 'moment';

export default function Equipement({auth, equipements, setEquipements, currentId, setCurrentId}) {
    const locationSearch = useLocation().search
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(['achat', 'tenue'].includes(auth.role))
    const navigate = useNavigate()

    let searchItems = [
        {label: 'Référence', name: 'id', type:'number'},
        {label: 'Sans employé', name: "sans_employe", type:"number"},
        {label: 'Pour validation', name: "pour_validation", type:"number"},
        {label: 'Valider', name: "valider", type:"number"},
        {label: 'Status', name: 'status', type:'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Employe', name: 'employe_id', type:'number'},
        {label: 'Site', name: 'site_id', type:'number'},
        {label: 'Type d\'équipement', name: 'type_equipement', type:'string'},
    ]
    if(auth.role != 'superviseur')
        searchItems.push({label: 'Superviseur', name: 'user_id', type:'number'})
    if(!["achat", "tenue"].includes(auth.role)){
        searchItems.push({label:"Type de service", name: "type_service", type : "string"})
    }
    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch)
        if (!params.get('type_service')) {
            params.set('type_service', "tenue")
            navigate(location.pathname + "?" + params)
        }
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            params.set("offset", 0)
        }
        else
           params.set("offset", equipements.length)
        
        if(!initial && equipements.length)
            params.set("last_id", equipements[equipements.length - 1].id)
        axios.get('/api/equipement' + '?' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial) {
                        setEquipements(res.data.equipements)
                    }
                    else {
                        const list = equipements.slice().concat(res.data.equipements)
                        setEquipements(list)
                    }
                    setDataLoaded(['achat', 'tenue'].includes(auth.role) ? res.data.equipements.length == 0 : res.data.equipements.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>Equipement</h2>
                {   
                    (['superviseur', 'resp_sup', 'resp_op'].includes(auth.role)) && 
                    <Link className='btn btn-primary' to="/equipement/add">Nouvelle demande</Link>
                }
            </div>
            <SearchBar listItems={searchItems}/>
            {

                equipements.length == 0 ?
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                    <InfiniteScroll
                        dataLength={equipements.length}
                        next={fetchMoreData}
                        hasMore={!allDataLoaded}
                        loader={<LoadingPage/>}
                    >
                        <div className="line-container">
                            <div className="row-list">
                                <b className="line-cell-md">Site</b>
                                <b className="status-line">
                                    <StatusLabel color="grey"/>
                                </b>
                                <b className='line-cell-sm'>Date</b>
                                {
                                    auth.role == "tenue" &&
                                        <b className="status-line">Nb</b>
                                }
                                <b>{auth.role == "tenue" ? "Employé" : "Demande"}</b>
                            </div>
                        </div>
                        {
                            equipements.map(eq => (
                                <div key={eq.id}
                                    className={`line-container ${currentId && currentId == eq.id ? 'selected' : ''}`}
                                    onClick={() => setCurrentId(eq.id)}
                                >
                                    <div className="row-list">
                                        <span className="line-cell-md capitalize">
                                            {
                                                eq.site ? 
                                                    eq.site 
                                                :
                                                    <span>
                                                        {eq.user_nom} <span className='secondary'>{" <" + eq.user_email  + ">"}</span>
                                                    </span>
                                            }
                                        </span>
                                        <span className="status-line">
                                            {
                                                eq.type == "tenue" ?
                                                    <RiTShirtLine color={eq.status_color}/>
                                                : eq.type == "guerite" ?
                                                    <MdHouseSiding color={eq.status_color}/>
                                                : eq.type == "tonfa" ?
                                                    <GiBaseballBat color={eq.status_color}/>
                                                : eq.type == "torche" ?
                                                    <IoFlashlightSharp color={eq.status_color}/>
                                                : eq.type == "epi" ?
                                                    <AiOutlineSafety color={eq.status_color}/>
                                                : eq.type == "chargeur" ?
                                                    <FaSolarPanel color={eq.status_color}/>
                                                : eq.type == "fourniture_bureau" ?
                                                    <GiNotebook color={eq.status_color}/>
                                                : eq.type == "fourniture_entretien" ?
                                                    <FaSprayCan color={eq.status_color}/>
                                                :
                                                    <FaHandHolding color={eq.status_color}/>
                                            }
                                        </span>
                                        <span className='line-cell-sm'>{eq.created_at ? `${moment(eq.created_at).format("DD/MM/YY HH:mm")}` : ""}</span>
                                        
                                        {
                                            auth.role == "tenue" &&
                                            <span className="status-line">
                                                {eq.articles.length}
                                            </span>
                                        }
                                        <span>
                                            {
                                                eq.employe ?
                                                    matricule(eq) + " " + eq.employe
                                                :
                                                    <span>
                                                        {eq.motif} 
                                                        <span className='secondary'>{eq.detail ? "<" + eq.detail + ">" : ""}</span>
                                                    </span>
                                            }
                                        </span>
                                            {/* {eq.articles && eq.articles.join(", ")} */}
                                    </div>
                                </div>
                            ))
                        }
                    </InfiniteScroll>
            }
        </div>
    }
    </>
}
