const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const { sendMail } = require('../auth')

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == 'task')
const destination_vg = (emails) => {
    return ["<EMAIL>","<EMAIL>", "<EMAIL>","<EMAIL>","<EMAIL>"].concat(emails.map(e => e.email))
}
// const destination_test = ["<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

function getDayOrNightExport(){
	let beginDay = moment().set({hour:8, minute:10, second:0})
	let endDay = moment().set({hour:20, minute:10, second:0})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
	else {
		if(moment().isBefore(beginDay))
			return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 06:00:00"
		return moment().format("YYYY-MM-DD") + " 06:00:00"
	}
}
function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectOperationEmail = "SELECT u.email FROM users u " + 
    "WHERE u.role in ('superviseur', 'resp_sup', 'resp_op') " + 
    "and u.email not in ('<EMAIL>', '<EMAIL>', '<EMAIL>') "

const sqlSelectLastVisitePosteExport = "SELECT value FROM params p WHERE p.key = 'last_visite_poste_export'"

const sqlSelectAlarm = "SELECT v.idademco, v.employe_id, v.site_id, v.dtarrived from alarms v " +
    "where v.dtarrived > ? " +
    "order by v.dtarrived asc"

const sqlSelectVisitePoste = (dateString) =>{
    const begin = dateString
    const end = moment(dateString).add(12, "hours").format("YYYY-MM-DD HH:mm:ss")
	return "SELECT vp.id, vp.user_id, vp.site_id, vp.date_visite, vp.start, vp.end, vp.isBreackDown, vp.compte_rendu, vp.created_at, s.pointeuse," +
        "u.name as 'user_nom', coalesce(ur.email, u.email) as 'user_email', u.flotte, s.idsite, s.nom as 'site', " +
        "vp.employe_id, e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit " +
        "from visite_postes vp " +
        "left join sites s on s.idsite = vp.site_id " +
        "left join users u on u.id = vp.user_id " +
        "left join users ur on ur.id = u.real_email_id " +
        "left join employes e on e.id = vp.employe_id " +
        "where u.role in ('superviseur', 'resp_sup') and vp.date_visite > '" + begin +"' and vp.date_visite <= '" + end +"' " +
        "order by vp.user_id, vp.created_at"
}

function sqlUpdateLastVisitePosteExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_visite_poste_export'"
}

function generateManqueExcelFile(workbook, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
    const fillHeader = {
		type: 'pattern',
		pattern: 'solid',
		fgColor: {argb:'88cccccc'}
	}

	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    const worksheet = [
        {label : "Pas en panne", value: 0},
        {label : "En panne", value: 1},
    ]
    worksheet.forEach(sheet => {
        const ws = workbook.addWorksheet(sheet.label)
        ws.getColumn('A').width = 50
        ws.getColumn('B').width = 15
        ws.getColumn('C').width = 50
        ws.getColumn('D').width = 10
        ws.getColumn('E').width = 10
        ws.getColumn('F').width = 10
        ws.getColumn('G').width = 20
        ws.getColumn('H').width = 50
        ws.getCell('A1').value = "Manque de confirmation biométrique (" + data.filter((d) => d.isBreackDown == sheet.value).length + ")" 
        ws.getCell('A1').font = fontHeader
        ws.mergeCells('A1:H1')
        
        let line = 3
        ws.getCell('A' + line).value = "Superviseur"
        ws.getCell('A' + line).border = borderStyle
        ws.getCell('A' + line).font = fontBold
        ws.getCell('B' + line).value = "Contact"
        ws.getCell('B' + line).border = borderStyle
        ws.getCell('B' + line).alignment = alignmentStyle
        ws.getCell('B' + line).font = fontBold
        ws.getCell('C' + line).value = "Site"
        ws.getCell('C' + line).border = borderStyle
        ws.getCell('C' + line).font = fontBold
        ws.getCell('D' + line).value = "Entré"
        ws.getCell('D' + line).border = borderStyle
        ws.getCell('D' + line).alignment = alignmentStyle
        ws.getCell('D' + line).font = fontBold
        ws.getCell('E' + line).value = "Sortie"
        ws.getCell('E' + line).border = borderStyle
        ws.getCell('E' + line).alignment = alignmentStyle
        ws.getCell('E' + line).font = fontBold
        ws.getCell('F' + line).value = "Panne"
        ws.getCell('F' + line).border = borderStyle
        ws.getCell('F' + line).alignment = alignmentStyle
        ws.getCell('F' + line).font = fontBold
        ws.getCell('G' + line).value = "Date de la visite"
        ws.getCell('G' + line).border = borderStyle
        ws.getCell('G' + line).font = fontBold
        ws.getCell('G' + line).alignment = alignmentStyle
        ws.getCell('H' + line).value = "Vérification"
        ws.getCell('H' + line).border = borderStyle
        ws.getCell('H' + line).font = fontBold
        ws.getCell('H' + line).alignment = alignmentStyle
        ws.mergeCells('A' + (line-1) + ':H' + (line-1))
            
        line++
        let lastName = data[0].user_nom
        let colorOdd = true
        data.filter((d) => d.isBreackDown == sheet.value).forEach(vp => {
            if(lastName != vp.user_nom){
                lastName = vp.user_nom
                colorOdd = !colorOdd
            }
            if(colorOdd){
                ws.getCell("A" + line).fill = fillHeader
                ws.getCell("B" + line).fill = fillHeader
                ws.getCell("C" + line).fill = fillHeader
            }
            ws.getCell('A' + line).value = vp.user_nom + " <" + vp.user_email + ">"
            ws.getCell('A' + line).border = borderStyle
            ws.getCell('B' + line).value = vp.flotte ? vp.flotte : ""
            ws.getCell('B' + line).border = borderStyle
            ws.getCell('B' + line).alignment = alignmentStyle
            ws.getCell('C' + line).value = capitalizeFirstLetter(vp.site)
            ws.getCell('C' + line).border = borderStyle
            ws.getCell('D' + line).font = { 
                name: 'Segoe UI Emoji',
                color: { argb: vp.start != null ? '00FF00' : 'FF0000' }
            };
            ws.getCell('D' + line).value = vp.start != null ? "✅" : "❌"
            ws.getCell('D' + line).border = borderStyle
            ws.getCell('D' + line).alignment = alignmentStyle
            ws.getCell('E' + line).font = { 
                name: 'Segoe UI Emoji',
                color: { argb: vp.end != null ? '00FF00' : 'FF0000' }
            };
            ws.getCell('E' + line).value = vp.end != null ? "✅" : "❌"
            ws.getCell('E' + line).border = borderStyle
            ws.getCell('E' + line).alignment = alignmentStyle
            ws.getCell('F' + line).font = { 
                name: 'Segoe UI Emoji',
                color: { argb: vp.isBreackDown == 0 ? '00FF00' : 'FF0000' }
            };
            ws.getCell('F' + line).value = vp.isBreackDown == 0 ? "🟢" : "🔴"
            ws.getCell('F' + line).border = borderStyle
            ws.getCell('F' + line).alignment = alignmentStyle
            ws.getCell('G' + line).value = moment(vp.date_visite).format("DD-MM-YY HH:mm")
            ws.getCell('G' + line).border = borderStyle
            ws.getCell('G' + line).alignment = alignmentStyle
            ws.getCell('H' + line).value = {
                text: 'Cliquez ici',
                hyperlink: "https://154.126.33.80:8001/site?id="+ vp.idsite + "&date-alarm=" + moment(vp.date_visite).format("YYYY-MM-DD") + "%20" +  moment(vp.date_visite).format("HH:mm")
            };
            ws.getCell('H' + line).border = borderStyle
            ws.getCell('H' + line).alignment = alignmentStyle
            line++
        })
    })
}

const stayOnSite = (start, end) => {
    if (start == null || end == null)
        return ""
    const diffMinutes = moment(end).diff(moment(start), 'minutes')
    if (diffMinutes < 60) {
        return `${diffMinutes} minutes`
    } else {
        const hours = Math.floor(diffMinutes / 60)
        const minutes = diffMinutes % 60
        if (minutes == 0) {
            return `${hours} heure`
        } else if (minutes == 1) {
            return (`${hours} heure ${minutes} minute`)
        } else {
            return (`${hours} heure ${minutes} minutes`)
        }
    }
}

function generateVisitePosteExcelFile(workbook, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
	const fontPrimary = { color: { argb: '336666' } }
	const fontDanger = { color: { argb: 'e91e63' } }

	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }
    data.forEach(sup => {
        const worksheet = workbook.addWorksheet(sup.nom + " <" + sup.email + ">")
        worksheet.getColumn('A').width = 50
        worksheet.getColumn('B').width = 20
        worksheet.getColumn('C').width = 50
        worksheet.getColumn('D').width = 20
        worksheet.getColumn('E').width = 20
        worksheet.getColumn('F').width = 20
        worksheet.getCell('A1').value = sup.nom + " (" + sup.visites.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:E1')
    
        let line = 3
        
        worksheet.getCell('A' + line).value = "Site"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontBold
        worksheet.getCell('B' + line).value = "Date de la visite"
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).font = fontBold
        worksheet.getCell('B' + line).alignment = alignmentStyle
        worksheet.getCell('C' + line).value = "Compte rendu"
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).font = fontBold
        worksheet.getCell('D' + line).value = "Début"
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).font = fontBold
        worksheet.getCell('E' + line).value = "Fin"
        worksheet.getCell('E' + line).border = borderStyle
        worksheet.getCell('E' + line).font = fontBold
        worksheet.getCell('F' + line).value = "Durée(s)"
        worksheet.getCell('F' + line).border = borderStyle
        worksheet.getCell('F' + line).font = fontBold
        worksheet.mergeCells('A' + (line-1) + ':F' + (line-1))
        line++
    
        sup.visites.forEach(vp => {
            worksheet.getCell('A' + line).value = capitalizeFirstLetter(vp.site)
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = moment(vp.date_visite).format("DD-MM-YY HH:mm")
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('B' + line).alignment = alignmentStyle
            worksheet.getCell('C' + line).value = vp.compte_rendu
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('D' + line).value = vp.start ? moment(vp.start).format("DD-MM-YY HH:mm") : ""
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('E' + line).value = vp.end ? moment(vp.end).format("DD-MM-YY HH:mm") : ""
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('F' + line).value = vp.start && vp.end && stayOnSite(vp.start, vp.end)
            worksheet.getCell('F' + line).border = borderStyle
            if(vp.pointeuse){
                if(vp.vigilances.length > 0) {
                    worksheet.getCell('A' + line).font = fontPrimary
                    worksheet.getCell('B' + line).font = fontPrimary
                    worksheet.getCell('C' + line).font = fontPrimary
                    worksheet.getCell('D' + line).font = fontPrimary
                    worksheet.getCell('E' + line).font = fontPrimary
                    worksheet.getCell('F' + line).font = fontPrimary
                }
                else {
                    worksheet.getCell('A' + line).font = fontDanger
                    worksheet.getCell('B' + line).font = fontDanger
                    worksheet.getCell('C' + line).font = fontDanger
                    worksheet.getCell('D' + line).font = fontDanger
                    worksheet.getCell('E' + line).font = fontDanger
                    worksheet.getCell('F' + line).font = fontDanger
                }
            }
            line++
        })
        
    });
}

function doVisitePosteExport(dateString){
	console.log("do visite service")
    pool.query(sqlSelectOperationEmail, [], async (err, emails) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb email: " + emails.length)
            pool.query(sqlSelectVisitePoste(dateString), [], async (err, visitePostes) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb visite poste: " + visitePostes.length)
                    pool.query(sqlSelectAlarm, [moment(dateString).subtract(1, "day").format("YYYY-MM-DD HH:mm:ss")], async (err, vigilances) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Nb vigilance: " + vigilances.length)
                            let visitePosteBySup = []
                            visitePostes.forEach(vp => {
                                vp.vigilances = []
                                vigilances.forEach(vg => {
                                    if(vg.employe_id == vp.employe_id 
                                        && moment(vp.date_visite).isAfter(moment(vg.dtarrived).subtract(10, "minutes"))
                                        && moment(vp.date_visite).isBefore(moment(vg.dtarrived).add(10, "minutes"))
                                    ) {
                                        vp.vigilances.push(vg)
                                    }
                                })
                            })
                            visitePostes.forEach(vst => {
                                if(!visitePosteBySup.map(sup => sup.id).includes(vst.user_id))
                                    visitePosteBySup.push({
                                        id: vst.user_id,
                                        nom: vst.user_nom,
                                        email: vst.user_email,
                                        visites: []
                                    })
                            })
                            visitePosteBySup.forEach(sup => {
                                visitePostes.forEach(vst => {
                                    if(sup.id == vst.user_id)
                                        sup.visites.push(vst)
                                })
                                sup.count = sup.visites.length
                                sup.manque = sup.visites.filter(
                                    v => (v.pointeuse == 1 && (v.start == null || v.end == null)) 
                                    || (v.pointeuse == 1 && (v.start == null && v.end == null))).length
                            })

                            const workbookVisitePoste = new Excel.Workbook()
                            const header = "Visite de poste " + moment(dateString).format("DD MMMM YYYY") + (moment(dateString).format("HH:mm:ss") == "06:00:00" ? " JOUR" : " NUIT")
                            generateVisitePosteExcelFile(workbookVisitePoste, visitePosteBySup)
                            const visitePosteSiteBuffer = await workbookVisitePoste.xlsx.writeBuffer()
                            
                            const manqueHeader = "Manque de confirmation d'empreinte " + moment(dateString).format("DD MMMM YYYY") + (moment(dateString).format("HH:mm:ss") == "06:00:00" ? " JOUR" : " NUIT")
                            const workbookManque = new Excel.Workbook()
                            generateManqueExcelFile(workbookManque, visitePostes.filter(v => (v.pointeuse && v.vigilances.length == 0 || v.pointeuse && (v.start == null || v.end == null) || v.pointeuse && (v.start == null || v.end == null)))
                                .sort((a, b) => (a.user_nom > b.user_nom) ? 1 : (a.user_nom < b.user_nom) ? -1 : 0))
                            const manqueBuffer = await workbookManque.xlsx.writeBuffer()
                            let manques = visitePostes.filter(v => (v.pointeuse && v.vigilances.length == 0 || v.pointeuse && (v.start == null || v.end == null) || v.pointeuse && (v.start == null || v.end == null)))
                            let manquesSansPanne = manques.filter(m => m.isBreackDown == 0)

                            sendMail(
                                pool,
                                isTask ? destination_vg(emails) : destination_test,
                                header, 
                                visitePosteBySup.length > 0 ? 
                                    "<p>Manque de confirmation d'empreinte : " + manquesSansPanne.length + "</p>" +
                                    "<h3>Visites de postes</h3>" +
                                    "<ul>" +
                                        visitePosteBySup.map(v => "<li>" + v.nom + "&#60;" + v.email + "&#62;" + " : " + v.count + "</li>").join("") +
                                    "</ul>"
                                :
                                    "<p>Aucune viste de poste durant le service</p>"
                                ,
                                [
                                    {
                                        filename: header + ".xlsx",
                                        content: visitePosteSiteBuffer
                                    },
                                    {
                                        filename: manqueHeader + ".xlsx",
                                        content: manqueBuffer
                                    },
                                ],
                                (response) => {
                                    if(response && isTask){
                                        pool.query(sqlUpdateLastVisitePosteExport(dateString), [], (e, r) =>{
                                            if(e)
                                                console.error(e)
                                            else
                                                console.log("update last diag export: " + r)
                                            process.exit(1)
                                        })
                                    }
                                    else
                                        process.exit(1)
                                }
                                , isTask
                            )
                        }
                    })
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && ["06:00:00", "18:00:00"].includes(process.argv[3])){
    console.log("send test...")
    doVisitePosteExport(process.argv[2] + ' ' + process.argv[3])
}
else if(isTask){
    let date_vigilance = getDayOrNightExport()
    pool.query(sqlSelectLastVisitePosteExport, [], (err, result) => {
        if(err)
            console.error(err)
        else if(result && result[0].value == date_vigilance) {
            console.log("export list visite_poste already done!")
            process.exit(1)
        }
        else {
            console.log("exporting ...")
            doVisitePosteExport(date_vigilance)
        }
    })
}
else
    console.log("please specify command!")