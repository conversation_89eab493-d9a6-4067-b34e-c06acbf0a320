const moment = require('moment')
const mysql = require('mysql2')
const nodemailer = require("nodemailer")
const hbs = require('nodemailer-express-handlebars')
const path = require('path')

const email_config = {
	host: "ssl0.ovh.net",
	port: 465,
	secure: true,
	auth: {
		user: "<EMAIL>",
		pass: "ArTl$DrXP4$21"
	},
	tls: {
		rejectUnauthorized: false
	}
}

moment.locale('fr')

const db_config = require("../auth").db_config_admin

const pool = mysql.createPool(db_config)

const destination_test = {
    to: "<EMAIL>",
    cc: "<EMAIL>"
}

function sqlUpdateLastReminderPart(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_reminder_part'"
}

const sqlSelectLastReminderPart = "SELECT value FROM params p WHERE p.key = 'last_reminder_part'"

const destination_vg = (users) => {
    const emailList = [...new Set(users.map(row => row.email))] // Extraction unique des emails
        .filter(email => email !== null)
        .join(', ')
    return {to: emailList, cc: "<EMAIL>, <EMAIL>"}
}


let transporter = nodemailer.createTransport(email_config)

const handlebarOptions = {
    viewEngine: {
        partialsDir: path.resolve('./export/views/'),
        defaultLayout: false,
    },
    viewPath: path.resolve('./export/views/'),
};

transporter.use('compile', hbs(handlebarOptions))

const sqlFindUser = (dateString) =>{
    return "SELECT COALESCE(u2.email, u.email) as 'email' "
    + " FROM employes emp "
    + " LEFT JOIN part_variables pv ON pv.employe_id = emp.id and pv.date_paie = '" + moment(dateString).format('YYYY-MM') + "-20' "
    + " LEFT JOIN employes resp ON resp.id = emp.resp_part_id "
    + " LEFT JOIN users u ON u.employe_id = emp.resp_part_id "
    + " left join users u2 on u2.id = u.real_email_id "
    + " WHERE (emp.soft_delete IS NULL OR emp.soft_delete = 0) "
    + " AND emp.part_variable = 1 "
    + " AND emp.resp_part_id IS NOT NULL "
    + " AND pv.id IS NULL "
    + " AND (emp.soft_delete IS NULL OR emp.soft_delete = 0) GROUP BY resp.id, u.name, u.id, u.email"
}

function sendEMail(destination, subject, date_string, callback){
	const message = {
		from: email_config.auth.user,
		to: destination.to,
		cc: destination.cc,
		subject: subject,
        template: "reminder_part",
        context: {
            title: subject,
            date_part: date_string,
        }
	};
	transporter.sendMail(message , (err, info) => {
		if(err){
			console.error(err)
            process.exit()
        }
		else {
    		callback()
        }
	})
}

function doReminderPart(dateString){
	console.log("doReminderPart")
    pool.query(sqlFindUser(dateString), [], async (err, emails) => {
        if(err)
            console.error(err)
        else {
            const header = "Rappel part variable " + moment(dateString).format("MMMM YYYY") ;
            sendEMail(
                process.argv[2] == 'task' ? destination_vg(emails) : destination_test,
                header,
                moment(dateString).format("MMMM YYYY"),
                () => {
                    if(process.argv[2] == 'task'){
                        pool.query(sqlUpdateLastReminderPart(dateString), [], (e, r) =>{
                            if(e)
                                console.error(e)
                            else
                                console.log("update last reminder part: " + r)
                            process.exit(1)
                        })
                    }
                    else
                        process.exit(1)
                }
            )
        }
    })
}

if(/^\d{4}-\d{2}$/.test(process.argv[2])){
    console.log("send test...")
    doReminderPart(process.argv[2])
} else if(process.argv[2] == 'task'){
    let date_reminder = moment().format('YYYY-MM') + "-22 09:00:00"
    if(moment().isSame(moment(date_reminder), 'hour')){
        pool.query(sqlSelectLastReminderPart, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && result[0].value == date_reminder) {
                console.log("Send reminding email already done!")
                process.exit(1)
            }
            else {
                console.log("reminder ...")
                doReminderPart(date_reminder)
            }
        })
    }
    else{
        console.log("Reminder date part variable has not yet arrived.")
        process.exit(1)
    }
} else{
    console.log("please specify command!")
    process.exit(1)
}