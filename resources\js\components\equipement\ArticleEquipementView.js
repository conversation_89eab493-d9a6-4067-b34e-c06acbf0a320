import React, { useEffect, useState } from 'react';
import { useParams } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import SearchBar from '../input/SearchBar';
import ActionArticle from './article/ActionArticle';
import LoadingPage from '../loading/LoadingPage';

export default function ArticleEquipementView({auth}) {
    const params = useParams();
    const [isLoading, toggleLoading] = useState(false)
    const [articles, setArticles] = useState(null)
    const [search, setSearch] = useState()

    let searchItems = [
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Employe', name: 'employe_id', type:'number'},
        {label: 'Site', name: 'site_id', type:'number'},
        {label: 'Article', name: 'article', type: 'string'},
        {label: 'Superviseur', name: 'user_id', type:'number'},
    ]

    const updateData = () => {
        if(search){
            let isMounted = true;
            setArticles(null)
            toggleLoading(true)
            const request = (params.status ? ('status=' + params.status) : (
                'search=' + search.name + '&type=' + search.type + '&value=' + search.value
            ))
            axios.get('/api/article_equipement?' + request,  useToken())
            .then((res) => {
                if(isMounted) {
                    if(res.data.error)
                        console.error(res.data.error)
                    else 
                        setArticles(res.data.articles)
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e)
                toggleLoading(false)
            })
            return () => { isMounted = false };
        }
    }

    useEffect(updateData, [params, search])

    return (
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="title-container space-between">
                <h2>
                    Demande d'équipement par article
                </h2>
            </div>
            <SearchBar search={search} setSearch={setSearch} listItems={searchItems}/>
            {
                articles && (
                    articles.length == 0 ? <h3 className='center secondary'>Aucun données trouvé</h3>
                    :
                    articles.map(ac => (
                        <div className="card-container" key={ac.id}>
                            <h3>
                                {ac.designation}
                            </h3>
                            {
                                ac.employe_id &&
                                <div>
                                    Employe: <span className='text'>
                                        {matricule(ac)} {ac.employe} 
                                    </span>
                                </div>
                            }
                            {
                                ac.site &&
                                <div>
                                    Site: 
                                    <span className='text'>
                                        {ac.site}
                                    </span>
                                </div>
                            }
                            <p style={{whiteSpace: "pre-line"}}>
                                Motif: <span className='text'>{ac.motif}</span>
                            </p>
                            {
                                ac.user_id != auth.id &&
                                <div>
                                    Demandeur: <span className='text'>{ac.user_nom + " <" + ac.user_email + ">"}</span>
                                </div>
                            }
                            <div className='card-header'>
                                <ActionArticle article={ac} updateData={updateData}/>
                            </div>
                        </div>
                    )
                ))
            }
        </div>
    )
}