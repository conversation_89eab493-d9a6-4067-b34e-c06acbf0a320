import React, { useEffect, useState } from "react";
import { registerLocale } from "react-datepicker";
import fr from "date-fns/locale/fr"; // the locale you want
registerLocale("fr", fr); // register it with the name you want

import "react-datepicker/dist/react-datepicker.css";
import { useLocation, useNavigate } from "react-router-dom";
import InputMonthYear from "./InputMonthYear";

export default function InputMonthYearModal({ onChange, closeModal, useLink, paramsMonthYear }) {
    const navigate = useNavigate();
    const location = useLocation();
    const [selectedDate, setDate] = useState({
        month: "",
        year: "",
    });
    const [monthYear, setMonthYear] = useState("");
    useEffect(() => {
        setMonthYear(selectedDate.year+"-"+selectedDate.month.toString().padStart(2, "0"));
    }, [selectedDate]);


    const handleOk = () => {
        if (useLink) {
            let params = new URLSearchParams(location.search);
            if (paramsMonthYear)
                params.set(paramsMonthYear, monthYear);
            else
                params.set("date_paie", monthYear);
            navigate(location.pathname + "?" + params);
        }
        onChange(selectedDate);
        closeModal();
    };

    return (
        <div className="modal">
            <div>
                <h2>Date</h2>
                <div >
                    <div style={{marginBottom:50}}>
                        <InputMonthYear value={selectedDate} onChange={setDate} required />
                    </div>
                </div>
                <div className="form-button-container">
                    <button
                        disabled={!selectedDate}
                        className="btn-primary"
                        onClick={handleOk}
                    >
                        OK
                    </button>
                    <button onClick={() => closeModal()}>Annuler</button>
                </div>
            </div>
        </div>
    );
}
