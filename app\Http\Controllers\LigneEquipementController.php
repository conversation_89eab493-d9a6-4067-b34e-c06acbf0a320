<?php

namespace App\Http\Controllers;
use App\Models\LigneEquipement;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Request;

class LigneEquipementController extends Controller
{
    public function ligne_equipement($id) {
        $articles = DB::select("SELECT le.id, ac.designation as nom_article, le.status, le.estUsé, le.estDeduit
        FROM ligne_equipement le
        LEFT JOIN articles ac ON ac.name = le.article
        WHERE le.mouvement_equipement_id = ?", [$id]);
        return response()->json($articles);
    }

    public static function new_ligne_equipement($article, $mouvement_equipement_id, $status) {
        $ligne_equipement = new LigneEquipement();
        $ligne_equipement->article = $article;
        $ligne_equipement->mouvement_equipement_id = $mouvement_equipement_id;
        $ligne_equipement->status = $status;
        $ligne_equipement->save();
        return response(["success" => "Ligne bien enregistrée", "id" => $ligne_equipement->id]);
    }

    public static function update_ligne_equipement($action, $selected_articles, $mouvement_equipement_id) {
        $ligne_equipements = LigneEquipement::where('mouvement_equipement_id', $mouvement_equipement_id)->get();
        $status = $action == "retourner"
        ? "retourné"
        : ($action == "transferer"
            ? "transferé"
            : "direct");
        if ($action !== "usure" && $action !== "deduction") {
            foreach($ligne_equipements as $le) {
                if (in_array($le->article, $selected_articles)) {
                    $le->status = $status;
                    $le->save();
                }
            }
        }
        if ($action === "usure") {
            foreach($ligne_equipements as $le) {
                if (in_array($le->article, $selected_articles)) {
                    $le->estUsé = 1;
                    $le->save();
                }
            }
        }
        if ($action === "deduction") {
            foreach($ligne_equipements as $le) {
                if (in_array($le->article, $selected_articles)) {
                    $le->estDeduit = 1;
                    $le->save();
                }
            }
        }

        return response(["success" => "Ligne bien modifié", "id" => $mouvement_equipement_id]);
    }
}
