import React, { useEffect, useState } from 'react';
import {useLocation, useParams} from 'react-router-dom'
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import Textarea from '../input/Textarea';
import InputAgence from '../input/InputAgence';
import DualContainer from '../container/DualContainer';
import InputSite from '../input/InputSite';
import removeDuplicateBreak from '../util/stringUtil';

export default function EditJuridique({recouvrement, title, action}) {
    const params = useParams()
    const locationSearch = useLocation().search
    const searchParams = new URLSearchParams(locationSearch)
    const [reference, setReference] = useState("")
    const [agence, setAgence] = useState(null)
    const [site, setSite] = useState(null);
    const [debiteur, setDebiteur] = useState("");
    const [contrat, setContrat] = useState("");
    const [facture, setFacture] = useState("");
    const [montant, setMontant] = useState(0)
    const [police, setPolice] = useState("");
    const [agent, setAgent] = useState("");
    const [fait, setFait] = useState("")
    const [suivi, setSuivi] = useState("");
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const contratStr = removeDuplicateBreak(contrat)
        const faitStr = removeDuplicateBreak(fait)
        const suiviStr = removeDuplicateBreak(suivi)
        const data = {
            recouvrement: recouvrement ? 1 : 0,
            reference: reference,
            agence_id: agence ? agence.id : '',
            site_id: site ? site.id : '',
            debiteur: debiteur,
            contrat: contratStr,
            facture: facture,
            montant: montant,
            police: police,
            agent: agent,
            fait: faitStr,
            suivi: suiviStr,
            fait_id: searchParams.get("fait_id"),
        }
        axios.post(action + (params.id ? "/" + params.id : ""), data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setNotification({success: "Une erreur est survenue."})
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    useEffect(() => {
        let isMounted = true
        const siteId = searchParams.get("site_id")
        if(!recouvrement && siteId){
            axios.get('/api/site/show/' + siteId, useToken())
            .then((res) => {
                if(isMounted){
                    setSite(res.data)
                }
            })
        }
        if(params.id){
            axios.get('/api/juridique/detail/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    const juridique = res.data
                    if(juridique.agence)
                        setAgence(juridique.agence)
                    if(juridique.site)
                        setSite(juridique.site)
                    if(juridique.reference)
                        setReference(juridique.reference)
                    if(juridique.debiteur)
                        setDebiteur(juridique.debiteur)
                    if(juridique.contrat)
                        setContrat(juridique.contrat)
                    if(juridique.facture)
                        setFacture(juridique.facture)
                    if(juridique.montant)
                        setMontant(juridique.montant)
                    if(juridique.police)
                        setPolice(juridique.police)
                    if(juridique.agent)
                        setAgent(juridique.agent)
                    if(juridique.fait)
                        setFait(juridique.fait)
                    if(juridique.suivi)
                        setSuivi(juridique.suivi)
                }
            })
        }
        return () => { isMounted = false };
    }, [])

    return (
        <div id="content">
            {
                notification ? 
                    <Notification 
                        next={(recouvrement ? "/recouvrement" : "/plainte") + (notification.id ? "?id=" + notification.id : "")} 
                        message={notification.success}/>
                :
                    <div>
                        <div className="title-container">
                            <h2>{title}</h2>
                        </div>
                        <form onSubmit={handleSubmit}>
                            {
                                recouvrement ?
                                    <InputText 
                                        required
                                        label="Débiteur"
                                        value={debiteur} 
                                        onChange={setDebiteur}/>
                                :
                                    <InputSite
                                        withoutDelete
                                        required
                                        value={site}
                                        onChange={setSite}/>
                            }
                            <DualContainer>
                                <InputAgence
                                    required
                                    currentSelect={agence} 
                                    setCurrentSelect={setAgence}/>
                                <InputText 
                                    label="Référence"
                                    value={reference} 
                                    onChange={setReference}/>
                            </DualContainer>
                            {
                                recouvrement ?
                                    <>
                                        <InputText 
                                            required
                                            label="Contrat"
                                            value={contrat} 
                                            onChange={setContrat}/>
                                        <InputText 
                                            required
                                            label="Facture(s)"
                                            value={facture} 
                                            onChange={setFacture}/>
                                        <InputText 
                                            required
                                            label="Montant"
                                            value={montant} 
                                            onChange={setMontant}
                                            type="number"/>
                                    </>
                                :
                                    <>
                                        <InputText 
                                            required
                                            label="Agent(s) concerné"
                                            value={agent} 
                                            onChange={setAgent}/>
                                        <InputText
                                            required
                                            label="Rappelle des faits"
                                            value={fait}
                                            onChange={setFait}/>
                                        <InputText 
                                            required
                                            label="Police/Gendarme compétent"
                                            value={police} 
                                            onChange={setPolice}/>
                                    </>
                            }
                            {
                                !params.id &&
                                <Textarea
                                    required
                                    label="Chose faite"
                                    value={suivi}
                                    onChange={setSuivi}/>
                            }
                            {
                                error &&
                                <div className='container-error'>
                                    {error}
                                </div>
                            }
                            <ButtonSubmit disabled={submitDisabled}/>
                        </form>
                    </div>
            }
        </div>
    )
}