<?php

namespace App\Http\Controllers;

use App\Models\Status;
use App\Models\Service;
use App\Models\User;
use App\Models\Employe;
use App\Models\Agence;
use App\Models\Site;
use App\Models\TypeEquipement;
use App\Models\Article;
use App\Models\EtapeContentieux;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    public function index(Request $request){
        $user = null;
        $status = null;
        $unread = null;
        $superviseur = null;
        $employe = null;
        $site = null;
        $type_equipement = null;
        $article = null;
        $agence = null;
        $service = null;
        $etape = null;
        if($request->etape_id)
            $etape = EtapeContentieux::select('id','nom')->find($request->etape_id);
        if($request->service_id)
            $service = Service::select('id','designation')->find($request->service_id);
        if($request->status)
            $status = Status::select('name','description')->find($request->status);
        if($request->unread)
            $unread = [
                'value' => "Non lu"
            ];
        if($request->user_id || $request->resp_sup_id || $request->superviseur_id)
            $user = DB::select("SELECT u.id, u.name, coalesce(u2.email, u.email) as 'email' FROM users u
                LEFT JOIN users u2 on u2.id = u.real_email_id
                WHERE u.id = ?", [$request->user_id ?? $request->resp_sup_id ?? $request->superviseur_id])[0];
        if($request->superviseur_id)
            $superviseur = DB::select("SELECT u.id, u.name, coalesce(u2.email, u.email) as 'email' FROM users u
                LEFT JOIN users u2 on u2.id = u.real_email_id
                WHERE u.id = ?", [$request->superviseur_id])[0];
        if($request->employe_id)
            $employe = Employe::select('id', 'nom', 'societe_id', 'numero_employe', 'num_emp_soit', 'numero_stagiaire', 'num_emp_saoi')->find($request->employe_id);
        if($request->site_id)
            $site = Site::select('idsite','nom')->find($request->site_id);
        if($request->agence_id)
            $agence = Agence::select('id','nom')->find($request->agence_id);
        if($request->type_equipement)
            $type_equipement = TypeEquipement::select('name', 'designation')->find($request->type_equipement);
        if($request->article)
            $article = Article::select('name', 'designation')->find($request->article);
        return response(compact('status', 'unread', 'user', 'superviseur', 'employe', 'agence',
            'service', 'site', 'type_equipement', 'article', 'etape'));
    }
}
