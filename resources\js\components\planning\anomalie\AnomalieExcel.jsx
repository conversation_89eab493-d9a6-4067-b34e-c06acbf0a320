import React, { useEffect, useState } from 'react'
import ExcelJS from 'exceljs';
import moment from 'moment';

export default function AnomalieExcel({ anomalies }) {
    const [reformatedData, setReformatedData] = useState([]);
    const workbook = new ExcelJS.Workbook();
    workbook.creator = "ADMIN";

    const border = {
        top: { style: 'thin', color: { argb: 'FF000000' } }, // Black
        left: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
    }
    const addBorderForCell = (row) => {
        return row.eachCell({ includeEmpty: true }, (cell) => {
            cell.border = border;
        })
    }

    const fontStyle = {
        size: 12,
        color: { argb: 'FFFFFFFF' },
        bold: true
    };

    const groupByRespSup = () => {
        const groupedData = anomalies.reduce((result, item) => {
            const { resp_sup_id, superviseur_id } = item;
            if (!result[resp_sup_id]) {
                result[resp_sup_id] = {};
            }
            if (!result[resp_sup_id][superviseur_id]) {
                result[resp_sup_id][superviseur_id] = [];
            }
            result[resp_sup_id][superviseur_id].push(item);
            return result;
        }, {});

        const reformated = Object.entries(groupedData).map(([respSupId, superviseurs]) => ({
            respSupId,
            respSupNom: anomalies.find(a => a.resp_sup_id === Number(respSupId))?.resp_sup || 'Unknown',
            superviseurs: Object.entries(superviseurs).map(([superviseurId, items]) => ({
                superviseurId,
                items,
            })),
        }));

        setReformatedData(reformated);
    };

    useEffect(() => {
        groupByRespSup();
    }, []);

    const excel = () => {
        const headers = ['', 'FLOTTE', 'JOUR', 'NUIT', 'MANQUE', 'SURPLUS', 'INCOHÉRENCE'];
        reformatedData.forEach(dataGroup => {
            const worksheet = workbook.addWorksheet(`${dataGroup.respSupNom}`);
            worksheet.eachRow(row => {
                row.eachCell({ includeEmpty: true }, (cell) => {
                    cell.border = border;
                })
            })
            dataGroup.superviseurs.forEach((supGroup, index) => {
                if (index !== 0) {
                    worksheet.addRow([]);
                }
                const superviseurRow = worksheet.addRow([
                    (supGroup.items[0].superviseur || '').toUpperCase(),
                    supGroup.items[0].superviseur_flotte || '',
                    supGroup.items[0].superviseur_email || ''
                ]);
                worksheet.mergeCells(`C${superviseurRow.number}:G${superviseurRow.number}`);
                superviseurRow.eachCell((cell) => {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFFF0000' }, // Red color
                    };
                    cell.font = fontStyle;
                });

                const beforeHeader = worksheet.addRow([null, null, 'CONTRAT', '', 'DEPLOYER']);
                worksheet.mergeCells(`C${beforeHeader.number}:D${beforeHeader.number}`);
                worksheet.mergeCells(`E${beforeHeader.number}:F${beforeHeader.number}`);
                beforeHeader.eachCell({ includeEmpty: true }, (cell, colNumber) => {
                    if (colNumber > 2) {
                        cell.fill = {
                            type: 'pattern',
                            pattern: 'solid',
                            fgColor: { argb: 'FFD3D3D3' }, // Gray color
                        };
                        cell.border = border;
                    }
                });

                const addHeader = worksheet.addRow(headers);
                addBorderForCell(addHeader)
                let numberNight = 0
                let numberDay = 0
                let numberIncoherence = 0
                let numberManque = 0
                let numberSurplus = 0
                supGroup.items.forEach(data => {
                    numberNight += data.nb_agent_night ?? 0
                    numberDay += data.nb_agent_day ?? 0
                    numberIncoherence += data.incoherence ?? 0
                    numberManque += data.manque ?? 0
                    numberSurplus += data.surplus ?? 0
                    const row = worksheet.addRow([
                        data.site || '',
                        data.superviseur_flotte || '',
                        data.nb_agent_day || '',
                        data.nb_agent_night || '',
                        data.manque || '',
                        data.surplus || '',
                        data.incoherence || ''
                    ]);
                    addBorderForCell(row)
                });
                worksheet.addRow([
                    '',
                    '',
                    numberDay,
                    numberNight,
                    numberManque,
                    numberSurplus,
                    numberIncoherence
                ]);
                ['A', 'B', 'C', 'D', 'E', 'F', 'G'].forEach(col => {
                    worksheet.getColumn(col).alignment = { horizontal: col == 'A' ? 'left' : 'center' }
                });
            });
        });

    };

    const onSubmit = () => {
        workbook.eachSheet((worksheet, sheetId) => {
            worksheet.getColumn('A').width = 30
            worksheet.getColumn('B').width = 15
            worksheet.getColumn('C').width = 10
            worksheet.getColumn('D').width = 10
            worksheet.getColumn('E').width = 15
            worksheet.getColumn('F').width = 15
            worksheet.getColumn('G').width = 20
        });

        workbook.xlsx.writeBuffer().then((buffer) => {
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = "ANOMALIE_" + (moment().format("MMM YYYY")).toUpperCase() + '.xlsx';
            a.click();
        });
    }

    return <div className="btn btn-primary" onClick={() => { excel(), onSubmit() }}>
        Exporter
    </div>
}
