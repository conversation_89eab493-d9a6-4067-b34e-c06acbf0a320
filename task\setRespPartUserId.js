const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const sqlSelectRespEmploye = "SELECT e.id, e.resp_part_id, re.nom FROM employes e " +
    "LEFT JOIN employes re on re.id = e.resp_part_id " +
    "WHERE e.resp_part_id is not null and e.resp_part_user_id is null"
const sqlSelectRespUser = "SELECT u.id FROM users u WHERE u.employe_id = ?"
const sqlUpdateRespEmployeUser = "UPDATE employes SET resp_part_user_id = ? WHERE id = ?"

const updateData = () => {
        pool.query(sqlSelectRespEmploye, [], async (err, employes) => {
            if(err)
                console.error(err)
            else {
                if(employes.length > 0) {
                    const currentEmploye = employes[0]
                    console.log(currentEmploye)
                    pool.query(sqlSelectRespUser, [currentEmploye.resp_part_id], async (err, users) => {
                        if(err)
                            console.error(err)
                        else if(users.length > 0){
                            const userId = users[0].id
                            pool.query(sqlUpdateRespEmployeUser, [userId, currentEmploye.id], async (err) => {
                                if(err)
                                    console.error(err)
                                else {
                                    setTimeout(() => updateData(), 50)
                                }
                            })
                        }
                        else 
                            console.log("employé sans compte utilisateur : " + currentEmploye.nom)
                    })
                }
                else {
                    console.log("no update")
                    process.exit(1)
                }
            }
        })
}

updateData()