import React from "react";
import { View, StyleSheet } from "@react-pdf/renderer";
import TableHeader from "./TableHeader";
import TableRow from "./TableRow";
import TableFooter from "./TableFooter";

const tableRowsCount = 5;

const styles = StyleSheet.create({
    tableContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        marginTop: 24,
        borderWidth: 1,
        // borderColor: "#bff0fd",
    },
});

const Items = ({ invoice }) => (
    <View style={styles.tableContainer}>
        <TableHeader />
        <TableRow items={invoice.items} />

        <TableFooter items={invoice.items} />
    </View>
);

export default Items;
