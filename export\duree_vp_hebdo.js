const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const { sendMail } = require('../auth')

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == 'task')
const destination_vg = (emails) => {
    return ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"].concat(emails.map(e => e.email))
}
// const destination_test = ["<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>"]

function getDate(){
    return moment().format("YYYY-MM-DD")
}

function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectOperationEmail = "SELECT u.email FROM users u " + 
    "WHERE u.role in ('superviseur', 'resp_sup', 'resp_op') " + 
    "and u.email not in ('<EMAIL>', '<EMAIL>', '<EMAIL>') "

const sqlSelectAlarm = "SELECT v.idademco, v.employe_id, v.site_id, v.dtarrived from alarms v " +
    "where v.dtarrived > ? " +
    "order by v.dtarrived asc"

const sqlSelectLastDureeVpHebdo = "SELECT value FROM params p WHERE p.key = 'last_duree_vp_hebdo'"

const sqlSelectVisitePoste = (dateString) =>{
    const begin = moment(dateString).subtract(7, 'days').format("YYYY-MM-DD") + " 00:00:00"
    const end = moment(dateString).subtract(1, "day").format("YYYY-MM-DD") + " 23:59:59"
    // return "SELECT vp.id, vp.user_id, vp.site_id, vp.date_visite, vp.start, vp.end, vp.compte_rendu, vp.created_at, s.pointeuse," +
    //     "u.name as 'user_nom', coalesce(ur.email, u.email) as 'user_email', s.nom as 'site', " +
    //     "vp.employe_id, e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit " +
    //     "from visite_postes vp " +
    //     "left join sites s on s.idsite = vp.site_id " +
    //     "left join users u on u.id = vp.user_id " +
    //     "left join users ur on ur.id = u.real_email_id " +
    //     "left join employes e on e.id = vp.employe_id " +
    //     "where u.role in ('superviseur', 'resp_sup', 'resp_op') and vp.created_at > '" + begin +"' and vp.created_at <= '" + end +"' " +
    //     "order by vp.user_id, vp.created_at"
    return `
        SELECT 
            u.id AS 'user_id', u.name AS 'user_nom', COALESCE(ur.email, u.email) AS 'user_email', 
            vp.id AS 'visite_id', vp.site_id, vp.date_visite, vp.start, vp.end, 
            vp.compte_rendu, vp.created_at, s.nom AS 'site', s.pointeuse, 
            vp.employe_id, e.nom AS 'employe', e.societe_id, e.numero_stagiaire, 
            e.numero_employe, e.num_emp_soit 
        FROM users u
        LEFT JOIN visite_postes vp 
            ON vp.user_id = u.id AND vp.created_at > '${begin}' AND vp.created_at <= '${end}'
        LEFT JOIN sites s 
            ON s.idsite = vp.site_id
        LEFT JOIN users ur 
            ON ur.id = u.real_email_id
        LEFT JOIN employes e 
            ON e.id = vp.employe_id
        WHERE u.role IN ('superviseur', 'resp_sup', 'resp_op')
        ORDER BY u.id, vp.created_at
    `;
    // SELECT 
    //     vp.id, vp.user_id, vp.site_id, vp.date_visite, vp.start, vp.end, 
    //     vp.compte_rendu, vp.created_at, s.pointeuse, 
    //     u.name AS 'user_nom', COALESCE(ur.email, u.email) AS 'user_email', 
    //     s.nom AS 'site', 
    //     vp.employe_id, e.nom AS 'employe', e.societe_id, e.numero_stagiaire, 
    //     e.numero_employe, e.num_emp_soit 
    // FROM users u
    // LEFT JOIN visite_postes vp 
    //     ON vp.user_id = u.id AND vp.created_at > '${begin}' AND vp.created_at <= '${end}'
    // LEFT JOIN sites s 
    //     ON s.idsite = vp.site_id
    // LEFT JOIN users ur 
    //     ON ur.id = u.real_email_id
    // LEFT JOIN employes e 
    //     ON e.id = vp.employe_id
    // WHERE u.role IN ('superviseur', 'resp_sup', 'resp_op')
    // ORDER BY u.id, vp.created_at
}

function sqlUpdateLastDureeVpHebdoExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_duree_vp_hebdo'"
}

const formatTotalVisite = (total) => {
    if (total < 60) {
        return `${total}min`;
    } else {
        const duration = moment.duration(total, 'minutes');
        const formattedTime = moment.utc(duration.asMilliseconds()).format('HH[h]mm');
        return formattedTime;
    }
};

function generateDureeVpHebdoExcelFile(workbook, data, dateString) {
    const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}

    const fontBold = {
        bold: true
    }
    const fillHeader = {
		type: 'pattern',
		pattern: 'solid',
		fgColor: {argb:'88cccccc'}
	}

    const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    const worksheet = workbook.addWorksheet("Durée")
    worksheet.getColumn('A').width = 50
    worksheet.getColumn('B').width = 20
    worksheet.getColumn('C').width = 20
    worksheet.getColumn('D').width = 20
    worksheet.getColumn('E').width = 20
    worksheet.getColumn('F').width = 20
    worksheet.getCell('A1').font = fontHeader
    worksheet.mergeCells('A1:F1')
    worksheet.mergeCells('A4:A5')
    worksheet.mergeCells('B4:C4')
    worksheet.mergeCells('D4:D5')
    worksheet.mergeCells('E4:F4')

     worksheet.getCell('A1').value = "Durée visite de poste par superviseur"
     worksheet.getCell('A2').value = 
        moment(dateString).subtract(7, "days").format("DD MMMM YYYY") 
        + " au " + moment(dateString).subtract(1, "day").format("DD MMMM YYYY")
     worksheet.getCell('A4').value = "Superviseur"
     worksheet.getCell('A4').border = borderStyle
     worksheet.getCell('A4').alignment = alignmentStyle
     worksheet.getCell('B4').value = "Nb visite"
     worksheet.getCell('B4').border = borderStyle
     worksheet.getCell('B4').alignment = alignmentStyle
     worksheet.getCell('E4').value = "Durée sur post (sites avec pointeuse)"
     worksheet.getCell('E4').border = borderStyle
     worksheet.getCell('E4').alignment = alignmentStyle
     worksheet.getCell('B5').value = "Sans pointeuse"
     worksheet.getCell('B5').border = borderStyle
     worksheet.getCell('B5').alignment = alignmentStyle
     worksheet.getCell('C5').value = "Avec pointeuse"
     worksheet.getCell('C5').border = borderStyle
     worksheet.getCell('C5').alignment = alignmentStyle
     worksheet.getCell('E5').value = "Total"
     worksheet.getCell('E5').border = borderStyle
     worksheet.getCell('E5').alignment = alignmentStyle
     worksheet.getCell('F5').value = "Moyenne (min / visite)"
     worksheet.getCell('F5').border = borderStyle
     worksheet.getCell('F5').alignment = alignmentStyle
     worksheet.getCell('D4').value = "Sans Confirmation"
     worksheet.getCell('D4').border = borderStyle
     worksheet.getCell('D4').alignment = alignmentStyle

     let line = 6;
     data.forEach(sup => {
        worksheet.getCell('A' + line).value = sup.nom + " <" + sup.email + ">"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).alignment = alignmentStyle
        let nbVisiteSansPointeuse = sup.visites.filter(v => (v.pointeuse == 0)).length;
        worksheet.getCell('B' + line).value = nbVisiteSansPointeuse
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).alignment = alignmentStyle
        let VisiteAvecPointeuse = sup.visites.filter(v => (v.pointeuse == 1));
        worksheet.getCell('C' + line).value = VisiteAvecPointeuse.length;
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).alignment = alignmentStyle
        let nbVstWithoutConfirmation = VisiteAvecPointeuse.filter(v => (
            (v.start == null || v.end == null) || (v.start == null && v.end == null))
        ).length;
        worksheet.getCell('D' + line).value = nbVstWithoutConfirmation
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).alignment = alignmentStyle
        let visiteWithStartEnd = sup.visites.filter(v => (v.start != null && v.end != null))
        if (visiteWithStartEnd.length > 0){
            let diffStartEnd = []
            visiteWithStartEnd.forEach(v => {
                diffStartEnd.push(moment(v.end).diff(moment(v.start), 'minutes'))
            })
            let total = diffStartEnd.reduce((a, b) => a + b, 0)
            worksheet.getCell('E' + line).value = formatTotalVisite(total)
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('E' + line).alignment = alignmentStyle
            worksheet.getCell('F' + line).value = 
                (total / visiteWithStartEnd.length) < 60 ?
                (total / visiteWithStartEnd.length).toFixed(2) :
                (total / visiteWithStartEnd.length).toFixed(2) + ' (' + formatTotalVisite(total / visiteWithStartEnd.length.toFixed(0)) + ')'
            worksheet.getCell('F' + line).border = borderStyle
            worksheet.getCell('F' + line).alignment = alignmentStyle
        }
        line++;
     })
}

function doVisitePosteExport(dateString){
	console.log("do visite service")
    pool.query(sqlSelectOperationEmail, [], async (err, emails) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb email: " + emails.length)
            pool.query(sqlSelectVisitePoste(dateString), [], async (err, visitePostes) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb visite poste: " + visitePostes.length)
                    pool.query(sqlSelectAlarm, [moment(dateString).subtract(7, "days").format("YYYY-MM-DD") + " 00:00:00"], async (err, vigilances) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Nb vigilance: " + vigilances.length)
                            let visitePosteBySup = []
                            visitePostes.forEach(vp => {
                                vp.vigilances = []
                                vigilances.forEach(vg => {
                                    if(vg.employe_id == vp.employe_id 
                                        && moment(vp.date_visite).isAfter(moment(vg.dtarrived).subtract(10, "minutes"))
                                        && moment(vp.date_visite).isBefore(moment(vg.dtarrived).add(10, "minutes"))
                                    ) {
                                        vp.vigilances.push(vg)
                                    }
                                })
                            })
                            visitePostes.forEach(vst => {
                                if(!visitePosteBySup.map(sup => sup.id).includes(vst.user_id))
                                    visitePosteBySup.push({
                                        id: vst.user_id,
                                        nom: vst.user_nom,
                                        email: vst.user_email,
                                        visites: []
                                    })
                            })
                            visitePosteBySup.forEach(sup => {
                                visitePostes.forEach(vst => {
                                    if(sup.id == vst.user_id)
                                        sup.visites.push(vst)
                                })
                                sup.count = sup.visites.length
                                sup.manque = sup.visites.filter(
                                    v => (v.pointeuse == 1 && (v.start == null || v.end == null)) 
                                    || (v.pointeuse == 1 && (v.start == null && v.end == null))).length
                            })

                            const workbookDureeVisitePoste = new Excel.Workbook()
                            const header = "Durée visite de poste par superviseur du " + moment(dateString).subtract(7, "days").format("DD MMMM YYYY") + " au " + moment(dateString).subtract(1, "day").format("DD MMMM YYYY")
                            generateDureeVpHebdoExcelFile(workbookDureeVisitePoste, visitePosteBySup, dateString)
                            const visitePosteSiteBuffer = await workbookDureeVisitePoste.xlsx.writeBuffer()
                            sendMail(
                                pool,
                                isTask ? destination_vg(emails) : destination_test,
                                header, 
                                "<h3>" +
                                    "Veuillez trouver ci-joint le rapport de durée de visite de poste des superviseurs " +
                                "</h3>",
                                [
                                    {
                                        filename: header + ".xlsx",
                                        content: visitePosteSiteBuffer
                                    }
                                ],
                                (response) => {
                                    if (response && isTask) {
                                        pool.query(sqlUpdateLastDureeVpHebdoExport(dateString), [], (e, r) => {
                                            if (e)
                                                console.error(e)
                                            else 
                                                console.log("update last duree vp hebdo export: " + r)
                                            process.exit(1)
                                        })
                                    } else
                                        process.exit(1)
                                }
                                , isTask
                            )
                        }
                    })
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2])){
    console.log("send test...")
    doVisitePosteExport(process.argv[2])
}
else if(isTask){
    let date = getDate()
    pool.query(sqlSelectLastDureeVpHebdo, [], (err, result) => {
        if (err)
            console.error(err)
        else if(result && result[0].value == date) {
            console.log("export list duree_vp_hebdo already done!")
            process.exit(1)
        } else {
            if (moment(date).day() === 1 && moment().isAfter(moment().set({hour: 8, minute: 0}))) {
                console.log("exporting ...")
                doVisitePosteExport(date)
            } else {
                console.log("not now...")
                process.exit(1)
            }
        }
    })
}
else
    console.log("please specify command!")
