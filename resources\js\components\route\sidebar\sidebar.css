#sidebar {
    z-index: 3 !important;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    scrollbar-width: thin;
}

.show-sidebar {
    padding: 0px 20px 20px 20px;
    width: 280px;
    max-width: 280px;
    height: 100vh;
    background-color: white;
    overflow: auto;
    /* z-index: 1 !important; */
}

.custom-scroll::-webkit-scrollbar {
    width: 5px;
}
.custom-scroll::-webkit-scrollbar-track {
    background: white; 
}
.custom-scroll::-webkit-scrollbar-thumb {
    background: #aaa;
}
.custom-scroll::-webkit-scrollbar-thumb:hover {
    background: #ccc;
}

.show-sidebar>ul,
.show-sidebar>ul>li>ul {
    list-style-type: none;
}

.show-sidebar>ul>li>span,
.show-sidebar>ul>li>a,
.show-sidebar>ul>li>ul>li>span,
.show-sidebar>ul>li>ul>li>a {
    text-decoration: none;
    color: #444;
    cursor: pointer;
}

.show-sidebar>ul>li {
    padding: 20px 10px;
    border-bottom: 1px solid #ccc;
}
.sub-menu{
    color: #666;
    padding-left: 10px;
}

.list-detail {
    list-style: none;
    margin-top: 10px;
    padding: 7px 10px;
    background-color: #ddd;
}

.list-detail>li {
    padding: 13px;
    border-bottom: 1px solid #ccc;
}

.list-detail>li>span {
    cursor: pointer;
}

.list-detail>li:last-child {
    border: none;
}

.list-detail>li>span {
    color: #444;
}

@-webkit-keyframes SHOW-BOX {
    0% {
        left: -360px;
        opacity: 0.3;
    }
    100% {
        left: 0px;
        opacity: 1;
    }
}

@-moz-keyframes SHOW-BOX {
    0% {
        left: -360px;
        opacity: 0.3;
    }
    100% {
        left: 0px;
        opacity: 1;
    }
}

@-o-keyframes SHOW-BOX {
    0% {
        left: -360px;
        opacity: 0.3;
    }
    100% {
        left: 0px;
        opacity: 1;
    }
}

@keyframes SHOW-BOX {
    0% {
        left: -360px;
        opacity: 0.3;
    }
    100% {
        left: 0px;
        opacity: 1;
    }
}

@-webkit-keyframes HIDE-BOX {
    0% {
        left: 0px;
        opacity: 1;
    }
    100% {
        left: -360px;
        opacity: 0.3;
    }
}

@-moz-keyframes HIDE-BOX {
    0% {
        left: 0px;
        opacity: 1;
    }
    100% {
        left: -360px;
        opacity: 0.3;
    }
}

@-o-keyframes HIDE-BOX {
    0% {
        left: 0px;
        opacity: 1;
    }
    100% {
        left: -360px;
        opacity: 0.3;
    }
}

@keyframes HIDE-BOX {
    0% {
        left: 0px;
        opacity: 1;
    }
    100% {
        left: -360px;
        opacity: 0.3;
    }
}

div.show-sidebar {
    position: absolute;
    -webkit-animation: SHOW-BOX .5s ease;
    -moz-animation: SHOW-BOX .5s ease;
    -o-animation: SHOW-BOX .5s ease;
    animation: SHOW-BOX .5s ease;
}

div.hide-sidebar {
    -webkit-animation: HIDE-BOX .5s ease;
    -moz-animation: HIDE-BOX .5s ease;
    -o-animation: HIDE-BOX .5s ease;
    animation: HIDE-BOX .5s ease;
}

ul.sub-menu.active{
    max-height:600px;
    margin-top: 5px;
    padding: 5px 0px;
}

.menutitle:hover .sub-menu {
    max-height:600px;
    margin-top: 5px;
    padding: 5px 0px;
}
ul.sub-menu > li{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    list-style: none;
    color: #5a5050;
    padding: 10px;
}
ul.sub-menu > li > div{
    cursor: pointer;
}
ul.sub-menu{
    display: block;
    width: 100%;
    list-style: none;
    line-height: 2em; 
    line-height: 2em;
    overflow: hidden;
    max-height:0; 
    transition-property: max-height,padding, margin-top;
    transition-duration: 0.3s;
    transition-timing-function: ease-in; 
    margin-top: 0px;
    padding: 0px 0px;
}

.submenu-item{
    /* margin-right: 10px; */
    cursor: pointer;
}
.sub-menu > li{
    padding-left: 20px;
}

.submenu-item.active {
    background-color: #dddddd;
}