.calendar {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.calendar-header {
    display: flex;
    justify-content: space-around;
    width: 100%;
    background-color: #e9edf2;
    color: rgb(0, 0, 0);
    margin-bottom: 10px;
    margin-top: 10px;
}

.calendar-header-day {
    flex: 1;
    padding: 10px;
    text-align: center;
}
.horaire-content{
    flex: 1;
    height: 35px;
    text-align: center;
    border: 1px solid #ddd;
}

.calendar-body {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    overflow-x: visible;
    min-width: 452px;
    width: 100%;
}

.calendar-day {
    position: relative;
    flex: 0 0 14.2857%;
    /* 100% / 7 jours */
    height: 100px;
    border: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    margin-bottom: 50px;
}

.calendar-day-in-modal {
    position: relative;
    flex: 0 0 14.2857%;
    /* 100% / 7 jours */
    height: 100px;
    border: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    margin-bottom: 22px;
}

.calendar-day-half {
    flex: 1;
    display: flex;
    /* align-items: center; */
    justify-content: space-between;
    /* justify-content: center; */
    width: 100%;
}

.calendar-day-half--day {
    background-color: rgba(255, 255, 255, 0.7);
    border-bottom: 1px solid #ddd;
}

.calendar-day-half--day-not-enough{
    background-color: rgb(222 0 0 / 96%);
    border-bottom: 1px solid #ddd;
}

.calendar-day-half--day-not-enough > span{
    color: rgba(252, 252, 252, 0.96);
}

.calendar-day-half--day-surplus{
    /* background-color: rgb(255 85 7 / 90%); */
    background-color: rgb(234, 7, 255);
    border-bottom: 1px solid #ddd;
}

.calendar-day-half--day-surplus > span{
    color: rgba(252, 252, 252, 0.96);
}

.calendar-day-half--night {
    background-color: rgba(0, 0, 0, 0.1);
}

.calendar-day-half--night-not-enough {
    background-color: rgb(222 0 0 / 96%);
}

.calendar-day-half--night-surplus {
    /* background-color: rgb(255 85 7 / 90%); */
    background-color: rgb(234, 7, 255);

}
.calendar-day-half--night-surplus > span, .calendar-day-half--night-not-enough > span{
    color: rgba(252, 252, 252, 0.96);
}

.day {
    margin-left: 4px;
    margin-top: 4px;
    margin-right: 4px;
}
.comment-add{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.number-agent{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    cursor: pointer;
}

.horaire {
    position: relative;
    flex: 0 0 14.2857%;
    height: 100px;
    border: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    margin-bottom: 50px;
}
.progress-bar {
    width: 100%;
    height: 5px;
    background-color: #e0e0df;
    border-radius: 5px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background-color: #073570;
    width: 0;
    transition: width 0.5s ease-in-out;
    border-radius: 5px 0 0 5px;
}

.color-lack{
    color: rgb(222 0 0 / 96%);
}
.color-surplus{
    color: rgb(234, 7, 255)
}
.color-number-3{
    /* color: #cddc39; */
    color: #9eac20;
}
.color-number-4{
    /* color: #fdd835; */
    color: #ddb70c;
}
.color-number-5{
    /* color: #8bc34a; */
    color: #569909;
}
.color-number-6{
    color: #ff9800;
}
.color-number-7{
    color: #00bcd4;
}
.color-number-default{
    color: #795548;
}
.day-action{
    display: flex;
    align-items: center;
    justify-content: center;
}

.check-box-in-case > label> .checkmark{
    background-color: darkgrey;
}
.check-box-in-case >.checkbox-container{
    display: inline;
}
.effectif{
    margin-right: 10px;
}

.icon-list{
    display: block;
    min-width: 68px;
}

.icon-list > span{
    margin-left: 10px;
}
.header-modal-replace-agent{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}