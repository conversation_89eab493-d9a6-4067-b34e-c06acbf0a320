<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Equipement;
use App\Models\TypeEquipement;
use App\Models\Article;
use App\Models\ArticleEquipement;
use App\Models\Employe;
use App\Models\User;
use App\Models\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\HistoriqueController;
use App\Http\Controllers\MailController;
use App\Models\Flotte;
use App\Models\Historique;
use App\Models\Sav;
use Carbon\Carbon;

class EquipementController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = "eq.id = '". $request->id ."'";
        else {
            if($request->type_service && !in_array($request->user()->role , ["achat", "tenue", ""])){
                $searchArray[] = " a.service = '" .$request->type_service . "'";
            }
            if($request->sans_employe) {
                if (in_array($request->user()->role , ['superviseur', 'resp_sup', 'resp_op'])) {
                    $searchArray[] = " eq.recruiting = 1";
                    $searchArray[] = " eq.employe_id is null";
                    $searchArray[] = " eq.user_id = " . $request->user()->id;
                } else if (in_array($request->user()->role , ['tenue','validateur'])) {
                    $searchArray[] = " eq.recruiting = 1";
                    $searchArray[] = " eq.employe_id is null";
                }
            }
            if($request->pour_validation) {
                if (in_array($request->user()->role , ['tenue','validateur'])) {
                    $searchArray[] = " eq.isValidated = 0";
                    $searchArray[] = " eq.`status` != 'done'";
                    $searchArray[] = " aeq.article = 'botte_de_pluie'";
                }
            }
            if($request->valider){
                if (in_array($request->user()->role , ['tenue','validateur'])) {
                    $searchArray[] = " eq.isValidated = 1";
                    $searchArray[] = " eq.`status` != 'done'";
                }
            }
            if($request->status) {
                if(in_array($request->user()->role, ['tenue', 'achat']) && $request->status == "demande")
                    $searchArray[] = "(stat.name = 'demande' or stat.name = 'traite') " ;
                else
                    $searchArray[] = "stat.name = '". $request->status ."'";
            }
            if($request->created_at)
                $searchArray[] = " eq.created_at > '" . $request->created_at . " 00:00:00' and ".
                    "eq.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " eq.user_id = " . $request->user_id . " ";
            if($request->employe_id)
                $searchArray[] = " eq.employe_id = " . $request->employe_id . " ";
            if($request->site_id)
                $searchArray[] = " eq.site_id = " . $request->site_id . " ";
            if($request->type_equipement)
                $searchArray[] = " teq.name = '" . $request->type_equipement . "' ";
        }

        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        if(in_array($request->user()->role, ['superviseur','room','rh','resp_rh','tech','electronique','simple','operation'])){
            $query_where = $query_where . " GROUP BY eq.id order by eq.id desc limit ". $request->offset . ", 30";
            $query_and = $query_and . " GROUP BY eq.id order by eq.id desc limit ". $request->offset . ", 30";
        }
        else if(!in_array($request->user()->role, ['tenue', 'achat'])){
            $query_where = $query_where . " order by eq.id desc limit ". $request->offset . ", 30";
            $query_and = $query_and . " order by eq.id desc limit ". $request->offset . ", 30";
        }
        return compact('query_where', 'query_and');
    }

    public function index(Request $request){
        $search = $this->search($request);
        $sql = '';
        if(in_array($request->user()->role, ['tenue', 'achat'])){
            $query_status = "";
            if(in_array($request->status, ["demande"]))
                $query_status = " and aeq.done is null ";
            else if($request->status)
                $query_status = " and stat.name = '". $request->status ."' ";
            if($request->last_id) {
                $articles = DB::select("SELECT aeq.id, aeq.equipement_id FROM article_equipements aeq
                LEFT JOIN articles a ON a.name = aeq.article
                LEFT JOIN equipements eq ON eq.id = aeq.equipement_id
                LEFT JOIN `status` stat on stat.name = eq.status
                LEFT JOIN type_equipements teq on teq.name = eq.type
                WHERE a.service = ? and aeq.equipement_id < ? ". $query_status . $search['query_and'] .
                "ORDER BY aeq.equipement_id DESC LIMIT 300
                ", [$request->user()->role, $request->last_id]);
            }
            else {
                $articles = DB::select("SELECT aeq.id, aeq.equipement_id FROM article_equipements aeq
                    LEFT JOIN articles a ON a.name = aeq.article
                    LEFT JOIN equipements eq ON eq.id = aeq.equipement_id
                    LEFT JOIN `status` stat on stat.name = eq.status
                    LEFT JOIN type_equipements teq on teq.name = eq.type
                    WHERE a.service = ? ". $query_status . $search['query_and'] .
                    "ORDER BY aeq.equipement_id DESC LIMIT 300
                    ", [$request->user()->role]);
            }
            $equipement_ids = array_unique(array_column($articles, 'equipement_id'));
            if(count($articles) > 0){
                $equipements = DB::select("SELECT eq.id, eq.type, eq.user_id, eq.employe_id, eq.site_id, eq.recruiting, eq.isValidated,
                    teq.designation as 'type_demande', teq.for_agent, eq.status, eq.created_at, eq.motif, eq.demande, eq.detail,
                    stat.description as 'status_description', stat.color as 'status_color',  eq.personal,
                    emp.nom as 'employe', emp.societe_id, emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi,
                    st.nom as 'site', u.name as 'user_nom', coalesce(ur.email, u.email) as 'user_email'
                    FROM equipements eq
                    LEFT JOIN type_equipements teq on teq.name = eq.type
                    LEFT JOIN employes emp on emp.id = eq.employe_id
                    LEFT JOIN sites st on st.idsite = eq.site_id
                    LEFT JOIN users u on u.id = eq.user_id
                    LEFT JOIN users ur on ur.id = u.real_email_id
                    LEFT JOIN `status` stat on stat.name = eq.status
                    WHERE eq.id in (" . implode(',', $equipement_ids) . ") order by eq.id desc"
                    , []);
            }
            else{
                $equipements = [];
            }
        }
        else if(in_array($request->user()->role, ['validateur', 'access','resp_sup'])){
                $equipements = DB::select("SELECT DISTINCT eq.id, eq.type, eq.user_id, eq.employe_id, eq.site_id, eq.recruiting, eq.isValidated,
                    teq.designation as 'type_demande', teq.for_agent, eq.status, eq.created_at, eq.motif, eq.demande, eq.detail,
                    stat.description as 'status_description', stat.color as 'status_color',  eq.personal,
                    emp.nom as 'employe', emp.societe_id, emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi,
                    st.nom as 'site', u.name as 'user_nom', coalesce(ur.email, u.email) as 'user_email'
                    FROM equipements eq
                    LEFT JOIN article_equipements aeq on aeq.equipement_id = eq.id
                    LEFT JOIN articles a ON a.name = aeq.article
                    LEFT JOIN type_equipements teq on teq.name = eq.type
                    LEFT JOIN employes emp on emp.id = eq.employe_id
                    LEFT JOIN sites st on st.idsite = eq.site_id
                    LEFT JOIN users u on u.id = eq.user_id
                    LEFT JOIN users ur on ur.id = u.real_email_id
                    LEFT JOIN `status` stat on stat.name = eq.status". $search['query_where']
                    , []);
        }
        else if (in_array($request->user()->role, ['resp_op'])) {
            $regions = RegionUsersController::getRegions($request);
            $equipements = DB::select("SELECT DISTINCT eq.id, eq.type, eq.user_id, eq.employe_id, eq.site_id, eq.recruiting, eq.isValidated,
            teq.designation as 'type_demande', teq.for_agent, eq.status, eq.created_at, eq.motif, eq.demande, eq.detail,
            stat.description as 'status_description', stat.color as 'status_color',  eq.personal,
            emp.nom as 'employe', emp.societe_id, emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi,
            st.nom as 'site', u.name as 'user_nom', coalesce(ur.email, u.email) as 'user_email'
            FROM equipements eq
            LEFT JOIN article_equipements aeq on aeq.equipement_id = eq.id
            LEFT JOIN articles a ON a.name = aeq.article
            LEFT JOIN type_equipements teq on teq.name = eq.type
            LEFT JOIN employes emp on emp.id = eq.employe_id
            LEFT JOIN sites st on st.idsite = eq.site_id
            LEFT JOIN users u on u.id = eq.user_id
            LEFT JOIN users ur on ur.id = u.real_email_id
            LEFT JOIN `status` stat on stat.name = eq.status
            WHERE st.group_pointage_id IN ($regions)". $search['query_and']
            , []);
        }
        else if(in_array($request->user()->role, ['superviseur','room','rh','resp_rh','tech','electronique','simple','operation'])){
            $equipements = DB::select("SELECT eq.id, eq.type, eq.user_id, eq.employe_id, eq.site_id, eq.recruiting, eq.isvalidated,
                teq.designation as 'type_demande', teq.for_agent, eq.status, eq.created_at, eq.motif, eq.demande, eq.detail,
                stat.description as 'status_description', stat.color as 'status_color',  eq.personal,
                emp.nom as 'employe', emp.societe_id, emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi,
                st.nom as 'site', u.name as 'user_nom', coalesce(ur.email, u.email) as 'user_email'
                FROM equipements eq
                LEFT JOIN article_equipements aeq on aeq.equipement_id = eq.id
                LEFT JOIN type_equipements teq on teq.name = eq.type
                LEFT JOIN articles a ON a.name = aeq.article
                LEFT JOIN employes emp on emp.id = eq.employe_id
                LEFT JOIN sites st on st.idsite = eq.site_id
                LEFT JOIN users u on u.id = eq.user_id
                LEFT JOIN users ur on ur.id = u.real_email_id
                LEFT JOIN `status` stat on stat.name = eq.status
                where eq.user_id = ? ". $search['query_and']
                , [$request->user()->id]);
            $sql = "SELECT eq.id, eq.type, eq.user_id, eq.employe_id, eq.site_id, eq.recruiting, eq.isvalidated,
                teq.designation as 'type_demande', teq.for_agent, eq.status, eq.created_at, eq.motif, eq.demande, eq.detail,
                stat.description as 'status_description', stat.color as 'status_color',  eq.personal,
                emp.nom as 'employe', emp.societe_id, emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi,
                st.nom as 'site', u.name as 'user_nom', coalesce(ur.email, u.email) as 'user_email'
                FROM equipements eq
                LEFT JOIN article_equipements aeq on aeq.equipement_id = eq.id
                LEFT JOIN type_equipements teq on teq.name = eq.type
                LEFT JOIN articles a ON a.name = aeq.article
                LEFT JOIN employes emp on emp.id = eq.employe_id
                LEFT JOIN sites st on st.idsite = eq.site_id
                LEFT JOIN users u on u.id = eq.user_id
                LEFT JOIN users ur on ur.id = u.real_email_id
                LEFT JOIN `status` stat on stat.name = eq.status
                where eq.user_id = ". $request->user()->id . $search['query_and'];
        }
        else
            return response(["error" => "EACCES"]);

        $ids = array_column($equipements, 'id');
        if(count($ids) > 0){
            $pieces = DB::select("SELECT id, equipement_id FROM piece_jointes WHERE equipement_id in (" . implode(",", array_column($equipements, "id")) . ")");
            foreach ($equipements as $item) {
                $nb_pj = 0;
                foreach ($pieces as $pj) {
                    if($item->id == $pj->equipement_id)
                        $nb_pj += 1;
                }
                $item->nb_pj = $nb_pj;
            }

            if(in_array($request->user()->role, ['tenue','achat'])){
                $articles = DB::select("SELECT aeq.equipement_id, aeq.article, aeq.done, ac.designation
                    FROM article_equipements aeq
                    LEFT JOIN articles ac ON ac.name = aeq.article
                    where ac.service = ? and aeq.equipement_id in (". implode(',', $ids) .")", [$request->user()->role]);
            }
            else
                $articles = DB::select("SELECT aeq.equipement_id, aeq.article, aeq.done, ac.designation
                    FROM article_equipements aeq
                    LEFT JOIN articles ac ON ac.name = aeq.article
                    where aeq.equipement_id in (". implode(',', $ids) .")", []);

            $eqs = [];
            foreach ($equipements as $eq) {
                $displays = [];
                foreach ($articles as $ac) {
                    if($eq->id == $ac->equipement_id)
                        $displays[] = [
                            'designation' => $ac->designation,
                            'done' => $ac->done,
                        ];
                }
                if(count($displays) > 0){
                    $eq->articles = array_column($displays, "designation");
                    $eqs[] = $eq;
                }
            }
            $equipements = $eqs;
        }
        return response(compact('equipements', 'sql'));
    }

    public function show($id, Request $request){
        $equipement = DB::select("SELECT eq.id, eq.type, eq.user_id, eq.employe_id, eq.site_id, eq.recruiting, eq.isValidated,
            teq.designation as 'type_demande', teq.for_agent, eq.status, eq.created_at, eq.motif, eq.demande, eq.detail,
            stat.description as 'status_description', stat.color as 'status_color',  eq.personal,
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
            st.nom as 'site', u.name as 'user_nom', coalesce(ur.email, u.email) as 'user_email'
            FROM equipements eq
            LEFT JOIN type_equipements teq on teq.name = eq.type
            LEFT JOIN employes a on a.id = eq.employe_id
            LEFT JOIN sites st on st.idsite = eq.site_id
            LEFT JOIN users u on u.id = eq.user_id
            LEFT JOIN users ur on ur.id = u.real_email_id
            LEFT JOIN `status` stat on stat.name = eq.status
            where eq.id = ?", [$id])[0];
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj
            where equipement_id = ?
            order by pj.created_at desc", [$id]);
        $equipement->nb_pj = count($pieces);
        if(in_array($request->user()->role, ['tenue','achat'])){
            $articles = DB::select("SELECT aeq.equipement_id, aeq.article, aeq.done, ac.designation, ac.name
                FROM article_equipements aeq
                LEFT JOIN articles ac ON ac.name = aeq.article
                where ac.service = ? and aeq.equipement_id = ?", [$request->user()->role, $id]);
        }
        else
            $articles = DB::select("SELECT aeq.equipement_id, aeq.article, aeq.done, ac.designation, ac.name
                FROM article_equipements aeq
                LEFT JOIN articles ac ON ac.name = aeq.article
                where aeq.equipement_id = ?", [$id]);

        $displays = [];
        foreach ($articles as $ac) {
            if($equipement->id == $ac->equipement_id)
                $displays[] = [
                    'designation' => $ac->designation,
                    'done' => $ac->done,
                    'name' => $ac->name,
                ];
        }
        if(count($displays) > 0)
            $equipement->articles = $displays;

        return response()->json($equipement);
    }

    public function article($id){
        $articles = DB::select("SELECT aeq.id, aeq.article, aeq.done, ac.designation, ac.service
            FROM article_equipements aeq
            LEFT JOIN articles ac ON ac.name = aeq.article
            where aeq.equipement_id = ?", [$id]);
        return response()->json($articles);
    }

    public function detail($id){
        $equipement = DB::select("SELECT eq.id, eq.type, eq.user_id, eq.employe_id, eq.site_id, eq.recruiting,
            teq.designation as 'type_demande', eq.status, eq.created_at, eq.motif, eq.demande, eq.detail,
            stat.description as 'status_description', stat.color as 'status_color', eq.personal,
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
            st.nom as 'site', sup.name as 'user_nom', sup.email as 'user_email'
            FROM equipements eq
            LEFT JOIN type_equipements teq on teq.name = eq.type
            LEFT JOIN employes a on a.id = eq.employe_id
            LEFT JOIN sites st on st.idsite = eq.site_id
            LEFT JOIN users sup on sup.id = eq.user_id
            LEFT JOIN `status` stat on stat.name = eq.status
            where eq.id = ?", [$id])[0];
        $articles = DB::select("SELECT aeq.id, aeq.article, aeq.done, ac.designation, ac.service
            FROM article_equipements aeq
            LEFT JOIN articles ac ON ac.name = aeq.article
            where aeq.equipement_id = ?", [$id]);
        $pieces = DB::select("SELECT pj.id, pj.nature, pj.path, pj.user_id, pj.created_at, pj.updated_at
            FROM piece_jointes pj
            where equipement_id = ?
            order by pj.created_at desc", [$id]);
        $historiques = DB::select("SELECT h.id, h.objet, h.note, h.detail, h.created_at, h.user
            FROM historiques h
            where h.equipement_id = ?
            order by h.created_at desc
            limit 10", [$id]);
        return response()->json(compact('equipement', 'articles', 'historiques', 'pieces'));
    }

    protected function validateAndSetEquipement($request, $equipement, &$items){
        $auth = $request->user();
        $equipement->employe_id = null;
        $equipement->site_id = null;
        $equipement->demande = null;
        $equipement->detail = null;
        if($request->type == "tenue"){
            if(!(
                $request->blouson ||
                $request->casquette ||
                $request->ceinture ||
                $request->rangers ||
                $request->chemise ||
                $request->pantalon ||
                $request->impermeable ||
                $request->sifflet ||
                $request->tshirt ||
                $request->botte_de_pluie
            ))
                return ["error" => "Vous devez coché au moins un élément"];
            if($request->me){
                $validator = Validator::make($request->all(), [
                    'motif' => 'required',
                ]);
                $equipement->personal = true;
                $equipement->recruiting = false;
            }
            else if($request->recruiting && in_array($request->user()->role, ["resp_sup", "resp_op"])){
                $validator = Validator::make($request->all(), [
                    'site_id' => 'required',
                    'motif' => 'required',
                ]);
                $equipement->personal = false;
                $equipement->recruiting = true;
                $equipement->site_id = $request->site_id;
            }
            else {
                $validator = Validator::make($request->all(), [
                    'employe_id' => 'required',
                    'motif' => 'required',
                ]);
                $equipement->personal = false;
                $equipement->recruiting = false;
                $equipement->employe_id = $request->employe_id;
                $equipement->site_id = Employe::find($request->employe_id)->real_site_id;
            }
            if($request->blouson) $items[] = ['article' => 'blouson'];
            if($request->casquette) $items[] = ['article' => 'casquette'];
            if($request->ceinture) $items[] = ['article' => 'ceinture'];
            if($request->rangers) $items[] = ['article' => 'rangers'];
            if($request->chemise) $items[] = ['article' => 'chemise'];
            if($request->pantalon) $items[] = ['article' => 'pantalon'];
            if($request->impermeable) $items[] = ['article' => 'impermeable'];
            if($request->sifflet) $items[] = ['article' => 'sifflet'];
            if($request->tshirt) $items[] = ['article' => 'tshirt'];
            if($request->botte_de_pluie) $items[] = ['article' => 'botte_de_pluie'];
        }
        else if($request->type == "guerite"){
            $validator = Validator::make($request->all(), [
                'site_id' => 'required',
                'motif' => 'required',
            ]);
            $items[] = ['article' => 'guerite'];
            $equipement->site_id = $request->site_id;
        }
        else if($request->type == "tonfa"){
            if(!($request->tonfa || $request->porte_tonfa))
                return ["error" => "Vous devez coché au moin un élément"];
            $validator = Validator::make($request->all(), [
                'site_id' => 'required',
                'motif' => 'required',
            ]);
            if($request->tonfa) $items[] = ['article' => 'tonfa'];
            if($request->porte_tonfa) $items[] = ['article' => 'porte_tonfa'];
            $equipement->site_id = $request->site_id;
        }
        else if($request->type == "torche"){
            if(!($request->torche || $request->pile))
                return ["error" => "Vous devez coché au moin un élément"];
            $validator = Validator::make($request->all(), [
                'site_id' => 'required',
                'motif' => 'required',
            ]);
            if($request->torche) $items[] = ['article' => 'torche'];
            if($request->pile) $items[] = ['article' => 'pile'];
            $equipement->site_id = $request->site_id;
        }
        else if($request->type == "chargeur"){
            $validator = Validator::make($request->all(), [
                'site_id' => 'required',
                'motif' => 'required',
            ]);
            $items[] = ['article' => 'chargeur'];
            $equipement->site_id = $request->site_id;
        }
        else if($request->type == "epi"){
            if(!(
                $request->casque_securite ||
                $request->chaussure_securite ||
                $request->gant_protection ||
                $request->lunette_protection ||
                $request->protection_oreille ||
                $request->protection_respiratoire ||
                $request->combinaison
            ))
                return ["error" => "Vous devez coché au moins un élément"];
                if($request->me){
                    $validator = Validator::make($request->all(), [
                        'motif' => 'required',
                    ]);
                    $equipement->personal = true;
                    $equipement->recruiting = false;
                }
                else if($request->recruiting){
                    $validator = Validator::make($request->all(), [
                        'site_id' => 'required',
                        'motif' => 'required',
                    ]);
                    $equipement->personal = false;
                    $equipement->recruiting = true;
                    $equipement->site_id = $request->site_id;
                }
                else {
                    $validator = Validator::make($request->all(), [
                        'employe_id' => 'required',
                        'motif' => 'required',
                    ]);
                    $equipement->personal = false;
                    $equipement->recruiting = false;
                    $equipement->employe_id = $request->employe_id;
                    $equipement->site_id = Employe::find($request->employe_id)->real_site_id;
                }
            if($request->casque_securite) $items[] = ['article' => 'casque_securite'];
            if($request->chaussure_securite) $items[] = ['article' => 'chaussure_securite'];
            if($request->gant_protection) $items[] = ['article' => 'gant_protection'];
            if($request->lunette_protection) $items[] = ['article' => 'lunette_protection'];
            if($request->protection_oreille) $items[] = ['article' => 'protection_oreille'];
            if($request->protection_respiratoire) $items[] = ['article' => 'protection_respiratoire'];
            if($request->combinaison) $items[] = ['article' => 'combinaison'];
        }
        else if($request->type == "fourniture_bureau"){
            if($request->me){
                $validator = Validator::make($request->all(), [
                    'motif' => 'required',
                    'detail' => 'required',
                ]);
                $equipement->personal = true;
            }
            else {
                $validator = Validator::make($request->all(), [
                    'site_id' => 'required',
                    'motif' => 'required',
                    'detail' => 'required',
                ]);
                $equipement->personal = false;
                $equipement->site_id = $request->site_id;
            }
            $items[] = ['article' => 'fourniture_bureau'];
            $equipement->detail = $request->detail;
        }
        else if($request->type == "fourniture_entretien"){
            if($request->me){
                $validator = Validator::make($request->all(), [
                    'motif' => 'required',
                    'detail' => 'required',
                ]);
                $equipement->personal = true;
            }
            else {
                $validator = Validator::make($request->all(), [
                    'site_id' => 'required',
                    'motif' => 'required',
                    'detail' => 'required',
                ]);
                $equipement->personal = false;
                $equipement->site_id = $request->site_id;
            }
            $items[] = ['article' => 'fourniture_entretien'];
            $equipement->detail = $request->detail;
        }
        else if ($request->type == "impermeable") {
            $validator = Validator::make($request->all(), [
                'site_id' => 'required',
                'qte' => 'required',
                'motif' => 'required',
            ]);
            $equipement->site_id = $request->site_id;
            $equipement->qte = $request->qte;
            $items[] = ['article' => 'impermeable'];
        }
        else if($request->type == "other"){
            if($request->me){
                $validator = Validator::make($request->all(), [
                    'motif' => 'required',
                    'demande' => 'required',
                ]);
                $equipement->personal = true;
            }
            else {
                $validator = Validator::make($request->all(), [
                    'site_id' => 'required',
                    'demande' => 'required',
                    'motif' => 'required',
                ]);
                $equipement->personal = false;
                $equipement->site_id = $request->site_id;
            }
            $items[] = ['article' => 'other'];
            $equipement->demande = $request->demande;
        }
        $equipement->type = $request->type;
        $equipement->motif = $request->motif;
        $equipement->user_id = $request->user()->id;
        if($validator->fails())
            return ['error' => $validator->errors()->first()];
        return ['error' => ''];
    }

    public function saveEquipement($request){
        $equipement = new Equipement();
        $items = [];
        $validator = $this->validateAndSetEquipement($request, $equipement, $items);
        if($validator['error'])
            return $validator;

        $equipement->status = 'demande';
        $equipement->created_at = new \DateTime();
        $equipement->updated_at = new \DateTime();
        $equipement->employe_id = $request->employe_id;
        if($equipement->save()){
            for ($i=0; $i < count($items); $i++) {
                $items[$i]['equipement_id'] = $equipement->id;
            }
            DB::table('article_equipements')->insert($items);
            HistoriqueController::new_equipement($request, $equipement);
            $roles = array_column(
                DB::select("SELECT DISTINCT ac.service FROM article_equipements aeq
                    LEFT JOIN articles ac ON ac.name = aeq.article
                    WHERE aeq.equipement_id = ?"
                , [$equipement->id])
            , 'service');
            $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'
                from users us
                left join users ur on ur.id=us.real_email_id
                where us.role in ('". implode("','", $roles) ."')", []);
            $emails = [];
            foreach ($users as $u) {
                $emails[] = ['address' => $u->email, 'name' => $u->name];
            }
            if($request->type == 'guerite'){
                $emails[] = ['address' => '<EMAIL>', 'name' => 'Idealy'];
                $emails[] = ['address' => '<EMAIL>', 'name' => 'Tsiory'];
            }
            //MailController::equipement($request, $equipement->id, "Demande d'équipement", $emails);
            return $equipement;
        }
    }

    public function store(Request $request){
        $names = array_column(TypeEquipement::select('name')->get()->toArray(), 'name');
        $auth = $request->user();

        if($request->for_agent) {
            $equipements = [];
            if(!$request->recruiting && count($request->employe_ids) > 0){
                foreach ($request->employe_ids as $employe_id) {
                    $request->merge(['employe_id' => $employe_id]);
                    $equipement = $this->saveEquipement($request);
                    if($equipement['error'])
                        return response($equipement);
                    $equipements[] = $equipement;
                }
                if(count($equipements) == 1)
                    return response(["success" => "Equipement bien envoyé", "id" => $equipements[0]->id]);
                else
                    return response(["success" => "Equipement bien envoyé"]);
            }
            else if($request->recruiting){
                $nb = 0;
                while ($nb < $request->nb_employe) {
                    $request->merge(['employe_id' => null]);
                    $equipement = $this->saveEquipement($request);
                    if($equipement['error'])
                        return response($equipement);
                    $equipements[] = $equipement;
                    $nb++;
                }
                if(count($equipements) == 1)
                    return response(["success" => "Equipement bien envoyé", "id" => $equipements[0]->id]);
                else
                    return response(["success" => "Equipement bien envoyé"]);
            }
            else
                return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        else if($request->type == "impermeable") {
            $nb = 0;
            while ($nb < $request->qte) {
                $equipement = $this->saveEquipement($request);
                if($equipement['error'])
                    return response($equipement);
                $equipements[] = $equipement;
                $nb++;
            }
            if(count($equipements) == 1)
                return response(["success" => "Equipement bien envoyé", "id" => $equipements[0]->id]);
            else
                return response(["success" => "Equipement bien envoyé"]);
        }
        else {
            $equipement = $this->saveEquipement($request);
            if($equipement['error'])
                return response($equipement);
            $equipements[] = $equipement;
            return response(["success" => "Equipement bien envoyé", "id" => $equipement->id]);
        }
    }

    public function transfer(Request $request, $id){
        $auth = $request->user();
        $equipement = Equipement::find($id);
        if ($equipement->status == "traite") {
            $nb_article = DB::select("SELECT count(aeq.id) as 'nb_article' FROM article_equipements aeq
                WHERE aeq.done = 1 and aeq.equipement_id = ?", [$equipement->id])[0];
            if ($nb_article->nb_article > 0)
                return response(['error' => 'Action non permise, au moins un article a été terminé.']);
        }
        if ((in_array($auth->role, ['tenue', 'achat']) || $equipement->user_id == $auth->id)
            && in_array($equipement->status, ['traite', 'demande'])) {
            if ($request->to =="flotte" ) {
                $flotte = new Flotte();
                $flotte->site_id = $equipement->site_id;
                $flotte->objet = $equipement->demande . " " . $equipement->detail;
                $flotte->status = "demande";
                $flotte->personal = $equipement->personal;
                $flotte->commentaire = $equipement->motif;
                $flotte->user_id = $equipement->user_id;
                $flotte->created_at = new \DateTime();
                $flotte->updated_at = new \DateTime();
                if ($flotte->save()) {
                    $request->merge(['site_id' => $flotte->site_id]);
                    $request->merge(['objet' => $flotte->objet]);
                    $request->merge(['commentaire' => $flotte->commentaire]);
                    $request->merge(['is_transfert' => true]);
                    HistoriqueController::new_flotte($request, $flotte->id);
                    $equipement->status = "draft";
                    $equipement->updated_at = new \DateTime();
                    $equipement->note_id = HistoriqueController::action_equipement($request, "Équipement transféré au flotte", $id);
                    $equipement->save();
                    $users = DB::select("SELECT us.id, us.name, coalesce(ur.email, us.email) as 'email'
                        from users us
                        left join users ur on ur.id=us.real_email_id
                        where us.role = 'tech'", []);
                    $notifications = [];
                    $historique_id = (Historique::where('flotte_id', $flotte->id)->first())->id;
                    foreach ($users as $u) {
                        $notifications[] =
                        [
                            'historique_id' => $historique_id,
                            'receiver_id' => $u->id,
                            'user_id' => $request->user()->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }
                    Notification::insert($notifications);
                    return response(["succes" => "Transférer au flotte", "id" => $equipement->id]);
                }
            }
            else if ($request->to == "sav") {
                if($equipement->site_id){
                    $sav = new Sav();
                    $demandeur = User::find($equipement->user_id);
                    $sav->type_sav =  "autre";
                    $sav->status = "demande";
                    $sav->motif = $equipement->motif . " (Demande d'équipement par " . $demandeur->name . " <" . $demandeur->email . "> , transféré au SAV)";
                    if ($demandeur->role == 'superviseur')
                        $sav->superviseur_id = $demandeur->id;

                    $sav->site_id = $equipement->site_id;
                    $sav->user_id = $equipement->user_id;
                    $sav->mesure = $equipement->demande ? $equipement->demande . "/" . $equipement->detail : $equipement->detail;
                    $sav->created_at = new \DateTime();
                    $sav->updated_at = new \DateTime();
                    if ($sav->save()) {
                        $request->merge(['motif' => $sav->motif]);
                        $request->merge(['site_id' => $sav->site_id]);
                        $request->merge(['is_tranfert' => true]);
                        $h = HistoriqueController::new_sav($request, $sav->id);
                        $equipement->status = "draft";
                        $equipement->updated_at = new \DateTime();
                        $equipement->note_id = HistoriqueController::action_equipement($request, "Équipement transféré au SAV", $id);
                        $equipement->save();
                        $users = DB::select("SELECT us.id, us.name, coalesce(ur.email, us.email) as 'email'
                            from users us
                            left join users ur on ur.id = us.real_email_id
                            where us.role in ('electronique', 'tech')", []);
                        $historique_id = (Historique::where('flotte_id', $sav->id)->first())->id;
                        foreach ($users as $u) {
                            $notifications[] =
                            [
                                'historique_id' => $sav->id,
                                'receiver_id' => $u->id,
                                'user_id' => $request->user()->id,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                        }
                        Notification::insert($notifications);
                        return response(["succes" => "Transféré au SAV", "id" => $sav->id, "type" => $sav->type_sav]);
                    }
                }
                return response(["error" => "Le site est réquis pour le SAV"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function set_employe(Request $request, $id){
        $type_equipement = $request->input('type');
        $equipement = Equipement::find($id);
        if(
            !$equipement->employe_id && $equipement->recruiting &&
            $request->user()->id == $equipement->user_id &&
            $equipement->status != "done"
        ){
            $validator = Validator::make($request->all(), [
                'employe_id' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $equipement->employe_id = $request->employe_id;
            $equipement->recruiting = false;
            $equipement->updated_at = new \DateTime();
            $request->merge([
                "motif" => $equipement->motif,
                "site_id" => $equipement->site_id,
            ]);
            if($equipement->save() && $type_equipement == "tenue") {
                $articles = DB::select("SELECT ac.name FROM article_equipements aeq
                LEFT JOIN articles ac ON ac.name = aeq.article
                WHERE aeq.equipement_id = ?", [$id]);
                $selected_articles = array_map(function($article) { return $article->name; }, $articles);
                MouvementEquipementController::new_dotation("nouveau", "sortie", Carbon::now()->format('Y-m-d'), $selected_articles, $request->employe_id, $request->user()->id);
                $equipement->status = 'done';
                $equipement->save();
                foreach($articles as $article) {
                      DB::update("UPDATE article_equipements SET done = 1 WHERE article = ? AND equipement_id = ?", [$article->name, $id]);
                }
                HistoriqueController::update_equipement($request, $equipement, "Mention de l'employe");
                return response(["success" => "Employe mentionné avec succès", "id" => $equipement->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $equipement = Equipement::find($id);
        $old_equipement = clone $equipement;
        $names = array_column(TypeEquipement::select('name')->get()->toArray(), 'name');
        $auth = $request->user();

        if($request->user()->id == $equipement->user_id && $equipement->status == "draft"){
            $items = [];
            $validator = $this->validateAndSetEquipement($request, $equipement, $items);
            if($validator['error'])
                return response($validator);

            $equipement->status = 'demande';
            $equipement->updated_at = new \DateTime();
            if($equipement->save()){
                ArticleEquipement::where('equipement_id', $equipement->id)->delete();
                for ($i=0; $i < count($items); $i++) {
                    $items[$i]['equipement_id'] = $equipement->id;
                }
                DB::table('article_equipements')->insert($items);
                HistoriqueController::update_equipement($request, $old_equipement, "Renvoie de la demande");
                $roles = array_column(
                    DB::select("SELECT DISTINCT ac.service FROM article_equipements aeq
                        LEFT JOIN articles ac ON ac.name = aeq.article
                        WHERE aeq.equipement_id = ?"
                    , [$equipement->id])
                , 'service');
                $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'
                    from users us
                    left join users ur on ur.id=us.real_email_id
                    where us.role in  ('". implode("','", $roles) ."')", []);
                $emails = [];
                foreach ($users as $u) {
                    $emails[] = ['address' => $u->email, 'name' => $u->name];
                }
                //MailController::equipement($request, $equipement->id, "Renvoie de la demande d'équipement", $emails);
                return response(["success" => "Renvoie de la demande réussi", "id" => $equipement->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function note(Request $request, $id){
        $equipement = Equipement::find($id);
        if(
            ($request->user()->id == $equipement->user_id && in_array($equipement->status, ['demande'])) ||
            (in_array($request->user()->role, ['tenue', 'achat']) && in_array($equipement->status, ['demande', 'traite']))
        ){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            if(in_array($request->user()->role, ['tenue', 'achat']))
                $equipement->status = "traite";
            $equipement->updated_at = new \DateTime();
            $equipement->note_id = HistoriqueController::action_equipement($request, "Note", $id);
            if($equipement->save())
                return response(["success" => "Accusé de reception", "id" => $equipement->id]);
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_equipement(Request $request, $id){
        $equipement = Equipement::find($id);
        if(
            ($request->user()->id == $equipement->user_id && in_array($equipement->status, ['demande'])) ||
            (in_array($request->user()->role, ['tenue', 'achat']) && in_array($equipement->status, ['demande', 'traite']))
        ){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            if(in_array($request->user()->role, ['tenue', 'achat']) && in_array($equipement->status, ['traite'])){
                $nb_article = DB::select("SELECT count(aeq.id) as 'nb_article' FROM article_equipements aeq
                    WHERE aeq.done = 1 and aeq.equipement_id = ?"
                , [$equipement->id])[0]['nb_article'];
                if($nb_article > 0)
                    return response(['error' => 'Action non permise, au moins un article a été terminé.']);
            }

            $equipement->status = 'draft';
            $equipement->updated_at = new \DateTime();

            $equipement->note_id = HistoriqueController::action_equipement($request, "Demande annulé", $id);
            if($equipement->save()){
                ArticleEquipement::where("equipement_id", $equipement->id)->whereNull("done")->update(['done' => 0]);
                if(in_array($request->user()->role, ["superviseur", 'resp_sup', 'resp_op'])){
                    $roles = array_column(
                        DB::select("SELECT DISTINCT ac.service FROM article_equipements aeq
                            LEFT JOIN articles ac ON ac.name = aeq.article
                            WHERE aeq.equipement_id = ?"
                        , [$equipement->id])
                    , 'service');
                    $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'
                        from users us
                        left join users ur on ur.id=us.real_email_id
                        where us.role in ('". implode("','", $roles) ."')", []);
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                }
                else {
                    $superviseur =  DB::select("SELECT u.name, coalesce(u2.email, u.email) as 'email' from users u
                        left join users u2 on u2.id = u.real_email_id
                        WHERE u.id = ?", [$equipement->superviseur_id])[0];
                }
                return response(["success" => "Demande 'équipement annulé", "id" => $equipement->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function request_validation(Request $request, $id){
        $articles = DB::select('SELECT aeq.id from article_equipements aeq
            left join articles ac on ac.name = aeq.article
            where aeq.done is null and aeq.equipement_id = ? and ac.service = ?', [$id, $request->user()->role]);
        $equipement = Equipement::find($id);
        if(count($articles) != 0 && in_array($equipement->status, ["demande", "traite"])) {
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $equipement->status = 'validation';
            $equipement->updated_at = new \DateTime();

            $equipement->note_id = HistoriqueController::action_equipement($request, "Requête de validation", $id);
            if($equipement->save()){
                return response(["success" => "Demande de validation envoyé", "id" => $equipement->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }


    public function cancel_validation(Request $request, $id){
        $articles = DB::select('SELECT aeq.id from article_equipements aeq
            left join articles ac on ac.name = aeq.article
            where aeq.done is null and aeq.equipement_id = ? and ac.service = ?', [$id, $request->user()->role]);
        $equipement = Equipement::find($id);
        if(count($articles) != 0 && $equipement->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $equipement->status = 'traite';
            $equipement->updated_at = new \DateTime();

            $equipement->note_id = HistoriqueController::action_equipement($request, "Demande de validation annulé", $id);
            if($equipement->save()){
                return response(["success" => "Demande de validation annulé", "id" => $equipement->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function reply_validation(Request $request, $id){
        $equipement = Equipement::find($id);
        if($request->user()->role == 'validateur' && $equipement->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $equipement->status = 'traite';
            $equipement->updated_at = new \DateTime();

            $equipement->note_id = HistoriqueController::action_equipement($request, "Réponse à la demande", $id);
            if($equipement->save()){
                $roles = array_column(
                    DB::select("SELECT DISTINCT ac.service FROM article_equipements aeq
                        LEFT JOIN articles ac ON ac.name = aeq.article
                        WHERE aeq.equipement_id = ?"
                    , [$equipement->id])
                , 'service');
                if($request->type == 'guerite')
                    $users = DB::select("SELECT us.id from users us where us.email in ('<EMAIL>', '<EMAIL>') or us.role in ('". implode("','", $roles) ."')", []);
                else
                    $users = DB::select("SELECT us.id from users us where us.role in ('". implode("','", $roles) ."')", []);
                foreach ($users as $u) {
                    Notification::create([
                        'historique_id' => $equipement->note_id,
                        'receiver_id' => $u->id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                return response(["success" => "Réponse de la demande envoyé", "id" => $equipement->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function validate_equipement(Request $request, $id){
        $equipement = Equipement::find($id);
        if($request->user()->role == 'validateur' && $equipement->status == 'demande'){
            $equipement->isValidated = 1;
            $equipement->updated_at = new \DateTime();
            $request->note = "Commentaire: " . $request->commentaire;
            $equipement->note_id = HistoriqueController::action_equipement($request, "Demande validée", $id);
            if($equipement->save()){
                return response(["success" => "Demande validée"]);
            }
        }
    }

    public function refuse_equipement(Request $request, $id) {
        $equipement = Equipement::find($id);

        if (!$equipement) {
            return response()->json(["error" => "Équipement introuvable"], 404);
        }

        if ($request->user()->role == 'validateur' && $equipement->status == 'demande') {
            ArticleEquipement::where('equipement_id', $equipement->id)
                             ->where("article", "botte_de_pluie")
                             ->delete();

            if (ArticleEquipement::where('equipement_id', $equipement->id)->count() > 0) {
                $request->merge(['note' => "Commentaire: " . $request->commentaire]);
                $equipement->note_id = HistoriqueController::action_equipement($request, "Demande de botte de pluie refusée", $id);
                $equipement->save();

                return response()->json(["success" => "Demande refusée, article supprimé"]);
            } else {
                $equipement->delete();
                return response()->json(["success" => "Demande refusée, demande d'équipement supprimée"]);
            }
        }
        return response(["error" => "EACCES"]);
    }
}
