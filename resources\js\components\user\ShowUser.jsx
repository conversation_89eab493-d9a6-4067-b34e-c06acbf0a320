import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import useToken from '../util/useToken';
import ActionUser from './ActionUser';
import LoadingScreen from '../loading/LoadingScreen';
import ShowHeader from '../view/ShowHeader';
import Tab from '../layout/Tab';

export default function ShowUser({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [user, setUser] = useState(null)

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/user/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setUser(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(user)
    }, [user]);

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingScreen/>
        :
            <div>
                {
                    user &&
                    <>
                        <ShowHeader size={size} label="Sanction" id={user.id} closeDetail={() => setCurrentId()}/>
                        <div className="card-container">
                            <div className='badge-container'>
                                {
                                    user.blocked ?
                                        <span>
                                            <span className="badge-outline badge-outline-pink">
                                                désactivé
                                            </span> 
                                        </span>
                                    :
                                        <span>
                                            <span className={'badge-outline ' + (user.type == "fictif" ? "badge-outline-purple" : user.type == "unique" ? "badge-outline-green" : "")}>
                                                {user.type}
                                            </span> 
                                        </span>

                                }
                            </div>
                            <h3>{user.name}</h3>
                            <p style={{whiteSpace: "pre-line"}}>
                                Email: <span className='text'>{user.email}</span>
                            </p>
                            <p style={{whiteSpace: "pre-line"}}>
                                Mot de passe mail: <span className='text'>{user.email_password}</span>
                            </p>
                            <p style={{whiteSpace: "pre-line"}}>
                                Role: <span className='text'>{user.role}</span>
                            </p>
                            {user.flotte &&
                                <p style={{ whiteSpace: "pre-line" }}>
                                    Flotte: <span className='text'>{user.flotte}</span>
                                </p>
                            }
                                
                            <div className='card-header'>
                                <ActionUser auth={auth} user={user} updateData={updateData}/>
                            </div>
                        </div>
                        <Tab auth={auth} name="user_id" value={user.id} updateData={updateData}/>
                    </>
                }
            </div>
    } </>
}