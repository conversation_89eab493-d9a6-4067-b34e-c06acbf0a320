import React, { useState } from 'react'
import useToken from '../util/useToken';
import { upperCase } from 'lodash';

export default function TransferModal({ equipement, updateData, closeModal}) {
    const destinataireList = ["flotte", "sav"]
    const [error, setError] = useState("")

    const handleSubmit = (item) => {
        axios.post('/api/equipement/transfer/' + equipement.id, {to:item}, useToken())
        .then((res)=>{
            console.error(res.data.error);
            if (res.data.error) {
                setError(res.data.error);
            }
            else {
                closeModal();
                updateData();
            }
        })
    }

    return (
        <div className='modal'>
            <div>
                <h2>Transfért</h2>
                {
                    destinataireList.map((item) => {
                        return (
                            <div className='table line-container' key={item} onClick={() => {handleSubmit(item)}}>
                                <span>{upperCase(item)}</span>
                            </div>
                        )
                    })
                }
                {error && <div className='container-error'>{error}</div>}
                <div className='form-button-container'>
                    <button type='button' onClick={() => closeModal()}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
