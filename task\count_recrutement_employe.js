const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const {db_config_admin} = require("../auth")
const pool_admin = mysql.createPool(db_config_admin)

const sqlSelectEmploye = "SELECT id, nom as 'employe', cin_text FROM employes WHERE cin_text is not null"
const sqlSelectRecrutement = "SELECT id, nom as 'recrutement', cin_text FROM recrutements"

function checkLastDatePointage(pointages, index){
    if(index < pointages.length) {
        const currentPointage = pointages[index]
        pool_admin.query(sqlSelectAgent, [currentPointage.agent_id], async (err, agents) => {
            if(err)
                console.error(err)
            else {
                const currentAgent = agents[0]
                const lastDateFromPointage = moment(currentPointage.max_date_pointage).format("YY-MM-DD HH:mm:ss")
                const lastDateFromAgent = moment(currentAgent.last_date_pointage).format("YY-MM-DD HH:mm:ss")
                if(lastDateFromPointage != lastDateFromAgent){
                    console.log(currentAgent.id + " : " + lastDateFromPointage + " != " + lastDateFromAgent)
                    pool_admin.query(sqlUpdateAgent, [currentPointage.max_date_pointage, currentPointage.agent_id], async (err, res) => {
                        if(err)
                            console.error(err)
                        else 
                            setTimeout(() => checkLastDatePointage(pointages, index+1), 100)
                    })
                }
                else
                    setTimeout(() => checkLastDatePointage(pointages, index+1), 100)
            }
        })
    }
    else 
        console.log("check done ...")
}

pool_admin.query(sqlSelectEmploye, [], async (err, employes) => {
    if(err)
        console.error(err)
    else {
        console.log("Nb employe: " + employes.length)
        pool_admin.query(sqlSelectRecrutement, [], async (err, recrutements) => {
            if(err)
                console.error(err)
            else {
                console.log("Nb recrutement: " + recrutements.length)
                employes.forEach(e => {
                    recrutements.forEach(r => {
                        if(e.cin_text == r.cin_text) {
                            console.log(e.employe + " == " + r.recrutement)
                        }
                    })
                })
            }
        })
    }
})