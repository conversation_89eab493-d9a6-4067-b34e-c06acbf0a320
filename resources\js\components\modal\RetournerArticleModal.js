import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import InputDate from '../input/InputDate';
import moment from 'moment';

export default function RetournerArticleModal ({action, updateData, closeModal}) {
    const [dateDeRetour, setDateRetour] = useState(null);
    const [submitDisabled, setSubmitDisabled] = useState(true);

    const handleOk = () => {
        const data = {
            date_mouvement: moment(dateDeRetour).format('YYYY-MM-DD')
        }
        if (action) {
            axios.post(action.request, data, useToken())
            .then(res => {
                closeModal()
                if (updateData) updateData()
            })
            .catch((e) => {
                console.error(e)
            })
        }
    }

    useEffect(() => {
        setSubmitDisabled(!(dateDeRetour))
    }, [dateDeRetour]);

    return <div className='modal'>
        <div>
            <h2>Retour d'un article</h2>
            <InputDate
                required
                label="Date de retour"
                value={dateDeRetour}
                onChange={setDateRetour}
            />
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Enregistrer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
}
