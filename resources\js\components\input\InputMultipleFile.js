import { useRef, useState } from 'react';
import InfoModal from '../modal/InfoModal';
import LoadingScreen from '../loading/LoadingScreen';
import { AiOutlineDelete } from "react-icons/ai";

export default function InputMultipleFile({files, setFiles, hideInput, hideFile}) {
    const fileInputRef = useRef(null);
    // const [uploadedFiles, setUploadedFiles] = useState([]);
    const [showInfo, toggleInfo] = useState(false)
    const [showLoading, toggleLoading] = useState(false)

    const deletePj = (currentIndex) => {
        const newFileList = []
        files.forEach((file, index) => {
            if(currentIndex != index) {
                newFileList.push(file)
            }
        })
        setFiles(newFileList)
    }

    const handleFileInputClick = () => {
        fileInputRef.current.click();
    }

    const uploadFile = (event) => {
        toggleLoading(true)
        const newFileList = [...files]
        Array.from(event.target.files).forEach(file => {
            if(file.size < 50*1024*1024){  
                newFileList.push(file)
            }
            else
                toggleInfo(true)
        })
        setTimeout(() => setFiles(newFileList), 200)
        toggleLoading(false)        
    }
    // useEffect(() => {
    //     toggleLoading(false)
    //     setFiles(uploadedFiles)
    // }, [uploadedFiles]);

    return <div className='input-container'>
        {
            showInfo &&
            <InfoModal msg="Fichier trop volumineux" closeModal={() => toggleInfo(false)}/>
        }
        {
            showLoading &&
            <LoadingScreen/>
        }
        <input 
            multiple 
            hidden
            type="file" 
            name="file"
            // accept=".jpg,.png,.pdf,.mp4"
            ref={fileInputRef}
            onChange={uploadFile}/>
        {
            !hideInput &&
            <div style={{ paddingTop: '10px', paddingBottom: '10px' }}>
                <div className='action-container'>
                    <span>
                        <span onClick={handleFileInputClick}>Ajouter une pièce jointe</span>
                    </span>
                </div>
            </div>
        }
        {
            !hideFile &&
            <div className='action-container'>
                {
                    files.map((file, index) => 
                        <div className='pj-file space-between' key={file.name}>
                            <span className='secondary'>{file.name}</span>
                            <span>
                                <AiOutlineDelete onClick={() => deletePj(index)}/>
                            </span>
                        </div>
                    )
                }
            </div>
        }
    </div>;
}