import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import InputSelect from '../input/InputSelect';
import InputUser from '../input/InputUser';
import InputEmploye from '../input/InputEmploye';

export default function EditPrime({auth, title, action}) {
    const params = useParams()
    const [name, setName] = useState('')
    const [email, setEmail] = useState('')
    const [employe, setEmploye] = useState(null)
    const [type, setType] = useState('')
    const [user, setUser] = useState(null)
    const [password, setPassword] = useState('')
    const [role, setRole] = useState('')
    const [emailPassword, setEmailPassword] = useState('')
    const [mustChangePassword, setMustChangePassword] = useState('')
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    const [flotte, setFlotte] = useState()
    const [services, setServices] = useState([])
    const [service, setService] = useState()

    const handleChangeFlotte = (value) => {
        if (!/^\d*$/.test(value) || value.length > 10) {
            return;
        }
        setFlotte(value);
    };
    
    const roleList = [
        'admin',
        'room',
        'resp_rh' ,
        'rh',
        'superviseur',
        'tech',
        'resp_sup',
        'resp_op',
        'tenue',
        'simple',
        'achat',
        'daf',
        'validateur',
        'access',
        'electronique',
        'compta',
        'juridique'
    ]

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const data = {
            type: type,
            name: name,
            email: email,
            password: password,
            real_email_id: user ? user.id : '',
            role: role,
            email_password: emailPassword,
            must_change_password: mustChangePassword,
            employe_id: employe ? employe.id : null,
            flotte: flotte,
            service_id: service ? service.id : null,
        }
        axios.post(action + (params.id ? "/" + params.id : ""), data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setNotification({success: "Une erreur est survenue."})
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    useEffect(() => {
        let isMounted = true; 
        axios.get('/api/user/service', useToken())
        .then((res)=>{
            if (isMounted) {
                if (res.data.services) {
                    let reformatServices = res.data.services.map(sc => {
                        return {
                            designation : sc.designation,
                            id : sc.id,
                            label : sc.designation,
                            name : sc.name,
                            value: String(sc.id),
                        }
                    })
                    setServices(reformatServices)
                }
            }
            else if (res.data.error) {
                setError(res.data.error)
            }
        }).catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
        return () => isMounted == false
    }, [])

    useEffect(() => {
        let isMounted = true
        if(params.id){
            axios.get('/api/user/show/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    const user = res.data
                    if(user.name) setName(user.name)
                    if(user.email) setEmail(user.email)
                    if(user.password) setPassword(user.password)
                    if(user.role) setRole(user.role)
                    if(user.type) setType(user.type)
                    if(user.email_password) setEmailPassword(user.email_password)
                    if(user.must_change_password) setMustChangePassword(user.must_change_password)
                    if(user.parent_name) 
                        setUser({
                            id: user.real_email_id,
                            name: user.parent_name, 
                            email: user.parent_email
                        })
                    if(user.employe){
                        const emp = user.employe
                        emp.matricule = matricule(emp)
                        setEmploye(emp)
                    }
                    if (user.flotte) setFlotte(user.flotte)
                    if (user.service_id) {
                        setService({
                            id: user.service_id,
                            value: String(user.service_id),
                            name: user.service_name,
                            designation: user.service_designation,
                            label: user.service_designation,
                        })
                    }
                }
            })
        }
        return () => { isMounted = false };
    }, [])

    return (
        <div id="content">
            {
                notification ? 
                    <Notification next="/user" message={notification.success}/>
                :
                <div>
                    <div className="title-container">
                        <h2>{title}</h2>
                    </div>
                    <div>                          
                        <form onSubmit={handleSubmit}>
                            <InputSelect 
                                label="Type de compte"
                                selected={type} setSelected={setType} options={["parent", "unique", "fictif"]}
                                required
                            />
                            {
                                ["unique", "fictif"].includes(type) &&
                                <InputEmploye value={employe} onChange={setEmploye} required withouDelete/>
                            }
                            <InputText 
                                label="Nom d'utilisateur"
                                value={name} 
                                onChange={setName}
                                required
                            />
                            <InputText 
                                label="Flotte"
                                value={flotte}
                                onChange={handleChangeFlotte}
                                max={10}
                                min={10}
                            />
                            <InputText 
                                label="Email"
                                value={email} 
                                onChange={setEmail}
                                required
                            />
                            {
                                type != "parent" &&
                                <InputSelect 
                                    label="Rôle"
                                    selected={role} setSelected={setRole} options={roleList}
                                    required
                                />
                            }
                            {
                                type != "fictif" &&
                                <InputText 
                                    label="Mot de passe email"
                                    value={emailPassword} 
                                    onChange={setEmailPassword}
                                    required
                                />
                            }
                            {
                                <InputSelect label="Service"
                                    selected={service}
                                    setSelected={setService}
                                    options={services}
                                    required
                                />
                            }
                            {
                                type == "fictif" &&
                                <InputUser
                                    role="parent" 
                                    required
                                    value={user} 
                                    onChange={setUser}/>
                                }
                                
                            {
                                error &&
                                <div className='container-error'>
                                    {error}
                                </div>
                                
                            }
                            <ButtonSubmit disabled={submitDisabled}/>
                        </form>       

                    </div>
                    
                </div>
            }
        </div>
    )
}