<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Employe;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\HistoriqueController;
use Illuminate\Support\Facades\DB;
use Hash;

class UserController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = "u.id = '". $request->id ."'";
        else {
            if($request->status)
                $searchArray[] = "u.must_change_password = '". $request->status ."'";
            if($request->created_at)
                $searchArray[] = " u.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "u.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->name)
                $searchArray[] = "u.name like '%". $request->name ."%'";
            if($request->email)
                $searchArray[] = "u.email like '%". $request->email ."%'";
            if($request->account)
                $searchArray[] = "(u.name like '%". $request->account ."%' or u.email like '%". $request->account ."%' or u2.email like '%". $request->account ."%')";
            if($request->matricule)
                $searchArray[] = " e.numero_stagiaire = " . $request->matricule . 
                    " or e.numero_employe = " . $request->matricule .
                    " or e.num_emp_soit = " . $request->matricule .
                    " or e.num_emp_saoi = " .$request->matricule .
                    " ";
            if($request->type_user){
                if (in_array($request->type_user,["unique", "fictif", "parent"])) {
                    $searchArray[] = " u.type = '". $request->type_user ."' and (u.blocked is null or u.blocked = 0)";
                }
                else 
                    $searchArray[] = " u.blocked = 1";
            }
        }

        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by id desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by id desc limit ". $request->offset . ", 30";

        return compact('query_where', 'query_and');
    }

    public function index(Request $request){
        $search = $this->search($request);
        $auth = $request->user();
        if(in_array($auth->role, ["admin"])){
            $users = DB::select("SELECT u.id, u.name, u.email, u.type, u.blocked, u.role, u.email_password, u.must_change_password, 
                u.created_at, u.updated_at 
                FROM users u 
                LEFT JOIN employes e ON e.id = u.employe_id
                LEFT JOIN users u2 ON u2.id = u.real_email_id" . $search['query_where'], []);
        }
        else
            return response(["error" => "EACCES"]);
        return response(compact('users'));
    }

    public function role(Request $request){
        if($request->user()){
            $roles = DB::select("SELECT DISTINCT role As 'name' FROM users 
                where role is not null AND role != '' AND role != 'validateur' ");
        }
        else
            return response(["error" => "EACCES"]);
        return response(compact('roles'));
    }

    public function get_all_role(Request $request){
        $role = $request->user()->role;
        if(in_array($request->user()->id, [1, 2, 221, 220])){
            $roles = DB::select("SELECT DISTINCT role As 'name' FROM users 
                where role is not null AND role != '' AND role != '$role' order by role ASC");
        }
        else
            return response(["error" => "EACCES"]);
        return response(compact('roles'));
    }
    
    public function get_all_roles(Request $request){
            $roles = DB::select("SELECT DISTINCT `role` As 'name' FROM users where `role` is not null order by `role` ASC");
        return response(compact('roles'));
    }
    
    public function edit_role(Request $request, $role){
        $auth = $request->user();
        if(in_array($auth->id, [1, 2, 220, 221])){
            $user = User::find($request->user()->id);
            $user->role = $role;
            if($user->save()){
                return response(["success" => "Rôle modifié"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function modal(Request $request){
        if($request->roles){
            $users =  DB::select("SELECT us.id, us.name, coalesce(ur.email, us.email) as 'email'  
                from users us
                left join users ur on ur.id = us.real_email_id 
                where (us.blocked is null or us.blocked = 0) and us.type in ('fictif', 'unique') and us.role in (" . $request->roles . ")", []);
            return response($users);
        }
        if($request->superviseur){
            $users =  DB::select("SELECT us.id, us.name, coalesce(ur.email, us.email) as 'email'  
                from users us
                left join users ur on ur.id = us.real_email_id 
                where (us.blocked is null or us.blocked = 0) and us.type in ('fictif', 'unique') and us.role in ('resp_sup', 'superviseur')", []);
        }
        else if($request->parent){
            $users =  DB::select("SELECT us.id, us.name, coalesce(ur.email, us.email) as 'email'  
                from users us
                left join users ur on ur.id = us.real_email_id 
                where (us.blocked is null or us.blocked = 0) and us.type = 'parent'", []);
        }
        else if($request->search){
            $users =  DB::select("SELECT us.id, us.name, coalesce(ur.email, us.email) as 'email'  
                from users us
                left join users ur on ur.id = us.real_email_id 
                where (us.blocked is null or us.blocked = 0) and us.type in ('fictif', 'unique') 
                and (us.name like ? or us.email like ? or ur.email like ?)", 
                    ["%" . $request->search . "%", "%" . $request->search . "%", "%" . $request->search . "%"]);
        }
        else if($request->resp_part){
            $employe_ids = DB::select("SELECT DISTINCT e.resp_part_id 
                FROM employes e 
                WHERE (e.soft_delete = 0 OR e.soft_delete is null) 
                AND e.part_variable = 1 
                AND e.resp_part_id is not null", []);
            $employe_ids = collect($employe_ids)->pluck('resp_part_id')->toArray();
            $users =  DB::select("SELECT us.id, us.name, us.employe_id, coalesce(ur.email, us.email) as 'email'  
                from users us
                left join users ur on ur.id = us.real_email_id 
                where (us.name like ? or us.email like ? or ur.email like ? ) and us.employe_id in (" . implode(",", $employe_ids) . ")",
                    ["%" . $request->search . "%", "%" . $request->search . "%", "%" . $request->search . "%"]);
        }
        else{
            $users =  DB::select("SELECT us.id, us.name, coalesce(ur.email, us.email) as 'email'  
                from users us
                left join users ur on ur.id = us.real_email_id
                where us.type in ('fictif', 'unique')", []);
        } 
        return response($users);
    }

    public function show(Request $request, $id){
        $auth = $request->user();
        $user = DB::select("SELECT u.id, u.name, u.email, u.type, u.blocked, u.role, u.email_password, u.must_change_password, 
            u.created_at, u.updated_at, u.real_email_id, u2.name as 'parent_name', u2.email as 'parent_email', u.employe_id, 
            u.flotte, sc.id as 'service_id', sc.name as 'service_name', sc.designation as 'service_designation'
            FROM users u
            left join users u2 on u2.id = u.real_email_id
            left join services sc on u.service_id = sc.id
            where u.id = ?", [$id])[0];
        if($user && $user->employe_id){
            $user->employe = Employe::find($user->employe_id);
        }

        return response()->json($user);
    }

    public function detail(Request $request,$id){
        $auth = $request->user();
        if(in_array($auth->role, ["admin"])){
            $user = DB::select("SELECT u.id, u.name, u.email, u.type, u.blocked, u.role, u.email_password, u.must_change_password, u.created_at, u.updated_at, u.real_email_id
                FROM users u
                where u.id = ?", [$id])[0];
            $historiques = DB::select("SELECT h.id, h.objet, h.note, h.detail, h.created_at, h.user
                FROM historiques h
                where h.user_id = ?
                order by h.created_at desc
                limit 10", [$id]);
            return response()->json(compact('user', 'historiques'));
        }
        return response(["error" => "EACCES"]);
    }

    protected function validateAndSetUser($request, $user){
        if($user->id){
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'type' => 'required|in:unique,parent,fictif',
                'email' => 'required|unique:users,id,' . $user->id,
            ]);
        }
        else {
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'type' => 'required|in:unique,parent,fictif',
                'email' => 'required|unique:users',
            ]);
        }
        if ($validator->fails()){
            return $validator;
        }
        else {
            $user->type = $request->type;
            $user->service_id = $request->service_id;
            if($request->type  == "parent"){
                $validator = Validator::make($request->all(), [
                    'email_password' => 'required',
                ]);
                $user->role = null;
                $user->email_password = $request->email_password;
                $user->real_email_id = null;
                $user->employe_id = null;
            }
            else if($request->type  == "unique") {
                $validator = Validator::make($request->all(), [
                    'email_password' => 'required',
                    'role' => 'required',
                    'employe_id' => 'required',
                    'service_id' => 'required',
                ]);
                $user->role = $request->role;
                $user->email_password = $request->email_password;
                $user->real_email_id = null;
                $user->employe_id = $request->employe_id;
            }
            else if($request->type  == "fictif") {
                $validator = Validator::make($request->all(), [
                    'role' => 'required',
                    'real_email_id' => 'required',
                    'employe_id' => 'required',
                    'service_id' => 'required',
                ]);
                $user->role = $request->role;
                $user->email_password = null;
                $user->real_email_id = $request->real_email_id;
                $user->employe_id = $request->employe_id;
            }
            $user->flotte = null;
            if ($request->flotte) {
                $user->flotte = $request->flotte;
                $validator = Validator::make($request->all(),[
                    'flotte' => ['required', 'regex:/(^03[23478]{1}\d{7}$)/u'],
                ]);
            }
            $user->name = $request->name;
            $user->email = $request->email;
            /*if(!Hash::check($request->password, $user->password)){
                $user->password = bcrypt($request->password);
            }*/
            return $validator;
        }
    }

    public function store(Request $request){
        $auth = $request->user();
        if($auth->role == "admin"){
            $user = new User();
            $validator = $this->validateAndSetUser($request, $user);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $user->must_change_password = 1;
            $user->password = bcrypt('123456');
            if($user->save()){
                $user_id = $user->id;
                HistoriqueController::new_user($request, $user);             
                return response(["success" => "Utilisateur bien envoyée", "id" => $user->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function edit(Request $request, $id){
        $user = User::find($id);
        $old_user = clone $user;
        $auth = $request->user();
        if($auth->role == "admin"){
            $validator = $this->validateAndSetUser($request, $user);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            
            if(in_array($request->type, ['fictif', 'unique']) && $user->employe_id && $user->employe_id != $request->employe_id) {
                Employe::where('responsable_id', $user->employe_id)
                    ->where(function ($query) {
                        return $query->whereNull('soft_delete')->orWhere('soft_delete', 0);
                    })
                    ->update(['responsable_id' => $request->employe_id, 'resp_part_id' => $request->employe_id]);
            }
            
            if($request->type != "parent"){
                $has_email_id = User::where("real_email_id", $id)->first();
                if($has_email_id)
                    return response(["error" => "Ce compte est déjà utilisé en tant que parent."]);
            }
            if($user->save()){
                HistoriqueController::update_user($request, $old_user, "Utilisateur modifié");          
                return response(["success" => "Utilisateur modifié", "id" => $user->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function service(Request $request){
        if ($request->user()->role == "admin") {
            $services = DB::select("SELECT * FROM services");
            return response()->json(compact('services'));
        }
        return response(["error" => "EACCES"]);
    }
}
