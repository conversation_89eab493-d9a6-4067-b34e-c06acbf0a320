import {useState,useRef, useEffect} from 'react'

import useClickOutside from '../util/useClickOutside'
import axios from 'axios'
import useToken from '../util/useToken'

export default function InputFonction({currentSelect, setCurrentSelect, numberToshow, required}) {
    const [showSelect, toggleSelect] = useState(false)
    const [label, setLabel] = useState("")
    const [fonctions, setFonctions] = useState([]);
    const selectRef = useRef(null)

    useClickOutside(selectRef, () => {
        toggleSelect(false)
        if(currentSelect && currentSelect.libelle)
            setLabel(currentSelect.libelle)
        else
            setLabel("")
    })

    useEffect(() => {
        setLabel(currentSelect ? currentSelect.libelle: "")
    }, [currentSelect])
    
    useEffect(() => {
        let isMounted = true
        axios.get("/api/fonction", useToken())
        .then((res) => {
            if(isMounted){
                setFonctions(res.data)
            }
        })
        return () => {isMounted = false}
    }, [])

    return (
        <div ref={selectRef} className='input-container'>
            <label>Fonction {required && <span className='danger'>*</span>}</label>
            <input className='select-search' 
                onClick={() => toggleSelect(!showSelect)} 
                value={label} 
                onChange={(e) => {
                    setLabel(e.target.value)
                    toggleSelect(true)
                    if(!e.target.value) setCurrentSelect(null)
                }}/>
            <div className='input-select-relative'>
                {
                    showSelect && 
                    <div className='select-list'>
                        {
                            fonctions.filter(fonc => (new RegExp(label.toLowerCase(), 'g')).test(fonc.libelle.toLowerCase()))
                            .map((imt, index) => (
                                numberToshow ? index < numberToshow &&
                                    <div 
                                        key={index}
                                        className="select-item"
                                        onClick={(e) => {
                                            setCurrentSelect(imt)
                                            toggleSelect(false)
                                            setLabel(imt.libelle)
                                        }}
                                    >
                                        {imt.libelle}
                                    </div>
                                :
                                    <div
                                        key={index}
                                        className="select-item"
                                        onClick={(e) => {
                                            setCurrentSelect(imt)
                                            toggleSelect(false)
                                            setLabel(imt.libelle)
                                        }}
                                    >
                                        {imt.libelle}
                                    </div>
                        ))
                        }
                        { numberToshow && <div className="select-item"> ... </div> }
                        
                    </div>
                }
            </div>
        </div>
    )
}
