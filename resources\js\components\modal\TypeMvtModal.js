import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import InputSelect from '../input/InputSelect';

export default function TypeMvtModal({ closeModal, useLink }) {
    const navigate = useNavigate();
    const location = useLocation();

    const [selectType, setSelectType] = useState();

    const handleOk = () => {
        if (useLink && selectType.value) {
            let params = new URLSearchParams(location.search);
            params.set("type_mvt", selectType.value);
            navigate(location.pathname + "?" + params);
        }
        closeModal();
    };

    return (
        <div className="modal">
            <div>
                <h3>Type de mouvement</h3>
                <InputSelect 
                    selected={selectType} 
                    setSelected={setSelectType}
                    options={[
                        { label: "entré", value: "entré" },
                        { label: "sortie", value: "sortie" },
                    ]}
                />
                <div className="form-button-container" style={{marginTop: "40px"}}>
                    <button className="btn-primary" onClick={handleOk}>OK</button>
                    <button onClick={closeModal}>Annuler</button>
                </div>
            </div>
        </div>
    );
}