import React, { useState, useEffect } from 'react';
import { Link, useLocation } from "react-router-dom";
import { AiOutlineClose } from 'react-icons/ai' 
import { RiRefreshLine } from "react-icons/ri";

import './sidebar.css'
import ConfirmModal from '../../modal/ConfirmModal';
import LoadingScreen from '../../loading/LoadingScreen';

export default function MailSidebar({auth, closeSidebar, disconnectUser}) {
    const [showLogoutModal, toggleLogoutModal] = useState(false)
    const [isLoading, toggleLoading] = useState(false)
    const [currentService, setCurrentService] = useState(0)
    const locationSearch = useLocation().search
    const searchParams = new URLSearchParams(locationSearch)
    const serviceId = searchParams.get("service_id")

    const handleCloseSidebar = () => {
        if(closeSidebar)
            closeSidebar()
    }
    const handleDashBoard = (id) => {
        if(serviceId == id)
            window.location.reload()
        if(id != currentService){
            toggleLoading(true)
            setTimeout(() => toggleLoading(false), 500)
            setCurrentService(id)
        }
        if(closeSidebar)
            closeSidebar()
    }
    const getWindowSize = () => {
        const {innerWidth, innerHeight} = window;
        return {innerWidth, innerHeight};
    }
    const [windowSize, setWindowSize] = useState(getWindowSize())

    useEffect(() => {
        function handleWindowResize() {
          setWindowSize(getWindowSize());
        }
    
        window.addEventListener('resize', handleWindowResize);
    
        return () => {
          window.removeEventListener('resize', handleWindowResize);
        }

    }, [windowSize])

    return <>
        {
            isLoading &&
            <LoadingScreen/>
        }
        <div className='show-sidebar custom-scroll' 
            onClick={(e) => {e.stopPropagation()}}
            style= {
                windowSize.innerWidth > 1000 ? {
                    height: (windowSize.innerHeight*100/80 - 71) + "px",
                    top: '71px',
                    position: 'fixed',
                    borderRight: '1px solid #ddd'
                }
                : {
                    height: (windowSize.innerHeight*100/80) + "px",
                    paddingBottom: "70px",
                    borderRight: "1px solid #ddd"
                }
            }
        >
            {
                showLogoutModal &&
                <ConfirmModal 
                    msg="Se déconnecter ?" 
                    confirmAction={disconnectUser} 
                    closeModal={() => toggleLogoutModal(false)}/>
            }
            <ul>
                {
                    windowSize.innerWidth < 1000 &&
                    <li>
                        <AiOutlineClose size={30} onClick={closeSidebar}/>
                    </li>
                }
                <li className='sub-menu space-between'>
                    <Link onClick={() => handleDashBoard(-1)} to="/message"><b>Message entrant</b></Link>
                    <RiRefreshLine color='#666' size={20} cursor="pointer" onClick={() => window.location.reload(false)}/>
                </li>
                {
                    auth.services.map(s => (
                        <li key={s.id}>
                            <span className='sub-menu space-between'>
                                <Link className='link-no-style' onClick={() => handleDashBoard(s.id)} to={"/message?service_id=" + s.id}>{s.designation}</Link>
                                {
                                    s.nb_unread > 0 && 
                                    <span className='badge-number'>{s.nb_unread}</span>
                                }
                            </span>
                        </li>
                    ))
                }
                <li>
                    <Link onClick={handleCloseSidebar} to="/message?sent=1"><b>Envoyés</b></Link>
                </li>
                    <li>
                        <span className='sub-menu space-between'>
                            <Link className='link-no-style' to="/message/sent-unread">
                                {auth.role == "validateur" ? "Consigne non lu" : "Envoyé non lu"}
                            </Link>
                            {
                                auth.consigne_non_lu > 0 &&
                                <span className='badge-number'>{auth.consigne_non_lu}</span>
                            }
                        </span>
                    </li>
                    <li>
                        <span className='sub-menu'  >
                            <span>
                                <Link className='link-no-style' to="/message/model">
                                    Model de courrier
                                </Link>
                            </span>
                        </span>
                    </li>
                <li>
                    <span onClick={() => toggleLogoutModal(true)}><b>Déconnecter</b></span>
                </li>
            </ul>
        </div>
    </>
}