import React, { useState } from 'react'
import { Link } from 'react-router-dom';
import NoteModal from '../input/NoteModal';

export default function ActionService24({ auth, service, updateData }) {
    const [currentAction, setAction] = useState(null)
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [defaultNote, setDefaultNote] = useState("")
    const handleCancel = (id) => {
        setDefaultNote("")
        setAction({
            header: "Annuler la demande",
            request: "/api/service24/cancel/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    const handleValidate = (id) => { 
        setDefaultNote("ok")
        setAction({
            header: "Validation",
            request: "/api/service24/reply_validation/" + id,
        })
        toggleNoteModal(true)
    }
    return (
        <>
            {
                showNoteModal && (
                <NoteModal
                    action={currentAction}
                    updateData={() => updateData(true)}
                    closeModal={() => toggleNoteModal(false)}
                    name="service24_id"
                    value={service.id}
                    defaultNote={defaultNote}
                />
            )}
            <div className='action-container'>
                
                {["validateur"].includes(auth.role) && (service.status == 'validation') && (
                    <span onClick={()=>handleValidate(service.id)}>
                    Valider
                    </span>
                )}
                {auth.id == service.user_id && service.status == "draft" && (
                    <span>
                        <Link to={"/service24/send_back/" + service.id}>Renvoyer</Link>
                    </span>
                )}
                {["superviseur", "validateur", "resp_sup", "resp_op"].includes(auth.role) && (
                    ["validation"].includes(service.status) && (auth.id == service.user_id || auth.role == "validateur") && (
                        <span onClick={() => handleCancel(service.id)}>
                            Annuler
                        </span>
                    )
                )}
            </div>
        </>
    )
}
