<?php

namespace App\Http\Controllers;

use App\Models\Employe;
use App\Models\ModelMessage;
use App\Models\NoteModelMessage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ModelMessageController extends Controller
{
    public static function search(Request $request)
    {
        $searchArray = [];
        if ($request->id)
            $searchArray[] = " mm.id = " . $request->id . " ";
        else {
            if ($request->user_id) {
                $searchArray[] = " ur.id = " . $request->user_id;
            }
            if ($request->objet) {
                $searchArray[] = " mm.objet like '%" . $request->objet . "%' ";
            }
            if ($request->nom) {
                $searchArray[] = " mm.type like '%" . $request->nom . "%' ";
            }
            if ($request->created_at) {
                $searchArray[] =  " mm.created_at > '$request->created_at 00:00:00' and mm.created_at <= '$request->created_at 23:59:59' ";
            }
        }
        $query_where = "";
        $query_and = "";
        if ($request->offset || $request->offset > -1) {
            $orderBy = " order by mm.id DESC limit " . $request->offset . ", 30";
        } else {
            $orderBy = " order by mm.id DESC";
        }
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . "";
        }
        $query_where = $query_where . " " . $orderBy;
        $query_and = $query_and . " " . $orderBy;
        return compact("query_where", "query_and");
    }


    public static function validateAndSetModel(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required',
            'access' => 'required',
        ]);
        if ($validator->fails())
            return ['error' => "Le nom du model est requis"];

        $auth = $request->user(); 
        if ($request->copies && $request->receivers) {
            $copies = [];
            foreach ($request->copies as $id) {
                    $inDestinataire = false;
                    foreach ($request->receivers as $rec_id) {
                        if ($id == $rec_id) {
                            $inDestinataire = true;
                        }
                    }
                    if (!$inDestinataire) {
                        $copies[] = $id;
                    }
                }
            $request->merge(['copies' => $copies]);
        }
        if (!($request->objet || $request->content || $request->copies || $request->receivers)) {
            return ["error" => "Vous devez remplir au moins un des champs."];
        }

        if (($request->access == "all" && $auth->role != "admin") || (!in_array($request->access, ['all', 'service', 'me']))) {
            return ["error" => "EACCES"];
        } 
        else if ($request->access == "service") {
            $service_id = (Employe::find($auth->employe_id))->service_id;
            if (!$service_id) {
                return ["error" => "Vous n'avez pas de service"];
            }
        }
        return ["error" => ""];
    }

    public function store(Request $request)
    {
        $model = new ModelMessage();
        $validator = $this->validateAndSetModel($request);
        if ($validator['error'])
            return response($validator);
        else {
            $model->user_id = $request->user()->id;
            $model->created_at = new \DateTime();
            $model->updated_at = new \DateTime();
            $model->objet = $request->objet;
            $model->type = $request->type;
            $model->content = $request->content;
            $model->access = $request->access;
            if ($model->save()) {
                if ($request->receivers) {
                    foreach ($request->receivers as $id) {
                        $note = new NoteModelMessage();
                        $note->message_model_id = $model->id;
                        $note->user_id = $id;
                        $note->follow = 1;
                        $note->created_at = new \DateTime();
                        $note->updated_at = new \DateTime();
                        $note->save();
                    }
                }
                if ($request->copies) {
                    foreach ($request->copies as $id) {
                        if (($request->receivers && !in_array($id, $request->receivers)) || !$request->receivers) {
                            $note = new NoteModelMessage();
                            $note->message_model_id = $model->id;
                            $note->user_id = $id;
                            $note->follow = null;
                            $note->created_at = new \DateTime();
                            $note->updated_at = new \DateTime();
                            $note->save();
                        }
                    }
                }
                return response(['success' => "Model enregisté"]);
            }
        }
    }

    public function get_model_by_id(Request $request, $id)
    {
        $employe_user = Employe::find($request->user()->employe_id);
        $model = DB::select(
            "SELECT mm.id, mm.type, mm.content, mm.objet, mm.user_id, mm.access
            FROM model_messages mm
            LEFT JOIN users u ON u.id = mm.user_id
            LEFT JOIN employes emp ON emp.id = u.employe_id
            WHERE  mm.id = ?
            AND ((emp.service_id = ? AND mm.access = 'service')
            OR (emp.service_id = ? AND mm.access is NULL)
            OR (mm.access = 'me' AND mm.user_id = ?) 
            OR (mm.access = 'all')
            OR (mm.user_id = ?))", 
            [
                $id, 
                $employe_user->service_id,
                $employe_user->service_id,
                $request->user()->id,
                $request->user()->id
            ])[0];

            " WHERE ";

        $users = DB::select(
            "SELECT note.follow, note.user_id, u.name as 'user_name', u.email as 'user_email'
            FROM note_model_messages note
            LEFT JOIN users u on u.id = note.user_id
            WHERE note.soft_delete is null and note.message_model_id = ? ",
            [$id]
        );
        $model->users = $users;
        return response(compact('model'));
    }

    public function index(Request $request)
    {
        $employe_user = Employe::find($request->user()->employe_id);
        $service_id = $employe_user->service_id;
        $ids = DB::select(
            "SELECT mm.id, mm.access
            FROM model_messages mm 
            LEFT JOIN users u ON u.id = mm.user_id
            LEFT JOIN employes emp ON emp.id = u.employe_id
            WHERE ((emp.service_id = ? AND mm.access = 'service')
            OR (emp.service_id = ? AND mm.access is NULL)
            OR (mm.access = 'me' AND mm.user_id = ?) 
            OR (mm.access = 'all')
            OR (mm.user_id = ?))"
            . ($this->search($request))['query_and'],
            [
                $service_id, 
                $service_id, 
                $request->user()->id,
                $request->user()->id
            ]);

        if (empty($ids)) {
            return response()->json(['models' => []]);
        }

        $ids = array_map(function ($row) {
            return $row->id;
        }, $ids);
        $query = "SELECT mm.id, mm.objet, mm.type, mm.user_id, mm.content, mm.created_at, mm.updated_at,
            note.follow, note.user_id, u.name as user_name, u.email as user_email
            FROM model_messages mm
            LEFT JOIN note_model_messages note ON mm.id = note.message_model_id
            LEFT JOIN users u ON u.id = note.user_id
            WHERE mm.id IN (" . implode(',', array_fill(0, count($ids), '?')) . ")
            AND (note.soft_delete = 0 OR note.soft_delete IS NULL)
            ORDER BY mm.id DESC";

        $results = DB::select($query, $ids);

        $models = [];
        foreach ($results as $row) {
            if (!isset($models[$row->id])) {
                $models[$row->id] = (object) [
                    'id' => $row->id,
                    'type' => $row->type,
                    'created_at' => $row->created_at,
                    'updated_at' => $row->updated_at,
                    'objet' => $row->objet,
                    'users' => []
                ];
            }
            if ($row->user_id) {
                $models[$row->id]->users[] = (object) [
                    'follow' => $row->follow,
                    'user_id' => $row->user_id,
                    'user_name' => $row->user_name,
                    'user_email' => $row->user_email
                ];
            }
        }
        $models = array_values($models);
        return response(compact('models'));
    }

    public function update(Request $request, $id)
    {
        $model = ModelMessage::find($id);
        $resp_room_can_edit = false;
        if($request->user()->id == 73){
            $user_service = Employe::find(User::find($model->user_id)->employe_id)->service_id;
            $model_user_service = Employe::find($request->user()->employe_id)->service_id;
            if($model->access == "service" && $model_user_service == $user_service){
                $resp_room_can_edit = true;
            }
        }
        if ($request->user()->id == $model->user_id || $resp_room_can_edit ){
            $validator = $this->validateAndSetModel($request);
            if ($validator['error'])
                return response($validator);
            else {
                $model->updated_at = new \DateTime();
                $model->objet = $request->objet;
                $model->type = $request->type;
                $model->content = $request->content;
                $model->access = $request->access;
                if ($model->save()) {
                    if ($request->copies) {
                        $existingCopyNotes = NoteModelMessage::where('message_model_id', $id)
                            ->whereNull('follow')
                            ->whereNull('soft_delete')
                            ->get();

                        $existingCopyUserIds = $existingCopyNotes->pluck('user_id')->toArray();
                        foreach ($request->copies as $userId) {
                            if (!in_array($userId, $existingCopyUserIds)) {
                                $note = new NoteModelMessage();
                                $note->message_model_id = $id;
                                $note->user_id = $userId;
                                $note->created_at = new \DateTime();
                                $note->updated_at = new \DateTime();
                                $note->save();
                            }
                        }
                        foreach ($existingCopyNotes as $cpNote) {
                            if (!in_array($cpNote->user_id, $request->copies)) {
                                $cpNote->update(["soft_delete" => 1]);
                            }
                        }
                    } else {
                        $existingCopyNotes = NoteModelMessage::where('message_model_id', $id)
                            ->where('follow', 1)
                            ->whereNull('soft_delete')
                            ->get();
                        if (count($existingCopyNotes) > 0) {
                            NoteModelMessage::where('message_model_id', $id)
                                ->where('follow', 1)
                                ->whereNull('soft_delete')
                                ->update(["soft_delete" => 1]);
                        }
                    }

                    if ($request->receivers) {
                        $existingUsersNotes = NoteModelMessage::where('message_model_id', $id)
                            ->where('follow', 1)
                            ->whereNull('soft_delete')
                            ->get();
                        $existingUserIds = $existingUsersNotes->pluck('user_id')->toArray();

                        foreach ($request->receivers as $userId) {
                            if (!in_array($userId, $existingUserIds)) {
                                $note = new NoteModelMessage();
                                $note->message_model_id = $id;
                                $note->user_id = $userId;
                                $note->follow = 1;
                                $note->created_at = new \DateTime();
                                $note->updated_at = new \DateTime();
                                $note->save();
                            }
                        }
                        foreach ($existingUsersNotes as $userNote) {
                            if (!in_array($userNote->user_id, $request->receivers)) {
                                $userNote->update(["soft_delete" => 1]);
                            }
                        }
                    } else {
                        $existingUsersNotes = NoteModelMessage::where('message_model_id', $id)
                            ->where('follow', 1)
                            ->whereNull('soft_delete')
                            ->get();
                        if (count($existingUsersNotes) > 0) {
                            NoteModelMessage::where('message_model_id', $id)
                                ->where('follow', 1)
                                ->whereNull('soft_delete')
                                ->update(["soft_delete" => 1]);
                        }
                    }

                    return response(['success' => "Model enregisté"]);
                }
            }
        }
        return response(['error' => "Vous n'avez pas le droit de modifier ce model"]);
    }

    public function get_all_users(Request $request)
    {
        $users = User::where('type', '!=', 'parent')
            ->Where(function ($query) {
                $query->where('blocked', '0')
                    ->orWhereNull('blocked');
            })
            ->orderBy('name', 'asc')
            ->get();
        return response()->json($users);
    }
    
    public function get_users_by_service(Request $request, $id){
        $users = User::where('type', '!=', 'parent')
            ->Where('service_id', $id)
            ->Where(function ($query) {
                $query->where('blocked', '0')
                ->orWhereNull('blocked');
            })
            ->orderBy('name', 'asc')
            ->get();
        return response()->json($users);
    }
}
