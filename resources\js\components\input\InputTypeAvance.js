import { useState, useRef, useEffect } from 'react'

import useClickOutside from '../util/useClickOutside'
import axios from 'axios'
import useToken from '../util/useToken'

export default function InputTypeAvance({ currentSelect, setCurrentSelect, required }) {
    const [showSelect, toggleSelect] = useState(false)
    const [label, setLabel] = useState("")
    const [typeAvance, setTypeAvance] = useState([]);
    const selectRef = useRef(null)

    useClickOutside(selectRef, () => {
        toggleSelect(false)
        if (currentSelect && currentSelect.description)
            setLabel(currentSelect.description)
        else
            setLabel("")
    })

    useEffect(() => {
        setLabel(currentSelect ? currentSelect.description : "")
    }, [currentSelect])

    useEffect(() => {
        let isMounted = true
        axios.get("/api/avance/type", useToken())
            .then((res) => {
                if (isMounted) {
                    setTypeAvance(res.data.type)
                    if (res.data.type.length == 1) {
                        setCurrentSelect(res.data.type[0])
                    }
                }
            })
        return () => { isMounted = false }
    }, [])

    return (
        <div ref={selectRef} className='input-container'>
            <label>Type avance {required && <span className='danger'>*</span>}</label>
            <input className='select-search'
                onClick={() => toggleSelect(!showSelect)}
                value={label}
                onChange={(e) => {
                    setLabel(e.target.value)
                    toggleSelect(true)
                    if (!e.target.value) setCurrentSelect(null)
                }} />
            <div className='input-select-relative'>
                {
                    showSelect &&
                    <div className='select-list'>
                        {
                            typeAvance.filter(fonc => (new RegExp(label.toLowerCase(), 'g')).test(fonc.description.toLowerCase()))
                            .map((imt, index) => (
                                <div
                                    key={index}
                                    className="select-item"
                                    onClick={(e) => {
                                        setCurrentSelect(imt)
                                        toggleSelect(false)
                                        setLabel(imt.description)
                                    }}
                                >
                                    {imt.description}
                                </div>
                            ))
                        }

                    </div>
                }
            </div>
        </div>
    )
}
