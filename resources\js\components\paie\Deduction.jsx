import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'

export default function Deduction({ deduction, setDeduction}) {
    return (
    <div>
        <DualContainer>
            <InputText label="Retenue formation"
                value={deduction.retenueFormation}
                onChange={(value) => setDeduction({ ...deduction, retenueFormation: parseFloat(value) })}
            />
            <InputText label="Déduction" value={deduction.autreDeduction} disabled />
        </DualContainer>
        <DualContainer>
            <InputText label="Avance 15é" value={deduction.avance15} disabled />
            <InputText label="Avance spéciale" value={deduction.avanceSpeciale} disabled />
        </DualContainer>
        <DualContainer>
            <InputText value={deduction.avanceSpecialeEmbauche} label="Avance Embauche" disabled />
        </DualContainer>   
    </div>
  )
}
