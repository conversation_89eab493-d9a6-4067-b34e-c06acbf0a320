const moment = require('moment')
const mysql = require('mysql2')

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const sqlSelectRecrutement = "SELECT id, cin_text FROM recrutements where employe_id is null"
const sqlSelectEmploye = "SELECT id FROM employes WHERE cin_text = ?"
const sqlUpdateRecrutement = "UPDATE recrutements set employe_id = ?, status = 'Recruté' WHERE id = ?"
const sqlUpdateEmploye = "UPDATE employes set recrutement_id = ? WHERE id = ?"


function updateData(recrutements, index){
    if(index < recrutements.length) {
        const currentRecru = recrutements[index]
        pool.query(sqlSelectEmploye, [currentRecru.cin_text], async (err, employes) => {
            if(err)
                console.error(err)
            else if(employes.length > 0){
                const employe = employes[0]
                console.log(employe)
                pool.query(sqlUpdateRecrutement, [employe.id, currentRecru.id], async (err, result) => {
                    if(err)
                        console.error(err)
                    else {
                        pool.query(sqlUpdateEmploye, [currentRecru.id, employe.id], async (err, result) => {
                            if(err)
                                console.error(err)
                            else {
                                console.log(result)
                                setTimeout(() => {
                                    updateData(recrutements, index+1)
                                }, 100);
                            }
                        })
                    }
                })
            }
            else {
                setTimeout(() => {
                    updateData(recrutements, index+1)
                }, 100);
            }
        })
    }
    else {
        console.log("update recrutement done!")
        process.exit(1)
    }
}

pool.query(sqlSelectRecrutement, [], async (err, recrutements) => {
    if(err){
        console.log(err)
    }
    else {
        console.log("Nb recur : " + recrutements.length)
        updateData(recrutements, 0)
    }
})