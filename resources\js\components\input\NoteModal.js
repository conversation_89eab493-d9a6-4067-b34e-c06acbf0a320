import React, { useState } from 'react';

import Textarea from './Textarea';
import axios from 'axios';
import useToken from '../util/useToken';
import InputMultipleUser from './InputMutipleUser';
import InputMonthYear from './InputMonthYear';
import InputCheckBox from './InputCheckBox';

export default function NoteModal({ id, action, name, toDone, setToDone, value, updateData, closeModal, required, defautUsers, defaultNote }) {
    const equipement_id = (name == "equipement_id" ? value : "")
    const sanction_id = (name == "sanction_id" ? value : "")
    const prime_id = (name == "prime_id" ? value : "")
    const absence_id = (name == "absence_id" ? value : "")
    const sav_id = (name == "sav_id" ? value : "")
    const flotte_id = (name == "flotte_id" ? value : "")
    const appro_id = (name == "approvisionnement_id" ? value : "")
    const visite_id = (name == "visite_poste_id" ? value : "")
    const fait_id = (name == "fait_marquant_id" ? value : "")
    const employe_id = (name == "employe_id" ? value : "")
    const user_id = (name == "user_id" ? value : "")
    const juridique_id = (name == "juridique_id" ? value : "")
    const paie_id = (name == "paie_id" ? value : "")
    const deduction_id = (name == "deduction_id" ? value : "")
    const avance_id = (name == "avance_id" ? value : "")
    const service24_id = (name == "service24_id" ? value : "")
    const reclamation_id = (name == "reclamation_id" ? value : "")
    const site_id = (name == "site_id" ? value : "")
    const part_variable_id = (name == "part_variable_id" ? value : "")
    const satisfaction_id = (name == "satisfaction_id" ? value : "")
    const dotation_id = (name == "dotation_id" ? value.id : "")

    const [date, setDate] = useState({ year: "", month: "", });
    const [users, setUsers] = useState(defautUsers ? defautUsers : []);
    const [note, setNote] = useState(defaultNote ? defaultNote : "")
    const [error, setError] = useState("")
    const [follow, setFollow] = useState(true)
    const [submitDisabled, disableSubmit] = useState(false)
    
    const handleOk = () => {
        disableSubmit(true)
        setError("")
        let formData = new FormData()
        formData.append("note", note)
        if(id)
            formData.append("note_id", id)
        if (toDone) {
            formData.append("date_paie", date.year + "-" + date.month + "-20")
        }
        if(follow)
            formData.append("follow", 1)
        formData.append("user_ids", JSON.stringify(users.map(u => u.id)))

        if (equipement_id) {
            formData.append("equipement_id", equipement_id)
        }
        else if (visite_id) {
            formData.append("visite_poste_id", visite_id)
        }
        else if (fait_id) {
            formData.append("fait_marquant_id", fait_id)
        }
        else if (sanction_id) {
            formData.append("sanction_id", sanction_id)
        }
        else if (prime_id) {
            formData.append("prime_id", prime_id)
        }
        else if (absence_id) {
            formData.append("absence_id", absence_id)
        }
        else if (sav_id) {
            formData.append("sav_id", sav_id)
        }
        else if (flotte_id) {
            formData.append("flotte_id", flotte_id)
        }
        else if (appro_id) {
            formData.append("approvisionnement_id", appro_id)
        }
        else if (employe_id) {
            formData.append("employe_id", employe_id)
        }
        else if (user_id) {
            formData.append("employe_id", user_id)
        }
        else if (juridique_id) {
            formData.append("juridique_id", juridique_id)
        }
        else if (paie_id) {
            formData.append("paie_id",paie_id)
        }
        else if (deduction_id) {
            formData.append("deduction_id",deduction_id)
        }
        else if (avance_id) {
            formData.append("avance_id",avance_id)
        }
        else if (service24_id) {
            formData.append("service24_id", service24_id)
        }
        else if (reclamation_id) {
            formData.append("reclamation_id",reclamation_id)
        }
        else if (site_id) {
            formData.append("site_id", site_id)
        }
        else if (part_variable_id) {
            formData.append("part_variable_id", part_variable_id)
        }
        else if (satisfaction_id) {
            formData.append("satisfaction_id", satisfaction_id)
        }
        else if (dotation_id) {
            formData.append("dotation_id", dotation_id)
        }
        axios.post(action ? action.request : "/api/notification/add", formData, useToken())
            .then((res) => {
                disableSubmit(false)
                if (res.data.success) {
                    setToDone && setToDone(false)
                    closeModal()
                    if (updateData) updateData()
                }
                else if (res.data.error == "EACCES")
                    setError("Une erreur est survenue.")
                else if (res.data.error)
                    setError(res.data.error)
            })
            .catch((e) => {
                console.error(e)
                disableSubmit(false)
                setError("Erreur d'envoie, réessayez.")
            })
    }

    return <div className='modal'>
        <div>
            <h3>
                {action ? action.header : "Note"}
            </h3>
                {
                    toDone && 
                    <div className='input-container'>
                        <InputMonthYear label={"Fiche de paie"} value={date} onChange={setDate} required />
                    </div>
                }
                {
                    !action &&
                    <InputMultipleUser label="Destinataire" users={users} setUsers={setUsers} />
                }
                <Textarea
                    value={note}
                    label="Commentaire"
                    onChange={(value) => setNote(value)}
                    required={action ? action.required : required} />
                {
                    !action &&
                    <InputCheckBox label="Suivre la réponse" checked={follow} onChange={setFollow} />
                }
                {
                    error &&
                    <div className='container-error'>
                        {error}
                    </div>
                }
                <div className='form-button-container'>
                    <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Envoyer</button>
                    <button onClick={() => { closeModal(); setToDone?setToDone(false):""}}>Annuler</button>
                </div>
            </div>
        </div>
}