const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const matricule = require("../resources/js/components/util/matricule")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)
const {sendMail} = require("../auth")

const isTask = (process.argv[2] == "task")
const destination_vg = ["ogros<PERSON><EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectLastAbsenceExport = "SELECT value FROM params p WHERE p.key = 'last_absence_export'"

const sqlSelectStatus = "SELECT name, description FROM status order by ordre"

function sqlSelectAbsence(dateString){
    const begin = moment(dateString).subtract(7, "day").format("YYYY-MM-DD") + " 06:00:00"
    const end = dateString + " 06:00:00"
	return "SELECT abs.id, abs.type_absence, abs.employe_id, abs.site_id, abs.depart, abs.retour, abs.motif, " +
        "abs.created_at, abs.status, u.name as 'user_nom', u.email as 'user_email', " +
        "s.nom as 'site', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.nom as 'employe' " +
        "from absences abs " +
        "left join employes a on a.id = abs.employe_id " +
        "left join sites s on s.idsite = abs.site_id " +
        "left join users u on u.id = abs.user_id " +
        "where (abs.status not in ('done', 'draft') " + 
        "or (abs.status in ('done', 'draft') and abs.updated_at > '" + begin +"' and abs.updated_at <= '" + end +"')) " +
        "order by abs.created_at"
}

function sqlUpdateLastAbsenceExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_absence_export'"
}

function generateAbsenceExcelFile(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    data.forEach(stat => {
        const worksheet = workbook.addWorksheet(stat.description)
        worksheet.getColumn('A').width = 50
        worksheet.getColumn('B').width = 50
        worksheet.getColumn('C').width = 20
        worksheet.getColumn('D').width = 20
        worksheet.getColumn('E').width = 50
        worksheet.getColumn('F').width = 40
        worksheet.getColumn('G').width = 15
        worksheet.getCell('A1').value = stat.description + " (" + stat.absences.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:G1')

        let line = 3
        
        worksheet.getCell('A' + line).value = "Employe"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontBold
        worksheet.getCell('B' + line).value = "Site"
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).font = fontBold
        worksheet.getCell('C' + line).value = "Départ"
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).alignment = alignmentStyle
        worksheet.getCell('C' + line).font = fontBold
        worksheet.getCell('D' + line).value = "Retour"
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).alignment = alignmentStyle
        worksheet.getCell('D' + line).font = fontBold
        worksheet.getCell('E' + line).value = "Motif"
        worksheet.getCell('E' + line).border = borderStyle
        worksheet.getCell('E' + line).font = fontBold
        worksheet.getCell('F' + line).value = "Demandeur"
        worksheet.getCell('F' + line).border = borderStyle
        worksheet.getCell('F' + line).font = fontBold
        worksheet.getCell('G' + line).value = "Créé"
        worksheet.getCell('G' + line).border = borderStyle
        worksheet.getCell('G' + line).font = fontBold
        worksheet.getCell('G' + line).alignment = alignmentStyle
        line++

        stat.absences.forEach(abs => {
            worksheet.getCell('A' + line).value = (
                matricule(abs)
            ) + " " + abs.employe
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = (abs.site ? capitalizeFirstLetter(abs.site) : "")
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = moment(abs.depart).format("DD-MM-YY") + " " + (moment(abs.depart).format("HH:mm:ss") == "06:00:00" ? " JOUR" : " NUIT")
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('C' + line).alignment = alignmentStyle
            worksheet.getCell('D' + line).value = moment(abs.retour).format("DD-MM-YY") + " " + (moment(abs.retour).format("HH:mm:ss") == "06:00:00" ? " JOUR" : " NUIT")
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('D' + line).alignment = alignmentStyle
            worksheet.getCell('E' + line).value = abs.motif
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('F' + line).value = abs.user_nom + " <" + abs.user_email + ">"
            worksheet.getCell('F' + line).border = borderStyle
            worksheet.getCell('G' + line).value = moment(abs.created_at).fromNow()
            worksheet.getCell('G' + line).border = borderStyle
            worksheet.getCell('G' + line).alignment = alignmentStyle
            line++
        })
    })
}

function doAbsenceExport(dateString){
	console.log("doAbsenceExport")
    pool.query(sqlSelectStatus, [], async (err, status) => {
        if(err)
            console.error(err)
        else {
            pool.query(sqlSelectAbsence(dateString), [], async (err, absences) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb absence: " + absences.length)
                    const conges = absences.filter(abs => abs.type_absence == "conge")
                    const permissions = absences.filter(abs => abs.type_absence == "permission")
                    let congeByStatus = []
                    let permissionByStatus = []
                    status.forEach(stat => {
                        congeByStatus.push(Object.assign({}, stat))
                        permissionByStatus.push(Object.assign({}, stat))
                    })
                    congeByStatus.map(stat => {
                        stat.absences = []
                        conges.map(abs => {
                            if(stat.name == abs.status)
                                stat.absences.push(abs)
                        })
                    })
                    permissionByStatus.map(stat => {
                        stat.absences = []
                        permissions.map(abs => {
                            if(stat.name == abs.status)
                                stat.absences.push(abs)
                        })
                    })
                    congeByStatus = congeByStatus.filter(stat => stat.absences.length > 0)
                    permissionByStatus = permissionByStatus.filter(stat => stat.absences.length > 0)
                    const dateService = moment(dateString).format("DD MMMM YYYY")
                    const objet = "Absence " + dateService
                    const attachements = []
                    if(congeByStatus.length > 0){
                        const workbookConge = new Excel.Workbook()
                        const congeHeader = "Congé " + dateService
                        generateAbsenceExcelFile(workbookConge, congeHeader, congeByStatus)
                        const congeBuffer = await workbookConge.xlsx.writeBuffer()
                        attachements.push({
                            filename: congeHeader + ".xlsx",
                            content: congeBuffer
                        })
                    }
                    if(permissionByStatus.length > 0){
                        const workbookPermission = new Excel.Workbook()
                        const permissionHeader = "Permission " + dateService
                        generateAbsenceExcelFile(workbookPermission, permissionHeader, permissionByStatus)
                        const permissionBuffer = await workbookPermission.xlsx.writeBuffer()
                        attachements.push({
                            filename: permissionHeader + ".xlsx",
                            content: permissionBuffer
                        })
                    }
                    sendMail(
                        pool,
                        isTask ? destination_vg : destination_test,
                        objet, 
                        "Veuillez trouver ci-joint le rapport des demandes d'absences du " + moment(dateString).subtract(7, "days").format("DD MMMM YYYY") + " au " + moment(dateString).format("DD MMMM YYYY") + "<br/>"
                        + "<ul>" 
                            + "<li>Congé : "
                                + "<ul>"
                                + (congeByStatus.map(stat => "<li>" + stat.description + ": " + stat.absences.length + "</li>").join(""))
                                + "</ul>"
                            + "</li>"
                            + "<li>Permission : "
                                + "<ul>"
                                + (permissionByStatus.map(stat => "<li>" + stat.description + ": " + stat.absences.length + "</li>").join(""))
                                + "</ul>"
                            + "</li>"
                        + "</ul>"
                        ,
                        attachements
                        ,
                        (response) => {
                            if(response && isTask){
                                pool.query(sqlUpdateLastAbsenceExport(dateString), [], (e, r) =>{
                                    if(e)
                                        console.error(e)
                                    else
                                        console.log("update last diag export: " + r)
                                    process.exit(1)
                                })
                            }
                            else
                                process.exit(1)
                        },
                        isTask
                    )
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2])){
    console.log("send test...")
    doAbsenceExport(process.argv[2])
}
else if(isTask){
    if(moment().day() == 3 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
        let date_vigilance = moment().format("YYYY-MM-DD")
        pool.query(sqlSelectLastAbsenceExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && result[0].value == date_vigilance) {
                console.log("export list absence already done!")
                process.exit(1)
            }
            else {
                console.log("exporting ...")
                doAbsenceExport(date_vigilance)
            }
        })
    }
    else {
        console.log("Not wednesday, skip Absence")
    }
}
else
    console.log("please specify command!")