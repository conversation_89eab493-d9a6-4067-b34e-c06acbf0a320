import React, { useEffect, useState } from 'react';

import axios from 'axios';
import useToken from '../util/useToken';
import LoadingPage from '../loading/LoadingPage';

export default function Stock({auth, stocks, setStocks, currentName, setCurrentName}) {
    const [isLoading, toggleLoading] = useState(false)

    const updateData = () => {
        let isMounted = true;
        toggleLoading(true);
        axios.get('/api/article/all_article', useToken())
        .then((res) => {
            if (isMounted) {
                if (res.data.error)
                    console.error(res.data.error);
                else {
                    setStocks(res.data.articles);
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e);
        })
        return () => {
            isMounted = false;
        }
    }

    useEffect(() => {
        updateData()
    }, [])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div>
                <div className='padding-container space-between'>
                    <h2>Stock</h2>
                </div>
            </div>
            {
                (stocks && stocks.length == 0) ?
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :   (
                    <>
                        <div className='line-container'>
                            <div className='row-list'>
                                <b className='line-cell-md'>Article</b>
                                <b className="line-cell-md">Type</b>
                                <b className="line-cell-sm">NB stock</b>
                            </div>
                        </div>
                        {stocks.map((st, index) => (
                            <div key={`${st.name}-${index}`}
                                className={`table line-container ${currentName == st.name ? 'selected' : ''}`}
                                onClick={() => setCurrentName(st.name)}
                            >
                                <div>
                                    <div className='row-list'>
                                        <span className="line-cell-md">{st.name}</span>
                                        <span className="line-cell-md">{st.type}</span>
                                        <span className="line-cell-sm">{st.nbStock}</span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </>
                )
            }
        </div>
    }
    </>
}
