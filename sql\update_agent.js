const moment = require('moment')
const mysql = require('mysql')
const fs = require("fs");

moment.locale('fr')
const auth = require("../auth")

const db_config_ovh = auth.db_config_ovh
const pool_tls = mysql.createPool(db_config_ovh)

const db_config_admin = auth.db_config_admin
const pool_admin = mysql.createPool(db_config_admin)

const sqlSelectAgent = "select id, societe_id, numero_stagiaire, numero_employe, num_emp_soit, nom, " +
    "site_id , real_site_id, date_embauche, date_confirmation, date_conf_soit, date_sortie, soft_delete, " +
    "last_date_pointage, fonction_id, observation, agence_id, last_update " +
    "from employes"
const sqlInsertOrUpdate = "INSERT INTO agents(id, societe_id, numero_stagiaire, numero_employe, num_emp_soit, nom, " +
    "site_id , real_site_id, date_embauche, date_confirmation, date_conf_soit, date_sortie, soft_delete, " +
    "last_date_pointage, fonction_id, observation, agence_id, last_update " +
    ") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) " +
    "ON DUPLICATE KEY UPDATE societe_id=?, numero_stagiaire=?, numero_employe=?, num_emp_soit=?, nom=?, " +
    "site_id=?, real_site_id=?, date_embauche=?, date_confirmation=?, date_conf_soit=?, date_sortie=?, soft_delete=?, " +
    "last_date_pointage=?, fonction_id=?, observation=?, agence_id=?, last_update=? "

function updateAgentById(agents, index){
    if(index < agents.length){
        const agent = agents[index]
        console.log(agent.id + " : " + agent.nom)
        const params = [agent.id, agent.societe_id, agent.numero_stagiaire, agent.numero_employe, agent.num_emp_soit, agent.nom,
            agent.site_id , agent.real_site_id, agent.date_embauche, agent.date_confirmation, agent.date_conf_soit, agent.date_sortie, agent.soft_delete,
            agent.last_date_pointage, agent.fonction_id, agent.observation, agent.agence_id, agent.last_update
        ]
        pool_tls.query(sqlInsertOrUpdate, [...params, ...params.slice(1)], async (err, res) => {
            if(err){
                console.log("err found")
                console.error(err)
                setTimeout(() => {
                    updateAgentById(agents, index)
                }, 300)
            }
            else {
                setTimeout(() => {
                    updateAgentById(agents, index+1)
                }, 100)
            }
        })
    }
    else {
        console.log("process done!")
        process.exit()
    }
}
function updateData(){
    pool_admin.query(sqlSelectAgent, [], async (err, agents) => {
        if(err){
            console.error(err)
        }
        else {
            console.log("agent to sync: " + agents.length)
            updateAgentById(agents, 0)
        }
    })
}

updateData()