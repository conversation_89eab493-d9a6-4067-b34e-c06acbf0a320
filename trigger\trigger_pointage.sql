drop trigger IF EXISTS after_insert_pointage;
drop trigger IF EXISTS after_delete_pointage;
drop trigger IF EXISTS after_update_pointage;

DELIMITER |
CREATE TRIGGER after_insert_pointage AFTER INSERT
ON pointages 
FOR EACH ROW BEGIN 
    set @last_date_pointage = (SELECT last_date_pointage from employes WHERE id = NEW.employe_id);
    if(coalesce(NEW.soft_delete, 0) != 1 and (@last_date_pointage is null or NEW.date_pointage > @last_date_pointage)) then
        begin
            UPDATE employes set last_date_pointage = NEW.date_pointage where id = NEW.employe_id;
        end;
    end if;
END 
| DELIMITER ;

DELIMITER |
CREATE TRIGGER after_delete_pointage AFTER DELETE
ON pointages 
FOR EACH ROW BEGIN 
    set @date_pointage = (SELECT max(date_pointage) from pointages where employe_id = OLD.employe_id and (soft_delete is null or soft_delete = 0)); 
    UPDATE employes set last_date_pointage = @date_pointage where id = OLD.employe_id;
END 
| DELIMITER ;

DELIMITER |
CREATE TRIGGER after_update_pointage
AFTER UPDATE
ON pointages FOR EACH ROW
BEGIN
    if(coalesce(NEW.soft_delete, 0) != coalesce(OLD.soft_delete, 0) or NEW.date_pointage != OLD.date_pointage or NEW.employe_id != OLD.employe_id) then
        begin
            set @date_pointage = (SELECT max(date_pointage) from pointages where employe_id = NEW.employe_id and (soft_delete is null or soft_delete = 0)); 
            UPDATE employes set last_date_pointage = @date_pointage where id = NEW.employe_id;
            if(NEW.employe_id != OLD.employe_id) then
                begin
                    set @old_date_pointage = (SELECT max(date_pointage) from pointages where employe_id = OLD.employe_id and (soft_delete is null or soft_delete = 0)); 
                    UPDATE employes set last_date_pointage = @old_date_pointage where id = OLD.employe_id;
                end;
            end if;
        end;
    end if;
END
| DELIMITER ;