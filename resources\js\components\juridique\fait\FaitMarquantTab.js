import React, { useEffect, useState } from 'react'
import LoadingPage from '../../loading/LoadingPage';
import useToken from '../../util/useToken';
import moment from 'moment';

export default function FaitMarquantTab({id}) {
    const [isLoading, toggleLoading] = useState(true)
    const [fait, setFait] = useState([])

    const updateData = (isMounted) => {
        axios.get("/api/fait_marquant/show/" + id,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else
                    setFait(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }
    useEffect(() => {
        let isMounted = true
        updateData(isMounted)
        return () => { isMounted = false };
    }, []);

    return <>
        {
            isLoading ?
                <LoadingPage/>
            : <>
                {
                    fait && 
                    <div className="card-container">
                        <h3>
                            {fait.objet}
                        </h3>
                        <p className='text' style={{whiteSpace: "pre-line"}}>
                            {fait.commentaire}
                        </p>
                        <br/>
                        <div>
                            Superviseur : <span className='text'>{fait.user_nom} {" <" + fait.user_email + ">"}</span>
                        </div>
                        <div>
                            Le: <span className='text'>{moment(fait.created_at).format("DD MMMM YYYY")}</span>
                        </div>
                    </div>
                }
            </>
        } </>
}
