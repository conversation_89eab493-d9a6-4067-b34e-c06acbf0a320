import React from 'react';

// import UserModal from '../modal/UserModal';
// import axios from 'axios';
// import useToken from '../util/useToken';
import { Link } from 'react-router-dom';

export default function ActionSite({auth, site, /*updateData*/}) {
    // const [showUserModal, toggleUserModal] = useState(false)
    
    // const setSuperviseur = (user) => {
    //     axios.post("/api/site/set_superviseur/" + site.id, {superviseur_id: user.id}, useToken())
    //     .then(res => {
    //         updateData()
    //         toggleUserModal(false)
    //     })
    // }
    
    return <div>
        {/* {
            showUserModal && 
            <UserModal 
                onChange={setSuperviseur}
                closeModal={() => toggleUserModal(false)}
            /> 
        } */}
        <div className='action-container'>
            {
                (["rh", "resp_rh", "resp_sup", "resp_op", "room"].includes(auth.role)) && 
                <span>
                    <Link to={'/site/edit/' + site.id}>Modifier</Link>
                </span>
            }
        </div>
    </div>
}