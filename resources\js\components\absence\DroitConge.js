import React, { useEffect, useState } from 'react';
import moment from 'moment';
import LoadingPage from '../loading/LoadingPage';
import useToken from '../util/useToken';

export default function DroitConge({id}) {
    const [isLoading, toggleLoading] = useState(true)
    const [employe, setEmploye] = useState(null)

    useEffect(() => {
        let isMounted = true
        axios.get('/api/absence/conge_done/' + id, useToken())
        .then((res) => {
            if(isMounted){
                const employe = res.data.employe
                const conges = res.data.conges
                employe.date_begin = employe.societe_id == 1 ? employe.date_confirmation
                 : employe.societe_id == 2 ? employe.date_conf_soit
                 : ''
                 if(employe.date_begin) {
                    employe.droit = (moment().diff(moment(employe.date_begin), 'days') / 30.5 * 2.5)
                        - (conges.map(c => moment(c.retour).diff(moment(c.depart), 'hours')).reduce((a, b) => a+b, 0) / 24)
                 }
                 else {
                    employe.droit = 0
                 }
                if((employe.droit % 1) >= 0.5)
                    employe.droit = Math.round(employe.droit) + 0.5
                else
                    employe.droit = Math.round(employe.droit)
                setEmploye(employe)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })

        return () => { isMounted = false };
    }, []);

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        (
            employe &&
            <div className='card-container'>
                {
                    employe.date_begin &&
                    <div>
                        Date d'embauche : <span className='text'> 
                            {moment(employe.date_begin).format("DD MMM YYYY")}
                        </span>
                    </div>
                }
                {
                    [1, 2].includes(Number.parseInt(employe.societe_id)) &&
                    <div>
                        Droit de congé de l'employe : <span className='text'> 
                            {employe.droit} jour(s)
                        </span>
                    </div>
                }
            </div>
        )
    }</>
}