const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')
const auth = require("./auth")

const db_config_admin = auth.db_config_admin
const pool_admin = mysql.createPool(db_config_admin)

const sqlSelectSite = "SELECT idsite as 'id' from sites where last_visite_id is null and (soft_delete is null or soft_delete = 0) " +
    "and pointage = 1 limit 1"
const sqlSelectLastVisite = "SELECT id FROM visite_postes WHERE site_id = ? ORDER BY date_visite DESC LIMIT 1"
const sqlUpdateSite = "UPDATE sites set last_visite_id = ? where idsite = ? "

function updateData(){
    console.log("\n------")
    pool_admin.query(sqlSelectSite, [], async (err, sites) => {
        if(err){
            console.log("err select site")
            console.error(err)
            waitBeforeUpdate()
        }
        else {
            if(sites.length > 0){
                const site = sites[0]
                console.log("selected site: " + site.id)
                pool_admin.query(sqlSelectLastVisite, [site.id], async (err, visites) => {
                    if(err){
                        console.log("err select visite")
                        console.error(err)
                        waitBeforeUpdate()
                    }
                    else {
                        const visite_id = visites.length > 0 ? visites[0].id : 1
                        console.log("selected visite: " + visite_id)
                        pool_admin.query(sqlUpdateSite, [visite_id, site.id], async (err, res) => {
                            if(err){
                                console.log("err update site")
                                console.error(err)
                            }
                        })
                        waitBeforeUpdate()
                    }
                })
            }
            else {
                waitBeforeUpdate()
                console.log("no site\n...\n\n")
            }
        }
    })
}

function waitBeforeUpdate(){
    setTimeout(() => {
        updateData()
    }, 400)
}

updateData()