import React, { useState } from 'react';
import UserModal from '../modal/UserModal';

export default function InputUser({label, role, roles,value, onChange,isRespPart, required}) {
    const [modalOpen, toggleModal] = useState(false)
    
    const handleSelectUser = (ag) => {
        toggleModal(false)
        onChange(ag)
    }

    const handleCloseModal = () => {
        toggleModal(false)
    }

    return <div>
        <div className='input-container'>
            <label>
                {label ? label : (role == 'superviseur' ? 'Superviseur responsable' : role == 'parent' ? 'Compte parent' : 'Utilisateur')} 
                {required && <span className='danger'>*</span>}
            </label>
            <input
                type="text"
                value={value ? (value.name + " <" + value.email + ">") : ""}
                readOnly
                onClick={() => {toggleModal(true)}}
                />
        </div>
        {
            modalOpen &&
            <UserModal closeModal={handleCloseModal} onChange={handleSelectUser} role={role} roles={roles} isRespPart={isRespPart}/>
        }
    </div>;
}