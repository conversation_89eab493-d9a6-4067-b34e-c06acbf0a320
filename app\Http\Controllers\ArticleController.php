<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Article;
use Illuminate\Support\Facades\DB;

class ArticleController extends Controller
{
    public function index(Request $request){
        return response(Article::where('service', $request->user()->role)->orderBy('name')->get());
    }

    public function all() {
          $articles = DB::select("SELECT `name`, designation, `type`, `service`, nbStock FROM articles WHERE type = \"tenue\"");
          return response(compact('articles'));
    }

    public function detail(Request $request, $name) {
        $article = DB::select("SELECT `name`, designation, `type`, `service`, nbStock FROM articles WHERE type = \"tenue\" AND `name` = ?", [$name])[0];
        return response(compact('article'));
    }

    public function update_stock(Request $request, $name) {
        $article = Article::where('name', $name)->first();
        $nbStockBefore = $article->nbStock;
        $stockExistant = $request->input('stockExistant');
        $stockReel = $request->input('stockReel');
        $article->nbStock = $stockExistant;
        $article->save();
        $nbStockAfter = $article->nbStock;
        $variationStock = [
            "nbStockBefore" => $nbStockBefore,
            "stockReel" => $stockReel,
            "nbStockAfter" => $nbStockAfter,
        ];
        HistoriqueController::action_stock($request, "Mise à jour du stock", $variationStock, $name);
        return response(["message" => "Stock mis à jour"]);
    }

    public function approvisionner(Request $request, $name) {
        $article = Article::where('name', $name)->first();
        $nbStockBefore = $article->nbStock;
        $article->nbStock = $request->input('nbStock');
        $article->save();
        $nbStockAfter = $article->nbStock;
        $variationStock = [
            "nbStockBefore" => $nbStockBefore,
            "nbStockAfter" => $nbStockAfter
        ];
        HistoriqueController::action_stock($request, "Approvisionnement du stock", $variationStock, $name);
        return response(["message" => "Apporvisionnement effectuée"]);
    }

    public function mouvement_tab(Request $request, $name) {
        $tenLastMouvement = DB::select('SELECT meq.id, emp.nom, emp.societe_id, emp.numero_employe, emp.num_emp_saoi, emp.num_emp_soit, emp.numero_stagiaire, meq.date_mouvement 
        FROM mouvement_equipement meq
        LEFT JOIN employes emp ON emp.id = meq.employe_id
        LEFT JOIN ligne_equipement leq ON leq.mouvement_equipement_id = meq.id
        WHERE leq.article = ?
        order by meq.date_mouvement desc
        LIMIT 10', [$name]);

        return response(compact('tenLastMouvement'));
    }

    public static function get_nbStock($name) {
        $nbStock = Article::where('name', $name)->first()->nbStock;
        return $nbStock;
    }

    public static function incrementerStock($name) {
        $article = Article::where('name', $name)->first();
        $article->nbStock += 1;
        $article->save();
        return response(["message" => "Stock mis à jour"]);
    }

    public static function decrementerStock($name) {
        $article = Article::where('name', $name)->first();
        // if ($article->nbStock > 0) {
        //     $article->nbStock -= 1;
        // } else {
        //     $article->nbStock -= 0;
        // }
        $article->nbStock -= 1;
        $article->save();
        return response(["message" => "Stock mis à jour"]);
    }
}
