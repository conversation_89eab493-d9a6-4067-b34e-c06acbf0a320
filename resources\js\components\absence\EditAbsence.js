import React, { useEffect, useState } from 'react';
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import moment from 'moment';
import InputCheckBox from '../input/InputCheckBox';
import InputDatePointage from '../input/InputDatePointage';
import InputText from '../input/InputText';
import LoadingScreen from '../loading/LoadingScreen';
import InputAgent from '../input/InputAgent';
import DualContainer from '../container/DualContainer';
import InputSelect from '../input/InputSelect';
import InputUser from '../input/InputUser';
import InputPointage from '../input/InputPointage';
import { useParams } from 'react-router-dom';
import matricule from '../util/matricule';
moment.locale('fr')

export default function EditAbsence({auth, action, title}) {
    const params = useParams()
    const [me, setMe] = useState(false)
    const [currentType, setCurrentType] = useState()
    const [isLoading, toggleLoading] = useState(true)
    const [employe, setEmploye] = useState(null)
    const [employeRemplacant, setEmployeRemplacant] = useState(null)
    const [employeTemporaire, setEmployeTemporaire] = useState("")
    const [isTmp, setIsTmp] = useState(false)
    const [conge, setConge] = useState(null)
    const [depart, setDepart] = useState()
    const [retour, setRetour] = useState()
    const [motif, setMotif] = useState("")
    const [notification, setNotification] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    const [nbJour, setNbJour] = useState(0)
    const [conditionMisePied, setConditionMisePied] = useState(false);
    const [superviseur, setSuperviseur] = useState(null);
    const [dateService, setDateService] = useState(null);
    const [typeMisePied, setTypeMisePied] = useState("");
    const [absenceSansMotif, setAbsenceSansMotif] = useState(false);
    const [isFictif, toggleFictif] = useState(false);
    
    const miseApiedType = ["BOUTON", "BIOMETRIQUE", "SOMMEIL/RETARD", "TAG", "AUTRE"];
    useEffect(()=>{
        setConditionMisePied((params && params.type == "mis_a_pied") || (currentType && currentType.name == "mis_a_pied"));
    },[params, currentType])

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        let data = {}
        let currentAction = action;
        if (isFictif && currentType.name == "conge") {	
            data = {
                employe_id: (employe ? employe.id : ""),
                nb_jour: nbJour,
                fictif: isFictif,
                type_absence: currentType ? currentType.name : '',
                motif : motif,
            }
            currentAction = "/api/absence/store_fictif";
        }
        else {
            data = {
                type_absence: currentType ? currentType.name : '',
                employe_id: (employe ? employe.id : ""),
                is_tmp : isTmp,
                nb_jour: nbJour? parseFloat(nbJour):null,
                employe_remplacant_id : (employeRemplacant ? employeRemplacant.id : ''),
                employe_temporaire : employeTemporaire,
                depart: depart ? (conditionMisePied ? moment(depart).hour(6).minute(0).second(0).format("YYYY-MM-DD HH:mm:ss") : moment(depart).format("YYYY-MM-DD HH:mm:ss")) : "", 
                retour: retour ? moment(retour).format("YYYY-MM-DD HH:mm:ss") : "",
                date_pointage: dateService ? moment(dateService.date_pointage).format("YYYY-MM-DD HH:mm:ss") : "",
                site_id: (dateService && dateService.site) ? dateService.site.id:'',
                motif: motif,
                superviseur_id: superviseur?superviseur.id:null,
                type_mis_a_pied: typeMisePied ? typeMisePied :null,
                absence_sans_motif: absenceSansMotif,
            }
        }
        axios.post(currentAction + (params.id ? ("/" + params.id) : ""), data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.error)
                setError(res.data.error)
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else
                setNotification(res.data)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }

    useEffect(() => {
        let isMounted = true
        if(params.type){
            toggleLoading(true)
            axios.get('/api/type_absence/show/' + params.type, useToken())
            .then((res) => {
                if(isMounted){
                    setCurrentType(res.data)
                }
                toggleLoading(false)
            })
        }
        return () => { isMounted = false };
    }, [params.type])

    useEffect(() => {
        let isMounted = true
        if(me){
            toggleLoading(true)
            setEmploye(null)
            axios.get('/api/employe/auth', useToken())
            .then((res) => {
                if(isMounted){
                    let employe = res.data
                    if(employe.id) {
                        employe.matricule = matricule(employe)
                        setEmploye(employe)
                    }
                    // else {
                        toggleLoading(false)
                    // }
                }
            })
        }
        return () => { isMounted = false };
    }, [me])

    useEffect(() => {
        let isMounted = true;
        if(params.sanctionId){
            axios.get('/api/sanction/show/' + params.sanctionId, useToken()).then(res => {
                if (isMounted) {
                    let employe = res.data;
                    if(employe.employe_id){
                        employe.matricule = matricule(employe)
                        employe.id = employe.employe_id;
                        employe.nom=employe.employe;
                        setEmploye(employe)
                    }
                    if (employe.sup_nom) setSuperviseur({
                        id: employe.superviseur_id,
                        name: employe.sup_nom,
                        email: employe.sup_email,
                    })
                    if (employe.date_pointage) {
                        setDateService({
                            id: employe.pointage_id,
                            date_pointage: employe.date_pointage,
                            sanction_id: params.sanctionId,
                            site: {
                                id: employe.site_id,
                                nom: employe.site
                            }
                        })
                    }
                    else setAbsenceSansMotif(true)
                    setMotif(res.data.motif);
                }
            }).catch((e) => {
                console.error(e);
            });
        }
        return () => { isMounted = false };
    },[]);
    
    useEffect(() => {
        let isMounted = true
        if(params.id){
            toggleLoading(true)
            axios.get('/api/absence/show/' + params.id, useToken())
            .then((res) => {
                if(isMounted){   
                    const absence = res.data
                    setNbJour(absence.nb_jour)
                    setMotif(absence.motif)
                    setDepart(absence.depart ? moment(absence.depart).toDate() : null)
                    setRetour(absence.retour ? moment(absence.retour).toDate() : null)
                    setCurrentType({name: absence.type_absence})
                    setDateService({date_pointage : absence.date_pointage ? absence.date_pointage:null})
                    setTypeMisePied(absence.type_mis_a_pied ? absence.type_mis_a_pied:null)
                    if (absence.superviseur_id) {
                        setSuperviseur({
                            id: absence.superviseur_id,
                            name: absence.sup_nom,
                            email: absence.sup_email,
                        })
                    }
                    setEmploye({
                        id: absence.employe_id,
                        nom: absence.employe,
                        date_embauche: absence.date_embauche,
                        matricule: (matricule(absence))
                    })
                    setAbsenceSansMotif(absence.absence_sans_motif)
                    if(absence.employe_remplacant_id){
                        setEmployeRemplacant({
                            id: absence.employe_remplacant_id,
                            nom: absence.arp_employe,
                            date_embauche: null,
                            matricule: (matricule(absence))
                        })
                    } else if (absence.employe_temporaire){
                        setIsTmp(true)
                        setEmployeTemporaire(absence.employe_temporaire)
                    }                    
                    toggleLoading(false)
                }
            })
        }
        return () => { isMounted = false };
    }, [])
    
    useEffect(() => {
        let isMounted = true
        if(currentType && currentType.name == "conge" && employe){
            toggleLoading(true)
            axios.get('/api/employe/conge/' + employe.id, useToken())
            .then((res) => {
                if(isMounted){
                    const employe = res.data.employe
                    const conges = res.data.conges
                    employe.date_begin = employe.societe_id == 1 ? employe.date_confirmation
                     : employe.societe_id == 2 ? employe.date_conf_soit
                     : employe.societe_id == 6 ? employe.date_conf_saoi
                     : ''
                     if(employe.date_begin) {
                        employe.droit = (moment().diff(moment(employe.date_begin), 'days') / 30.5 * 2.5)
                            - (conges.map(c => moment(c.retour).diff(moment(c.depart), 'hours')).reduce((a, b) => a+b, 0) / 24)
                    }
                     else {
                        employe.droit = 0
                     }
                    if((employe.droit % 1) >= 0.5)
                        employe.droit = Math.round(employe.droit) + 0.5
                    else
                        employe.droit = Math.round(employe.droit)
                    setConge(employe)
                    toggleLoading(false)
                }
            })
        }
        return () => { isMounted = false };
    }, [employe])

    useEffect(()=>{
        if(depart && nbJour)
            setRetour(moment(depart).add(nbJour * 24, 'hours'))
    },[nbJour, depart])
    
    useEffect(() => {
        if (absenceSansMotif) setTypeMisePied("AUTRE")
    }, [absenceSansMotif])

    return (
        <div id="content">
            {
                notification ? 
                    <Notification next={"/absence/"+ currentType.name + "?id=" + notification.id} message={notification.success}/>
                :
                <div>
                    {
                        isLoading ?
                            <LoadingScreen/>
                        : <div>
                            <div className="title-container">
                                <h2>{title?title :currentType ? currentType.description : ''}</h2>
                            </div>
                            <form onSubmit={handleSubmit}>
                                <div className='card-container'>
                                    <DualContainer>
                                        {!(conditionMisePied) && 
                                            <InputCheckBox label="Pour moi même"
                                                checked={me}
                                                onChange={setMe} 
                                            />
                                        }
                                        {["rh", "resp_rh"].includes(auth.role) && currentType.name == 'conge' &&
                                            <InputCheckBox label="Congé fictif"
                                                checked={isFictif}
                                                onChange={toggleFictif}
                                            />
                                        }
                                    </DualContainer>
                                    {
                                        (me && !employe && !isLoading) &&
                                        <div className='warning-container'>
                                            <span className='danger'>
                                                Contacter le service informatique pour parametrer votre compte
                                            </span>
                                        </div>
                                    }
                                </div>
                                <InputAgent
                                    required
                                    disabled={me || (conditionMisePied && auth.role == 'superviseur')}
                                    value={employe} 
                                    onChange={(value) => setEmploye(value)}
                                />
                                
                                {
                                    !isFictif && conge &&
                                    <DualContainer>
                                        <InputText
                                            disabled
                                            label="Date d'embauche"
                                            value={moment(conge.date_begin).format('DD MMM YYYY')}/>
                                        <InputText
                                            disabled
                                            label="Droit"
                                            value={conge.droit + " jour(s)"}/>
                                    </DualContainer>
                                }
                                
                                {((conditionMisePied && auth && (["rh", "resp_rh", "superviseur"].includes(auth.role))) || isFictif) && 
                                        <InputText type="number" 
                                            label="Nombre de jour"
                                            value={nbJour} 
                                            onChange={setNbJour} 
                                            disabled={auth.role != "rh" && auth.role != "resp_rh"}
                                            required 
                                        />
                                    
                                }
                                {!isFictif && 
                                    <>
                                        {((conditionMisePied && auth && ["rh", "resp_rh"].includes(auth.role) && params.id) || !conditionMisePied)  ?
                                            <DualContainer>
                                                <InputDatePointage
                                                    required={auth.role != "resp_rh" && auth.role != "rh"}
                                                    label="Date de départ"
                                                    value={depart}
                                                    defaultHour={conditionMisePied ? "06:00:00" : null}
                                                    showHour={!conditionMisePied}
                                                    onChange={(dt) => { setDepart(dt) }}
                                                />
                                                <InputDatePointage
                                                    required={auth.role != "resp_rh" && auth.role != "rh"}
                                                    label="Date de retour"
                                                    dateFormat="dd/MM/yyyy hh:mm"
                                                    value={retour}
                                                    onChange={(dt) =>{setRetour(dt)}}
                                                    disabled={conditionMisePied}
                                                />
                                            </DualContainer> 
                                        :
                                            null
                                        }
                                        <DualContainer>
                                            {
                                                (depart && retour && (!conditionMisePied)) &&
                                                <InputText
                                                    disabled
                                                    label="Durée"
                                                    value={moment(retour).diff(moment(depart), 'hours')/24 + " jour(s)"}/>
                                            }
                                            {
                                                (depart && retour && conge) &&
                                                <InputText
                                                    disabled
                                                    label="Solde restants"
                                                    value={conge.droit - (moment(retour).diff(moment(depart), 'hours')/24) + " jour(s)"}/>
                                            }
                                        </DualContainer>
                                        {   (!conditionMisePied || (auth && auth.role !="rh" && auth.role !="resp_rh")) &&
                                            <> 
                                                <div className='field-container'>
                                                    <InputCheckBox label="Remplacé par un employé temporaire" checked={isTmp} onChange={setIsTmp}/>
                                                </div>
                                                {
                                                    (!isTmp) &&
                                                    <InputAgent
                                                        value={employeRemplacant} 
                                                        label="Remplaçant"
                                                        onChange={(value) => setEmployeRemplacant(value)}/>
                                                }
                                                {
                                                    isTmp && 
                                                    <InputText
                                                        label="Employé temporaire"
                                                        value={employeTemporaire}
                                                        onChange={(value) => {setEmployeTemporaire(value)}}/>
                                                }
                                            </>
                                        }
                                        {   
                                            (params.type == 'mis_a_pied' || currentType.name == "mis_a_pied") &&
                                            (auth && ["rh", "resp_rh"].includes(auth.role)) &&
                                                <>
                                                    {employe && 
                                                        <> 
                                                            <div className='field-container'>
                                                                <InputCheckBox label="Sans pointage" checked={absenceSansMotif} onChange={setAbsenceSansMotif} />
                                                            </div>
                                                            {(!absenceSansMotif) &&
                                                                <InputPointage employeId={employe.id} value={dateService} onChange={setDateService} />
                                                            }
                                                        </>
                                                    }
                                                    <InputSelect label="Type" setSelected={setTypeMisePied} selected={typeMisePied} options={miseApiedType} required />
                                                </>
                                        }
                                    </>
                                }
                                
                                {
                                    ((currentType.name == 'mis_a_pied' && ["rh", "resp_rh"].includes(auth.role)) ||
                                    (currentType.name != 'mis_a_pied')) &&
                                        <InputText
                                            required
                                            label="Motif"
                                            value={motif}
                                            onChange={(value) => {setMotif(value)}}
                                        />
                                }
                                    
                                {
                                    conditionMisePied && 
                                    auth && ["rh", "resp_rh"].includes(auth.role) && 
                                    <InputUser role="superviseur" value={superviseur} onChange={setSuperviseur} required/>
                                }
                                
                                {
                                    error &&
                                    <div className='container-error'>
                                        {error}
                                    </div>
                                }
                                <ButtonSubmit disabled={submitDisabled}/>
                            </form>
                        </div>
                    }
                </div>
            }
        </div>
    )
}