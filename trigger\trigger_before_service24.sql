drop trigger IF EXISTS before_update_service24;


DELIMITER |
CREATE TRIGGER before_update_service24
BEFORE UPDATE
ON service24s FOR EACH ROW
BEGIN
    if(COALESCE(NEW.site_id, 0) != COALESCE(OLD.site_id, 0) 
          or COALESCE(NEW.employe_id, 0) != COALESCE(OLD.employe_id, 0)
          or COALESCE(NEW.motif, '') != COALESCE(OLD.motif, '') 
          or NEW.date_pointage != OLD.date_pointage 
          or NEW.begin_pointage != OLD.begin_pointage 
          or NEW.end_pointage != OLD.end_pointage
          or NEW.status != OLD.status
    ) then
          begin
     	     set NEW.admin_updated_at = now();
          end;
     end if;
END
| DELIMITER ;