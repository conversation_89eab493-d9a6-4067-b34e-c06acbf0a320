import {IoMdClose} from 'react-icons/io';
import {AiTwotoneEdit} from 'react-icons/ai'
import EditItem from './EditItem';
import { useState } from 'react';
import ConfirmModal from '../../modal/ConfirmModal';
import './item.css'
import axios from 'axios';
import useToken from '../../util/useToken';
import ActionDaItem from './ActionDaItem';
import useNumberUtil from '../../util/useNumberUtil';

export default function Item({currentAppro, auth, items, setItems, updateData}) {
    const [showDeleteModal, toggleDeleteModal] = useState(false) 
    const [showEditModal, toggleEditModal] = useState(false) 
    const [currentItem, setCurrentItem] = useState(null)

    const total = (
        (items && items.every(item => item.prix)) ? 
            items.map(item => item.quantite*item.prix).reduce((a, b) => a+b, 0).toFixed(2)
        : 
            0
    )

    const handleConfirmDelete = () => {
        if(currentItem.id){
            axios.post('/api/approvisionnement/destroy_da_items/' + currentItem.id, [], useToken())
            .then((res) => {
                if(res.data.success){
                    if(updateData) updateData()
                    toggleDeleteModal(false)
                }                
                else if(res.data.error == "EACCES")
                    setError("Une erreur est survenue.")
                else if(res.data.error)
                    setError(res.data.error)
            })
            .catch((e) => {
                console.error(e)
                setError("Erreur d'envoie, réessayez.")
            })
        }
        else {
            const newItems = items.filter((item, index) => currentItem.index != index)
            setItems(newItems)
            toggleDeleteModal(false)
        }
    }

    const handleDeleteItem = (item, index) => {
        item.index = index
        setCurrentItem(item)
        toggleDeleteModal(true)
    }

    const handleEditItem = (item, index) => {
        item.index = index
        setCurrentItem(item)
        toggleEditModal(true)
    }

    const handleAddItem = () => {
        setCurrentItem(null)
        toggleEditModal(true)
    }

    const showEditAction = (!currentAppro || (currentAppro && auth.id == currentAppro.user_id && currentAppro.status == "draft"))

    return (
        <>
            {
                showEditModal &&
                <EditItem 
                    currentItem={currentItem}
                    currentApproId={currentAppro ? currentAppro.id : ''}
                    items={items}
                    setItems = {setItems}
                    updateData={updateData}
                    closeModal={() => toggleEditModal(false)} 
                />
            }
            {
                showDeleteModal &&
                <ConfirmModal 
                    msg="Voulez-vous vraiment supprimer cette article ?"
                    confirmAction={handleConfirmDelete}
                    closeModal={() => toggleDeleteModal(false)} 
                />
            }
            {
                showEditAction &&
                <div className='tab-list-action'>
                    <div className='action-container'>
                        <span onClick={handleAddItem}>
                            Ajouter un article
                        </span>
                    </div>
                </div>
            }
            <div>
                {
                    items && items.map((item, index) => (
                        <div key={index} className="card-container">
                            <div className='item-container'>
                                <span>
                                    {item.designation}
                                    <span className='secondary'>
                                        {
                                            " [" +
                                            (
                                                item.price_only ? 
                                                    item.prix + " Ar"
                                                : item.prix ? 
                                                    item.prix + " x " + item.quantite + " " + item.unite + " : " + useNumberUtil(item.quantite * item.prix) + ""
                                                :
                                                    item.quantite + " " + item.unite
                                            )
                                            + ( 
                                                (item.cout && item.prix && (item.cout != item.prix*item.quantite)) ? (" -> "  + item.cout + " Ar") : ""
                                            )
                                            + "]"
                                        }
                                    </span>
                                </span>
                                {
                                    showEditAction &&
                                    <div>
                                        <span onClick={() => handleEditItem(item, index)}>
                                            <AiTwotoneEdit size={20}/>
                                        </span>
                                        <span onClick={() => handleDeleteItem(item, index)}>
                                            <IoMdClose size={20}/>
                                        </span>
                                    </div>
                                }
                                {
                                    (!showEditAction && currentAppro && ["compta", "daf"].includes(auth.role) && ["traite"].includes(currentAppro.status)) && 
                                    (
                                        item.done !== null ?
                                            <div>
                                                {
                                                    item.done ? 
                                                        <span className='badge-outline'>Fait</span>
                                                    :
                                                        <span className='badge-outline'>Annulé</span>
                                                }
                                            </div>
                                        :
                                            <div>
                                                <ActionDaItem article={item} updateData={updateData}/>
                                            </div>
                                    )
                                }
                            </div>
                        </div>
                    ))
                }
                {
                    total != 0 &&
                    <div className="card-container right">
                        <h3 className={(currentAppro && currentAppro.total) ? 'secondary' : ''}>Sous-total : <span>{useNumberUtil(total)}</span></h3>
                    </div>
                }
                {
                    (currentAppro && currentAppro.total > 0) &&
                    <h3 style={{textAlign: 'right',padding: '0 10px'}}>
                        Montant payé : <span>
                            {useNumberUtil(currentAppro.total)}
                        </span>
                    </h3>
                }
            </div>
        </>
    )
}
