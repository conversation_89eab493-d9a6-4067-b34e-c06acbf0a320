import React from 'react';

export default function Textarea({label, value, onChange, row, required}) {
    return <div className='input-container'>
        {
            label &&
            <label>{label} {required && <span className='danger'>*</span>}</label>
        }
        <textarea
            onChange={(e) => {onChange(e.target.value)}}
            value={value}
            rows={row ? row : "5"}
        >
        </textarea>
    </div>;
}