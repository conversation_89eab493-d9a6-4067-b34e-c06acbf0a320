import React from 'react';
import { CgClose } from 'react-icons/cg'
import { IoMdArrowBack } from 'react-icons/io';

export default function ShowHeader({size, label, id, closeDetail}) {
    return <div className="padding-container">
        {
            size == "sm" ?
                <div className='vertical-align'>
                    <div style={{paddingRight: "10px"}} onClick={() => closeDetail()}>
                        <IoMdArrowBack className='secondary' size={26}/>
                    </div>
                        <h2>
                            {label} <span className='secondary'>/ Ref : {("00000" + id).slice(-6)}</span>
                        </h2>
                </div>
            :
                <div className='space-between'>
                    <h2>
                        <span >Ref : {("00000" + id).slice(-6)}</span>
                    </h2>
                    <CgClose className='secondary' size={30} onClick={() => closeDetail()}/>
                </div>
        }
    </div>;
}
