import React, { useState } from 'react';

import Textarea from '../input/Textarea';
import InputText from '../input/InputText';
import axios from 'axios';
import useToken from '../util/useToken';

export default function DoneApproModal({action, updateData, closeModal}) {
    const [note, setNote] = useState("")
    const [total, setTotal] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleOk = () => {
        disableSubmit(true)
        setError("")
        const data = {
            note: note,
            total: total
        }
        axios.post(action.request, data, useToken())
        .then((res) => {
            if(res.data.error){
                setError(res.data.error)
                disableSubmit(false)
            }
            else if(res.data.success){
                closeModal()
                updateData()
            }
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
        })
    }
    return <div className='modal'>
        <div>
            <h3>{action.header}</h3>
            <InputText
                required={action.required}
                label="Coût total"
                value={total}
                onChange={setTotal}/>
            <Textarea value={note} label="Commentaire" onChange={(value) => setNote(value)} />
            {
                error &&
                <div className='container-error'>
                    {error}
                </div>
            }
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Envoyer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
    
}
