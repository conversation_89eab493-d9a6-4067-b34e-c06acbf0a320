import React, { useState, lazy, Suspense } from 'react';
import ReactDOM from 'react-dom';
import {
    BrowserRouter,
    Routes,
    Route,
} from "react-router-dom"

import './app.css'
import './list.css'
import './input/input.css'
import './input/modal.css'
import './input/checkbox.css'
import './input/search.css'
import './loading/loading.css'
import './loading/loading_page.css'
import './home/<USER>'
import './mail/mail.css'

import PrivateRoute from './route/PrivateRoute'
import GuestRoute from './route/GuestRoute'
import MessageRoute from './route/MessageRoute'
import LoadingScreen from './loading/LoadingScreen';

import moment from 'moment';
import ErrorBoundary from './ErrorBoundary';
import EditSite from './site/EditSite';
import ShowPartNonFait from './partvariable_nonfait/ShowPartNonFait';
import Dashboard from './Dashboard/Dashboard';
moment.locale("fr")

const Login = lazy(() => import('./login/Login'))
const EditSanction = lazy(() => import('./sanction/EditSanction'))
const View = lazy(() => import('./layout/main/View'))
const ShowSanction = lazy(() => import('./sanction/ShowSanction'))
const EditPrime = lazy(() => import('./prime/EditPrime'))
const ShowPrime = lazy(() => import('./prime/ShowPrime'))
const ShowVisitePoste = lazy(() => import('./visite/ShowVisitePoste'))
const EditVisitePoste = lazy(() => import('./visite/EditVisitePoste'))
const ShowFaitMarquant = lazy(() => import('./fait/ShowFaitMarquant'))
const EditFaiMarquant = lazy(() => import('./fait/EditFaitMarquant'))
const TypeSav = lazy(() => import('./sav/TypeSav'))
const EditSav = lazy(() => import('./sav/EditSav'))
const ShowSav = lazy(() => import('./sav/ShowSav'))
const EditFlotte = lazy(() => import('./flotte/EditFlotte'))
const ShowFlotte = lazy(() => import('./flotte/ShowFlotte'))
const TypeEquipement = lazy(() => import('./equipement/TypeEquipement'))
const EditEquipement = lazy(() => import('./equipement/EditEquipement'))
const ShowEquipement = lazy(() => import('./equipement/ShowEquipement'))
const Home = lazy(() => import('./home/<USER>'))
const Me = lazy(() => import('./home/<USER>'))
const EditAbsence = lazy(() => import('./absence/EditAbsence'))
const ShowAbsence = lazy(() => import('./absence/ShowAbsence'))
const TypeAbsence = lazy(() => import('./absence/TypeAbsence'))
const ResetPassword = lazy(() => import('./login/ResetPassword'))
const EditUser = lazy(() => import('./user/EditUser'))
const ShowUser = lazy(() => import('./user/ShowUser'))
const User = lazy(() => import('./user/User'))
const ShowApprovisionnement = lazy(() => import('./approvisionnement/ShowApprovisionnement'))
const EditApprovisionnement = lazy(() => import('./approvisionnement/EditApprovisionnement'))
const ItemEmploye = lazy(() => import('./employe/item/ItemEmploye'))
const PieceJointe = lazy(() => import('./piecejointe/PieceJointe'))
const NotificationView = lazy(() => import('./notification/NotificationView'))
const EditEmploye = lazy(() => import('./employe/EditEmploye'))
const EditRecrutement = lazy(() => import('./recrutement/EditRecrutement'))
const EditJuridique = lazy(() => import('./juridique/EditJuridique'))
const EditPlainte = lazy(() => import('./plainte/EditPlainte'))
const EditPaie = lazy(() => import('./paie/EditPaie'))
const EditReclamation = lazy(() => import('./reclamation/EditReclamation'))
const EditDeduction = lazy(() => import('./Deduction/EditDeduction'))
const EditAvance = lazy(() => import('./avance/EditAvance'))
const EditPartVariable = lazy(() => import('./partvariable/EditPartVariable'))
const ShowPartVariable = lazy(() => import('./partvariable/ShowPartVariable'))
const Service24Route = lazy(() => import('./route/Service24Route'))
const EditService24 = lazy(() => import('./service24/EditService24'))
const EditMessage = lazy(() => import('./message/EditMessage'))
const EditSatisfaction = lazy(() => import('./satisfaction/EditSatisfaction'))
const ShowSatisfaction = lazy(() => import('./satisfaction/ShowSatisfaction'))
const EditPlanning = lazy(() => import('./planning/EditPlanning'))
const EditModelMessage = lazy(() => import('./model_message/EditModelMessage'))
const EditDotation = lazy(() => import('./dotation/EditDotation'))

const isLocalhost = window.location.href.includes('localhost');
if (isLocalhost) {
    document.getElementById('pageTitle').textContent = "Localhost - ADMIN"
}

export default function App() {
    const [auth, setAuth] = useState()
    return <Suspense fallback={<LoadingScreen/>}>
        <BrowserRouter>
            <Routes>
                <Route path="/" element={<GuestRoute auth={auth} setAuth={(value) => setAuth(value)}/>}>
                    <Route path='login'  element={<Login/>}/>
                    <Route path="message" element={<MessageRoute auth={auth} setAuth={setAuth}/>}>
                        <Route index element={<View name="message" auth={auth}/>}/>
                        <Route path='sent-unread' element={<View name="message-sent" auth={auth}/>}/>
                        <Route path='add' element={<EditMessage auth={auth} />} />
                        <Route path='reply/:id' element={<EditMessage auth={auth} title="Répondre" />} />
                        <Route path="model">
                            <Route index element={<View name="model-message" auth={auth}/>}/>
                            <Route path='update/:id' element={<EditModelMessage auth={auth} title="Modification de model"/>} />
                            <Route path='add' element={<EditModelMessage auth={auth} title="Nouveau model" />} />
                        </Route>
                    </Route>
                    <Route path="/" element={<PrivateRoute auth={auth}/>}>
                        <Route index element={<Home auth={auth}/>}/>
                        <Route path='dashboard' element={<Dashboard auth={auth} />} />
                        <Route path="me" element={<Me auth={auth}/>} />
                        <Route path='change_password' element={<ResetPassword auth={auth}/>}/>
                        <Route path='notification' element={<NotificationView auth={auth}/>}/>
                        <Route path='equipement'>
                            <Route path='add' element={<TypeEquipement auth={auth}/>}/>
                            <Route path='add/:type' element={<EditEquipement auth={auth} action={"/api/equipement/add"}/>}/>
                            <Route 
                                path='send_back/:id' 
                                element= {<EditEquipement auth={auth} action="/api/equipement/send_back" title="Renvoie de la demande"/>}
                            />
                            <Route path="show/:id" element={<ShowEquipement auth={auth}/>}/>
                            <Route index element={<View auth={auth} name="equipement"/>}/>
                        </Route>

                        <Route path='reclamation'>
                            <Route path='add' element={<EditReclamation auth={auth} title="Nouveau réclamation" action="/api/reclamation/add"/>}/>
                            <Route path='edit/:id' element={<EditReclamation auth={auth} title="Modifier la réclamation" action="/api/reclamation/update"/>}/>
                            <Route index element={<View name="reclamation" auth={auth}/>}/>
                        </Route>

                        <Route path='site'>
                            <Route index element={<View name="site" auth={auth}/>}/>
                            <Route path='edit/:id' element={<EditSite auth={auth} title="Modification du site"/>}/>
                        </Route>

                        <Route path='absence'>
                            <Route path='add' element={<TypeAbsence auth={auth}/>}/>
                            <Route 
                                path="add/:type" 
                                element={<EditAbsence auth={auth} action="/api/absence/add"/>}
                            />
                            <Route path="show/:id" element={<ShowAbsence auth={auth}/>}/>
                            <Route 
                                path='send_back/:id' 
                                element= {<EditAbsence action="/api/absence/send_back" title="Renvoie de la demande" auth={auth}/>}
                            />
                            <Route path='edit/:id' element = {<EditAbsence action="/api/absence/edit" title="Modification" auth={auth} />} />
                            <Route path='apply/:id' element={<EditAbsence action="/api/absence/apply_mise_a_pied" title="Application" auth={auth}/>} />
                            <Route path=':type' element={<View auth={auth} name="absence"/>}/>
                        </Route>

                        <Route path='sav'>
                            <Route path='add' element={<TypeSav auth={auth}/>}/>
                            <Route 
                                path="add/:type" 
                                element={<EditSav auth={auth} action="/api/sav/add" title="Nouveau SAV"/>}
                            />
                            <Route path="show/:id" element={<ShowSav auth={auth}/>}/>
                            <Route 
                                path='edit/:id' 
                                element={<EditSav auth={auth} action="/api/sav/edit" title="Modification du SAV"/>}
                            />
                            <Route 
                                path='do_traite/:id' 
                                element={<EditSav auth={auth} action="/api/sav/do_traite" title="Traitement du SAV"/>}
                            />
                            <Route 
                                path='send_back/:id' 
                                element= {<EditSav auth={auth} action="/api/sav/send_back" title="Renvoie de la demande"/>}
                            />
                            <Route path='controlroom' element={<View auth={auth} name="sav"/>}/>
                            <Route path=':type' element={<View auth={auth} name="sav"/>}/>
                        </Route>

                        <Route path='flotte'>
                            <Route 
                                path="add" 
                                element={<EditFlotte auth={auth} action="/api/flotte/add" title="Demande ou problème de flotte"/>}
                            />
                            <Route path="show/:id" element={<ShowFlotte auth={auth}/>}/>
                            <Route 
                                path='edit/:id' 
                                element={<EditFlotte auth={auth} action="/api/flotte/edit" title="Modification de la demande de Flotte"/>}
                            />
                            <Route 
                                path='do_traite/:id' 
                                element={<EditFlotte auth={auth} action="/api/flotte/do_traite" title="Traitement de la demande de Flotte"/>}
                            />
                            <Route 
                                path='send_back/:id' 
                                element= {<EditFlotte auth={auth} action="/api/flotte/send_back" title="Renvoie de la demande"/>}
                            />
                            <Route index element={<View auth={auth} name="flotte"/>}/>
                        </Route>

                        <Route path='sanction'>
                            <Route 
                                path="add" 
                                element={<EditSanction auth={auth} action="/api/sanction/add" title="Nouvelle sanction"/>}
                            />
                            <Route 
                                path="add/:type/:sanctionId" 
                                element={<EditAbsence auth={auth} action="/api/absence/add" title="Demande de mise à pied"/>}
                            />
                            <Route 
                                path='edit/:id' 
                                element={<EditSanction auth={auth} action="/api/sanction/edit" title="Modification de la sanction"/>}
                            />
                            <Route 
                                path='do_traite/:id' 
                                element={<EditSanction auth={auth} action="/api/sanction/do_traite" title="Traitement de la sanction"/>}
                            />
                            <Route 
                                path='do_convocation/:id' 
                                element= {<EditSanction auth={auth} action="/api/sanction/do_convocation" title="Convocation de l'employe"/>}
                            />
                            <Route 
                                path='send_back/:id' 
                                element= {<EditSanction auth={auth} action="/api/sanction/send_back" title="Renvoie de la demande"/>}
                            />
                            <Route path=":type/:id" element={<ItemEmploye auth={auth}/>}/>
                            <Route path="show/:id" element={<ShowSanction auth={auth}/>}/>
                            <Route path='controlroom' element={<View auth={auth} name="sanction"/>}/>
                            <Route index element={<View auth={auth} name="sanction"/>}/>
                        </Route>

                        <Route path='prime'>
                            <Route 
                                path="add" 
                                element={<EditPrime auth={auth} action="/api/prime/add" title="Nouvelle prime"/>}
                            />
                            <Route 
                                path='edit/:id' 
                                element={<EditPrime auth={auth} action="/api/prime/edit" title="Modification de la prime"/>}
                            />
                            <Route 
                                path='do_validation/:id' 
                                element={<EditPrime auth={auth} action="/api/prime/reply_validation" title="Validation de la prime"/>}
                            />
                            <Route 
                                path='do_traite/:id' 
                                element={<EditPrime auth={auth} action="/api/prime/do_traite" title="Traitement de la prime"/>}
                            />
                            <Route 
                                path='do_convocation/:id' 
                                element= {<EditPrime auth={auth} action="/api/prime/do_convocation" title="Convocation de l'employe"/>}
                            />
                            <Route 
                                path='send_back/:id' 
                                element= {<EditPrime auth={auth} action="/api/prime/send_back" title="Renvoie de la demande"/>}
                            />
                            <Route path=":type/:id" element={<ItemEmploye auth={auth}/>}/>
                            <Route path="show/:id" element={<ShowPrime auth={auth}/>}/>
                            <Route index element={<View name="prime" auth={auth}/>}/>
                        </Route>

                        <Route path='visite-poste'>
                            <Route path="add" element={<EditVisitePoste/>}/>
                            <Route path="show/:id" element={<ShowVisitePoste auth={auth}/>}/>
                            <Route index element={<View name="visite" auth={auth}/>}/>
                        </Route>

                        <Route path='part-variable'>
                            <Route path="add" element={<EditPartVariable title="Nouvelle part variable" action="/api/part_variable/add" auth={auth}/>}/>
                            <Route path="edit/:id" element={<EditPartVariable auth={auth} action="/api/part_variable/update" title="Modifier la part variable"/>}/>
                            <Route path="show/:id" element={<ShowPartVariable auth={auth}/>}/>
                            <Route index element={<View name="part" auth={auth}/>}/>
                        </Route>

                        <Route path='part-non-fait'>
                            <Route path="show/:id" element={<ShowPartNonFait auth={auth}/>}/>
                            <Route index element={<View name="part-non-fait" auth={auth}/>}/>
                        </Route>

                        <Route path='planning-non-fait'>
                            {/* <Route path="show/:id" element={<ShowPartVariableNonFait auth={auth}/>}/> */}
                            <Route index element={<View name="planning-non-fait" auth={auth}/>}/>
                        </Route>
                        <Route path='non-fait-manager'>
                            {/* <Route path="show/:id" element={<ShowPartVariableNonFait auth={auth}/>}/> */}
                            <Route index element={<View name="planning-non-fait-manager" auth={auth}/>}/>
                        </Route>

                        <Route path='fait-marquant'>
                            <Route path="add" element={<EditFaiMarquant auth={auth}/>}/>
                            <Route path="show/:id" element={<ShowFaitMarquant auth={auth}/>}/>
                            <Route index element={<View name="fait" auth={auth}/>}/>
                        </Route>

                        <Route path='user'>
                            <Route path="add" element={<EditUser auth={auth} action="/api/user/add" title="Ajout d'utilisateur"/>}/>
                            <Route path="edit/:id" element={<EditUser auth={auth} action="/api/user/edit" title="Modification d'utilisateur"/>}/>
                            <Route path="show/:id" element={<ShowUser auth={auth}/>}/>
                            <Route path=':status' element={<User auth={auth}/>}/>
                            <Route index element={<View name="user" auth={auth}/>}/>
                        </Route>

                        <Route path='da'>
                            <Route path="add" element={<EditApprovisionnement auth={auth} action="/api/approvisionnement/add" title="Nouvelle DA"/>}/>
                            <Route 
                                path="edit/:id" 
                                element={<EditApprovisionnement auth={auth} action="/api/approvisionnement/edit" title="Modification de la DA"/>}/>
                            <Route path="show/:id" element={<ShowApprovisionnement auth={auth}/>}/>
                            <Route index element={<View name="da" auth={auth}/>}/>
                        </Route>

                        <Route path='pj'>
                            <Route path=":type/:id" element={<ItemEmploye auth={auth}/>}/>
                            <Route index element={<PieceJointe auth={auth}/>}/>
                        </Route>

                        <Route path='employe'>
                            <Route path="add" element={<EditEmploye auth={auth} action="/api/employe/add" title="Nouveau employé"/>}/>
                            <Route 
                                path="edit/:id" 
                                element={<EditEmploye auth={auth} action="/api/employe/update/" title="Modification"/>}/>
                            <Route index element={<View name="employe" auth={auth}/>}/>
                        </Route>
                        <Route path='recrutement'>
                            <Route path="add" element={<EditRecrutement auth={auth} action="/api/recrutement/add" title="Nouveau recrutement"/>}/>
                            <Route 
                                path="edit/:id" 
                                element={<EditRecrutement auth={auth} action="/api/recrutement/update/" title="Modification"/>}/>
                            <Route index element={<View name="recrutement" auth={auth}/>}/>
                        </Route>
                        <Route path='dotation'>
                            <Route path='add/:type' element={<EditDotation auth={auth} title="Nouveau dotation"/>}/>
                            <Route index element={<View name="dotation" auth={auth} />} />
                        </Route>
                        <Route path='stock'>
                            <Route index element={<View name="stock" auth={auth} />} />
                        </Route>
                        <Route path='badge'>
                            <Route index element={<View name="badge" auth={auth} />} />
                        </Route>

                        <Route path='recouvrement'>
                            <Route path="add" element={<EditJuridique recouvrement action="/api/juridique/add" title="Nouveau recouvrement"/>}/>
                            <Route path="unread" element={<View auth={auth} name="unread_recouvrement"/>}/>
                            <Route 
                                path="edit/:id" 
                                element={<EditJuridique recouvrement action="/api/juridique/edit" title="Modification recouvrement"/>}/>
                            <Route index element={<View auth={auth} name="recouvrement"/>}/>
                        </Route>
                        <Route path='plainte'>
                            <Route path="add" element={<EditPlainte action="/api/plainte/add" title="Nouveau plainte"/>}/>
                            <Route path="unread" element={<View auth={auth} name="unread_plainte"/>}/>
                            <Route 
                                path="edit/:id" 
                                element={<EditPlainte action="/api/plainte/edit" title="Modification plainte"/>}/>
                            <Route index element={<View auth={auth} name="plainte"/>}/>
                        </Route>
                        <Route path='paie'>
                            <Route index element={<View name="paie" auth={auth} />} />
                            <Route path='add' element={<EditPaie auth={auth} action="/api/paie/store" title={"Nouvelle paie"} />} />
                            <Route path='edit/:id' element={<EditPaie auth={auth} action="/api/paie/edit/" title="Modification de la paie" modify />} />
                            <Route path='edit_done/:id' element={<EditPaie auth={auth} action="/api/paie/edit_done/" title="Traitement de la paie" modify />} />
                            <Route path='do_traite/:id' element={<EditPaie auth={auth} action="/api/paie/do_traite/" title="Traitement de la paie" modify />} />
                            <Route path='send_back/:id' element={<EditPaie auth={auth} action="/api/paie/send_back/" title="Refaire" modify />} />
                        </Route>
                        
                        <Route path='appelle'>
                            <Route index element={<View name="appelle" auth={auth} />} />
                        </Route> 

                        <Route path='rappel'>
                            <Route index element={<View name='rappel' auth={auth} />} />
                        </Route>
                       
                        <Route path='deduction'>
                            <Route index element={<View name="deduction" auth={auth} />} />
                        <Route path='add' element={<EditDeduction auth={auth} action="/api/deduction/store" title={"Nouvelle Déduction"} />} />
                            <Route path='do_traite/:id' element={<EditDeduction auth={auth} action="/api/deduction/do_traite/" title={"Traitement"} />} />
                            <Route path='edit/:id' element={<EditDeduction auth={auth} action="/api/deduction/edit/" title={"Modification"} />} />
                            <Route path='send_back/:id' element={<EditDeduction auth={auth} action="/api/deduction/send_back/" title={"Renvoyer"} />} />
                        </Route> 

                        <Route path='avance'>
                            <Route index element={<View name="avance" auth={auth} />} />
                            <Route path='add' element={<EditAvance auth={auth} action="/api/avance/store" title="Nouvelle avance" />} />
                            <Route path='edit/:id' element={<EditAvance auth={auth} action="/api/avance/edit/" title={"Modification"} />} />
                            <Route path='do_traite/:id' element={<EditAvance auth={auth} action="/api/avance/do_traite/" title="Traitement" />} /> 
                            <Route path='send_back/:id' element={<EditAvance auth={auth} action="/api/avance/send_back/" title="Renvoie de demande d'avance" />} />
                        </Route> 
                        
                        <Route path='service24' element={<Service24Route auth={auth} />} >
                            <Route index element={<View name="service24" auth={auth} />} />
                            <Route path='add' element={<EditService24 auth={auth} action="/api/service24/store" title="Nouveau service24" />} />
                            <Route path='send_back/:id' element={<EditService24 auth={auth} action="/api/service24/send_back/" title={"Renvoie de demande"} />} />
                        </Route>

                        <Route path='satisfaction'>
                            <Route index element={<View name="satisfaction" auth={auth} />} />
                            <Route path='add' element={<EditSatisfaction auth={auth} action="/api/satisfaction/store" title="Nouveau satisfaction" />} />
                            <Route path='edit/:id' element={<EditSatisfaction auth={auth} action="/api/satisfaction/edit/" title={"Modification"} />} />
                            <Route path='show/:id' element={<ShowSatisfaction auth={auth} />} />
                        </Route>

                        <Route path='planning'>
                            <Route index element={<View name="planning" auth={auth} />} />
                            <Route path='add' element={<EditPlanning auth={auth} title="Nouveau planning" />} />
                            <Route path='edit/:id' element={<EditPlanning auth={auth} isEdit title="Modification planning" />} />
                            <Route path='duplicate/:id' element={<EditPlanning auth={auth} toDuplicate title="Duplication planning" />} />
                        </Route>

                        <Route path='anomalie'>
                            <Route index element={<View name="anomalie" auth={auth} />} />
                        </Route>
                        
                        <Route path='anomalie-planning'>
                            <Route index element={<View name="anomalie-planning" auth={auth} />} />
                        </Route>

                        <Route path='anomalie-service'>
                            <Route index element={<View name="anomalie-service" auth={auth} />} />
                        </Route>

                    </Route>
                </Route>
            </Routes>
        </BrowserRouter>
    </Suspense>
}

if (document.getElementById('app')) {
    ReactDOM.render(
        <ErrorBoundary>
            <App />
        </ErrorBoundary>
    , document.getElementById('app'));
}
