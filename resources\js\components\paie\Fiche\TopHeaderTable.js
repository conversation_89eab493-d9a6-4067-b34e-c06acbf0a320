import React, { Fragment } from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";
import moment from "moment";
import matricule from '../../util/matricule';
const styles = StyleSheet.create({
    row: {
        flexDirection: "row",
        alignItems: "center",
        fontSize: 7,
        lineHeight: 1,
    },

    rubrique: {
        width: "41%",
        textAlign: "left",
        borderRightWidth: 1,
        paddingTop: 4,
        paddingBottom: 4,
        marginLeft:2,
    },
    chargesPatronale: {
        width: "59%",
        textAlign: "left",
        padding: 4,
    },
    chargesPatronal: {
        width: "59%",
        textAlign: "left",
        padding: 4,
        flexDirection: "row",
        justifyContent: "space-between",
    },
    nameAgence: {
        width: "59%",
        textAlign: "left",
        padding: 4,
        // borderRightWidth: 3,
        flexDirection: "row",
        justifyContent: "space-between",
    },
});

const TopHeaderTable = ({ items, siret, setSiret }) => {
    const rows = (
        <View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>
                    Matricule :
                    {matricule(items)}
                </Text>
                <View style={styles.chargesPatronale}>
                    <Text>Sécurité sociale : </Text>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>
                    {items.fonction_designation}
                </Text>
                <View style={styles.chargesPatronal}>
                    <Text>APE </Text>
                    <Text>SIRET : {siret} </Text>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>
                    Qualification :{items.categorie}
                </Text>
                <Text style={styles.chargesPatronale}> {items.nom}</Text>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>
                    Entrée le :{" "}
                    {moment(items.date_embauche).format("DD/MM/YYYY")}
                </Text>
                <Text style={styles.chargesPatronale}> </Text>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>
                    Heures contrat : {items.nb_heure_contrat} {" heures"}
                </Text>
                <Text style={styles.chargesPatronale}>
                    {" "}
                    {items.code_agence
                        ? items.code_agence
                        : items.nom_agence
                        ? items.nom_agence
                        : "non definie"}
                </Text>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Plafond CNAPS :1 910 400.00</Text>
                <Text style={styles.chargesPatronale}> </Text>
            </View>
        </View>
    );
    return <Fragment>{rows}</Fragment>;
};
export default TopHeaderTable;
