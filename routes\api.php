<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\SearchController;
use App\Http\Controllers\StatusController;
use App\Http\Controllers\Auth\UserAuthController;
use App\Http\Controllers\EmployeController;
use App\Http\Controllers\ApprovisionnementController;
use App\Http\Controllers\DaItemController;
use App\Http\Controllers\SanctionController;
use App\Http\Controllers\PrimeController;
use App\Http\Controllers\PointageController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SiteController;
use App\Http\Controllers\VisitePosteController;
use App\Http\Controllers\FaitMarquantController;
use App\Http\Controllers\SeenController;
use App\Http\Controllers\SavController;
use App\Http\Controllers\TypeSavController;
use App\Http\Controllers\FlotteController;
use App\Http\Controllers\TypeEquipementController;
use App\Http\Controllers\EquipementController;
use App\Http\Controllers\ArticleEquipementController;
use App\Http\Controllers\ArticleController;
use App\Http\Controllers\AbsenceController;
use App\Http\Controllers\TypeAbsenceController;
use App\Http\Controllers\MailController;
use App\Http\Controllers\PieceJointeController;
use App\Http\Controllers\HistoriqueController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\ImmatriculationController;
use App\Http\Controllers\FonctionController;
use App\Http\Controllers\AgenceController;
use App\Http\Controllers\AvanceController;
use App\Http\Controllers\DeductionController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\AlarmController;
use App\Http\Controllers\AnomaliePlanningController;
use App\Http\Controllers\AnomalieController;
use App\Http\Controllers\AnomalieService24Controller;
use App\Http\Controllers\JuridiqueController;
use App\Http\Controllers\SuiviJuridiqueController;
use App\Http\Controllers\ReclamationController;
use App\Http\Controllers\PaieController;
use App\Http\Controllers\PointageReclamationController;
use App\Http\Controllers\HierarchieController;
use App\Http\Controllers\CriterePartController;
use App\Http\Controllers\PartVariableController;
use App\Http\Controllers\CritereMensuelController;
use App\Http\Controllers\SatisfactionController;
use App\Http\Controllers\Service24Controller;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\ModelMessageController;
use App\Http\Controllers\PlanningController;
use App\Http\Controllers\EtapeContentieuxController;
use App\Http\Controllers\PlainteAgentController;
use App\Http\Controllers\MouvementEquipementController;
use App\Http\Controllers\LigneEquipementController;
use App\Http\Controllers\AppelleController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\RecrutementController;
use App\Http\Controllers\RegionUsersController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group([
    'middleware' => ['auth:api', 'trim', 'cors']
    ], function() {

        Route::get('/auth', [UserAuthController::class, 'auth']);
        Route::post('/logout', [UserAuthController::class, 'logout']);
        Route::post('/change_password', [UserAuthController::class, 'change_password']);
        Route::post('/send_email', [MailController::class, 'send']);

        Route::get('/validation', [HomeController::class, 'validation']);
        Route::get('/tech', [HomeController::class, 'tech']);
        Route::get('/electronique', [HomeController::class, 'electronique']);
        Route::get('/rh', [HomeController::class, 'rh']);
        Route::get('/achat', [HomeController::class, 'achat']);
        Route::get('/operation', [HomeController::class, 'operation']);
        Route::get('/access', [HomeController::class, 'access']);
        Route::get('/daf', [HomeController::class, 'daf']);
        Route::get('/admin', [HomeController::class, 'admin']);
        Route::get('/room', [HomeController::class, 'room']);
        Route::get('/juridique_role', [HomeController::class, 'juridique']);
        Route::get('/superviseur', [HomeController::class, 'superviseur']);
        Route::get('/home', [HomeController::class, 'index']);

        Route::get('/dashboard', [DashboardController::class, 'index']);

        Route::get('/search', [SearchController::class, 'index']);

        Route::get('/status', [StatusController::class, 'index']);
        Route::get('/status/{name}', [StatusController::class, 'show']);

        Route::get('/etape_contentieux', [EtapeContentieuxController::class, 'index']);

        Route::post('/piece_jointe/add', [PieceJointeController::class, 'store_and_join']);
        Route::post('/piece_jointe/edit/{type}/{id}', [PieceJointeController::class, 'edit']);
        Route::post('/piece_jointe/add/{type}', [PieceJointeController::class, 'store_and_show']);
        Route::delete('/piece_jointe/destroy/{type}/{id}/{historique_id}', [PieceJointeController::class, 'destroy']);
        Route::get('/piece_jointe', [PieceJointeController::class, 'index']);
        Route::get('/piece_jointe/tab', [PieceJointeController::class, 'tab']);

        Route::get('/historique/tab', [HistoriqueController::class, 'tab']);

        Route::post('/site/set_superviseur/{id}', [SiteController::class, 'set_superviseur']);
        Route::post('/site/edit/{id}', [SiteController::class, 'edit']);
        Route::get('/site/childs/{id}', [SiteController::class, 'childs']);
        Route::post('/site/add_child/{id}', [SiteController::class, 'add_child']);
        Route::get('/site/show/{id}', [SiteController::class, 'show']);
        Route::get('/site/search', [SiteController::class, 'search']);
        Route::get('/site/search_have_pointeuse/{id}', [SiteController::class, 'search_have_pointeuse']);
        Route::get('/site/search_by_resp_sup', [SiteController::class, 'search_by_resp_sup']);
        Route::get('/site/search_with_pointage', [SiteController::class, 'search_with_pointage']);
        Route::get('/site/search_site_planning', [SiteController::class, 'search_site_planning']);
        Route::get("/site/un_finished",[SiteController::class, 'index_unfinished_planning']);
        Route::get("/site/show_unfinished/{id}",[SiteController::class, 'show_unfinished_planning']);
        Route::get('/site/export', [SiteController::class, 'export']);
        Route::get('/site/alarm/{id}', [SiteController::class, 'alarm']);
        Route::post('/site/store_horaire/{id}', [SiteController::class, 'store_horaire']);
        Route::get('/site', [SiteController::class, 'index']);

        Route::get('/pointage/employe_input/{id}', [PointageController::class, 'employe_input']);
        Route::get('/pointage/employe/{id}', [PointageController::class, 'employe']);
        Route::get('/pointage/site/{id}', [PointageController::class, 'site']);

        Route::post('/hierarchie/add/{id}', [HierarchieController::class, 'add']);
        Route::post('/hierarchie/remove/{id}', [HierarchieController::class, 'remove']);
        Route::get('/hierarchie/employe/{id}', [HierarchieController::class, 'employe']);

        Route::post('/part_variable/add', [PartVariableController::class, 'store']);
        Route::post('/part_variable/update/{id}', [PartVariableController::class, 'update']);
        Route::post('/part_variable/cancel/{id}', [PartVariableController::class, 'cancel']);
        Route::post('/part_variable/send_back/{id}', [PartVariableController::class, 'send_back']);
        Route::post('/part_variable/save_done/{id}', [PartVariableController::class, 'save_done']);
        Route::post('/part_variable/discard/{id}', [PartVariableController::class, 'discard']);
        Route::get('/part_variable/show/{id}', [PartVariableController::class, 'show']);
        Route::get('/part_variable', [PartVariableController::class, 'index']);
        Route::post('/part_variable/done_multiple', [PartVariableController::class, 'done_multiple']);
        Route::get('/part_variable/get_part_export', [PartVariableController::class, 'get_part_export']);
        Route::post('/part_variable/store_multiple', [PartVariableController::class, 'store_multiple']);

        Route::post('/critere_part/add', [CriterePartController::class, 'store']);
        Route::post('/critere_part/update/{id}', [CriterePartController::class, 'update']);
        Route::post('/critere_part/delete/{id}', [CriterePartController::class, 'delete']);
        Route::get('/critere_part/employe/{id}', [CriterePartController::class, 'employe']);

        Route::post('/critere_mensuel/update/{id}', [CritereMensuelController::class, 'update']);
        Route::get('/critere_mensuel/employe/{id}', [CritereMensuelController::class, 'employe']);
        Route::get('/critere_mensuel/part_variable/{id}', [CritereMensuelController::class, 'part_variable']);

        Route::post('/employe/update_ignore_name', [EmployeController::class, 'updateIgnoreName']);
        Route::get('/employe/duplicated_name/{id}/{name}', [EmployeController::class, 'duplicatedName']);
        Route::post('/employe/blacklist/{id}', [EmployeController::class, 'blacklist']);
        Route::get('/employe/sanction', [EmployeController::class, 'sanction']);
        Route::post('/employe/add', [EmployeController::class, 'store']);
        Route::post('/employe/update/{id}', [EmployeController::class, 'update']);
        Route::post('/employe/soft_delete/{id}', [EmployeController::class, 'soft_delete']);
        Route::post('/employe/restore/{id}', [EmployeController::class, 'restore']);
        Route::post('/employe/change_real_site/{id}', [EmployeController::class, 'change_real_site']);
        Route::get('/employe/search', [EmployeController::class, 'search']);
        Route::get('/employe/hierarchie_search', [EmployeController::class, 'hierarchie_search']);
        Route::get('/employe/conge/{id}', [EmployeController::class, 'conge']);
        Route::get('/employe/last_sanction/{id}', [EmployeController::class, 'last_sanction']);
        Route::get('/employe/last_prime/{id}', [EmployeController::class, 'last_prime']);
        Route::get('/employe/show/{id}', [EmployeController::class, 'show']);
        Route::get('/employe/complement/{id}', [EmployeController::class, 'complement']);
        Route::get('/employe/show_detail/{id}', [EmployeController::class, 'detail']);
        Route::get('/employe/list_detail/{id}', [EmployeController::class, 'list_detail']);
        Route::get('/employe/notification', [EmployeController::class, 'notification']);
        Route::get('/employe/search', [EmployeController::class, 'search']);
        Route::get('/employe/sous_hierarchie', [EmployeController::class, 'sous_hierarchie']);
        Route::get('/employe/interim', [EmployeController::class, 'interim']);
        Route::get('/employe/auth', [EmployeController::class, 'auth']);
        Route::get('/employe/site/{id}', [EmployeController::class, 'site']);
        Route::get('/employe/sous_hierarchie_user', [EmployeController::class, 'sous_hierarchie_user']);
        Route::get('/employe/get_employe_planning', [EmployeController::class, 'get_employe_planning']);
        Route::get('/employe/get_pv_not_done', [EmployeController::class, 'get_pv_not_done']);
        Route::get('/employe/show_pv_not_done/{id}', [EmployeController::class, 'show_pv_not_done']);
        Route::get('/employe', [EmployeController::class, 'index']);

        Route::get('/recrutement/getGrille', [RecrutementController::class, 'get_grille_competences']);
        Route::get('/recrutement/getPj/{id}', [RecrutementController::class, 'get_pj']);
        Route::post('/recrutement/restore/{id}', [RecrutementController::class, 'restore']);
        Route::post('/recrutement/soft_delete/{id}', [RecrutementController::class, 'soft_delete']);
        Route::get('/recrutement/formation/{numero_stagiaire}', [RecrutementController::class, 'formation']);
        Route::get('/recrutement/manque/{numero_stagiaire}', [RecrutementController::class, 'manque']);
        Route::get('/recrutement/complement/{id}', [RecrutementController::class, 'complement']);
        Route::post('/recrutement/update/{id}', [RecrutementController::class, 'update']);
        Route::post('/recrutement/add', [RecrutementController::class, 'store']);
        Route::get('/recrutement', [RecrutementController::class, 'index']);
        Route::get('/recrutement/show_detail/{id}', [RecrutementController::class, 'detail']);

        Route::get('/type_absence/show/{id}', [TypeAbsenceController::class, 'show']);
        Route::get('/type_absence', [TypeAbsenceController::class, 'index']);

        Route::post('/absence/add', [AbsenceController::class, 'store']);
        Route::post('/absence/cancel_absence/{id}', [AbsenceController::class, 'cancel_absence']);
        Route::post('/absence/save_done/{id}', [AbsenceController::class, 'save_done']);
        Route::post('/absence/send_back/{id}', [AbsenceController::class, 'send_back']);
        Route::post('/absence/request_validation/{id}', [AbsenceController::class, 'request_validation']);
        Route::post('/absence/reply_validation/{id}', [AbsenceController::class, 'reply_validation']);
        Route::post('/absence/cancel_validation/{id}', [AbsenceController::class, 'cancel_validation']);
        Route::post('/absence/note/{id}', [AbsenceController::class, 'note']);
        Route::get('/absence/show/{id}', [AbsenceController::class, 'show']);
        Route::get('/absence/conge_done/{id}', [AbsenceController::class, 'conge_done']);
        Route::get('/absence/show_detail/{id}', [AbsenceController::class, 'detail']);
        Route::get('/absence/superviseur/{type}', [AbsenceController::class, 'superviseur']);
        Route::get('/absence/get_responsable/{id}', [AbsenceController::class, 'getResponsable']);
        Route::post('/absence/edit/{id}', [AbsenceController::class, 'edit']);
        Route::get('/absence/{type}', [AbsenceController::class, 'index']);
        Route::post('/absence/apply_mise_a_pied/{id}', [AbsenceController::class, 'ApplyMisePied']);
        Route::post('/absence/store_fictif', [AbsenceController::class, 'storeFictif']);

        Route::post('/approvisionnement/add', [ApprovisionnementController::class, 'store']);
        Route::post('/approvisionnement/item/add/{id}', [ApprovisionnementController::class, 'store_item']);
        Route::post('/approvisionnement/edit/{id}', [ApprovisionnementController::class, 'edit']);
        Route::post('/approvisionnement/edit_item/{id}', [ApprovisionnementController::class, 'edit_item']);
        Route::post('/approvisionnement/destroy_da_items/{id}', [ApprovisionnementController::class, 'destroy_da_items']);
        Route::post('/approvisionnement/reply_validation/{id}', [ApprovisionnementController::class, 'reply_validation']);
        Route::post('/approvisionnement/cancel_approvisionnement/{id}', [ApprovisionnementController::class, 'cancel_approvisionnement']);
        Route::post('/approvisionnement/send_back/{id}', [ApprovisionnementController::class, 'send_back']);
        Route::post('/approvisionnement/do_traite/{id}', [ApprovisionnementController::class, 'do_traite']);
        //Route::post('/approvisionnement/save_done/{id}', [ApprovisionnementController::class, 'save_done']);
        Route::get('/approvisionnement/show/{id}', [ApprovisionnementController::class, 'show']);
        Route::get('/approvisionnement/article/{id}', [ApprovisionnementController::class, 'article']);
        Route::get('/approvisionnement/show_item/{id}', [ApprovisionnementController::class, 'show_item']);
        Route::get('/approvisionnement/show_detail/{id}', [ApprovisionnementController::class, 'detail']);
        Route::get('/last_approvisionnement', [ApprovisionnementController::class, 'last_approvisionnement']);
        Route::get('/approvisionnement/demandeur', [ApprovisionnementController::class, 'demandeur']);
        Route::get('/approvisionnement', [ApprovisionnementController::class, 'index']);

        Route::post('/da_item/done/{id}', [DaItemController::class, 'done']);
        //Route::post('/da_item/cancel/{id}', [DaItemController::class, 'cancel']);

        Route::get('/type_equipement/show/{name}', [TypeEquipementController::class, 'show']);
        Route::get('/type_equipement', [TypeEquipementController::class, 'index']);

        Route::post('/equipement/refuse/{id}', [EquipementController::class, 'refuse_equipement']);
        Route::post('/equipement/validation/{id}', [EquipementController::class, 'validate_equipement']);
        Route::post('/equipement/add', [EquipementController::class, 'store']);
        Route::post('/equipement/set_employe/{id}', [EquipementController::class, 'set_employe']);
        Route::post('/equipement/cancel_equipement/{id}', [EquipementController::class, 'cancel_equipement']);
        Route::post('/equipement/send_back/{id}', [EquipementController::class, 'send_back']);
        Route::post('/equipement/request_validation/{id}', [EquipementController::class, 'request_validation']);
        Route::post('/equipement/reply_validation/{id}', [EquipementController::class, 'reply_validation']);
        Route::post('/equipement/cancel_validation/{id}', [EquipementController::class, 'cancel_validation']);
        Route::post('/equipement/note/{id}', [EquipementController::class, 'note']);
        Route::get('/equipement/show/{id}', [EquipementController::class, 'show']);
        Route::get('/equipement/article/{id}', [EquipementController::class, 'article']);
        Route::get('/equipement/show_detail/{id}', [EquipementController::class, 'detail']);
        Route::get('/equipement/superviseur', [EquipementController::class, 'superviseur']);
        Route::get('/equipement', [EquipementController::class, 'index']);
        Route::post('equipement/transfer/{id}', [EquipementController::class, 'transfer']);

        Route::post('/article_equipement/done/{id}', [ArticleEquipementController::class, 'done']);
        Route::post('/article_equipement/done_all/{id}', [ArticleEquipementController::class, 'done_all']);
        Route::post('/article_equipement/cancel/{id}', [ArticleEquipementController::class, 'cancel']);
        Route::get('/article_equipement', [ArticleEquipementController::class, 'index']);

        Route::post('/article/approvisionnement/{name}', [ArticleController::class, 'approvisionner']);
        Route::post('/article/update_stock/{name}', [ArticleController::class, 'update_stock']);
        Route::get('/article/mouvement_tab/{name}', [ArticleController::class, 'mouvement_tab']);
        Route::get('/article/show_detail/{name}', [ArticleController::class, 'detail']);
        Route::get('/article', [ArticleController::class, 'index']);
        Route::get('/article/all_article', [ArticleController::class, 'all']);

        Route::post('/mouvement_article/add', [MouvementEquipementController::class, 'store']);
        Route::post('/mouvement_article/deduction/{id}', [MouvementEquipementController::class, 'deduction']);
        Route::post('/mouvement_article/declaration_usure/{id}', [MouvementEquipementController::class, 'declaration_usure']);
        Route::post('/mouvement_article/do_transfert/{id}', [MouvementEquipementController::class, 'do_transfert']);
        Route::post('/mouvement_article/do_back/{id}', [MouvementEquipementController::class, 'do_back']);
        Route::get('/mouvement_article/equipement/{id}', [MouvementEquipementController::class, 'dotation_equipement']);
        Route::get('/mouvement_article/employe/{id}', [MouvementEquipementController::class, 'dotation_employe']);
        Route::get('/mouvement_article/show_detail/{id}', [MouvementEquipementController::class, 'detail']);
        Route::get('/mouvement_article/get_actif_agent', [MouvementEquipementController::class, 'get_actif_agent']);
        Route::get('/mouvement_article', [MouvementEquipementController::class, 'index']);

        Route::get('/ligne_equipement/article/{id}', [LigneEquipementController::class, 'ligne_equipement']);

        Route::get('/immatriculation', [ImmatriculationController::class, 'index']);
        Route::get('/fonction', [FonctionController::class, 'index']);
        Route::get('/agence', [AgenceController::class, 'index']);
        Route::get('/service', [ServiceController::class, 'index']);

        Route::post('/sanction/add', [SanctionController::class, 'store']);
        Route::post('/sanction/edit/{id}', [SanctionController::class, 'edit']);
        Route::post('/sanction/confirm_sanction/{id}', [SanctionController::class, 'confirm_sanction']);
        Route::post('/sanction/cancel_confirmation/{id}', [SanctionController::class, 'cancel_confirmation']);
        Route::post('/sanction/request_validation/{id}', [SanctionController::class, 'request_validation']);
        Route::post('/sanction/reply_validation/{id}', [SanctionController::class, 'reply_validation']);
        Route::post('/sanction/cancel_validation/{id}', [SanctionController::class, 'cancel_validation']);
        Route::post('/sanction/cancel_sanction/{id}', [SanctionController::class, 'cancel_sanction']);
        Route::post('/sanction/send_back/{id}', [SanctionController::class, 'send_back']);
        Route::post('/sanction/do_convocation/{id}', [SanctionController::class, 'do_convocation']);
        Route::post('/sanction/cancel_convocation/{id}', [SanctionController::class, 'cancel_convocation']);
        Route::post('/sanction/do_traite/{id}', [SanctionController::class, 'do_traite']);
        Route::post('/sanction/save_done/{id}', [SanctionController::class, 'save_done']);
        Route::get('/sanction/employe/{id}', [SanctionController::class, 'employe']);
        Route::get('/sanction/show/{id}', [SanctionController::class, 'show']);
        Route::get('/sanction/show_detail/{id}', [SanctionController::class, 'detail']);
        Route::get('/sanction/superviseur', [SanctionController::class, 'superviseur']);
        Route::get('/sanction/controlroom', [SanctionController::class, 'index_room']);
        Route::get('/sanction', [SanctionController::class, 'index']);

        Route::post('/prime/add', [PrimeController::class, 'store']);
        Route::post('/prime/edit/{id}', [PrimeController::class, 'edit']);
        Route::post('/prime/request_validation/{id}', [PrimeController::class, 'request_validation']);
        Route::post('/prime/reply_validation/{id}', [PrimeController::class, 'reply_validation']);
        Route::post('/prime/cancel_validation/{id}', [PrimeController::class, 'cancel_validation']);
        Route::post('/prime/cancel_prime/{id}', [PrimeController::class, 'cancel_prime']);
        Route::post('/prime/send_back/{id}', [PrimeController::class, 'send_back']);
        Route::post('/prime/do_traite/{id}', [PrimeController::class, 'do_traite']);
        Route::post('/prime/save_done/{id}', [PrimeController::class, 'save_done']);
        Route::get('/prime/show/{id}', [PrimeController::class, 'show']);
        Route::get('/prime/show_detail/{id}', [PrimeController::class, 'detail']);
        Route::get('/prime/superviseur', [PrimeController::class, 'superviseur']);
        Route::get('/prime', [PrimeController::class, 'index']);

        Route::get('/alarm', [AlarmController::class, 'index']);
        Route::get('/alarm/employe/{id}', [AlarmController::class, 'employe']);

        Route::get('/visite_poste/get_last_visite', [VisitePosteController::class, 'get_last_visite']);
        Route::post('/visite_poste/add', [VisitePosteController::class, 'store']);
        Route::get('/visite_poste/show/{id}', [VisitePosteController::class, 'show']);
        Route::get('/visite_poste/show_detail/{id}', [VisitePosteController::class, 'detail']);
        Route::get('/visite_poste', [VisitePosteController::class, 'index']);

        Route::get('/type_sav/show/{id}', [TypeSavController::class, 'show']);
        Route::get('/type_sav', [TypeSavController::class, 'index']);

        Route::post('/sav/add', [SavController::class, 'store']);
        Route::post('/sav/edit/{id}', [SavController::class, 'edit']);
        Route::post('/sav/request_validation/{id}', [SavController::class, 'request_validation']);
        Route::post('/sav/reply_validation/{id}', [SavController::class, 'reply_validation']);
        Route::post('/sav/cancel_validation/{id}', [SavController::class, 'cancel_validation']);
        Route::post('/sav/do_traite/{id}', [SavController::class, 'do_traite']);
        Route::post('/sav/save_done/{id}', [SavController::class, 'save_done']);
        Route::post('/sav/cancel_sav/{id}', [SavController::class, 'cancel_sav']);
        Route::post('/sav/send_back/{id}', [SavController::class, 'send_back']);
        Route::get('/sav/show/{id}', [SavController::class, 'show']);
        Route::get('/sav/show_detail/{id}', [SavController::class, 'detail']);
        Route::get('/last_sav/{id}', [SavController::class, 'last_sav']);
        Route::get('/sav/superviseur/{type}', [SavController::class, 'superviseur']);
        Route::get('/sav/{type}', [SavController::class, 'index']);

        Route::post('/flotte/add', [FlotteController::class, 'store']);
        Route::post('/flotte/edit/{id}', [FlotteController::class, 'edit']);
        Route::post('/flotte/request_validation/{id}', [FlotteController::class, 'request_validation']);
        Route::post('/flotte/reply_validation/{id}', [FlotteController::class, 'reply_validation']);
        Route::post('/flotte/cancel_validation/{id}', [FlotteController::class, 'cancel_validation']);
        Route::post('/flotte/note/{id}', [FlotteController::class, 'note']);
        Route::post('/flotte/save_done/{id}', [FlotteController::class, 'save_done']);
        Route::post('/flotte/cancel_flotte/{id}', [FlotteController::class, 'cancel_flotte']);
        Route::post('/flotte/send_back/{id}', [FlotteController::class, 'send_back']);
        Route::get('/flotte/show/{id}', [FlotteController::class, 'show']);
        Route::get('/flotte/show_detail/{id}', [FlotteController::class, 'detail']);
        Route::get('/flotte/superviseur', [FlotteController::class, 'superviseur']);
        Route::get('/last_flotte/{id}', [FlotteController::class, 'last_flotte']);
        Route::get('/flotte/valide', [FlotteController::class, 'index_valide']);
        Route::get('/flotte', [FlotteController::class, 'index']);

        Route::post('/fait_marquant/add', [FaitMarquantController::class, 'store']);
        Route::get('/fait_marquant/unread', [FaitMarquantController::class, 'unread']);
        Route::get('/fait_marquant/show/{id}', [FaitMarquantController::class, 'show']);
        Route::get('/fait_marquant/show_detail/{id}', [FaitMarquantController::class, 'detail']);
        Route::get('/fait_marquant', [FaitMarquantController::class, 'index']);

        Route::post('/seen/fait_marquant/{id}', [SeenController::class, 'fait_marquant']);
        Route::post('/seen/fait_marquant_all', [SeenController::class, 'fait_marquant_all']);
        Route::post('/seen/visite_poste/{id}', [SeenController::class, 'visite_poste']);
        Route::post('/seen/visite_poste_all', [SeenController::class, 'visite_poste_all']);
        Route::post('/seen/juridique/{id}', [SeenController::class, 'juridique']);
        Route::post('/seen/juridique_all', [SeenController::class, 'juridique_all']);
        Route::post('/seen/plainte_all', [SeenController::class, 'plainte_all']);

        Route::post('/notification/add', [NotificationController::class, 'store']);
        Route::post('/notification/seen', [NotificationController::class, 'seen']);
        Route::post('/notification/remove_seen/{id}', [NotificationController::class, 'remove_seen']);
        Route::get('/notification', [NotificationController::class, 'index']);

        Route::get('/user/modal', [UserController::class, 'modal']);
        Route::post('/user/add', [UserController::class, 'store']);
        Route::post('/user/edit/{id}', [UserController::class, 'edit']);
        Route::post('/user/reset_password/{id}', [UserAuthController::class, 'reset_password']);
        Route::get('/user/show/{id}', [UserController::class, 'show']);
        Route::get('/user/show_detail/{id}', [UserController::class, 'detail']);
        Route::get('/role', [UserController::class, 'role']);
        Route::get('/user/get_all_roles', [UserController::class, 'get_all_roles']);
        Route::get('/user/get_all_role', [UserController::class, 'get_all_role']);
        Route::post('/user/edit_role/{role}', [UserController::class, 'edit_role']);
        Route::get('/user', [UserController::class, 'index']);
        Route::get('/user/service', [UserController::class, 'service']);

        Route::get('/juridique/show/{id}', [JuridiqueController::class, 'show']);
        Route::get('/juridique/detail/{id}', [JuridiqueController::class, 'detail']);
        Route::get('/juridique', [JuridiqueController::class, 'index']);
        Route::post('/juridique/add', [JuridiqueController::class, 'store']);
        Route::post('/juridique/edit/{id}', [JuridiqueController::class, 'edit']);
        Route::post('/juridique/update_status/{id}', [JuridiqueController::class, 'update_status']);

        Route::get('/plainte/show/{id}', [PlainteAgentController::class, 'show']);
        Route::get('/plainte/detail/{id}', [PlainteAgentController::class, 'detail']);
        Route::get('/plainte', [PlainteAgentController::class, 'index']);
        Route::post('/plainte/save_done/{id}', [PlainteAgentController::class, 'save_done']);
        Route::post('/plainte/cancel/{id}', [PlainteAgentController::class, 'cancel']);
        Route::post('/plainte/add', [PlainteAgentController::class, 'store']);
        Route::post('/plainte/edit/{id}', [PlainteAgentController::class, 'edit']);

        Route::get('/suivi_juridique', [SuiviJuridiqueController::class, 'index']);
        Route::get('/suivi_juridique/unread_recouvrement', [SuiviJuridiqueController::class, 'unread_recouvrement']);
        Route::get('/suivi_juridique/unread_plainte', [SuiviJuridiqueController::class, 'unread_plainte']);
        Route::get('/suivi_juridique/show/{id}', [SuiviJuridiqueController::class, 'show']);
        Route::post('/suivi_juridique/add', [SuiviJuridiqueController::class, 'store']);

        Route::post('/reclamation/cancel_reclamation/{id}', [ReclamationController::class, 'cancel_reclamation']);
        Route::post('/reclamation/send_back/{id}', [ReclamationController::class, 'send_back']);
        Route::post('/reclamation/do_traite/{id}', [ReclamationController::class, 'do_traite']);
        Route::post('/reclamation/save_done/{id}', [ReclamationController::class, 'save_done']);
        Route::get('/reclamation/show/{id}', [ReclamationController::class, 'show']);
        Route::get('/reclamation', [ReclamationController::class, 'index']);

        Route::post('/pointage_reclamation/add', [PointageReclamationController::class, 'store']);
        Route::post('/pointage_reclamation/update/{id}', [PointageReclamationController::class, 'update']);
        Route::post('/pointage_reclamation/delete/{id}', [PointageReclamationController::class, 'delete']);
        Route::get('/pointage_reclamation/{reclamation_id}', [PointageReclamationController::class, 'index']);


        Route::get('/paie', [PaieController::class, 'index']);
        Route::get('/paie/get_heure_travaille', [PaieController::class, 'getHeureTravaille']);
        Route::post('/paie/store', [PaieController::class, 'storePaie']);
        Route::post('/paie/save_done/{id}', [PaieController::class, 'save_done']);
        Route::delete('/paie/delete/{id}', [PaieController::class, 'deletePaie']);
        Route::post('/paie/cancel_paie/{id}', [PaieController::class, 'cancel_paie']);
        Route::post('/paie/send_back/{id}', [PaieController::class, 'send_back']);
        Route::post('/paie/do_traite/{id}', [PaieController::class, 'do_traite']);
        Route::post('/paie/edit/{id}', [PaieController::class, 'edit']);
        Route::post('/paie/edit_done/{id}', [PaieController::class, 'editAndDone']);
        Route::get('/paie/get_conge_paye',[PaieController::class, 'getCongePaye']);
        Route::get('/paie/get_employe',[PaieController::class, 'getEmploye']);
        Route::post('/paie/generate_paie/{id}',[PaieController::class, 'generate_paie']);
        Route::get('/paie/get_reclamation/{id}',[PaieController::class, 'getReclamationByPaieId']);
        Route::get('/paie/get_reclamation',[PaieController::class, 'getReclamation']);
        Route::get('/paie/conge_done/{id}',[PaieController::class, 'conge_done']);
        Route::get('/paie/get_to_done_all',[PaieController::class, 'get_to_done_all']);
        Route::post('/paie/get_only_calcul',[PaieController::class, 'get_only_calcul']);
        Route::post('/paie/save_done_multiple', [PaieController::class, 'save_done_multiple']);
        Route::get('/paie/show/{id}', [PaieController::class, 'show']);

        Route::get("/deduction",[DeductionController::class, 'index']);
        Route::get("/deduction/show/{id}",[DeductionController::class, 'show']);
        Route::post("/deduction/store",[DeductionController::class, 'store']);
        Route::post("/deduction/send_back/{id}",[DeductionController::class, 'send_back']);
        Route::post("/deduction/do_traite/{id}",[DeductionController::class, 'do_traite']);
        Route::post("/deduction/edit/{id}",[DeductionController::class, 'edit']);
        Route::post("/deduction/save_done/{id}",[DeductionController::class, 'save_done']);
        Route::post("/deduction/cancel_deduction/{id}",[DeductionController::class, 'cancel_deduction']);

        Route::get("/avance", [AvanceController::class, 'index']);
        Route::post("/avance/store", [AvanceController::class, 'store']);
        Route::get("/avance/type", [AvanceController::class, 'getType']);
        Route::post("/avance/edit/{id}", [AvanceController::class, 'edit']);
        Route::post("/avance/do_traite/{id}", [AvanceController::class, 'do_traite']);
        Route::post("/avance/cancel/{id}", [AvanceController::class, 'cancel_avance']);
        Route::post("/avance/send_back/{id}", [AvanceController::class, 'send_back']);
        Route::post("/avance/save_done/{id}", [AvanceController::class, 'save_done']);
        Route::get("/avance/show/{id}", [AvanceController::class, 'show']);
        Route::get("/avance/get_to_done", [AvanceController::class, 'get_to_done']);
        Route::post("/avance/done_multiple", [AvanceController::class, 'done_multiple']);

        Route::post("/service24/store", [Service24Controller::class, 'store']);
        Route::get("/service24/show/{id}", [Service24Controller::class, 'show']);
        Route::post("/service24/cancel/{id}", [Service24Controller::class, 'cancel_service']);
        Route::post("/service24/send_back/{id}", [Service24Controller::class,'send_back']);
        Route::post("/service24/reply_validation/{id}", [Service24Controller::class,'reply_validation']);
        Route::post("/service24/done_multiple", [Service24Controller::class, 'done_multiple']);
        Route::get("/service24/get_data_validation", [Service24Controller::class, 'get_data_validation']);
        Route::get("/service24", [Service24Controller::class, 'index']);

        Route::get("/satisfaction",[SatisfactionController::class, 'index']);
        Route::get("/satisfaction/show/{id}",[SatisfactionController::class, 'show']);
        Route::post("/satisfaction/store", [SatisfactionController::class, 'store']);
        // Route::post("/satisfaction/edit/{id}", [SatisfactionController::class, 'edit']);
        Route::post("/satisfaction/seen_satisfaction/{id}", [SatisfactionController::class, 'seen_satisfaction']);
        Route::post("/satisfaction/seen_all", [SatisfactionController::class, 'seen_all']);

        Route::get("/service/message", [ServiceController::class, 'message']);

        Route::get("/message",[MessageController::class, 'index']);
        Route::get("/message/show/{id}",[MessageController::class, 'show']);
        Route::post("/message/add", [MessageController::class, 'store']);
        Route::post("/message/seen_all",[MessageController::class, 'seen_all']);
        Route::post("/message/seen/{id}",[MessageController::class, 'seen']);
        Route::post("/message/unfollow/{id}",[MessageController::class, 'unfollow']);
        Route::get("/message/get_absence/{id}",[MessageController::class, 'get_absence']);
        Route::post("/message/unfollow_all",[MessageController::class, 'unfollow_all']);
        Route::post("/message/unfollow_all_by_user",[MessageController::class, 'unfollow_all_by_user']);
        Route::post("/message/handle_to_do",[MessageController::class, 'handle_to_do']);

        Route::get("/message_sent_unread/show/{id}",[MessageController::class, 'show_sent_unread']);
        Route::get("/message_sent_unread",[MessageController::class, 'index_sent_unread']);

        Route::post("/model_message/add",[ModelMessageController::class, 'store']);
        Route::get("/model_message/all_users", [ModelMessageController::class, 'get_all_users']);
        Route::get("/model_message/users_by_service/{id}", [ModelMessageController::class, 'get_users_by_service']);
        Route::get("/model_message/{id}",[ModelMessageController::class, 'get_model_by_id']);
        Route::get("/model_message",[ModelMessageController::class, 'index']);
        Route::post("/model_message/update/{id}",[ModelMessageController::class, 'update']);

        Route::post("/planning/add",[PlanningController::class, 'store']);
        Route::get("/planning/get_data_to_print", [PlanningController::class, 'get_data_to_print']);
        Route::get("/planning/not_done", [PlanningController::class, 'planning_not_done_manager']);
        Route::get("/planning/show_not_done/{id}", [PlanningController::class, 'show_not_done_manager']);
        Route::get("/planning/get_planning",[PlanningController::class, 'get_planning']);
        Route::get("/planning/show/{id}", [PlanningController::class, 'show']);
        Route::get("/planning/site_detail/{site_id}", [PlanningController::class, 'get_site_detail']);
        Route::get("/planning/check_planning/{id}", [PlanningController::class, 'check_planning']);
        Route::get("/planning", [PlanningController::class, 'index']);

        Route::get("/anomalie/show/{id}",[AnomalieController::class, 'show']);
        Route::get("/anomalie_he/show/{id}",[AnomalieController::class, 'show_pointage_effectif']);
        Route::get("/anomalie_he",[AnomalieController::class, 'index_pointage_effectif']);
        Route::get("/anomalie_pl_eff/show/{id}",[AnomalieController::class, 'show_planning_effectif']);
        Route::get("/anomalie_pl_eff",[AnomalieController::class, 'index_planning_effectif']);
        Route::get("/anomalie",[AnomalieController::class, 'index']);

        Route::get("/anomalie_planning/show/{id}",[AnomaliePlanningController::class, 'show']);
        Route::post("/anomalie_planning/assign/{id}",[AnomaliePlanningController::class, 'assign']);
        Route::post("/anomalie_planning/assign_all/{id}",[AnomaliePlanningController::class, 'assign_all']);
        Route::get("/anomalie_planning", [AnomaliePlanningController::class, 'index']);

        Route::get("/anomalie_service24/show/{id}",[AnomalieService24Controller::class, 'show']);
        Route::get("/anomalie_service24", [AnomalieService24Controller::class, 'index']);

        Route::get("/appelle/get_indice", [AppelleController::class, 'get_indice']);
        Route::get("/appelle/get_recall", [AppelleController::class, 'get_recall']);
        Route::get("/appelle/traceability", [AppelleController::class, 'traceability']);
        Route::get("/appelle", [AppelleController::class, 'index_admin']);

        Route::post("/region_users/specify/{id}", [RegionUsersController::class, 'specify']);
        Route::get("/region_users/show/{id}", [RegionUsersController::class, 'show']);
    }
);
Route::get('/appelles/a_rappeler', [AppelleController::class, 'a_rappeler']);
Route::get('/appelles', [AppelleController::class, 'index']);

Route::post('/appelle/make_call', [AppelleController::class, 'make_call']);
Route::post('/login', [UserAuthController::class, 'login']);
