import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';

export default function RH({auth}) {
    const [nbData, setData] = useState(null) 

    useEffect(() => {
        let isMounted = true
        axios.get("/api/rh", useToken())
        .then((res) => {
            if(isMounted)
                setData(res.data)
        })
        return () => { isMounted = false };
    }, [])

    return (
        auth.role == "rh" ?
            <MenuView>
                {
                    nbData && <div>
                    {
                        (nbData.nb_sanction != 0 || nbData.nb_conge != 0 || nbData.nb_permission != 0 || nbData.nb_avance != 0) ?
                        <div>
                            <h3 className='sub-title-menu'>RH</h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_sanction != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sanction?status=demande">Demande de sanction</Link>
                                        <span className='badge-outline'>{nbData.nb_sanction}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_prime != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/prime?status=demande">Demande de prime</Link>
                                        <span className='badge-outline'>{nbData.nb_prime}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_conge != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/absence/conge?status=demande">Demande de congé</Link>
                                        <span className='badge-outline'>{nbData.nb_conge}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_permission != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/absence/permission?status=demande">Demande de permission</Link>
                                        <span className='badge-outline'>{nbData.nb_permission}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_avance != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/avance?status=demande">Demande d'avance</Link>
                                        <span className='badge-outline'>{nbData.nb_avance}</span>
                                    </div>
                                }
                                
                            </div>
                        </div>
                        :
                        <div className='center secondary'>
                            <br/>
                            <br/>
                            <h3>Aucune demande pour l'instant</h3>
                        </div>
                    }
                    </div>
                }
            </MenuView>
        : 
            <Navigate to="/"/>
    );
}