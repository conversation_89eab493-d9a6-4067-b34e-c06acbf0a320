import React, { useEffect, useState } from 'react'
import InputMont<PERSON><PERSON>ear from '../input/InputMonthYear'
import moment from 'moment'
import useToken from '../util/useToken'

export default function DuplicatePlanning({planning, closeModal}) {
    const [datePlanning, setDatePlanning] = useState({year:'', month:''})
    const [error, setError] = useState('')
    const [disableSubmit, setDisableSubmit] = useState(true)
    useEffect(() => {
        if (/^\d{4}$/.test(datePlanning.year) && /^\d{2}$/.test(datePlanning.month))
            setDisableSubmit(false)
        else
            setDisableSubmit(true)
    }, [datePlanning])


    const initFormPlanning = (dtPl) => {
        if (dtPl && dtPl?.year && dtPl?.month) {
            const currentNumberDays = moment(`${dtPl.year}-${dtPl.month}`, 'YYYY-MM').daysInMonth()
            const planningTemps = []
            for (let i = 1; i <= currentNumberDays; i++) {
                const date = moment(`${dtPl.year}-${dtPl.month}-${i}`, 'YYYY-MM-DD');
                if (date.clone().set({ hour: 6, minute: 0, second: 0 }).isAfter(moment())) {
                    planningTemps.push({
                        id: i + "J",
                        day: i,
                        nom: date.format('ddd'),
                        service: (date.clone().set({ hour: 6, minute: 0, second: 0 })).format('YYYY-MM-DD HH:mm:ss'),
                        form: {id:null, edition:false, comment: '' },
                        employes: [],
                    })
                }
                if (date.clone().set({ hour: 18, minute: 0, second: 0 }).isAfter(moment())){
                    planningTemps.push({
                        id: i + "N",
                        day: i,
                        nom: date.format('ddd'),
                        service: (date.clone().set({ hour: 18, minute: 0, second: 0 })).format('YYYY-MM-DD HH:mm:ss'),
                        form: {id:null, edition: false, comment: '' },
                        employes: [],
                    })
                }
            }
            return planningTemps
        }
    }

    const duplicatePointage = (pointages) => {
        let ptg = []
        ptg = initFormPlanning(datePlanning)
        pointages.forEach((pointage) => {
            const date = moment(pointage.date_pointage, 'YYYY-MM-DD HH:mm:ss');
            const existing = ptg.find((p) => moment(p.service).format('DD HH:mm:ss') === date.format('DD HH:mm:ss'));       
            if (existing) {
                existing.employes.push(pointage.agent_id)
            }
        })
        return ptg
    }

    const onSubmit = () => {
        const pointages = duplicatePointage(planning.pointages)
        if (!pointages) {
            return
        }
        else{
            const data = {
                site_id: planning.planning.idsite,
                date_planning: datePlanning,
                pointages: pointages
            }
            axios.post("/api/planning/add", data, useToken()).then((res) => {
                if (res.data.error) {
                    console.error(res.data.error);
                    setError(res.data.error)
                }
                // else if (res.data.success)
                //     setNotification(res.data);
                // setDisableSubmit(false)
            })
        }
    }

    return (
        <div className='modal'>
            <div>
                <h3>Dupliquation planning</h3>
                <span><b>Site: {planning.planning.site}</b></span> 
                <InputMonthYear label='Date planning' value={datePlanning} onChange={setDatePlanning} required/>
                <div className='form-button-container'>
                    {!disableSubmit && <button type='button' onClick={()=>{duplicatePointage(planning.pointages)}}>Dupliquer</button>}
                    <button type='button' onClick={() => closeModal()}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
