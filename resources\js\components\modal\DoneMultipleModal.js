import React, { useEffect, useState } from 'react';
import InputCheckBox from '../input/InputCheckBox';
import useToken from '../util/useToken';
import InputDate from '../input/InputDate';
import { useLocation, useNavigate } from 'react-router-dom';
import moment from 'moment';
import InputAgent from '../input/InputAgent';
import axios from 'axios';
import InputSite from '../input/InputSite';

export default function DoneMultipleModal({ action, task, name, data, closeModal, updateData }) {
    const location = useLocation();
    const navigate = useNavigate();
    const [dateDeRetour, setDateRetour] = useState(null);
    const [selectedAgent, setSelectedAgent] = useState(null);
    const [selectedSite, setSelectedSite] = useState(null);
    const [error, setError] = useState()
    const [checkedItems, setCheckedItems] = useState({});
    const [disabledItems, setDisabledItems] = useState({});
    const [isLoading, setIsLoading] = useState(false);


    useEffect(() => {
        const initialCheckedItems = {};
        const disabledItems = {};
        if (data && data.articles) {
            data.articles.forEach(item => {
                if (name === "equipement" && item.done === null) {
                    if (data.isValidated === 0 && item.designation === 'Botte de pluie') {
                        initialCheckedItems[item.designation] = false;
                        disabledItems[item.designation] = true;
                    } else {
                        initialCheckedItems[item.designation] = true;
                    }
                } else if (name === "dotation" && task === "retourner" && item.status !== "retourné") {
                    initialCheckedItems[item.designation] = true;
                } else if (name === "dotation" && task === "transferer") {
                    if (data.type_mouvement === "entré" && item.status !== "transferé") {
                        initialCheckedItems[item.designation] = true;
                    } else if (data.type_mouvement === "sortie" && item.status === "attribué") {
                        initialCheckedItems[item.designation] = true;
                    }
                } else if (name === "dotation" && task === "usure") {
                    initialCheckedItems[item.designation] = false;
                } else if (name === "dotation" && task === "deduction") {
                    initialCheckedItems[item.designation] = false;
                }
            });
        }
        setCheckedItems(initialCheckedItems);
        setDisabledItems(disabledItems);
    }, [data, name, task]);

    const onChange = (item) => {
        if (disabledItems[item]) return;
        setCheckedItems(prevCheckedItems => ({
            ...prevCheckedItems,
            [item]: !prevCheckedItems[item]
        }));
    };

    const handleOk = () => {
        setIsLoading(true);
        const selectedItems = Object.keys(checkedItems).filter(item => checkedItems[item]);
        const payload = {
            selectedItems: data.articles
                .filter(article =>
                    selectedItems.includes(article.designation) &&
                    (
                        name === "equipement" ||
                        (
                            name === "dotation" && task === "retourner" &&
                            article.status !== "retourné" &&
                            article.status !== "direct" &&
                            article.estUsé !== '1' &&
                            article.estDeduit !== '1'
                        ) ||
                        (
                            name === "dotation" && task === "transferer" &&
                            data.type_mouvement === "entré" &&
                            article.status !== "transferé" &&
                            article.estUsé !== '1' &&
                            article.estDeduit !== '1'
                        ) ||
                        (
                            name === "dotation" && task === "transferer" &&
                            data.type_mouvement === "sortie" &&
                            article.status === "attribué" &&
                            article.estUsé !== '1' &&
                            article.estDeduit !== '1'
                        ) ||
                        (
                            name === "dotation" && task === "usure" &&
                            (
                                (article.status === "attribué" ||
                                article.status === "retourné" ||
                                article.status === "transferé") &&
                                article.estUsé !== '1' &&
                                article.estDeduit !== '1'
                            )
                        ) ||
                        (
                            name === "dotation" && task === "deduction" &&
                            (
                                (article.status === "attribué" ||
                                article.status === "retourné" ||
                                article.status === "transferé") &&
                                article.estDeduit !== '1' &&
                                article.estUsé !== '1'
                            )
                        )
                    )
                )
                .map(article => article.name)
        };

        if (name === "dotation" && task === "retourner") {
            payload.date_mouvement = moment(dateDeRetour).format('YYYY-MM-DD')
            payload.type = data.type_mouvement
        } else if (name === "dotation" && task === "transferer") {
            if (selectedAgent) {
                payload.selectedAgent = selectedAgent
            } else {
                if (selectedSite) {
                    payload.selectedSite = selectedSite
                }
            }
            payload.type = data.type_mouvement
            payload.date_mouvement = moment().format('YYYY-MM-DD')
        } else if (name === "dotation" && task === "assignation") {
            if (selectedAgent) {
                payload.selectedAgent = selectedAgent
            } else {
                if (selectedSite) {
                    payload.selectedSite = selectedSite
                }
            }
        }
        axios.post(action.request, payload, useToken())
        .then((res) => {
            if (res.data.error === "EACCES")
                setError("Une erreur est survenue.")
            else if (res.data.error)
                setError(res.data.error)
            else {
                if (name === "dotation" && (task !== "usure" && task !== "deduction")) {
                    const newId = res.data.newId;
                    const searchParams = new URLSearchParams(location.search);
                    searchParams.set('id', newId);
                    navigate({
                        pathname: location.pathname,
                        search: searchParams.toString(),
                    });
                    setIsLoading(false);
                }
                if (closeModal) closeModal()
                if (updateData) updateData(true)
            }
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoi, réessayez.")
            setIsLoading(false);
        })
    };
    
    return (
        <div className='modal'>
            <div>
                <h2>
                    {name === "equipement"
                    ? "Terminer les demandes" : name === "dotation" && task === "retourner"
                    ? "Retourner les articles" : name === "dotation" && task === "transferer"
                    ? "Transferer les articles" : name === "dotation" && task === "usure"
                    ? "Déclaration d'usure" : name === "dotation" && task === "deduction"
                    && "Deduction"}
                </h2>
                {name === "dotation" && task === "retourner" &&
                    <InputDate
                        required
                        label="Date de retour"
                        value={dateDeRetour}
                        onChange={setDateRetour}
                    />
                }
                {name === "dotation" && (task === "transferer" || task === "assignation") && (
                    data.employe_id != null ?
                    <InputAgent required value={selectedAgent} onChange={setSelectedAgent}/>
                    :
                    data.site_id != null &&
                    <InputSite required value={selectedSite} onChange={setSelectedSite}/>
                )}
                <div className='grid-modal-container'>
                    {
                        data.articles && data.articles.map((item, index) => {
                            let shouldRender = false;
                            let isDisabled = false;
                            if (name === "equipement") {
                                shouldRender = item.done === null;
                                if (item.isValidated === 0 && item.designation === 'Botte de pluie') {
                                    isDisabled = true;
                                }
                            } else if (name === "dotation" && task === "retourner") {
                                shouldRender = item.status !== "retourné" && item.status !== "direct" && item.estUsé !== '1' && item.estDeduit !== '1';
                            } else if (name === "dotation" && task === "transferer" && data.type_mouvement === "entré") {
                                shouldRender = item.status !== "transferé" && item.estUsé !== '1' && item.estDeduit !== '1';
                            } else if (name === "dotation" && task === "transferer" && data.type_mouvement === "sortie") {
                                shouldRender = item.status === "attribué" && item.estUsé !== '1' && item.estDeduit !== '1';
                            } else if (name === "dotation" && task === "usure" && data.type_mouvement === "sortie") {
                                shouldRender = item.estUsé !== '1' && item.status === "attribué" && item.estDeduit !== '1';
                            } else if (name === "dotation" && task === "usure" && data.type_mouvement === "entré") {
                                shouldRender = item.estUsé !== '1' && item.status === "retourné" && item.estDeduit !== '1';
                            } else if (name === "dotation" && task === "deduction") {
                                shouldRender = item.estDeduit !== '1' && item.estDeduit !== '1' && item.estUsé !== '1';
                            }

                            return shouldRender ? (
                                <div key={index}>
                                    <InputCheckBox
                                        disabled={disabledItems[item.designation] || false}
                                        name={item.designation}
                                        label={item.designation}
                                        checked={checkedItems[item.designation] || false}
                                        onChange={() => onChange(item.designation)}
                                    />
                                </div>
                            ) : null;
                        })
                    }
                </div>
                <div className='form-button-container'>
                    <button className='btn-primary' disabled={isLoading} onClick={handleOk}>Envoyer</button>
                    <button type='button' onClick={closeModal}>Annuler</button>
                </div>
            </div>
        </div>
    );
}
