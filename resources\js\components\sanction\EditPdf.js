import { useState,useEffect } from 'react'
import PdfAvertAutre from './pdf/PdfAvertAutre'
import PdfAvertBouton from './pdf/PdfAvertBouton'
import PdfAvertBoutonAbusif from './pdf/PdfAvertBoutonAbusif'
import PdfAvertBiometrique from './pdf/PdfAvertBiometrique'
import PdfAvertCheckPhone from './pdf/PdfAvertCheckPhone'
import PdfAvertSommeilRetard from './pdf/PdfAvertSommeilRetard'
import PdfAvertTag from './pdf/PdfAvertTag'
import PdfConvocation from './pdf/PdfConvocation'
import PdfFinEssai from './pdf/PdfFinEssai'
import PdfLicenciement from './pdf/PdfLicenciement'
import PdfAvertNonRespect from './pdf/PdfNonRespect'
import PdfRuptureStage from './pdf/PdfRuptureStage'
import {PDFDownloadLink} from '@react-pdf/renderer'
import Textarea from '../input/Textarea'
import InputSelect from '../input/InputSelect'
import InputText from '../input/InputText'
import matricule from '../util/matricule';
import PdfMisePied from './pdf/PdfMisePied'

export default function EditPdf({header,sanction,setShowPrintModal}) {
    const [printType, setPrintType] = useState("")
    const [genre, setGenre] = useState("")
    const [motif, setMotif] = useState("")
    const [societeSt, setSocieteSt] = useState("")
    const [dateConvocation, setDateConvocation] = useState("")
    const [avertissementGrade, setAvertissementGrade] = useState("")
    const [nbJour, setNbJour] = useState("")
    const [enablePrint, setEnablePrint] = useState(false)
    const validDate = /(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/
    const validDateNoTime = /(\d{4})-(\d{2})-(\d{2})$/
    const validNbJour = /(\d{2}) [a-z]{4,8}$/
    const type = [
        'Avertissement Bouton',
        'Avertissement Bouton Abusif',
        'Avertissement Biometrique',
        'Avertissement Check Phone',
        'Avertissement Sommeil/Retard',
        'Avertissement TAG',
        'Autre Avertissement',
        'Convocation',
        'Fin essai',
        'Licenciement',
        'Avertissement Non Respect',
        'Rupture de Stage'
    ]

    const gradeList = [
        'Avertissement',
        '01er Avertissement',
        '02ème Avertissement',
        '03ème Avertissement',
        '04ème Avertissement'
    ]

    const genreList = [
        'Monsieur',
        'Madame'
    ]

    const societeList = [
        'SOIT',
        'DIRICKX GUARD'
    ]
    
    useEffect(() => {
        
        if (printType == "Mise à pied")
            setEnablePrint(genre.trim() && nbJour.trim())
        else if(
            ['Avertissement Bouton','Avertissement Bouton Abusif','Avertissement Biometrique','Avertissement Check Phone',
             'Avertissement Sommeil/Retard','Avertissement TAG','Avertissement Non Respect'
            ].includes(printType)
        ){
            setDateConvocation("")
            if(sanction.societe_id == 3) {
                setEnablePrint(avertissementGrade.trim() && genre.trim() && societeSt.trim())
            }else{
                setEnablePrint(avertissementGrade.trim() && genre.trim())
            }
                     
        }else if(
            ['Autre Avertissement'].includes(printType)
        ){
            setDateConvocation("")
            if(sanction.societe_id == 3) {
                setEnablePrint(avertissementGrade.trim() && genre.trim() && societeSt.trim() && motif.trim())
            }else{
                setEnablePrint(avertissementGrade.trim() && genre.trim() && motif.trim())
            }
        }else if(
            ['Convocation'].includes(printType)
        ){
            setAvertissementGrade("")
            if(sanction.societe_id == 3) {
                setEnablePrint(motif.trim() && genre.trim() && societeSt.trim())
            }else{
                setEnablePrint(motif.trim() && genre.trim())
            }
        }else if(
            ['Fin essai','Rupture de Stage'].includes(printType)
        ){
            setAvertissementGrade("")
            if(sanction.societe_id == 3) {
                setEnablePrint(genre.trim() && societeSt.trim())
            }else{
                setEnablePrint(genre.trim())
            }
            
        }else if(
            ['Licenciement'].includes(printType)
        ){
            setAvertissementGrade("")
            if(sanction.societe_id == 3) {
                setEnablePrint(genre.trim() && motif.trim() && societeSt.trim())
            }else{
                setEnablePrint(genre.trim() && motif.trim())
            }
            
        }else{
            setEnablePrint(false)
        }
    },[printType,dateConvocation,avertissementGrade,motif,genre,nbJour,societeSt])

    return <div className='modal'>
        <div style={{maxHeight: '60rem',overflowY: 'auto'}}>
            <h3>{header}</h3>
            <div className='input-container'>
                <InputSelect 
                    label='Type'
                    selected={printType}
                    setSelected={setPrintType}
                    options={type}
                    required/>
            </div>
            {sanction.societe_id == 3 &&
                <InputSelect 
                    label='Societe'
                    selected={societeSt}
                    setSelected={setSocieteSt}
                    options={societeList}
                    required/>
            }
            <div className='input-container'>
                <InputSelect 
                    label='Genre'
                    selected={genre}
                    setSelected={setGenre}
                    options={genreList}
                    required/>
            </div>
                {[
                    'Avertissement Bouton',
                    'Avertissement Bouton Abusif',
                    'Avertissement Biometrique',
                    'Avertissement Check Phone',
                    'Avertissement Sommeil/Retard',
                    'Avertissement TAG',
                    'Autre Avertissement',
                    'Avertissement Non Respect'
                ].includes(printType) &&
                    <div className='input-container'>
                        <InputSelect 
                            label='Niveau'
                            selected={avertissementGrade}
                            setSelected={setAvertissementGrade}
                            options={gradeList}
                            required/>
                    </div>
                }
                {['Mise à pied'].includes(printType) &&
                    <InputText
                        required
                        label="Nombre de jour "
                        // placeholder='03 jours'
                        type="number"
                        value={nbJour}
                        onChange={setNbJour}
                    />
                }
                {printType == "Convocation" &&
                    <div className='input-container'>
                        <label>Date de convocation</label>
                        <input
                            type="datetime-local"
                            id="dateConvocation"
                            name="dateConvocation"
                            value={dateConvocation}
                            min={new Date()}
                            onChange={(e) => setDateConvocation(e.target.value)}
                        />
                     </div>
                }
            <Textarea value={motif} label="Motif" onChange={(value) => setMotif(value)} required={['Autre Avertissement','Convocation','Licenciement'].includes(printType)}/>
            <div className='form-button-container'>
                {!enablePrint &&
                    <button className='btn-primary' disabled>Imprimer</button>
                }
                {enablePrint &&
                    <PDFDownloadLink 
                        document={
                            printType == "Avertissement Bouton" ? <PdfAvertBouton sanction={sanction} motif={motif} avertissementGrade={avertissementGrade} genre={genre} nbJour={nbJour} societeSt={societeSt}/>
                            :
                            printType == "Avertissement Bouton Abusif" ? <PdfAvertBoutonAbusif sanction={sanction} motif={motif} avertissementGrade={avertissementGrade} genre={genre} nbJour={nbJour} societeSt={societeSt}/>
                            :
                            printType == "Avertissement Biometrique" ? <PdfAvertBiometrique sanction={sanction} motif={motif} avertissementGrade={avertissementGrade} genre={genre} nbJour={nbJour} societeSt={societeSt}/>
                            :
                            printType == "Avertissement Check Phone" ? <PdfAvertCheckPhone sanction={sanction} motif={motif} avertissementGrade={avertissementGrade} genre={genre} nbJour={nbJour} societeSt={societeSt}/>
                            :
                            printType == "Avertissement Sommeil/Retard" ? <PdfAvertSommeilRetard sanction={sanction} motif={motif} avertissementGrade={avertissementGrade} genre={genre} nbJour={nbJour} societeSt={societeSt}/>
                            :
                            printType == "Avertissement TAG" ? <PdfAvertTag sanction={sanction} motif={motif} avertissementGrade={avertissementGrade} genre={genre} nbJour={nbJour} societeSt={societeSt}/>
                            :
                            printType == "Autre Avertissement" ? <PdfAvertAutre sanction={sanction} motif={motif} avertissementGrade={avertissementGrade} genre={genre} nbJour={nbJour} societeSt={societeSt}/>
                            :
                            printType == "Convocation" ? <PdfConvocation sanction={sanction} motif={motif} dateConvocation={dateConvocation} genre={genre} societeSt={societeSt}/>
                            :
                            printType == "Fin essai" ? <PdfFinEssai sanction={sanction} motif={motif} genre={genre} societeSt={societeSt}/>
                            :
                            printType == "Licenciement" ? <PdfLicenciement sanction={sanction} motif={motif} genre={genre} societeSt={societeSt}/>
                            :
                            printType == "Avertissement Non Respect" ? <PdfAvertNonRespect sanction={sanction} motif={motif} avertissementGrade={avertissementGrade} genre={genre} nbJour={nbJour} societeSt={societeSt}/>
                            :
                            printType == "Rupture de Stage" ? <PdfRuptureStage sanction={sanction} motif={motif} genre={genre} societeSt={societeSt}/>
                            : 
                            printType == "Mise à pied" ? <PdfMisePied absence={sanction} motif={motif} genre={genre} jour={nbJour} societeSt={societeSt}/> : null
                        } 
                        fileName={
                            (
                            printType == "Avertissement Bouton" ? "AV_BOUTON"
                            :
                            printType == "Avertissement Bouton Abusif" ? "AV_BOUTONABUSIF"
                            :
                            printType == "Avertissement Biometrique" ? "AV_BIOMETRIQUE"
                            :
                            printType == "Avertissement Check Phone" ? "AV_CHECKPHONE"
                            :
                            printType == "Avertissement Sommeil/Retard" ? "AV_SOMMEIL_RETARD"
                            :
                            printType == "Avertissement TAG" ? "AV_TAG"
                            :
                            printType == "Autre Avertissement" ? "AV_AUTRE"
                            :
                            printType == "Convocation" ? "CONVOCATION"
                            :
                            printType == "Fin essai" ? "FIN_ESSAI"
                            :
                            printType == "Licenciement" ? "LICENCIEMENT"
                            :
                            printType == "Avertissement Non Respect" ? "AV_NONRESPECT"
                            :
                            printType == "Rupture de Stage" ? "RUPTURE_STAGE"
                            :
                            printType == "Mise à pied"? "MISE_A_PIED" 
                            : ""
                            ) + "_" +
                            (matricule(sanction))
                        }>
                        {({ loading }) =>
                            loading ? (
                                <span></span>
                            ) : (
                                <button style={{
                                    display: 'inline-block', 
                                    fontSize: '12pt', 
                                    padding: '10px 15px',
                                    width: '150px',
                                    border: 'none',
                                    color: '#fff',
                                    cursor: 'pointer'}} 
                                    className='btn-primary'
                                    onClick={() => {setShowPrintModal(false);setMotif("");setPrintType("");setDateConvocation("")}}>
                                        Imprimer
                                </button>
                            )
                        }
                    </PDFDownloadLink>
                }
                
                <button onClick={() => {setShowPrintModal(false);setMotif("");setPrintType("")}}>Annuler</button>
            </div>
        </div>
    </div>
}
