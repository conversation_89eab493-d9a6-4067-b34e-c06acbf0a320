import React, { useEffect, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import LoadingPage from '../../loading/LoadingPage'
import useToken from '../../util/useToken'
import axios from 'axios'
import ShowHeader from '../../view/ShowHeader'
import matricule from '../../util/matricule'
import moment from 'moment'
import "../../layout/tab.css"

export default function ShowAnomalie({ auth, currentId, setCurrentId, size }) {
    const [isLoading, toggleLoading] = useState(false)
    const [anomalie, setAnomalie] = useState()
    const [activeMenu, setActiveMenu] = useState("non_prevue")
    const params = useParams()
    const [site, setSite] = useState()
    const locationSearch = new URLSearchParams(useLocation().search);
    const date_service = locationSearch.get('date_service') ? moment(locationSearch.get('date_service')) : moment().subtract(1, 'days')

    const viewAgent = (activeMenue, anomalie) => {
        if (activeMenue === 'non_prevue') {
            const agents = anomalie.ptg_agents.filter(agent =>
                !anomalie.pl_ptg_agents.some(plAgent => plAgent.employe_id === agent.employe_id)
            );

            return agents.map((agent, index) => (
                <div key={index} className='card-container'>
                    <span className="text secondary">{matricule(agent)} {agent.employe}</span>
                </div>
            ));
        } else {
            const agents = anomalie.pl_ptg_agents.filter(agent =>
                !anomalie.ptg_agents.some(ptgAgent => ptgAgent.employe_id === agent.employe_id)
            );
            return agents.map((agent, index) => (
                <div key={index} className='card-container'>
                    <span className="text secondary">{matricule(agent)} {agent.employe}</span>
                </div>
            ));
        }
    };


    useEffect(() => {
        if (!anomalie?.ptg_agents?.length > 0) {
            setActiveMenu('non_pointes')
        }
    }, [anomalie])

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/anomalie/show/' + (currentId ? currentId : params.id) + "?date_service=" + date_service.format('YYYY-MM-DD HH:mm:ss'), useToken())
            .then((res) => {
                if (isMounted) {
                    if (!res.data)
                        setCurrentId()
                    else {
                        setAnomalie(res.data.anomalie)
                        setSite(res.data.site)
                    }
                }
                toggleLoading(false)
            })
            .catch((e) => {
                console.error(e)
                toggleLoading(false)
            })
    }
    useEffect(() => {
        updateData()
    }, [currentId])

    return (
        <div>
            {
                isLoading ?
                    <LoadingPage />
                    :
                    <>
                        {anomalie &&
                            <div>
                                <ShowHeader size={size} label="Anomalie" id={currentId} closeDetail={() => setCurrentId()} />
                                <div className="card-container">
                                    <div className="badge-container">
                                        <span>
                                            {
                                                anomalie.surplus > 0 ?
                                                    <span className="badge-outline badge-outline-purple">Surplus: {anomalie.surplus}</span>
                                                    :
                                                    anomalie.manque > 0 ?
                                                        <span className={"badge-outline badge-outline-pink"}>
                                                            Manque: {anomalie.manque}
                                                        </span>
                                                        :
                                                        <span className={"badge-outline badge-outline-orange"}>
                                                            Incohérence: {anomalie.incoherence}
                                                        </span>
                                            }
                                        </span>
                                    </div>
                                    <h3><div>{anomalie.site}</div></h3>
                                    {anomalie?.non_pointes > 0 &&
                                        <p style={{ whiteSpace: "pre-line" }}>
                                            Employé absent planifié: <span className='text'>{anomalie.non_pointes}</span>
                                        </p>
                                    }
                                    {anomalie?.non_planning > 0 &&
                                        <p style={{ whiteSpace: "pre-line" }}>
                                            Non planifié mais présent: <span className='text'>{anomalie.non_planning}</span>
                                        </p>
                                    }
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Nb agents plannifiés: <span className='text'>{anomalie.pl_ptg_agents.length}</span>
                                    </p>
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Nb agents pointés : <span className='text'>{anomalie.ptg_agents.length}</span>
                                    </p>
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Incohérence: <span className='text'>{anomalie.incoherence}</span>
                                    </p>
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Horaire du site: <span className='text'>{site.horaire}</span>
                                    </p>
                                    {
                                        site.total_hour &&
                                        <p style={{ whiteSpace: "pre-line" }}>
                                            Heures facturés: <span className='text'>{site.total_hour} Heures</span>
                                        </p>
                                    }
                                    {
                                        site.superviseur_id &&
                                        <p style={{ whiteSpace: "pre-line" }}>
                                            Superviseur: <span className='text'>{site.superviseur + " <" + site.superviseur_email + ">"}</span>
                                        </p>
                                    }
                                    {
                                        site.resp_sup_id &&
                                        <p style={{ whiteSpace: "pre-line" }}>
                                            Manager: <span className='text'>{site.resp_sup + " <" + site.resp_sup_email + ">"}</span>
                                        </p>
                                    }

                                    <div className="tab-container">
                                        <div className="tab-list">
                                            {anomalie.ptg_agents.length > 0 &&
                                                <div className={activeMenu == 'non_prevue' ? 'active' : ''} onClick={() => setActiveMenu('non_prevue')}>Non planifié</div>
                                            }
                                            <div className={activeMenu == 'non_pointes' ? 'active' : ''} onClick={() => setActiveMenu('non_pointes')}>Absent</div>
                                        </div>
                                        <div className="tab-content">
                                            <div >
                                                {viewAgent(activeMenu, anomalie)}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </>
            }
        </div>
    )
}
