import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';
import numberUtil from '../util/numberUtil'

import useToken from '../util/useToken';
import matricule from '../util/matricule';
import ActionPrime from './ActionPrime';
import Tab from '../layout/Tab';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';
import moment from 'moment';

export default function ShowPrime({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [prime, setPrime] = useState()
    const [isLoading, toggleLoading] = useState(false)
    const [defautUsers, setDefautUsers] = useState()
    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/prime/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setPrime(res.data)
                const newUser = []
                if (auth.id != res.data.user_id) 
                    newUser.push({ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom })
                setDefautUsers(newUser)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(prime)
    }, [prime])

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                prime &&
                <>
                    <ShowHeader size={size} label="Prime" id={prime.id} closeDetail={() => setCurrentId()}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + prime.status_color}>
                                    {prime.status_description}
                                </span> {
                                    prime.nb_pj > 0 &&
                                    <span className='badge-outline'>
                                        Pièce jointe : {prime.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {matricule(prime)} {prime.employe} 
                        </h3>
                        {
                            prime.site &&
                            <div>
                                <span className='text'>Site : {prime.site}</span>
                            </div>
                        }
                        {
                            prime.objet &&
                            <div>
                                Objet : <span className='text'>{prime.objet}</span>
                            </div>
                        }
                        {
                            prime.montant > 0 &&
                            <div>
                                Montant : <span className='text'>{numberUtil(prime.montant)}</span>
                            </div>
                        }
                        <p>
                            Motif : <span className='text'>{prime.motif}</span>
                        </p>
                        {
                            prime.user_id != prime.superviseur_id && prime.superviseur_id &&
                            <div>
                                Superviseur responsable : <span className='text'> 
                                    {prime.sup_nom} {' <' + prime.sup_email + '>'}
                                </span>
                            </div>
                        }
                        <div>
                            Demandeur : <span className='text'> 
                                {prime.user_nom} {' <' + prime.user_email + '>'}
                            </span>
                        </div>
                        <div>
                            Le : <span className='text'> 
                                {moment(prime.created_at).format("DD MMMM YYYY")}
                            </span>
                        </div>
                        <div className='card-action'>
                            <ActionPrime auth={auth} prime={prime} updateData={updateData} toggleLoading={toggleLoading}/>
                        </div>
                    </div>
                        <Tab auth={auth} name="prime_id" value={prime.id} updateData={updateData} defautUsers={defautUsers} />
                </>
            }
        </div>
    }</>
}