import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import InputText from '../input/InputText';
import DualContainer from '../container/DualContainer';

export default function ApprovisionnementModal({action, data, closeModal, updateData}) {
    const [qteStock, setQteStock] = useState(data.nbStock)
    const [newQte, setNewQte] = useState("")
    const [error, setError] = useState()

    useEffect(() => {
        if (newQte) {
            setQteStock(data.nbStock + Number(newQte))
        } else {
            setQteStock(data.nbStock)
        }
    }, [newQte])
    
    const handleOk = () => {
        const payload = {
            nbStock: qteStock
        }
        axios.post(action.request, payload, useToken())
        .then((res) => {
            if (res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if (res.data.error)
                setError(res.data.error)
            else {
                if (closeModal) closeModal()
                if (updateData) updateData(true)
            }
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    return (
        <div className='modal'>
            <div>
                <h2>Approvisionner le stock</h2>
                <DualContainer>
                    <InputText
                        disabled
                        label="Stock existant"
                        value={qteStock}
                    />
                    <InputText
                        disabled={false}
                        label="Qte à ajouter"
                        value={newQte}
                        onChange={setNewQte}
                    />
                </DualContainer>
                <div className='form-button-container'>
                    <button className='btn-primary' onClick={handleOk}>Envoyer</button>
                    <button type='button' onClick={closeModal}>Annuler</button>
                </div>
            </div>
        </div>
    )
}