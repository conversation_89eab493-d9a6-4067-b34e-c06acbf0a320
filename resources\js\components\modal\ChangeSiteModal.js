import axios from 'axios'
import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';

export default function ChangeSiteModal({employe, closeModal, updateData}) {
    const [error, setError] = useState()
    const [sites, setSites] = useState([])
    const [disabled, setDisabled] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [searchValue, setSearchValue] = useState("")
    const [selectedSite, setSelectedSite] = useState(null)

    const handleSelectSite = (st) => {
        setSelectedSite(st)
    }

    const handleConfirmModal = (e) => {
        e.preventDefault()
        axios.post("/api/employe/change_real_site/" + employe.id, {"site_id" : selectedSite.id}, useToken())
        .then((res) => {
            if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
            else {
                if(closeModal) closeModal()
                if(updateData) updateData(true)
            } 
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
        })
    }
    
    const handleSearch = (initial, e) => {
        if(e) e.preventDefault()
        const params = new URLSearchParams()
        if(initial){
            setDataLoaded(true)
            setSites([])
            params.set("offset", 0)
        }
        else
            params.set("offset", sites.length)
        params.set("value", searchValue.replace('+', '%2B'))
        axios.get('/api/site/search_with_pointage?' + params, useToken())
        .then((res) => {
            if(res.data){
                if(initial)
                    setSites(res.data.sites)
                else {
                    const list = sites.slice().concat(res.data.sites)
                    setSites(list)
                }
                setDataLoaded(res.data.sites.length < 30)
            }
        })
        .catch((e) => {
            console.error(e)
            setDisabled(false)
            setData(null)
        })
    }
    
    const fetchMoreData = () => {
        setTimeout(() => {
            handleSearch()
        }, 300);
    }

    useEffect(() => {
        handleSearch(true)
    }, [])

    return <div className='modal'>
        <div>
            <h2>
                Site
            </h2>
            <div className='search-container'>
                <input type="text" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} placeholder="Nom du site"/>
                <button onClick={(e) => handleSearch(true, e)} disabled={disabled}>Rechercher</button>
            </div>
            <div id="scrollableList"> 
                <InfiniteScroll
                    dataLength={sites.length}
                    next={fetchMoreData}
                    hasMore={!allDataLoaded}
                    loader={<LoadingPage/>}
                    scrollableTarget="scrollableList"
                >
                    <div className='list-container'>
                        <ul>
                            {
                                sites.map(st => {
                                    return <li key={st.id} onClick={() => handleSelectSite(st)} 
                                    className={(selectedSite && selectedSite.id == st.id) ? "selected" : ""}>
                                        {st.nom}<br/>
                                        <span className='secondary'>{st.group}</span>
                                    </li>
                                })
                            }
                        </ul>
                    </div>
                </InfiniteScroll>
            </div>
            {
                error &&
                <div className='danger'><br/>{error}</div>
            }
            <div className='form-button-container'>
                {
                    selectedSite &&
                    <button onClick={handleConfirmModal} className='primary'>Confirmer</button>
                }
                <button onClick={closeModal}>Annuler</button>
            </div>
        </div>
    </div>;
}