import axios from 'axios';
import React, { useEffect, useState } from 'react';
import {Link, useLocation} from 'react-router-dom'
import useToken from '../util/useToken';
import { RiShirtLine } from 'react-icons/ri';

export default function TypeEquipement({auth}) {
    const [typeEquipements, setTypeEquipements] = useState(null)
    const me = useLocation().search == "?me=1"
    useEffect(() => {
        let isMounted = true
        axios.get("/api/type_equipement" + (me ? "?personal=1" : ""), useToken())
        .then((res) => {
            if(isMounted) {
                setTypeEquipements(res.data)
            }
        })
        .catch( e => console.error(e))
        return () => {
            isMounted = false
        }
    }, [])

    return <div id="wrapper">
        <div>
            <div className='menu-card'>
                <h3 className='sub-title-menu'>
                    <RiShirtLine className='menu-icon'/> 
                    <span>équipement</span>
                    </h3>
                <div className='palette-container'>
                    {
                        typeEquipements && typeEquipements.map(item => (
                            <div key={item.name} className='palette-item'>
                                <Link className='link-no-style' to={"/equipement/add/" + item.name + (!["validateur","superviseur","resp_sup","resp_op"].includes(auth.role) || me ? "?me=1" : "")}>{item.description}</Link>
                            </div>
                        ))
                    }
                </div>
            </div>
        </div>
    </div>;
}