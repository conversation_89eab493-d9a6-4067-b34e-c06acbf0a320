import { useState } from "react"
import MapModal from "../modal/MapModal"

export default function InputMap({label, value, onChange, required}) {
    const [modalOpen, toggleModal] = useState(false)

    const handleSelectPosition = (latitude, longitude) => {
        onChange(latitude + "," + longitude)
        handleCloseModal()
    }

    const handleCloseModal = () => {
        toggleModal(false)
    }

    return <div>
        <div className='input-container'>
            <label>
                {label} 
                {required && <span className='danger'>*</span>}
            </label>
            <input
                type="text"
                value={value}
                readOnly
                onClick={() => {toggleModal(true)}}
            />
        </div>
        {
            modalOpen &&
            <MapModal closeModal={handleCloseModal} handleSelectPosition={handleSelectPosition}/>
        }
    </div>
}