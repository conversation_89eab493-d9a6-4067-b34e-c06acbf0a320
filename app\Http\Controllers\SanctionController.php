<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Site;
use App\Models\Sanction;
use App\Models\Pointage;
use App\Models\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\HistoriqueController;
use App\Http\Controllers\MailController;
use App\Http\Util\EmployeUtil;
use App\Models\Message;
use App\Models\NoteMessage;

class SanctionController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }
    
    public function show($id){
        $sanction = DB::select("SELECT sanc.id, sanc.user_id, sanc.employe_id, sanc.superviseur_id, sanc.site_id, sanc.pointage_id,
            sanc.status, sanc.created_at, sanc.objet, sanc.montant, sanc.motif, sanc.date_pointage, sanc.absence,
            stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site', 
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
            a.fonction_id, a.date_embauche, a.date_confirmation,
            a.date_conf_soit, a.categorie, us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom',
            coalesce(ur.email, sup.email) as 'sup_email'
            FROM sanctions sanc
            LEFT JOIN employes a on a.id = sanc.employe_id
            LEFT JOIN sites st on st.idsite = sanc.site_id
            LEFT JOIN users us on us.id = sanc.user_id
            LEFT JOIN users sup on sup.id = sanc.superviseur_id
            LEFT JOIN users ur on ur.id = sup.real_email_id
            LEFT JOIN `status` stat on stat.name = sanc.status
            where sanc.id = ?", [$id])[0];
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj
            WHERE pj.sanction_id = ?
            order by pj.created_at desc", [$id]);
        $sanction->nb_pj = count($pieces);
        return response()->json($sanction);
    }

    public function employe($id, Request $request){
        $sanction = Sanction::find($id);
        if($request->done){
            $last_sanctions = DB::select("SELECT sanc.id, sanc.objet, sanc.motif, sanc.date_pointage, sanc.created_at, st.nom as 'site'
                FROM sanctions sanc
                left join sites st on st.idsite = sanc.site_id
                where sanc.status = 'done' && sanc.id != ? && sanc.employe_id = ?
                order by sanc.updated_at desc
                limit 10", [$id, $sanction->employe_id]);
        }
        else {
            $last_sanctions = DB::select("SELECT sanc.id, sanc.status, sanc.objet, sanc.motif, sanc.date_pointage, sanc.created_at,
                stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site'
                FROM sanctions sanc
                left join sites st on st.idsite = sanc.site_id
                LEFT JOIN `status` stat on stat.name = sanc.status
                where sanc.id != ? && sanc.employe_id = ?
                order by sanc.updated_at desc
                limit 10", [$id, $sanction->employe_id]);
        }
        return response()->json($last_sanctions);
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = "sanc.id = ". $request->id ." ";
        else {
            if($request->status)
                $searchArray[] = "sanc.status = '". $request->status ."'";
            if($request->created_at)
                $searchArray[] = " sanc.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "sanc.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " sanc.user_id = " . $request->user_id . " ";
            if($request->employe_id)
                $searchArray[] = " sanc.employe_id = " . $request->employe_id . " ";
            if($request->site_id)
                $searchArray[] = " sanc.site_id = " . $request->site_id . " ";
            if($request->superviseur_id)
                $searchArray[] = " sanc.superviseur_id = " . $request->superviseur_id . " ";
            if($request->date_pointage)
                $searchArray[] = " sanc.date_pointage = '" . $request->date_pointage . "' ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by sanc.id desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by sanc.id desc limit ". $request->offset . ", 30";
        return compact('query_where', 'query_and');
    }

    public function index(Request $request){
        $search = $this->search($request);
        if(in_array($request->user()->role, ['rh', 'resp_rh', 'validateur', 'access', 'daf','resp_sup'])){
            $sanctions = DB::select("SELECT sanc.id, sanc.user_id, sanc.employe_id, sanc.superviseur_id, sanc.site_id, sanc.pointage_id,
                sanc.status, sanc.created_at, sanc.objet, sanc.montant, sanc.motif, sanc.date_pointage, sanc.absence,
                stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site', 
                a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
                FROM sanctions sanc
                LEFT JOIN employes a on a.id = sanc.employe_id
                LEFT JOIN sites st on st.idsite = sanc.site_id
                LEFT JOIN users us on us.id = sanc.user_id
                LEFT JOIN users sup on sup.id = sanc.superviseur_id
                LEFT JOIN users ur on ur.id = sup.real_email_id
                LEFT JOIN `status` stat on stat.name = sanc.status
                ". $search['query_where'], []);
        }
        else if(in_array($request->user()->role, ['resp_op'])){
            $regions = RegionUsersController::getRegions($request);
            $sanctions = DB::select("SELECT sanc.id, sanc.user_id, sanc.employe_id, sanc.superviseur_id, sanc.site_id, sanc.pointage_id,
            sanc.status, sanc.created_at, sanc.objet, sanc.montant, sanc.motif, sanc.date_pointage, sanc.absence,
            stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site', 
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
            st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
            FROM sanctions sanc
            LEFT JOIN employes a on a.id = sanc.employe_id
            LEFT JOIN sites st on st.idsite = sanc.site_id
            LEFT JOIN users us on us.id = sanc.user_id
            LEFT JOIN users sup on sup.id = sanc.superviseur_id
            LEFT JOIN users ur on ur.id = sup.real_email_id
            LEFT JOIN `status` stat on stat.name = sanc.status
            WHERE st.group_pointage_id IN ($regions)
            ". $search['query_and'], []);
        }
        else if(in_array($request->user()->role, ['superviseur'])){
            $sanctions = DB::select("SELECT sanc.id, sanc.user_id, sanc.employe_id, sanc.superviseur_id, sanc.site_id, sanc.pointage_id,
                sanc.status, sanc.created_at, sanc.objet, sanc.montant, sanc.motif, sanc.date_pointage, sanc.absence,
                stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site', 
                a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
                FROM sanctions sanc
                LEFT JOIN employes a on a.id = sanc.employe_id
                LEFT JOIN sites st on st.idsite = sanc.site_id
                LEFT JOIN users us on us.id = sanc.user_id
                LEFT JOIN users sup on sup.id = sanc.superviseur_id
                LEFT JOIN users ur on ur.id = sup.real_email_id
                LEFT JOIN `status` stat on stat.name = sanc.status
                where sanc.user_id = ? or sanc.superviseur_id = ?" . $search['query_and']
                , [$request->user()->id, $request->user()->id]);
        }
        else if(in_array($request->user()->role, ['room'])){
            $sanctions = DB::select("SELECT sanc.id, sanc.user_id, sanc.employe_id, sanc.superviseur_id, sanc.site_id, sanc.pointage_id,
                sanc.status, sanc.created_at, sanc.objet, sanc.montant, sanc.motif, sanc.date_pointage, sanc.absence,
                stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site', 
                a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
                FROM sanctions sanc
                LEFT JOIN employes a on a.id = sanc.employe_id
                LEFT JOIN sites st on st.idsite = sanc.site_id
                LEFT JOIN users us on us.id = sanc.user_id
                LEFT JOIN users sup on sup.id = sanc.superviseur_id
                LEFT JOIN users ur on ur.id = sup.real_email_id
                LEFT JOIN `status` stat on stat.name = sanc.status
                where ((us.role='room' and sanc.created_at >= ?) or sanc.status = 'confirmation') " . $search['query_and']
                , [$this->getDateLimit()]);
        }
        return response(compact('sanctions'));
    }

    function getDateLimit() {
        $current_date = new \DateTime();
        if(new \DateTime >= (new \DateTime)->setTime(6, 0, 0) &&
                new \DateTime < (new \DateTime)->setTime(18, 00, 0)){
            $limit_date = (new \DateTime)->setTime(6, 00, 0)->format('Y-m-d H:i:s');
        }
        else {
            if(new \DateTime < (new \DateTime)->setTime(06, 0,0)){
                $limit_date = (new \DateTime)->sub(new \DateInterval('P1D'))->setTime(18, 0, 0)->format('Y-m-d H:i:s');
            }
            else
                $limit_date = (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
        }
        return $limit_date;
    }
    
    protected function validateAndSetSanction($request, $sanction){
        $auth = $request->user();
        $sanction->mesure = null;
        $sanction->objet = null;
        $sanction->montant = null;
        if(in_array($request->user()->role, ['superviseur','resp_sup','resp_op'])){
            if($request->absence){
                $validator = Validator::make($request->all(), [
                    'employe_id' => 'required',
                    'site_id' => 'required',
                    'motif' => 'required',
                ]);
                if($request->user()->role == 'superviseur'){
                    $site = Site::find($request->site_id);
                    $site->superviseur_id = $request->user()->id;
                    $site->save();
                }
            }
            else {
                $validator = Validator::make($request->all(), [
                    'employe_id' => 'required',
                    'pointage_id' => 'required',
                    'motif' => 'required',
                ]);
                if($request->user()->role == 'superviseur'){
                    $pointage = Pointage::find($request->pointage_id);
                    $site = Site::find($pointage->site_id);
                    $site->superviseur_id = $request->user()->id;
                    $site->save();
                }
            }
            $sanction->superviseur_id = $auth->id;
        }
        else if($auth->role == "room"){
            if($request->absence)
                $validator = Validator::make($request->all(), [
                    'employe_id' => 'required',
                    'site_id' => 'required',
                    'motif' => 'required',
                    'mesure' => 'required',
                    'superviseur_id' => 'required',
                ]);
            else {
                if($auth->id == 191){
                    $validator = Validator::make($request->all(), [
                        'employe_id' => 'required',
                        'pointage_id' => 'required',
                        'motif' => 'required',
                    ]);
                    $pointage = Pointage::find($request->pointage_id);
                    if($pointage != null){
                        $site = Site::find($pointage->site_id);
                        if($site != null)
                            $sanction->superviseur_id = $site->superviseur_id;
                    }
                }
                else
                    $validator = Validator::make($request->all(), [
                        'employe_id' => 'required',
                        'pointage_id' => 'required',
                        'motif' => 'required',
                        'mesure' => 'required',
                        'superviseur_id' => 'required',
                    ]);
                    $sanction->superviseur_id = $request->superviseur_id;
            }
            $sanction->mesure = $request->mesure;
        }
        else if(in_array($auth->role, ["rh", "resp_rh"])){
            if($request->absence){
                $validator = Validator::make($request->all(), [
                    'employe_id' => 'required',
                    'site_id' => 'required',
                    'objet' => 'required',
                    'montant' => 'integer',
                    'motif' => 'required',
                    'superviseur_id' => 'required',
                ]);
                
                $site = Site::find($request->site_id);
                $site->superviseur_id = $request->superviseur_id;
                $site->save();
                
            }
            else {
                $validator = Validator::make($request->all(), [
                    'employe_id' => 'required',
                    'pointage_id' => 'required',
                    'objet' => 'required',
                    'montant' => 'integer',
                    'motif' => 'required',
                    'superviseur_id' => 'required',
                ]);
                
                $pointage = Pointage::find($request->pointage_id);
                $site = Site::find($pointage->site_id);
                $site->superviseur_id = $request->superviseur_id;
                $site->save();
                
            }
            $sanction->objet = $request->objet;
            $sanction->montant = $request->montant;
            $sanction->superviseur_id = $request->superviseur_id;
        }
        if($sanction->id == null || $auth->id == $sanction->user_id){
            $sanction->employe_id = $request->employe_id; 
            
            $sanction->absence = null;
            $sanction->pointage_id = null;
            $sanction->site_id = null;
            $sanction->date_pointage = null;
            if($request->absence){
                $sanction->absence = true;
                $sanction->site_id = $request->site_id;
            }
            else if($request->pointage_id){
                $sanction->pointage_id = $request->pointage_id;
                $pointage = Pointage::find($request->pointage_id);
                if($pointage == null && $request->date_pointage)
                    $pointage = Pointage::where($request->employe_id)->where($request->date_pointage)->first();
                if($pointage != null){
                    $sanction->site_id = $pointage->site_id;
                    $sanction->date_pointage = $pointage->date_pointage;
                }
                if($pointage == null){
                    $request->pointage_id = null;
                    $validator = Validator::make($request->all(), [
                        'pointage_id' => 'required',
                    ]);
                }
            }
            $sanction->motif = $request->motif;
        }
        return $validator;
    }

    public function store(Request $request){
        $auth = $request->user();
        if(in_array($auth->role, ["rh", "resp_rh", "superviseur", "resp_sup", "resp_op", "room"])){
            $sanction = new Sanction();
            $validator = $this->validateAndSetSanction($request, $sanction);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sanction->user_id = $auth->id;
            if(in_array($auth->role, ["rh", "resp_rh"]))
                $sanction->status = "traite";
            if($auth->id == 191 && $request->status)
                $sanction->status = $request->status;
            else
                $sanction->status = "demande";

            $sanction->created_at = new \DateTime();
            $sanction->updated_at = new \DateTime();

            if($sanction->save()){
                HistoriqueController::new_sanction($request, $sanction);
                return response(["success" => "Sanction bien envoyée", "id" => $sanction->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function edit(Request $request, $id){
        $sanction = Sanction::find($id);
        $old_sanction = clone $sanction;
        $auth = $request->user();
        if(in_array($auth->role, ["rh", "resp_rh"]) && $sanction->status == "traite"){
            $validator = $this->validateAndSetSanction($request, $sanction);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
                
            $sanction->updated_at = new \DateTime();

            if($sanction->save()){
                HistoriqueController::update_sanction($request, $old_sanction, "Sanction modifié");
                return response(["success" => "Sanction modifié", "id" => $sanction->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function do_traite(Request $request, $id){
        $sanction = Sanction::find($id);
        $old_sanction = clone $sanction;
        if(in_array($request->user()->role, ["rh", "resp_rh"]) && $sanction->status == "demande"){
            $validator = $this->validateAndSetSanction($request, $sanction);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            
            $sanction->status = "traite";
            $sanction->updated_at = new \DateTime();

            if($sanction->save()){
                HistoriqueController::update_sanction($request, $old_sanction, "Sanction en cours de traitement");
                return response(["success" => "Sanction bien envoyée", "id" => $sanction->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $sanction = Sanction::find($id);
        $old_sanction = clone $sanction;
        if($request->user()->id == $sanction->user_id && $sanction->status == "draft"){
            $validator = $this->validateAndSetSanction($request, $sanction);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            if (in_array($request->user()->role, ["rh", "resp_rh"]))
                $sanction->status = "traite";
            else
                $sanction->status = "demande";
            $sanction->updated_at = new \DateTime();

            if($sanction->save()){
                HistoriqueController::update_sanction($request, $old_sanction, "Renvoie de la demande");
                return response(["success" => "Renvoie de la demande réussi", "id" => $sanction->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }
    
    public function request_validation(Request $request, $id){
        $sanction = Sanction::find($id);
        if(in_array($request->user()->role, ["rh", "resp_rh"]) && in_array($sanction->status, ['traite'])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sanction->status = 'validation';
            $sanction->updated_at = new \DateTime();

            $sanction->note_id = HistoriqueController::action_sanction($request, "Requête de validation", $id);
            if($sanction->save()){
                return response(["success" => "Demande de validation envoyé", "id" => $sanction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function reply_validation(Request $request, $id){
        $sanction = Sanction::find($id);
        if($request->user()->role == 'validateur' && $sanction->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sanction->status = 'traite';
            $sanction->updated_at = new \DateTime();

            $sanction->note_id = HistoriqueController::action_sanction($request, "Réponse à la demande", $id);
            if($sanction->save()){
                $users = DB::select("SELECT us.id from users us where us.role = 'rh' or us.role = 'resp_rh'", []);
                foreach ($users as $u) {
                    Notification::create([
                        'historique_id' => $sanction->note_id,
                        'receiver_id' => $u->id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                return response(["success" => "Réponse de la demande envoyé", "id" => $sanction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function confirm_sanction(Request $request, $id){
        $sanction = Sanction::find($id);
        if($request->user()->role == 'room' && $sanction->status == 'confirmation'){
            $sanction->status = 'demande';
            $sanction->updated_at = new \DateTime();
            $sanction->note_id = HistoriqueController::action_sanction($request, "Sanction confirmé", $id);
            if($sanction->save()){
                return response(["success" => "Sanction confirmé", "id" => $sanction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function cancel_confirmation(Request $request, $id){
        $sanction = Sanction::find($id);
        if($request->user()->role == 'room' && $sanction->status == 'confirmation'){
            $sanction->status = 'draft';
            $sanction->updated_at = new \DateTime();
            $sanction->note_id = HistoriqueController::action_sanction($request, "Sanction non considéré", $id);
            if($sanction->save()){
                return response(["success" => "Sanction non considéré", "id" => $sanction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function cancel_validation(Request $request, $id){
        $sanction = Sanction::find($id);
        if(in_array($request->user()->role, ["rh", "resp_rh"]) && $sanction->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sanction->status = 'traite';
            $sanction->updated_at = new \DateTime();

            $sanction->note_id = HistoriqueController::action_sanction($request, "Demande de validation annulé", $id);
            if($sanction->save()){
                return response(["success" => "Demande de validation annulé", "id" => $sanction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_sanction(Request $request, $id){
        $sanction = Sanction::find($id);
        if(
            ($request->user()->id == $sanction->user_id && in_array($request->user()->role, ['superviseur', 'resp_sup', "resp_op", 'room'])  && in_array($sanction->status, ['demande'])) || 
            (in_array($request->user()->role, ["rh", "resp_rh"]) && in_array($sanction->status, ['demande', 'traite']))
        ){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sanction->objet = null;
            $sanction->montant = null;
            $sanction->status = 'draft';
            $sanction->updated_at = new \DateTime();

            $sanction->note_id = HistoriqueController::action_sanction($request, "Sanction annulé", $id);
            if($sanction->save()){
                if(in_array($request->user()->role, ["superviseur","resp_sup","resp_op"])){
                    $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                        from users us
                        left join users ur on ur.id = us.real_email_id 
                        where us.role = 'rh' or us.role = 'resp_rh'", []);
                    $emails = [];
                    foreach ($users as $u) {
                        $emails[] = ['address' => $u->email, 'name' => $u->name];
                    }
                    //MailController::sanction($request, $sanction->id, "Sanction annulé", $emails);
                }
                else if($sanction->superviseur_id){
                    $superviseur =  DB::select("SELECT u.name, coalesce(u2.email, u.email) as 'email' from users u
                        left join users u2 on u2.id = u.real_email_id
                        WHERE u.id = ?", [$sanction->superviseur_id])[0];
                    //MailController::sanction($request, $sanction->id, "Sanction annulé"
                    //    , [["address" => $superviseur->email, "name" => $superviseur->name]]);
                }
                return response(["success" => "Sanction annulé", "id" => $sanction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function do_convocation(Request $request, $id){
        $sanction = Sanction::find($id);
        $old_sanction = clone $sanction;
        if(in_array($request->user()->role, ["rh", "resp_rh"]) && in_array($sanction->status, ['demande', 'traite'])){
            $validator = $this->validateAndSetSanction($request, $sanction);
            if($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            
            $sanction->status = 'convocation';
            $sanction->updated_at = new \DateTime();
            if($sanction->save()){
                HistoriqueController::update_sanction($request, $old_sanction, "Convocation de l'employe");
                // $superviseur =  DB::select("SELECT u.name, coalesce(u2.email, u.email) as 'email' from users u
                //     left join users u2 on u2.id = u.real_email_id
                //     WHERE u.id = ?", [$sanction->superviseur_id])[0];
                // // MailController::sanction($request, $id, "Convocation de l'employe"
                //     , [["address" => $superviseur->email, "name" => $superviseur->name]]);
                $this->message_convocation($request, $id);
                return response(["success" => "Convocation envoyé", "id" => $sanction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    protected function message_convocation(Request $request, $id){
        $sanction = DB::select("SELECT sanc.id, sanc.user_id, sanc.employe_id, sanc.superviseur_id, sanc.site_id, sanc.motif, sanc.date_pointage,
            sanc.status, sanc.created_at, sanc.objet, sanc.montant, sanc.absence,
            stat.description as 'status_description', stat.color as 'status_color',
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
            st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
            FROM sanctions sanc
            LEFT JOIN employes a on a.id = sanc.employe_id
            LEFT JOIN sites st on st.idsite = sanc.site_id
            LEFT JOIN users us on us.id = sanc.user_id
            LEFT JOIN users sup on sup.id = sanc.superviseur_id
            LEFT JOIN `status` stat on stat.name = sanc.status
            where sanc.id = ?", [$id])[0];

        $last_sanctions = DB::select("SELECT sanc.id, sanc.objet, sanc.motif, sanc.date_pointage, sanc.absence
            FROM sanctions sanc
            where sanc.status = 'done' && sanc.id != ? && sanc.employe_id = ?
            order by sanc.date_pointage desc
            limit 3", [$id, $sanction->employe_id]);


        if($sanction->date_pointage){
            $date_pointage = new \DateTime($sanction->date_pointage);
            if($date_pointage->format('H:i:s') == "07:00:00")
                $sanction->date_service = $date_pointage->format('d/m/Y') . ' JOUR';
            else
                $sanction->date_service = $date_pointage->format('d/m/Y') . ' NUIT';
        }

        foreach ($last_sanctions as $sanc) {
            if($sanc->date_pointage){
                $date_pointage = new \DateTime($sanc->date_pointage);
                if($date_pointage->format('H:i:s') == "07:00:00")
                    $sanc->date_service = $date_pointage->format('d/m/Y') . ' JOUR';
                else
                    $sanc->date_service = $date_pointage->format('d/m/Y') . ' NUIT';
            }
        }

        $message = new Message();
        $employe = EmployeUtil::getEmployeById($sanction->employe_id);
        $message->objet = "Convocation de l'employé: ". $employe;
        $message->user_id = $request->user()->id;
        $message->created_at = new \DateTime();
        $message->updated_at = new \DateTime();
        $message->content = '<h2>Demande de sanction</h2>' ;
        $message->content .= '<p><b>Employé</b>: '.$employe . '</p>' . '<p><b>Site : </b>' . $sanction->site . '<p/>' ;
        $message->content .=  ($sanction->absence ? 'Sans pointage' : ('<p>Date du service :'. $sanction->date_service .' </p>'));
        $message->content .= $sanction->objet ? '<p><b>Objet: </b>'. $sanction->objet . '<p/>' : '';
        $message->content .= $sanction->montant > 0 ? '<p><b>Montant: </b>' . $sanction->montant . 'Ar </p>' : '';
        $message->content .= '<p><b>Motif: </b>' . $sanction->motif . '</p>';
        $message->content .= $sanction->superviseur_id != $sanction->user_id ? '<p><b>Superviseur responsable: </b>'. $sanction->sup_nom.' &lt;' . $sanction->sup_email . '&gt;</p>' : '';
        $message->content .= '<p><b>Demandeur: </b>'. $sanction->user_nom . ' &lt;' . $sanction->user_email . '&gt;</p>';
        $last_content = '';
        if(count($last_sanctions) > 0){
            $last_content .= '<h3>Sanction déjà reçu par l\'employe</h3>';
            foreach ($last_sanctions as $sanc) {
                $last_content .= '<p>*'.$sanc->objet . '<p/>';
                $last_content .= '<p><b>Motif: </b>' . $sanc->motif . '</p>';
                $last_content .= $sanc->absence == true ? '<p>Sans pointage</p>': '<p><b>Date du service: </b>' . $sanc->date_service . '</p>';
            }     
        }
        $message->content .= $last_content;
        if ($message->save()) {
            $note = new NoteMessage();
            $note->message_id = $message->id;
            $note->user_id = $sanction->superviseur_id;
            $note->follow = 1;
            $note->created_at = new \DateTime();
            $note->updated_at = new \DateTime();
            $note->save();
        }
    }

    public function cancel_convocation(Request $request, $id){
        $sanction = Sanction::find($id);
        if(in_array($request->user()->role, ["rh", "resp_rh"]) && in_array($sanction->status, ['convocation'])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sanction->status = 'traite';
            $sanction->updated_at = new \DateTime();

            $sanction->note_id = HistoriqueController::action_sanction($request, "Convocation annulé", $id);
            if($sanction->save()){
                $superviseur =  DB::select("SELECT u.name, coalesce(u2.email, u.email) as 'email' from users u
                    left join users u2 on u2.id = u.real_email_id
                    WHERE u.id = ?", [$sanction->superviseur_id])[0];
                MailController::sanction($request, $id, "Convocation annulé"
                    , [["address" => $superviseur->email, "name" => $superviseur->name]]);
                return response(["success" => "Convocation annulé", "id" => $sanction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done(Request $request, $id){
        $sanction = Sanction::find($id);
        if(in_array($request->user()->role, ["rh", "resp_rh"]) && in_array($sanction->status, ['convocation', 'traite'])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $sanction->status = 'done';
            $sanction->updated_at = new \DateTime();

            $sanction->note_id = HistoriqueController::action_sanction($request, "Sanction terminé", $id);
            if($sanction->save()){
                return response(["success" => "Sanction terminé", "id" => $sanction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
}
