<?php

namespace App\Http\Controllers;

use App\Http\Util\EmployeUtil;
use App\Models\Employe;
use App\Models\PartVariable;
use App\Models\CritereMensuel;
use App\Models\CriterePart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PartVariableController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    private static $attributeNames = array(
        'employe_id' => 'Employé',
        'commentaire' => 'Commentaire',
        'criteres' => 'Critère',
        'date_paie' => 'Date de la fiche de paie'
    );

    protected static function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = "pv.id = '". $request->id ."'";
        else {
            if($request->employe_id)
                $searchArray[] = " pv.employe_id = " . $request->employe_id . " ";
            if($request->commentaire)
                $searchArray[] = " pv.commentaire like '%" . $request->commentaire . "%' ";
            if($request->date_paie)
                $searchArray[] = " pv.date_paie = '" . $request->date_paie . "-20' ";
            if($request->status)
                $searchArray[] = "pv.status = '". $request->status ."'";
            if($request->created_at)
                $searchArray[] = " pv.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "pv.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " pv.user_id = " . $request->user_id . " ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        if(!in_array($request->user()->role, ["resp_rh", "validateur"]) || $request->user_id){
            $query_where = $query_where . ($request->offset_date ? " and pv.updated_at <= '". $request->offset_date . "'" : "") . " order by pv.updated_at desc limit ". $request->offset . ", 30";
            $query_and = $query_and . ($request->offset_date ? " and pv.updated_at <= '". $request->offset_date . "'" : "") . " order by pv.updated_at desc limit ". $request->offset . ", 30";
        }
        return compact('query_where', 'query_and');
    }

    public static function index(Request $request){
        $auth = $request->user();
        $search = self::search($request);
        if(in_array($auth->role, ["resp_rh", "validateur"])){
            if($request->user_id){
                $part_variables = DB::select("SELECT pv.id, pv.employe_id, pv.commentaire, pv.updated_at,
                    e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                    stat.color as 'status_color', pv.date_paie, pv.user_id, pv.status, stat.color
                    FROM part_variables pv
                    LEFT JOIN employes e on e.id = pv.employe_id
                    LEFT JOIN `status` stat on stat.name = pv.status
                    " . $search['query_where'], []);
            }
            else {
                $part_variables = DB::select("SELECT pv.user_id, u.name, u.email, count(pv.id) as 'nb_pv'
                    FROM part_variables pv
                    LEFT JOIN users u on u.id = pv.user_id
                    LEFT JOIN employes e on e.id = pv.employe_id
                    LEFT JOIN `status` stat on stat.name = pv.status
                    " . $search['query_where'] . " group by pv.user_id", []);
            }
        }
        else if($request->user()->employe_id){
            $ids = [$request->user()->employe_id];
            $resp_ids = array_column(DB::select("SELECT e.id FROM employes e WHERE e.resp_part_id in (" . implode(",", $ids) . ")", []), 'id');
            foreach ($resp_ids as $id){
                if(!in_array($id, $ids))
                    array_push($ids, $id);
            }
            $resp_ids = array_column(DB::select("SELECT e.id FROM employes e WHERE e.resp_part_id in (" . implode(",", $ids) . ")", []), 'id');
            foreach ($resp_ids as $id) {
                if(!in_array($id, $ids))
                    array_push($ids, $id);
            }

            $part_variables = DB::select("SELECT pv.id, pv.employe_id, pv.commentaire, pv.updated_at,
                e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                stat.color as 'status_color', pv.date_paie, pv.user_id
                FROM part_variables pv
                LEFT JOIN employes e on e.id = pv.employe_id
                LEFT JOIN `status` stat on stat.name = pv.status " 
                . (count($ids) > 0 ? " WHERE e.resp_part_id in (". implode(",", $ids) .") " . $search['query_and'] : $search['query_where'])
                , []);
        }
        else{
            return response(["error"=>"EACCES"]);
        }

        if($request->user_id || !in_array($auth->role, ["resp_rh", "validateur"])) {
            if(count($part_variables) > 0) {
                $criteres = DB::select("SELECT id, montant, part_variable_id FROM critere_mensuels 
                    WHERE part_variable_id in (". implode(",", array_column($part_variables, "id")) .")", []);
                $critere_part = DB::select("SELECT sum(montant) as 'total', employe_id FROM critere_parts 
                    WHERE (soft_delete = 0 or soft_delete is null) and employe_id in (". implode(",", array_column($part_variables, "employe_id")) . ") group by employe_id", []);
                foreach ($part_variables as $pv) {
                    $pv->montant = 0;
                    foreach ($criteres as $cr) {
                        if($cr->part_variable_id == $pv->id){
                            $pv->montant += $cr->montant;
                        }
                    }
                    foreach ($critere_part as $cp) {
                        if ($cp->employe_id == $pv->employe_id) {
                            $pv->total = $cp->total;
                        }
                    }
                }
            }
            if($request->offset == 0 && count($part_variables) > 0){
                $offset_date = $part_variables[0]->updated_at;
                return response(compact('part_variables', 'offset_date'));
            }
        }
        return response(compact('part_variables'));
    }

    public static function show($id){        
        $part_variable = DB::select("SELECT pv.id, pv.employe_id, pv.date_paie, pv.commentaire, pv.updated_at, pv.user_id, pv.status,
            e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
            f.libelle as 'fonction', ac.nom as 'agence', e.fonction_id, e.titre,
            us.name as 'user_nom', us.email as 'user_email', stat.description as 'status_description', stat.color as 'status_color'
            FROM part_variables pv
            LEFT JOIN employes e on e.id = pv.employe_id
            LEFT JOIN fonctions f on f.id = e.fonction_id
            LEFT JOIN agences ac on ac.id = e.agence_id
            LEFT JOIN `status` stat on stat.name = pv.status
            LEFT JOIN users us on us.id = pv.user_id
            WHERE pv.id = ?", [$id])[0];
        if($part_variable != null){
            $criteres = DB::select("SELECT id, montant, part_variable_id FROM critere_mensuels 
                WHERE part_variable_id = ?", [$id]);
            $critere_part = DB::select("SELECT sum(montant) as 'total', employe_id FROM critere_parts 
                WHERE (soft_delete = 0 or soft_delete is null) and employe_id = ? group by employe_id", [$part_variable->employe_id]);
            $montant = 0;
            foreach ($criteres as $cr) {
                $montant += $cr->montant;
            }
            $part_variable->montant = $montant;
            if (count($critere_part) > 0) 
                $part_variable->total = $critere_part[0]->total;
            else {
                $part_variable->total = 0;
            }
        }
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj where pj.part_variable_id = ?", [$id]);
        $part_variable->nb_pj = count($pieces);
        return response()->json($part_variable);
    }

    public static function get_part_export(Request $request){
        $auth = $request->user();
        $pvs = [];
        if (in_array($auth->role, ["resp_rh", "validateur"])) {
            $part_variables = DB::select("SELECT pv.id, pv.created_at, pv.status, pv.date_paie,
                pv.user_id, us.name as user_name, us.email as user_email, e.nom as 'employe', e.resp_part_id,
                e.societe_id, e.numero_stagiaire, e.numero_employe, e.titre, e.num_emp_soit, e.num_emp_saoi,
                cm.part_variable_id, cm.critere, cm.id as 'critere_id', cm.montant, e.fonction_id,
                fonc.libelle as 'fonction', agc.nom as 'agence'
                FROM part_variables pv
                LEFT JOIN critere_mensuels cm on cm.part_variable_id = pv.id
                LEFT JOIN employes e on e.id = pv.employe_id
                LEFT JOIN users us on us.id = pv.user_id
                LEFT JOIN fonctions fonc on fonc.id = e.fonction_id
                LEFT JOIN agences agc on agc.id = e.agence_id
                WHERE pv.status = 'done' and pv.date_paie = ? ",[$request->date_paie]);
            if(count($part_variables) > 0){
                $users = DB::select("SELECT us.employe_id, us.name as user_name, us.email as user_email FROM users us
                    LEFT JOIN employes e on e.id = us.employe_id
                    WHERE e.id in (". implode(",", array_column($part_variables, "resp_part_id")) .")");
                foreach ($part_variables as $p) {
                    foreach ($users as $u) {
                        if($u->employe_id == $p->resp_part_id){
                            $p->user_name = $u->user_name;
                            $p->user_email = $u->user_email;
                        }
                    }
                }
            }
            $pvs = [];
            foreach ($part_variables as $row) {
                if (!isset($pvs[$row->id])) {
                    $pvs[$row->id] = (object) [
                        'id' => $row->id,
                        'status' => $row->status,
                        'created_at' => $row->created_at,
                        'date_paie' => $row->date_paie,
                        'user_id' => $row->user_id,
                        'user_name' => $row->user_name,
                        'user_email' => $row->user_email,
                        'employe' => $row->employe,
                        'societe_id' => $row->societe_id,
                        'numero_stagiaire' => $row->numero_stagiaire,
                        'numero_employe' => $row->numero_employe,
                        'num_emp_soit' => $row->num_emp_soit,
                        'num_emp_saoi' => $row->num_emp_saoi,
                        "fonction" => $row->fonction,
                        'fonction_id' => $row->fonction_id,
                        'titre' => $row->titre,
                        "agence" => $row->agence,
                        "resp_part_id" => $row->resp_part_id,
                        'criteres' => []
                    ];
                }
                if ($row->critere_id) {
                    $pvs[$row->id]->criteres[] = (object) [
                        'condition' => $row->critere,
                        'montant' => $row->montant,
                        'user_name' => $row->user_name,
                    ];
                }
            }
            $pvs = array_values($pvs);
        }
        return response(compact('pvs'));
    }

    protected static function validateAndSetPartVariable($request, $part_variable){
        $auth = $request->user();
        if($part_variable->id){
            $validator = Validator::make($request->all(), [
                'employe_id' => 'required',
                'date_paie' => 'required',
            ]);
        }
        else {
            $validator = Validator::make($request->all(), [
                'employe_id' => 'required',
                'date_paie' => 'required',
                'criteres' => 'required|array',
                'criteres.*.id' => 'required|integer',
                'criteres.*.value' => 'required|integer',
            ]);
        }
        return $validator->setAttributeNames(self::$attributeNames);
    }

    public static function store(Request $request) {
        $employe = Employe::find($request->employe_id);
        $resp_resp_id = null;

        $date_paie = \DateTime::createFromFormat("Y-m-d", $request->date_paie);
        $date_begin = (new \DateTime())->setDate((new \DateTime())->format("Y"), (new \DateTime())->format("m"), 1);
        $date_end = (new \DateTime())->setDate((new \DateTime())->format("Y"), (new \DateTime())->format("m"), 1)->add(new \DateInterval('P1M'));
        if(
            $date_paie <= $date_begin ||
            ($request->user()->role != "resp_rh" && $date_paie > $date_end)
        )
            return ['error' => "La part variable pour le mois selectionné n'est pas acceptable"];

        if ($employe->resp_part_id) {
            $resp_resp_id = (Employe::find($employe->resp_part_id))->resp_part_id;
        }
        if($request->user()->role == "validateur" ||
            in_array($request->user()->role, ["resp_rh"]) || 
            $employe->resp_part_id == $request->user()->employe_id ||
            ($resp_resp_id && $resp_resp_id == $request->user()->employe_id)
        ) {
            $part_variable = new PartVariable();
            $part_variable->status = "validation";
            if ($request->user()->id == 105) {
                $part_variable->status = "done";
            }
            $part_variable->user_id = $request->user()->id;
            $part_variable->created_at = new \DateTime();
            $part_variable->updated_at = new \DateTime();
            
            if($employe->part_variable != 1)
                return ['error' => "Cette employé n'a pas de part variable"];

            $validator = self::validateAndSetPartVariable($request, $part_variable);
            if($validator->fails())
                return ['error' => $validator->errors()->first()];

            if (!in_array($request->user()->role, ["resp_rh", "validateur"])) {
                $min_date = (new \DateTime())->setDate((new \DateTime())->format('Y'), (new \DateTime())->format('m'), 20)->setTime(0, 0, 0);
                $max_date = (new \DateTime())->setDate((new \DateTime())->format('Y'), (new \DateTime())->format('m'), 24)->setTime(23, 59, 59);
                if ((new \DateTime()) < $min_date || (new \DateTime()) > $max_date) {
                    return ['error' => 'L\'envoi de la variable doit se faire entre le 20 et le 23 du mois'];
                }
            }

            $queryValidation = PartVariable::where('employe_id', $request->employe_id)->where('date_paie', $request->date_paie);
            if($queryValidation->first() != null)
                return ['error' => "La part variable de l'employé pour ce mois existe déjà"];

            $criteres = DB::select("SELECT id, montant, designation FROM critere_parts
                WHERE (soft_delete is null or soft_delete = 0) and employe_id = ?", [$request->employe_id]);
            if(count($criteres) > 0) {
                foreach ($criteres as $cr) {
                    foreach ($request->criteres as $item) {
                        if($cr->id == $item['id'])
                            $cr->value = $item['value'];
                    }
                }
                foreach ($criteres as $cr) {
                    if($cr->value > $cr->montant && $request->user()->id != 105)
                        return ['error' => "Le montant de la part variable '". $cr->designation ."' ne doit pas dépasser l'attribuable"];
                }
                

                $part_variable->employe_id = $request->employe_id;
                $part_variable->date_paie = $request->date_paie;
                $part_variable->commentaire = $request->commentaire;
                $part_variable->save();
                $all_critere_save = [];
                foreach ($criteres as $cr) {
                    $mensuel = new CritereMensuel();
                    $mensuel->part_variable_id = $part_variable->id;
                    $mensuel->critere_id = $cr->id;
                    $mensuel->montant = $cr->value;
                    $mensuel->created_at = new \Datetime();
                    $mensuel->updated_at = new \Datetime();
                    if($mensuel->save()){
                        $data = $mensuel;
                        $data->critere = $cr->designation;
                        $all_critere_save[] = $data;
                    };
                }
                HistoriqueController::new_part_variable($request, $part_variable, $all_critere_save);
                return response(["success" => "Part variable enregistré"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public static function store_multiple(Request $request){
        $date_paie = \DateTime::createFromFormat("Y-m-d", $request->date_paie);
        $date_begin = (new \DateTime())->setDate((new \DateTime())->format("Y"), (new \DateTime())->format("m"), 1);
        $date_end = (new \DateTime())->setDate((new \DateTime())->format("Y"), (new \DateTime())->format("m"), 1)->add(new \DateInterval('P1M'));
        if ($date_paie <= $date_begin || $date_paie > $date_end )
            return ['error' => "La part variable pour le mois selectionné n'est pas acceptable"];

        $ids = $request->employe_ids;
        $employes = Employe::WhereIn('id', $ids)->get();
        foreach ($employes as $emp) {
            if ($emp->part_variable != 1) {
                return ['error' => "L'employé " . EmployeUtil::getEmploye($emp) . " n'a pas de part variable"];
            }
            if ($emp->soft_delete == 1) {
                return ['error' => "L'employé " . EmployeUtil::getEmploye($emp) . " a été archivé"];
            }
        }

        if ($request->user()->role == "validateur") {
            $queryValidation = DB::select("SELECT pv.employe_id, emp.nom, emp.societe_id, emp.societe_id, 
                emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi, emp.part_variable
                FROM part_variables pv
                LEFT JOIN employes emp ON emp.id = pv.employe_id
                WHERE pv.date_paie = ? 
                AND pv.employe_id in (" . implode(",", $ids) . ")",
                [$request->date_paie]);

            if (count($queryValidation) > 0)
                return ['error' => "La part variable de l'employé " . EmployeUtil::getEmploye($queryValidation[0]) . " pour ce mois existe déjà"];

            $criteres = DB::select("SELECT id, employe_id, montant, designation FROM critere_parts
                WHERE (soft_delete is null or soft_delete = 0) and employe_id in (" . implode(",", $ids) . ") ORDER BY employe_id ASC", []);

            if (count($criteres) > 0) {
                $groupByEmploye = [];
                foreach ($criteres as $cr) {
                    $groupByEmploye[$cr->employe_id][] = $cr;
                }
                foreach ($groupByEmploye as $gre) {
                    $part_variable = new PartVariable();
                    if($request->user()->id == 105)
                        $part_variable->status = "done";
                    else
                        $part_variable->status = "validation";
                    $part_variable->user_id = $request->user()->id;
                    $part_variable->created_at = new \DateTime();
                    $part_variable->updated_at = new \DateTime();
                    $part_variable->employe_id = $gre[0]->employe_id;
                    $part_variable->date_paie = $request->date_paie;
                    $part_variable->save();
                    HistoriqueController::new_part_variable($request, $part_variable);
                    foreach ($gre as $cr) {
                        $mensuel = new CritereMensuel();
                        $mensuel->part_variable_id = $part_variable->id;
                        $mensuel->critere_id = $cr->id;
                        $mensuel->montant = $cr->montant;
                        $mensuel->created_at = new \Datetime();
                        $mensuel->updated_at = new \Datetime();
                        $mensuel->save();
                    }
                }
                return response(["success" => "Part variable enregistré"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public static function update($id, Request $request) {
        $part_variable = PartVariable::find($id);
        if($request->user()->id == $part_variable->user_id && in_array($part_variable->status, ["draft"])){
            $validator = self::validateAndSetPartVariable($request, $part_variable);
            if($validator->fails())
                return ['error' => $validator->errors()->first()];
            
            $queryValidation = PartVariable::where('id', '<>', $part_variable->id)
                ->where('employe_id', $request->employe_id)->where('date_paie', $request->date_paie);

            if($queryValidation->first() != null)
                return ['error' => "La part variable de l'employé pour ce mois existe déjà"];
            
            $date_paie = \DateTime::createFromFormat("Y-m-d", $request->date_paie);
            $date_begin = (new \DateTime())->setDate((new \DateTime())->format("Y"), (new \DateTime())->format("m"), 1);
            $date_end = (new \DateTime())->setDate((new \DateTime())->format("Y"), (new \DateTime())->format("m"), 1)->add(new \DateInterval('P1M'));
            if(
                $date_paie <= $date_begin ||
                ($request->user()->role != "resp_rh" && $date_paie > $date_end)
            )
                return ['error' => "La part variable pour le mois selectionné n'est pas acceptable"];

            HistoriqueController::update_part_variable($request, $part_variable, "Part variable modifié");
            $part_variable->employe_id = $request->employe_id;
            $part_variable->date_paie = $request->date_paie;
            $part_variable->commentaire = $request->commentaire;
            $part_variable->save();
            return response(["success" => "Part variable modifié", "id" => $part_variable->id]);
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $part_variable = PartVariable::find($id);
        if($request->user()->id == $part_variable->user_id  && in_array($part_variable->status, ['draft'])){
            $part_variable->status = 'validation';
            $part_variable->updated_at = new \DateTime();

            HistoriqueController::action_part_variable($request, "Part variable renvoyé", $id);
            if($part_variable->save()){
                return response(["success" => "Part variable renvoyé", "id" => $part_variable->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel(Request $request, $id){
        $part_variable = PartVariable::find($id);
        if($request->user()->id == $part_variable->user_id  && in_array($part_variable->status, ['validation'])){
            $part_variable->status = 'draft';
            $part_variable->updated_at = new \DateTime();

            HistoriqueController::action_part_variable($request, "Part variable annulé", $id);
            if($part_variable->save()){
                return response(["success" => "Part variable annulé", "id" => $part_variable->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done(Request $request, $id){
        $part_variable = PartVariable::find($id);
        if($request->user()->role == "validateur"  && in_array($part_variable->status, ['validation'])){
            $max_validation_date = (new \DateTime())->setDate((new \DateTime())->format('Y'), (new \DateTime())->format('m'), 29)->setTime(23, 59, 0);
            if ((new \DateTime($part_variable->date_paie))->format('Y-m') == (new \DateTime())->format('Y-m') &&
                new \DateTime() <= $max_validation_date
            ) {
                $part_variable->status = 'done';
                $part_variable->updated_at = new \DateTime();

                $criteres = CritereMensuel::where('part_variable_id', $id)->get();
                foreach ($criteres as $cr) {
                    $cr->critere = CriterePart::find($cr->critere_id)->designation;
                    $cr->save();
                }
                HistoriqueController::action_part_variable($request, "Part variable confirmé", $id);
                if($part_variable->save()){
                    $paie = DB::select("SELECT * FROM paies WHERE date_paie = '$part_variable->date_paie' AND (status = 'demande') AND employe_id = $part_variable->employe_id");
                    if (count($paie) > 0) {
                        $new_request = clone $request;
                        $new_request->employe_id = $part_variable->employe_id;
                        $new_request->date_paie = $part_variable->date_paie;
                        $new_request->paie_id = $paie[0]->id;   
                        PaieController::RecalculePaie($new_request);
                    }
                    return response(["success" => "Part variable confirmé", "id" => $part_variable->id]);
                }
            }
            return response (["error" => "La date de confirmation (25/". (new \DateTime($part_variable->date_paie))->format('m/Y') . ") est dépassée"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function done_multiple(Request $request){
        if ($request->user()->role == "validateur") {
            $part_variables = PartVariable::where('date_paie', $request->date_paie)->where('user_id', $request->user_id)->get();
            $max_validation_date = (new \DateTime())->setDate((new \DateTime())->format('Y'), (new \DateTime())->format('m'), 29)->setTime(23, 59, 0);
            if ((new \DateTime($request->date_paie))->format('Y-m') == (new \DateTime())->format('Y-m') &&
                new \DateTime() <= $max_validation_date
            ) {
                $offset_date = null;
                foreach ($part_variables as $part_variable) {
                    $part_variable->status = 'done';
                    $part_variable->updated_at = new \DateTime();
                    $criteres = CritereMensuel::where('part_variable_id', $part_variable->id)->get();
                    foreach ($criteres as $cr) {
                        $cr->critere = CriterePart::find($cr->critere_id)->designation;
                        $cr->save();
                    }
                    $part_variable->save();
                    HistoriqueController::action_part_variable($request, 'Part variable comfirmé', $part_variable->id);
                    $offset_date = $part_variable->updated_at;
                }
                
                return response(["success" => "Parts variables confirmés", "offset_date" => $offset_date]);
            }
            return response (["error" => "La date de confirmation (25/" . (new \DateTime($request->date_paie))->format('m/Y') . ") est dépassée"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function discard(Request $request, $id){
        $part_variable = PartVariable::find($id);
        if($request->user()->role == "validateur" && in_array($part_variable->status, ['validation'])){
            $part_variable->status = 'done';
            $part_variable->updated_at = new \DateTime();

            $criteres = CritereMensuel::where('part_variable_id', $id)->get();
            foreach ($criteres as $cr) {
                $cr->montant = 0;
                $cr->critere = CriterePart::find($cr->critere_id)->designation;
                $cr->save();
            }

            HistoriqueController::action_part_variable($request, "Part variable non attribué", $id);
            if($part_variable->save()){
                return response(["success" => "Part variable non attribué", "id" => $part_variable->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
}
