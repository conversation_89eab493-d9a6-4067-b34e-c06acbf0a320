import React, { useEffect, useState } from 'react';
import { Link, Navigate } from "react-router-dom";
import MenuView from '../view/MenuView';
import useToken from '../util/useToken';

export default function Achat({auth}) {
    const [nbData, setData] = useState(null) 

    useEffect(() => {
        let isMounted = true
        axios.get("/api/achat", useToken())
        .then((res) => {
            if(isMounted)
                setData(res.data)
        })
        return () => { isMounted = false };
    }, [])

    return (
        auth.role == "achat" ?
            <MenuView title="">
                {
                    nbData &&
                    (
                        (nbData.nb_equipement != 0 || nbData.nb_da != 0) ?
                        <div>
                            <h3 className='sub-title-menu'>ACHAT</h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_equipement != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/equipement?status=demande">Demande d'équipement</Link>
                                        <span className='badge-outline'>{nbData.nb_equipement}</span>
                                    </div>
                                }
                                {/*
                                    nbData.nb_da != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/da?status=traite">DA validé</Link>
                                        <span className='badge-outline'>{nbData.nb_da}</span>
                                    </div>
                                */}
                            </div>
                        </div>
                        :
                        <div className='center secondary'>
                            <br/>
                            <br/>
                            <h3>Aucune demande pour l'instant</h3>
                        </div>
                    )
                }
            </MenuView>
        : 
            <Navigate to="/"/>
    );
}