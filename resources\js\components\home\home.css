.palette-title {
    text-transform: uppercase;
    color: #666;
    margin: 20px 0px 10px 0px;
}

.palette-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
}

.palette-item>a,
.palette-item>span {
    color: #777;
    cursor: pointer;
    font-size: 11pt;
}

.palette-item>a:hover{
    color: #073570;
}

.sub-title-menu {
    display: flex;
    margin-top: 0px;
    color: #444;
    text-transform: uppercase;
    font-weight: normal;
    font-size: 2em;
    align-items: center;
}
.sub-title-menu > span {
    padding-left: 10px;
}
.menu-icon {
    font-size: 50px;
    background-color: #eee;
    padding: 5px;
}
.tab-menu {
    display: flex;
    margin-top: 20px;
    padding: 5px;
    flex-wrap: wrap;
    border: solid 1px #ddd;
    justify-content: space-between;
}

.tab-menu>span,
.tab-menu>a {
    width: 46%;
    padding: 10px 20px;
    margin: 5px 2%;
    text-align: center;
    background-color: inherit;
    color: #444;
    cursor: pointer;
    align-items: center;
    text-decoration: none;
}

.tab-menu>span.active {
    background-color: #073570;
    color: whitesmoke;
}