<?php

namespace App\Http\Controllers;

use App\Models\PartVariable;
use App\Models\CritereMensuel;
use App\Models\CriterePart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CritereMensuelController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }
    
    public static function employe($id, Request $request){
        $criteres = DB::select("SELECT cr.id, cr.designation, cr.montant FROM critere_parts cr
            WHERE cr.employe_id = ? and (cr.soft_delete is null or cr.soft_delete = 0)
            order by id desc", [$id]);
        return response(compact('criteres'));
    }

    public static function part_variable($id, Request $request){
        $part_mensuels = DB::select("SELECT cm.id, cm.critere_id, cm.montant FROM critere_mensuels cm 
            WHERE cm.part_variable_id = ?", [$id]);
        if(count($part_mensuels) > 0){
            $criteres = DB::select("SELECT cr.id, cr.designation, cr.montant FROM critere_parts cr
                WHERE id in (" . implode(",", array_column($part_mensuels, "critere_id")) . ") " .
                "order by id desc", []);
            foreach ($part_mensuels as $mens) {
                foreach ($criteres as $cr) {
                    if($cr->id == $mens->critere_id){
                        $mens->maximum = $cr->montant;
                        $mens->designation = $cr->designation;
                    }
                }
            }
        }
        return response(compact('part_mensuels'));
    }

    public static function update($id, Request $request){
        $mensuel = CritereMensuel::find($id);
        $critere = CriterePart::find($mensuel->critere_id);
        $part_variable = PartVariable::find($mensuel->part_variable_id);
        if(
            ($request->user()->id == $part_variable->user_id  && in_array($part_variable->status, ['draft']))
            || ($request->user()->role == "validateur"  && in_array($part_variable->status, ['validation']))
        ){
            if($request->montant > $critere->montant && $request->user()->id != 105)
                return ['error' => "Le montant ne doit pas dépasser l'attribuable"];
            if($mensuel->montant != $request->montant){
                $detail = '';
                $old_montant = HistoriqueController::getMontant($mensuel->montant);
                $new_montant = HistoriqueController::getMontant($request->montant);
                HistoriqueController::addUpdateDetail($detail, "Montant", $old_montant, $new_montant);
                $request->note = $detail;
                HistoriqueController::action_part_variable($request, "Critère '" . $critere->designation . "' modifié" , $mensuel->part_variable_id);
                $mensuel->montant = $request->montant;
                $mensuel->save();
                return response(["success" => "Montant modifié"]);
            }
            return response(["success" => "Aucune modification effectué"]);
        }
        return response(["error" => "EACCES"]);
    }
}
