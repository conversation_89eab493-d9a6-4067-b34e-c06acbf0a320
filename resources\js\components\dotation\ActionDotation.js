import React, { useEffect, useState } from 'react';
import DoneMultipleModal from '../modal/DoneMultipleModal';

export default function ActionDotation({auth, dotation, updateData}) {
    const [currentAction, setAction] = useState(null)
    const [transfertArticle, startTransfertArticle] = useState(false)
    const [retournerArticle, startRetournerArticle] = useState(false)
    const [declarationUsure, startDeclarationUsure] = useState(false)
    const [deduction, startDeduction] = useState(false)

    useEffect(() => {
        setAction({
            header: "Transfert d'article",
            request: "/api/mouvement_article/do_transfert/" + dotation.id,
            required: true
        })
    }, [transfertArticle])

    useEffect(() => {
        setAction({
            header: "Retour d'article",
            request: "/api/mouvement_article/do_back/" + dotation.id,
            required: true
        })
    }, [retournerArticle])

    useEffect(() => {
        setAction({
            header: "Declaration d'usure",
            request: "/api/mouvement_article/declaration_usure/" + dotation.id,
            required: true
        })
    }, [declarationUsure])

    useEffect(() => {
        setAction({
            header: "Deduction",
            request: "/api/mouvement_article/deduction/" + dotation.id,
            required: true
        })
    }, [deduction])

    return <div>
        <div className='action-container'>
            {
                retournerArticle &&
                <DoneMultipleModal
                    action={currentAction}
                    task={"retourner"}
                    name={"dotation"}
                    data={dotation}
                    closeModal={() => startRetournerArticle(false)}
                    updateData={() => updateData(true)}
                />
            }
            {
                transfertArticle &&
                <DoneMultipleModal
                    action={currentAction}
                    task={"transferer"}
                    name={"dotation"}
                    data={dotation}
                    closeModal={() => startTransfertArticle(false)}
                    updateData={() => updateData(true)}
                />
            }
            {
                declarationUsure &&
                <DoneMultipleModal
                    action={currentAction}
                    task={"usure"}
                    name={"dotation"}
                    data={dotation}
                    closeModal={() => startDeclarationUsure(false)}
                    updateData={() => updateData(true)}
                />
            }
            {
                deduction &&
                <DoneMultipleModal
                    action={currentAction}
                    task={"deduction"}
                    name={"dotation"}
                    data={dotation}
                    closeModal={() => startDeduction(false)}
                    updateData={() => updateData(true)}
                />
            }
            {   
                ["tenue"].includes(auth.role) &&
                dotation.articles.some((article) => article.status !== "direct") &&
                dotation.status !== "termine" && (
                    <>
                        {(dotation.employe_id !== null || dotation.site_id !== null) && (
                            <>
                                {dotation.type_mouvement === "sortie" &&
                                    dotation.articles.some((article) => article.status === "attribué" && article.estUsé !== '1') && (
                                        <>
                                            {dotation.articles.some((article) => article.status === "attribué") && (
                                                <span onClick={() => startRetournerArticle(true)}>Retourner</span>
                                            )}
                                            <span onClick={() => startTransfertArticle(true)}>Transferer</span>
                                        </>
                                )}
                                {dotation.type_mouvement === "entré" &&
                                    dotation.articles.some((article) => article.status !== "transferé" && article.estUsé !== '1') && (
                                        <span onClick={() => startTransfertArticle(true)}>Transferer</span>
                                )}
                            </>
                        )
                        }
                    </>
                )
            }
            {
                ["tenue"].includes(auth.role) &&
                dotation.articles.some((article) => article.status !== "direct") &&
                dotation.status !== "termine" && (
                    <>
                        {
                            !dotation.articles.every((article) => article.estUsé === "1") &&
                            <span onClick={() => startDeclarationUsure(true)}>Déclarer comme usé</span>
                        }
                    </>
                )
            }
            {
                ["tenue"].includes(auth.role) &&
                dotation.articles.some((article) => article.status !== "direct") &&
                dotation.status !== "termine" &&
                <span onClick={() => startDeduction(true)}>Deduction</span>
            }
        </div>
    </div>
}
