import React from 'react';

export default function SalForfaitModal({onChange, closeModal}) {
    const types = [
        {name: "Par heure", value: false},
        {name: "Forfaitaire", value: true}
    ]

    const handleSelectType = (t) => {
        onChange(t.value)
        closeModal()
    }

    return <div className='modal'>
        <div>
            <h2>Salaire</h2>
            <div className='list-container'>
                <ul>
                    {
                        types.map(t => {
                            return <li key={t.value} onClick={() => handleSelectType(t)}>
                                {t.name}<br/>
                            </li>
                        })
                    }
                </ul>
            </div>
            <div className='form-button-container'>
                <button onClick={closeModal}>Annuler</button>
            </div>
        </div>
    </div>
}