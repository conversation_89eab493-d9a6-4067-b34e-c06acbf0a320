import React, { useEffect, useState } from 'react'
import NoteModal from '../input/NoteModal';
import LoadingPage from '../loading/LoadingPage';
import SendMailModal from '../mail/SendMailModal';
import useToken from '../util/useToken';
import Story from './Story';
import MessageModal from '../input/MessageModal';

export default function Tracabilite({ auth, name, value, updateData, defautUsers }) {
    const [isLoading, toggleLoading] = useState(true)
    const [historiques, setHistoriques] = useState([])
    const [showMailModal, toggleMailModal] = useState(false)
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [showMessage, toggleMessage] = useState(false)
    
    useEffect(() => {
        let isMounted = true
        const params = new URLSearchParams()
        if (name !== "dotation_id") {
            params.append(name, value)
            params.append("offset", 0)
        } else {
            params.append(name, value.id)
            params.append("offset", 0)
        }
        axios.get("/api/historique/tab?" + params, useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else
                    setHistoriques(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false};
    }, []);

    return <>
        {
            showMailModal &&
            <SendMailModal auth={auth} name={name} value={value} updateData={updateData} closeModal={() => toggleMailModal(false)}/>
        } 
        {
            showNoteModal &&
            <NoteModal required name={name} value={value} updateData={updateData} closeModal={() => toggleNoteModal(false)} defautUsers={defautUsers} />
        }
        {
            showMessage &&
            <MessageModal defautUsers={defautUsers} value={value} name={name} updateData={updateData} closeModal={() => toggleMessage(false)} />
        }
        {
            isLoading ?
                <LoadingPage/>
            : <>
                <div className='tab-list-action'>
                    <div className='action-container'>
                        {
                            name == 'fait_marquant_id' &&
                            <span>
                                <span onClick={() => toggleMailModal(true)}>Envoyer un email</span>
                            </span>
                        }
                        
                        <span>
                            <span onClick={() => toggleMessage(true)}>Message interne</span>
                        </span>    
                        
                        {
                            name != 'fait_marquant_id' &&
                            <span>
                                <span onClick={() => toggleNoteModal(true)}>Ajouter une note</span>
                            </span>
                        }
                    </div>
                </div>
                {
                    historiques.length > 0 ?
                        historiques.map((h) => (
                            <Story key={h.id} trace={h}/>
                        ))
                    :
                        <div className="card-container secondary">
                            Aucune historique
                        </div>
                }
            </>
        } </>
}
