import React from 'react';
import moment from 'moment'

export default function HeaderFlotte({auth, data}) {
    return (
        <div>
            <h3>
                {
                    data.site ? 
                        data.site 
                    : 
                    <div> 
                        {data.user_nom} <span className='secondary'> {" <" + data.user_email + ">"} </span> 
                    </div>
                } 
            </h3>
            <p style={{whiteSpace: "pre-line"}}>
                Objet: <span className='text'>{data.objet}</span>
            </p>
            
            <div>
                Commentaire: <span className='text'>{data.commentaire}</span>
            </div>
            <div className='card-footer'>
                <span>
                    {
                        data.site &&
                        <>
                            <span>Demandeur : </span>
                            <span className='text'>{data.user_nom} {" <" + data.user_email + ">"}</span>
                        </>
                    }
                </span>
                <span className='secondary'>{moment(data.created_at).from(auth.datetime)}</span>
            </div>
        </div>
    )
}