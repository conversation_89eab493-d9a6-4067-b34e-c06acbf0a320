import React, { useEffect, useState } from 'react'
import {useParams} from 'react-router-dom'
import axios from 'axios'
import ShowHeader from '../view/ShowHeader'

import useToken from '../util/useToken';
import matricule from '../util/matricule';
import moment from 'moment';

import Tab from '../layout/Tab';
import LoadingPage from '../loading/LoadingPage';
import ActionEmploye from './ActionEmploye';

export default function ShowEmploye({auth, currentId, setCurrentId, setCurrentItem, size, currentNature, setCurrentNature}) {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [employe, setEmploye] = useState(null)

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/employe/show_detail/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setEmploye(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(employe)
    }, [employe]);

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
            <div>
                {
                    employe &&
                    <>
                        <ShowHeader label="Employe" id={employe.id} closeDetail={() => setCurrentId()} size={size}/>
                        <div className="card-container">
                            <div className='badge-container'>
                                <span>
                                    <span className={'badge-outline badge-outline-' + (employe.soft_delete == 1 ? "brown" : "cyan")}>
                                        {employe.soft_delete == 1 ? "En archive" : "En fonction"}
                                    </span> {
                                        employe.nb_pj > 0 &&
                                        <span className='badge-outline'>
                                            Pièce jointe : {employe.nb_pj}
                                        </span>
                                    }
                                </span>
                            </div>
                            <h3>
                                <div> 
                                    {matricule(employe)} {employe.nom}
                                </div>                        
                            </h3>
                            {
                                (!employe.sal_forfait && employe.site) &&
                                <p style={{whiteSpace: "pre-line"}}>
                                    Site: <span className='text'>{employe.site}</span>
                                </p>
                            }
                            <p style={{whiteSpace: "pre-line"}}>
                                {employe.fonction_id == 12 ?
                                    <>Titre: <span className='text'>{employe.titre}</span></>
                                    :<>Fonction: <span className='text'>{employe.nom_fonction}</span></>
                                }
                            </p>
                            <p style={{whiteSpace: "pre-line"}}>
                                Agence: <span className='text'>{employe.nom_agence}</span>
                            </p>
                            {
                                !employe.soft_delete ?
                                    (
                                        [1, 2, 3, 6].includes(Number.parseInt(employe.societe_id)) &&    
                                        <p style={{whiteSpace: "pre-line"}}>
                                            Date d'embauche: <span className='text'>
                                                {
                                                    employe.societe_id == 1 ? moment(employe.date_confirmation).format("DD MMM YYYY") :
                                                    employe.societe_id == 2 ? moment(employe.date_conf_soit).format("DD MMM YYYY") : 
                                                    employe.societe_id == 3 ? moment(employe.date_embauche).format("DD MMM YYYY") :
                                                    employe.societe_id == 6 ? moment(employe.date_conf_saoi).format("DD MMM YYYY") :
                                                    ''
                                                }
                                            </span>
                                        </p>
                                    )
                                :
                                    <>
                                        <p style={{whiteSpace: "pre-line"}}>
                                            Observation: <span className='text'>{employe.observation}</span>
                                        </p>
                                        <p style={{whiteSpace: "pre-line"}}>
                                            Date de sortie: <span className='text'>{moment(employe.date_sortie).format("DD MMM YYYY")}</span>
                                        </p>
                                    </>
                            }
                            <div className='card-action'>
                                <ActionEmploye auth={auth} employe={employe} updateData={updateData}/>
                            </div>
                        </div>
                        <Tab auth={auth} name="employe_id" value={employe.id} data={employe} updateData={updateData} currentNature={currentNature} setCurrentNature={setCurrentNature}/>
                    </>
                }
            </div>
    } </>
}
