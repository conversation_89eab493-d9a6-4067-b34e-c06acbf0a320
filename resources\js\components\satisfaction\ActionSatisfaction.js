import axios from 'axios';
import React, { useState } from 'react';
import useToken from '../util/useToken';

export default function ActionSatisfaction({ auth, satisfaction, updateData }) {
    const [disableSeen, setDisableSeen] = useState(false);
    const handleSeen = () => {
        setDisableSeen(true);
        axios.post('/api/satisfaction/seen_satisfaction/' + satisfaction.id, null, useToken())
        .then((res) => {
            if (res.data.success)
                updateData()
        }).finally(() => {
            setDisableSeen(false);
        });

    }
    return (
        <div>

            <div className="action-container">
                {/* {satisfaction.status == "demande" && auth.id == satisfaction.user_id &&
                    <span >
                        <Link to={"/satisfaction/edit/" + satisfaction.id}>Editer</Link>
                    </span>
                } */}
                {!satisfaction.seen && auth.id != satisfaction.user_id && 
                    <span >
                        {!disableSeen && <span onClick={handleSeen} >Marquer comme lu</span>}
                    </span>
                }
            </div>
        </div>
    )
}
