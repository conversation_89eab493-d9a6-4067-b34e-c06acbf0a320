import React, { useEffect, useState } from 'react'
import moment from 'moment'

import useToken from '../../util/useToken'
import LoadingPage from '../../loading/LoadingPage'
import { AiTwotoneEdit } from 'react-icons/ai'
import { IoMdClose } from 'react-icons/io'
import EditPointage from '../pointage/EditPointage'
import ConfirmModal from '../../modal/ConfirmModal'

export default function PointageTab({reclamation, auth, updateData}) {
    const [isLoading, toggleLoading] = useState(true)
    const [pointages, setPointages] = useState([])
    const [totalHeure, setTotalHeure] = useState(0)
    const [currentPointage, setCurrentPointage] = useState(null)
    const [showEditModal, toggleEditModal] = useState(false)
    const [showDeleteModal, toggleDeleteModal] = useState(false)

    const handleConfirmDelete = () => {
        if(currentPointage.id){
            axios.post('/api/pointage_reclamation/delete/' + currentPointage.id, [], useToken())
            .then((res) => {
                if(res.data.success){
                    if(updateData) updateData()
                    toggleDeleteModal(false)
                }                
                else if(res.data.error == "EACCES")
                    setError("Une erreur est survenue.")
                else if(res.data.error)
                    setError(res.data.error)
            })
            .catch((e) => {
                console.error(e)
                setError("Erreur d'envoie, réessayez.")
            })
        }
        else {
            const newItems = items.filter((item, index) => currentPointage.index != index)
            setItems(newItems)
            toggleDeleteModal(false)
        }
    }

    const handleDeletePointage = (pointage, index) => {
        pointage.index = index
        setCurrentPointage(pointage)
        toggleDeleteModal(true)
    }

    const handleEditPointage = (pointage, index) => {
        pointage.index = index
        setCurrentPointage(pointage)
        toggleEditModal(true)
    }

    const handleAddPointage = () => {
        setCurrentPointage(null)
        toggleEditModal(true)
    }

    useEffect(() => {
        let isMounted = true;
        toggleLoading(true)
        axios.get('/api/pointage_reclamation/' + reclamation.id,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else if(res.data){
                    setPointages(res.data)
                    let total = 0
                    res.data.map(p => {
                        total += 12
                    })
                    setTotalHeure(total)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }, []);
    
    const showEditAction = (
        (["rh", "resp_rh"].includes(auth.role) && ["demande", "traite"].includes(reclamation.status))
        || (auth.id == reclamation.user_id && reclamation.status == "draft")
    )

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <>
                    {
                        showEditModal &&
                        <EditPointage
                            currentReclamId={reclamation.id}
                            currentPointage={currentPointage}
                            updateData={updateData}
                            closeModal={() => toggleEditModal(false)}/> 
                    }
                    {
                        showDeleteModal &&
                        <ConfirmModal
                            msg="Supprimé ce pointage" 
                            confirmAction={handleConfirmDelete} 
                            closeModal={() => toggleDeleteModal(false)}/>
                    }
                    <div className='line-container'>
                        <div className='header-pointage'>
                            <h3>
                                Heure réclamé : {totalHeure}
                            </h3>
                            {
                                showEditAction && 
                                <div className='action-container'>
                                    <span>
                                        <span onClick={() => handleAddPointage()}>Ajouter</span>
                                    </span>
                                </div>
                            }
                        </div>
                    </div>
                    {
                        pointages.map(p => 
                            <div key={p.id} className='line-container'>
                                <div className='pointage-container'>
                                    <div>
                                        {
                                            moment(p.date_pointage).format("DD MMM YYYY") 
                                            + " " + (moment(p.date_pointage).format("HH:mm:ss") == "07:00:00" ? "JOUR" : "NUIT")
                                        }<br/>
                                        <span className='secondary'>
                                            {p.site}
                                        </span>
                                    </div>
                                    {
                                        showEditAction &&
                                        <div style={{width:45, minWidth:45, maxWidth:45}}>
                                            <span>
                                                <AiTwotoneEdit onClick={() => handleEditPointage(p)} size={20}/>
                                            </span>
                                            {
                                                pointages.length > 1 &&
                                                <span>
                                                    <IoMdClose onClick={() => handleDeletePointage(p)} size={20}/>
                                                </span>
                                            }
                                        </div>
                                    }
                                </div>
                            </div>
                        )
                    }
                </>
        }
    </>
}