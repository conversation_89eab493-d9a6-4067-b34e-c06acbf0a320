const axios = require('axios');
const FormData = require('form-data');
const moment = require('moment');
const { performance } = require('perf_hooks');
const mysql = require('mysql');
// const { default: calcul_paie } = require('../resources/js/components/paie/Calcul/calc_paie');

moment.locale('fr');

const db_config_prod = require("../auth").db_config_admin
const db_config = {
    host: "127.0.0.1",
    port: "3306",
    user: "root",
    database: "admin",
    password: ""
}
const pool = mysql.createPool(process.argv[2] == 'task' ? db_config_prod : db_config)
const tokenAdminTest = "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
const date_paie = moment().format('YYYY-MM') + "-20";

const sqlSelectEmploye = () =>{
    return "SELECT emp.id as 'employe_id', emp.* FROM employes emp where (emp.sal_forfait is null or emp.sal_forfait = 0) and (emp.last_date_paie is null or emp.last_date_paie <> ? ) and (emp.soft_delete is null or emp.soft_delete = 0 ) LIMIT 10";
}

function generatePaie(){
    pool.query(sqlSelectEmploye(), [date_paie], async (err, employes) => {
        if(err)
            console.error(err)
        else{
            console.log("Nb employe: " + employes.length)
            for(let i = 0; i < employes.length; i++){
                let startTimeExecution = performance.now();
                const data = new FormData();
                data.append('date_paie', date_paie);
                data.append('employe_id', employes[i].employe_id);
                axios.post((process.argv[2] == 'task' ? "https://app.dirickx.mg:8001" : "http://127.0.0.1:8000") + "/api/paie/generate_paie/" + employes[i].employe_id, data, {
                    headers:
                        {
                            ...data.getHeaders(),
                            'Authorization': 'Bearer ' + (process.argv[2] == 'task' ? require("../auth").tokenAdmin : tokenAdminTest)
                        }
                }).then((res) => {
                    let endTime = performance.now();
                    const executionTime = endTime - startTimeExecution;
                    console.log('Response', res.data, `Execution time: ${(executionTime/60000).toFixed(2)} minutes`);
                    if(employes.length < 10){
                        process.exit(1)
                    }
                    else if(i+1 == employes.length){
                        setTimeout(()=>generatePaie(), 4000);
                    }
                })
            }
        }
    })
}
generatePaie()