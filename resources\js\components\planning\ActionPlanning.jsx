import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import ShiftDuplicate from './ShiftDuplicate'
import PlanningExcel from './PlanningExcel'

export default function ActionPlanning({auth, planning}) {
    const [showDuplicate, toggleDuplicate] = useState(false)
    const [firstPointage, setFirstPointage] = useState([])
    const [showExport, toggleExport] = useState(false)

    useEffect(() => {
        // const ptgs = planning.pointages.filter(pt => moment(pt.date_pointage).format('DD/MM/YYYY HH:mm:ss') == (moment(planning.planning.date_planning).date(1).format('DD/MM/YYYY') + ' 06:00:00'))
        const ptgs = planning.pointages.filter(pt => 
            moment(pt.date_pointage).isSame(moment(planning.planning.date_planning).date(1).hour(6).minute(0).second(0), 'second')
          );
        setFirstPointage(ptgs)
    }, []);

    const { planning: dataToExport, pointages } = planning;
    const [year, month] = dataToExport.date_planning.split("-").map(Number);
    dataToExport.datePlanning = { year, month };
    dataToExport.pointages = pointages.map(dt => ({
        ...dt,
        service: dt.date_pointage
    }));

    return (
        <div className="action-container">
            {(auth.id == planning.planning.resp_sup_id || auth.id == planning.planning.user_id || ['resp_op', 'validateur'].includes(auth.role)) &&
                <>
                    {moment(planning.planning.date_planning, 'YYYY-MM').endOf('month').isAfter(moment()) &&
                        <span>
                            <Link to={"/planning/edit/" + planning.planning.id}>
                                Modifier
                            </Link>
                        </span>
                    }
                    <span onClick={() => toggleDuplicate(true)}>
                        Dupliquer
                    </span>
                    {dataToExport && <PlanningExcel plannings={[dataToExport]} datePlanning={dataToExport.datePlanning} closeModal={()=>toggleExport(false)} actionButton />}
                </>
            }
            {
                showDuplicate &&
                <ShiftDuplicate planning={planning.planning} 
                    pointages={planning.pointages} 
                    firstPointage={firstPointage} 
                    setFirstPointage={setFirstPointage}
                    auth={auth} 
                    closeModal={() => toggleDuplicate(false)}
                    />
            }
        </div>
    )
}
