const moment = require('moment')
const mysql = require('mysql')
const fs = require("fs");

moment.locale('fr')
const auth = require("../auth")

const db_config_ovh = auth.db_config_ovh
const pool_tls = mysql.createPool(db_config_ovh)

const db_config_admin = auth.db_config_admin
const pool_admin = mysql.createPool(db_config_admin)

const sqlSelectSite = "select idsite, group_pointage_id from sites"
const sqlUpdate = "UPDATE sites SET group_pointage_id = ? WHERE idsite=?"

function updateSiteById(sites, index){
    if(index < sites.length){
        const site = sites[index]
        console.log(site.idsite + " : " + site.group_pointage_id)
        const params = [site.group_pointage_id, site.idsite]
        pool_admin.query(sqlUpdate, [...params, ...params.slice(1)], async (err, res) => {
            if(err){
                console.log("err found")
                console.error(err)
                setTimeout(() => {
                    updateSiteById(sites, index)
                }, 300)
            }
            else {
                setTimeout(() => {
                    updateSiteById(sites, index+1)
                }, 100)
            }
        })
    }
    else {
        console.log("process done!")
        process.exit()
    }
}
function updateData(){
    pool_tls.query(sqlSelectSite, [], async (err, sites) => {
        if(err){
            console.error(err)
        }
        else {
            console.log("site to sync: " + sites.length)
            updateSiteById(sites, 0)
        }
    })
}

updateData()