import React, { useEffect, useState } from 'react';
import moment from 'moment';
import './calendar.css';
import { HiOutlineUserAdd } from 'react-icons/hi';
import ModalAgent from './ModalAgent';
import { MdModeComment, MdOutlineModeComment } from 'react-icons/md';
import CommentModal from './CommentModal';
import InputEmploye from '../input/InputEmploye';
import InputCheckBox from '../input/InputCheckBox';

export default function CalendarView({
    datePlanning,
    plannings,
    addUser,
    setShowEmploye,
    handleDeleteEmploye,
    handleAddComment,
    addMultipleCase,
    contrat,
    showMessage,
    site,
    handleCountHours,
    setCurrentDatePtg,
    handleToggleSubmit,
    currentUserToAdd, setCurrentUserToAdd, 
    currentEmployeToCopy,
    horaires
}) {
    const daysOfWeek = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];
    const [days, setDays] = useState([]);
    const [regroupPlanning, setRegroupPlanning] = useState([])
    const [numberOfBlank, setNumberOfBlank] = useState(0)
    const [showAgent, setShowAgent] = useState(false)
    const [currentPlanning, setCurrentPlanning] = useState([])
    const [showComment, setShowComment] = useState(false)

    useEffect(() => {setRegroupPlanning(groupByDay(plannings))}, [plannings])
    // useEffect(() => {setCurrentDatePtg(currentPlanning.service)}, [currentPlanning])
    const groupByDay = (data) =>{
        const grouped = {};
        data.forEach(item => {
            const day = item.day;
            if (!grouped[day]) {
                grouped[day] = [];
            }
            grouped[day].push(item);
        });
        return Object.values(grouped);
    }

    const handleAddUserMultipleCase = (id) => {
        if(!site?.id) showMessage(null,"Veuillez choisir un site.")
        else if (currentUserToAdd && addMultipleCase) {
            const newPlannings = [];
            const currentPlan = [...plannings.filter(p => p.id == id)][0]
            // const otherPlanInSameDay = [...plannings.filter(p => (p.day == currentPlan.day && p.id != currentPlan.id))][0]
            const existingUser = currentPlan?.employes?.find(p => p.id === currentUserToAdd.id)
            let canAdd = true
            // if (otherPlanInSameDay?.employes?.find(p => p.id === currentUserToAdd.id))
            //     handleDeleteEmploye(currentUserToAdd, otherPlanInSameDay.id)
            
            if (existingUser)
                handleDeleteEmploye(currentUserToAdd, id)
            
            // let currentContrat = moment(currentPlan.service).format('HH:mm') == '06:00' ? contrat.agent_day : contrat.agent_night
            // if (currentPlan.employes.length >= currentContrat) {
            //     showMessage(currentPlan)
            // }
            if(moment(currentPlan.service).isBefore(moment())){
                showMessage(null, "La date doit etre après aujourd'hui.", "Date depassée")
            }
            if(!existingUser && canAdd && moment(currentPlan.service).isAfter(moment())) {
                currentPlan?.employes?.push(currentUserToAdd)
                plannings.forEach((item) => {
                    if (item.id == id)
                        newPlannings.push(currentPlan)
                    else
                        newPlannings.push(item)
                })
                setRegroupPlanning(groupByDay(plannings));
            }
            handleCountHours()
        }
        handleToggleSubmit()
    }

    const pasteCurrentEmploye = (id) =>{
        if(currentEmployeToCopy.length > 0){
            const currentPlan = [...plannings.filter(p => p.id == id)][0]
            const isChecked = currentEmployeToCopy.every(cur => currentPlan.employes.some(emp => emp.id == cur.id))
            if(isChecked){
                currentEmployeToCopy.map(emp =>{
                    handleDeleteEmploye(emp, id)
                })    
            }
            else{
                const elementsManquants = currentEmployeToCopy
                .filter(cur => !currentPlan.employes.some(emp => cur.id === emp.id))
                .reduce((unique, item) => {
                    if (!unique.some(el => el.id === item.id)) {
                        unique.push(item);
                    }
                    return unique;
                }, []);

                currentPlan.employes.push(...elementsManquants);
            }
            setRegroupPlanning(groupByDay(plannings))
        }
    }

    useEffect(() => {
        if (datePlanning) {
            const today = moment(`${datePlanning.year}-${datePlanning.month}`, 'YYYY-MM');
            const monthStart = today.clone().startOf('month');
            const monthEnd = today.clone().endOf('month');
            const startDate = monthStart.clone().startOf('week');
            const endDate = monthEnd.clone().endOf('week');
            const all_days = [];
            let day = startDate;
            let nbBlank = 0
            let isBefore = true;
            while (day.isBefore(endDate, 'day') || day.isSame(endDate, 'day')) {
                all_days.push({
                    date: day.clone(),
                    day: day.month() === today.month() ? day.date() : '',
                    isCurrentMonth: day.month() === today.month(),
                });
                if (isBefore && day.month() !== today.month()) {
                    nbBlank += 1
                }
                else isBefore = false;
                day.add(1, 'day');
            }
            setNumberOfBlank(nbBlank)
            setDays(all_days);
        }
    }, [datePlanning]);

    return (
        <div>
            {!currentEmployeToCopy &&
                <div className='card-container'>
                    {
                        addMultipleCase &&
                        <InputEmploye label='Agent' value={currentUserToAdd} onChange={setCurrentUserToAdd} />
                    }
                </div>
            }
            <div className="calendar">
                {
                    showAgent &&
                    <ModalAgent planning={currentPlanning} closeModal={()=>setShowAgent(false)} setShowAgent={setShowAgent} handleDeleteEmploye={handleDeleteEmploye} />	
                }
                {
                    showComment &&
                    <CommentModal planning={currentPlanning } handleAddComment={handleAddComment} closeModal={()=>{ setShowComment(false) }}/>
                }
                <div className="calendar-header">
                    {daysOfWeek.map((day) => (
                        <div key={day} className="calendar-header-day">
                            {day}
                        </div>
                    ))}
                </div>
                
                {days && (
                    <div className="calendar-body">
                        {days.slice(0, numberOfBlank).map((day, index) => (
                            <div key={index} className={currentEmployeToCopy?.length > 0 ? `calendar-day-in-modal` : `calendar-day`}>
                                <div className="calendar-day-half calendar-day-half--day"></div>
                                <div className="calendar-day-half calendar-day-half--night"></div>
                            </div>
                        ))}
                        {regroupPlanning.map((pl, index) => (
                            <div key={index} className={currentEmployeToCopy?.length > 0 ? `calendar-day-in-modal` : `calendar-day`} >
                                {pl.map((day) =>
                                    <React.Fragment key={day.id}>
                                        {
                                            day?.service &&
                                            <div className={`calendar-day-half 
                                                ${'calendar-day-half--' + (moment(day?.service).format('HH') == '06' ? 
                                                    ('day' + (day.employes.length < horaires.day[moment(day.service).isoWeekday() - 1] ? '-not-enough' : (day.employes.length > horaires.day[moment(day.service).isoWeekday() - 1] ? '-surplus' : ''))) 
                                                :
                                                    ('night' + (day.employes.length < horaires.night[moment(day.service).isoWeekday() - 1] ? '-not-enough' :(day.employes.length > horaires.night[moment(day.service).isoWeekday() - 1] ? '-surplus' : ''))))}`}
                                            >
                                                {window.innerWidth <= 370 ? 
                                                    <span className='day secondary comment-add'>
                                                        <span>
                                                            {moment(day.service).format('D')} 
                                                        </span>
                                                        <span>
                                                            {(moment(day?.service).format('HH:mm:ss') == '06:00:00' ? 'J' : 'N')}
                                                        </span>
                                                    </span>
                                                    :
                                                    <span className='day secondary'>
                                                        {moment(day?.service).format('D') + (moment(day?.service).format('HH:mm:ss') == '06:00:00' ? 'J' : 'N')} 
                                                    </span>
                                                }
                                                <span className={`number-agent secondary`} >
                                                    {
                                                        (addMultipleCase && currentUserToAdd) ?
                                                            <span className='check-box-in-case'>
                                                                {moment(day.service).isAfter(moment()) &&
                                                                    <InputCheckBox checked={day.employes.some(emp => emp.id == currentUserToAdd.id)}
                                                                        onChange={()=>handleAddUserMultipleCase(day.id)} />
                                                                }
                                                            </span>
                                                        :
                                                            currentEmployeToCopy?.length > 0 ?
                                                            <span className='check-box-in-case'>
                                                                {moment(day.service).isAfter(moment()) &&
                                                                    <InputCheckBox 
                                                                        checked={currentEmployeToCopy.every(cur => day.employes.some(emp => emp.id == cur.id))}
                                                                        onChange={()=>pasteCurrentEmploye(day.id)} 
                                                                    />
                                                                }
                                                            </span>
                                                        :
                                                            day.employes.length > 0 &&
                                                            <b onClick={() => { setShowAgent(true), setCurrentPlanning(day) }}>
                                                                {day.employes.length}
                                                            </b>
                                                    }
                                                </span>
                                                  
                                                <span className='day secondary comment-add'>
                                                    {moment().isBefore(moment(day.service)) &&
                                                        <HiOutlineUserAdd size={15} 
                                                            onClick={() => { setCurrentDatePtg(moment(day.service).format('YYYY-MM-DD HH:mm:ss')), setShowEmploye(true), addUser(day.id) }} 
                                                        />
                                                    }
                                                    {moment().isBefore(moment(day.service).add(12, 'hours')) && (day.comment.comment_content?.trim() == "" ? 
                                                        <MdOutlineModeComment size={15} onClick={() => { setShowComment(true), setCurrentPlanning(day) }} />
                                                        :
                                                        <MdModeComment size={15} color='#073570' onClick={() => { setShowComment(true), setCurrentPlanning(day) }} />)
                                                    }
                                                </span>
                                            </div>
                                        }
                                    </React.Fragment>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
