import React, { useState } from 'react';

import Textarea from '../../input/Textarea';
import InputText from '../../input/InputText';
import axios from 'axios';
import useToken from '../../util/useToken';

export default function DoneDaItemModal({action, updateData, closeModal}) {
    const [note, setNote] = useState("")
    const [cout, setCout] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleOk = () => {
        disableSubmit(true)
        setError("")
        const data = {
            note: note,
            cout: cout
        }
        axios.post(action.request, data, useToken())
        .then((res) => {
            if(res.data.error){
                setError(res.data.error)
                disableSubmit(false)
            }
            else if(res.data.success){
                closeModal()
                updateData()
            }
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
        })
    }
    return <div className='modal'>
        <div>
            <h3>{action.header}</h3>
            <InputText
                required
                label="Coût"
                value={cout}
                onChange={setCout}/>
            <Textarea value={note} label="Commentaire" onChange={(value) => setNote(value)} />
            {
                error &&
                <div className='container-error'>
                    {error}
                </div>
            }
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Envoyer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
    
}
