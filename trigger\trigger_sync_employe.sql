drop trigger IF EXISTS before_update_employe;


DELIMITER |
CREATE TRIGGER before_update_employe
BEFORE UPDATE
ON employes FOR EACH ROW
BEGIN
     if(COALESCE(NEW.societe_id, 0) != COALESCE(OLD.societe_id, 0) 
          or COALESCE(NEW.numero_stagiaire, 0) != COALESCE(OLD.numero_stagiaire, 0) 
          or COALESCE(NEW.numero_employe, 0) != COALESCE(OLD.numero_employe, 0) 
          or COALESCE(NEW.num_emp_soit, 0) != COALESCE(OLD.num_emp_soit, 0) 
          or COALESCE(NEW.num_emp_saoi, 0) != COALESCE(OLD.num_emp_saoi, 0) 
          or COALESCE(NEW.nom, '') != COALESCE(OLD.nom, '') 
          or COALESCE(NEW.real_site_id, 0) != COALESCE(OLD.real_site_id, 0)
          or NEW.date_embauche != OLD.date_embauche 
          or NEW.date_confirmation != OLD.date_confirmation 
          or NEW.date_conf_soit != OLD.date_conf_soit
          or NEW.date_conf_saoi != OLD.date_conf_saoi
          or NEW.date_sortie != OLD.date_sortie 
          or COALESCE(NEW.sal_forfait, 0) != COALESCE(OLD.sal_forfait, 0)
          or COALESCE(NEW.fonction_id, 0) != COALESCE(OLD.fonction_id, 0)
          or COALESCE(NEW.observation, '') != COALESCE(OLD.observation, '')
          or COALESCE(NEW.agence_id, 0) != COALESCE(OLD.agence_id, 0)
          or NEW.last_update != OLD.last_update
     or COALESCE(NEW.soft_delete, 0) != COALESCE(OLD.soft_delete, 0)) then
          begin
     	     set NEW.admin_updated_at = now();
          end;
     end if;
END
| DELIMITER ;