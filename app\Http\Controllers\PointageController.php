<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Util\PaieUtil;

class PointageController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }
    
    public function employe_input($id){
        $pointages = DB::select("SELECT p.id, p.site_id, p.date_pointage, s.nom as 'site'
            FROM pointages p
            LEFT JOIN sites s ON s.idsite = p.site_id
            WHERE p.employe_id = ? and (p.soft_delete is null or p.soft_delete = 0)
            ORDER BY date_pointage desc
            LIMIT 10", [$id]);
        $pointage_ids = array_column($pointages, "id");
        if(count($pointage_ids) > 0){
            $sanctions = DB::select("SELECT sanc.id, sanc.pointage_id
                FROM sanctions sanc
                WHERE sanc.pointage_id in (" . implode(",", $pointage_ids) . ")", []);
            foreach ($pointages as $ptg) {
                foreach ($sanctions as $sanc) {
                    if($sanc->pointage_id == $ptg->id)
                        $ptg->sanction_id = $sanc->id;
                }
            }
        }
        return response($pointages);
    }
    
    public function employe($id, Request $request){
        $interval = PaieUtil::getIntervalByAgent($request->year, $request->month);
        if(!$interval)
            return response()->json(['message' => 'set_site']);
        
        $begin = $interval['begin'];
        $end = $interval['end'];

        $month = $end->format('n');
        $year = $end->format('Y');
        $begin = $begin->format("Y-m-d");
        $end = $end->format("Y-m-d");
        $pointages = DB::select("SELECT p.id, s.nom as 'site', p.date_pointage FROM pointages p
            LEFT JOIN sites s ON s.idsite = p.site_id
            WHERE (p.soft_delete is null or p.soft_delete = 0) and p.employe_id = ? and p.date_pointage > ? and p.date_pointage < ?
            ORDER BY p.date_pointage DESC", [$id, $begin . ' 00:00:00', $end . ' 23:59:00']);

        return response()->json(compact('month', 'year', 'begin', 'end', 'pointages'));
    }
    
    public function site($id, Request $request){
        $interval = PaieUtil::getIntervalByAgent($request->year, $request->month);
        if(!$interval)
            return response()->json(['message' => 'set_site']);
        
        $begin = $interval['begin'];
        $end = $interval['end'];

        $month = $end->format('n');
        $year = $end->format('Y');
        $begin = $begin->format("Y-m-d");
        $end = $end->format("Y-m-d");
        $pointages = DB::select("SELECT p.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi, p.date_pointage 
            FROM pointages p
            LEFT JOIN employes e ON e.id = p.employe_id
            WHERE (p.soft_delete is null or p.soft_delete = 0) and p.site_id = ? and p.date_pointage > ? and p.date_pointage < ?
            ORDER BY p.date_pointage DESC", [$id, $begin . ' 00:00:00', $end . ' 23:59:00']);
        return response()->json(compact('month', 'year', 'begin', 'end', 'pointages'));
    }
}
