<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Alarm;
use Illuminate\Support\Facades\DB;
use App\Http\Util\PaieUtil;

class AlarmController extends Controller
{
    public function __construct() {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public static function index(Request $request){
        if($request->employe_id)
            $employe_id = $request->employe_id;
        else
            $employe_id = $request->user()->employe_id;
        if($employe_id){
            $begin = (\DateTime::createFromFormat("Y-m-d H:i:s", $request->date_visite))
                ->sub(new \DateInterval('PT10M'));
            $end = (\DateTime::createFromFormat("Y-m-d H:i:s", $request->date_visite))
                ->add(new \DateInterval('PT10M'));
            $vigilances = Alarm::where("employe_id", $employe_id)
                ->where("site_id", $request->site_id)
                ->where("dtarrived", ">", $begin)->where("dtarrived", "<=", $end)
                ->orderBy("dtarrived", "asc")
                ->get();

            if (!$vigilances->isEmpty()) {
                $startVisite = $vigilances[0]['dtarrived'];
                if ($startVisite) {
                    $startVisiteTime = (\DateTime::createFromFormat("Y-m-d H:i:s", $startVisite));
                    $startTime = $startVisiteTime->add(new \DateInterval('PT10M'))->format('Y-m-d H:i:s');
                    $endTime = $startVisiteTime->add(new \DateInterval('PT90M'))->format('Y-m-d H:i:s');

                    $endVisite = DB::select('SELECT max(dtarrived) as date FROM alarms WHERE
                    employe_id = ? AND
                    site_id = ? AND
                    dtarrived BETWEEN ? AND ?', [
                        $employe_id,
                        $request->site_id,
                        $startTime,
                        $endTime
                    ]);
                    return response()->json([
                        'vigilances' => $vigilances,
                        'endVisite' => $endVisite,
                        'isCompleted' => $endVisite[0]->date != null
                    ]);
                }
                return response()->json([
                    'vigilances' => $vigilances,
                    'endVisite' => null,
                    'isCompleted' => false
                ]);
            }
        }
        return response()->json([]);
    }

    public function employe($id, Request $request){
        $begin = (new \DateTime($request->date))->format("Y-m-d 00:00:00");
        $end = (new \DateTime($request->date))->format("Y-m-d 23:59:59");
        if ($request->datetime) {
            $begin = (new \DateTime($request->datetime))->sub(new \DateInterval('PT1H'))->format("Y-m-d H:i:s");
            $end = (new \DateTime($request->datetime))->add(new \DateInterval('PT1H'))->format("Y-m-d H:i:s");
        }
        $empreintes = DB::select("SELECT a.idademco, s.nom as 'site', a.dtarrived FROM alarms a
            LEFT JOIN sites s ON s.idsite = a.site_id
            WHERE a.employe_id = ? and( a.dtarrived BETWEEN ? AND ?) and codeTevent=1000
            ORDER BY a.dtarrived DESC", [$id, $begin, $end]);
        $month = (new \DateTime($request->date))->format("M");
        $year = (new \DateTime($request->date))->format("Y");
        return response()->json(compact('month', 'year', 'begin', 'end', 'empreintes'));
    }
}
