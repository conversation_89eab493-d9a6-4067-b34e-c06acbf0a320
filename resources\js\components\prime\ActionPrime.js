import React, { useState } from 'react';

import { Link } from 'react-router-dom';
import NoteModal from '../input/NoteModal';

export default function ActionPrime({auth, prime, updateData}) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [currentAction, setAction] = useState(null)
    const [showPrintModal, setShowPrintModal] = useState(false)
    const [toDone, setToDone] = useState(false);

    const handleCancelPrime = (id) => {
        setAction({
            header: auth.role == 'validateur' ? "Annuler le prime" : "Annuler la demande de prime",
            request: "/api/prime/cancel_prime/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleDoTraite = (id) => {
        setAction({
            header: "Traitement de la demande",
            request: "/api/prime/do_traite/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleCancelValidation = (id) => {
        setAction({
            header: "Annuler la demande de validation",
            request: "/api/prime/cancel_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleRequestValidation = (id) => {
        setAction({
            header: "Demande de validation",
            request: "/api/prime/request_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    

    const handleReplyValidation = (id) => {
        setAction({
            header: "Réponse à la demande",
            request: "/api/prime/reply_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    
    const handleDone = (id) => {
        setAction({
            header: "Terminer le traitement de la prime",
            request: "/api/prime/save_done/" + id,
            required: true
        })
        toggleNoteModal(true)
        setToDone(true);
    }
    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction}
                updateData={() => updateData()}
                closeModal={() => toggleNoteModal(false)}
                toDone={toDone}
                setToDone={setToDone}
            /> 
            
        }
        <div className='action-container'>
            {
                (["traite","done"].includes(prime.status) && auth.role == "resp_rh") && 
                <span onClick={() => setShowPrintModal(true)}>Imprimer</span>
            }
            {
                (["traite"].includes(prime.status) && ["resp_rh"].includes(auth.role) && prime.montant) && 
                <span onClick={() => handleDone(prime.id)}>Terminer</span>
            }
            {
                (['demande'].includes(prime.status) && ["resp_rh"].includes(auth.role) && !prime.montant) &&
                <span onClick={() => handleRequestValidation(prime.id)}>Validation</span>
            }
            {
                (["traite"].includes(prime.status) && auth.role == "validateur") && 
                <span>
                    <Link to={"/prime/edit/" + prime.id}>Editer</Link>
                </span>
            }
            {
                (["validation"].includes(prime.status) && ["validateur"].includes(auth.role)) &&
                <span>
                    <Link to={"/prime/do_validation/" + prime.id}>Répondre</Link>
                </span>
            }
            {
                (["validation"].includes(prime.status) && ["resp_rh"].includes(auth.role)) &&
                <span onClick={() => handleCancelValidation(prime.id)}>Annuler la demande de validation</span>
            }
            {
                ((['demande'].includes(prime.status) && ["resp_rh"].includes(auth.role) && !prime.montant) ||
                (['validation','traite'].includes(prime.status) && ["validateur"].includes(auth.role)) || 
                (['demande'].includes(prime.status) && auth.id == prime.user_id && ["superviseur","resp_sup","resp_op"].includes(auth.role))) &&
                <span onClick={() => handleCancelPrime(prime.id)}>{auth.role == 'validateur' ? "Annuler le prime" : "Annuler la demande de prime"}</span>
            }
            {
                (["draft"].includes(prime.status) && auth.id == prime.user_id) && 
                <span>
                    <Link to={"/prime/send_back/" + prime.id}>{auth.role == "resp_rh" ? "Refaire le traitement" : "Renvoyer"}</Link>
                </span>
            }
        </div>
    </div>
}