const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const {sendMail} = require("../auth")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == "task")
const destination_vg = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

function getDayOrNightExport(){
	let beginDay = moment().set({hour:6, minute:10, second:0})
	let endDay = moment().set({hour:18, minute:10, second:0})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
	else {
		if(moment().isBefore(beginDay))
			return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 06:00:00"
		return moment().format("YYYY-MM-DD") + " 06:00:00"
	}
}
function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectLastFaitMarquantExport = "SELECT value FROM params p WHERE p.key = 'last_fait_marquant_export'"

function sqlSelectFaitMarquant(dateString){
    const begin = dateString
    const end = moment(dateString).add(12, "hours").format("YYYY-MM-DD HH:mm:ss")
	return "SELECT fm.id, fm.site_id, fm.objet, fm.commentaire, fm.created_at, " +
        "s.nom as 'site', u.name as 'user_nom', coalesce(ur.email, u.email) as 'user_email', u.role " +
        "from fait_marquants fm " +
        "left join sites s on s.idsite = fm.site_id " +
        "left join users u on u.id = fm.user_id " +
        "left join users ur on ur.id = u.real_email_id " +
        "where fm.created_at > '" + begin +"' and fm.created_at <= '" + end +"' " +
        "order by fm.created_at"
}

function sqlUpdateLastFaitMarquantExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_fait_marquant_export'"
}

function generateFaitMarquantExcelFile(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
	const fontHeader = { size: 16, bold: true }

    const worksheet = workbook.addWorksheet("fait marquant")
    worksheet.getColumn('A').width = 40
    worksheet.getColumn('B').width = 40
    worksheet.getColumn('C').width = 70
    worksheet.getColumn('D').width = 40
    worksheet.getColumn('E').width = 15
    worksheet.getCell('A1').value = header + " (" + data.length + ")"
    worksheet.getCell('A1').font = fontHeader
    worksheet.mergeCells('A1:E1')

    let line = 3
    
    worksheet.getCell('A' + line).value = "Site"
    worksheet.getCell('A' + line).border = borderStyle
    worksheet.getCell('A' + line).font = fontBold
    worksheet.getCell('B' + line).value = "Objet"
    worksheet.getCell('B' + line).border = borderStyle
    worksheet.getCell('B' + line).font = fontBold
    worksheet.getCell('C' + line).value = "Commentaire"
    worksheet.getCell('C' + line).border = borderStyle
    worksheet.getCell('C' + line).font = fontBold
    worksheet.getCell('D' + line).value = "Utilisateur"
    worksheet.getCell('D' + line).border = borderStyle
    worksheet.getCell('D' + line).font = fontBold
    worksheet.getCell('E' + line).value = "Créé le"
    worksheet.getCell('E' + line).border = borderStyle
    worksheet.getCell('E' + line).font = fontBold
    line++

    data.forEach(fm => {
        worksheet.getCell('A' + line).value = capitalizeFirstLetter(fm.site)
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('B' + line).value = fm.objet
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('C' + line).value = fm.commentaire
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('D' + line).value = fm.user_nom + " <" + fm.user_email + ">"
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('E' + line).value = moment(fm.created_at).format("DD-MM-YY HH:mm")
        worksheet.getCell('E' + line).border = borderStyle
        line++
    })
}

function doFaitMarquantExport(dateString){
	console.log("doFaitMarquantExport")
    pool.query(sqlSelectFaitMarquant(dateString), [], async (err, faitMarquants) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb fait_marquant: " + faitMarquants.length)
            const workbookFaitMarquant = new Excel.Workbook()
            const header = "Fait marquant " + moment(dateString).format("DD MMMM YYYY") + (moment(dateString).format("HH:mm:ss") == "06:00:00" ? " JOUR" : " NUIT")
            generateFaitMarquantExcelFile(workbookFaitMarquant, header, faitMarquants)
            const faitMarquantSiteBuffer = await workbookFaitMarquant.xlsx.writeBuffer()
            sendMail(
                pool,
                isTask ? destination_vg : destination_test,
                header, 
                faitMarquants.length > 0 ?
                "<h2>Fait marquant</h2>" +
                "<table>" +
                    faitMarquants.map(f => "<tr>" +
                            "<td>" +
                                "<b>" + f.site + "</b>" +
                                "<div>" + f.objet + "</div>" +
                            "</td>" +
                            "<td>" +
                                "<div>" + f.commentaire.replace("<", "&lsaquo;").replace(">", "&rsaquo;") + "</div>" +
                            "</td>" +
                        "</tr>"
                    ).join(" ")
                + "</table>"
                :
                "<p>Aucun fait marquant durant le service</p>"
                , [
                    {
                        filename: header + ".xlsx",
                        content: faitMarquantSiteBuffer
                    },
                ],
                (response) => {
                    if(response && isTask){
                        pool.query(sqlUpdateLastFaitMarquantExport(dateString), [], (e, r) =>{
                            if(e)
                                console.error(e)
                            else
                                console.log("update last diag export: " + r)
                            process.exit(1)
                        })
                    }
                    else
                        process.exit()
                },
                isTask
            )
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && ["06:00:00", "18:00:00"].includes(process.argv[3])){
    console.log("send test...")
    doFaitMarquantExport(process.argv[2] + ' ' + process.argv[3])
}
else if(isTask){
    let date_vigilance = getDayOrNightExport()
    pool.query(sqlSelectLastFaitMarquantExport, [], (err, result) => {
        if(err){
            console.error(err)
            process.exit()
        }
        else if(result && result[0].value == date_vigilance) {
            console.log("export list fait_marquant already done!")
            process.exit(1)
        }
        else {
            console.log("exporting ...")
            doFaitMarquantExport(date_vigilance)
        }
    })
}
else
    console.log("please specify command!")