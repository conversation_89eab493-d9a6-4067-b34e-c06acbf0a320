import React, { useState } from 'react'
import InputText from '../../input/InputText'
import MyDocument from './FichePaie';

export default function ModalPrintAll({ datas, show, toggleShow }) {
    const [siret, setSiret] = useState("51333 31 2011 0 00658");
    const paieDone = datas.filter(item => item.status == 'done');
    return (
        <>
            {show &&
                <div className="modal">
                    <div style={{ maxHeight: '60rem', overflowY: 'auto' }}>
                        <h3>Fiche de paie</h3>
                        <InputText label="Siret" onChange={setSiret} value={siret} />
                        <div className="form-button-container">
                            <div className="" onClick={() => toggleShow(false)}>
                                <MyDocument data={paieDone} siret={siret} />
                            </div>
                            <button className="btn" onClick={()=>toggleShow(false)}>Annuler</button>  
                        </div>
                    </div>
                </div>
            }
        </>
  )
}
