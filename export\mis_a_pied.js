const moment = require('moment')
const mysql = require('mysql2')
const nodemailer = require("nodemailer")
const hbs = require('nodemailer-express-handlebars')
const path = require('path')
const Excel = require("exceljs")
const { rapport_config, sendMail } = require('../auth')
const matricule = require("../resources/js/components/util/matricule")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const destination_vg = (emails, superviseurs) => {
    return ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
        "<EMAIL>","<EMAIL>", "<EMAIL>"]
        .concat(emails.map(e => e.email)).concat(superviseurs.map(sup => sup.superviseur_email))
}

const destination_test = ["<EMAIL>", "<EMAIL>"]

const isTask = process.argv[2] == 'task'

const sqlSelectEmailResp = "SELECT u.id, coalesce(ur.email, u.email) as 'email' FROM users u " +
    "LEFT JOIN users ur ON ur.id = u.real_email_id " +
    "WHERE u.role = 'resp_sup' or u.role = 'resp_op'"

const sqlSelectMisAPied = "SELECT a.id, a.depart, a.retour, a.employe_id, a.superviseur_id, a.nb_jour, " +
    "e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.societe_id, " +
    "s.nom as 'site', us.name as 'superviseur_nom', coalesce(ur.email, us.email) as 'superviseur_email' " +
    "FROM absences a " + 
    "left join employes e on e.id = a.employe_id " +
    "left join sites s on s.idsite = e.real_site_id " +
    "left join users us on us.id = a.superviseur_id " +
    "left join users ur on ur.id = us.real_email_id " +
    "WHERE a.type_absence = 'mis_a_pied' and status='demande' " +
    "order by a.superviseur_id"

const sqlSelectDateExport = "SELECT value FROM params p WHERE p.key = 'last_mis_a_pied_export'"

const sqlUpdateDateEport = "UPDATE params p SET p.value = ? WHERE p.key = 'last_mis_a_pied_export'"

function generateMisAPiedExcelFile(workbook, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }

	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    data.forEach(sup => {
        const worksheet = workbook.addWorksheet(sup.superviseur_nom + " <" + sup.superviseur_email + ">")
        worksheet.getColumn('A').width = 20
        worksheet.getColumn('B').width = 50
        worksheet.getColumn('C').width = 10
        worksheet.getColumn('D').width = 50
        worksheet.getCell('A1').value = sup.superviseur_nom + " (" + sup.absences.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:E1')
    
        let line = 3
        
        worksheet.getCell('A' + line).value = "Matricule"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontBold
        worksheet.getCell('B' + line).value = "Agent"
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).font = fontBold
        worksheet.getCell('C' + line).value = "Nb Jour"
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).alignment = alignmentStyle
        worksheet.getCell('C' + line).font = fontBold
        worksheet.getCell('D' + line).value = "Site"
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).font = fontBold
        worksheet.mergeCells('A' + (line-1) + ':D' + (line-1))
        line++
    
        sup.absences.forEach(ab => {
            worksheet.getCell('A' + line).value = matricule(ab)
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = ab.nom
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = ab.nb_jour
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('C' + line).alignment = alignmentStyle
            worksheet.getCell('D' + line).value = ab.site
            worksheet.getCell('D' + line).border = borderStyle
            line++
        })
        
    });
}

function doMisAPiedExport(dateString){
	console.log("doMisAPiedExport")
    pool.query(sqlSelectEmailResp, [], async (err, emails) => {
        if(err)
            console.error(err)
        else {
            console.log("nb email: " + emails.length)
            pool.query(sqlSelectMisAPied, [], async (err, absences) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb mis à pied : " + absences.length)
                    const misAPiedBySup = []
                    let currentSup = {"absences": [], ...absences[0]}
                    absences.forEach(ab => {
                        if(currentSup.superviseur_id != ab.superviseur_id){
                            currentSup = {"absences": [ab], ...ab}
                            misAPiedBySup.push(currentSup)
                        }
                        else 
                            currentSup.absences.push(ab)
                
                    })
                    console.log(misAPiedBySup)

                    const workbook = new Excel.Workbook()
                    const header = "Mis à pied non appliqué par superviseur"
                    generateMisAPiedExcelFile(workbook, misAPiedBySup)
                    const absenceBuffer = await workbook.xlsx.writeBuffer()
                    
                    sendMail(
                        pool,
                        isTask ? destination_vg(emails, misAPiedBySup) : destination_test,
                        header,
                        misAPiedBySup.length > 0 ?
                            "<p>Ci-joint les mis à pieds non appliqué par superviseur : </p>" +
                            "<ul>" +
                                misAPiedBySup.map(sup => "<li>" + sup.superviseur_nom + " &#60;" + sup.superviseur_email + "&#62;" + "</li>").join("") +
                            "</ul>"
                        :
                            "<p>Aucune mis à pied à appliqué</p>",
                        [
                            {
                                filename: header + ".xlsx",
                                content: absenceBuffer
                            },
                        ],
                        (response) => {
                            if(response && isTask){
                                pool.query(sqlUpdateDateEport, [dateString], (e, r) =>{
                                    if(e)
                                        console.error(e)
                                    else
                                        console.log("update last diag export: " + r)
                                    process.exit(1)
                                })
                            }
                            else
                                process.exit(1)
                        },
                        isTask
                    )
                }
            })
        }
    })
}


if(process.argv[2] == 'test'){
    console.log("send test...")
    doMisAPiedExport(moment().format("YYYY-MM-DD"))
}
else if(isTask){
    if(moment().day() == 4 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
        pool.query(sqlSelectDateExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && moment().format("YYYY-MM-DD") == result[0].value){
                console.log("export mis à pied already done!")
                process.exit()
            }
            else
                doMisAPiedExport(moment().format("YYYY-MM-DD"))
        })
    }
    else {
        console.log("Not thursday, skip export mis à pied.")
    }
}
else
    console.log("please specify command!")