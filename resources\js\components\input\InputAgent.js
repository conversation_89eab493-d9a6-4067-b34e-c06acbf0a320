import axios from 'axios'
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';

export default function InputAgent({label, value, disabled, onChange, required, hideInput, closeModal, useLink, useAction, updateData, actionParam}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [error, setError] = useState()
    const [modalOpen, toggleModal] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [employes, setEmployes] = useState([])
    const [searchValue, setSearchValue] = useState("")
    const [selectedEmploye, setSelectedEmploye] = useState(null)

    const handleSelectEmploye = (ag) => {
        toggleModal(false)
        if(onChange) onChange(ag)
        if(useLink) {
            let params = new URLSearchParams(location.search)
            params.set("employe_id", ag.id)
            navigate(location.pathname + "?" + params)
        }
        if(useAction){
            setSearchValue("")
            setSelectedEmploye(ag)
        }
        else if(closeModal) closeModal()
    }

    const handleConfirmModal = (e) => {
        e.preventDefault()
        axios.post(useAction, {employe_id: selectedEmploye.id}, useToken())
        .then((res) => {
            if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
            else {
                if(closeModal) closeModal()
                if(updateData) updateData(true)
            } 
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    const handleCloseModal = () => {
        toggleModal(false)
        if(selectedEmploye) {
            setSelectedEmploye(null)
            setEmployes([])
        }
        else if(closeModal) 
            closeModal()
    }
    
    const handleSearch = (initial, e) => {
        if(e) e.preventDefault()
        const params = new URLSearchParams()
        if(initial){
            setDataLoaded(true)
            setEmployes([])
            params.set("offset", 0)
        }
        else
            params.set("offset", employes.length)
        params.set("value", searchValue)
        axios.get('/api/employe/search?' + params, useToken())
        .then((res) => {
            if(res.data){
                let dataList = []
                res.data.employes.forEach(ag => {
                    dataList.push({
                        id: ag.id,
                        matricule: matricule(ag),
                        date_embauche: ag.date_embauche,
                        nom: ag.nom,
                        danger: ag.soft_delete ? true : false,
                        site: {
                            id: ag.site_id,
                            nom: ag.site,
                        }
                    })
                });
                if(initial)
                    setEmployes(dataList)
                else {
                    const list = employes.slice().concat(dataList)
                    setEmployes(list)
                }
                setDataLoaded(res.data.employes.length < 30)
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }
    
    const fetchMoreData = () => {
        setTimeout(() => {
            handleSearch()
        }, 300);
    }

    useEffect(() => {
        handleSearch(true)
    }, [])

    return <div>
        {
            !hideInput &&
            <div className='input-container'>
                <label>{label ? label : "Employé"} {required && <span className='danger'>*</span>}</label>
                <input
                    type="text" 
                    disabled={disabled}
                    value={value ? (value.matricule + ' ' + value.nom) : ''}
                    readOnly
                    onClick={() => {toggleModal(true)}}
                    />
            </div>
        }
        {
            (hideInput || modalOpen) &&
            <div className='modal'>
                <div>
                    <h2>
                        {selectedEmploye ? 'Confirmation' : 'Agent'}
                    </h2>
                    {
                        !selectedEmploye &&
                        <div className='search-container'>
                            <input type="text" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} placeholder="Nom de l'employe ou matricule"/>
                            <button onClick={e => handleSearch(true, e)}>Rechercher</button>
                        </div>
                    }
                    {
                        selectedEmploye ?
                            <div>
                                <span>{selectedEmploye.matricule} {selectedEmploye.nom}</span><br/>
                                <span className='secondary'>{selectedEmploye.site.nom}</span><br/>
                            </div>
                        :
                            <div id="scrollableList"> 
                                <InfiniteScroll
                                    dataLength={employes.length}
                                    next={fetchMoreData}
                                    hasMore={!allDataLoaded}
                                    loader={<LoadingPage/>}
                                    scrollableTarget="scrollableList"
                                >
                                    <div className='list-container'>
                                        <ul>
                                            {
                                                employes.map(ag => {
                                                    return <li key={ag.id} onClick={() => handleSelectEmploye(ag)} className={ag.danger ? 'danger' : ''}>
                                                        {ag.matricule} {ag.nom}<br/>
                                                        <span className='secondary'>{ag.site && ag.site.nom}</span>
                                                    </li>
                                                })
                                            }
                                        </ul>
                                    </div>
                                </InfiniteScroll>
                            </div>
                    }
                    {
                        error &&
                        <div className='danger'><br/>{error}</div>
                    }
                    <div className='form-button-container'>
                        {
                            selectedEmploye &&
                            <button onClick={handleConfirmModal} className='primary'>Confirmer</button>
                        }
                        <button onClick={handleCloseModal}>Annuler</button>
                    </div>
                </div>
            </div>
        }
    </div>;
}