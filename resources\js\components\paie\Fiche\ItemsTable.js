import React from "react";
import { View, StyleSheet } from "@react-pdf/renderer";
import TableHeader from "./TableHeader";
import TableRow from "./TableRow";
import TableFooter from "./TableFooter";
import TopHeaderTable from "./TopHeaderTable";

const styles = StyleSheet.create({
    tableContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        // marginTop: 24,
        borderWidth: 1,
        // borderColor: "#bff0fd",
        width: "107%",
    },
});

const ItemsTable = ({ paie, siret, setSiret }) => (
    <View>
        {paie && (
            <View style={styles.tableContainer}>
                <TopHeaderTable
                    items={paie}
                    siret={siret}
                    setSiret={setSiret}
                />
                <TableHeader />
                <TableRow items={paie} />

                <TableFooter items={paie} />
            </View>
        )}
    </View>
);

export default ItemsTable;
