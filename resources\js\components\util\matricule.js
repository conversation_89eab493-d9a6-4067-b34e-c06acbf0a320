function matricule(employe, arp = false, recrutement = false) {
    let result = 'Ndf';
    if (arp) {
        if (employe.arp_societe_id == 1)
            result = "DGM-" + employe.arp_numero_employe
        else if (employe.arp_societe_id == 2)
            result = 'SOIT-' + employe.arp_num_emp_soit
        else if (employe.arp_societe_id == 3)
            result = 'ST-' + employe.arp_numero_stagiaire
        else if (employe.arp_societe_id == 4)
            result = 'SM-'
        else if (employe.arp_societe_id == 5)
            result = 'TMP'
        else if (employe.arp_societe_id == 6)
            result = 'SAOI-' + employe.arp_num_emp_saoi
        else
            result = 'Ndf'
    }
    else if (employe.societe_id) {
        if (employe.societe_id == 1)
            result = "DGM-" + employe.numero_employe
        else if (employe.societe_id == 2)
            result = 'SOIT-' + employe.num_emp_soit
        else if (employe.societe_id == 3)
            result = recrutement ? 'RC-' + employe.numero_stagiaire : 'ST-' + employe.numero_stagiaire
        else if (employe.societe_id == 4)
            result = 'SM-'
        else if (employe.societe_id == 5) 
            result = 'TMP'
        else if (employe.societe_id == 6) {
            result = 'SAOI-' + employe.num_emp_saoi
        }
        else
            result = 'Ndf'
    }   
    return result
}

module.exports = matricule;