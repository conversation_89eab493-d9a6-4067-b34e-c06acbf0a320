import React, { useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import axios from 'axios';
import useToken from '../util/useToken';
import moment from 'moment';

export default function Rappel({auth, calls, setCalls, setCurrentId, currentId}) {
    const locationSearch = useLocation().search;
    const [isLoading, toggleLoading] = useState(false);
    const [allDataLoaded, setDataLoaded] = useState(false);

    const updateData = (initial) =>{
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if(initial){
            toggleLoading(true);
            params.set("offset", 0);
        }
        else params.set("offset", calls.length);
        axios.get("/api/appelle/get_recall?" + params, useToken()).then((res) => {
            if(isMounted){
                if(res.data.error)
                    console.error("Error: " + res.data.error);
                else {
                    if(initial) setCalls(res.data.results);
                    else {
                        const list = calls.slice().concat(res.data.results);
                        setCalls(list);
                    }
                    // setDataLoaded(res.data.results.length < 30)
                    setDataLoaded(true)
                }
                toggleLoading(false);
            } 
        })
    }
    useEffect(() => updateData(true), [locationSearch]);
    const fetchMoreData = () => {
        setTimeout(() => updateData(), 300)
    }
    return (
        <>
            {
                isLoading ? 
                <LoadingPage />
                :
                <div> 
                    <div className="padding-container space-between">
                        <h2>Contact à rappeler</h2>
                    </div> 
                    {
                        calls.length == 0 ?
                        <h3 className="center secondary">Aucun données trouvé</h3> 
                        :
                        <div>
                            <InfiniteScroll dataLength={calls.length} next={fetchMoreData} hasMore={!allDataLoaded} loader={<LoadingPage />}>
                                <div className="line-container">
                                    <div className="row-list">
                                        <b className='line-cell-md'>Contact</b>
                                        <b className="line-cell-md">Type</b>
                                        <b className="">Heure</b>
                                    </div>
                                </div>
                                {
                                    calls.map((cl) => (
                                        <div onClick={() => setCurrentId(cl.uniqueid)} 
                                            className={`line-container ${currentId && currentId == cl.uniqueid ? "selected" : ""}`} 
                                            key={cl.uniqueid}
                                        >
                                            <div className="row-list">
                                                <span className="line-cell-md">
                                                    {cl.src}
                                                </span>
                                                <span className="line-cell-md">{cl.disposition == "BUSY" ? "OCCUPÉ" : cl.disposition == "NO ANSWER" ? "PAS DE RÉPONSE" : cl.disposition}</span>
                                                <span className="">{moment(cl.datetime).from(auth.datetime)}</span>
                                                {/* <span className="">{moment(cl.datetime).format('DD-MMM-YYYY HH:mm:ss')}</span> */}
                                            </div>
                                        </div>
                                    ))
                                }
                            </InfiniteScroll>
                        </div>
                    }
                </div> 
                
            }
            
        </>
    )
}
