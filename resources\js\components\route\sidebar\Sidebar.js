import React, { useState,useEffect } from 'react';
import { Link, useLocation, useNavigate } from "react-router-dom";
import './sidebar.css'
import ConfirmModal from '../../modal/ConfirmModal';
import moment from 'moment';
import useToken from '../../util/useToken';
import { GiInjustice, GiMoneyStack } from 'react-icons/gi';
import { GrUserAdmin } from 'react-icons/gr';
import { MdOutlineDashboard, MdOutlineHandshake, MdOutlineRealEstateAgent } from 'react-icons/md';
import { RiShirtLine } from 'react-icons/ri';
import { AiOutlineClose } from 'react-icons/ai';
import { FiHeadphones, FiLogOut } from 'react-icons/fi';
import { LiaToolsSolid } from 'react-icons/lia';
import { BiPurchaseTag } from 'react-icons/bi';

export default function Sidebar({auth, closeSidebar, disconnectUser}) {
    const [showLogoutModal, toggleLogoutModal] = useState(false)
    const [currentItem, setCurrentItem] = useState(null)
    // const [allRoles, setAllRoles] = useState([])
    const location = useLocation();

    // useEffect(()=>{
    //     let isMounted = true;
    //     axios.get('/api/user/get_all_roles', useToken())
    //         .then((res) => {
    //             if(isMounted){
    //                 if (res.data.roles) {
    //                     const roles = res.data.roles
    //                     setAllRoles(roles.map(r => r.name))
    //                 }
    //             }
    //         })
    //         .catch((e) => {
    //             console.error(e)
    //         }) 
    //     return () => {
    //         isMounted = false;
    //     } 
    // }, [])

    const handleCloseSidebar = () => {
        if(closeSidebar)
            closeSidebar()
    }
    // console.log(allRoles)
    const handleDashBoard = () => {
        setCurrentItem(null);
        if(closeSidebar){
            closeSidebar()
        }
    }
    const getWindowSize = () => {
        const {innerWidth, innerHeight} = window;
        return {innerWidth, innerHeight};
    }    
    const [windowSize, setWindowSize] = useState(getWindowSize())

    useEffect(() => {
        function handleWindowResize() {
          setWindowSize(getWindowSize());
        }
        window.addEventListener('resize', handleWindowResize);
        return () => {
            window.removeEventListener('resize', handleWindowResize);
        }
    }, [windowSize])
    
    function getCurrentService() {
        const now = moment();
        let serviceDate;
        if (now.hour() >= 18)
            serviceDate = now.set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        else if (now.hour() >= 6)
            serviceDate = now.set({ hour: 6, minute: 0, second: 0, millisecond: 0 });
        else 
            serviceDate = now.subtract(1, 'days').set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        return serviceDate.format('YYYY-MM-DD HH:mm:ss');
    }

    useEffect(() => {
        const allMenuItems = [
            ...operationMenuItems,
            ...rhMenuItems,
            ...techMenuItems,
            ...paieMenuItems,
            ...roomMenuItems,
            ...juridiqueMenuItems,
            ...tenueItems
        ];
        const matchedItem = allMenuItems.find(item => {
            const cleanPath = item.path.split('?')[0];
            return location.pathname === cleanPath || location.pathname.startsWith(`${cleanPath}/`);
        });
        if(matchedItem){
            if (matchedItem.key == 'equipement' && auth.role == 'achat') {
                setCurrentItem('equipement_achat');
            }
            else 
                setCurrentItem(matchedItem.key)
        }
        else setCurrentItem(null)
    }, [location.pathname]);

    const makeActive = (item) => currentItem === item ? "submenu-item active secondary" : "submenu-item";
    const operationMenuItems = [
        { key: 'satisfaction', label: 'Fiche de satisfaction', path: '/satisfaction', roles: ["resp_sup", "resp_op", "superviseur", "validateur", "access"] },
        { key: 'service_24', label: 'Service 24', path: '/service24', roles: ["superviseur", "validateur", "resp_sup", "resp_op", "room"] },
        { key: 'planning', label: 'Planning', path: `/planning?date_planning=${moment().format('YYYY-MM')}`, roles: ["resp_sup", "resp_op", "superviseur", 'validateur','access'] },
        { key: 'site', label: 'Site', path: `/site`, roles: ["rh", "resp_rh", "validateur", "superviseur", "resp_sup", "resp_op", "access", "room", "admin"] },
        { key: 'visite_poste', label: 'Visite de poste', path: `/visite-poste`, roles: ["superviseur", "resp_sup", "resp_op", "validateur", "access"] },
        { key: 'fait_marquant', label: 'Fait marquant', path: `/fait-marquant`, roles: ["superviseur", "resp_sup", "resp_op", "validateur", "juridique", "room", "access", "rh", "resp_rh"] },
    ];

    const rhMenuItems = [
        { key: 'employe', label: 'Employé', path: '/employe?actif=1', roles: ["rh", "resp_rh", "validateur", "juridique", "resp_sup", "resp_op", "superviseur", "admin", "tenue", "room"] },
        { key: 'recrutement', label: 'Recrutement', path: `/recrutement`, roles: []},
        { key: 'sanction', label: 'Sanction', path: ["room"].includes(auth.role) ? "/sanction/controlroom": "/sanction", roles: ["rh", "resp_rh", "superviseur", "resp_sup", "resp_op", "room", "validateur", "access"]},
        { key: 'conge', label: 'Congé', path: '/absence/conge', roles: [] },
        { key: 'permission', label: 'Permission', path: '/absence/permission', roles: ["superviseur", "rh", "resp_rh", "validateur", "access", "resp_sup", "resp_op"] },
        { key: 'mis_a_pied', label: 'Mise à pied', path: '/absence/mis_a_pied', roles: ["superviseur", "rh", "resp_rh", "validateur", "access", "resp_sup", "resp_op"] },
        { key: 'badge', label: 'Badge', path: `/badge`, roles: ["rh", "resp_rh"]},
    ];

    const techMenuItems = [
        { key:'sav', label: "SAV", path:"/sav/autre", roles: ["tech", "superviseur", "resp_sup", "resp_op", "room", "validateur", "access"]},
        { key:'tag_rondier', label:"Tage et rondier", path:"/sav/tag", roles: ["tech", "superviseur", "resp_sup", "resp_op", "room", "validateur", "access"]},
        { key:"biometrique", label: "Biométrique", path:"/sav/biometrique", roles: ["electronique", "tech", "superviseur", "resp_sup", "resp_op", "room", "validateur", "access"]},
        { key: 'flotte', label: 'Flotte', path: `/flotte`, roles: ["superviseur", "rh", "resp_rh", "validateur", "access", "resp_sup", "resp_op", "tech","electronique","achat","room","tenue","simple"] }
    ]

    const paieMenuItems = [
        { key: 'avance', label: 'Avance', path: `/avance?date_paie=${moment().format('Y-M')}`, roles: ["resp_rh", "superviseur", "resp_sup", "resp_op", "validateur"]},
        { key: 'part_variable', label: "Part variable", path: `/part-variable?date_paie=${moment().format("YYYY-MM")}`, roles: ["resp_rh", "validateur", ...(auth.nb_pv > 0 ? [auth.role] : [])]},
        { key: 'reclamation', label: 'Réclamation', path: '/reclamation', roles: ["superviseur", "rh", "resp_rh", "validateur", "access", "resp_sup", "resp_op", "admin"] },
        { key: 'paie', label: 'Paie', path: '/paie', roles: ["resp_rh", "validateur"] },
        { key: 'prime', label: "Prime", path: "/prime", roles: ["resp_rh", "rh", "superviseur", "resp_sup", "resp_op", "validateur", "access"]},
        { key: 'deduction', label: "Déduction", path:'/deduction', roles:["resp_rh", "validateur"]},
    ]

    const roomMenuItems = [
        { key: 'appelle', label: 'Appelle', path: '/appelle', roles: ["admin", "room", "validateur", "resp_room"] },
        { key: 'a_rappeler', label: 'Contact à rappeler', path: '/rappel', roles: ['room', 'resp_room'] },
    ]

    const juridiqueMenuItems = [
        { key: 'recouvrement', label: 'Recouvrement', path: '/recouvrement', roles: ["juridique", "validateur"] },
        { key: 'plainte', label: 'Plainte', path: '/plainte?status=traite',  roles: ["juridique", "validateur"] },
    ]

    const tenueItems = [
        { key: 'dotation', label: 'Dotation', path: '/dotation', roles: ["tenue", "validateur", "superviseur","resp_op"]},
        { key: 'stock', label: 'Stock', path: '/stock', roles: ["tenue", "validateur"] },
        { key: 'equipement', label: 'Equipement', path: '/equipement?type_service=tenue', roles: ["superviseur", "validateur", "access", "resp_sup",  "resp_op", "tech", "electronique","tenue","simple"] },
    ]
    
    const achatMenuItems = [
        { key: 'da', label: 'DA', path: `/da`, roles: [] },
        { key: 'equipement_achat', label: 'Equipement', path: '/equipement?type_service=achat', roles: ["superviseur", "validateur", "access", "resp_sup",  "resp_op", "tech", "electronique","achat","simple"] },
    ]
    
    const adminItems = [
        { key: 'user', label: 'Utilisateur', path: '/user', roles: ["admin"]},
    ]

    const possibleRolesAccess = (menuItems) => {
        let allRole = []
        menuItems.map(items => {
            if(items.roles){
                if(items.roles.length > 0 ){
                    items.roles.map(item => {
                        if(!allRole.includes(item)){
                            allRole.push(item)
                        }
                    })
                }
                else if (!allRole.includes(auth.role)){
                    allRole.push(auth.role)
                }
            }
        })
        return allRole
    }

    const renderMenuGroup = ({ title, menuItems, icon: Icon, color }) => {
        const navigate = useNavigate();
        const isActive = menuItems.some(item => item.key === currentItem);
        return (
            <li className="menutitle" style={{ padding: '14px 10px' }}>
                <span
                    onClick={() => {
                            const firstItem = menuItems.find(i => i.roles.includes(auth.role) || i.roles.length == 0);
                            if (firstItem) {
                                setCurrentItem(firstItem.key);
                                navigate(firstItem.path)
                            };
                        }
                    }
                    style={{ display:'flex', textAlign: 'center' }}
                >
                    {Icon && <Icon style={{ marginRight: 5 }} size={30} color={color ??'#6a6b68'}/>}
                    <span style={{ marginTop: 8}}>{title.toUpperCase()}</span>
                </span>
                <ul className={isActive ? 'active sub-menu' : 'sub-menu'} >
                    {menuItems.map(({ key, label, path, roles }) => {
                        return (roles.includes(auth.role) || roles.length === 0) && (
                        <li key={key} className={makeActive(key)} style={{ paddingLeft : 30 }} onClick={() => {
                            setCurrentItem(key);
                            handleCloseSidebar();
                            navigate(path);
                        }}>
                            {label}
                        </li>
                        );
                    })}
                </ul>
            </li>
        );
    };

    return (
        <div className='show-sidebar custom-scroll' 
            onClick={(e) => {e.stopPropagation()}}
            style= {
                windowSize.innerWidth > 1000 ? {
                    height: (windowSize.innerHeight*100/80 - 71) + "px",
                    top: '71px',
                    position: 'fixed',
                    borderRight: '1px solid #ddd'
                }
                : {
                    height: (windowSize.innerHeight*100/80) + "px",
                    paddingBottom: "70px",
                    borderRight: "1px solid #ddd"
                }
            }
        >
            {
                showLogoutModal &&
                <ConfirmModal 
                    msg="Se déconnecter ?" 
                    confirmAction={disconnectUser} 
                    closeModal={() => toggleLogoutModal(false)}/>
            }
            <ul>
                {
                    windowSize.innerWidth < 1000 &&
                    <li>
                        <AiOutlineClose size={30} onClick={closeSidebar}/>
                    </li>
                }
                <li onClick={handleDashBoard} style={{ padding: '14px 10px' }} >
                    <Link to="/" style={{ display:'flex', textAlign: 'center' }} >
                        <MdOutlineDashboard style={{ marginRight: 5 }} size={30} color={' #01579b'}/>
                        <span  style={{ marginTop: 8}}>Tableau de bord</span>
                    </Link>
                </li>
                {
                    possibleRolesAccess(achatMenuItems).includes(auth.role) &&
                    renderMenuGroup({ title: 'achat', menuItems: achatMenuItems, icon: BiPurchaseTag, color: ' #ba68c8' })
                }
                {
                    possibleRolesAccess(rhMenuItems).includes(auth.role) &&
                    renderMenuGroup({ title: 'rh', menuItems: rhMenuItems, icon: MdOutlineHandshake, color: ' #e91e63' })
                }
                {
                    possibleRolesAccess(paieMenuItems).includes(auth.role) &&
                    renderMenuGroup({ title: 'paie', menuItems: paieMenuItems, icon: GiMoneyStack, color: ' #00e676' })
                }
                {
                    possibleRolesAccess(operationMenuItems).includes(auth.role) &&
                    renderMenuGroup({ title: 'operation', menuItems: operationMenuItems, icon: MdOutlineRealEstateAgent, color: ' #795548' })
                }
                {
                    possibleRolesAccess(tenueItems).includes(auth.role) &&
                    renderMenuGroup({ title: 'tenue', menuItems: tenueItems, icon: RiShirtLine, color: ' #1e88e5' })
                }
                {
                    possibleRolesAccess(juridiqueMenuItems).includes(auth.role) &&
                    renderMenuGroup({ title: 'juridique', menuItems: juridiqueMenuItems, icon: GiInjustice, color: ' #ff5722' })
                }
                {
                    possibleRolesAccess(techMenuItems).includes(auth.role) &&
                    renderMenuGroup({ title: 'tech', menuItems: techMenuItems, icon: LiaToolsSolid, color: ' #00838f' })
                }
                {
                    possibleRolesAccess(roomMenuItems).includes(auth.role) &&
                    renderMenuGroup({ title: 'room', menuItems: roomMenuItems, icon: FiHeadphones, color: ' #fbc02d' })
                }
                {
                    possibleRolesAccess(adminItems).includes(auth.role) &&
                    renderMenuGroup({ title: 'admin', menuItems: adminItems, icon: GrUserAdmin, color: ' #2772c2' })
                }
                <li style={{ display:'flex', textAlign: 'center', padding: '14px 10px' }} onClick={() => toggleLogoutModal(true)} >
                    <FiLogOut style={{ marginRight: 5 }} size={30} color={' #9e9e9e'}/>
                    <span style={{ marginTop: 8}}>Déconnecter</span>
                </li>
            </ul>
        </div>
    )
}