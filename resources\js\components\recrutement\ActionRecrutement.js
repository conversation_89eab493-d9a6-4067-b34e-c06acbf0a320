import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import useToken from '../util/useToken';
import ArchiveRecrutementModal from './ArchiveRecrutementModal';
import ConfirmModal from '../modal/ConfirmModal';
export default function ActionRecrutement({auth, recrutement, updateData}) {
    const [showArchiveModal, toggleArchiveModal] = useState(false);
    const [showRestoreModal, toggleRestoreModal] = useState(false);
    const [idsPj, setIdsPj] = useState([])

    useEffect(() => {
        getPj()
    }, [])

    const handleRestore = () => {
        toggleRestoreModal(false)
        axios.post("/api/recrutement/restore/" + recrutement.id, {}, useToken())
        .then(res => {
            updateData()
        })
    }

    const getPj = () => {
        axios.get("/api/recrutement/getPj/" + recrutement.id, useToken())
        .then(res => {
            setIdsPj(res.data.ids)
            console.log(res.data.ids)
        })
        .catch((error) => {
            console.error(error)
        })
    }

    return <div>
        {
            showArchiveModal &&
            <ArchiveRecrutementModal
                recrutement={recrutement}
                updateData={() => updateData(true)}
                closeModal={() => toggleArchiveModal(false)}/>
        }
        {
            showRestoreModal &&
            <ConfirmModal msg="Restaurer le recrutement ?"
                confirmAction={handleRestore}
                closeModal={() => toggleRestoreModal(false)}/>
        }
        <div className='action-container'>
            {
                (["rh", "resp_rh"].includes(auth.role) && !recrutement.soft_delete) &&
                (recrutement.status == "En cours") && 
                <span>
                    <Link to={"/recrutement/edit/" + recrutement.id}>Modifier</Link>
                </span>
            }
            {
                (["rh", "resp_rh"]).includes(auth.role) && recrutement.status != "Recruté" && !recrutement.soft_delete &&
                <span>
                    <Link to="/employe/add" state={{ recrutement, idsPj }}>Créer un employé</Link>
                </span>
            }
            {
                (["rh", "resp_rh"].includes(auth.role) && !recrutement.soft_delete) && recrutement.status != "Recruté" && 
                <span onClick={() => {toggleArchiveModal(true)}}>Mettre en archive</span>
            }
            {
                (["rh", "resp_rh"].includes(auth.role) && recrutement.soft_delete == 1) && 
                <span onClick={() => {toggleRestoreModal(true)}}>Restaurer</span>
            }
        </div>
    </div>
}