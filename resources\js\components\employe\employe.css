#notifEmpContainer{
    position: fixed;
    bottom: 10px;
    right: 10px;
    background-color: white;
    border: solid 1px #ccc;
    padding: 10px;
    width: 450px;
    max-width: 450px;
    min-width: 450px;
    z-index: 2000;
}
#notifEmpHeader{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#notifEmpContainer:hover{
    animation: none
}
#notifEmpContainer:hover #notificationEmpList {
    max-height: 400px;
    color: rgba(0, 0, 0, .8);
}
#notificationEmpList{
    margin: 0;
    padding: 0;
    display: block;
    list-style: none;
    line-height: 1em;
    overflow: hidden;
    max-height:0;
    transition: max-height 0.5s ease-in; 
}

#notificationEmpList > div {
    cursor: pointer;
}
#notificationEmpList > div:hover {
    color: rgba(0, 0, 0, .5);
}