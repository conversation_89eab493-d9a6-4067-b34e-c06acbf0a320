import React, { useEffect, useState } from 'react'
import InputSelect from '../input/InputSelect'
import InputAgent from '../input/InputAgent'
import { forEach } from 'lodash'
import moment from 'moment'
import LoadingPage from '../loading/LoadingPage'

export default function ReplaceAgentModal({plannings, closeModal}) {
    const [agentInPlannings, setAgentInPlannings] = useState(null)
    const [agentToReplace, setAgentToReplace] = useState(null)
    const [agentToAdd, setAgentToAdd] = useState(null)
    const [showButtonReplace, toggleButtonReplace] = useState(false)

    useEffect(() => {   
        if (agentToReplace && agentToAdd) {
            toggleButtonReplace(true)
        } else {
            toggleButtonReplace(false)
        }
    }, [agentToReplace, agentToAdd])
    
    useEffect(() => {    
        let agents = []
        plannings.forEach(pl => {
            if (moment(pl.service).isAfter(moment())) {
                pl.employes.forEach(e => {
                    if(!agents.find(a => a.id == e.id)) 
                        agents.push({
                            id: e.id, 
                            label:(e.matricule + " " + e.nom), 
                            matricule: e.matricule ,
                            nom: e.nom,
                            value: (e.id).toString()
                        })
                })
            }
        })
        setAgentInPlannings(agents)
    }, [plannings])

    const handleReplaceAgent = (agentToReplace, agentToAdd) => {
        plannings.forEach(p => {
            if (moment(p.service).isAfter(moment())) {
                forEach(p.employes, e => {
                    if (e.id == agentToReplace.id) {
                        e.matricule = agentToAdd.matricule
                        e.nom = agentToAdd.nom
                        e.id = agentToAdd.id
                    }
                })
            }
        });
        closeModal()
    }
    return (
        <div className='modal'>
            {
                !agentInPlannings ? 
                    <LoadingPage />
                :
                    <div>
                        <InputSelect label="Agent à remplacer" selected={agentToReplace} setSelected={setAgentToReplace} options={agentInPlannings} required/>
                        <InputAgent label="Nouvel agent" value={agentToAdd} onChange={setAgentToAdd} required/>
                        <div className='form-button-container' style={{marginTop: 70}}>
                            {
                                showButtonReplace && 
                                <button type='button' className='btn-primary' onClick={()=>handleReplaceAgent(agentToReplace, agentToAdd)}>Remplacer</button>
                            }
                            <button type='button' className='btn' onClick={closeModal}>Fermer</button>
                        </div>
                    </div>
            }
        </div>
    )
}
