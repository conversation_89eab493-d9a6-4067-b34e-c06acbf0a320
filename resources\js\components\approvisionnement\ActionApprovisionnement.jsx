import React, { useState } from 'react';
import NoteModal from '../input/NoteModal';
import { useParams } from 'react-router-dom';
import InputText from '../input/InputText';
import axios from 'axios';
import useToken from '../util/useToken';

export default function ActionApprovisionnement({auth, approvisionnement, updateData}) {
    const params = useParams()
    const [showNoteModal, toggleNoteModal] = useState(false)
    //const [showDoneModal, toggleDoneModal] = useState(false)
    const [currentAction, setAction] = useState(null)
    const [showEditModal, toggleEditModal] = useState(false)
    const [objet, setObjet] = useState(approvisionnement.objet)
    const [disableConfirm, setConfirmButton] = useState(false)
    const [error, setError] = useState("")

    const handleEditDA = (e) => {
        e.preventDefault()
        setConfirmButton(true)
        const data = {
            objet: objet
        }
        axios.post("/api/approvisionnement/edit/" + params.id, data, useToken())
        .then((res) => {
            if(res.data.success){
                toggleEditModal(false)
                updateData()
            }                
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
            setConfirmButton(false)
        })
        .catch((e) => {
            console.error(e)
            setConfirmButton(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    const handleSendBackDA = () => {
        setAction({
            header: "Renvoyer la DA",
            request: "/api/approvisionnement/send_back/" + approvisionnement.id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleCancelDA = () => {
        setAction({
            header: "Annuler la DA",
            request: "/api/approvisionnement/cancel_approvisionnement/" + approvisionnement.id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleReplyValidation = () => {
        setAction({
            header: "Réponse à la demande",
            request: "/api/approvisionnement/reply_validation/" + approvisionnement.id,
            required: false
        })
        toggleNoteModal(true)
    }

    const handleDoTraite = () => {
        setAction({
            header: "Traitement de la DA",
            request: "/api/approvisionnement/do_traite/" + approvisionnement.id,
            required: true
        })
        toggleNoteModal(true)
    }
    /*
    const handleDone = () => {
        setAction({
            header: "Terminer le traitement de la DA",
            request: "/api/approvisionnement/save_done/" + approvisionnement.id,
            required: true
        })
        toggleDoneModal(true)
    }
    */

    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction} 
                updateData={() => updateData(true)} 
                closeModal={() => toggleNoteModal(false)}/> 
        }
        {/*
            showDoneModal && 
            <DoneApproModal 
                action={currentAction} 
                updateData={() => updateData(true)} 
                closeModal={() => toggleDoneModal(false)}/> 
        */}
        {
            showEditModal && 
            <div className='modal'>
                <div>
                    <h3>Modification de la DA</h3>
                    <div className='input-container'>
                        <InputText value={objet} label="Motif" onChange={setObjet} required/>
                    </div>
                    {
                        error &&
                        <div className='danger'>
                            <br/>
                            <span>{error}</span>
                        </div>
                    }
                    <div className='form-button-container'>
                        <button disabled={disableConfirm} className='btn-primary' onClick={handleEditDA}>Envoyer</button>
                        <button onClick={() => toggleEditModal(false)}>Annuler</button>
                    </div>
                </div>
            </div>
        }
        <div className='action-container'>
            {
                (["demande"].includes(approvisionnement.status) && ["daf"].includes(auth.role)) && 
                <span onClick={handleDoTraite}>Accuser la réception</span>
            }
            {/*
                (
                    (["demande", "traite"].includes(approvisionnement.status) && ["daf"].includes(auth.role)) ||
                    (["traite"].includes(approvisionnement.status) && ["compta"].includes(auth.role))
                ) && 
                <span onClick={handleDone}>Terminer</span>
            */}
            {
                (params.id && ["draft"].includes(approvisionnement.status) && auth.id == approvisionnement.user_id && !["validateur"].includes(auth.role)) && 
                <span onClick={() => {toggleEditModal(true)}}>Editer</span>
            }
            {
                (["validation"].includes(approvisionnement.status) && ["validateur"].includes(auth.role)) &&
                <span onClick={handleReplyValidation}>Valider</span>
            }
            {
                ((['validation', 'demande', 'traite'].includes(approvisionnement.status) && ["validateur"].includes(auth.role)) || 
                (['validation'].includes(approvisionnement.status) && auth.id == approvisionnement.user_id )) &&
                <span onClick={handleCancelDA}>Annuler la demande</span>
            }
            {
                ( ["draft"].includes(approvisionnement.status) && auth.id == approvisionnement.user_id && !["validateur"].includes(auth.role)) && 
                <span onClick={() => {handleSendBackDA(true)}}>Renvoyer la demande</span>
            }
        </div>
    </div>
}