import {useState,useRef, useEffect} from 'react'

import useClickOutside from '../util/useClickOutside'
import axios from 'axios'
import useToken from '../util/useToken'

export default function InputAgence({currentSelect, setCurrentSelect,required}) {
    const [showSelect, toggleSelect] = useState(false)
    const [label, setLabel] = useState("")
    const [agences, setAgences] = useState([]);
    const selectRef = useRef(null)

    useClickOutside(selectRef, () => {
        toggleSelect(false)
        if(currentSelect && currentSelect.nom)
            setLabel(currentSelect.nom)
        else
            setLabel("")
    })

    useEffect(() => {
        setLabel(currentSelect ? currentSelect.nom: "")
    }, [currentSelect]);

    useEffect(() => {
        let isMounted = true
        axios.get("/api/agence", useToken())
        .then((res) => {
            if(isMounted){
                setAgences(res.data)
            }
        })
        return () => {isMounted = false}
    }, [])

    return (
        <div ref={selectRef} className='input-container'>
            <label>Agence {required && <span className='danger'>*</span>}</label>
            <input className='select-search' 
                onClick={() => toggleSelect(!showSelect)} 
                value={label} 
                onChange={(e) => {
                    setLabel(e.target.value)
                    if(!e.target.value) setCurrentSelect(null)
                }}/>
            <div className='input-select-relative'>
                {
                    showSelect && 
                    <div className='select-list'>
                        {
                            agences.filter(agc => (new RegExp(label.toLowerCase(), 'g')).test(agc.nom.toLowerCase()))
                            .slice(0, 5)
                            .map((agc, index) => (
                                (index == 4) ?
                                    <div 
                                        key={index}
                                        className="select-item"
                                    >
                                        ...
                                    </div>
                                :
                                <div 
                                    key={index}
                                    className="select-item"
                                    onClick={(e) => {
                                        setCurrentSelect(agc)
                                        toggleSelect(false)
                                        setLabel(agc.nom)
                                    }}
                                >
                                    {agc.nom}
                                </div>
                            ))
                        }
                    </div>
                }
            </div>
        </div>
    )
}
