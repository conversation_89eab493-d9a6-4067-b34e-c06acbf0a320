@if ($note != null)
<p style="white-space: pre-line;">
    {{$note}}
</p>
@endif
<h3>Demande de prime</h3>
<p style="white-space: pre-line;">
    Employe: {{$employe}}<br/>
    Site : {{$prime->site}} <br/>
    Date du service : {{$prime->date_service}} <br/>

    @if($prime->objet != null)
        Objet : {{$prime->objet}} <br/>
    @endif
    @if($prime->montant > 0)
        Montant : {{$prime->montant}} Ar<br/>
    @endif
    Motif: {{$prime->motif}} <br/>
    @if($prime->superviseur_id != $prime->user_id)
        Superviseur responsable: {{$prime->sup_nom . ' <' . $prime->sup_email . '>'}} <br/>
    @endif
    Demandeur: {{$prime->user_nom . ' <' . $prime->user_email . '>'}} <br/>
</p>

@if(count($last_primes) > 0)
    <h4>Prime déjà reçu par l'employe</h4>
@endif

@foreach ($last_primes as $pr)
    <b>{{$pr->objet}}</b><br/>
    Motif: {{$pr->motif}}<br/>
    Date du service: {{$pr->date_service}}<br/>
    <br/>
@endforeach
<br/>
<br/>
<a href={{env('APP_URL'). "/prime/show/". $prime->id}}>Cliquez ici pour voir les détails ou répondre</a>