import React from 'react'
import InputSelect from '../input/InputSelect'
import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'

export default function Payement({ payement, setPayement, banques }) {
    return (
        <div>
            <InputSelect label={"Mode de paiement"}
                selected={payement.type}
                setSelected={(value) => setPayement({ ...payement, type: value })}
                options={["Virement bancaire", "Mobile money"]}
                required
            /> 
            {payement.type == "Mobile money" ?
                    <InputText required label={"Numero de téléphone"}
                        value={payement.numeroTelephone}
                        onChange={(value) => setPayement({ ...payement, numeroTelephone: value })}
                    />
                :
                    payement.type == "Virement bancaire" ?
                            <InputSelect label="Banque"
                                selected={payement.banque}
                                required setSelected={(value) => setPayement({ ...payement, banque: value })}
                                options={banques}
                            />
                        :
                            null
            }
           
            {payement.type == "Virement bancaire" &&
                <>
                    <DualContainer>
                        <InputText label="Numéro de compte"
                            value={payement.numeroCompte}
                            required
                            onChange={(value) => setPayement({ ...payement, numeroCompte: value })}
                        />
                        <InputText label="Code Banque"
                            value={payement.codeBanque}
                            onChange={(value) => setPayement({ ...payement, codeBanque: value })}
                            required
                        />  
                    </DualContainer>
                    <DualContainer>
                        <InputText label="Code Guichet"
                            value={payement.codeGuichet}
                            required
                            onChange={(value) => setPayement({ ...payement, codeGuichet: value })}
                        />
                        <InputText label="RIB" value={payement.rib} required onChange={(value) => setPayement({ ...payement, rib: value })}/>
                    </DualContainer>
                </>
            }  
            <InputText label="Numéro CIN" value={payement.cin} required onChange={(value) => setPayement({ ...payement, cin: value })}/>
        </div>
                

  )
}
