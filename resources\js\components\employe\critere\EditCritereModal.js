import React, { useEffect, useState } from 'react';
import InputText from '../../input/InputText';

export default function EditCritereModal({confirmEdit, currentCritere, closeModal}) {
    const [designation, setDesignation] = useState("")
    const [montant, setMontant] = useState(0)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(true)

    const handleSubmit = (e) => {
        e.preventDefault()
        confirmEdit(designation, montant)
    }

    const handleCancel = (e) => {
        closeModal()
    }

    useEffect(() => {
        if(currentCritere){
            setDesignation(currentCritere.designation)
            setMontant(currentCritere.montant)
        }
    }, [])

    useEffect(() => {
        disableSubmit(!(designation && designation.trim() && montant))

    }, [designation, montant]);

    return (
        <div className='modal'>
            <div style={{maxHeight: 'none'}}>
                <div className="title-container" >
                    <h2>{currentCritere ? "Modifer le critère" : "Ajouter un critère"}</h2>
                </div>
                <div>
                    <div className='form-body'>
                        <InputText required
                            label="Désignation"
                            value={designation}
                            onChange={setDesignation}/>
                        <InputText required
                            type="number"
                            label="Montant"
                            value={montant}
                            onChange={setMontant}/>
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                        }
                    </div>
                    <div className="form-button-container">
                        <button className='primary' disabled={submitDisabled} onClick={handleSubmit}> 
                            {(currentCritere && currentCritere.index) ? "Modifier" : "Ajouter" }
                        </button>
                        <button className='secondary' onClick={handleCancel}> Annuler </button>
                    </div>
                </div>
            </div>
        </div>
    )
}
