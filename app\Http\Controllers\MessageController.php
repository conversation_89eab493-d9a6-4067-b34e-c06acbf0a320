<?php

namespace App\Http\Controllers;

use App\Http\Util\EmployeUtil;
use App\Models\PieceJointe;
use App\Models\Message;
use App\Models\NoteMessage;
use App\Models\Planning;
use App\Models\PlanningPointage;
use App\Models\Seen;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Svg\Tag\Rect;

class MessageController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }
    public static function search(Request $request){
        $searchArray = [];
        if($request->id) 
            $searchArray[] = " ms.id = ".$request->id. " ";
        else {
            if($request->user_id){
                $searchArray[] = " u.id = " . $request->user_id;
            }
            if($request->service_id){
                $searchArray[] = " u.service_id = " . $request->service_id;
            }
            if($request->objet){
                $searchArray[] = " ms.objet like '%".$request->objet . "%' ";
            }
            if ($request->created_at) {
                $searchArray[] =  " ms.created_at > '$request->created_at 00:00:00' and ms.created_at <= '$request->created_at 23:59:59' ";
            }
            if ($request->unread) {
                if ($request->sent)
                    $searchArray[] = " (n.seen is null or n.seen = 0)";
                else
                    $searchArray[] = " (n.seen is null or n.seen = 0) ";
            }
            if ($request->to_do) {
                if (!$request->sent) {
                    $searchArray[] = " (n.to_do = 1) ";	
                }
            }
            if ($request->message_content) {
                // $message_content = explode(" ", $request->message_content); 
                // $searchArray[] = " ms.content like \"%" . implode("%\" and content like \"%", $message_content) . "%\" ";
                $searchArray[] = " REGEXP_REPLACE(REGEXP_REPLACE(content, '<[^>]+>', ' ', 1, 0, 'n'), '[\r\n]+', ' ') LIKE \"%".$request->message_content."%\"";
            }
        }
        $query_where = "";
        $query_and = "";
        if ($request->offset || $request->offset > -1) {
            $orderBy = " order by ms.id DESC limit " . $request->offset . ", 30";
        } else {
            $orderBy = " order by ms.id DESC";
        }
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . "";
        }
        $query_where = $query_where . " " . $orderBy;
        $query_and = $query_and . " " . $orderBy;
        return compact("query_where", "query_and");
    }

    public function seen_all(Request $request){
        $auth = $request->user();
        if ($request->user()) {
            $messages = DB::select("SELECT n.id FROM note_messages n
                LEFT JOIN messages ms on ms.id = n.message_id
                LEFT JOIN users u on u.id = ms.user_id
                WHERE n.user_id = ? and n.id <= ?
            ". ($this->search($request))['query_and'], [$request->user()->id, $request->last_id]);
            NoteMessage::whereIn("id", array_column($messages, "id"))->update(["seen" => 1]);
            return response(['success' => "Message lu"]);
        }
        return response(['error' => "Erreur inatendue"]);
    }

    public function unfollow_all(Request $request) {
        if ($request->user()) {
            $messages = DB::table('note_messages as note')
                ->leftJoin('messages as ms', 'ms.id', '=', 'note.message_id')
                ->leftJoin('users as u', 'u.id', '=', 'ms.user_id')
                ->where('ms.user_id', $request->user()->id)
                ->where('note.follow', 1)
                ->where(function ($query) {
                    $query->where('note.seen', 0)
                        ->orWhereNull('note.seen');
                })
                ->update(["follow"=> null]);
            return (["success" => "Message non suivi"]);
        }
        return response()->json(['error' => "Erreur inatendue"]);
    }
    
    public function unfollow_all_by_user(Request $request) {
        if ($request->user()) {
            $messages = DB::table('note_messages as note')
                ->leftJoin('messages as ms', 'ms.id', '=', 'note.message_id')
                ->leftJoin('users as u', 'u.id', '=', 'ms.user_id')
                ->where('ms.user_id', $request->user()->id)
                ->where('note.user_id', $request->user_id)
                ->where('note.follow', 1)
                ->where(function ($query) {
                    $query->where('note.seen', 0)
                        ->orWhereNull('note.seen');
                })
                ->update(["follow"=> null]);
            return (["success" => "Message non suivi"]);
        }
        return response()->json(['error' => "Erreur inatendue"]);
    }

    public function handle_to_do(Request $request){
        $auth = $request->user();
        if ($request->user()) {
            $note = NoteMessage::where('id', $request->note_id)->where('user_id', $request->user()->id)->first();
            if ($note->to_do)
                $note->to_do = null;
            else 
                $note->to_do = 1;
            $note->save();
            return response(['success' => "Message a revoir"]);
        }
        return response(['error' => "Erreur inatendue"]);
    }

    public function index(Request $request){
        if($request->sent){
            if($request->unread){
                $messages = DB::select("SELECT n.id, ms.objet, n.seen, n.created_at, u.name, u.email, n.follow
                    FROM note_messages n
                    LEFT JOIN messages ms on ms.id = n.message_id
                    LEFT JOIN users u on u.id = n.user_id
                    WHERE ms.user_id = ? and n.follow = 1
                ". ($this->search($request))['query_and'], [$request->user()->id]);
            }
            else {
                $messages = DB::select("SELECT ms.id, ms.objet, ms.created_at, u.name, u.email
                    FROM messages ms
                    LEFT JOIN users u on u.id = ms.user_id
                    WHERE ms.user_id = ?
                ". ($this->search($request))['query_and'], [$request->user()->id]);
                if(count($messages) > 0){
                    $notes = DB::select("SELECT n.id, n.message_id, u.name, u.email
                        FROM note_messages n 
                        LEFT JOIN users u ON u.id = n.user_id
                        WHERE n.message_id in (" . implode(",", array_column($messages, "id")) . ")");
                    foreach ($messages as $m) {
                        $m->notes = [];
                        foreach ($notes as $n) {
                            if($n->message_id == $m->id)
                                $m->notes[] = $n;
                        }
                    }
                }
            }
        }
        else {
            $messages = DB::select("SELECT n.id, n.message_id, ms.objet, n.seen, n.created_at, u.name, u.email, n.replied, n.to_do
                FROM note_messages n
                LEFT JOIN messages ms on ms.id = n.message_id
                LEFT JOIN users u on u.id = ms.user_id
                WHERE n.user_id = ?
            ". ($this->search($request))['query_and'], [$request->user()->id]);
        }
        
        if (count($messages) > 0) {
            $pieces = DB::select("SELECT id, message_id FROM piece_jointes WHERE message_id in (" . implode(",", array_column($messages, "id")) . ")");
            foreach ($messages as $ms) {
                $ms->nb_pj = 0;
                foreach ($pieces as $pj) {
                    if ($ms->id == $pj->message_id)
                        $ms->nb_pj += 1;
                }
            }
        }
        return response()->json(compact('messages'));
    }

    public function show(Request $request, $id){
        $messages = [];
        $copies = [];
        if($request->sent){
            if($request->unread){
                $note = DB::select("SELECT n.id, n.message_id, ms.parent_id, ms.objet, ms.content, n.created_at, n.user_id, 
                    u.name, u.email, ms.user_id as 'sender_id', us.name as 'sender_name', us.email as 'sender_email',  n.follow, n.seen
                    FROM note_messages n
                    LEFT JOIN messages ms on ms.id = n.message_id
                    LEFT JOIN users u on u.id = n.user_id
                    LEFT JOIN users us on us.id = ms.user_id
                    WHERE n.id = ?", [$id])[0];
                $copies = DB::select("SELECT u.id as user_id, u.name as 'user_name', u.email as 'user_email', n.follow
                    FROM note_messages n
                    LEFT JOIN users u on u.id = n.user_id
                    WHERE n.message_id = ? AND u.id <> ?
                ", [$note->message_id, $request->user()->id]);
                $receivers = DB::select("SELECT n.id, n.follow, u.id as 'user_id', u.name, u.email FROM note_messages n 
                    LEFT JOIN users u ON u.id = n.user_id 
                    WHERE n.message_id = ?", [$note->message_id]);
            }
            else {
                $note = DB::select("SELECT ms.id, ms.parent_id, ms.objet, ms.content, ms.created_at, ms.user_id, 
                    u.name, u.email
                    FROM messages ms
                    LEFT JOIN users u on u.id = ms.user_id
                    WHERE ms.id = ?", [$id])[0];
                $note->notes = DB::select("SELECT n.id, n.message_id, u.name, u.email 
                    FROM note_messages n
                        LEFT JOIN users u ON u.id = n.user_id
                    WHERE n.message_id = ?", [$id]);
                $copies = DB::select("SELECT u.id as user_id, u.name as 'user_name', u.email as 'user_email', n.follow
                    FROM note_messages n
                    LEFT JOIN users u on u.id = n.user_id
                    WHERE n.message_id = ? AND u.id <> ?
                ", [$note->id, $request->user()->id]);
                $receivers = DB::select("SELECT n.id, n.follow, u.id as 'user_id', u.name, u.email FROM note_messages n 
                    LEFT JOIN users u ON u.id = n.user_id 
                    WHERE n.message_id = ?", [$note->id]);
            }
        }
        else {
            NoteMessage::where("id", $id)->update(["seen" => 1]);
            $note = DB::select("SELECT n.id, n.message_id, ms.parent_id, ms.objet, ms.content, ms.created_at, ms.user_id, 
                u.name, u.email, n.seen, n.follow, n.replied, n.to_do
                FROM note_messages n
                LEFT JOIN messages ms on ms.id = n.message_id
                LEFT JOIN users u on u.id = ms.user_id
                WHERE n.id = ?", [$id])[0];

            $copies = DB::select("SELECT u.id as user_id, u.name as 'user_name', u.email as 'user_email', n.follow
                FROM note_messages n
                LEFT JOIN users u on u.id = n.user_id
                WHERE n.message_id = ? AND u.id <> ?
            ", [$note->message_id, $request->user()->id]);
            $receivers = DB::select("SELECT n.id, n.follow, u.id as 'user_id', u.name, u.email FROM note_messages n 
                LEFT JOIN users u ON u.id = n.user_id 
                WHERE n.message_id = ?", [$note->message_id]);
        }

        $note->to = [];
        $note->cc = [];
        foreach ($receivers as $u) {
            if($u->follow)
                $note->to[] = $u;
            else
                $note->cc[] = $u;
        }
        
        if($request->sent && !$request->unread) // !$request->unread
            $message_id = $note->id;
        else
            $message_id = $note->message_id;
        $reply_all = NoteMessage::select('id', 'user_id')
            ->where('message_id', $message_id) //note->message_id
            ->where('user_id', '<>', $note->user_id)
            ->where('user_id', '<>', $request->user()->id)
            ->get();
        if (count($reply_all)) {
            $note->reply_all = true;
        }
        
        $pj = PieceJointe::select('id', 'nature', 'path')->where('message_id', $message_id)->get(); //note->message_id
        $note->pieces = $pj;

        $parent_id = $note->parent_id;
        

        while($parent_id) {
            $message = DB::select("SELECT ms.id, ms.user_id, ms.parent_id, ms.objet, ms.content, ms.created_at, u.name, u.email
                FROM messages ms
                LEFT JOIN users u on u.id = ms.user_id
                WHERE ms.id = ?", [$parent_id])[0]; //$request->user()->id, 
            $pj = PieceJointe::select('id', 'nature', 'path')->where('message_id', $parent_id)->get();
            $message->pieces = $pj;
            $messages[] = $message;
            $parent_id = $message->parent_id;
        }
        
        return response()->json(compact('note', 'messages', 'copies'));
    }

    public function search_unread(Request $request){
        $searchArray = [];
        $query_where = "";
        $query_and = "";
        if($request->user_id){
            $searchArray[] = " u.id = " . $request->user_id;
        }
        if($request->service_id){
            $searchArray[] = " u.service_id = " . $request->service_id;
        }
        if($request->objet){
            $searchArray[] = " ms.objet like '%".$request->objet . "%' ";
        }
        if ($request->created_at) {
            $searchArray[] =  " ms.created_at > '$request->created_at 00:00:00' and ms.created_at <= '$request->created_at 23:59:59' ";
        }
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . "";
        }
        return compact("query_where", "query_and");
    }
    public function index_sent_unread(Request $request){
        $auth = $request->user();
        if($auth->role == "admin") {
            $messages = DB::select("SELECT COUNT(n.id) AS nb,  MAX(n.id) AS max_note_id,  u.name, u.email, 
                u.id AS user_id, u.id as 'id'
                FROM  note_messages n
                LEFT JOIN  messages ms ON ms.id = n.message_id
                LEFT JOIN  users u ON u.id = n.user_id
                WHERE (n.seen IS NULL OR n.seen = 0) and n.follow=1 ". ($this->search_unread($request))['query_and'] ."
                GROUP BY  u.id  
                ORDER BY 
                    CASE WHEN u.id = 221 THEN 0 ELSE 1 END, max_note_id DESC
                LIMIT ?, 30", [$request->offset]);
        }
        else{
            $messages = DB::select("SELECT 
                COUNT(n.id) AS nb,  MAX(n.id) AS max_note_id,  u.name, 
                u.email,  u.id AS user_id, u.id as 'id'
                FROM  note_messages n
                LEFT JOIN  messages ms ON ms.id = n.message_id
                LEFT JOIN  users u ON u.id = n.user_id
                WHERE  ms.user_id = ? AND (n.seen IS NULL OR n.seen = 0) AND n.follow=1". ($this->search_unread($request))['query_and'] ."
                GROUP BY  u.id
                ORDER BY max_note_id DESC
                LIMIT ?, 30", [$auth->id, $request->offset]);
        }
        if(count($messages) > 0){
            $init_messages = DB::select("SELECT ms.id, ms.objet, n.created_at, n.id as 'note_id' FROM messages ms
                LEFT JOIN note_messages n ON n.message_id = ms.id
                WHERE n.id IN (" . implode(",", array_column($messages, "max_note_id")) . ")");
            foreach ($messages as $note) {
                foreach ($init_messages as $ms) {
                    if($ms->note_id == $note->max_note_id) {
                        $note->objet = $ms->objet;
                        $note->ms_id = $ms->id;
                        $note->created_at = $ms->created_at;
                    }
                }
            }
        }
        return response()->json(compact('messages'));
    }

    public function show_sent_unread(Request $request, $id){
        $auth = $request->user();
        if ($auth->role == "admin") {
            $messages = DB::select("SELECT n.id, u.name, u.email, u.id AS user_id, ms.created_at, ms.content, ms.objet,
                u2.name as 'sender', u2.email as 'sender_email', u2.id as 'sender_id', n.message_id
                FROM  note_messages n
                LEFT JOIN messages ms ON ms.id = n.message_id
                LEFT JOIN users u ON u.id = n.user_id
                LEFT JOIN users u2 ON u2.id = ms.user_id
                WHERE (n.seen IS NULL OR n.seen = 0) AND n.follow=1 ". ($this->search_unread($request))['query_and'] ."
                AND n.user_id = ?
                ORDER BY n.id DESC", [$id]);
        }
        else {
            $messages = DB::select("SELECT 
                n.id, u.name, u.email,  u.id AS user_id, ms.created_at, ms.content, ms.objet, n.message_id
                FROM  note_messages n
                LEFT JOIN messages ms ON ms.id = n.message_id
                LEFT JOIN users u ON u.id = n.user_id
                WHERE  ms.user_id = ? AND (n.seen IS NULL OR n.seen = 0) AND n.follow=1 ". ($this->search_unread($request))['query_and'] ."
                AND n.user_id = ?
                ORDER BY n.id DESC", [$request->user()->id, $id]);
        }
        $pjs = DB::select("SELECT pj.id, pj.message_id, pj.path, pj.nature, pj.user_id, pj.created_at
                FROM piece_jointes pj
                WHERE pj.message_id in (" . implode(",", array_column($messages, "message_id")) . ")");
            foreach ($messages as $ms) {
                $ms->notes = [];
                foreach ($pjs as $pj) {
                    if($pj->message_id == $ms->message_id)
                        $ms->pieces[] = $pj;
                }
            }
            return response()->json(compact('messages'));
    }

    protected function validateAndSetMessage($request, $message){
        $message->objet = $request->objet;
        $message->content = $request->content;
        $receivers = [];
        if($request->receivers){
            $hasRapport = false;
            foreach($request->receivers as $id){
                if($id != $request->user()->id && $id != 191){
                    $receivers[] = $id;
                }
                if($id == 191){
                    $hasRapport = true;
                }
            }
            if(count($receivers) > 0)
                $request->receivers = $receivers;
            else if($hasRapport)
                return ["error" => "\"Rapport\" n'existe pas comme destinataire, veuillez mettre une autre."];
            else
                return ["error" => "Veuillez ajouter au moins un destinataire."];

            if($request->copies){
                $copies = [];
                foreach($request->copies as $id){
                    if($id != $request->user()->id){
                        $inDestinataire = false;
                        foreach ($request->receivers as $rec_id) {
                            if ($id == $rec_id) {
                                $inDestinataire = true;
                            }
                        }
                        if (!$inDestinataire) {
                            $copies[] = $id;
                        }
                    }
                }
                $request->copies = $copies;
            }
        }
        if ($request->fait_marquant_id || 
            $request->approvisionnement_id || 
            $request->absence_id ||
            $request->sanction_id ||
            $request->sav_id ||
            $request->flotte_id ||
            $request->juridique_id ||
            $request->reclamation_id ||
            $request->service24_id ||
            $request->part_variable_id ||
            $request->equipement_id ||
            $request->visite_poste_id ||
            $request->deduction_id ||
            $request->prime_id ||
            $request->paie_id ||
            $request->avance_id ||
            $request->employe_id ||
            $request->satisfaction_id
        ) {
            $validator = Validator::make($request->all(), [
                'receivers' => ["required"],
                'content' => ["required"],
            ], ["content.required" => "Le contenu est requis.", "receivers.required" => "Veuillez ajouter au moins un destinataire."]);
        }
        else if($request->has('emails') && count($request->emails) > 0){
            $validator = Validator::make($request->all(), [
                'objet' => ["required"],
                'content' => ["required"],
            ], ["content.required" => "Le contenu est requis.", "receivers.required" => "Veuillez ajouter au moins un destinataire."]);
        }
        else {
            $validator = Validator::make($request->all(), [
                'receivers' => ["required"],
                'objet' => ["required"],
                'content' => ["required"],
            ], ["content.required" => "Le contenu est requis.", "receivers.required" => "Veuillez ajouter au moins un destinataire."]);
        }
        if ($validator->fails()) {
            return ['error' => $validator->errors()->first()];
        }
        return ['error' => ''];
    }

    public function store(Request $request){
        $message = new Message();
        $accent = [
            'À', 'Â', 'Ä', 'É', 'È', 'Ê', 'Ë', 'Î', 'Ï', 'Ô', 'Ö', 'Ù', 'Û', 'Ü', 'Ÿ', 'Ç', 'Ñ', 'Æ', 'Œ',
            'à', 'â', 'ä', 'é', 'è', 'ê', 'ë', 'î', 'ï', 'ô', 'ö', 'ù', 'û', 'ü', 'ÿ', 'ç', 'ñ', 'æ', 'œ',
            'Ð', 'ð', 'Ø', 'ø', 'Å', 'å'
        ];
        
        $auth = $request->user();
        $validator = $this->validateAndSetMessage($request, $message);
        if ($validator['error']) {
            return response(['error' => $validator['error']]);
        }
        if ($request->reply) {
            // $note = NoteMessage::find($request->note_id);
            $old_message = Message::find($request->message_id);
            if ($old_message != null) {
                $note = NoteMessage::where('message_id', $old_message->id)->where('user_id', $auth->id)->first();
                if ($note != null) {
                    $note->replied = 1;
                    $note->seen = 1;
                    $note->save();
                    // if ($note->message_id) {
                    //     $message->parent_id = $note->message_id;
                    // }
                }
                $message->parent_id = $old_message->id;
            }
        } 
        else if ($request->fait_marquant_id) {
            $fait_message = $this->defaultFaitMessage($request, $request->fait_marquant_id);
            $respData = json_decode($fait_message->getContent(), true);
            $message->objet = substr($respData['objet'], 0, 100);

            $message->content = $message->content . $respData['content'];

            $seen = Seen::where('fait_marquant_id', $request->fait_marquant_id)->where('user_id', $request->user()->id)->first();
            if($seen == null){
                $seen = new Seen();
                $seen->fait_marquant_id = $request->fait_marquant_id;
                $seen->user_id = $request->user()->id;
                $seen->created_at = new \DateTime();
            }
            $seen->send_email = true;
            $seen->save();
        }
        else {
            $default_message = $this->defaultMessage($request);
            $message->objet = self::remove_accented_ending(substr($default_message['objet'], 0, 100));
            $message->content = $message->content . $default_message['content'];
        }
        $message->user_id = $request->user()->id;
        $message->created_at = new \DateTime();
        $message->updated_at = new \DateTime();
        if($message->save()){
            if($request->has('emails') && count($request->emails) > 0){
                $users = User::select('id')->whereIn("type", ["unique", "fictif"])->whereIn('email', $request->emails)->get();
                $request->receivers = [];
                foreach ($users as $u) {
                    $request->receivers[] = $u->id;
                }
            }
            $real_received_ids = [];
            $receivers = DB::select("SELECT id, `type` from users 
                where id != 191 and (blocked is null or blocked = 0) and id in (" . implode(",", $request->receivers) .")
                order by niveau_hierarchie asc");
            $parent_ids = [];
            foreach ($receivers as $r) {
                if($r->type == "parent")
                    $parent_ids[] = $r->id;
                else
                    $real_received_ids[] = $r->id;
            }
            if(count($parent_ids) > 0){
                $receiver_chidren = DB::select("SELECT id from users 
                    where id != 191 and (blocked is null or blocked = 0) and id in (" . implode(",", $parent_ids) ."
                    order by niveau_hierarchie asc)");
                foreach ($receiver_chidren as $r) {
                    $real_received_ids[] = $r->id;
                }
            }
            if(count($real_received_ids) == 0)
                return response(['error' => ['blocked' => 'Erreur, destinataire(s) bloqué']]);

            foreach($real_received_ids as $id){
                $note = new NoteMessage();
                $note->message_id = $message->id;
                $note->user_id = $id;
                $note->follow = 1;
                $note->created_at = new \DateTime();
                $note->updated_at = new \DateTime();
                $note->save();
            }
            if($request->copies){
                foreach($request->copies as $id){
                    if(!in_array($id, $real_received_ids)){
                        $note = new NoteMessage();
                        $note->message_id = $message->id;
                        $note->user_id = $id;
                        $note->created_at = new \DateTime();
                        $note->updated_at = new \DateTime();
                        $note->save();
                    }
                }
            }
            if($request->file('files')){
                $nb_pj = 0;
                $all_pj_name = '';
                foreach ($request->file('files') as $key => $file) {
                    $original_name = $file->getClientOriginalName();
                    $file_name = $original_name;
                    $file->storeAs('uploads/messages/' . $message->id, $file_name, 'public');
                    $piece_jointe = new PieceJointe();
                    $piece_jointe->path = "messages/" . $message->id . "/" . $file_name;
                    $piece_jointe->nature = $original_name;
                    $piece_jointe->user_id = $request->user()->id;
                    $piece_jointe->created_at = new \DateTime();
                    $piece_jointe->message_id = $message->id;
                    $piece_jointe->save();
                    $all_pj_name = $all_pj_name . $original_name .'<' .$file_name .'>' . '\n'; 
                    $nb_pj += 1;
                }
            }
            return response(['success' => "Message enregisté"]);
        }
    }

    public function seen(Request $request, $id){
        $message = Message::find($id);
        $auth = $request->user();
        if ($request->user()) {
            $note = NoteMessage::where('user_id', $auth->id)->where("message_id", $id)->first();
            if ($note != null && $message->user_id != $auth->id) {
                $note->seen=1;
                $note->save();
                return response(['success' => "Message lu"]);
            }

            return response(['error' => "Message erreur"]);
        }
        return response(['error' => "Erreur inatendue"]);
    }

    public static function replace_chevron($text){
        return mb_convert_encoding(str_replace(['<', '>'], ['&lt;', '&gt;'], $text), "UTF-8", "UTF-8");
    }

    public static function remove_accented_ending($string) {
        $accentedChars = 'ÀÂÄÉÈÊËÎÏÔÖÙÛÜŸÇÑÆŒàâäéèêëîïôöùûüÿçñæœÐðØøÅå';
        while (mb_strlen($string, 'UTF-8') > 0) {
            $lastChar = mb_substr($string, -1, 1, 'UTF-8');
            if (mb_strpos($accentedChars, $lastChar) === false) {
                break;
            }
            $string = mb_substr($string, 0, -1, 'UTF-8');
        }
        return $string;
    }

    protected static function defaultFaitMessage(Request $request, $id){
        $fait = DB::select("SELECT st.nom as 'site',fait.id, fait.objet as 'title', fait.commentaire as 'content', 
            fait.created_at, fait.user_id, us.name as 'user_nom', us.email as 'user_email'
            FROM fait_marquants fait
            LEFT JOIN sites st ON fait.site_id = st.idsite
            LEFT JOIN users us ON fait.user_id = us.id
            WHERE fait.id = ?
        ",[$id])[0];
        $objet = 'Fait Marquant : ' . $fait->site;
        $content = '<p><h3>Fait Marquant</h3> </p>';
        $content = $content . '<p><b>Site : </b>' . $fait->site . '</p>';
        $content = $content . '<p><b>Objet : </b>' . self::replace_chevron($fait->title) . '</p>';
        $content = $content . '<p><b>Commentaire : </b>' . self::replace_chevron($fait->content) .'</p>';
        $content = $content . '<p><b>Superviseur : </b>' . $fait->user_nom . ' &lt;'. $fait->user_email .'&gt;'. '</p>';
        return response()->json(compact( 'objet', 'content'));
    }

    protected static function defaultMessage(Request $request){
        $objet = '';
        $content = '';
        if ($request->approvisionnement_id) {
            $approvisionnement = DB::select("SELECT appro.id, appro.objet, appro.user_id, appro.created_at, appro.updated_at,
                us.name as 'user_nom', appro.created_at, appro.reference
                FROM approvisionnements appro
                LEFT JOIN users us on us.id = appro.user_id
                where appro.id = ?", [$request->approvisionnement_id])[0];
            $approvisionnement->year = (\DateTime::createFromFormat("Y-m-d H:i:s", $approvisionnement->created_at))->format("Y");
            $approvisionnement->reference = str_pad($approvisionnement->reference, 6, '0', STR_PAD_LEFT);
            $da_items = DB::select("SELECT da.id, da.designation, da.unite, da.quantite, da.prix, da.created_at, da.updated_at, da.price_only
                FROM da_items da
                WHERE da.approvisionnement_id = ?
                order by da.id desc", [$request->approvisionnement_id]);
            $content = $content . "<p><b>Motif : </b>" . self::replace_chevron($approvisionnement->objet) . "</p>";
            $content = $content . "<p><b>Demandeur : </b>". $approvisionnement->user_nom ."</p>";
            $objet = 'DA : ' . self::replace_chevron(substr($approvisionnement->objet, 0, 100));
            $table = '';
            $count_price_only = 0;
            foreach ($da_items as $item) {
                if ($item->price_only == 1) {
                    $count_price_only++;
                }
            }
            $header = "<thead>
                            <tr> 
                                <th>Article</th>
                                <th>Prix</th>
                                <th>Quantité</th>
                                <th>Prix unitaire</th>
                            </tr>
                        </thead>";
            $body ='';
            $total = 0;
            if (!empty($da_items) && array_reduce($da_items, function ($carry, $item) {
                return $carry && isset($item->prix);
            }, true)) {
                $total = array_reduce($da_items, function ($carry, $item) {
                    return $carry + ($item->quantite * $item->prix);
                }, 0);
            }
            $total = number_format($total, 0, ',', '.');
            if (count($da_items) > 0) {
                $body = "<tbody>";
                foreach ($da_items as $item) {   
                    $body = $body . "<tr>";
                    $body = $body . "<td>" . self::replace_chevron($item->designation) . "</td>";

                    if ($item->price_only)
                        $body = $body . "<td>" . mb_convert_encoding(number_format($item->prix, 0, ',', '.'), 'UTF-8', 'UTF-8') ."</td>". "<td></td><td></td>";

                    else {
                        $body = $body . "<td></td>". "<td>". mb_convert_encoding($item->quantite . $item->unite, 'UTF-8', 'UTF-8') . "</td>";

                        if ($item->prix)
                            $body = $body . "<td>" . number_format($item->prix, 0, ',', '.') . "</td>";
                        else 
                            $body = $body . "<td></td>";
                    }

                    $body = $body . "</tr>";
                }
                $body = $body . "</tbody>";
                if ($total > 0) {
                    $body = $body . "<tfoot><tr><td colspan=\"4\" style='text-align:right;'><b>Sous-Total : " . $total . "</b></td></tr></tfoot>";
                }
            }
            $table = "<table style='border-collapse: collapse;' border='1'>" . $header . $body . "</table>";
            $content = $content . $table;
        }

        else if ($request->absence_id) {
            $absence = DB::select("SELECT cg.id, cg.employe_id, cg.depart, cg.retour, cg.motif, cg.status, cg.created_at,
                t.designation as 'type', cg.site_id, st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', cg.user_id,
                a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi
                FROM absences cg
                LEFT JOIN type_absences t ON t.name = cg.type_absence
                LEFT JOIN employes a ON a.id = cg.employe_id
                LEFT JOIN sites st ON st.idsite = cg.site_id
                LEFT JOIN users us on us.id = cg.user_id
                LEFT JOIN `status` stat on stat.name = cg.status
                WHERE cg.id = ?", [$request->absence_id])[0];
            $employe = EmployeUtil::getEmploye($absence);
            $objet = $absence->type . ' : ' . $employe;
            $content = $content . '<p><b>' . $employe . '</b></p>';
            
            $depart = new \DateTime($absence->depart);
            if ($depart->format('H:i:s') == "18:00:00")
                $depart = $depart->format('d/m/Y') . ' NUIT';
            else
                $depart = $depart->format('d/m/Y') . ' JOUR';

            $retour = new \DateTime($absence->retour);
            if ($retour->format('H:i:s') == "18:00:00")
                $retour = $retour->format('d/m/Y') . ' NUIT';
            else
                $retour = $retour->format('d/m/Y') . ' JOUR';
            $content = $content . 
                '<p><b>Depart : </b>' . $depart . '<p/>' .
                '<b>Retour : </b>'. $retour . '' .
                '<p><b>Motif : </b>' . self::replace_chevron($absence->motif) . '</p>' .
                '<p><b>Ref : </b>' . $absence->id . '</p>';
            $content = $content . "<p><b>Demandeur : </b>" . $absence->user_nom . " &lt;" . $absence->user_email . "&gt;" . "</p>";
        }
        else if ($request->avance_id) {
            $avanceController = new AvanceController();
            $avance = $avanceController->show($request, $request->avance_id);
            $avance = json_decode($avance->getContent());
            if ($avance) {
                $employe = EmployeUtil::getEmploye($avance);
                $objet = 'Avance : '. $employe;
                $content = $content . '<p><b>'. $employe . '</b></p>';
                $date_paie = new \DateTime($avance->date_paie);
                $content = $content . "<p><b>Montant : </b>". number_format($avance->montant, 0, ',', '.') . " Ar" . "</p>";
                $content = $content . "<p><b>Type : </b>". $avance->type_description . "</p>";
                $content = $content . "<p><b>Date paie : </b>". $date_paie->format('m-Y') . "</p>";
                if ($avance->motif) {
                    $content = $content . "<p><b>Motif : </b>". self::replace_chevron($avance->motif) . "</p>";
                }
                $content = $content . "<p><b>Demandeur : </b>". $avance->user_nom . " &lt;" . $avance->user_email . "&gt;" ."</p>";
            }
        }
        else if ($request->service24_id){
            $serviceController = new Service24Controller();
            $service = $serviceController->show($request, $request->service24_id);
            $service = json_decode($service->getContent());
            if ($service) {
                $employe = EmployeUtil::getEmploye($service);
                $objet = 'Service 24 : ' . $employe;
                $content = $content . '<p><b>'. $employe . '</b></p>';
                $begin_pointage = new \DateTime($service->begin_pointage);
                if ($begin_pointage->format('H:i:s') == "18:00:00")
                    $begin_pointage = $begin_pointage->format('d/m/Y') . ' NUIT';
                else
                    $begin_pointage = $begin_pointage->format('d/m/Y') . ' JOUR';

                $end_pointage = new \DateTime($service->end_pointage);
                if ($end_pointage->format('H:i:s') == "18:00:00")
                    $end_pointage = $end_pointage->format('d/m/Y') . ' NUIT';
                else
                    $end_pointage = $end_pointage->format('d/m/Y') . ' JOUR';
                $content = $content . "<p><b>Demandeur : </b>". $service->user_nom . " &lt;" . $service->user_email . "&gt;" ."</p>";
                $content = $content . "<p><b>Debut : </b>". $begin_pointage . "</p>";
                $content = $content . "<p><b>Fin : </b>". $end_pointage . "</p>";
                $content = $content . "<p><b>Motif : </b>". self::replace_chevron($service->motif) . "</p>";
                $content = $content . "<p><b>Ref : </b>". $service->id . "</p>";
            }
        }
        else if ($request->sanction_id) {
            $sanction = (new SanctionController())->show($request->sanction_id);
            $sanction = json_decode($sanction->getContent());
            if ($sanction) {
                $employe = EmployeUtil::getEmploye($sanction);
                $objet = 'Sanction : '. $employe;
                $content = $content . "<p><b>Site : </b>" . $sanction->site . "</p>";
                if ($sanction->objet) {
                    $content = $content . "<p><b>Objet : " . self::replace_chevron($sanction->objet) . "</b>";
                }
                $content = $content . "<p><b>Demandeur : </b>" . $sanction->user_nom . ' &lt;' . $sanction->user_email . '&gt; </p>';
                if ($sanction->absence) {
                    $content = $content . "<p><b>Motif : </b>Sans pointage</p>";
                }
                else {
                    $date_service = MessageController::reformat_date($sanction->date_pointage);
                    $content = $content . "<p><b>Date du service : </b>" . $date_service . "</p>";
                }
                $content = $content . "<p><b>Motif : </b>" . self::replace_chevron($sanction->motif) . "</p>";
                if ($sanction->superviseur_id != $sanction->user_id){
                    $content = $content . "<p><b>Superviseur responsable : </b>" . $sanction->sup_nom . ' &lt;' . $sanction->sup_email . "&gt;</p>";
                }
            }
        }
        else if ($request->prime_id) {
            $prime = (new PrimeController())->show($request->prime_id);
            $prime = json_decode($prime->getContent());
            $last_primes = DB::select("SELECT pr.id, pr.objet, pr.motif, pr.date_pointage
                FROM primes pr
                where pr.status = 'done' && pr.id != ? && pr.employe_id = ?
                order by pr.date_pointage desc
                limit 3", [$request->prime_id, $prime->employe_id]);
            if ($prime) {
                $date_service = new \DateTime($prime->date_pointage);
                if ($date_service->format('H:i:s') == "18:00:00")
                    $date_service = $date_service->format('d/m/Y') . ' NUIT';
                else
                    $date_service = $date_service->format('d/m/Y') . ' JOUR';

                $employe = EmployeUtil::getEmploye($prime);
                $objet = 'Prime : '. $employe;
                $content = $content . "<p><b>". $employe . "</b></p>";
                $content = $content . "<p><b>Site : </b>" . $prime->site . "</p>";
                $content = $content . "<p><b> Date du service : </b>" . $date_service . "</p>";
                if ($prime->objet) {
                   $content = $content . "<p><b>" . $prime->objet. "</b></p>";
                }
                if ($prime->montant > 0) {
                    $content = $content . "<p><b> Montant : </b>" . number_format($prime->montant, 0, ',', '.') . " Ar" . "</p>";
                }
                $content = $content . "<p><b>Motif : </b>" . $prime->motif . "</p>";
                $content = $content . "<p><b>Demandeur : </b>" . $prime->user_nom . ' &lt;' . $prime->user_email . '&gt; </p>';

                if (count($last_primes) > 0) {
                    $content = $content . "<p><b>Prime déjà reçu par l'employe</b></p>";
                    foreach ($last_primes as $pr) {
                        $content = $content . "<p><b>Objet : </b>" . $pr->objet . "</p>";
                        $content = $content . "<p><b>Motif : </b>" . self::replace_chevron($pr->motif) . "</p>";
                        $dt_service = MessageController::reformat_date($pr->date_service);
                        $content = $content . "<p><b> Date du service : </b>" . $dt_service . "</p>";
                    }
                }
                // $content = $content . "<p><b>Ref : </b>". $prime->id . "</p>";
            }
        }
        elseif ($request->sav_id) {
            $sav = (new SavController())->show($request->sav_id);
            $sav = json_decode($sav->getContent());
            if ($sav) {
                $objet = "SAV : " . $sav->site;
                $content = $content . "<p><b>". $sav->site . "</b></p>";
                $content = $content . "<p><b>Type : </b>" . $sav->type->designation . "</p>";
                if ($sav->date_sav) {
                    $content = $content . "<p><b>Date prevu : </b>" . (new \DateTime($sav->date_sav))->format('d/m/Y H:i:s') . "</p>";
                }
                if ($sav->technicien) {
                    $content = $content . "<p><b>Technichien : </b>" . $sav->technicien . "</p>";
                }
                if ($sav->superviseur_id) {
                    $content = $content . "<p><b>Superviseur responsable : </b>" . $sav->sup_nom . ' &lt;' . $sav->sup_email . "&gt;</p>";
                }
                $content = $content . "<p><b>Motif : </b>" . self::replace_chevron($sav->motif) . '</p>';
                $content = $content . "<p><b>Demandeur : </b>" . $sav->user_nom . ' &lt;' . $sav->user_email . '&gt; </p>';
            }
        }
        elseif ($request->flotte_id) {
            $flotte = (new FlotteController())->show($request->flotte_id);
            $flotte = json_decode($flotte->getContent());
            if ($flotte) {
                $objet = "Flotte : " . $flotte->site;
                $content = $content . "<p><b>". ucfirst($flotte->objet) . "</b></p>";
                $content = $content . "<p><b>Commentaire : </b>" . self::replace_chevron($flotte->commentaire) . "</p>";
                $content = $content . "<p><b>Demandeur : </b>" . $flotte->user_nom . ' &lt;' . $flotte->user_email . '&gt; </p>';
                $content = $content . "<p><b>Le : </b>" . (new \DateTime($flotte->created_at))->format('d/m/Y') . '</p>';
            }
        }
        elseif ($request->juridique_id) {
            $juridique = (new JuridiqueController())->show($request->juridique_id);
            $juridique = json_decode($juridique->getContent());
            if ($juridique) {
                if ($juridique->recouvrement) {
                    $objet = "Recouvrement : " . $juridique->debiteur;
                    $content = $content . "<p><b>Débiteur : </b>" . $juridique->debiteur  . "</p>";
                    $content = $content . "<p><b>Contrat : </b>" . $juridique->contrat  . "</p>";
                    $content = $content . "<p><b>Facture : </b>" . $juridique->facture  . "</p>";
                    $content = $content . "<p><b>Montant : </b>" . number_format($juridique->montant, 0, ',', '.') . " Ar </p>";
                }
                else{
                    $objet = "Plainte : " . $juridique->site;
                    $content = $content . "<p><b>Site : </b>" . $juridique->site  . "</p>";
                    $content = $content . "<p><b>Police ou gendarme compétent : </b>" . $juridique->police  . "</p>";
                    $content = $content . "<p><b>Agent(s) concerné : </b>" . $juridique->agent  . "</p>";
                    $content = $content . "<p><b>Rappelle des faits : </b>" . self::replace_chevron($juridique->fait)  . "</p>";
                }
                if ($juridique->reference) {
                    $content = $content . "<p><b>Réf : </b>" . $juridique->reference . "</p>";
                }
                $content = $content . "<p><b>Utilisateur : </b>" . $juridique->user_nom . ' &lt;' . $juridique->user_email . '&gt; </p>';
                $suivis = DB::select("SELECT s.id, s.commentaire, s.created_at, u.name as 'user'
                    from suivi_juridiques s 
                    left join users u on u.id = s.user_id
                    where s.juridique_id = ?
                    order by s.created_at asc", [$request->juridique_id]);
                if (count($suivis) > 0) {
                    $content = $content . "<p><b>Suivis : </b></p>";
                    foreach ($suivis as $sv) {
                        $content = $content . "<p> * ". "<b> [" . $sv->user . " le : " . (new \DateTime($sv->created_at))->format('m/d/Y') . "] : </b>" . self::replace_chevron($sv->commentaire) ."</br></p>";
                    }
                }
                
            }
        }
        elseif ($request->reclamation_id) {
            $reclamation = (new ReclamationController())->show($request->reclamation_id);
            $reclamation = json_decode($reclamation->getContent());
            if ($reclamation && $reclamation->date_pointage) {
                $employe = EmployeUtil::getEmploye($reclamation);
                $objet = "Réclamation : " . $employe;
                $content = $content . "<p><b>". $employe . "</b></p>";
                if ($reclamation->type) {
                    $content = $content . "<p><b>Type de réclamation : </b>" . ($reclamation->type == 'sm' ? "Agent non enregistré" : ($reclamation->type == "service24" ? "Manque de demande service 24" : ($reclamation->type == "archive" ? "Agent archivé" : ""))) . "</p>";
                }
                $content = $content . "<p><b>Date de service réclamé : </b>" . (new \DateTime( $reclamation->date_pointage))->format('d/m/Y') . "</p>";
                // $pointages = DB::table('pointage_reclamations as ptg')
                //     ->select("ptg.date_pointage", "st.nom as site")
                //     ->leftJoin('sites as st', 'ptg.site_id', '=', 'st.idsite')
                //     ->where('reclamation_id', $request->reclamation_id)->get();
                // if (count($pointages) > 0) {
                //     $content = $content . "<p><b> Pointages : </b></p>";
                //     foreach ($pointages as $ptg) {
                //         $dt_ptg = MessageController::reformat_date($ptg->date_pointage);
                //         $content = $content . "<p> * ". $dt_ptg . " : ". $ptg->site . "</br></p>";
                //     }
                // }
            }
        }
        elseif ($request->part_variable_id) {
            $part = (new PartVariableController())->show($request->part_variable_id);
            $part = json_decode($part->getContent());
            if ($part) {
                
                $objet = "Part variable : ". EmployeUtil::getEmploye($part);
                $content = $content . "<p><b>". EmployeUtil::getEmploye($part) . "</b></p>";
                $content = $content . "<p><b>Fiche de paie : </b>" . (new \DateTime($part->date_paie))->format('m/Y'). "</p>";
                $cr_pv = (new CritereMensuelController())->part_variable($request->part_variable_id, $request);
                $cr_pv =(json_decode($cr_pv->getContent()))->part_mensuels;
                $critere = "<thead>
                                <tr> 
                                    <th>Critères</th>
                                    <th>Maximum</th>
                                    <th>Montant</th>
                                </tr>
                            </thead>
                            <tbody>";
                $body = '';
                $montant_total = 0;
                foreach ($cr_pv as $cr) {
                    $montant_total = $montant_total + $cr->montant;
                    $body = $body . "<tr>";
                    $body = $body . "<td>". self::replace_chevron($cr->designation) . "</td>";
                    $body = $body . "<td>". number_format($cr->maximum,0, ',', '.') . "</td>";
                    $body = $body . "<td>". number_format($cr->montant,0, ',', '.') . "</td>";
                    $body = $body . "</tr>";
                }
                $critere = $critere . $body . "</tbody>";
                $critere = $critere . "<tfoot><tr><td colspan=\"3\" style='text-align:right;'><b>Montant Total : " . number_format($montant_total, 0, ',', '.'). " Ar" . "</b></td></tr></tfoot>";
                $content = $content . "<table style='border-collapse: collapse;' border='1'>" . $critere . "</table>";
            }
        }
        elseif ($request->equipement_id) {
            $equipement = (new EquipementController())->show($request->equipement_id, $request);
            $equipement = json_decode($equipement->getContent());
            if ($equipement) {
                $objet = "Equipement : ". $equipement->site;
                $content = $content . "<p><b> Site : </b>" . $equipement->site . "</p>";
                if ($equipement->employe) {
                    $content = $content . "<p><b>Employé : </b>" . EmployeUtil::getEmploye($equipement) . "</p>";
                }
                if ($equipement->demande) {
                    $content = $content . "<p><b> Demande : </b>" . $equipement->demande . "</p>";
                }
                else {
                    if (count($equipement->articles) > 0) {
                        $articles = $equipement->articles;
                        $content = $content . "<p><b> Articles : </b></p>";
                        foreach ($articles as $article) {
                            $content = $content . "<p> * " . $article . "</p>";
                        }
                        //$content = $content . "<p><b> Demande : </b>" . $equipement-> . "</p>";
                    }
                }
                $content = $content . "<p><b> Motif : </b>". self::replace_chevron($equipement->motif) ."</p>";
                $content = $content . "<p><b>Demandeur : </b>" . $equipement->user_nom . ' &lt;' . $equipement->user_email . '&gt; </p>';
            }
        }
        elseif ($request->visite_poste_id) {
            $visite = (new VisitePosteController())->show($request, $request->visite_poste_id);
            $visite = json_decode($visite->getContent());
            if ($visite) {
                $objet = "Visite de poste : " . $visite->site;
                $content = $content . "<p><b>" . $visite->site . "</b></p>";
                $content = $content . "<p><b> Date de la visite : </b>" . (new \DateTime($visite->date_visite))->format('d/m/Y H:i:s') . "</p>";
                $content = $content . "<p><b> Compte rendu : </b>" . self::replace_chevron($visite->compte_rendu) . "</p>";
                $content = $content . "<p><b> Superviseur : </b>" . $visite->user_nom . ' &lt;' . $visite->user_email . "&gt; </p>";
            }
        }
        elseif ($request->deduction_id) {
            $deduction = (new DeductionController())->show($request->deduction_id, $request);
            $deduction = json_decode($deduction->getContent());
            if ($deduction) {
                $employe = EmployeUtil::getEmploye($deduction);
                $objet = "Déduction : " . $employe;
                $content = $content . "<p><b>". $employe . "</b></p>";
                if ($deduction->date_paie) {   
                    $content = $content . "<p><b> Date de la paie : </b>" . (new \DateTime($deduction->date_paie))->format('m/Y') . "</p>";
                }
                $content = $content . "<p><b> Montant : </b>" . number_format($deduction->montant, 0, ',', '.') . " Ar" . "</p>";
                $content = $content . "<p><b> Motif : </b>". self::replace_chevron($deduction->motif) ."</p>";
                $content = $content . "<p><b> Demandeur : </b>" . $deduction->user_nom . ' &lt;' . $deduction->user_email . "&gt; </p>";
                $content = $content . "<p><b> Date de création : </b>" . (new \DateTime($deduction->created_at))->format('d/m/Y') . "</p>";
            }
        }
        elseif ($request->paie_id) {
            $paie = (new PaieController())->show($request->paie_id, $request);
            $paie = json_decode($paie->getContent());
            if ($paie) {
                $employe = EmployeUtil::getEmploye($paie);
                $objet = "Paie : " . $employe;
                $content = $content . "<p><b>". $employe . "</b></p>";
                $content = $content . "<p><b> Date de la paie : </b>" . (new \DateTime($paie->date_paie))->format('m/Y') . "</p>";
                $content = $content . "<p><b> Heure travaillé : </b>" . $paie->nb_heure_travaille . " h" . "</p>";
                $content = $content . "<p><b> Heure contrat : </b>" . $paie->nb_heure_contrat . " h" . "</p>";
                $content = $content . "<p><b> Net a payé : </b>" . number_format($paie->net_a_payer, 0, ',', '.') . " Ar" . "</p>";
                $content = $content . "<p><b> Salaire mensuel : </b>" . number_format($paie->salaire_mensuel, 0, ',', '.') . " Ar" . "</p>";
                $content = $content . "<p><b> Salaire Brut : </b>" . number_format($paie->salaire_brut, 0, ',', '.') . " Ar" . "</p>";
                $content = $content . "<p><b> Cnaps : </b>" . number_format($paie->cnaps, 0, ',', '.') . " Ar" . "</p>";
                $content = $content . "<p><b> Salfa : </b>" . number_format($paie->salfa, 0, ',', '.') . " Ar" . "</p>";
                $content = $content . "<p><b> Irsa : </b>" . number_format($paie->irsa, 0, ',', '.') . " Ar" . "</p>";
                $content = $content . "<p><b> Date de création : </b>" . (new \DateTime($paie->created_at))->format('d/m/Y') . "</p>";
                $content = $content . "<p><b> Demandeur : </b>" . $paie->user_nom . ' &lt;' . $paie->user_email . "&gt; </p>";
                
            }
        }
        else if ($request->satisfaction_id) {
            $satisfaction = (new SatisfactionController())->show($request, $request->satisfaction_id);
            $satisfaction = (json_decode($satisfaction->getContent()))->satisfaction;
            if ($satisfaction) {
                $objet = "Satisfaction : " . $satisfaction->site_nom;
                $content = $content . "<p><b>Site : </b>" . $satisfaction->site_nom . "<p/>";
                $content = $content . "<p><b>Commentaire : </b>" . self::replace_chevron($satisfaction->comment) ."</p>";
                $content = $content . "<p><b> Demandeur : </b>" . $satisfaction->user_nom . ' &lt;' . $satisfaction->user_email . "&gt; </p>";
                $content = $content . "<p><b> Date de création : </b>" . (new \DateTime($satisfaction->created_at))->format('d/m/Y') . "</p>";
                $content = $content . "<p><b> Ref : </b>" . $satisfaction->id . "</p>";
            }
        }
        else if ($request->employe_id) {
            $employe = (new EmployeController())->show($request->employe_id);
            $employe = json_decode($employe->getContent());
            if ($employe) {
                $emp = EmployeUtil::getEmploye($employe);
                $objet = "Employé : " . $emp;
                $content = $content . "<p><b>". $emp . "</b></p>";
                if ($employe->site) {
                    $content = $content . "<p><b> Site : </b>" . $employe->site. "</p>";
                }
                else {
                    $content = $content . "<p><b> Agence : </b>" . $employe->agence . "</p>";
                }
                $date_embauche = null;
                if($employe->societe_id == 1) $date_embauche = (new \DateTime($employe->date_confirmation))->format("d M Y");
                if($employe->societe_id == 2) $date_embauche = (new \DateTime($employe->date_conf_soit))->format("d M Y");
                if($employe->societe_id == 3) $date_embauche = (new \DateTime($employe->date_embauche))->format("d M Y");
                if($employe->societe_id == 6) $date_embauche = (new \DateTime($employe->date_conf_saoi))->format("d M Y");
                if ($date_embauche) {
                    $content = $content . "<p><b> Date d'embauche : </b>". $date_embauche . "</p>";
                }
            }
        }
        // else if($request->plannin_id){
        //     $planning = DB::select("SELECT pl.id as planning_id, pl.date_planning, st.nom as 'site', pl.site_id, 
        //         sup.name as 'superviseur', sup.email as 'superviseur_email', resp.name as 'resp_sup', resp.email as 'resp_sup_email', 
        //         u.name as 'user', u.email as 'user_email',
        //         st.total_hour, st.nb_agent_night, st.nb_agent_day, hr.nom as 'horaire'
        //         FROM plannings pl 
        //         LEFT JOIN sites st on pl.site_id = st.idsite 
        //         LEFT JOIN horaires hr on hr.id = st.horaire_pointage_id
        //         LEFT JOIN users sup on sup.id = st.superviseur_id'
        //         LEFT JOIN users resp on resp.id = st.resp_sup_id
        //         LEFT JOIN users u on u.id = pl.user_id
        //         WHERE pl.id = ?", 
        //         [$request->plannin_id]);
        //     $pointages = DB::table('planning_pointages as ptg')->leftJoin('employes as emp', 'emp.id', '=', '') PlanningPointage::where('planning_id', $request->planning_id)->get();
        //     $objet = "Planning : " . $planning[0]->site . " [" . (new \DateTime($planning[0]->date_planning))->format("M Y") . "]";
        //     $content = $content . "<p><b>Planning : </b>" . $planning[0]->site . "</p>";
        //     $content = $content . "<p><b>Date planning : </b>" . (new \DateTime($planning[0]->date_planning))->format("d M Y") . "</p>";
        //     if ($planning[0]->superviseur_id) {
        //         $content = $content . "<p><b>Superviseur : </b>" . $planning[0]->superviseur . " &lt;" . $planning[0]->superviseur_email . "&gt;</p>";
        //     }
        //     if ($planning[0]->resp_sup_id) {
        //         $content = $content . "<p><b>Manager : </b>" . $planning[0]->resp_sup . " &lt;" . $planning[0]->resp_sup_email . "&gt;</p>";
        //     }
        //     if ($planning[0]->user_id) {
        //         $content = $content . "<p><b>Utilisateur : </b>" . $planning[0]->user . " &lt;" . $planning[0]->user_email . "&gt;</p>";
        //     }
        //     $content = $content . "<p><b>Heure Facturés : </b>" . $planning[0]->total_hour . "</p>";
        //     $content = $content . "<p><b>Heures pointées : </b>" . count($pointages) *12 . "</p>";
        //     $content = $content . "<p><b>Type de contrat : </b>" . $planning[0]->horaire. "</p>";
        //     $group_pointages = self::groupPointagePlanning($pointages);
        //     foreach($group_pointages as $group){
        //         $agents = '\n';
        //         $label = '*' . (new \DateTime($group['date_pointage']))->format('d-m-Y');
        //         if ((new \DateTime($group['date_pointage']))->format('H:i:s') == '18:00:00') 
        //             $label .= ' NUIT';
        //         else
        //             $label .= ' JOUR';
        //         foreach($group['agents'] as $agent){
        //             $agent = HistoriqueController::getEmploye($agent);
        //             $agents .=  ' -' . $agent . '\n';
        //         }

        //     }

        // }
        else {
            $objet = $request->objet;
        }
        return compact('objet', 'content');
    }

    public function get_absence(Request $request, $id){
        $absence = DB::select("SELECT cg.id, cg.employe_id, cg.depart, cg.retour, cg.motif, cg.status, cg.created_at,
            t.designation as 'type', t.name as 'type_name', cg.site_id, st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', cg.user_id,
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
            sup.name as 'sup_nom', sup.email as 'sup_email', cg.superviseur_id
            FROM absences cg
            LEFT JOIN type_absences t ON t.name = cg.type_absence
            LEFT JOIN employes a ON a.id = cg.employe_id
            LEFT JOIN sites st ON st.idsite = cg.site_id
            LEFT JOIN users us on us.id = cg.user_id
            LEFT JOIN users sup on sup.id = cg.superviseur_id
            LEFT JOIN `status` stat on stat.name = cg.status
            WHERE cg.id = ?", [$id])[0];
        return response()->json($absence);
    }
    public static function reformat_date($date){
        $date = new \DateTime($date);
        if ($date->format('H:i:s') == "18:00:00")
            return $date->format('d/m/Y') . ' NUIT';
        else
            return $date->format('d/m/Y') . ' JOUR';
    }

    public function unfollow(Request $request, $id){
        $note = NoteMessage::find($id);
        $message = Message::where('id', $note->message_id)->first();
        if ($message->user_id == $request->user()->id) {
            $note->follow = null;
            if ($note->save()) {
                return response()->json(['success' => "Message n'est plus suivi"]);
            }
            return response()->json(['error' => "Une erreur s'est produite"]);
        }
        return response()->json(['error' => "EACCES"]);
    }

    public static function groupPointagePlanning($data){
        $result =  $data
                ->sortBy('date_pointage') 
                ->groupBy('date_pointage')
                ->map(function ($group) {
                    return [
                        'date_pointage' => $group->first()['date_pointage'],
                        'agents' => $group->pluck('agent_id')->all(),
                    ];
            })->values()->all();
        return $result;
    }
}
