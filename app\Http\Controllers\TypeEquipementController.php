<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TypeEquipement;
use App\Models\Article;

class TypeEquipementController extends Controller
{
    public function index(Request $request){
        if($request->personal)
            $types = TypeEquipement::where('personal', '1')->orderBy('ordre', 'ASC')->get();
        else
            $types = TypeEquipement::orderBy('ordre', 'ASC')->get();
        return response($types);
    }
    public function show($name){
        $type = TypeEquipement::find($name);
        $articles = Article::where('type', $name)->orderBy('designation')->get();
        return response(compact('type', 'articles'));
    }
}
