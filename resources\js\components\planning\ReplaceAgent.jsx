import React, { useState } from 'react'
import InputCheckBox from '../input/InputCheckBox'
import InputAgent from '../input/InputAgent'
import moment from 'moment'
import { forEach } from 'lodash'

export default function ReplaceAgent({plannings, currentAgent, setPlannings, closeModal}) {

    const [isReplaceAll, toggleReplaceAll] = useState(currentAgent.soft_delete == 1 ? true: false)
    const [agentToAdd, setAgentToAdd] = useState(null)
    const handleReplaceAgent = (currentAgent, agentToAdd) => {
        plannings.forEach(p => {
            if (moment(p.service).isAfter(moment())) {
                forEach(p.employes, e => {
                    if ( isReplaceAll && e.id == currentAgent.id) {
                        e.matricule = agentToAdd.matricule
                        e.nom = agentToAdd.nom
                        e.id = agentToAdd.id
                    }
                    else if(!isReplaceAll && e.id == currentAgent.id && p.id == currentAgent.planning_id){
                        e.matricule = agentToAdd.matricule
                        e.nom = agentToAdd.nom
                        e.id = agentToAdd.id
                    }
                })
            }
        });
        closeModal()
    }

    return (
        <div className='modal'>
            <div>
                <h2 className='header-modal-replace-agent'>{'[' + currentAgent.matricule + '] ' +currentAgent.nom}</h2>
                <InputCheckBox label="Remplacer tous les services de l'agent" checked={isReplaceAll} onChange={() => toggleReplaceAll(!isReplaceAll)} required/>
                <InputAgent label="Remplaçant" value={agentToAdd} onChange={setAgentToAdd} required/>
                <div className='form-button-container'>
                    <button type='button' className='btn-primary' onClick={()=>handleReplaceAgent(currentAgent, agentToAdd)}>Remplacer</button>
                    <button type='button' className='btn' onClick={closeModal}>Fermer</button>
                </div>
            </div>
        </div>
    )
}
