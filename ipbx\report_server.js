const http = require('http');
const mysql = require('mysql')
const moment = require('moment')

const {db_config_nomedia} = require("../auth")
const poolOvh = mysql.createPool(db_config_nomedia)
const sqlInsertCall = "INSERT INTO calls(action, call_id, sn, `from`, `to`, call_duration, talk_duration, " +
    "recording, status, json, created_at, updated_at) " +
	"values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now(), now())"

const server = http.createServer((req, res) => {
    //console.log(req.url, req.method)
    if (req.url === '/' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => {
          body += chunk.toString();
        });
    
        req.on('end', () => {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end("OK");
            const parsedData = JSON.parse(body);
            const action = parsedData.action
            const call_id = parsedData.callid
            let sn = null
            let from = null
            let to = null
            let call_duration = null
            let talk_duration = null
            let recording = null
            let status = null

            if(['ExtensionStatus', 'ALERT'].includes(parsedData.action)){

            }
            else if(['NewCdr', 'BYE', 'ANSWER', 'ANSWERED', 'RING'].includes(parsedData.action)){
                sn = parsedData.sn
                if(parsedData.action == 'NewCdr'){
                    from = parsedData.callfrom
                    to = parsedData.callto
                    recording = parsedData.recording
                    status = parsedData.status
                }
                else if(['BYE', 'ANSWER', 'ANSWERED', 'RING'].includes(parsedData.action)){
                    if(parsedData.outbound){
                        from = parsedData.outbound.from
                        to = parsedData.outbound.to
                        status = "OUTBOUND"
                    }
                    else if(parsedData.inbound){
                        from = parsedData.inbound.from
                        to = parsedData.inbound.to
                        status = "INBOUND"
                    }
                }
            }
            else {
                //console.log(parsedData)
            }
            poolOvh.query(sqlInsertCall, [action, call_id, sn, from, to, call_duration, talk_duration, recording, status, JSON.stringify(parsedData, null, " ")], (err, result) => {
                if(err)
                    console.error(err)
                else {
                    //console.log(action + " | " + from + " | " + to + " | " + status + " | " + call_id)
                    //console.log(parsedData)
                }
            })
            
            console.log(moment().format("YY-MM-DD HH:mm"),parsedData)
        });
    }
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('Hello, World!\n');
});


server.listen(8260, () => {
    console.log('Server is listening on port 8260');
});