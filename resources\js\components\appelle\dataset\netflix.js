const translations = {
    nb_inbound: 'Entrant',
    nb_outbound: 'Sortant',
    nb_internal: 'Interne',
    nb_transfer: 'Transferé',
    nb_other: 'Non défini',
    nb_inbound_success: "<PERSON><PERSON>",
    nb_inbound_missing: "<PERSON>q<PERSON>",
    nb_inbound_busy: "Occupé",
    nb_outbound_success: "About<PERSON>",
    nb_outbound_missing: "Injoignable",
    nb_outbound_busy: "Occupé"
  };
  
  export function addLabels(series) {
    return series.map((item) => ({
      ...item,
      label: translations[item.dataKey],
      valueFormatter: (v) => (v ? `${v.toLocaleString()}` : '0'),
    }));
  }