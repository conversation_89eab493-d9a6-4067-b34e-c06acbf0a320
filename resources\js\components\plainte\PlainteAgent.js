import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import LoadingPage from '../loading/LoadingPage';
import InfiniteScroll from 'react-infinite-scroll-component';
import StatusLabel from '../input/StatusLabel';

export default function PlainteAgent({auth, juridiques, setJuridiques, currentId, setCurrentId}) {
    const locationSearch = useLocation().search
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'ID', name: 'id', type:'number'},
        {label: 'Réference juridique', name: 'reference', type:'string'},
        {label: 'Agence', name: 'agence_id', type:'number'},
        {label: "Site", name: "site_id", type: "number"},
        {label: "Police", name: "police", type: "string"},
        {label: "Agent", name: "agent", type: "string"},
        {label: "Fait", name: "fait", type: "string"},
        {label: 'Status', name: 'status', type:'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Utilisateur', name: 'user_id', type:'number'}
    ]
        
    const updateData = (initial) => {
        let isMounted = true;
        const urlParams = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            urlParams.set("offset", 0)
        }
        else
            urlParams.set("offset", juridiques.length)

        axios.get("/api/plainte?" + urlParams ,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setJuridiques(res.data.juridiques)
                    else {
                        const list = juridiques.slice().concat(res.data.juridiques)
                        setJuridiques(list)
                    }
                    setDataLoaded(res.data.juridiques.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }
    
    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        : 
            <div>
                <div className="padding-container space-between">
                    <h2>Plainte</h2>
                    {   
                        ['juridique'].includes(auth.role) && 
                        <Link className='btn btn-primary' to="/plainte/add">Nouveau</Link>
                    }
                </div>
                <SearchBar hasEmploye listItems={searchItems}/>
                {
                    juridiques.length == 0 ? 
                        <h3 className='center secondary'>Aucun données trouvé</h3>
                    :
                        <InfiniteScroll
                            dataLength={juridiques.length}
                            next={fetchMoreData}
                            hasMore={!allDataLoaded}
                            loader={<LoadingPage/>}
                        >
                            <div className="line-container ">
                                <div className='row-employe'>
                                    <b className='sujet-juridique'>
                                        Site
                                    </b>
                                    <b className="status-line"><StatusLabel color="grey"/></b>
                                    <b>
                                        Fait
                                    </b>
                                </div>
                            </div>
                            {
                                juridiques.map((j) => (
                                    <div className={`table line-container ${currentId && currentId == j.id ? 'selected' : ''}`} key={j.id}>
                                        <div className={"row-employe"} onClick={() => setCurrentId(j.id)}>
                                            <span className='sujet-juridique'>
                                                {j.site}
                                            </span>
                                            <span className="status-line">
                                                <StatusLabel color={j.status_color}/>
                                            </span>
                                            <span>
                                                {j.fait}
                                            </span>
                                        </div>
                                    </div>
                                ))
                            }
                        </InfiniteScroll>
                }
            </div>
    } </>
}