import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Link, Navigate } from 'react-router-dom';
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';

export default function Electronique({auth}) {
    const [nbData, setData] = useState(null) 

    useEffect(() => {        
        let isMounted = true
        axios.get('/api/electronique', useToken())
        .then(res => {
            setData(res.data)
        })
        .catch(err => {
            console.error(err)
        })
        return () => { isMounted = false }
    }, [])
    return (
        auth.role == "electronique" ?
            <MenuView title="">
                {
                    nbData &&
                    (
                        (nbData.nb_biometrique != 0) ?
                        <div>
                            <h3 className='sub-title-menu'>Electronique</h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_biometrique != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sav/biometrique?status=demande">Demande de SAV - Pointeuse Biometrique</Link>
                                        <span className='badge-outline'>{nbData.nb_biometrique}</span>
                                    </div>
                                }
                            </div>
                        </div>
                        :
                        <div className='center secondary'>
                            <br/>
                            <br/>
                            <h3>Aucune demande pour l'instant</h3>
                        </div>                        
                    )
                }
            </MenuView>
        : 
            <Navigate to="/"/>
    );
}