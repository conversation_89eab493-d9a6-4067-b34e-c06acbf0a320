const https = require('https');
const { default: axios } = require('axios');
const db_config = require("../auth").db_config_nomedia

const dataLogin = {
    "username": "api",
    "password": "efd8227869ba708eb9966e2b392c81f1",
    "port": "8260"
}
const agent = new https.Agent({  
    rejectUnauthorized: false
});

axios.post("https://192.168.10.150:8088/api/v1.1.0/login", dataLogin, { 
    httpsAgent: agent,
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
})
.then(res => {
    //console.log(res.data)
    if(res.data.token){
        const dataCall = {
            "extid": process.argv[2],
            "outto": process.argv[3],
            "autoanswer": "no"
        }
        axios.post("https://192.168.10.150:8088/api/v1.1.0/extension/dial_outbound?token=" + res.data.token, dataCall, {
            httpsAgent: agent,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(res => {
            //console.log(res.data)
            if(res.data){
                console.log(res.data.callid)
            }
            else console.log("error call")
        })
        .catch(e => {
            console.error(e)
        })
    }
    else console.log("error login!")
})
.catch(e => {
    console.error(e)
})