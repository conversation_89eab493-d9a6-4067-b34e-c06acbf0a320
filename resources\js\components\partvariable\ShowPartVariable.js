import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import useToken from '../util/useToken';
import matricule from '../util/matricule';         
import Tab from '../layout/Tab';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';
import ActionPartVariable from './ActionPartVariable';
import moment from 'moment';

export default function ShowPartVariable({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [partVariable, setPartVariable] = useState()
    const [isLoading, toggleLoading] = useState(false)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/part_variable/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setPartVariable(res.data)
                const newUser = []
                if (auth.id != res.data.user_id)
                    newUser.unshift({ id: res.data.user_id, address: res.data.user_email, name: res.data.user_name })
                setDefautUsers(newUser)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(partVariable)
    }, [partVariable])

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                partVariable &&
                <>
                    <ShowHeader size={size} label="Part variable" id={partVariable.id} closeDetail={() => setCurrentId()}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + partVariable.status_color}>
                                    {partVariable.status_description}
                                </span> {
                                    partVariable.nb_pj > 0 &&
                                    <span className='badge-outline'>
                                        Pièce jointe : {partVariable.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {matricule(partVariable)} {partVariable.employe} 
                        </h3>
                        <p>
                            Fiche de paie : <span className='capitalize text'>{moment(partVariable.date_paie).format("MMM YYYY")}</span>
                        </p>
                        <p>
                            Fonction : <span className='text'>
                                {partVariable.fonction_id != 12 ? partVariable.fonction : partVariable.titre}
                            </span>
                        </p>
                        <p>
                            Agence : <span className='text'>{partVariable.agence}</span>
                        </p>
                        {
                            partVariable.commentaire && 
                            <p>
                                Commentaire : <span className='text'>{partVariable.commentaire}</span>
                            </p>
                        }
                        <div className='card-action'>
                            <ActionPartVariable auth={auth} partVariable={partVariable} updateData={updateData} toggleLoading={toggleLoading}/>
                        </div>
                    </div>
                <Tab auth={auth} data={partVariable} name="part_variable_id" value={partVariable.id} updateData={updateData} defautUsers={defautUsers}/>
                </>
            }
        </div>
    }</>
}