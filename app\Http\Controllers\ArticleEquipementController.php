<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Models\Equipement;
use App\Models\ArticleEquipement;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\HistoriqueController;
use App\Http\Controllers\MouvementEquipementController;

class ArticleEquipementController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    protected function search(Request $request){
        if(in_array($request->search, ['created_at', 'user_id', 'employe_id', 'site_id', 'article'])){
            if($request->type == 'date')
                return " eq." . $request->search . " > '" . $request->value . " 00:00:00' and ".
                    "eq." . $request->search . " <= '" . $request->value . " 23:59:59' ";
            else if($request->type == 'number')
                return " eq." . $request->search . " = " . $request->value . " ";
            else if($request->search == 'article')
                return " ac.name = '" . $request->value . "' ";
        }
        return false;
    }

    public function index(Request $request){
        $articles = [];
        if(in_array($request->user()->role, ['tenue', 'achat'])){
        $search = $this->search($request);
            $articles = DB::select("SELECT aeq.id, eq.user_id, eq.employe_id, eq.site_id, eq.motif,
                ac.designation, eq.demande, eq.detail, eq.status, eq.created_at,
                a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
                st.nom as 'site', us.name as 'user_nom', us.email as 'user_email'
                FROM article_equipements aeq
                LEFT JOIN equipements eq on eq.id = aeq.equipement_id
                LEFT JOIN articles ac on ac.name = aeq.article
                LEFT JOIN employes a on a.id = eq.employe_id
                LEFT JOIN sites st on st.idsite = eq.site_id
                LEFT JOIN users us on us.id = eq.user_id
                WHERE ac.service = ? and aeq.done is null " . ($search ? (" and ". $search) : "") ."
                order by eq.created_at desc", [$request->user()->role]);
        }
        else
            return response(["error" => "EACCES"]);

        return response(compact('articles'));
    }

    public function done(Request $request, $id){
        $article = DB::select("SELECT aeq.id, aeq.equipement_id, ac.service, ac.designation, aeq.done
            FROM article_equipements aeq 
            LEFT JOIN articles ac on ac.name = aeq.article
            WHERE aeq.id = ?", [$id])[0];
        $equipement = Equipement::find($article->equipement_id);
        if(!$equipement->recruiting && is_null($article->done) && $article->service == $request->user()->role && in_array($equipement->status, ["demande", "traite"])){
            ArticleEquipement::where('id', $id)->update(['done' => 1]);
            $equipement->note_id = HistoriqueController::action_equipement($request, $article->designation . " bien reçu", $equipement->id);
            $not_traite = DB::select('SELECT aeq.id FROM article_equipements aeq WHERE aeq.done is null and aeq.equipement_id = ?'
            , [$article->equipement_id]);
            if(count($not_traite) == 0){
                $has_done = DB::select('SELECT aeq.id FROM article_equipements aeq WHERE aeq.done = 1 and aeq.equipement_id = ?'
                , [$article->equipement_id]);
                if(count($has_done) > 0)
                    $equipement->status = 'done';
                else
                    $equipement->status = 'draft';
            }
            $equipement->save();
            return response(["success" => "Equipement bien envoyée", "id" => $equipement->id]);
        }
        return response(["error" => "EACCES"]);
    }

    public function done_all(Request $request, $id) {
        $selected_articles = $request->input('selectedItems');
        $type_equipement = $request->input('type');
        if (is_array($selected_articles) && count($selected_articles) > 0) {
            $equipement = Equipement::find($id);
            $agent = $equipement->employe_id;
            $site = $equipement->site_id;
            $demandeur = $equipement->user_id;
            foreach($selected_articles as $article) {
                $article_equipements = DB::select("SELECT aeq.id, aeq.equipement_id, aeq.article as nom_article, ac.service, ac.designation, aeq.done
                FROM article_equipements aeq
                LEFT JOIN articles ac ON ac.name = aeq.article
                WHERE aeq.article = ?
                AND aeq.equipement_id = ?", [$article != "T-shirt" ? $article : "tshirt", $id])[0];
                if (!$equipement->recruiting && is_null($article_equipements->done) && $article_equipements->service == $request->user()->role && in_array($equipement->status, ["demande", "traite"])) {
                    ArticleEquipement::where('id', $article_equipements->id)->update(['done' => 1]);
                    $equipement->note_id = HistoriqueController::action_equipement($request, $article_equipements->designation . " bien reçu", $equipement->id);
                }
                if ($equipement->recruiting && is_null($article_equipements->done) && $article_equipements->service = $request->user()->role && in_array($equipement->status, ["demande", "traite"])) {
                    ArticleEquipement::where('id', $article_equipements->id)->update(['done' => 1]);
                    $equipement->note_id = HistoriqueController::action_equipement($request, $article_equipements->designation . " bien reçu", $equipement->id);
                }
            }
            $non_traite = DB::select('SELECT aeq.id FROM article_equipements aeq WHERE aeq.done is null and aeq.equipement_id = ?'
            , [$equipement->id]);
            if(count($non_traite) == 0){
                $has_done = DB::select('SELECT aeq.id FROM article_equipements aeq WHERE aeq.done = 1 and aeq.equipement_id = ?'
                , [$equipement->id]);
                if(count($has_done) > 0)
                    $equipement->status = 'done';
                else
                    $equipement->status = 'draft';
            }
            $equipement->save();
            if ($type_equipement == "tenue") {
                MouvementEquipementController::new_dotation("nouveau", "sortie", Carbon::now()->format('Y-m-d'), $selected_articles, $agent, $demandeur);
            }
            if ($type_equipement == "impermeable") {
                MouvementEquipementController::new_dotation("nouveau", "sortie", Carbon::now()->format('Y-m-d'), $selected_articles, $site, $demandeur);
            }
            return response(["success" => "Equipement bien envoyée", "id" => $equipement->id]);
        } else {
            return response(["message" => "Aucun article sélectionné"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel(Request $request, $id){
        $validator = Validator::make($request->all(), [
            'note' => 'required',
        ]);
        if ($validator->fails())
            return response(['error' => $validator->errors()->first()]);

        $article = DB::select("SELECT aeq.id, aeq.equipement_id, ac.service, ac.designation, aeq.done
            FROM article_equipements aeq
            LEFT JOIN articles ac on ac.name = aeq.article
            WHERE aeq.id = ?", [$id])[0];
        $equipement = Equipement::find($article->equipement_id);
        if(is_null($article->done) && $article->service == $request->user()->role && in_array($equipement->status, ["demande", "traite"])){
            ArticleEquipement::where('id', $id)->update(['done' => 0]);
            $equipement->note_id = HistoriqueController::action_equipement($request, $article->designation . " annulé", $equipement->id);
            $not_traite = DB::select('SELECT aeq.id FROM article_equipements aeq WHERE aeq.done is null and aeq.equipement_id = ?'
            , [$article->equipement_id]);
            if(count($not_traite) > 0)
                $equipement->status = 'traite';
            else {
                $has_done = DB::select('SELECT aeq.id FROM article_equipements aeq WHERE aeq.done = 1 and aeq.equipement_id = ?'
                , [$article->equipement_id]);
                if(count($has_done) > 0)
                    $equipement->status = 'done';
                else
                    $equipement->status = 'draft';
            }
            $equipement->save();
            return response(["success" => "Equipement bien envoyée", "id" => $equipement->id]);
        }
        return response(["error" => "EACCES"]);
    }
}
