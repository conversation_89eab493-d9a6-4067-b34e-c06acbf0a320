import moment from 'moment'
import React, { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import useToken from '../util/useToken'
import ShiftDuplicate from '../planning/ShiftDuplicate'

export default function LastPlanning({auth, name, value, lastPlannings}) {
    const navigate = useNavigate()
    const [showDuplicate, toggleDuplicate] = useState(false)
    const [firstPointage, setFirstPointage] = useState([])
    const [currentPlanning, setCurrentPlanning] = useState(null)
    const [pointages, setPointages] = useState([])
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const date = queryParams.get('date');

    const getPointageCurrentPlanning = (pl) => {
        axios.get('/api/planning/show/' + pl.id, useToken()).then(res => {

            setCurrentPlanning(res.data.planning)
            let pointages = res.data.pointages
            const ptgs = pointages.filter(pt => 
                moment(pt.date_pointage).isSame(moment(pl.date_planning).date(1).hour(6).minute(0).second(0), 'second')
            );
            setPointages(pointages)
            setFirstPointage(ptgs)
            toggleDuplicate(true)
        })
    }

    return (
        <div>
            {
                lastPlannings.map(pl => 
                    <div key={pl.id}>
                        <div className='anomalie-container'>
                            <span>{moment(pl.date_planning).format('MMM YYYY')}</span>
                            <div className='action-container'>
                                <span onClick={()=>getPointageCurrentPlanning(pl)}>Dupliquer</span>
                            </div>
                        </div>
                    </div> 
                )
            }
            {
                showDuplicate &&
                <ShiftDuplicate planning={currentPlanning} 
                    firstPointage={firstPointage} 
                    setFirstPointage={setFirstPointage}
                    pointages={pointages}
                    defaultDate={date} 
                    auth={auth} 
                    closeModal={() => toggleDuplicate(false)}/>
            }
        </div>
    )
}
