<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\Service24;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class Service24Controller extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function search(Request $request){
        $searchArray = [];
        if ($request->id) {
            $searchArray[] = " sc.id = " . $request->id . " ";
        } else {
            if ($request->created_at) {
                $searchArray[] =  " sc.created_at > '$request->created_at 00:00:00' and sc.created_at <= '$request->created_at 23:59:59' ";
            }
            if ($request->employe_id) {
                $searchArray[] = " sc.employe_id = '$request->employe_id' ";
            }
            if ($request->status) {
                $searchArray[] = " sc.status = '$request->status' ";
            }
            if ($request->user_id) {
                $searchArray[] = " sc.user_id = '$request->user_id' ";
            }
        }
        $query_where = "";
        $query_and = "";
        if ($request->offset || $request->offset > -1) {
            $orderBy = " order by sc.id DESC limit " . $request->offset . ", 30";
        } else {
            $orderBy = " order by sc.id DESC";
        }
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . "";
        }
        $query_where = $query_where . " " . $orderBy;
        $query_and = $query_and . " " . $orderBy;
        return compact("query_where", "query_and");
    }

    public function index(Request $request){
        if (in_array($request->user()->role, ['validateur', 'room', 'resp_sup', 'resp_op'])) {
            $services = DB::select("SELECT sc.id, sc.employe_id,  sc.begin_pointage, sc.end_pointage, sc.date_pointage, sc.motif,
                stat.color as 'status_color', emp.nom as employe, emp.societe_id, emp.numero_stagiaire,
                emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi
                FROM service24s sc 
                LEFT JOIN `status` stat on stat.name = sc.status
                LEFT JOIN employes emp ON emp.id = sc.employe_id ". $this->search($request)['query_where']);
            return response(compact('services'));
        }
        if (in_array($request->user()->role, ['superviseur'])) {
            $services = DB::select("SELECT sc.id, sc.employe_id, sc.begin_pointage, sc.end_pointage, sc.date_pointage, sc.motif,
                stat.color as 'status_color', emp.nom as employe, emp.societe_id, emp.numero_stagiaire,
                emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi
                FROM service24s sc 
                LEFT JOIN `status` stat on stat.name = sc.status
                -- LEFT JOIN users us ON sc.user_id = us.id
                LEFT JOIN employes emp ON emp.id = sc.employe_id
                WHERE sc.user_id = ?" . $this->search($request)["query_and"], [$request->user()->id]);
            return response(compact('services'));
        }
        return response(["error" => "EACCES"]);
    }
    
    public function done_multiple(Request $request){
        if($request->user()->role == 'validateur') {
            // $ids = explode(",", $request->ids);
            // $services = DB::select("SELECT * FROM service24s WHERE id IN (" . implode(",", $ids) . ") AND status = 'validation'");
            $services = Service24::where('status', 'validation')->get();
            foreach ($services as $service) {
                if ($service->status == 'validation') {
                    $service->status = 'done';
                    $service->updated_at = new \DateTime();
                    $service->note_id = HistoriqueController::action_service24($request, "Service 24 validé", $service->id);
                    if ($service->save()) {
                        Notification::create([
                            'historique_id' => $service->note_id,
                            'receiver_id' => $service->user_id,
                            'user_id' => $request->user()->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }
            return response(["success" => "Réponse de la demande envoyé"]);
        }
    }
    public function get_data_validation(Request $request){
        if (in_array($request->user()->role, ['validateur'])) {
            $services = DB::select("SELECT sc.id, sc.employe_id
                FROM service24s sc WHERE sc.status = 'validation'");
            return response(compact('services'));
        }
        else return response(['services' => []]);
    }

    public function show(Request $request, $id){
        if (in_array($request->user()->role, ['superviseur', 'resp_sup', 'resp_op', 'validateur', 'room', 'resp_rh'])) {
            if ($request->user()->role == 'superviseur') {
                $service = DB::select("SELECT sc.id, sc.employe_id,  sc.begin_pointage, sc.end_pointage, sc.date_pointage, sc.motif, sc.status, 
                sc.user_id, stat.description as 'status_description', stat.color as 'status_color',
                emp.nom as employe, emp.societe_id, emp.numero_stagiaire, emp.numero_employe, 
                emp.num_emp_soit, emp.num_emp_saoi, us.name as 'user_nom', us.email as 'user_email'
                FROM service24s sc 
                LEFT JOIN `status` stat on stat.name = sc.status
                LEFT JOIN users us ON sc.user_id = us.id
                LEFT JOIN employes emp ON emp.id = sc.employe_id
                WHERE sc.user_id = ? and sc.id =?", [$request->user()->id, $id])[0];
            }
            else {
                $service = DB::select("SELECT sc.id, sc.employe_id,  sc.begin_pointage, sc.end_pointage, sc.date_pointage, sc.motif, sc.status, 
                sc.user_id, stat.description as 'status_description', stat.color as 'status_color',
                emp.nom as employe, emp.societe_id, emp.numero_stagiaire, emp.numero_employe, 
                emp.num_emp_soit, emp.num_emp_saoi, us.name as 'user_nom', us.email as 'user_email'
                FROM service24s sc 
                LEFT JOIN `status` stat on stat.name = sc.status
                LEFT JOIN users us ON sc.user_id = us.id
                LEFT JOIN employes emp ON emp.id = sc.employe_id
                WHERE sc.id =?", [$id])[0];
            }
            $piece_jointe = DB::select("SELECT id FROM piece_jointes where service24_id = ?", [$id]);
            $service->nb_pj = count($piece_jointe);
            return response()->json($service);
        }
    }

    protected function ValidateAndSetService($request, $service){
        $service->employe_id = $request->employe_id;
        $service->date_pointage = $request->date_pointage;
        $service->motif = $request->motif;
        $service->user_id = $request->user()->id;
        if (in_array($request->user()->role, ['superviseur', 'resp_sup', 'resp_op'])) {
            $validator = Validator::make($request->all(), [
                'employe_id' =>'required',
                // 'begin_pointage' =>'required',
                // 'end_pointage' =>'required',
                'date_pointage' =>'required',
                'motif' =>'required',
            ]);
        }
        if ($validator->fails())
            return ['error' => $validator->errors()->first()];

        $date_pointage = new \DateTime($service->date_pointage);
        // $end_pointage = new \DateTime($service->end_pointage);
        // $currentDate = new \DateTime();
        // if ((new \DateTime)->setTime(6, 0, 0) >= $currentDate) {
        //     $limit_date = (new \DateTime)->setTime(6, 0, 0)->add(new \DateInterval('P1D'));
        // }
        // else if ((new \DateTime)->setTime(18, 0, 0) >= $currentDate) {
        //     $limit_date = (new \DateTime)->setTime(18, 0, 0)->add(new \DateInterval('P1D'));
        // }
        // else if ((new \DateTime)->setTime(18, 0, 0) < $currentDate){
        //     $limit_date = (new \DateTime)->add(new \DateInterval('P1D'))->setTime(6, 0, 0);
        // }
        $limit_date = (new \DateTime)->modify('-11 hours');

        if ( $date_pointage < $limit_date ||
            !(in_array($date_pointage->format("H:i:s"), ["06:00:00", "18:00:00"]))
        ) {
            return ["error" => "La date de service mentionné n'est plus acceptable"];
        }
        // if (
        //     $begin_pointage >= $end_pointage ||
        //     !(in_array($begin_pointage->format("H:i:s"), ["06:00:00", "18:00:00"]))
        // ) {
        //     return ["error" => "La date fin doit être supérieure à la date du début"];
        // }
        return ['error' => ''];
    }

    public function store(Request $request){
        if (in_array($request->user()->role, ['superviseur','resp_sup','resp_op'])){
            $service = new Service24();
            $validator = $this->ValidateAndSetService($request,$service);
            if ($validator['error']) {
                return response($validator);
            }
            $service->status = 'validation';
            $service->updated_at = new \DateTime();
            $service->created_at = new \DateTime();
            if ($service->save()) {
                HistoriqueController::new_service24($request, $service);
                return response(["success" => "Demande de validation envoyé", "id" => $service->id]);
            }
            return response(["error" => "Une erreur est survenue"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function reply_validation(Request $request, $id){
        $service = Service24::find($id);
        if (in_array($request->user()->role, ["validateur"]) && $service->status == 'validation') {
            if (!$request->note)
                $request->note = "ok";

            $service->status = "done";
            $service->updated_at = new \DateTime();

            $service->note_id = HistoriqueController::action_service24($request, "Service 24 validé", $id);
            if ($service->save()) {
                $resp_rh = DB::select("SELECT id FROM users WHERE `role` = 'resp_rh'");
                $resp_ids = [];
                foreach ($resp_rh as $resp) {
                    array_push($resp_ids, $resp->id);
                }
                if (!in_array($service->user_id, $resp_ids)) {
                    $resp_ids[] = $service->user_id;
                }
                foreach ($resp_ids as $resp_id) {
                    Notification::create([
                        'historique_id' => $service->note_id,
                        'receiver_id' => $resp_id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
            return response(["success" => "Réponse de la demande envoyé", "id" => $service->id]);
        }
        return response(['error' =>"EACCES"]);
    }

    public function cancel_service(Request $request, $id){
        $service = Service24::find($id);
        if (($request->user()->id == $service->user_id || $request->user()->role == "validateur") &&
            $service->status == "validation") 
        {
            $validator = Validator::make($request->all(), [
                'note' =>'required',
            ]);
            if ($validator->fails()) 
            return ['error' => $validator->errors()->first()];
            $service->status = "draft";
            $service->updated_at = new \DateTime();
            if ($service->save()) {
                $service->note_id = HistoriqueController::action_service24($request, "Demande de validation annulé", $id);
                if ($request->user()->role == "validateur") {
                    Notification::create([
                        'historique_id' => $service->note_id,
                        'receiver_id' => $service->user_id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                return response(["success" => "Demande de validation annulé", "id" => $service->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $service = Service24::find($id);
        $old_service = clone $service;
        if ($request->user()->id == $service->user_id) {

            $validator = $this->ValidateAndSetService($request, $service);
            if ($validator['error'])
                return response($validator);
            $service->updated_at = new \DateTime();
            $service->status = "validation";
            if ($service->save()) {
                HistoriqueController::update_service24($request, $old_service, "Renvoie de demande de service 24");
                return response(["success" => "Renvoie de demande de service 24", "id" => $service->id]);
            }
        }
        return response(["error" => "EACCES"]);

    }
}
