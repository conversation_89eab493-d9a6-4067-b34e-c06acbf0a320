import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { Outlet } from "react-router-dom";

import './navbar.css'
import './menu.css'

import Sidebar from './sidebar/Sidebar';
import SidebarModal from './sidebar/SidebarModal';
import useToken from '../util/useToken';
import ResetPassword from '../login/ResetPassword';
import NavBar from './NavBar';

export default function PrivateRoute({auth}) {
    const [showSidebar, toggleSidebar] = useState(false)

    const handleDeconnect = () => {
        axios.post('/api/logout', {}, useToken())
        .then((res) => {
            localStorage.removeItem('token')
            window.location.reload()
        })
    }

    const getWindowSize = () => {
        const {innerWidth} = window;
        return (innerWidth > 1200 ? "lg" : innerWidth > 900 ? "md" : "sm");
    }

    const [size, setSize] = useState(getWindowSize())   

    useEffect(() => {
        function handleWindowResize() {
          setSize(getWindowSize());
        }
    
        window.addEventListener('resize', handleWindowResize);
    
        return () => {
          window.removeEventListener('resize', handleWindowResize);
        }

    }, [size])

    return (
        <div>
            <NavBar auth={auth} toggleSidebar={toggleSidebar} size={size}/>
            {
                ["xg", "lg"].includes(size) ? 
                    <Sidebar auth={auth} disconnectUser={handleDeconnect}/>
                : showSidebar ?
                    <SidebarModal auth={auth} closeSidebar={() => toggleSidebar(false)} disconnectUser={handleDeconnect}/>
                : 
                    <></>                
            }
            <div style={{width: '100%', paddingLeft: (["xg", "lg"].includes(size) ? "280px" : "0px")}}>
                {
                    auth.must_change_password ?
                        <ResetPassword auth={auth}/>
                    :
                        <Outlet/>
                }
            </div>
        </div>
    );
}