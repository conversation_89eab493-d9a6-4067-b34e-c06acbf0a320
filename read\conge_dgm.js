
const Excel = require("exceljs")
const mysql = require('mysql')
const moment = require('moment')

const {db_config_admin, db_config_ovh} = require("../auth")
const pool_admin = mysql.createPool(db_config_admin)
const pool_ovh = mysql.createPool(db_config_ovh)

var workbook = new Excel.Workbook()
workbook.xlsx.readFile("./read/province.xlsx")
.then(function() {
    console.log("begin...\n------------------------------------------------------------")
        var worksheet = workbook.getWorksheet("CONGE");
        const rows = [];
        worksheet.eachRow(function(row) {
            if(row.values[1]) rows.push(row)
        });
        updateRow(rows, 0)
})
const sqlSelectEmploye = "SELECT a.id, a.date_confirmation, a.real_site_id FROM employes a WHERE a.numero_employe = ? and a.nom like ?"
const sqlUpdateEmbaucheEmploye = "UPDATE employes SET date_confirmation = ? WHERE id= ?"
const sqlSelectConge = "SELECT depart, retour FROM absences WHERE status = 'done' and type_absence = 'conge' and employe_id = ?"
const sqlInsertConge = "INSERT INTO absences (type_absence, status, user_id, created_at, updated_at, depart, retour, employe_id, site_id) " +
                    "VALUES ('conge', 'done', 191, '2022-01-01 00:00:00', '2022-01-01 00:00:00', '2022-01-01 06:00:00', ?, ?, ?)"
function updateRow(rows, index){
    if(index < rows.length){
        const row = rows[index]
        const numero = row.values[1]
        const nom = row.values[2]
        const dateEmbauche = moment(row.values[4], "DD/MM/YYYY")
        const conge_restant = Number.parseFloat(row.values[5]) + 2.5
        pool_ovh.query(sqlSelectEmploye, [numero, nom.toString().replace(/ +/g, " ").trim()], (err, result) => {
            if(err){
                console.error(err)
                setTimeout(() => updateRow(rows, index), 500)
            }
            else if(result.length == 1){
                const employe = result[0]
                if(dateEmbauche.format("YYYY-MM-DD") != moment(employe.date_confirmation).format("YYYY-MM-DD")){
                    console.log(numero + " " + nom + " " + " " + dateEmbauche.format('DD/MM/YYYY') + " : date embauche incorrect")
                    pool_ovh.query(sqlUpdateEmbaucheEmploye, [dateEmbauche.format("YYYY-MM-DD"), employe.id], (err, result) => {
                        if(err){
                            console.error(err)
                            setTimeout(() => updateRow(rows, index), 500)
                        }
                        else {
                            setTimeout(() => updateRow(rows, index+1), 200)
                        }
                    })
                }
                else {
                    pool_admin.query(sqlSelectConge, [employe.id], (err, conges) => {
                        if(err){
                            console.error(err)
                            setTimeout(() => updateRow(rows, index), 500)
                        }
                        else {
                            /*
                            const droit = moment().diff(moment(employe.date_confirmation), "days")  / 30.5 * 2.5
                            let hours = 0
                            conges.forEach(cg => {
                                hours += moment(cg.retour).diff(moment(cg.depart), "hours");
                            });
                            const conge_pris = hours / 24
                            console.log("conge pris: " + conge_pris + ", droit: " + droit + ", conge restant: " + conge_restant)
                            let diff = droit - conge_pris - conge_restant
                            let time = " 06:00:00"
                            if(diff < 0.5)
                                diff = 0
                            else if((diff % 1) >= 0.5){
                                diff = Math.round(diff) + 0.5
                                time = " 18:00:00"
                            }
                            console.log("difference: " + diff)
                            if(diff > 0) {
                                diff = Math.round(diff)
                                const arrived = moment("2022-01-01").add(diff, "days").format("YYYY-MM-DD") + time
                                pool_admin.query(sqlInsertConge, [arrived, employe.id, employe.real_site_id], (err, result) => {
                                    if(err){
                                        console.error(err)
                                        setTimeout(() => updateRow(rows, index), 500)
                                    }
                                    else
                                        setTimeout(() => updateRow(rows, index+1), 100)
                                })
                            }
                            else
                            */
                                setTimeout(() => updateRow(rows, index+1), 100)
                        }
                    })
                }
            }
            else {
                console.log(numero + " " + nom + " " + " " + dateEmbauche.format('DD/MM/YYYY'))
                setTimeout(() => updateRow(rows, index+1), 200)
                
            }
        })
    }
    else {
        console.log("process done!!!")
        process.exit(1)
    }
}