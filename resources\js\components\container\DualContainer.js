import { Children } from "react";

export default function DualContainer({children}) {
    return (
        <div className="dual-container">
            {
                Children.map(children, (child, index) =>
                    <>
                        {
                            index == 0 ?
                                <div className="cell cell-left">
                                    {child}
                                </div>
                            :
                                <div className="cell cell-right">
                                    {child}
                                </div>

                        }
                    </>
                )
            }
        </div>
    )
}