import axios from 'axios'
import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import InputText from '../input/InputText';
import InputDate from '../input/InputDate';
import moment from 'moment';

export default function BlacklistEmployeModal({closeModal, updateData, employe}) {
    const [observation, setObservation] = useState("");
    const [date, setDate] = useState(null);
    const [submitDisabled, setSubmitDisabled] = useState(true);

    const handleOk = () => {
        const data = {
            observation: observation,
            date: moment(date).format('YYYY-MM-DD')
        }
        axios.post("/api/employe/blacklist/" + employe.id, data, useToken())
        .then(res => {
            updateData()
        })
    }
    
    useEffect(() => {
        setSubmitDisabled(!(observation && date))
    }, [observation, date]);
    
    return <div className='modal'>
        <div>
            <h2>Blacklist</h2>
            <InputDate
                required
                label="Date"
                value={date} 
                onChange={setDate}/>
            <InputText
                required
                type="text"
                label="Observation"
                value={observation}
                onChange={setObservation}/>
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Enregistrer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>;
}