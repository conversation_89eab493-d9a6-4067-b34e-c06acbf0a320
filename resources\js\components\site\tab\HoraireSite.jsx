import React, { useEffect, useState } from 'react';
import LoadingPage from '../../loading/LoadingPage';
import '../../planning/calendar.css';
import './horaire.css'
import EditHoraireSite from './EditHoraireSite';

export default function HoraireSite({ auth, value, updateData, site }) {
    const [isLoading, toggleLoading] = useState(false)
    const [error, setError] = useState('')
    const [showAddModal, toggleAddModal] = useState(false)
    const [horaires, setHoraires] = useState(null)
    const header = window.innerWidth > 395 ? ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim', 'F<PERSON><PERSON><PERSON>'] : ['L', 'M', 'M', 'J', 'V', 'S', 'D', 'F'];
    // const backgroundColorZero = ['white']
    const backgroundColor = ['indigo', 'bg-orange', 'purple', 'green']
    const allUniqueNumbers = []
    const [numberWithColor, setNumberWithColor] = useState([])

    const soustraireMultipleDe4 = (n) => {
        let multipleDe4 = Math.floor(n / 4) * 4;
        return n - multipleDe4;
    }

    const isInclude = (value) => {
        const tempNumberColor = []
        if(value > 0 && !allUniqueNumbers.includes(value)) {
            allUniqueNumbers.push(value)
            allUniqueNumbers.sort((a, b) => a - b);
        }
        if(allUniqueNumbers.length <= backgroundColor.length) {
            for(let i = 0; i < allUniqueNumbers.length; i++) {
                if(!tempNumberColor.find(n => n.value == allUniqueNumbers[i]))
                    tempNumberColor.push({color: backgroundColor[i], value: allUniqueNumbers[i]})
            }
            setNumberWithColor(tempNumberColor)
        }

        else if(allUniqueNumbers.length > backgroundColor.length) {
            for(let i = 0; i < allUniqueNumbers.length; i++) {
                if(!tempNumberColor.find(n => n.value == allUniqueNumbers[i])){
                    if(i < backgroundColor.length) {
                        tempNumberColor.push({color: backgroundColor[i], value: allUniqueNumbers[i]})
                    }
                    else {
                        tempNumberColor.push({color: backgroundColor[soustraireMultipleDe4(i)], value: allUniqueNumbers[i]})
                    }
                }

            }
            setNumberWithColor(tempNumberColor)
        }
    }
    useEffect(() => {
        if(site.horaires){
            const currentHoraire = site.horaires
            isInclude(currentHoraire.day_1) 
            isInclude(currentHoraire.day_2) 
            isInclude(currentHoraire.day_3) 
            isInclude(currentHoraire.day_4) 
            isInclude(currentHoraire.day_5) 
            isInclude(currentHoraire.day_6) 
            isInclude(currentHoraire.day_0) 
            isInclude(currentHoraire.day_ferie) 
            isInclude(currentHoraire.night_1)
            isInclude(currentHoraire.night_2)
            isInclude(currentHoraire.night_3)
            isInclude(currentHoraire.night_4)
            isInclude(currentHoraire.night_5)
            isInclude(currentHoraire.night_6)
            isInclude(currentHoraire.night_0)
            isInclude(currentHoraire.night_ferie)

            const days = [currentHoraire.day_1, currentHoraire.day_2, currentHoraire.day_3, currentHoraire.day_4, currentHoraire.day_5, currentHoraire.day_6, currentHoraire.day_0, currentHoraire.day_ferie]
            const nights = [currentHoraire.night_1, currentHoraire.night_2, currentHoraire.night_3, currentHoraire.night_4, currentHoraire.night_5, currentHoraire.night_6, currentHoraire.night_0, currentHoraire.night_ferie]
            setHoraires({
                day: days,
                night: nights
            })
        }
    }, [site]);
    
    return (
        <>
            <div className='card-container'>
                {
                    isLoading ?
                        <LoadingPage />
                        :
                        <>
                            {(["resp_op", "room"].includes(auth.role) || auth.id == site.resp_sup_id) && (site.group_planing_id == site.idsite || site.group_planing_id == null) &&
                                <div className='tab-list-action'>
                                    <div className='action-container'>
                                        <span>
                                            <span onClick={() => toggleAddModal(true)}>
                                                Mettre à jour
                                            </span>
                                        </span>
                                    </div>
                                </div>
                            }
                            {
                                horaires &&
                                <div className={(["resp_op"].includes(auth.role) || auth.id == site.resp_sup_id) ?"card-container" : ""}>
                                    <div className="calendar-header">
                                        {header.map((hr) => (
                                            <div key={hr} className="calendar-header-day">
                                                {hr}
                                            </div>
                                        ))}
                                    </div>
                                    <div className="calendar-header">
                                        {horaires.day.map((hr, index) => (
                                            <div key={index} className={"horaire-content " + numberWithColor.find(nc => nc.value == hr)?.color } style={{ color:'#fafafa', fontWeight:'bold', fontSize:'1.7em'}}>
                                                {hr}
                                            </div>
                                        ))}
                                    </div>
                                    <div className="calendar-header">
                                        {horaires.night.map((hr, index) => (
                                            <div key={index} 
                                            className={"horaire-content " + numberWithColor.find(nc => nc.value == hr)?.color }
                                                style={{ color:'#fafafa', fontWeight:'bold', fontSize:'1.7em'}}
                                            >{hr}</div>
                                        ))}
                                    </div>
                                </div>
                            }
                        </>
                }
            </div>
            {
                showAddModal &&
                <EditHoraireSite closeModal={() => toggleAddModal(false)} horaires={horaires} site={site} updateData={updateData}/>
            }
        </>
    );
};