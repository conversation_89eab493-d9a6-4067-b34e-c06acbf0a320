import {useState,useRef} from 'react'
import {IoMdArrowDropdown,IoMdArrowDropup} from 'react-icons/io'

import useClickOutside from '../util/useClickOutside'

export default function InputSelectSearch({label,selected, setSelected, className, options,required}) {
    const [isActive, setIsActive] = useState(false)
    const selectRef = useRef(null)
    useClickOutside(selectRef, () => setIsActive(false))

    return (
        <div className='input-container'>
            <label>{label} {required && <span className='danger'>*</span>}</label>
            <div className='select-search-container'>
                <div className={'select-input ' + className} ref={selectRef}>
                    <div className={isActive ? "select-placeholder active-select" : "select-placeholder" }
                        onClick={(e) => setIsActive(!isActive)}>
                        <span>{selected ? selected : ''}</span>
                        <span>{isActive ? <IoMdArrowDropup/> : <IoMdArrowDropdown/>}</span>
                    </div>
                    {isActive && 
                        <div className="select-content">
                            {options.map((option,index) => (
                                <div 
                                    key={index}
                                    onClick={(e) => {
                                        setSelected(option)
                                        setIsActive(false)
                                    }}
                                    className="select-option"
                                >
                                    {option && (option.label ? option.label : option)}
                                </div>
                            ))}
                            
                        </div>
                    }
                </div>
            </div>
        </div>
    )
}
