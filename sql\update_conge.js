const moment = require('moment')
const mysql = require('mysql')
const fs = require("fs");

moment.locale('fr')
const auth = require("../auth")

const db_config_admin = auth.db_config_admin
const pool_admin = mysql.createPool(db_config_admin)

const sqlSelectAgent = "select id, nom, date_confirmation, nb_conge from employes " +
    "where (nb_conge is not null and nb_conge != 0)"
const sqlSelectConge = "select id, depart, retour from absences where status = 'done' and type_absence = 'conge' and employe_id = ?"
const sqlInsertCongeCompensation = "insert into absences (type_absence, user_id, status, employe_id, depart, retour) " +
    "values ('conge', 191, 'done', ?, '2022-01-01 06:00:00', ?)"

function updateCongeById(agents, index){
    if(index < agents.length){
        const agent = agents[index]
        pool_admin.query(sqlSelectConge, [agent.id], async (err, conges) => {
            if(err){
                console.log("err found")
                console.error(err)
            }
            else {
                pool_admin.query(sqlSelectConge, [agent.id], async (err, conges) => {
                    if(err){
                        console.log("err found")
                        console.error(err)
                    }
                    else {
                        let congePris = 0
                        conges.forEach(cg => {
                            congePris = congePris + ((moment.duration(moment(cg.retour).diff(moment(cg.depart)))).asHours() / 24)
                        });
                        const realNbConge = agent.nb_conge + 1.5
                        const droitConge = (moment.duration(moment().diff(moment(agent.date_confirmation)))).asDays() / 30.5 * 2.5
                        let droitArrondit = 0;
                        if((droitConge % 1) >= 0.5)
                            droitArrondit = Math.round(droitConge) + 0.5
                        else
                            droitArrondit = Math.round(droitConge)
                        
                        const compensationConge = droitArrondit - congePris - realNbConge 
                        
                        console.log("\n-----")
                        console.log(agent.nom)
                        console.log(moment(agent.date_confirmation).format("DD-MM-YY"))
                        console.log("nb_conge: ", realNbConge, "\ndroit: ", droitArrondit, "\nconge pris: ", congePris, "\ncompensation: ", compensationConge)
                        
                        if(compensationConge >= 0.5) {
                            const retour = moment("2022-01-01 06:00:00").add(compensationConge*24, "hour").format("YYYY-MM-DD HH:mm:ss")
                            console.log(retour)
                            pool_admin.query(sqlInsertCongeCompensation, [agent.id, retour], async (err, res) => {
                                if(err) {
                                    console.log(err)
                                    setTimeout(() => {
                                        updateCongeById(agents, index)
                                    }, 500)
                                }
                                else {
                                    console.log("compensation inserted")
                                    setTimeout(() => {
                                        updateCongeById(agents, index+1)
                                    }, 200)
                                }
                            })
                        }
                        else
                            setTimeout(() => {
                                updateCongeById(agents, index+1)
                            }, 200)
                    }
                })
            }
        })
    }
    else {
        console.log("process done!")
        process.exit()
    }
}
function updateData(){
    pool_admin.query(sqlSelectAgent, [], async (err, agents) => {
        if(err){
            console.error(err)
        }
        else {
            console.log("agent to sync: " + agents.length)
            updateCongeById(agents, 0)
        }
    })
}

updateData()