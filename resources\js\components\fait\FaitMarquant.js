import React, { useEffect, useState } from 'react';
import { Link, useLocation  } from "react-router-dom";
import { FiMoreVertical } from 'react-icons/fi';
import moment from 'moment'
moment.locale('fr')

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import HeaderFaitMarquant from './HeaderFaitMarquant';

export default function FaitMarquant({auth, faits, setFaits, currentId, setCurrentId}) {
    const locationSearch = useLocation().search;
    const params = new URLSearchParams(locationSearch)
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'Réf<PERSON><PERSON>ce', name: 'id', type:'number'},
        {label: 'Non lu', name: 'unread', type:'string'},
        {label: 'Objet', name: 'objet', type:'string'},
        {label: 'Contenu', name: 'message_content', type: 'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Site', name: 'site_id', type:'number'},
    ]
    if(auth.role != 'superviseur')
        searchItems.push({label: 'Utilisateur', name: 'user_id', type:'number'})

    const handleSeenAll = () => {
        toggleLoading(true)
        axios.post('/api/seen/fait_marquant_all/', {ids: faits.map(f => f.id)}, useToken())
        .then((res) => {
            if(res.data.success){
                updateData(true)
            }
            else 
                toggleLoading(false)
        })
        .catch(() => {
            toggleLoading(false)
        })
    }

    const updateData = (initial) => {
        let isMounted = true;
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            params.set("offset", 0)
        }
        else
            params.set("offset", faits.length)
        axios.get('/api/fait_marquant?' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setFaits(res.data.faits)
                    else {
                        const list = faits.slice().concat(res.data.faits)
                        setFaits(list)
                    }
                    setDataLoaded(res.data.faits.length < 10)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    const stayOnSite = (start, end) => {
        if (start == null || end == null)
            return ""
        const diffMinutes = moment(end).diff(moment(start), 'minutes')
        if (diffMinutes < 60) {
            return `${diffMinutes} minutes`
        } else {
            const hours = Math.floor(diffMinutes / 60)
            const minutes = diffMinutes % 60
            if (minutes == 0) {
                return `${hours} heure`
            } else if (minutes == 1) {
                return (`${hours} heure ${minutes} minute`)
            } else {
                return (`${hours} heure ${minutes} minutes`)
            }
        }
    }

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>
                    Fait marquant
                </h2>
                <div>
                    {
                        (auth.role !='superviseur' && params.get("unread") && faits.length > 0) && 
                        <span className='btn btn-outline-secondary pointer' onClick={handleSeenAll}>Marquer tout comme lu</span>
                    }
                    {   
                        ['superviseur', 'resp_sup', "resp_op", 'room'].includes(auth.role) && 
                        <Link className='btn btn-primary' to="/fait-marquant/add">Nouveau fait</Link>
                    }
                </div>
            </div>
            <SearchBar listItems={searchItems}/>
            {
                faits.length == 0 ? 
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                    <InfiniteScroll
                        dataLength={faits.length}
                        next={fetchMoreData}
                        hasMore={!allDataLoaded}
                        loader={<LoadingPage/>}
                    >
                        {
                            faits.map((ft, index) => (
                                <div className={`card-container ${currentId && currentId == ft.id ? 'selected' : ''}`} key={index}>
                                    <div className='badge-container'>
                                        <span>
                                            {
                                                auth.role != 'superviseur' &&
                                                <span className={"badge-outline badge-outline-" + (ft.send_email ? "cyan" : ft.seen ? "green": "purple")}>
                                                    { ft.send_email ? "Message envoyé" : ft.seen ? "Lu" : "Non lu" }
                                                </span>
                                            }
                                            {
                                                ft.start != null && ft.end != null &&
                                                <span className='badge-outline badge-outline-green'>
                                                    {stayOnSite(ft.start, ft.end)}
                                                </span>
                                            }
                                            {
                                                ft.user_role != "room" && (
                                                    (ft.start == null && ft.end == null) ||
                                                    (ft.start == null && ft.end != null) ||
                                                    (ft.start != null && ft.end == null)
                                                ) && (
                                                    <span className='badge-outline badge-outline-red'>
                                                        ndf : {ft.start == null && ft.end == null ? "0/2" : "1/2"}
                                                    </span>
                                                )
                                            }
                                            {
                                                ft.nb_pj > 0 &&
                                                <span className="badge-outline">
                                                    Pièce jointe : {ft.nb_pj}
                                                </span>
                                            }
                                        </span>
                                        <span>
                                            <FiMoreVertical className='pointer' onClick={() => setCurrentId(ft.id)} size={20} color="#888"/>
                                        </span>
                                    </div>
                                    <HeaderFaitMarquant auth={auth} data={ft}/>
                                </div>
                            ))
                        }
                    </InfiniteScroll>
            }
        </div>
    } </>
}