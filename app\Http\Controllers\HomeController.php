<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Type\TrueType;
use Carbon\Carbon;

class HomeController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }

    public static function getNbArticle($dotation) {
        $articles = 0;
        foreach($dotation as $dotation) {
            $articles += $dotation->nbArticle;
        }
        return $articles;
    }

    public static function estRetourne($dotation_sortie, $dotation_entre) {
        if (empty($dotation_sortie) && empty($dotation_entre)) {
            return true;
        }
        else if (!empty($dotation_sortie) && empty($dotation_entre)) {
            return false;
        } else if (!empty($dotation_sortie) && !empty($dotation_entre)) {
            if (self::getNbArticle($dotation_sortie) == HomeController::getNbArticle($dotation_entre)) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    public function getDayOrNightDate(){
        if(new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
                new \DateTime < (new \DateTime)->setTime(17, 50, 0))
            return (new \DateTime)->setTime(05, 0, 0)->format('Y-m-d H:i:s');
        else if(new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(17, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(17, 00, 0)->format('Y-m-d H:i:s');
    }
    public function index(Request $request)
    {
        $role = $request->user()->role;
        $auth_id = $request->user()->id;
        $response = [];
        if($role == "validateur") {
            $response['nb_sanction_validation'] = DB::select("SELECT count(id) as 'nb' from sanctions where `status`='validation'")[0]->nb;
            $response['nb_prime_validation'] = DB::select("SELECT count(id) as 'nb' from primes where `status`='validation'")[0]->nb;
            $response['nb_equipement_validation'] = DB::select("SELECT count(id) as 'nb' from equipements where `status`='validation'")[0]->nb;
            $response['nb_conge_validation'] = DB::select("SELECT count(id) as 'nb' from absences where type_absence='conge' and `status`='validation'")[0]->nb;
            $response['nb_permission_validation'] = DB::select("SELECT count(id) as 'nb' from absences where type_absence='permission' and `status`='validation'")[0]->nb;
            $response['nb_service24_validation'] = DB::select("SELECT count(id) as 'nb' from service24s where `status`='validation'")[0]->nb;

            $response['nb_sav_validation'] = DB::select("SELECT count(s.id) as 'nb' FROM sav s WHERE s.status = ?", ['validation'])[0]->nb;
            $response['nb_sav_tag_validation'] = DB::select("SELECT count(s.id) as 'nb' FROM sav s WHERE s.status = ? and s.type_sav = 'tag'", ['validation'])[0]->nb;
            $response['nb_flotte_validation'] = DB::select("SELECT count(f.id) as 'nb' FROM flottes f WHERE f.status = ?", ['validation'])[0]->nb;
            $response['nb_da_validation'] = DB::select("SELECT count(appro.id) as 'nb' FROM approvisionnements appro WHERE appro.status = ?", ['validation'])[0]->nb;
            $response['nb_misapied_demande'] = DB::select("SELECT count(ab.id) as 'nb' FROM absences ab WHERE ab.type_absence='mis_a_pied' and ab.status = 'demande'")[0]->nb;
            $currentDate = new \DateTime();
            $response['nb_pv_validation'] = DB::select("SELECT count(p.id) as 'nb' FROM part_variables p WHERE p.status = ?", ['validation'])[0]->nb;

            $now = new \DateTime();
            $date_paie = (new \DateTime())->setDate($now->format('Y'), $now->format('m'), 20)->setTime(0, 0, 0);;
            $date_limit = (new \DateTime())->setDate($now->format('Y'), $now->format('m'), 1)->setTime(0, 0, 0)->add(new \DateInterval('P1M'));
            $response['nb_pv_non_fait'] = !($now > $date_paie && $now < $date_limit) ? 0 :
                DB::select("SELECT count(emp.id) as nb
                    FROM employes emp
                    LEFT JOIN part_variables pv ON pv.employe_id = emp.id and pv.date_paie = ?
                    WHERE (emp.soft_delete IS NULL OR emp.soft_delete = 0)
                    AND emp.part_variable = 1
                    AND emp.resp_part_id IS NOT NULL
                    AND pv.id IS NULL",
                    [$date_paie->format('Y-m-d')]
                )[0]->nb;
                $articles = DB::select("SELECT distinct aeq.equipement_id
                from article_equipements aeq
                left join articles ac on ac.name = aeq.article
                where ac.service = 'tenue' and aeq.done is null");
            $response['nb_equipement_tenue_demande'] = count($articles);

            $a_recuperer = 0;

                $employe_archive = DB::select("SELECT DISTINCT meq.employe_id FROM mouvement_equipement meq
                LEFT JOIN employes emp ON meq.employe_id = emp.id
                WHERE emp.soft_delete = 1
                AND (meq.`status` is null OR meq.`status` = 'en cours')");

            if (count($employe_archive) > 0) {
                foreach($employe_archive as $employe) {
                    $dotation_archive_sortie = DB::select("SELECT meq.id, meq.employe_id, COUNT(leq.id) as nbArticle
                    FROM mouvement_equipement meq
                    LEFT JOIN employes emp ON meq.employe_id = emp.id
                    LEFT JOIN ligne_equipement leq ON meq.id = leq.mouvement_equipement_id
                    WHERE meq.employe_id = ?
                    AND meq.type_mouvement = 'sortie'
                    AND (meq.`status` is null OR meq.`status` = 'en cours')
                    GROUP BY meq.id, meq.employe_id", [$employe->employe_id]);

                    $dotation_archive_entre = DB::select("SELECT meq.id, meq.employe_id, COUNT(leq.id) as nbArticle
                    FROM mouvement_equipement meq
                    LEFT JOIN employes emp ON meq.employe_id = emp.id
                    LEFT JOIN ligne_equipement leq ON meq.id = leq.mouvement_equipement_id
                    WHERE meq.employe_id = ?
                    AND meq.type_mouvement = 'entré'
                    GROUP BY meq.id, meq.employe_id", [$employe->employe_id]);

                    if (!self::estRetourne($dotation_archive_sortie, $dotation_archive_entre)) {
                        if (count($dotation_archive_sortie) > 1) {
                            $a_recuperer += count($dotation_archive_sortie);
                        } else {
                            $a_recuperer += 1;
                        }
                    }
                }
                $response['nb_a_recuperer'] = $a_recuperer;
            }

            $articles = DB::select("SELECT distinct aeq.equipement_id
            from article_equipements aeq
            left join articles ac on ac.name = aeq.article
            where ac.service = 'achat' and aeq.done is null");
            $response['nb_equipement_demande'] = count($articles);
            $response['sans_employe'] = DB::select("SELECT count(eq.id) as nombre FROM equipements eq WHERE eq.recruiting = 1 AND eq.employe_id IS NULL")[0]->nombre;

            $response['nb_demande_equipement_pour_validation'] = DB::select("SELECT count(eq.id) as nombre FROM equipements eq 
            LEFT JOIN article_equipements aeq ON aeq.equipement_id = eq.id 
            WHERE eq.isValidated = 0 and eq.`status` != 'done' and aeq.article = 'botte_de_pluie'")[0]->nombre;

            $response['nb_recrutement_en_cours'] = DB::select("SELECT count(id) as nombre FROM recrutements WHERE status = 'En cours'")[0]->nombre;
            $response['nb_recrutement_non_abouti'] = DB::select("SELECT count(id) as nombre FROM recrutements WHERE soft_delete = 1")[0]->nombre;
            
            $lastWeekStart = Carbon::now()->subWeek()->startOfWeek();
            $lastWeekEnd = Carbon::now()->subWeek()->endOfWeek();

            $thisWeekStart = Carbon::now()->startOfWeek();
            $thisWeekEnd = Carbon::now()->endOfWeek();

            $response['nb_recrute_last_week'] = DB::select("SELECT count(id) as nombre FROM recrutements WHERE status = 'Recruté' and created_at BETWEEN ? AND ?",[$lastWeekStart, $lastWeekEnd])[0]->nombre;
            $response['nb_recrute_this_week'] = DB::select("SELECT count(id) as nombre FROM recrutements WHERE status = 'Recruté' and created_at BETWEEN ? AND ?",[$thisWeekStart, $thisWeekEnd])[0]->nombre;
        }
        else if($role == "tenue") {
            $articles = DB::select("SELECT distinct aeq.equipement_id
                from article_equipements aeq
                left join articles ac on ac.name = aeq.article
                where ac.service = 'tenue' and aeq.done is null");
            $response['nb_equipement_tenue_demande'] = count($articles);

            $a_recuperer = 0;

                $employe_archive = DB::select("SELECT DISTINCT meq.employe_id FROM mouvement_equipement meq
                LEFT JOIN employes emp ON meq.employe_id = emp.id
                WHERE emp.soft_delete = 1
                AND (meq.`status` is null OR meq.`status` = 'en cours')");

            if (count($employe_archive) > 0) {
                foreach($employe_archive as $employe) {
                    $dotation_archive_sortie = DB::select("SELECT meq.id, meq.employe_id, COUNT(leq.id) as nbArticle
                    FROM mouvement_equipement meq
                    LEFT JOIN employes emp ON meq.employe_id = emp.id
                    LEFT JOIN ligne_equipement leq ON meq.id = leq.mouvement_equipement_id
                    WHERE meq.employe_id = ?
                    AND meq.type_mouvement = 'sortie'
                    AND (meq.`status` is null OR meq.`status` = 'en cours')
                    GROUP BY meq.id, meq.employe_id", [$employe->employe_id]);

                    $dotation_archive_entre = DB::select("SELECT meq.id, meq.employe_id, COUNT(leq.id) as nbArticle
                    FROM mouvement_equipement meq
                    LEFT JOIN employes emp ON meq.employe_id = emp.id
                    LEFT JOIN ligne_equipement leq ON meq.id = leq.mouvement_equipement_id
                    WHERE meq.employe_id = ?
                    AND meq.type_mouvement = 'entré'
                    GROUP BY meq.id, meq.employe_id", [$employe->employe_id]);

                    if (!self::estRetourne($dotation_archive_sortie, $dotation_archive_entre)) {
                        if (count($dotation_archive_sortie) > 1) {
                            $a_recuperer += count($dotation_archive_sortie);
                        } else {
                            $a_recuperer += 1;
                        }
                    }
                }
                $response['nb_a_recuperer'] = $a_recuperer;
            }

            $response['sans_employe'] = DB::select("SELECT count(eq.id) as nombre FROM equipements eq WHERE eq.recruiting = 1 AND eq.employe_id IS NULL")[0]->nombre;
            $response['demande_valide'] = DB::select("SELECT count(eq.id) as nombre FROM equipements eq WHERE eq.isValidated = 1 and eq.status != 'done'")[0]->nombre;
        }
        else if($role == "achat") {
            $articles = DB::select("SELECT distinct aeq.equipement_id
                from article_equipements aeq
                left join articles ac on ac.name = aeq.article
                where ac.service = 'achat' and aeq.done is null");
            $response['nb_equipement_demande'] = count($articles);
        }
        else if($role == "daf" || $role == "compta") {
            $da = DB::select("SELECT appro.status
                FROM approvisionnements appro
                wHERE appro.status in ('validation', 'demande', 'traite')", []);
            $nb_da_validation = 0;
            $nb_da_valide = 0;
            $nb_da_traite = 0;

            foreach ($da as $row) {
                if($row->status == 'validation')
                    $nb_da_validation++;
                else if($row->status == 'demande')
                    $nb_da_valide++;
                else if($row->status == 'traite')
                    $nb_da_traite++;
            }
            $response['nb_da_validation'] = $nb_da_validation;
            $response['nb_da_valide'] = $nb_da_valide;
            $response['nb_da_traite'] = $nb_da_traite;
        }
        else if($role == "tech") {
            $response['nb_sav_demande'] = DB::select("SELECT count(s.id) as 'nb' FROM sav s WHERE s.status = ?", ['demande'])[0]->nb;
            $response['nb_sav_tag_demande'] = DB::select("SELECT count(s.id) as 'nb' FROM sav s WHERE s.status = ? and s.type_sav = 'tag'", ['demande'])[0]->nb;
            $response['nb_flotte_demande'] = DB::select("SELECT count(f.id) as 'nb' FROM flottes f WHERE f.status = ?", ['demande'])[0]->nb;
        }
        else if($role == "electronique") {
            $response['nb_biometrique_demande'] = DB::select("SELECT count(s.id) as 'nb' FROM sav s WHERE s.status = ? and s.type_sav = 'biometrique'", ['demande'])[0]->nb;
        }
        else if($role == "rh" || $role == "resp_rh" ){
            $response['nb_sanction_demande'] = DB::select("SELECT count(id) as 'nb' from sanctions where `status`='demande'")[0]->nb;
            $response['nb_conge_demande'] = DB::select("SELECT count(id) as 'nb' from absences where type_absence = 'conge' and `status`='demande'")[0]->nb;
            $response['nb_permission_demande'] = DB::select("SELECT count(id) as 'nb' from absences where type_absence = 'permission' and `status`='demande'")[0]->nb;
            $response['nb_reclamation_demande'] = DB::select("SELECT count(id) as 'nb' from reclamations")[0]->nb;
            $response['nb_misapied_demande'] = DB::select("SELECT count(ab.id) as 'nb' FROM absences ab WHERE type_absence = 'mis_a_pied' and ab.status = 'demande'")[0]->nb;
            if($role == "resp_rh"){
                $response['nb_prime_demande'] = DB::select("SELECT count(id) as 'nb' from primes where `status`='demande'")[0]->nb;
                $response['nb_avance_demande'] = DB::select("SELECT count(id) as 'nb' from avances where `status`='demande'")[0]->nb;
                $response['nb_pv_validation'] = DB::select("SELECT count(p.id) as 'nb' FROM part_variables p WHERE p.status = ?", ['validation'])[0]->nb;
                $now = new \DateTime();
                $date_paie = (new \DateTime())->setDate($now->format('Y'), $now->format('m'), 20)->setTime(0, 0, 0);;
                $date_limit = (new \DateTime())->setDate($now->format('Y'), $now->format('m'), 1)->setTime(0, 0, 0)->add(new \DateInterval('P1M'));
                $response['nb_pv_non_fait'] = !($now > $date_paie && $now < $date_limit) ? 0 :
                    DB::select("SELECT count(emp.id) as nb
                        FROM employes emp
                        LEFT JOIN part_variables pv ON pv.employe_id = emp.id and pv.date_paie = ?
                        WHERE (emp.soft_delete IS NULL OR emp.soft_delete = 0)
                        AND emp.part_variable = 1
                        AND emp.resp_part_id IS NOT NULL
                        AND pv.id IS NULL",
                        [$date_paie->format('Y-m-d')]
                    )[0]->nb;
            }
            $response['nb_recrutement_en_cours'] = DB::select("SELECT count(id) as nombre FROM recrutements WHERE status = 'En cours'")[0]->nombre;
            $response['nb_recrutement_non_abouti'] = DB::select("SELECT count(id) as nombre FROM recrutements WHERE soft_delete = 1")[0]->nombre;
            
            $lastWeekStart = Carbon::now()->subWeek()->startOfWeek();
            $lastWeekEnd = Carbon::now()->subWeek()->endOfWeek();

            $thisWeekStart = Carbon::now()->startOfWeek();
            $thisWeekEnd = Carbon::now()->endOfWeek();

            $response['nb_recrute_last_week'] = DB::select("SELECT count(id) as nombre FROM recrutements WHERE status = 'Recruté' and created_at BETWEEN ? AND ?",[$lastWeekStart, $lastWeekEnd])[0]->nombre;
            $response['nb_recrute_this_week'] = DB::select("SELECT count(id) as nombre FROM recrutements WHERE status = 'Recruté' and created_at BETWEEN ? AND ?",[$thisWeekStart, $thisWeekEnd])[0]->nombre;
        }
        else if($role == "superviseur"){
            $date_now = (new \DateTime())->format('Y-m');
            $response['nb_planning_non_lu'] = DB::select("SELECT count(pl.id) as 'nb'
                FROM plannings pl
                left join sites st on st.idsite = pl.site_id
                where st.superviseur_id = ?
                and (pl.seen_at is null or pl.seen_at < pl.updated_at)
                and pl.date_planning = '$date_now' and pl.user_id != ?", [$request->user()->id, $request->user()->id])[0]->nb;
            
            $a_recuperer = 0;

                $employe_archive = DB::select("SELECT DISTINCT meq.employe_id FROM mouvement_equipement meq
                LEFT JOIN employes emp ON meq.employe_id = emp.id
                WHERE emp.soft_delete = 1
                AND (meq.`status` is null OR meq.`status` = 'en cours')
                AND meq.superviseur_id = ?", [$auth_id]);

            if (count($employe_archive) > 0) {
                foreach($employe_archive as $employe) {
                    $dotation_archive_sortie = DB::select("SELECT meq.id, meq.employe_id, COUNT(leq.id) as nbArticle
                    FROM mouvement_equipement meq
                    LEFT JOIN employes emp ON meq.employe_id = emp.id
                    LEFT JOIN ligne_equipement leq ON meq.id = leq.mouvement_equipement_id
                    WHERE meq.employe_id = ?
                    AND meq.type_mouvement = 'sortie'
                    AND (meq.`status` is null OR meq.`status` = 'en cours')
                    GROUP BY meq.id, meq.employe_id", [$employe->employe_id]);

                    $dotation_archive_entre = DB::select("SELECT meq.id, meq.employe_id, COUNT(leq.id) as nbArticle
                    FROM mouvement_equipement meq
                    LEFT JOIN employes emp ON meq.employe_id = emp.id
                    LEFT JOIN ligne_equipement leq ON meq.id = leq.mouvement_equipement_id
                    WHERE meq.employe_id = ?
                    AND meq.type_mouvement = 'entré'
                    GROUP BY meq.id, meq.employe_id", [$employe->employe_id]);

                    if (!self::estRetourne($dotation_archive_sortie, $dotation_archive_entre)) {
                        if (count($dotation_archive_sortie) > 1) {
                            $a_recuperer += count($dotation_archive_sortie);
                        } else {
                            $a_recuperer += 1;
                        }
                    }
                }
                $response['nb_a_recuperer'] = $a_recuperer;
            }
            $response["sans_employe"] = DB::select("SELECT  count(id) as 'nombre' FROM equipements where recruiting = 1 and employe_id is null and user_id = ?", [$auth_id])[0]->nombre;

            $nb_unread_this_month = 0;
            $nb_unread_last_month = 0;
            $satisfaction_ids = array_column(DB::select("select s.satisfaction_id from seen s
                left join satisfactions sat on sat.id = s.satisfaction_id
                left join sites st on st.idSite = sat.site_id
                where s.satisfaction_id is not null and st.superviseur_id = ?",[$auth_id]), "satisfaction_id");
            if(count($satisfaction_ids) > 0 ) {
                $nb_unread_this_month = DB::select("SELECT count(s.id) as 'nb' FROM satisfactions s
                    LEFT JOIN sites st ON s.site_id = st.idSite
                    WHERE month(s.created_at) = month(now()) 
                    and s.id not in (". implode(",", $satisfaction_ids) .")
                    and st.superviseur_id = ?", [$auth_id])[0]->nb;

                $nb_unread_last_month = DB::select("SELECT count(s.id) as 'nb' FROM satisfactions s
                    LEFT JOIN sites st ON s.site_id = st.idSite
                    WHERE month(s.created_at) = month(now()) - 1 
                    and s.id not in (". implode(",", $satisfaction_ids) .")
                    and st.superviseur_id = ?", [$auth_id])[0]->nb;
            }
            $response['nb_unread_this_month'] = $nb_unread_this_month;
            $response['nb_unread_last_month'] = $nb_unread_last_month;
        }
        else if($role == "admin"){
            $response['nb_users'] = DB::select("SELECT count(u.id) as 'nb' FROM users u WHERE must_change_password = 1")[0]->nb;
            $response['nb_all_users'] = DB::select("SELECT count(u.id) as 'nb' FROM users u")[0]->nb;
        }
        else if($role == "resp_sup"){
            $date_now = (new \DateTime())->format('Y-m');
            $response['nb_planning_non_lu_sup'] = DB::select("SELECT count(pl.id) as 'nb'
                FROM plannings pl
                WHERE (pl.seen_at is null or pl.seen_at < pl.updated_at)
                and pl.user_id = ?
                and pl.date_planning = ? ", [$request->user()->id, $date_now])[0]->nb;
            // $response['nb_planning_to_do'] = DB::select("SELECT count(s.idsite) as 'nb'
            //     FROM sites s
            //     LEFT JOIN plannings pl on s.idsite = pl.site_id and pl.date_planning = ?
            //     where s.resp_sup_id = ?
            //     AND (s.soft_delete is null or s.soft_delete = 0)
            //     AND s.pointage = 1
            //     AND pl.id is null ", [$date_now, $request->user()->id])[0]->nb;
            
            $response['nb_planning_to_do_next'] = DB::select("SELECT count(st.idsite)  as 'nb'
                FROM sites st
                LEFT JOIN plannings pl ON pl.site_id = st.idsite and pl.date_planning = ?
                WHERE (st.soft_delete is null or st.soft_delete = 0)
                AND pl.id is null
                AND st.resp_sup_id = ?
                AND st.pointage = 1",[(new \DateTime())->setDate((int)date('Y'), (int)date('m'), 1)->modify('+1 month')->format('Y-m') , $auth_id])[0]->nb;
            $response['nb_anomalie'] = DB::select("SELECT count(DISTINCT ptg.agent_id) as 'nb'
                FROM planning_pointages ptg
                LEFT JOIN employes emp on emp.id = ptg.agent_id
                LEFT JOIN plannings pl on pl.id = ptg.planning_id
                LEFT JOIN sites st on st.idsite = pl.site_id
                WHERE emp.soft_delete = 1 AND ptg.date_pointage > ? AND st.resp_sup_id = ? ", [(new \DateTime())->format('Y-m-d H:i:s'), $auth_id])[0]->nb;
            $response['nb_anomalie_24h'] = count(DB::select("WITH pointages_consecutifs AS (
                    SELECT
                        ptg.agent_id,
                        ptg.date_pointage,
                        LAG(ptg.date_pointage) OVER (PARTITION BY ptg.agent_id ORDER BY ptg.date_pointage) AS prev_pointage,
                        LEAD(ptg.date_pointage) OVER (PARTITION BY ptg.agent_id ORDER BY ptg.date_pointage) AS next_pointage,
                        CASE
                            WHEN LAG(ptg.date_pointage) OVER (PARTITION BY ptg.agent_id ORDER BY ptg.date_pointage) IS NULL
                                OR TIMESTAMPDIFF(HOUR, LAG(ptg.date_pointage) OVER (PARTITION BY ptg.agent_id ORDER BY ptg.date_pointage), ptg.date_pointage) > 12 THEN 1
                            ELSE 0
                        END AS debut_sequence
                    FROM planning_pointages ptg
                    LEFT JOIN plannings pl ON pl.id = ptg.planning_id
                    LEFT JOIN sites st ON st.idsite = pl.site_id
                    WHERE date_pointage >= ? AND (pl.user_id = ? OR st.resp_sup_id = ?)
                ),
                sequences AS ( SELECT agent_id, date_pointage, SUM(debut_sequence) OVER (PARTITION BY agent_id ORDER BY date_pointage) AS sequence_id FROM pointages_consecutifs )
                SELECT
                    agent_id,
                    MIN(date_pointage) AS debut_sequence,
                    MAX(date_pointage) AS fin_sequence,
                    COUNT(*) AS nombre_pointages
                FROM sequences
                GROUP BY agent_id, sequence_id
                HAVING COUNT(*) > 1
                ORDER BY agent_id, debut_sequence",[(new \DateTime())->format('Y-m-d H:i:s'), $auth_id, $auth_id]));

            $nb_unread_this_month = 0;
            $nb_unread_last_month = 0;
            $satisfaction_ids = array_column(DB::select("select s.satisfaction_id from seen s
                left join satisfactions sat on sat.id = s.satisfaction_id
                left join sites st on st.idSite = sat.site_id
                where s.satisfaction_id is not null and st.superviseur_id = ?",[$auth_id]), "satisfaction_id");
            if(count($satisfaction_ids) > 0 ) {
                $nb_unread_this_month = DB::select("SELECT count(s.id) as 'nb' FROM satisfactions s
                    LEFT JOIN sites st ON s.site_id = st.idSite
                    WHERE month(s.created_at) = month(now()) 
                    and s.id not in (". implode(",", $satisfaction_ids) .")
                    and st.resp_sup_id = ?", [$auth_id])[0]->nb;

                $nb_unread_last_month = DB::select("SELECT count(s.id) as 'nb' FROM satisfactions s
                    LEFT JOIN sites st ON s.site_id = st.idSite
                    WHERE month(s.created_at) = month(now()) - 1 
                    and s.id not in (". implode(",", $satisfaction_ids) .")
                    and st.resp_sup_id = ?", [$auth_id])[0]->nb;
            }
            $response['nb_unread_this_month'] = $nb_unread_this_month;
            $response['nb_unread_last_month'] = $nb_unread_last_month;
        }

        if(in_array($role, ["juridique", "validateur"])) {
            $response['nb_plainte'] = DB::select("SELECT count(sj.id) as 'nb'
                FROM suivi_juridiques sj
                LEFT JOIN plainte_agents j on j.id = sj.plainte_id
                LEFT JOIN (SELECT s.id, s.juridique_id FROM seen s WHERE s.user_id = ?) y ON y.juridique_id = sj.id
                WHERE y.id IS null and sj.plainte_id is not null and sj.user_id != ?", [$auth_id, $auth_id])[0]->nb;
            $response['nb_recouvrement'] = DB::select("SELECT count(sj.id) as 'nb'
                FROM suivi_juridiques sj
                LEFT JOIN juridiques j on j.id = sj.juridique_id
                LEFT JOIN (SELECT s.id, s.juridique_id FROM seen s WHERE s.user_id = ?) y ON y.juridique_id = sj.id
                WHERE y.id IS null and sj.juridique_id is not null and sj.user_id != ?", [$auth_id, $auth_id])[0]->nb;
            $response['nb_recouvrement_late'] = DB::select("SELECT count(id) as 'nb' FROM juridiques j
                WHERE j.etape_id != 11 and j.limit_date < CURDATE()
                and (j.next_suivi is null or j.next_suivi < CURDATE())")[0]->nb;
        }

        if(in_array($role, ["resp_op", "access", "validateur"])){
            $now = new \DateTime();
            if ($request->user()->role != "resp_op") {
                $response['nb_planning_non_fait'] = DB::select("SELECT count(st.idsite)  as 'nb'
                    FROM sites st
                    LEFT JOIN plannings pl ON pl.site_id = st.idsite and pl.date_planning = ?
                    WHERE (st.soft_delete is null or st.soft_delete = 0)
                    AND pl.id is null
                    -- AND st.superviseur_id is not null
                    -- AND st.resp_sup_id is not null
                    AND st.pointage = 1
                    AND (st.group_planning_id IS NULL OR st.group_planning_id = st.idsite) AND st.idsite != 1363",[$now->format('Y-m')])[0]->nb;

                $response['nb_planning_non_fait_next'] = DB::select("SELECT count(st.idsite)  as 'nb'
                FROM sites st
                LEFT JOIN plannings pl ON pl.site_id = st.idsite and pl.date_planning = ?
                WHERE (st.soft_delete is null or st.soft_delete = 0)
                AND pl.id is null
                -- AND st.superviseur_id is not null
                -- AND st.resp_sup_id is not null
                AND st.pointage = 1
                AND (st.group_planning_id IS NULL OR st.group_planning_id = st.idsite)",[(new \DateTime())->setDate((int)date('Y'), (int)date('m'), 1)->modify('+1 month')->format('Y-m')])[0]->nb;
            } else {
                $regions = RegionUsersController::getRegions($request);
                $response['nb_planning_non_fait'] = DB::select("SELECT count(st.idsite)  as 'nb'
                    FROM sites st
                    LEFT JOIN plannings pl ON pl.site_id = st.idsite and pl.date_planning = ?
                    WHERE (st.soft_delete is null or st.soft_delete = 0)
                    AND pl.id is null
                    -- AND st.superviseur_id is not null
                    -- AND st.resp_sup_id is not null
                    AND st.pointage = 1
                    AND (st.group_planning_id IS NULL OR st.group_planning_id = st.idsite) AND st.idsite != 1363 AND st.group_pointage_id in($regions)",[$now->format('Y-m')])[0]->nb;

                $response['nb_planning_non_fait_next'] = DB::select("SELECT count(st.idsite)  as 'nb'
                FROM sites st
                LEFT JOIN plannings pl ON pl.site_id = st.idsite and pl.date_planning = ?
                WHERE (st.soft_delete is null or st.soft_delete = 0)
                AND pl.id is null
                -- AND st.superviseur_id is not null
                -- AND st.resp_sup_id is not null
                AND st.pointage = 1
                AND (st.group_planning_id IS NULL OR st.group_planning_id = st.idsite) AND st.group_pointage_id in($regions)",[(new \DateTime())->setDate((int)date('Y'), (int)date('m'), 1)->modify('+1 month')->format('Y-m')])[0]->nb;

                $a_recuperer = 0;

                $employe_archive = DB::select("SELECT DISTINCT meq.employe_id FROM mouvement_equipement meq
                LEFT JOIN employes emp ON meq.employe_id = emp.id
                WHERE emp.soft_delete = 1
                AND (meq.`status` is null OR meq.`status` = 'en cours')");

                if (count($employe_archive) > 0) {
                foreach($employe_archive as $employe) {
                    $dotation_archive_sortie = DB::select("SELECT meq.id, meq.employe_id, COUNT(leq.id) as nbArticle
                    FROM mouvement_equipement meq
                    LEFT JOIN employes emp ON meq.employe_id = emp.id
                    LEFT JOIN ligne_equipement leq ON meq.id = leq.mouvement_equipement_id
                    WHERE meq.employe_id = ?
                    AND meq.type_mouvement = 'sortie'
                    AND (meq.`status` is null OR meq.`status` = 'en cours')
                    GROUP BY meq.id, meq.employe_id", [$employe->employe_id]);

                    $dotation_archive_entre = DB::select("SELECT meq.id, meq.employe_id, COUNT(leq.id) as nbArticle
                    FROM mouvement_equipement meq
                    LEFT JOIN employes emp ON meq.employe_id = emp.id
                    LEFT JOIN ligne_equipement leq ON meq.id = leq.mouvement_equipement_id
                    WHERE meq.employe_id = ?
                    AND meq.type_mouvement = 'entré'
                    GROUP BY meq.id, meq.employe_id", [$employe->employe_id]);

                    if (!self::estRetourne($dotation_archive_sortie, $dotation_archive_entre)) {
                        if (count($dotation_archive_sortie) > 1) {
                            $a_recuperer += count($dotation_archive_sortie);
                        } else {
                            $a_recuperer += 1;
                        }
                    }
                }
                $response['nb_a_recuperer'] = $a_recuperer;
                }
            }
            
            $response['nb_site_ndf'] = DB::select("SELECT count(s.idsite) as 'nb' FROM sites s
                WHERE s.pointage = 1
                and (s.superviseur_id is null or s.superviseur_id = 0)
                AND (s.group_planning_id is null or s.group_planning_id = s.idsite)
                and (s.soft_delete is null or s.soft_delete = 0)
                AND s.idsite != 1363")[0]->nb;
            $parent_site_delete = DB::select("SELECT count(s.group_planning_id) as 'nbst' 
                FROM sites s 
                LEFT JOIN sites child ON child.group_planning_id = s.idsite AND child.idsite != s.idsite AND (child.soft_delete = 0 or child.soft_delete IS NULL)
                WHERE s.pointage = 1 AND s.soft_delete = 1 AND s.group_planning_id is not null
                GROUP BY s.group_planning_id
                HAVING count(child.idsite) > 0 ", []);
            $response['nb_site_principale_archive'] = count($parent_site_delete);
            $response['nb_site_sans_manager'] = DB::select("SELECT count(s.idsite) as 'nb' FROM sites s
                WHERE s.pointage = 1
                and (s.resp_sup_id is null or s.resp_sup_id = 0)
                and (s.soft_delete is null or s.soft_delete = 0)
                and (s.group_planning_id is null or s.group_planning_id = s.idsite) 
                AND s.idsite != 1363")[0]->nb;
            $response['nb_misapied_demande'] = DB::select("SELECT count(ab.id) as 'nb' FROM absences ab
                WHERE ab.status = 'demande' and superviseur_id = ?", [$auth_id])[0]->nb;
            $date_now = (new \DateTime())->format('Y-m');

            $response['nb_anomalie'] = DB::select("SELECT count(DISTINCT ptg.agent_id) as 'nb'
                FROM planning_pointages ptg
                LEFT JOIN employes emp on emp.id = ptg.agent_id
                LEFT JOIN plannings pl on pl.id = ptg.planning_id
                LEFT JOIN sites st on st.idsite = pl.site_id
                WHERE emp.soft_delete = 1 AND ptg.date_pointage > '". (new \DateTime())->format('Y-m-d H:i:s') . "'",
                [])[0]->nb;

            $response['nb_anomalie_24h'] = count(DB::select("WITH pointages_consecutifs AS (
                    SELECT
                        agent_id,
                        date_pointage,
                        LAG(date_pointage) OVER (PARTITION BY agent_id ORDER BY date_pointage) AS prev_pointage,
                        LEAD(date_pointage) OVER (PARTITION BY agent_id ORDER BY date_pointage) AS next_pointage,
                        CASE
                            WHEN LAG(date_pointage) OVER (PARTITION BY agent_id ORDER BY date_pointage) IS NULL
                                OR TIMESTAMPDIFF(HOUR, LAG(date_pointage) OVER (PARTITION BY agent_id ORDER BY date_pointage), date_pointage) > 12 THEN 1
                            ELSE 0
                        END AS debut_sequence
                    FROM planning_pointages
                    WHERE date_pointage >= ?
                ),
                sequences AS ( SELECT agent_id, date_pointage, SUM(debut_sequence) OVER (PARTITION BY agent_id ORDER BY date_pointage) AS sequence_id FROM pointages_consecutifs )
                SELECT
                agent_id,
                MIN(date_pointage) AS debut_sequence,
                MAX(date_pointage) AS fin_sequence,
                COUNT(*) AS nombre_pointages
                FROM sequences
                GROUP BY agent_id, sequence_id
                HAVING COUNT(*) > 1
                ORDER BY agent_id, debut_sequence",[(new \DateTime())->format('Y-m-d H:i:s')]));
            if ($role != "resp_op") {
                $nb_unread_this_month = 0;
                $nb_unread_last_month = 0;
                $satisfaction_ids = array_column(DB::select("SELECT s.id, s.satisfaction_id FROM seen s
                    WHERE s.satisfaction_id is not null and s.user_id = ?", [$auth_id]), "satisfaction_id");
                if(count($satisfaction_ids) > 0 ) {
                    $nb_unread_this_month = DB::select("SELECT count(s.id) as 'nb' FROM satisfactions s
                        WHERE month(s.created_at) = month(now()) 
                        and s.id not in (". implode(",", $satisfaction_ids) .")
                        and s.user_id != ?", [$auth_id])[0]->nb;
    
                    $nb_unread_last_month = DB::select("SELECT count(s.id) as 'nb' FROM satisfactions s
                        LEFT JOIN sites st ON s.site_id = st.idSite
                        WHERE month(s.created_at) = month(now()) - 1 
                        and s.id not in (". implode(",", $satisfaction_ids) .")
                        and s.user_id != ?", [$auth_id])[0]->nb;
                }
                $response['nb_unread_this_month'] = $nb_unread_this_month;
                $response['nb_unread_last_month'] = $nb_unread_last_month;
            } else {
                $regions = RegionUsersController::getRegions($request);
                $nb_unread_this_month = 0;
                $nb_unread_last_month = 0;
                $satisfaction_ids = array_column(DB::select("SELECT s.id, s.satisfaction_id FROM seen s
                    WHERE s.satisfaction_id is not null and s.user_id = ?", [$auth_id]), "satisfaction_id");
                if(count($satisfaction_ids) > 0 ) {
                    $nb_unread_this_month = DB::select("SELECT count(s.id) as 'nb' FROM satisfactions s
                        left join sites st on st.idsite = s.site_id
                        WHERE month(s.created_at) = month(now()) 
                        and s.id not in (". implode(",", $satisfaction_ids) .")
                        and s.user_id != ?
                        and st.group_pointage_id in($regions)", [$auth_id])[0]->nb;
    
                    $nb_unread_last_month = DB::select("SELECT count(s.id) as 'nb' FROM satisfactions s
                        left join  sites st ON st.idsite = s.site_id
                        WHERE month(s.created_at) = month(now()) - 1 
                        and s.id not in (". implode(",", $satisfaction_ids) .")
                        and s.user_id != ?
                        and st.group_pointage_id in($regions)", [$auth_id])[0]->nb;
                }
                $response['nb_unread_this_month'] = $nb_unread_this_month;
                $response['nb_unread_last_month'] = $nb_unread_last_month;
            }
        }
        
        if(in_array($role, ["validateur", "resp_sup", "resp_op", "access"])){
            $now = new \DateTime();
            if($role == "resp_sup"){
                $response['nb_planning_non_fait'] = DB::select("SELECT count(st.idsite)  as 'nb'
                    FROM sites st
                    LEFT JOIN plannings pl ON pl.site_id = st.idsite and pl.date_planning = ?
                    WHERE (st.soft_delete is null or st.soft_delete = 0)
                    AND pl.id is null
                    AND st.superviseur_id is not null
                    AND st.resp_sup_id is not null
                    AND st.pointage = 1
                    AND st.resp_sup_id = ?", [$now->format('Y-m'), $auth_id])[0]->nb;
            }

            $nb_fait = 0;
            $fait_ids = array_column(DB::select("SELECT s.id, s.fait_marquant_id FROM seen s
                WHERE s.fait_marquant_id is not null and s.user_id = ?", [$auth_id]), "fait_marquant_id");
            if(count($fait_ids) > 0 && $role != "resp_op") {
                $nb_fait = DB::select("SELECT count(f.id) as 'nb' FROM fait_marquants f
                    WHERE f.user_id != ? and f.id not in (". implode(",", $fait_ids) .")", [$auth_id])[0]->nb;
            } else if (count($fait_ids) > 0 && $role == "resp_op") {
                $regions = RegionUsersController::getRegions($request);
                $nb_fait = DB::select("SELECT count(f.id) as 'nb' FROM fait_marquants f
                    LEFT JOIN sites s ON f.site_id = s.idsite
                    WHERE f.user_id != ? and f.id not in (". implode(",", $fait_ids) .")
                    and s.group_pointage_id in ($regions)", [$auth_id])[0]->nb;
            }
            $response['nb_fait'] = $nb_fait;
        }
        if (in_array($role, ["room", "resp_room"])) {
            // $begin_date = (new \DateTime())->sub(new \DateInterval('PT2H'))->format('Y-m-d H:i:s');
            $calls = DB::connection('mysql2')->select("SELECT  c1.src, c1.dst, c1.calltype, c1.disposition, c1.datetime, c1.recordfile, c1.billable, c1.uniqueid FROM cdr c1 WHERE datetime > ? ORDER BY datetime DESC", [HomeController::getDayOrNightDate()]);
            $normalizeNumber = function ($number) {
                return preg_replace('/^(0|\+261)/', '', $number);
            };

            $inbound_no_answered = [];
            $answered_calls = [];
            foreach ($calls as $call) {
                $normalize_src = $normalizeNumber($call->src);
                $normalize_dst = $normalizeNumber($call->dst);
                if ($call->disposition == 'ANSWERED' && $call->billable > 0) {
                    if (!isset($answered_calls[$normalize_src]) || strtotime($call->datetime) > strtotime($answered_calls[$normalize_src]['datetime'])) {
                        $answered_calls[$normalize_src]['datetime'] = $call->datetime;
                        $answered_calls[$normalize_src]['disposition'] = $call->disposition;
                    }
                    if (!isset($answered_calls[$normalize_dst]) || strtotime($call->datetime) > strtotime($answered_calls[$normalize_dst]['datetime'])) {
                        $answered_calls[$normalize_dst]['datetime'] = $call->datetime;
                        $answered_calls[$normalize_dst]['disposition'] = $call->disposition;
                    }
                }
                if (($call->disposition != 'ANSWERED' || $call->billable == 0) && $call->calltype == 'Inbound') {
                    if (!isset($inbound_no_answered[$normalize_src]) || strtotime($call->datetime) > strtotime($inbound_no_answered[$normalize_src]['datetime'])) {
                        $inbound_no_answered[$normalize_src]['datetime'] = $call->datetime;
                        $inbound_no_answered[$normalize_src]['disposition'] = $call->disposition;
                        $inbound_no_answered[$normalize_src]['origin_numero'] = $call->src;
                        $inbound_no_answered[$normalize_src]['src'] = $call->src;
                    }
                }
            }
            $results = [];
            foreach ($inbound_no_answered as $call) {
                $is_no_recal = false;
                if(isset($answered_calls[$normalizeNumber($normalizeNumber($call['origin_numero']))])){
                    $answered = $answered_calls[$normalizeNumber($normalizeNumber($call['origin_numero']))];
                    $call['date_last_answered'] = $answered['datetime'];
                    if(new \DateTime($call['datetime']) > new \DateTime($answered['datetime'])){
                        $is_no_recal = true;
                    }
                }
                if($is_no_recal){
                    $results[] = $call;
                }
            }
            $response["service"] = HomeController::getDayOrNightDate();
            $response["re_call"] = count($results);

            $response['nb_site_ndf'] = DB::select("SELECT count(s.idsite) as 'nb' FROM sites s
            WHERE s.pointage = 1
            and (s.superviseur_id is null or s.superviseur_id = 0)
            AND (s.group_planning_id is null or s.group_planning_id = s.idsite)
            and (s.soft_delete is null or s.soft_delete = 0)
            AND s.idsite != 1363")[0]->nb;

            $response['nb_site_sans_manager'] = DB::select("SELECT count(s.idsite) as 'nb' FROM sites s
            WHERE s.pointage = 1
            and (s.resp_sup_id is null or s.resp_sup_id = 0)
            and (s.soft_delete is null or s.soft_delete = 0)
            and (s.group_planning_id is null or s.group_planning_id = s.idsite) 
            AND s.idsite != 1363")[0]->nb;
        }
        // check if has part variable
        $currentDate = new \DateTime();
        if($currentDate > (new \DateTime)->setDate($currentDate->format("Y"), $currentDate->format("m"), 20)->setTime(0,0,0)
        && $currentDate < (new \DateTime)->setDate($currentDate->format("Y"), $currentDate->format("m"), 25)->setTime(0,0,0)) {
            $employes = DB::select("SELECT e.id FROM employes e
                WHERE (e.soft_delete is null or e.soft_delete = 0)
                AND e.part_variable = 1 AND e.resp_part_id = ?", [$request->user()->employe_id]);
            $emp_ids = array_column($employes, "id");
            if (count($emp_ids) > 0) {
                $datePaie = (new \DateTime)->setDate($currentDate->format("Y"), $currentDate->format("m"), 20);
                $parts = DB::select("SELECT id, employe_id FROM part_variables WHERE (status = 'validation' or status = 'done')
                    AND date_paie = ? AND employe_id in (" . implode(',', $emp_ids) . ")", [$datePaie->format('Y-m-d')]);
                $nb_pv = 0;
                foreach ($employes as $emp) {
                    $pv_done = false;
                    foreach ($parts as $pv) {
                        if ($emp->id == $pv->employe_id)
                            $pv_done = true;
                    }
                    if (!$pv_done)
                        $nb_pv++;
                }
                $response['nb_pv'] = $nb_pv;
            }
        }
        return response($response);
    }
}
