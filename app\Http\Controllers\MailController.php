<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Util\EmployeUtil;
use App\Models\PointageReclamation;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\Seen;
use App\Models\Site;
use \Mail;
use \Swift_Mailer;
use \Swift_SmtpTransport as SmtpTransport;

class MailController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    protected static function getSite($id){
        $site = Site::select('nom')->find($id);
        return $site->nom;
    }

    protected static function getUser($id){
        if(!$id) return 'Null';
        $user = DB::select("SELECT us.id, us.name, coalesce(ur.email, us.email) as 'email'  
            from users us
            left join users ur on ur.id=us.real_email_id 
            where us.id = ?", [$id])[0];
        return $user->name . ' <' . $user->email . '>';
    }

    protected static function getService($date_pointage){
        $dateTime = new \DateTime($date_pointage);
        $horaire = 'NUIT';
        if($dateTime->format('H:i:s') == '07:00:00')
            $horaire = 'JOUR';
        if($dateTime->format('H:i:s') == '06:00:00')
            $horaire = 'JOUR';
        return $dateTime->format('d-m-Y') . ' ' . $horaire;
    }

    protected static function getDateHour($date){
        $dateTime = new \DateTime($date);
        return $dateTime->format('d-m-Y H:i');
    }
    
    protected static function getAbsenceDetail($id){
        $absence = DB::select("SELECT cg.id, cg.employe_id, cg.depart, cg.retour, cg.motif, cg.status, cg.created_at,
            t.description as 'type', cg.site_id, st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', 
            stat.description as 'status_description', stat.color as 'status_color', cg.user_id,
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi
            FROM absences cg
            LEFT JOIN type_absences t ON t.name = cg.type_absence
            LEFT JOIN employes a ON a.id = cg.employe_id
            LEFT JOIN sites st ON st.idsite = cg.site_id
            LEFT JOIN users us on us.id = cg.user_id
            LEFT JOIN `status` stat on stat.name = cg.status
            WHERE cg.id = ?", [$id])[0];
        return compact('absence');
    }
    
    protected static function getSanctionDetail($id){
        $sanction = DB::select("SELECT sanc.id, sanc.user_id, sanc.employe_id, sanc.superviseur_id, sanc.site_id, sanc.motif, sanc.date_pointage,
            sanc.status, sanc.created_at, sanc.objet, sanc.montant, sanc.absence,
            stat.description as 'status_description', stat.color as 'status_color',
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
            st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
            FROM sanctions sanc
            LEFT JOIN employes a on a.id = sanc.employe_id
            LEFT JOIN sites st on st.idsite = sanc.site_id
            LEFT JOIN users us on us.id = sanc.user_id
            LEFT JOIN users sup on sup.id = sanc.superviseur_id
            LEFT JOIN `status` stat on stat.name = sanc.status
            where sanc.id = ?", [$id])[0];
        $last_sanctions = DB::select("SELECT sanc.id, sanc.objet, sanc.motif, sanc.date_pointage, sanc.absence
            FROM sanctions sanc
            where sanc.status = 'done' && sanc.id != ? && sanc.employe_id = ?
            order by sanc.date_pointage desc
            limit 3", [$id, $sanction->employe_id]);
        return compact('sanction', 'last_sanctions');
    }

    protected static function getPrimeDetail($id){
        $prime = DB::select("SELECT pr.id, pr.user_id, pr.employe_id, pr.superviseur_id, pr.site_id, pr.motif, pr.date_pointage,
            pr.status, pr.created_at, pr.objet, pr.montant,
            stat.description as 'status_description', stat.color as 'status_color',
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
            st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
            FROM primes pr
            LEFT JOIN employes a on a.id = pr.employe_id
            LEFT JOIN sites st on st.idsite = pr.site_id
            LEFT JOIN users us on us.id = pr.user_id
            LEFT JOIN users sup on sup.id = pr.superviseur_id
            LEFT JOIN `status` stat on stat.name = pr.status
            where pr.id = ?", [$id])[0];
        $last_primes = DB::select("SELECT pr.id, pr.objet, pr.motif, pr.date_pointage
            FROM primes pr
            where pr.status = 'done' && pr.id != ? && pr.employe_id = ?
            order by pr.date_pointage desc
            limit 3", [$id, $prime->employe_id]);
        return compact('prime', 'last_primes');
    }
    
    protected static function getSavDetail($id){
        $sav = DB::select("SELECT sav.id, sav.user_id, sav.superviseur_id, sav.site_id, sav.status, sav.created_at, 
            sav.motif, sav.date_sav, sav.technicien,
            stat.description as 'status_description', stat.color as 'status_color',
            st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
            FROM sav
            LEFT JOIN sites st on st.idsite = sav.site_id
            LEFT JOIN users us on us.id = sav.user_id
            LEFT JOIN users sup on sup.id = sav.superviseur_id
            LEFT JOIN `status` stat on stat.name = sav.status
            where sav.id = ?", [$id])[0];
        return compact('sav');
    }
    
    protected static function getEquipementDetail($id){
        $equipement = DB::select("SELECT eq.id, eq.type, eq.user_id, eq.employe_id, eq.site_id, 
            teq.designation as 'type_demande', eq.status, eq.created_at, eq.motif, eq.demande, eq.detail,
            stat.description as 'status_description', stat.color as 'status_color',
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi,
            st.nom as 'site', sup.name as 'sup_nom', sup.email as 'sup_email'
            FROM equipements eq
            LEFT JOIN type_equipements teq on teq.name = eq.type
            LEFT JOIN employes a on a.id = eq.employe_id
            LEFT JOIN sites st on st.idsite = eq.site_id
            LEFT JOIN users sup on sup.id = eq.user_id
            LEFT JOIN `status` stat on stat.name = eq.status
            where eq.id = ?", [$id])[0];
        $articles = array_column(
            DB::select("SELECT ac.designation
                FROM article_equipements aeq
                LEFT JOIN articles ac ON ac.name = aeq.article
                where aeq.equipement_id = ?", [$id])
            , 'designation');
        return compact('equipement', 'articles');
    }
    
    protected static function getVisite($id){
        $visite = DB::select("SELECT vp.id, vp.date_visite, vp.compte_rendu, vp.user_id, vp.site_id,
            sup.name as 'sup_nom', sup.email as 'sup_email', s.nom as 'site'
            FROM visite_postes vp
            LEFT JOIN users sup on sup.id = vp.user_id
            LEFT JOIN sites s on s.idsite = vp.site_id
            where vp.id = ?", [$id])[0];
        return compact('visite');
    }
    
    protected static function getFait($id){
        $fait = DB::select("SELECT f.id, f.objet, f.commentaire, f.created_at, st.nom as 'site',
            us.name as 'user_nom', us.email as 'user_email', f.user_id
            FROM fait_marquants f
            LEFT JOIN sites st on st.idsite = f.site_id
            LEFT JOIN users us on us.id = f.user_id
            where f.id = ?", [$id])[0];
        return compact('fait');
    }

    protected static function getSatisfactionDetail($id){
        $satisfaction = DB::select("SELECT stf.id, stf.site_id, stf.user_id, stf.comment,
            st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', stf.created_at
            FROM satisfactions stf
            LEFT JOIN sites st on stf.site_id = st.idsite
            LEFT JOIN users us on us.id = stf.user_id
            WHERE stf.id = ?", [$id]
        )[0];
        return compact('satisfaction');
    }
    
    public static function getMailer($request, &$emails){
        $auth = $request->user();
        if(!in_array($auth->email,["<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>", "<EMAIL>"])){
                if($auth->real_email_id){
                    $user = User::find($auth->real_email_id);
                    $emails[] = ['address' => $user->email, 'name' => $user->name];
                }
                else {
                    $emails[] = ['address' => $auth->email, 'name' => $auth->name];
                }
            }
        // if(env('DB_HOST') == "127.0.0.1")
        //     $emails[] = ['address' => '<EMAIL>', 'name' => 'Aro Nomeniaina'];
        
        if($auth->real_email_id){
            $user = User::find($auth->real_email_id);
            $from_email = $user->email;
            $email_name = $auth->name;
            $email_password = $user->email_password;
        }
        else if($auth->email_password){
            $from_email = $auth->email;
            $email_name = $auth->name;
            $email_password = $auth->email_password;
        }
        else {
            $from_email = "<EMAIL>";
            $email_name = "Compte parent";
            $email_password = "ArTl\$DrXP4\$21";
        }

        $transport = (new SmtpTransport(
            \Config::get('mail.mailers.smtp.host'), 
            // \Config::get('mail.mailers.smtp.port'))
            \Config::get('mail.mailers.smtp.port'), "ssl")
        )
        ->setUsername($from_email)
        ->setPassword($email_password);
        
        $mailer = new Swift_Mailer($transport);

        return compact('mailer', 'from_email', 'email_name');
    }

    public static function absence(Request $request, $absence_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getAbsenceDetail($absence_id);
        $absence = $data['absence'];
        $depart = new \DateTime($absence->depart);
        if($depart->format('H:i:s') == "18:00:00")
            $absence->depart = $depart->format('d/m/Y') . ' NUIT';
        else
            $absence->depart = $depart->format('d/m/Y') . ' JOUR';
        $retour = new \DateTime($absence->retour);
        if($depart->format('H:i:s') == "18:00:00")
            $absence->retour = $retour->format('d/m/Y') . ' NUIT';
        else
            $absence->retour = $retour->format('d/m/Y') . ' JOUR';
        $employe = EmployeUtil::getEmploye($absence);
        
        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.absence", compact('absence', 'employe', 'note')
            , function($message) use ($emails, $objet, $employe, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $employe);
            });
    }

    public static function visite(Request $request, $visite_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getVisite($visite_id);
        $visite = $data['visite'];
        $site = $visite->site;
        $date_visite = MailController::getDateHour($visite->date_visite);

        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.visite", compact('visite', 'site', 'date_visite', 'note')
            , function($message) use ($emails, $objet, $site, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $site);
            });
    }

    public static function fait(Request $request, $fait_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getFait($fait_id);
        $fait = $data['fait'];
        $site = $fait->site;

        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.fait", compact('fait', 'site', 'note')
            , function($message) use ($emails, $objet, $site, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $site);
            });
    }

    public static function equipement(Request $request, $equipement_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getEquipementDetail($equipement_id);
        $equipement = $data['equipement'];
        $employe = null;
        if($equipement->employe)
            $employe = EmployeUtil::getEmploye($equipement);
        $articles = implode(', ' , $data['articles']);
        $site = $equipement->site;

        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.equipement", compact('equipement', 'articles', 'employe', 'site', 'note')
            , function($message) use ($emails, $objet, $site, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $site);
            });
    }

    public static function sav(Request $request, $sav_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getSavDetail($sav_id);
        $sav = $data['sav'];
        $site = $sav->site;
        $date_sav = "";
        if($sav->date_sav) $date_sav = (new \DateTime($sav->date_sav))->format('d/m/Y H:i:s');

        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.sav", compact('sav', 'site', 'date_sav', 'note')
            , function($message) use ($emails, $objet, $site, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $site);
            });
    }

    public static function sanction(Request $request, $sanction_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getSanctionDetail($sanction_id);
        $sanction = $data['sanction'];
        if($sanction->date_pointage){
            $date_pointage = new \DateTime($sanction->date_pointage);
            if($date_pointage->format('H:i:s') == "07:00:00")
                $sanction->date_service = $date_pointage->format('d/m/Y') . ' JOUR';
            else
                $sanction->date_service = $date_pointage->format('d/m/Y') . ' NUIT';
        }
        $employe = EmployeUtil::getEmploye($sanction);
        $last_sanctions = $data['last_sanctions'];
        foreach ($last_sanctions as $sanc) {
            if($sanc->date_pointage){
                $date_pointage = new \DateTime($sanc->date_pointage);
                if($date_pointage->format('H:i:s') == "07:00:00")
                    $sanc->date_service = $date_pointage->format('d/m/Y') . ' JOUR';
                else
                    $sanc->date_service = $date_pointage->format('d/m/Y') . ' NUIT';
            }
        }
        
        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.sanction", compact('sanction', 'employe', 'last_sanctions', 'note')
            , function($message) use ($emails, $objet, $employe, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $employe);
            });
    }

    public static function prime(Request $request, $prime_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getPrimeDetail($prime_id);
        $prime = $data['prime'];
        if($prime->date_pointage){
            $date_pointage = new \DateTime($prime->date_pointage);
            if($date_pointage->format('H:i:s') == "07:00:00")
                $prime->date_service = $date_pointage->format('d/m/Y') . ' JOUR';
            else
                $prime->date_service = $date_pointage->format('d/m/Y') . ' NUIT';
        }
        $employe = EmployeUtil::getEmploye($prime);
        $last_primes = $data['last_primes'];
        foreach ($last_primes as $pr) {
            if($pr->date_pointage){
                $date_pointage = new \DateTime($pr->date_pointage);
                if($date_pointage->format('H:i:s') == "07:00:00")
                    $pr->date_service = $date_pointage->format('d/m/Y') . ' JOUR';
                else
                    $pr->date_service = $date_pointage->format('d/m/Y') . ' NUIT';
            }
        }
        
        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.prime", compact('prime', 'employe', 'last_primes', 'note')
            , function($message) use ($emails, $objet, $employe, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $employe);
            });
    }

    public static function getFlotteDetail($id){
        $flotte = DB::select("SELECT flottes.id, flottes.user_id, flottes.objet, flottes.commentaire, flottes.status, flottes.note_id, flottes.created_at,
        stat.description as 'status_description', stat.color as 'status_color',
        st.nom as 'site', us.name as 'user_nom', us.email as 'user_email'
        FROM flottes
        LEFT JOIN sites st on st.idsite = flottes.site_id
        LEFT JOIN users us on us.id = flottes.user_id
        LEFT JOIN `status` stat on stat.name = flottes.status
        where flottes.id = ?", [$id])[0];
        return compact('flotte');
    }

    public static function flotte(Request $request, $flotte_id, $objet, $emails)
    {
        $note = $request->note;
        $data = MailController::getFlotteDetail($flotte_id);
        $flotte = $data['flotte'];
        $site = $flotte->site;

        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send(
            "emails.flotte",
            compact('flotte', 'site', 'note'),
            function ($message) use ($emails, $objet, $site, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email) {
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $site);
            }
        );
    }

    protected static function getDeductionDetail($id){
        $deduction = DB::select("SELECT ded.id, ded.user_id, ded.employe_id, ded.status, 
        ded.created_at,ded.montant,ded.date_paie, ded.motif , stat.description as 'status_description', stat.color as 'status_color',
        emp.nom as 'employe', emp.societe_id, emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi,
        u.name as 'user_nom', u.email as 'user_email'
        FROM deductions ded
        LEFT JOIN employes emp on emp.id = ded.employe_id
        LEFT JOIN `status` stat on stat.name = ded.status
        LEFT JOIN users u on u.id = ded.user_id
        where ded.id = ?",[$id])[0];
        return compact('deduction');
        // $last_deductions = DB::select("SELECT ded.id, ded.motif, ded.created_at")
    }

    protected static function getService24Detail($id){
        $service24 = DB::select("SELECT sc.id, sc.user_id, sc.employe_id, sc.status,
            sc.created_at, sc.motif, sc.begin_pointage, sc.end_pointage, emp.nom as 'employe', emp.societe_id,
            emp.numero_employe, emp.numero_stagiaire, emp.num_emp_soit, emp.num_emp_saoi, 
            sc.user_id, us.name as 'user_nom', us.email as 'user_email', st.nom
            FROM service24s sc
            LEFT JOIN users us on sc.user_id = us.id
            LEFT JOIN sites st ON sc.site_id = st.idsite
            LEFT JOIN employes emp ON sc.employe_id = emp.id
            where sc.id = ?",[$id])[0];
        return compact('service24');
    }

    protected static function getAvanceDetails($id){
        $avance = DB::select("SELECT avc.id, avc.type_avance_id, avc.montant, avc.date_paie, emp.societe_id,
            emp.numero_employe, emp.numero_stagiaire, emp.num_emp_soit, emp.num_emp_saoi, emp.nom as 'employe',
            avc.motif, tpa.description as 'type_avance', us.name as 'user_nom', us.email as 'user_email', avc.created_at
            FROM avances avc
            LEFT JOIN users us ON us.id = avc.user_id
            LEFT JOIN employes emp ON avc.employe_id = emp.id
            LEFT JOIN type_avances tpa ON tpa.id = avc.type_avance_id
            WHERE avc.id = ?",[$id])[0];
        return compact('avance');
    }

    public static function avance(Request $request, $avance_id, $objet, $emails)
    {
        $note = $request->note;
        $data = MailController::getAvanceDetails($avance_id);
        $avance = $data['avance'];
        if ($avance->date_paie)
            $avance->date_paie = (new \DateTime($avance->date_paie))->format('m/Y');
        $employe = EmployeUtil::getEmploye($avance);
        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send(
            "emails.avance",
            compact('avance', 'employe', 'note'),
            function ($message) use ($emails, $objet, $employe, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email) {
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $employe);
            }
        );
    }

    protected static function getReclamationDetails($id){
        $reclamation = DB::select("SELECT rec.date_paie, rec.motif, rec.user_id, emp.nom as 'employe',
        emp.numero_employe, emp.societe_id, emp.numero_stagiaire, emp.num_emp_soit, emp.num_emp_saoi,
        us.name as 'user_nom', us.email as 'user_email', rec.created_at, rec.status
        FROM reclamations rec 
        LEFT JOIN users us ON rec.user_id = us.id
        LEFT JOIN employes emp ON rec.employe_id = emp.id
        WHERE rec.id = ?", [$id])[0];
        $reclamation->nb_heure = PointageReclamation::Where('reclamation_id', $id)->count() * 12;
        return compact('reclamation');
    }

    public static function reclamation(Request $request, $reclamation_id, $objet, $emails)
    {
        $note = $request->note;
        $data = MailController::getReclamationDetails($reclamation_id);
        $reclamation = $data['reclamation'];
        if ($reclamation->date_paie)
        $reclamation->date_paie = (new \DateTime($reclamation->date_paie))->format('m/Y');
        $employe = EmployeUtil::getEmploye($reclamation);
        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send(
            "emails.reclamation",
            compact('reclamation', 'employe', 'note'),
            function ($message) use ($emails, $objet, $employe, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email) {
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $employe);
            }
        );

    }

    public static function getPartVariableDetail($id){
        $part_variable = DB::select("SELECT pv.id, pv.employe_id, pv.date_paie, pv.commentaire, pv.user_id, pv.status, pv.created_at, pv.updated_at,
            stat.description as 'status_description', stat.color as 'status_color', 
            e.societe_id, e.nom, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, 
            us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email'
            FROM part_variables pv
            LEFT JOIN employes e ON e.id = pv.employe_id
            LEFT JOIN users us on us.id = pv.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN `status` stat on stat.name = pv.status
            where pv.id = ?", [$id])[0];
        $criteres = DB::select("SELECT ms.id, COALESCE(ms.critere, cr.designation) as 'designation', ms.montant, cr.montant as 'maximum'
            FROM critere_mensuels ms
            LEFT JOIN critere_parts cr ON cr.id = ms.critere_id
            WHERE ms.part_variable_id = ?
            order by ms.id desc", [$id]);
        return compact('part_variable','criteres');
    }

    public static function partVariable(Request $request, $part_variable_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getPartVariableDetail($part_variable_id);
        $part_variable = $data['part_variable'];
        $criteres = $data['criteres'];

        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.part_variable", compact('part_variable', 'criteres', 'note')
            , function($message) use ($emails, $objet, $part_variable, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . substr($part_variable->nom, 0, 30));
            });
    }

    public static function getApprovisionnementDetail($id){
        $approvisionnement = DB::select("SELECT appro.id, appro.objet, appro.user_id, appro.status, appro.created_at, appro.updated_at,
            stat.description as 'status_description', stat.color as 'status_color', 
            us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', appro.created_at, appro.reference, 
            sv.designation as 'service'
            FROM approvisionnements appro
            LEFT JOIN users us on us.id = appro.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN `status` stat on stat.name = appro.status
            LEFT JOIN services sv on sv.id = us.service_id
            where appro.id = ?", [$id])[0];
        $approvisionnement->year = (\DateTime::createFromFormat("Y-m-d H:i:s", $approvisionnement->created_at))->format("Y");
        $approvisionnement->reference = str_pad($approvisionnement->reference, 6, '0', STR_PAD_LEFT);
        $da_items = DB::select("SELECT da.id, da.designation, da.unite, da.quantite, da.prix, da.created_at, da.updated_at, da.price_only
            FROM da_items da
            WHERE da.approvisionnement_id = ?
            order by da.id desc", [$id]);
        return compact('approvisionnement','da_items');
    }

    public static function approvisionnement(Request $request, $appro_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getApprovisionnementDetail($appro_id);
        $appro = $data['approvisionnement'];
        $items = $data['da_items'];

        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.appro", compact('appro', 'items', 'note')
            , function($message) use ($emails, $objet, $appro, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . substr($appro->objet, 0, 30));
            });
    }

    public static function getJuridiqueDetail($id){
        $juridique =  DB::select("SELECT j.id, j.reference, j.recouvrement, j.agence_id, j.debiteur, j.contrat, 
            j.facture, j.montant, j.police, j.agent, j.fait, j.status, j.created_at, j.fait_id,
            stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site',
            (SELECT COUNT(*) FROM piece_jointes pj WHERE pj.juridique_id = j.id) as nb_pj,
            j.user_id, u.name as 'user_nom', u.email as 'user_email', agc.nom as 'agence'
            FROM juridiques j
            LEFT JOIN agences agc on agc.id = j.agence_id
            LEFT JOIN sites st on st.idsite = j.site_id
            LEFT JOIN `status` stat on stat.name = j.status
            LEFT JOIN users u on u.id = j.user_id
            WHERE j.id = ?"
            , [$id])[0];
        $suivis = DB::select("SELECT sj.id, sj.commentaire, sj.created_at 
            FROM suivi_juridiques sj 
            where sj.user_id = ? 
            order by sj.created_at desc", [$id]);
        foreach ($suivis as $sv) {
            $sv->date = date_format(date_create($sv->created_at), "d M Y"); 
        }
        return compact('juridique', 'suivis');
    }

    public static function juridique(Request $request, $juridique_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getJuridiqueDetail($juridique_id);
        $juridique = $data['juridique'];
        $suivis = $data['suivis'];

        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.juridique", compact('juridique', 'suivis', 'note')
            , function($message) use ($emails, $objet, $juridique, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject(($juridique->recouvrement ? "Recouvrement" : "Plainte") . " : " . substr(($juridique->recouvrement ? $juridique->debiteur : $juridique->site), 0, 30));
            });
    }

    public static function deduction(Request $request, $deduction_id, $objet, $emails){
        $note =$request->note;
        $data = MailController::getDeductionDetail($deduction_id);
        $deduction = $data['deduction'];
        if ($deduction->date_paie) {
            $date = new \DateTime($deduction->date_paie);
            // $month = date->format('F')
            $deduction->date_paie = $date->format('m/Y');
        }
        $employe = EmployeUtil::getEmploye($deduction);

        $transporter = MailController::getMailer($request, $emails);
        // return $emails;

        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.deduction", compact('deduction', 'employe', 'note')
                ,function($message) use ($emails, $objet, $employe, $from_email, $email_name){
                    $message->from($from_email,$email_name);
                    foreach ($emails as $email) {
                        $message->to($email['address'], $email['name']);
                    }
                    $message->subject($objet . " : ". $employe);
                });
    }

    public static function service24(Request $request, $service24_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getService24Detail($service24_id);
        $service24 = $data['service24'];
        if($service24->begin_pointage){
            $service24->begin_pointage = (MailController::getService($service24->begin_pointage));
        }
        if($service24->end_pointage){
            $service24->end_pointage = MailController::getService($service24->end_pointage);
        }
        $employe = EmployeUtil::getEmploye($service24);
        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.service", compact('service24', 'employe', 'note')
                ,function($message) use ($emails, $objet, $employe, $from_email, $email_name){
                    $message->from($from_email,$email_name);
                    foreach ($emails as $email) {
                        $message->to($email['address'], $email['name']);
                    }
                    $message->subject($objet . " : ". $employe);
                });

    }

    public static function satisfaction(Request $request, $satisfaction_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getSatisfactionDetail($satisfaction_id);
        $satisfaction = $data['satisfaction'];
        $site = MailController::getSite($satisfaction->site_id);
        $created_at = MailController::getDateHour($satisfaction->created_at);
        $satisfaction->created_at = $created_at;
        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.satisfaction",compact('satisfaction', 'site', 'note'),
            function ($message) use ($emails, $objet, $site, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email) {
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " . $site);
            }
        );
    }

    protected static function getPaieDetails($id){
        $paie = DB::select("SELECT p.id, p.sal_base as 'salBase', p.perdiem, p.idm_depl as 'idmDepl', 
                p.part_variable as 'partVariable', p.prime_anc as 'primeAnc', p.societe_id,
                p.nom as 'employe', p.numero_stagiaire, emp.num_emp_saoi, p.fonction_id, p.agence_id,
                p.numero_employe ,p.num_emp_soit, st.nom as 'site',p.net_imposable, p.irsa, p.cnaps, p.salfa,
                p.date_paie, emp.soft_delete, p.nb_heure_travaille, agc.nom as agence, p.net_a_payer,
                p.sal_base,p.salaire_mensuel,p.salaire_brut, p.masse_salariale, p.prime_exceptionnelle, p.idm_licenciement, p.prime_assid,p.prime_resp, p.status,
                stat.description, stat.color, p.employe_id, p.date_embauche, p.site_id, p.real_site_id, p.nb_heure_contrat  ,
                f.libelle as fonction_designation, agc.code_agence as code_agence, agc.nom as nom_agence,
                st.adresse as adresse_site, st.nom as 'site_nom', us.name as 'user_nom', us.email as 'user_email',p.user_id
                FROM paies p
                LEFT JOIN fonctions f ON p.fonction_id = f.id
                LEFT JOIN employes emp ON emp.id = p.employe_id
                LEFT JOIN status stat ON stat.name = p.status
                LEFT JOIN agences agc ON agc.id = p.agence_id
                LEFT JOIN sites st ON st.idsite = emp.site_id
                LEFT JOIN users us ON us.id = p.user_id
                WHERE p.id = ?", [$id])[0];
        return compact('paie');
    }

    public static function paie(Request $request, $paie_id, $objet, $emails){
        $note = $request->note;
        $data = MailController::getPaieDetails($paie_id);
        $paie = $data['paie'];
        $paie->date_paie = (new \DateTime($paie->date_paie))->format('F-Y');
        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        $employe = EmployeUtil::getEmployeById($paie->employe_id);
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.paie",compact('paie', 'note', 'employe'),
            function ($message) use ($emails, $objet, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email) {
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet . " : " );
            }
        );
    }


    public function send(Request $request){
        $emails = json_decode($request->user_ids, true);
        if(count($emails) == 0)
            return response(["error" => "Veuillez spécifier le destinataire"]);

        $validator = Validator::make($request->all(), [
            'objet' => 'required',
            'note' => 'required',
        ]);
        if ($validator->fails())
            return response(['error' => $validator->errors()->first()]);

        if($request->juridique_id){
            MailController::juridique($request, $request->juridique_id, $request->objet, $emails);
            HistoriqueController::action_juridique($request, $request->objet, $request->juridique_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->juridique_id];
        }
        else if ($request->deduction_id) {
            MailController::deduction($request, $request->deduction_id, $request->objet, $emails);
            HistoriqueController::action_deduction($request, $request->objet, $request->deduction_id);
            return ['success' =>"Email envoyé avec succès", 'id' => $request->deduction_id];
        }
        else if($request->equipement_id){
            MailController::equipement($request, $request->equipement_id, $request->objet, $emails);
            HistoriqueController::action_equipement($request, $request->objet, $request->equipement_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->equipement_id];
        }
        else if($request->visite_id){
            MailController::visite($request, $request->visite_id, $request->objet, $emails);
            HistoriqueController::action_visite($request, $request->objet, $request->visite_id);
            $seen = Seen::where('visite_poste_id', $request->visite_id)->where('user_id', $request->user()->id)->first();
            if($seen == null){
                $seen = new Seen();
                $seen->visite_poste_id = $request->visite_id;
                $seen->user_id = $request->user()->id;
                $seen->created_at = new \DateTime();
            }
            $seen->send_email = true;
            $seen->save();
            return ['success' => "Email envoyé avec succès", "id" => $request->visite_id];
        }
        else if($request->fait_id){
            MailController::fait($request, $request->fait_id, $request->objet, $emails);
            HistoriqueController::action_fait_marquant($request, $request->objet, $request->fait_id);
            $seen = Seen::where('fait_marquant_id', $request->fait_id)->where('user_id', $request->user()->id)->first();
            if($seen == null){
                $seen = new Seen();
                $seen->fait_marquant_id = $request->fait_id;
                $seen->user_id = $request->user()->id;
                $seen->created_at = new \DateTime();
            }
            $seen->send_email = true;
            $seen->save();
            return ['success' => "Email envoyé avec succès", "id" => $request->fait_id];
        }
        else if($request->sanction_id){
            MailController::sanction($request, $request->sanction_id, $request->objet, $emails);
            HistoriqueController::action_sanction($request, $request->objet, $request->sanction_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->sanction_id];
        }
        else if($request->prime_id){
            MailController::prime($request, $request->prime_id, $request->objet, $emails);
            HistoriqueController::action_prime($request, $request->objet, $request->sanction_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->sanction_id];
        }
        else if($request->absence_id){
            MailController::absence($request, $request->absence_id, $request->objet, $emails);
            HistoriqueController::action_absence($request, $request->objet, $request->absence_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->absence_id];
        }
        else if($request->sav_id){
            MailController::sav($request, $request->sav_id, $request->objet, $emails);
            HistoriqueController::action_sav($request, $request->objet, $request->sav_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->sav_id];
        }
        else if($request->flotte_id){
            MailController::flotte($request, $request->flotte_id, $request->objet, $emails);
            HistoriqueController::action_flotte($request, $request->objet, $request->flotte_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->flotte_id];
        }
        else if($request->appro_id){
            MailController::approvisionnement($request, $request->appro_id, $request->objet, $emails);
            HistoriqueController::action_approvisionnement($request, $request->objet, $request->appro_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->appro_id];
        }
        else if($request->part_variable_id){
            MailController::partVariable($request, $request->part_variable_id, $request->objet, $emails);
            HistoriqueController::action_part_variable($request, $request->objet, $request->part_variable_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->part_variable_id];
    }
        else if($request->service24_id){
            MailController::service24($request, $request->service24_id, $request->objet, $emails);
            HistoriqueController::action_service24($request, $request->objet, $request->service24_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->service24_id];
        }
        else if ($request->avance_id) {
            MailController::avance($request, $request->avance_id, $request->objet, $emails);
            HistoriqueController::action_avance($request, $request->objet, $request->avance_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->avance_id];
        }
        else if ($request->reclamation_id) {
            MailController::reclamation($request, $request->reclamation_id, $request->objet, $emails);
            HistoriqueController::action_reclamation($request, $request->objet, $request->reclamation_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->reclamation_id];
        }
        else if($request->satisfaction_id) {
            MailController::satisfaction($request, $request->satisfaction_id, $request->objet, $emails);
            HistoriqueController::action_satisfaction($request, $request->objet, $request->satisfaction_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->satisfaction_id];
        }
        else if($request->paie_id){
            MailController::paie($request, $request->paie_id, $request->objet, $emails);
            HistoriqueController::action_paie($request, $request->objet, $request->paie_id);
            return ['success' => "Email envoyé avec succès", "id" => $request->paie_id];
        }
        return response(['error' => 'EACCES']);
    }

    public static function user(Request $request, $user_id, $objet, $emails){
        $note = $request->note;
        $email = $request->email;
        $password = $request->password;
        $transporter = MailController::getMailer($request, $emails);
        $from_email = $transporter['from_email'];
        $email_name = $transporter['email_name'];
        Mail::setSwiftMailer($transporter['mailer']);
        Mail::send("emails.user", compact('note', 'password', 'email')
            , function($message) use ($emails, $objet, $from_email, $email_name) {
                $message->from($from_email, $email_name);
                foreach ($emails as $email){
                    $message->to($email['address'], $email['name']);
                }
                $message->subject($objet);
            });
    }
}