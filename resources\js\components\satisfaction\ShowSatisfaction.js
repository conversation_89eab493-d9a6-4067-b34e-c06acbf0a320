import React, { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom';
import useToken from '../util/useToken';
import LoadingPage from '../loading/LoadingPage';
import ShowHeader from '../view/ShowHeader';
import Tab from '../layout/Tab';
import ActionSatisfaction from './ActionSatisfaction';
import moment from 'moment';

export default function ShowSatisfaction({ auth, currentId, setCurrentId, setCurrentItem, size }) {
    const [isLoading, toggleLoading] = useState(false);
    const [satisfaction, setSatisfaction] = useState();
    const [defautUser, setDefautUser] = useState()
    const params = useParams()

    const updateData = () => {
        let isMounted = true;
        toggleLoading(true);
        axios.get("/api/satisfaction/show/" + (currentId? currentId : params.id), useToken()).then((res) => {
            if (isMounted) {
                // setCurrentItem(res.data.satisfaction)
                setSatisfaction(res.data.satisfaction)
                const newUser = [];
                if (auth.id != res.data.satisfaction.user_id) {
                    newUser.push({ id: res.data.satisfaction.user_id, address: res.data.satisfaction.user_email, name: res.data.satisfaction.user_nom})
                }
                setDefautUser(newUser)
                toggleLoading(false);
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if (setCurrentItem)setCurrentItem(satisfaction);
    }, [satisfaction])

    useEffect(() => updateData(), [currentId])

    return (
        <div>
            {
                (isLoading || !satisfaction) ?
                    <LoadingPage/>
                :
                    <div>
                        <ShowHeader label={"Satisfaction"} id={currentId} closeDetail={()=>setCurrentId()} size={size}/>
                        <div className='card-container'>
                            <div className='badge-container'>
                                <span>
                                    {satisfaction.user_id != auth.id &&
                                        <span className={"badge-outline badge-outline-" + (satisfaction.seen ? 'green' : 'purple')}>
                                        {   satisfaction.seen ? "Lu" : "Non lu" }
                                        </span>} {
                                            satisfaction.nb_pj > 0 && 
                                            <span className='badge-outline'>
                                                Pièce jointe : {satisfaction.nb_pj}
                                            </span>
                                    }
                                </span>
                            </div>
                            <h3>
                                {/* test */}
                            </h3>
                            <p style={{ whiteSpace: 'pre-line'}}>
                                Site: <span className='text'>{satisfaction.site_nom}</span>
                            </p>
                            <div>
                                Commentaire: <span className='text'>{satisfaction.comment}</span>
                            </div>
                            <div>
                                Demandeur: <span className='text'>{satisfaction.user_nom + ' <' + satisfaction.user_email + '>'}</span>
                            </div>
                            <div>
                                Le: <span className='text'>{moment(satisfaction.created_at).format("DD MMMM YYYY")}</span>
                            </div>
                            <div className='card-action'>
                                <ActionSatisfaction satisfaction={satisfaction} updateData={updateData}  auth={auth} />
                            </div>
                        </div>
                        <Tab auth={auth} name={'satisfaction_id'} value={satisfaction.id} defautUsers={defautUser} updateData={updateData}/>
                    </div>
            }
        </div>
    )
}
