import React, { useState } from 'react'
import InputText from '../../input/InputText'
import MyDocument from './FichePaie';
import Textarea from '../../input/Textarea';
import useToken from '../../util/useToken';

export default function ModalImprimer({ id, paie, show, toggleShow, updateData }) {
    const [siret, setSiret] = useState("51333 31 2011 0 00658");
    const [note, setNote] = useState(null);
    const [toPrint, setToPrint] = useState(false);
    // const [showModal, setShowModal] = useState(show?show:true);

    const handleSaveDone = () => {
        let formData = new FormData();
        setToPrint(true);
        formData.append('note', note);
        axios.post('/api/paie/save_done/' + id, formData, useToken()).then((res)=>{
            if (res.data.success) {
                setToPrint(true);
            }
            else {
                setToPrint(false);
            }
        })
    }
    return (<>
        {paie && show && <div className="modal">
            <div style={{ maxHeight: '60rem', overflowY: 'auto' }}>
                <h3>
                    {(paie.status == "done" || toPrint) ? "Fiche de paie" : "Terminer avant imprimer"}
                </h3>
                {
                    (paie.status == "done" || toPrint) &&
                        <InputText label={"SIRET"} value={siret} onChange={setSiret} />
                }
                {
                    paie && paie.status != "done" && !toPrint &&
                        <Textarea label={"Commentaire"} value={note} onChange={(value) => setNote(value)} required />
                }
                <div className="form-button-container">
                    {
                        !toPrint && paie.status != "done" &&
                        <div className="btn btn-primary" onClick={() => handleSaveDone()}>Terminer</div>
                    }
                    {
                        (toPrint || paie.status == "done") &&
                            <div onClick={() => toggleShow(false)}>
                                <MyDocument updateData={updateData} id={id} siret={siret} setSiret={setSiret} />
                            </div>
                    } 
                    <button className="btn" onClick={() => toggleShow(false)}>Annuler</button>
                </div>
            </div>
        </div>}
    </>
    )
}
