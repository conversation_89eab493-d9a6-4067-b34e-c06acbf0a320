import { Link } from 'react-router-dom';
import MenuView from '../view/MenuView';

export default function Me({auth}) {

    return <div id="content">
        <div>
            {
                auth.role != 'simple' &&
                <div className='tab-menu'>
                    <Link to='/'>
                        Tableau de bord
                    </Link>
                    <span className='active' >
                        Demande personnelle
                    </span>
                </div>
            }
            <MenuView title="">
                <h3 className='sub-title-menu'>Personnelle</h3>
                <div className='palette-container'>
                    <div className='palette-item'>
                        <Link className='link-no-style' to="/equipement/add?me=1">Demande d'équipement ou tenue</Link>
                    </div>
                    <div className='palette-item'>
                        <Link className='link-no-style' to="/flotte/add?me=1">Demande ou problème de flotte</Link>
                    </div>
                    <div className='palette-item'>
                        <Link className='link-no-style' to="/da/add">Demande d'approvisionnement</Link>
                    </div>
                    {
                        auth.role == 'superviseur' && <>
                            <h3 className='sub-title-menu'>Absence</h3>
                            <div className='palette-item'>
                                <Link className='link-no-style' to="/absence/add/conge?me=1">Demande de congé</Link>
                            </div>
                            <div className='palette-item'>
                                <Link className='link-no-style' to="/absence/add/permission?me=1">Demande de permission</Link>
                            </div>
                        </>
                    }
                </div>
            </MenuView>
        </div>
    </div>
}