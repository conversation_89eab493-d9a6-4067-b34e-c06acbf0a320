import {useEffect, useState} from 'react'
import { FiDownload } from 'react-icons/fi';
import LoadingPage from '../loading/LoadingPage';

import ConfirmModal from '../modal/ConfirmModal';
import useToken from '../util/useToken';
import EditPieceJointeModal from './EditPieceJointeModal';

export default function PieceJointe({name, value, updateData}) {
    const [isLoading, toggleLoading] = useState(true)
    const [pieces, setPieces] = useState([])
    const [showConfirmModal, toggleConfirmModal] = useState(false)
    const [showAddModal, toggleAddModal] = useState(false)
    const [pjId, setPjId] = useState(null)

    useEffect(() => {
        let isMounted = true
        const params = new URLSearchParams()
        if  (name !== "dotation_id") {
            params.append(name, value)
        } else {
            params.append(name, value.id)
        }
        axios.get("/api/piece_jointe/tab?" + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else
                    setPieces(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false};
    }, []);

    return <> 
        {
            showAddModal && 
            <EditPieceJointeModal name={name} value={name !== "dotation_id" ? value : value.id} updateData={updateData} closeModal={() => toggleAddModal(false)}/>
        }
        {
            (showConfirmModal && pjId) &&
            <ConfirmModal 
                msg="Etes vous sure de supprimer?"
                confirmAction={() => deletePj(pjId)}
                closeModal={() => toggleConfirmModal(false)}
                />
        }
        {
            isLoading ?
                <LoadingPage/>
            :
            <>
                <div className='tab-list-action'>
                    <div className='action-container'>
                        <span>
                            <span onClick={() => toggleAddModal(true)}>
                                Ajouter une pièce jointe
                            </span>
                        </span>
                    </div>
                </div>
                {
                    pieces.length > 0 ?
                        pieces.map((pj) => (
                            <div className="card-container" key={pj.id}>
                                <div className='item-container'>
                                    <span>
                                        <span>{pj.nature}</span>
                                        <span className='secondary'>{' <' + pj.path + '>'}</span>
                                    </span>
                                    <div>
                                        <a className='link-no-style' target="_blank" href={"/uploads/" + pj.path}>
                                            <FiDownload color='#888'/>
                                        </a>
                                    </div>
                                </div>
                            </div> 
                        ))
                    :
                        <div className="card-container secondary">
                            Aucun fichier trouvé
                        </div>
                }
            </>
        }
    </>
}
