import React, { useEffect, useState } from 'react';
import {Link, useParams} from 'react-router-dom'
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import moment from 'moment';
import LastSanction from './LastSanction';
import InputPointage from '../input/InputPointage';
import Textarea from '../input/Textarea';
import InputUser from '../input/InputUser';
import LoadingScreen from '../loading/LoadingScreen';
import InputCheckBox from '../input/InputCheckBox';
import InputSite from '../input/InputSite';
import InputAgent from '../input/InputAgent';

const EditSanction = ({auth, title, action}) => {
    const params = useParams()
    const [sanction, setSanction] = useState(null)
    const [objet, setObjet] = useState("")
    const [montant, setMontant] = useState(0)
    const [employe, setEmploye] = useState(null)
    const [absence, setAbsence] = useState(false)
    const [lastSanctions, setLastSanctions] = useState(null)
    const [pointage, setPointage] = useState(null)
    const [superviseur, setSuperviseur] = useState(null)
    const [site, setSite] = useState(null)
    const [motif, setMotif] = useState("")
    const [mesure, setMesure] = useState("")
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleChangeEmploye = (a) => {
        setEmploye({
            id: a.id,
            nom: a.nom,
            matricule: a.matricule
        })
        setPointage(null)
    }

    useEffect(() => {
        let isMounted = true
        if(employe){
            setLastSanctions(null)
            axios.get("/api/employe/last_sanction/" + employe.id, useToken())
            .then((res) => {
                if(isMounted) setLastSanctions(res.data)
            })
        }
        return () => {isMounted = false}
    }, [employe])

    const handleChangePointage = (ptg) => {
        setPointage({
            id: ptg.id,
            date_pointage: ptg.date_pointage,
            sanction_id: ptg.sanction_id
        })
        setSite(ptg.site)
    }


    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const data = {
            employe_id: employe ? employe.id : '',
            absence: absence,
            pointage_id: pointage ? pointage.id : '',
            site_id: site ? site.id : '',
            objet: objet,
            montant: montant,
            motif: motif,
            mesure: mesure,
            superviseur_id: superviseur ? superviseur.id : '',
        }
        axios.post(action + (params.id ? "/" + params.id : ""), data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    useEffect(() => {
        let isMounted = true
        if(params.id){
            axios.get('/api/sanction/show/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    const sanction = res.data
                    setSanction(sanction)
                    if(sanction.objet) setObjet(sanction.objet)
                    if(sanction.montant) setMontant(sanction.montant)
                    if(sanction.motif) setMotif(sanction.motif)
                    if(sanction.absence) setAbsence(sanction.absence)
                    if(sanction.employe) setEmploye({
                        id: sanction.employe_id,
                        nom: sanction.employe,
                        matricule: matricule(sanction) 
                    })
                    else 
                        setLastSanctions([])
                    if(sanction.site) setSite({
                        id: sanction.site_id,
                        nom: sanction.site
                    })
                    if(sanction.date_pointage) setPointage({
                        id: sanction.pointage_id,
                        date_pointage: sanction.date_pointage,
                    })
                    if(sanction.sup_nom) setSuperviseur({
                        id: sanction.superviseur_id,
                        name: sanction.sup_nom,
                        email: sanction.sup_email,
                    })
                }
            })
        }
        else 
            setLastSanctions([])
        return () => { isMounted = false };
    }, [])

    return (
        <div id="content">
            {
                notification ? 
                    <Notification next={notification.id ? "/sanction?id=" + notification.id : "/sanction"} message={notification.success}/>
                :
                <div>
                    <div className="title-container">
                        <h2>{title}</h2>
                    </div>
                    {
                        lastSanctions === null ?
                            <LoadingScreen/>
                        : <div>
                            {
                                lastSanctions.length > 0 &&
                                <LastSanction data={lastSanctions} onlyDone/>
                            }
                            {
                                lastSanctions.length > 0 &&
                                <div className="input-container-btn">
                                    <button onClick={() => setLastSanctions([])}>Continuer</button>
                                </div>
                            }
                            {
                                lastSanctions.length == 0 &&
                                <form onSubmit={handleSubmit}>
                                    {
                                        (sanction && auth.id != sanction.user_id) &&
                                        <div>
                                            <div className="card-container">
                                                <h3>
                                                    {matricule(sanction)} {sanction.employe} 
                                                </h3>
                                                <div>
                                                    Site: <span className='text'>{sanction.site}</span>
                                                </div>
                                                {
                                                    absence ?
                                                        <div>Sans pointage</div>
                                                    :
                                                        <div>
                                                            Date du service : <span className='text'>
                                                                {
                                                                    moment(sanction.date_pointage).format("dddd DD MMM YYYY") 
                                                                    + " " + (moment(sanction.date_pointage).format("HH:mm:ss") == "07:00:00" ? " JOUR" : " NUIT")
                                                                }
                                                            </span>
                                                        </div>
                                                }
                                                <p>
                                                    Motif: <span className='text'>{sanction.motif}</span>
                                                </p>
                                            </div>
                                        </div>
                                    }
                                    {
                                        (!params.id || (sanction && auth.id == sanction.user_id)) &&
                                        <div>
                                            <InputAgent
                                                required
                                                label="Employé"
                                                value={employe} 
                                                onChange={handleChangeEmploye}/>
                                            <div className='field-container'>
                                                <InputCheckBox label="Sans pointage" checked={absence} onChange={setAbsence}/>
                                            </div>
                                            {
                                                (employe && !absence) &&
                                                <div>
                                                    <InputPointage
                                                        required
                                                        employeId={employe.id}
                                                        value={pointage} 
                                                        onChange={handleChangePointage}/>
                                                    {
                                                        (pointage && pointage.sanction_id) && 
                                                        <Link className='link-no-style' to={"/sanction/show/" + pointage.sanction_id}>
                                                            <span className='danger'>Cliquez ici pour voir la sanction déjà existante.</span>
                                                        </Link>
                                                    }
                                                </div>
                                            }
                                            {
                                                (pointage || absence) &&
                                                <InputSite
                                                    withoutDelete
                                                    label="Site"
                                                    value={site} 
                                                    onChange={setSite}
                                                    required={absence}
                                                    disabled={!absence}/>
                                            }
                                        </div>
                                    }
                                    {
                                        ['rh', 'resp_rh'].includes(auth.role) && 
                                        <div>
                                            <InputText 
                                                required
                                                label="Objet"
                                                value={objet} 
                                                onChange={setObjet}/>
                                            <InputText 
                                                label="Montant"
                                                value={montant} 
                                                onChange={setMontant}
                                                type="number"/>
                                        </div>
                                    }
                                    {
                                        (!params.id || (sanction && auth.id == sanction.user_id)) && 
                                        <Textarea
                                            required
                                            label="Motif"
                                            value={motif}
                                            onChange={setMotif}/>
                                    }
                                    {
                                        (auth.role == "room" && (!params.id || (sanction && auth.id == sanction.user_id))) && 
                                        <Textarea
                                            required
                                            label="Mesure prise"
                                            value={mesure}
                                            onChange={setMesure}/>
                                    }
                                    {
                                        (['rh', 'resp_rh'].includes(auth.role) || (['room'].includes(auth.role) && (!sanction || auth.id == sanction.user_id))) &&
                                        <InputUser
                                            role="superviseur" 
                                            required
                                            value={superviseur} 
                                            onChange={setSuperviseur}/>
                                    }
                                    {
                                        error &&
                                        <div className='container-error'>
                                            {error}
                                        </div>
                                    }
                                    {
                                        lastSanctions!== null &&
                                        <ButtonSubmit disabled={submitDisabled}/>
                                    }
                                </form>
                            }
                        </div>
                    }
                </div>
            }
        </div>
    )
}

export default EditSanction