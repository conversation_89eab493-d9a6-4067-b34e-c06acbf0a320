import {GoogleMap,Marker,useLoadScript} from "@react-google-maps/api";
import { useMemo, useState } from "react";

export default function MapModal({closeModal, handleSelectPosition}) {
    const [latitude, setLatitude] = useState(null)
    const [longitude, setLongitude] = useState(null)
    const { isLoaded } = useLoadScript({
        googleMapsApiKey: process.env.MIX_GOOGLE_MAPS_API_KEY
    });

    const mapContainerStyle = {
        width: "100%",
        height: "400px",
        margin: "0 auto"
    };
    
    const center = useMemo(
        () => ({
            lat: latitude ? latitude : -18.933333,
            lng: longitude ? longitude : 47.516667
        }),
        [latitude, longitude]
    );

    const clickMap = (event) => {
        const latClick = event.latLng.lat();
        const lngClick = event.latLng.lng();
        setLatitude(latClick);
        setLongitude(lngClick);
    };

    if (!isLoaded) return <div>Chargement de la carte...</div>;
    return (
        <div className='modal'>
            <div>
                <h2>Plan de répérage</h2>
                <div style={mapContainerStyle}>
                    <GoogleMap
                        zoom={(latitude && longitude) ? 18 : 6}
                        center={center}
                        mapContainerStyle={{
                            width: "100%",
                            height: "100%",
                        }}
                        onClick={clickMap}
                    >
                        {latitude && longitude ? (
                            <Marker
                                position={{ lat: latitude, lng: longitude }}
                            />
                        ) : null}
                    </GoogleMap>
                </div>
                <div className='form-button-container'>
                    <button className='btn-primary' onClick={() => handleSelectPosition(latitude, longitude)} disabled={!latitude || !longitude}>Valider</button>
                    <button type='button' onClick={closeModal}>Annuler</button>
                </div>
            </div>
        </div>
    )
}