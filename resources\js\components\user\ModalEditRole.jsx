import axios from 'axios'
import React, { useEffect, useState } from 'react'
import useToken from '../util/useToken'

export default function ModalEditRole({auth, closeModal}) {
    const [roles, setRoles] = useState([])
    const [searchValue, setSearchValue] = useState('')

    const handleGetRole = () => {
        axios.get('/api/user/get_all_role', useToken())
        .then((res) => {
            if (res.data.roles) {
                setRoles(res.data.roles)
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }
console.log(roles)
    useEffect(() => {
        handleGetRole()
    }, [])

    const handleSubmit = (role) => {
        axios.post('/api/user/edit_role/' + role, {}, useToken())
        .then((res) => {
            if (res.data.success) {
                window.location.reload();
                closeModal()
            }
        })
        .catch((e) => {
            console.error(e)
        })
        closeModal()
    }

    // useEffect(() => {
    //     const cloneRoles = [...roles]
    //     const newRoles = cloneType.filter((tp) => tp.label.toLowerCase().includes(searchValue.toLocaleLowerCase()));
    //     setTypeNote(newTypes);
    // }, [searchValue])

    return (
        <div className='modal' style={{zIndex: 30000}}>
            <div>
                {/* <h3>Edition du rôle</h3> */}
                {/* <div className='search-container'>
                    <input type="text" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} placeholder="Ex: admin" />
                </div> */}
                {
                    roles.map((item, index) => (
                        <div className='table line-container' key={index} onClick={() => handleSubmit(item.name)}>
                            <span>{item.name}</span>
                        </div>
                    ))
                }
                <div className="form-button-container">
                    <button type="button" onClick={closeModal}>
                        Annuler
                    </button>
                </div>
            </div>
        </div>
    )
}
