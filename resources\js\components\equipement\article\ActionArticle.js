import React, { useState } from 'react';

import NoteModal from '../../input/NoteModal';

export default function ActionArticle({auth, article, updateData}) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [currentAction, setAction] = useState(null)

    const handleDoneArticle = (id) => {
        setAction({
            header: "Article bien reçu",
            request: "/api/article_equipement/done/" + id,
            required: false
        })
        toggleNoteModal(true)
    }
    const handleCancelArticle = (id) => {
        setAction({
            header: "Article annule",
            request: "/api/article_equipement/cancel/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction} 
                updateData={() => updateData()} 
                closeModal={() => toggleNoteModal(false)}/> 
        }
        <div className='action-container'>
            {
                auth.role === "achat" &&
                <span onClick={() => handleDoneArticle(article.id)}>Terminer</span>
            }
            <span onClick={() => handleCancelArticle(article.id)}>Annuler</span>
        </div>
    </div>
}