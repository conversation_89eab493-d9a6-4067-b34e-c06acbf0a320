<?php

namespace App\Http\Controllers;

use App\Models\Deduction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class DeductionController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function search(Request $request){
        $searchArray = [];
        if($request->id){
            $searchArray[] = " ded.id = ".$request->id . " ";
        }
        else {
            if($request->created_at){
                $searchArray[] =  " ded.created_at > '$request->created_at 00:00:00' and ded.created_at <= '$request->created_at 23:59:59' ";      
            }
            if( $request->employe_id){
                $searchArray[] = " ded.employe_id = '$request->employe_id' ";
            }
            if ($request->status) {
                $searchArray[] = " ded.status = '$request->status' ";
            }
            if ($request->date_paie) {
                $searchArray[] = " ded.date_paie = '$request->date_paie' and ded.status = 'done' ";
            }
        }
        $query_where = "";
        $query_and = "";
        if ($request->offset || $request->offset > -1) {
            $orderBy = " order by ded.id DESC limit ". $request->offset . ", 30";
        }
        else {
            $orderBy = " order by ded.id DESC";
        }
        if(count($searchArray) > 0) {
            $query_where = " WHERE ".implode(" and", $searchArray) . " ";
            $query_and = " AND ".implode(" and ", $searchArray). "";
        }
        $query_where = $query_where . " ". $orderBy;
        $query_and = $query_and . " " . $orderBy;
        return compact("query_where", "query_and");
    }

    public function index(Request $request){
        if(in_array($request->user()->role, ['resp_rh', 'validateur'])){
            $sql = "SELECT ded.id, ded.motif, ded.montant, ded.date_paie, ded.created_at, ded.status, 
                    stat.description as 'status_description', stat.color as 'status_color', ded.employe_id,
                    emp.nom as 'employe', emp.societe_id, emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi,
                    u.name as 'user_nom', u.email as 'user_email',
                    (SELECT p.id FROM paies p where status ='done' and p.date_paie = ded.date_paie and p.employe_id = ded.employe_id  limit 1) as 'paie_id'
                    FROM deductions ded 
                    LEFT JOIN employes emp on emp.id = ded.employe_id 
                    LEFT JOIN users u on u.id = ded.user_id
                    LEFT JOIN `status` stat on stat.name = ded.status
                    ";
            $deductions = DB::select($sql.($this->search($request))['query_where']);
            // $search = DeductionController::search($request);
            return response(compact('deductions'));
        }
        return response(["error" =>"EACCES"]);
    }

    public function store(Request $request){
        if (in_array($request->user()->role, ['resp_rh'])){
            $deduction = new Deduction();
            $validator = Validator::make($request->all(), [
                'employe_id' => ['required'],
                'montant' => ['required'],
                'motif' => ['required'],
            ]);
            if ($validator->fails()) {
                return \response()->json(['error' => $validator->errors()->first()]);
            }
            $deduction->fill($request->all());
            $deduction->user_id = $request->user()->id;
            $deduction->created_at = new \DateTime();
            $deduction->updated_at = new \DateTime();
            $deduction->status = "demande";
            if($deduction->save()){
                HistoriqueController::new_deduction($request, $deduction);
                return response()->json(["success" => "Deduction bien enregistré"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function edit(Request $request, $id){
        $deduction = Deduction::find($id);
        $old_deduction = clone $deduction;
        if(in_array($request->user()->role, ['resp_rh'])){
            $validator = Validator::make($request->all(), [
                'employe_id' => ['required'],
                'montant' => ['required'],
                'motif' => ['required'],
            ]);
            if ($validator->fails()) {
                return \response()->json(['error' => $validator->errors()->first()]);
            }
            $deduction->update($request->all());
            $deduction->updated_at = new \DateTime();
            if ($deduction->save()) {
                HistoriqueController::update_deduction($request, $old_deduction, 'Modification');
                return response(["success" => "Modification success", "id" => $deduction->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayer"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function do_traite(Request $request, $id){
        $deduction = Deduction::find($id);
        $old_deduction = clone $deduction;
        if (in_array($request->user()->role, ['resp_rh'])) {
            $validator = Validator::make($request->all(), [
                'employe_id' => ['required'],
                'montant' => ['required'],
                'motif' => ['required'],
            ]);
            if ($validator->fails()) {
                return \response()->json(['error' => $validator->errors()->first()]);
            }
            $deduction->status = "traite";
            $deduction->update($request->all());
            $deduction->updated_at = new \DateTime();
            if ($deduction->save()) {
                HistoriqueController::update_deduction($request, $old_deduction, 'Deduction en cours de traitement');
                return response(["success" => "Deduction en cours de traitement", "id" => $deduction->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayer"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $deduction = Deduction::find($id);
        $old_deduction = clone $deduction;
        if ($request->user()->id == $deduction->user_id && $deduction->status = "draft") {
            $validator = Validator::make($request->all(), [
                'employe_id' => ['required'],
                'montant' => ['required'],
                'motif' => ['required'],
            ]);
            if ($validator->fails()) {
                return \response()->json(['error' => $validator->errors()->first()]);
            }
            $deduction->status = "demande";
            $deduction->update($request->all());
            $deduction->updated_at = new \DateTime();
            if($deduction->save()) {
                HistoriqueController::update_deduction($request,$old_deduction, 'Renvoie de la deduction');
                return response(["success" => "Renvoie de la deduction reussi", "id" => $deduction->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayer"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_deduction(Request $request, $id){
        $deduction = Deduction::find($id);
        if(in_array($deduction->status, ['demande', 'traite']) && $request->user()->role == 'resp_rh'){
            $validator = Validator::make($request->all(),[
                'note' => 'required',
            ]);
            if($validator->fails()){
                return response(['error' => $validator->errors()->first()]);
            }
            $deduction->status = 'draft';
            $deduction->updated_at = new \DateTime();
            $deduction->note_id = HistoriqueController::action_deduction($request, "Deduction annulé", $id);
            if ($deduction->save()) {
                return response(["success"=> "Deduction annulé", "id"=>$deduction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done(Request $request, $id) {
        $deduction = Deduction::find($id);
        if(in_array($request->user()->role, ["resp_rh"]) && $deduction && in_array($deduction->status, ['demande', 'traite'])){
            $verifyDatePaie = DB::select("SELECT * FROM paies WHERE date_paie = '$request->date_paie' AND (status = 'traite' or status = 'done') AND employe_id = $deduction->employe_id");
            if (count($verifyDatePaie) > 0) {
                return (["error" => "Date de paie invalide"]);
            }
            if ($request->mensualite) {
                $validator = Validator::make($request->all(),[
                    'date_paie' => 'required',
                    'repartitions' => 'required'
                ]);
            }
            else{
                $validator = Validator::make($request->all(), [
                    'date_paie' => 'required',
                ]);
            }
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $deduction->status = "done";
            $deduction->date_paie = $request->date_paie;
            if ($request->mensualite) {
                $total = $deduction->montant;
                $deduction->montant = $request->mensualite;
                $deduction->group_id = $deduction->group_id;
                $deduction->montant_total = $total;
                $repartitions = json_decode($request->repartitions, true);
                foreach ($repartitions as $rep) {
                    $new_deduction = new Deduction();
                    $new_deduction->motif = $deduction->motif;
                    $new_deduction->employe_id = $deduction->employe_id;
                    $new_deduction->group_id = $deduction->id;
                    $new_deduction->date_paie = $rep['date_paie'];
                    $new_deduction->montant = $rep['montant'];
                    $new_deduction->user_id = $request->user()->id;
                    $new_deduction->montant_total = $total;
                    $new_deduction->status = "done";
                    $new_deduction->created_at = new \DateTime();
                    $new_deduction->updated_at = new \DateTime();
                    $new_deduction->save();
                    $paie = DB::select("SELECT * FROM paies WHERE date_paie = '$new_deduction->date_paie' AND (status = 'demande') AND employe_id = $deduction->employe_id");
                    if (count($paie) > 0) {
                        $new_request = clone $request;
                        $new_request->merge(['employe_id' => $new_deduction->employe_id]);
                        $new_request->merge(['date_paie'=> $new_deduction->date_paie]) ;
                        $new_request->merge(['paie_id' => $paie[0]->id]);
                        // PaieController::RecalculePaie($request);
                        $new_request->merge(['recalcul' => 1]);
                        $paieController = new PaieController();
                        $paieController->generate_paie($new_request, $new_deduction->employe_id);
                    }
                    HistoriqueController::new_repartition_deduction($request, $new_deduction);
                }
            }
            $deduction->updated_at = new \DateTime();
            $deduction->note_id = HistoriqueController::action_deduction($request, 'Deduction terminé', $id);
            if($deduction->save()){
                $paie = DB::select("SELECT * FROM paies WHERE date_paie = '$deduction->date_paie' AND (status = 'demande') AND employe_id = $deduction->employe_id");
                if (count($paie) > 0) {
                    $new_request = clone $request;
                    $new_request->merge(['employe_id' => $deduction->employe_id]);
                    $new_request->merge(['date_paie' => $deduction->date_paie]);
                    $new_request->merge(['paie_id' => $paie[0]->id]);
                    // PaieController::RecalculePaie($request);
                    $new_request->merge(['recalcul' => 1]);
                    $paieController = new PaieController();
                    $paieController->generate_paie($new_request, $deduction->employe_id);
                }
                return response(["success" => "Deduction terminée", "id" => $deduction->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function show($id, Request $request){
        if (in_array($request->user()->role, ["resp_rh", "validateur"]) ) {
            $deduction = DB::select("SELECT ded.id, ded.montant, ded.motif, ded.status, ded.user_id,
                stat.description as 'status_description', stat.color as 'status_color',
                emp.nom as 'employe', emp.num_emp_soit, emp.numero_employe, emp.numero_stagiaire, emp.num_emp_saoi,
                emp.societe_id, us.name as 'user_nom', us.email as 'user_email', ded.date_paie, ded.created_at,
                (SELECT p.id FROM paies p where status ='done' and p.date_paie = ded.date_paie and p.employe_id = ded.employe_id  limit 1) as 'paie_id'
                FROM deductions ded 
                LEFT JOIN users us on us.id = ded.user_id 
                LEFT JOIN employes emp on emp.id = ded.employe_id
                LEFT JOIN `status` stat on stat.name = ded.status
                WHERE ded.id = ?", [$id])[0];
            $pieces = DB::select("SELECT pj.id FROM piece_jointes pj 
                WHERE pj.deduction_id = ?", [$id]);
            $deduction->nb_pj = count($pieces);
            return response()->json($deduction);
        }
        return response(["error" => "EACCES"]);
    }
}
