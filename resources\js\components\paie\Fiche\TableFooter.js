import React, { Fragment } from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";

const borderColor = "";
const styles = StyleSheet.create({
    row: {
        flexDirection: "row",
        // borderBottomColor: "#bff0fd",
        // borderBottomWidth: 1,

        alignItems: "center",
        // height: 23,
        fontSize: 7,
        lineHeight: 1,
        // fontStyle: "bold",
    },
    leftRow: {
        flexDirection: "row",
        // borderBottomColor: "#bff0fd",
        borderBottomWidth: 1,

        alignItems: "center",
        // height: 23,
        fontSize: 7,
        lineHeight: 1,

        // fontStyle: "bold",
    },
    // border: {
    //     borderBottomWidth: 1,
    // },

    rubrique: {
        width: "29%",
        textAlign: "left",
        borderRightColor: borderColor,
        // borderRightWidth: 1,
        paddingTop: 6,
        paddingBottom: 6,
        marginLeft: 2,
    },
    base: {
        width: "12%",
        borderRightColor: borderColor,
        // borderRightWidth: 1,
        borderRightHeight: 10,
        textAlign: "left",
        padding: 6,
    },
    taux: {
        width: "10%",
        borderRightColor: borderColor,
        // borderRightWidth: 1,
        textAlign: "left",
        padding: 6,
    },
    deduire: {
        width: "12%",
        borderRightWidth: 1,
        textAlign: "left",
        padding: 6,
    },
    apayer: {
        width: "15%",
        borderRightWidth: 1,
        borderBottomWidth: 1,
        textAlign: "left",
        padding: 6,
    },
    chargesPatronale: {
        width: "22%",
        textAlign: "left",
        borderBottomWidth: 1,   
        padding: 6,
    },
    charges: {
        flexDirection: "row",
    },
    patronaleTaux: {
        width: "50%",
        borderRightWidth: 1,
        padding: 6,
    },
    patronaleMontant: {
        width: "45%",
        padding: 6,
    },
});

const TableFooter = ({ items }) => {
    const rows = (
        <View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>CONGES</Text>
                <Text style={styles.base}>Acquis</Text>
                <Text style={styles.taux}> Pris</Text>
                <Text style={styles.deduire}>Reste</Text>
                <Text style={styles.apayer}>NET A PAYER</Text>
                <View style={styles.chargesPatronale}>
                    <Text> </Text>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}> {" " }</Text>
                <Text style={styles.base}>{ " 2.50"}</Text>
                <Text style={styles.taux}> {items.conge_pris}</Text>
                <Text style={styles.deduire}>{items.conge_reste}</Text>
                <Text style={styles.apayer}> {parseInt(items.net_a_payer)}</Text>
                <View style={styles.chargesPatronale}>
                    <Text> </Text>
                </View>
            </View>
        </View>
    );
    return <Fragment>{rows}</Fragment>;
};

export default TableFooter;
