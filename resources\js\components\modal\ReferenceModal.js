import React, { useEffect, useState } from 'react';

import InputText from '../input/InputText';
import { useLocation, useNavigate } from 'react-router-dom';

export default function TextModal({label, name, type, closeModal}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [reference, setReference] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleOk = () => {
        let params = new URLSearchParams(location.search)
        params.set("id", reference)
        navigate(location.pathname + "?" + params)
        closeModal()
    }
    
    useEffect(() => {
        disableSubmit(!/^\d+$/.test(reference) || reference == 0)
    }, [reference])

    return <div className='modal'>
        <div>
            <div className='input-container'>
                <InputText
                    required
                    type={type ? type : "text"}
                    label={label}
                    value={reference}
                    onChange={setReference}
                    onEnter={handleOk}/>
            </div>
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Ok</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
    
}
