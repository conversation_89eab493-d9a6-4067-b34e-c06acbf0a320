import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

export default function TypeServiceEquipement({onChange,closeModal, useLink}) {
    const types = [
        { 'label': 'Achat', 'value': 'achat' },
        { 'label': 'Tenue', 'value': 'tenue' },
    ]
    const navigate = useNavigate()
    const location = useLocation()

    const handleSelect = (value) => {
        if (useLink) {
            let params = new URLSearchParams(location.search)
            params.set("type_service", value)
            navigate(location.pathname + "?" + params)
        }
        onChange(value)
        closeModal()
    }

    return (
        <div className='modal'>
            <div className='input-container'>
                <h2>Type d'equipement</h2>
                {
                    types.map((item, index) => {
                        return (
                            <div className='table line-container' key={index} onClick={() => handleSelect(item.value)}>
                                <span>{item.label}</span>
                            </div>
                        )
                    })
                }
                <div className='form-button-container'>
                    <button onClick={closeModal}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
