<?php

namespace App\Http\Controllers;

use App\Models\SuiviJuridique;
use App\Models\Juridique;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class SuiviJuridiqueController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public static function index(Request $request) {
        if($request->plainte_id){
            $da_items = DB::select("SELECT s.id, s.commentaire, s.created_at, u.name as 'user'
                from suivi_juridiques s 
                left join users u on u.id = s.user_id
                where s.plainte_id = ?
                order by s.created_at desc", [$request->plainte_id]);
        }
        else {
            $da_items = DB::select("SELECT s.id, s.commentaire, s.created_at, u.name as 'user'
                from suivi_juridiques s 
                left join users u on u.id = s.user_id
                where s.juridique_id = ?
                order by s.created_at desc", [$request->juridique_id]);
        }
        return response()->json($da_items);
    }

    public static function show(Request $request, $id) {
        $isPlainte = DB::select("SELECT sj.plainte_id FROM suivi_juridiques sj WHERE sj.id = ?", [$id])[0]->plainte_id;
        if($isPlainte != null){
            $suivi = DB::select("SELECT sj.id, sj.plainte_id, sj.commentaire, sj.created_at, j.reference, j.agence_id, 
                j.police, j.agent, j.fait, j.status, st.nom as 'site',
                stat.description as 'status_description', stat.color as 'status_color', 
                us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', y.id as 'seen',
                (SELECT COUNT(*) FROM piece_jointes pj WHERE pj.plainte_id = j.id) as nb_pj
                from suivi_juridiques sj
                LEFT JOIN (SELECT s.id, s.juridique_id FROM seen s WHERE s.user_id = ?) y ON y.juridique_id = sj.id
                LEFT JOIN plainte_agents j on j.id = sj.plainte_id
                LEFT JOIN sites st on st.idsite = j.site_id
                LEFT JOIN users us on us.id = sj.user_id
                LEFT JOIN users ur on ur.id = us.real_email_id
                LEFT JOIN `status` stat on stat.name = j.status
                WHERE sj.id = ?
                order by sj.created_at desc", [$request->user()->id, $id])[0];
        }
        else{
            $suivi = DB::select("SELECT sj.id, sj.juridique_id, sj.commentaire, sj.created_at, j.reference, j.agence_id, j.debiteur, j.contrat, 
                j.facture, j.montant, ec.nom as 'status_description', ec.color as 'status_color', 
                us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', y.id as 'seen',
                (SELECT COUNT(*) FROM piece_jointes pj WHERE pj.juridique_id = j.id) as nb_pj
                from suivi_juridiques sj
                LEFT JOIN (SELECT s.id, s.juridique_id FROM seen s WHERE s.user_id = ?) y ON y.juridique_id = sj.id
                LEFT JOIN juridiques j on j.id = sj.juridique_id
                LEFT JOIN etape_contentieux ec on ec.id = j.etape_id
                LEFT JOIN users us on us.id = sj.user_id
                LEFT JOIN users ur on ur.id = us.real_email_id
                WHERE sj.id = ?
                order by sj.created_at desc", [$request->user()->id, $id])[0];
        }
        return response()->json($suivi);
    }

    protected static function search(Request $request){
        $searchArray = [];
        if($request->created_at)
            $searchArray[] = " j.created_at > '" . $request->created_at . " 00:00:00' and ". 
                "j.created_at <= '" . $request->created_at . " 23:59:59' ";
        if($request->user_id)
            $searchArray[] = " j.user_id = " . $request->user_id . " ";
            
        $query_where = "";
        if(count($searchArray) > 0) 
            $query_where = " and " . implode(" and ", $searchArray) . " ";
        $query_where = $query_where . " order by sj.created_at desc limit ". $request->offset . ", 30";

        return $query_where;
    }

    public static function unread_recouvrement(Request $request) {
        $search = SuiviJuridiqueController::search($request);
        $suivis = DB::select("SELECT sj.id, sj.commentaire, sj.created_at, j.debiteur, j.contrat, 
            ec.nom as 'status_description', ec.color as 'status_color', 
            us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email'
            from suivi_juridiques sj
            LEFT JOIN (SELECT s.id, s.juridique_id FROM seen s WHERE s.user_id = ?) y ON y.juridique_id = sj.id
            LEFT JOIN users us on us.id = sj.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN juridiques j on j.id = sj.juridique_id
            LEFT JOIN etape_contentieux ec on ec.id = j.etape_id
            WHERE sj.juridique_id is not null and y.id is null and sj.user_id != ? " . $search, [$request->user()->id, $request->user()->id]);
        return response()->json(compact('suivis'));
    }

    public static function unread_plainte(Request $request) {
        $search = SuiviJuridiqueController::search($request);
        $suivis = DB::select("SELECT sj.id, sj.commentaire, sj.created_at,
            j.agent, j.fait, st.nom as 'site', 
            stat.description as 'status_description', stat.color as 'status_color', 
            us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email'
            from suivi_juridiques sj
            LEFT JOIN (SELECT s.id, s.juridique_id FROM seen s WHERE s.user_id = ?) y ON y.juridique_id = sj.id
            LEFT JOIN users us on us.id = sj.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN plainte_agents j on j.id = sj.plainte_id
            LEFT JOIN sites st on st.idsite = j.site_id
            LEFT JOIN `status` stat on stat.name = j.status
            WHERE sj.plainte_id is not null and y.id is null and sj.user_id != ? " . $search, [$request->user()->id, $request->user()->id]);
        return response()->json(compact('suivis'));
    }

    public static function store(Request $request) {
        $suivi = new SuiviJuridique();
        if($request->commentaire != "."){
            $suivi->juridique_id = $request->juridique_id;
            $suivi->plainte_id = $request->plainte_id;
            $suivi->commentaire = $request->commentaire;
            $suivi->user_id = $request->user()->id;
            $suivi->created_at = new \DateTime();
            $suivi->updated_at = new \DateTime();
            $suivi->save();
        }
        if($request->plainte_id)
            HistoriqueController::action_plainte($request, "Nouvel élément", $request->plainte_id);
        else if($request->juridique_id){
            $juridique = Juridique::find($request->juridique_id);
            $juridique->next_suivi = (new \DateTime())->add(new \DateInterval('P7D'));
            if($request->commentaire != ".")
                $juridique->last_suivi_id = $suivi->id;
            $juridique->save();
            HistoriqueController::action_juridique($request, "Nouvel élément", $request->juridique_id);
        }
        return response(["success" => "Suivi bien enregistré"]);
    }

    public static function store_plainte(Request $request) {
        $suivi = new SuiviJuridique();
        $suivi->plainte_id = $request->plainte;
        $suivi->commentaire = $request->commentaire;
        $suivi->user_id = $request->user()->id;
        $suivi->created_at = new \DateTime();
        $suivi->updated_at = new \DateTime();
        $suivi->save();
        return response(["success" => "Suivi bien enregistré"]);
    }
}
