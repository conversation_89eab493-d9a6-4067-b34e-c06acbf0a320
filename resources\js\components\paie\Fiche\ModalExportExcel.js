import React, { useEffect, useState } from 'react'
import ExcelPaie from './paie';
import InputMonthYear from '../../input/InputMonthYear';
import useToken from '../../util/useToken';
import moment from 'moment';

export default function ModalExportExcel({closeModal}) {
    const [paies, setPaies] = useState([]);
    const [datePaie, setDatePaie] = useState({
        month: "",
        year: ""
    })
    const [showElement, setShowElement] = useState(false);
    const [numberPaie, setNumberPaie] = useState(0)
    const [searchButton, toggleSearchButton] = useState(true)
    const [exportButton, toggleExportButton] = useState(false);
    const statusList = [{ value: 'demande', label: 'Demande' }, { value: 'traite', label: 'En cours de traitement' }]
    const [disabledSearch, toggleSearch] = useState(true);

    // const getStatus = (value) => {
    //     let selectedMode = null;
    //     statusList.forEach(st => {
    //         if (st.value == value)
    //             selectedMode = st.value;
    //     });
    //     return selectedMode;
    // } 

    useEffect(() => {
        if(moment().isBefore(moment().set('date', 20))){
            setDatePaie({
                month: (moment().subtract(1, 'month')).format('MM'),
                year: (moment().subtract(1, 'month')).format('YYYY')
            })
        }
        else{
            setDatePaie({
                month: moment().format('MM'),
                year: moment().format('YYYY')
            })
        }
    }, [])

    const onSearch = () => {
        let date_paie = datePaie.year + '-' + datePaie.month + '-20';
        axios.get('/api/paie?status=done' + '&date_paie=' + date_paie, useToken())
            .then((res) => {
                setNumberPaie(res.data.paies.length)
                setPaies(res.data.paies)
                setShowElement(true)
                if (res.data.paies.length > 0) {
                    toggleSearchButton(false)
                    toggleExportButton(true)
                }
            })
    }

    useEffect(() => {
        if (datePaie.year.toString().trim() && datePaie.month.trim()) {
            onSearch()
        }
    }, [datePaie])

    useEffect(() => {
        toggleSearch(!(datePaie.year.toString().trim() && datePaie.month.trim()))
        if (datePaie.year.toString().trim() && datePaie.month.trim()) {
            toggleSearchButton(true)
            toggleExportButton(false)
        }
        else if (paies && paies.length> 0) {
            toggleExportButton(true)
            toggleSearchButton(false)
        }
    }, [datePaie])



    return (
        <div className="modal">
            <div>
                <h2>Exportation</h2>
                <div style={{ marginBottom: 50 }}>
                    <InputMonthYear label="Date Paie" value={datePaie} onChange={setDatePaie} required />
                </div>
                    {showElement &&
                        <div >
                            {numberPaie + " élement(s) trouvé(s)"}
                        </div>
                    }
                <div className="form-button-container">
                    {/* {searchButton &&
                        <button onClick={() => onSearch()} className='btn btn-primary' disabled={disabledSearch}>Chercher</button>
                    } */}
                    {exportButton && <ExcelPaie paie={paies} datePaie={datePaie.year.toString()+"-"+datePaie.month}/>}
                    <button type="button" onClick={closeModal}>
                        Annuler
                    </button>
                </div>
            </div>
        </div>
    )
}
