import React from 'react';
export default function InputStock({label, name, value, onChange, type, disabled, required}) {
    return <div className='input-container'>
        <label>{label} {required && <span className='danger'>*</span>}</label>
        <input
            name={name}
            type={type}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            step="any"
        />
    </div>
}