import React, { useEffect, useRef, useState, lazy} from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useLocation } from 'react-router-dom'

const ShowAbsence = lazy(() => import('../absence/ShowAbsence'))
const ShowApprovisionnement = lazy(() => import('../approvisionnement/ShowApprovisionnement'))
const ShowEquipement = lazy(() => import('../equipement/ShowEquipement'))
const ShowFaitMarquant = lazy(() => import('../fait/ShowFaitMarquant'))
const ShowFlotte = lazy(() => import('../flotte/ShowFlotte'))
const ShowPrime = lazy(() => import('../prime/ShowPrime'))
const ShowSanction = lazy(() => import('../sanction/ShowSanction'))
const ShowSav = lazy(() => import('../sav/ShowSav'))
const ShowVisitePoste = lazy(() => import('../visite/ShowVisitePoste'))
const ShowDeduction = lazy(() => import('../Deduction/ShowDeduction'))
const ShowPaie = lazy(() => import('../paie/ShowPaie'))
const ShowAvance = lazy(() => import('../avance/ShowAvance'))
const ShowService24 = lazy(() => import('../service24/ShowService24'))
const ShowReclamation = lazy(() => import('../reclamation/ShowReclamation'))
const ShowEmploye = lazy(() => import('../employe/ShowEmploye'))
const ShowPartVariable = lazy(() => import('../partvariable/ShowPartVariable'))
const ShowSite = lazy(() => import('../site/ShowSite'))
const ShowJuridique = lazy(() => import('../juridique/ShowJuridique'))
const ShowPlainte = lazy(() => import('../plainte/ShowPlainte'))
const ShowSatisfaction = lazy(() => import('../satisfaction/ShowSatisfaction'))

import LoadingPage from '../loading/LoadingPage'
import LoadingScreen from '../loading/LoadingScreen'
import useToken from '../util/useToken'
import { FiMoreVertical } from 'react-icons/fi'
import { FaCaretDown, FaCaretUp } from "react-icons/fa";
import moment from 'moment'
import NoteModal from '../input/NoteModal'
import '../layout/main/view.css'
import SearchBar from '../input/SearchBar'
import axios from 'axios'
moment.locale('fr')

export default function NotificationView({auth}) {
    const locationSearch = useLocation().search;
    const params = new URLSearchParams(locationSearch)
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [currentItem, setCurrentItem] = useState(null)
    const [notifications, setNotifications] = useState([])
    const [request, setRequest] = useState(null)
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [showCc, toggleCc] = useState(0)
    const detailRef = useRef(null)
    
    const isLocalhost = window.location.href.includes('localhost');
    if (isLocalhost) {
        document.getElementById('pageTitle').textContent = "Localhost - ADMIN"
    }
    else {
        document.getElementById('pageTitle').textContent = (auth.note_message > 0 ? "(" + auth.note_message + ")" : "") + "Notification - ADMIN";
    }
    
    let searchItems = [
        {label: 'Envoyé', name: 'consigne', type:'string'},
        {label: 'Non lu', name: 'unread', type:'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Objet', name: 'type_note', type:'number'},
    ]

    const handleRemoveConsigne = (note) => {
        axios.post("/api/notification/remove_seen/" + note.id, {}, useToken())
        .then((res) => {
            if(res.error)
                console.error(res.error)
            else
                updateData(true)
        })
    }

    const handleSeen = (id) => {
        toggleLoading(true)
        setCurrentItem(null)
        const ids = []
        if(id)
            ids.push(id)
        else if(notifications.length > 0)
            notifications.forEach(n => {
                ids.push(n.id)
            })

        axios.post("/api/notification/seen", {ids : JSON.stringify(ids)}, useToken())
        .then((res) => {
            if(res.error)
                console.error(res.error)
            else
                updateData(true)
        })

    }
    
    const handleReplyAll = (note) => {
        let message = (
            note.sanction_id ? {name : "sanction_id", value: note.sanction_id}
            : note.equipement_id ? {name : "equipement_id", value: note.equipement_id}
            : note.prime_id ? {name : "prime_id", value: note.prime_id}
            : note.absence_id ? {name : "absence_id", value: note.absence_id}
            : note.sav_id ? {name : "sav_id", value: note.sav_id}
            : note.flotte_id ? {name : "flotte_id", value: note.flotte_id}
            : note.approvisionnement_id ? {name : "approvisionnement_id", value: note.approvisionnement_id}
            : note.visite_poste_id ? {name : "visite_poste_id", value: note.visite_poste_id}
            : note.fait_marquant_id ? {name : "fait_marquant_id", value: note.fait_marquant_id}
            : note.deduction_id ? {name : "deduction_id", value: note.deduction_id}
            : note.paie_id ? {name : "paie_id", value: note.paie_id}
            : note.avance_id ? { name: "avance_id", value: note.avance_id }
            : note.service24_id ? { name: "service24_id", value: note.service24_id}
            : note.reclamation_id ? { name: "reclamation_id", value: note.reclamation_id}
            : note.employe_id ? { name: "employe_id", value: note.employe_id}
            : note.site_id ? { name: "site_id", value: note.site_id}
            : note.part_variable_id ? { name: "part_variable_id", value: note.part_variable_id }
            : note.juridique_id ? { name: "juridique_id", value: note.juridique_id }
            : note.satisfaction_id ? { name: "satisfaction_id", value: note.satisfaction_id }
            : null
        )
        message.note_id = note.id
        message.users = [{
            id: note.user_id,
            name: note.expediteur_nom,
            address: note.expediteur_email
        }].concat(note.users)
        setRequest(message)
        toggleNoteModal(true)

    }

    const handleReply = (note) => {
        let message = (
            note.sanction_id ? {name : "sanction_id", value: note.sanction_id}
            : note.equipement_id ? {name : "equipement_id", value: note.equipement_id}
            : note.prime_id ? {name : "prime_id", value: note.prime_id}
            : note.absence_id ? {name : "absence_id", value: note.absence_id}
            : note.sav_id ? {name : "sav_id", value: note.sav_id}
            : note.flotte_id ? {name : "flotte_id", value: note.flotte_id}
            : note.approvisionnement_id ? {name : "approvisionnement_id", value: note.approvisionnement_id}
            : note.visite_poste_id ? {name : "visite_poste_id", value: note.visite_poste_id}
            : note.fait_marquant_id ? {name : "fait_marquant_id", value: note.fait_marquant_id}
            : note.deduction_id ? {name : "deduction_id", value: note.deduction_id}
            : note.paie_id? {name : "paie_id", value: note.paie_id}
            : note.avance_id ? { name: "avance_id", value: note.avance_id }
            : note.service24_id ? { name: "service24_id", value: note.service24_id}
            : note.reclamation_id ? { name: "reclamation_id", value: note.reclamation_id}
            : note.site_id ? { name: "site_id", value: note.site_id}
            : note.part_variable_id ? { name: "part_variable_id", value: note.part_variable_id }
            : note.juridique_id ? { name: "juridique_id", value: note.juridique_id }
            : note.satisfaction_id ? { name: "satisfaction_id", value: note.satisfaction_id }
            : null
        )
        message.note_id = note.id
        if(params.get("consigne")){
            message.users = [{
                id: note.user_id,
                name: note.destinataire_nom,
                address: note.destinataire_email
            }]
        }
        else {
            message.users = [{
                id: note.receiver_id,
                name: note.expediteur_nom,
                address: note.expediteur_email
            }]
        }
        setRequest(message)
        toggleNoteModal(true)

    }

    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentItem(null)
            params.set("offset", 0)
        }
        else
            params.set("offset", notifications.length)

        axios.get("/api/notification?" + params ,  useToken())
            .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setNotifications(res.data)
                    else {
                        const list = notifications.slice().concat(res.data)
                        setNotifications(list)
                    }
                    setDataLoaded(res.data.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }
    
    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    const getWindowSize = () => {
        const {innerWidth} = window;
        return (innerWidth > 1500 ? "xg" : innerWidth > 1200 ? "lg" : innerWidth > 900 ? "md" : "sm");
    }   
    const [size, setSize] = useState(getWindowSize())
 
    useEffect(() => {
        function handleWindowResize() {
          setSize(getWindowSize());
        }
    
        window.addEventListener('resize', handleWindowResize);
    
        return () => {
          window.removeEventListener('resize', handleWindowResize);
        }

    }, [size])

    useEffect(() => {
        if(detailRef && detailRef.current) detailRef.current.scrollTo(0, 0)
    }, [detailRef]);

    return <div>
        {
            (showNoteModal && request) && 
            <NoteModal 
                name={request.name} 
                value={request.value} 
                id={request.note_id}
                updateData={() => updateData(true)} 
                closeModal={() => toggleNoteModal(false)} 
                defautUsers={request.users}/>
        }
        {
            (size != "sm" || (size == "sm" && !currentItem)) &&
            <div className='view-container' 
                style={(size == "xg") ? 
                        { paddingRight: "800px" } 
                    : (size != "sm") ? 
                        { paddingRight: "600px" } 
                    : 
                        { paddingRight: "0px" }
                }   
            >
                <div className='view-list' style={{paddingLeft: '10px', paddingRight: '10px'}}>
                    {
                        isLoading ?
                            <LoadingScreen/>
                        :
                        <div>
                            <div className="padding-container space-between">
                                <h2>
                                    Notification
                                </h2>
                                <div>
                                    {
                                        (params.get("unread") && !params.get("consigne") && notifications.length > 0) && 
                                        <span className='btn btn-outline-secondary pointer' onClick={() => handleSeen()}>Marquer tout comme lu</span>
                                    }
                                </div>
                            </div>
                            <SearchBar listItems={searchItems}/>
                            {
                                notifications.length == 0 ? 
                                    <h3 className='center secondary'>Aucun données trouvé</h3>
                                :
                                    <InfiniteScroll
                                        dataLength={notifications.length}
                                        next={fetchMoreData}
                                        hasMore={!allDataLoaded}
                                        loader={<LoadingPage/>}
                                    >
                                        {
                                            notifications.map((n) => (
                                                <div className={`card-container ${currentItem && currentItem.id == n.id ? 'selected' : ''}`} key={n.id} >
                                                    <div className='badge-container'>
                                                        <div className='action-container'>
                                                            {
                                                                false &&
                                                                <span onClick={() => handleSeen(n.id)}>Bien reçu</span>
                                                            }
                                                            <span onClick={() => handleReply(n)}>
                                                                {!params.get("consigne") ? "Répondre" : "Relancer"}
                                                            </span>
                                                            {
                                                                (!params.get("consigne") && n.users.length > 0) &&
                                                                <span onClick={() => handleReplyAll(n)}>
                                                                    Répondre à tous
                                                                </span>
                                                            }
                                                            {
                                                                (params.get("consigne") == 1) &&
                                                                <span onClick={() => handleRemoveConsigne(n)}>
                                                                    Ne plus suivre
                                                                </span>
                                                            }
                                                        </div>
                                                        <span className='pointer' onClick={() => setCurrentItem(n)}>
                                                            <FiMoreVertical size={20} color="#888"/>
                                                        </span>
                                                    </div>
                                                    <h3>
                                                        {
                                                            n.sanction_id ?
                                                                <>Sanction <span className='secondary'> / Ref : {("00000" +  n.sanction_id).slice(-6)}</span></>
                                                            : n.equipement_id ?
                                                                <>Equipement <span className='secondary'> / Ref : {("00000" +  n.equipement_id).slice(-6)}</span></>
                                                            : n.prime_id ?
                                                                <> Prime <span className='secondary'> / Ref : {("00000" +  n.prime_id).slice(-6)}</span></>
                                                            : n.absence_id ?
                                                                <>Absence <span className='secondary'> / Ref : {("00000" +  n.absence_id).slice(-6)}</span></>
                                                            : n.sav_id ?
                                                                <>Sav <span className='secondary'> / Ref : {("00000" +  n.sav_id).slice(-6)}</span></>
                                                            : n.flotte_id ?
                                                                <>Flotte <span className='secondary'> / Ref : {("00000" +  n.flotte_id).slice(-6)}</span></>
                                                            : n.approvisionnement_id ?
                                                                <>DA <span className='secondary'> / Ref : {moment(n.da_created_at).format("YYYY")}/{("00000" + n.da_reference).slice(-6)}</span></>
                                                            : n.visite_poste_id ?
                                                                <>Visite de poste <span className='secondary'> / Ref : {("00000" +  n.visite_poste_id).slice(-6)}</span></>
                                                            : n.fait_marquant_id ?
                                                                <>Fait marquant <span className='secondary'> / Ref : {("00000" +  n.fait_marquant_id).slice(-6)}</span></>
                                                            : n.deduction_id ?
                                                                <>Deduction <span className='secondary'> / Ref : {("00000" +  n.deduction_id).slice(-6)}</span></>
                                                            : n.paie_id ?
                                                                <>Paie <span className='secondary'> / Ref : {("00000" +  n.paie_id).slice(-6)}</span></>
                                                            : n.avance_id ? 
                                                                <>Avance <span className='secondary'> / Ref : {("00000" + n.avance_id).slice(-6)}</span></>
                                                            : n.service24_id ? 
                                                                <>Service24 <span className='secondary'> /Ref : {("0000" + n.service24_id).slice(-6)}</span></>
                                                            : n.reclamation_id ? 
                                                                <>Réclamation <span className='secondary'> /Ref : {("0000" + n.reclamation_id).slice(-6)}</span></>
                                                            : n.employe_id ? 
                                                                <>Employé <span className='secondary'> /Ref : {("0000" + n.employe_id).slice(-6)}</span></>
                                                            : n.site_id ? 
                                                                <>Site <span className='secondary'> /Ref : {("0000" + n.site_id).slice(-6)}</span></>
                                                            : n.part_variable_id ? 
                                                                <>Part variable <span className='secondary'> /Ref : {("0000" + n.part_variable_id).slice(-6)}</span></>
                                                            : n.juridique_id ? 
                                                                <>Juridique <span className='secondary'> /Ref : {("0000" + n.juridique_id).slice(-6)}</span></>
                                                            : n.satisfaction_id ? 
                                                                <>Satisfaction <span className='secondary'> /Ref : {("0000" + n.satisfaction_id).slice(-6)}</span></>
                                                            :
                                                                <span>NDf</span>
                                                        }
                                                    </h3>
                                                    {(n.objet && n.objet != "Note") ? n.objet + " : " : ''} 
                                                    <span className='secondary'>{n.note ? '" ' + n.note + ' "' : "" }</span><br/><br/>
                                                    {
                                                        (!params.get("consigne") && showCc == n.id && n.users.length > 0) &&
                                                        <>
                                                            Cc: <span className='secondary'>{n.users.map(u => u.name).join(', ')}</span><br/>
                                                        </>
                                                    }
                                                    <div className='space-between'>
                                                        {
                                                            params.get("consigne") ?
                                                                <span>
                                                                    Pour: <span className='secondary'>{n.destinataire_nom + " <" + n.destinataire_email + "> "}</span>
                                                                </span>
                                                            :
                                                                <span>
                                                                    De: <span className='secondary'>{n.expediteur_nom + " <" + n.expediteur_email + "> "}</span>
                                                                    {
                                                                        n.users.length > 0 &&
                                                                        <span className='pointer' onClick={() => toggleCc(n.id == showCc ? 0 : n.id)}>
                                                                            {
                                                                                showCc == n.id ?
                                                                                    <FaCaretDown size={15} color="#888"/>
                                                                                :
                                                                                    <FaCaretUp size={15} color="#888"/>
                                                                            }
                                                                        </span>
                                                                    }
                                                                </span>

                                                        }
                                                        <span className='secondary'>
                                                            {moment(n.created_at).from(auth.datetime)}
                                                        </span>
                                                    </div>
                                                    </div>
                                            ))
                                        }
                                    </InfiniteScroll>
                            }
                        </div>
                    }
                </div>
            </div>
        }
        {
            (size != "sm" || (size == "sm" && currentItem)) &&
            <div className='view-detail custom-scroll' ref={detailRef} 
                style={
                    (size == "xg") ? 
                        { width: "800px" }
                    : (size != "sm") ? 
                        { width: "600px" }
                    : { width: "100%" }
                }
            >
                {
                    currentItem ? <>
                        {
                            currentItem.sanction_id ?
                                <ShowSanction auth={auth} currentId={currentItem.sanction_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.equipement_id ?
                                <ShowEquipement auth={auth} currentId={currentItem.equipement_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.prime_id ?
                                <ShowPrime auth={auth} currentId={currentItem.prime_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.absence_id ?
                                <ShowAbsence auth={auth} currentId={currentItem.absence_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.sav_id ?
                                <ShowSav auth={auth} currentId={currentItem.sav_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.flotte_id ?
                                <ShowFlotte auth={auth} currentId={currentItem.flotte_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.approvisionnement_id ?
                                <ShowApprovisionnement auth={auth} currentId={currentItem.approvisionnement_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.visite_poste_id ?
                                <ShowVisitePoste auth={auth} currentId={currentItem.visite_poste_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.fait_marquant_id ?
                                <ShowFaitMarquant auth={auth} currentId={currentItem.fait_marquant_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.deduction_id ?
                                <ShowDeduction auth={auth} currentId={currentItem.deduction_id} setCurrentId={setCurrentItem} size={size} />                                      
                            : currentItem.paie_id?
                                <ShowPaie auth={auth} currentId={currentItem.paie_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.avance_id ?
                                <ShowAvance auth={auth} currentId={currentItem.avance_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.service24_id ?
                                <ShowService24 auth={auth} currentId={currentItem.service24_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.reclamation_id ?
                                <ShowReclamation auth={auth} currentId={currentItem.reclamation_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.employe_id ?
                                <ShowEmploye auth={auth} currentId={currentItem.employe_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.site_id ?
                                <ShowSite auth={auth} currentId={currentItem.site_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.part_variable_id ?
                                <ShowPartVariable auth={auth} currentId={currentItem.part_variable_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.juridique_id ?
                                <ShowJuridique auth={auth} currentId={currentItem.juridique_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.plainte_id ?
                                <ShowPlainte auth={auth} currentId={currentItem.juridique_id} setCurrentId={setCurrentItem} size={size}/>
                            : currentItem.satisfaction_id ?
                                <ShowSatisfaction auth={auth} currentId={currentItem.satisfaction_id} setCurrentId={setCurrentItem} size={size}/>
                            :    
                                <span>Detail view not found</span>
                        }
                    </>
                    :
                        <img src='/img/tls_background.svg' width="100%"/>
                }
            </div>
        }
    </div>
}