import axios from 'axios';
import React, { useEffect, useState } from 'react'
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';
import Tab from '../layout/Tab';
import ActionPaie from './ActionPaie';

export default function ShowPaie({ auth, setCurrentItem, currentId, setCurrentId, setItems, items, size }) {
    const [isLoading, setIsLoading] = useState(true);
    const [paie, setPaie] = useState();
    const [defautUsers, setDefautUsers] = useState()

    useEffect(() => {
        if (setCurrentItem)
            setCurrentItem(paie)
    }, [paie]);

    useEffect(() => getPaie(), [currentId]);

    const getPaie = () => {
        let isMounted = true;
        setIsLoading(true)
        axios.get('/api/paie/show/' + (currentId), useToken())
        .then((response) => {
            response.data && setIsLoading(false)
            if (isMounted) {
                const newUser = []
                if (auth.id!=response.data.user_id)
                    newUser.unshift({ id: response.data.user_id, address: response.data.user_email, name: response.data.user_nom })
                setDefautUsers(newUser)
                setPaie(response.data)
            }
        })
        .catch((error) => {
            console.error(error)
        })
        return () => { isMounted = false }
    }
    
    return (
        <>
            {isLoading ? <LoadingPage /> : paie && <div>
                <ShowHeader label="Paie" id={paie.id} closeDetail={() => setCurrentId()} size={size} /> 
                <div className="card-container">
                    <div className='badge-container'>
                        <span>
                            <span className={'badge-outline badge-outline-' + (paie.color)}>
                                {paie.description}
                            </span> {
                                paie.nb_pj > 0 &&
                                <span className='badge-outline'>
                                    Pièce jointe : {paie.nb_pj}
                                </span>
                            }
                        </span>
                    </div>
                    <h3>
                        <div>
                            {matricule(paie)} {paie.employe}
                        </div>
                    </h3>
                    {
                        paie.site &&
                            <p style={{ whiteSpace: "pre-line" }}>
                                Site: <span className='text'>{paie.site}</span>
                            </p>                            
                    }
                    <p style={{ whitespace: 'pre-line' }}>
                        Heures travaillées: <span className='text'>
                            {paie.nb_heure_travaille} heures
                        </span>
                    </p>
                    <div className="card-action">
                        <ActionPaie paie={paie} updateData={getPaie} setItems={setItems} items={items} auth={auth} />
                    </div>
                </div>
                <Tab auth={auth} name={"paie_id"} value={paie.id} updateData={getPaie} defautUsers={defautUsers} />
            </div>
            }
        </>
    )
}
