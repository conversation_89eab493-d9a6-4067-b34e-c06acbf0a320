import { useState, useRef } from 'react'

import useClickOutside from '../util/useClickOutside'

export default function InputSuggestion({ label, suggestions, required, value, setValue, type, name }) {
    const [showSelect, toggleSelect] = useState(false)
    const currentType = type ? type : 'text';
    const selectRef = useRef(null)

    useClickOutside(selectRef, () => {
        toggleSelect(false)
    })

    return (
        <div ref={selectRef} className='input-container'>
            <label>{label} {required && <span className='danger'>*</span>}</label>
            <input className='select-search'
                onClick={() => toggleSelect(!showSelect)}
                value={value}
                onChange={(e) => setValue(e.target.value)}
                type={currentType}
                name={name ? name : ''}
            />
            <div className='input-select-relative'>
                {
                    showSelect &&
                    <div className='select-list'>
                        {
                            suggestions.filter(suggestion => (new RegExp((currentType =='number' ? value : value.toLowerCase()), 'g'))
                                .test(currentType == 'number' ? 
                                        (typeof suggestion == "string" ? suggestion : suggestion.value) 
                                    : 
                                        (typeof suggestion == "string" ? suggestion : suggestion.value.toLowerCase())
                                    )
                                )
                                .map((suggestion, index) => (
                                    <div key={index}
                                        className="select-item"
                                        onClick={() => {
                                            (typeof suggestion == "string" ? suggestion : setValue(suggestion.value))
                                            toggleSelect(false)
                                        }}
                                    >
                                        {typeof suggestion == "string" ? suggestion : suggestion.label}
                                    </div>
                                )
                            )
                        }
                    </div>
                }
            </div>
        </div>
    )
}
