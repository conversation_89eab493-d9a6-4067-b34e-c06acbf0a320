const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const matricule = require("../resources/js/components/util/matricule")

moment.locale('fr')

const {db_config_admin, sendMail} = require("../auth")
const pool = mysql.createPool(db_config_admin)

const isTask = (process.argv[2] == "task")
const destination_rh = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
	"<EMAIL>", "ogros<PERSON><EMAIL>", "<EMAIL>", "<EMAIL>"]

const destination_test = ["<EMAIL>", "<EMAIL>"]


let done = {
	archive: false,
	rapport: false,
}

function checkDone(response){
	if(!done.archive)
		return false
	else if(!done.rapport)
		return false
	else {
		if(response && process.argv[2] == 'task')
			pool.query(sqlUpdateLastExport, [], (e, r) =>{
				console.error(e)
				console.log("update last rh export: " + r)
				process.exit(1)
			})
		else
			process.exit(1)
	}
	return true
}

const sqlSelectDateExport = "SELECT value FROM params p WHERE p.key = 'last_export_rh'"
const sqlUpdateLastExport = "UPDATE params p SET p.value = DATE_FORMAT(NOW(), '%Y-%m-%d') WHERE p.key = 'last_export_rh'"

const sqlSelectAgentToArchive = (ids) => "SELECT e.societe_id, e.numero_employe, e.numero_stagiaire, e.num_emp_soit, e.num_emp_saoi, e.nom, e.last_date_pointage, s.nom as 'site' " +
	"FROM employes e " +
	"LEFT JOIN sites s ON s.idsite = e.real_site_id " +
	"WHERE " +
	"    (e.sal_forfait is null or e.sal_forfait = 0) and " +
	"    (e.soft_delete is null or e.soft_delete = 0) and " +
	"    (e.created_at is null or TIMESTAMPDIFF(DAY, e.created_at, now()) > 30) and " +
	"    (e.last_date_pointage is null or TIMESTAMPDIFF(DAY, e.last_date_pointage, now()) > 45) " +
	"	 and e.id not in (" + ids.join(",") + ") " +
	"ORDER BY e.last_date_pointage "

const sqlSelectAgentMustArchive = (ids) => "SELECT e.societe_id, e.numero_employe, e.numero_stagiaire, e.num_emp_soit, e.num_emp_saoi, e.nom, e.last_date_pointage, s.nom as 'site' " +
	"FROM employes e " +
	"LEFT JOIN sites s ON s.idsite = e.real_site_id " +
	"WHERE " +
	"   (e.sal_forfait is null or e.sal_forfait = 0) and " +
	"   (e.soft_delete is null or e.soft_delete = 0) and " +
	"   (e.last_date_pointage is not null and TIMESTAMPDIFF(DAY, e.last_date_pointage, now()) > 60) " +
	"	and e.id not in (" + ids.join(",") + ") " +
	"ORDER BY e.last_date_pointage "

const sqlSelectAgentToConfirm = (ids) => "SELECT societe_id, numero_employe, numero_stagiaire, num_emp_soit, e.num_emp_saoi, e.nom, date_embauche, s.nom as 'site' " +
	"FROM employes e " +
	"LEFT JOIN sites s ON s.idsite = e.real_site_id " +
	"WHERE  " +
	"	e.societe_id = 3 and " +
	"   (e.soft_delete is null or e.soft_delete = 0) and " +
	"	societe_id is not null and " +
	"	date_embauche is not null and " +
	"	numero_employe is null and  " +
	"	num_emp_soit is null and  " +
	"	TIMESTAMPDIFF(MONTH, date_embauche, now()) >= 24 " +
	"	and e.id not in (" + ids.join(",") + ") " +
	"ORDER BY date_embauche "

const sqlSelectAgentNotCompleteDoc = (ids) => "SELECT e.societe_id, e.numero_employe, e.numero_stagiaire, e.num_emp_soit, e.num_emp_saoi, e.nom, e.date_embauche, e.date_confirmation, e.date_conf_soit, " +
	"e.cin, e.cv, e.photo, e.residence, e.plan_reperage, e.bulletin_n3, e.bonne_conduite, s.nom as 'site' " +
	"FROM employes e " +
	"LEFT JOIN sites s ON s.idsite = e.real_site_id " + 
	"where ( " +
	"	(timestampdiff(DAY, e.created_at, now()) > 30) " +
	"	and " +
	"	( " +
	"		(e.cin is null or e.cin = 0) " +
	"		or (e.cv is null or e.cv = 0) " +
	"		or (e.photo is null or e.photo = 0) " +
	"		or (e.residence is null or e.residence = 0) " +
	"		or (e.plan_reperage is null or e.plan_reperage = 0) " +
	"		or (e.bulletin_n3 is null or e.bulletin_n3 = 0) " +
	"		or (e.bonne_conduite is null or e.bonne_conduite = 0) " +
	"	) " +
	") " +
	"and e.societe_id != 5 " +
	"and (e.soft_delete is null or e.soft_delete = 0)  " +
	"and e.id not in (" + ids.join(",") + ") " +
	"ORDER BY e.id"

const sqlSelectAbsence = "SELECT a.id, a.employe_id FROM absences a WHERE status = 'done' " +
	"and a.depart < now() and a.retour > now()"

function doExportRh(){
	console.log("export rh report...")
	pool.query(sqlSelectAbsence, [], async (err, absences) => {
		if(err)
			console.error(err)
		else if(absences){
			pool.query(sqlSelectAgentMustArchive(absences.map(a => a.employe_id)), [], async (err, result) => {
				if(err)
					console.error(err)
				else if(result.length == 0){
					done.archive = true
					checkDone()
				}
				else if(result){
					console.log("Nb agent MUST archive: " + result.length)
					const workbook = new Excel.Workbook()
					generateAgentExcelFile(workbook, "Liste des agents encore actifs mais qui ne sont pas pointés depuis deux mois", result, "pointage")
					const archiveAgentBuffer = await workbook.xlsx.writeBuffer()
					sendMail(
						pool,
						isTask ? destination_rh : destination_test,
						"Agent inactifs depuis 2 mois - RH", 
						"Veuillez trouver ci-joint la liste des agents encore actifs mais qui ne sont pas pointés depuis deux mois.",
						[
							{
								filename: "Agents inactifs depuis 2 mois " + moment().format("DD-MM-YYYY") + ".xlsx",
								content: archiveAgentBuffer
							}
						],
						(response) => {
							done.archive = true
							checkDone(response)
						}, 
						isTask)
				}
			})
			pool.query(sqlSelectAgentToArchive(absences.map(a => a.employe_id)), [], async (err, to_archive) => {
				if(err)
					console.error(err)
				else if(to_archive){
					pool.query(sqlSelectAgentToConfirm(absences.map(a => a.employe_id)), [], async (err, to_confirm) => {
						if(err)
							console.error(err)
						else if(to_confirm){
							pool.query(sqlSelectAgentNotCompleteDoc(absences.map(a => a.employe_id)), [], async (err, to_complete) => {
								if(err)
									console.error(err)
								else if(to_complete){
									let files = []
									if(to_confirm.length > 0){
										console.log("Nb agent to confirm: " + to_confirm.length)
										const workbookConfirm = new Excel.Workbook()
										generateAgentExcelFile(workbookConfirm, "Liste des agents à confirmer après vérification", to_confirm, "confirm")
										const confirmAgentBuffer = await workbookConfirm.xlsx.writeBuffer()
										files.push(
											{
												filename: "Agents à confirmer " + moment().format("DD-MM-YYYY") + ".xlsx",
												content: confirmAgentBuffer
											}
										)
									}
									if(to_archive.length > 0){
										console.log("Nb agent to archive: " + to_archive.length)
										const archiveWorkbook = new Excel.Workbook()
										generateAgentExcelFile(archiveWorkbook, "Liste des agents à vérifier et à mettre en archive s'ils ne travaillent plus", to_archive, "pointage")
										const archiveAgentBuffer = await archiveWorkbook.xlsx.writeBuffer()
										files.push(
											{
												filename: "Agents à archiver " + moment().format("DD-MM-YYYY") + ".xlsx",
												content: archiveAgentBuffer
											}
										)
									}
									if(to_complete.length > 0){
										console.log("Nb agent not complete doc: " + to_complete.length)
										const complementWorkbook = new Excel.Workbook()
										generateAgentExcelFile(complementWorkbook, "Liste des agents avec les compléments de documents manquants", to_complete, "to_complete")
										const documentAgentBuffer = await complementWorkbook.xlsx.writeBuffer()
										files.push(
											{
												filename: "Agents avec document manquant " + moment().format("DD-MM-YYYY") + ".xlsx",
												content: documentAgentBuffer
											}
										)
									}

									sendMail(
										pool,
										isTask ?  destination_rh : destination_test ,
										"Traitement de données des agents - RH",
										"Veuillez trouver ci-joint le rapport hebdomadaire des traitements de données des agents.", 
										files, 
										(response) => {
											done.rapport = true
											checkDone(response)
										},
										isTask
									)
								}
							})
						}
					})
				}
			})
		}
	})
}

	if(process.argv[2] == 'test'){
		console.log("send test...")
		doExportRh()
	}
	else if(isTask){
		if(moment().day() == 1 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
			pool.query(sqlSelectDateExport, [], (err, result) => {
				if(err)
					console.error(err)
				else if(result && moment().format("YYYY-MM-DD") == result[0].value){
					console.log("export rh already done!")
					process.exit()
				}
				else
					doExportRh()
			})
		}
		else {
			console.log("Not Monday, skip export RH.")
		}
	}
	else
		console.log("please specify command!")


function generateAgentExcelFile(workbook, header, data, date){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = {
		size: 16,
		bold: true
	}
	const fontBold = {
		bold: true
	}
	const worksheet = workbook.addWorksheet("Feuille")
	worksheet.getColumn('A').width = 10
	worksheet.getColumn('B').width = 40
	worksheet.getColumn('C').width = 40
	worksheet.getColumn('D').width = 20
	worksheet.getColumn('E').width = 50
	worksheet.mergeCells('A1:E1')
	worksheet.getCell('A1').value = header + " (" + data.length + ")"
	worksheet.getCell('A1').font = fontHeader
	worksheet.getCell('A2').value = "Matricule"
	worksheet.getCell('A2').border = borderStyle
	worksheet.getCell('A2').font = fontBold
	worksheet.getCell('B2').value = "Nom"
	worksheet.getCell('B2').border = borderStyle
	worksheet.getCell('B2').font = fontBold
	worksheet.getCell('C2').value = "Site"
	worksheet.getCell('C2').border = borderStyle
	worksheet.getCell('C2').font = fontBold
	if(date == "pointage"){
		worksheet.getCell('D2').value = "Dernière pointage"
		worksheet.getCell('D2').border = borderStyle
		worksheet.getCell('D2').font = fontBold
	}
	if(["confirm", "to_complete"].includes(date)){
		worksheet.getCell('D2').value = "Date d'embauche"
		worksheet.getCell('D2').border = borderStyle
		worksheet.getCell('D2').font = fontBold
	}
	if(["not_register"].includes(date)){
		worksheet.getCell('D2').value = "Dernière modification"
		worksheet.getCell('D2').border = borderStyle
		worksheet.getCell('D2').font = fontBold
	}
	if(date == "to_complete"){
		worksheet.getCell('E2').value = "Document manquant"
		worksheet.getCell('E2').border = borderStyle
		worksheet.getCell('E2').font = fontBold
	}
	let line = 3
	data.forEach(res => {
		worksheet.getCell('A' + line).value = (matricule(res))
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.getCell('B' + line).value = res.nom
		worksheet.getCell('B' + line).border = borderStyle
		worksheet.getCell('C' + line).value = res.site
		worksheet.getCell('C' + line).border = borderStyle
		if(date == "pointage"){
			worksheet.getCell('D' + line).value = res.last_date_pointage ? moment(res.last_date_pointage).format("DD MMM YYYY") : ''
			worksheet.getCell('D' + line).border = borderStyle
		}
		if(date == "confirm"){
			worksheet.getCell('D' + line).value = res.date_embauche ? moment(res.date_embauche).format("DD MMM YYYY") : ''
			worksheet.getCell('D' + line).border = borderStyle
		}
		if(["not_register", "to_complete"].includes(date)){
			worksheet.getCell('D' + line).value = res.date_embauche ? moment(res.date_embauche).format("DD MMM YYYY")
			: (
				(
					(res.date_confirmation && !res.date_conf_soit)
					|| (res.date_confirmation && res.date_conf_soit && moment(res.date_confirmation).isBefore(moment(res.date_conf_soit)))
				) ? moment(res.date_confirmation).format("DD MMM YYYY") : (res.date_conf_soit ? moment(res.date_conf_soit).format("DD MMM YYYY") : '')
			)
			worksheet.getCell('D' + line).border = borderStyle
		}
		if(date == "to_complete"){
			let manques = []
			if(!res.cin) manques.push('cin')
			if(!res.cv) manques.push('cv')
			if(!res.photo) manques.push('photo') 
			if(!res.residence) manques.push('residence')
			if(!res.plan_reperage) manques.push('plan_reperage')
			if(!res.bulletin_n3) manques.push('bulletin_n3')
			if(!res.bonne_conduite) manques.push('bonne_conduite')
			worksheet.getCell('E' + line).value = manques.join(', ')			
			worksheet.getCell('E' + line).border = borderStyle
		}
		line++
	})
}