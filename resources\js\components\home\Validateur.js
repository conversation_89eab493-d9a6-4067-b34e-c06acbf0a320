import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Link, Navigate } from 'react-router-dom';
import LoadingPage from '../loading/LoadingPage';
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';

export default function Validateur({auth}) {
    const [isLoading, toggleLoading] = useState(false)
    const [nbData, setData] = useState(null) 

    useEffect(() => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/validation', useToken())
        .then(res => {
            if(isMounted){
                setData(res.data)
                toggleLoading(false)
            }
        })
        .catch(err => {
            console.error(err)
        })
        return () => { isMounted = false }
    }, [])

    return (
        isLoading ?
            <LoadingPage/>
        : auth.role == "validateur" ?
            <MenuView title="">
                {
                    nbData && 
                    (
                        (nbData.nb_sanction != 0 || nbData.nb_prime != 0 || nbData.nb_conge != 0 || nbData.nb_permission != 0 ||
                            nbData.nb_fait != 0 || nbData.nb_da != 0 || nbData.nb_equipement != 0 || nbData.nb_sav != 0 || 
                            nbData.nb_sav_tag != 0 || nbData.nb_flotte != 0 || nbData.nb_prime != 0 || 
                            nbData.nb_plainte != 0 || nbData.nb_recouvrement != 0) ?
                        <div>
                            {
                                (nbData.nb_sanction != 0 || nbData.nb_prime != 0 || nbData.nb_conge != 0 || nbData.nb_permission != 0) &&
                                <>
                                    <h3 className='sub-title-menu'>RH</h3>
                                    <div>
                                        <div className='palette-container'>
                                            {
                                                nbData.nb_sanction != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/sanction?status=validation">Demande de validation sanction</Link>
                                                    <span className='badge-outline'>{nbData.nb_sanction}</span>
                                                </div>
                                            }
                                            {
                                                nbData.nb_prime != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/prime?status=validation">Demande de validation prime</Link>
                                                    <span className='badge-outline'>{nbData.nb_prime}</span>
                                                </div>
                                            }
                                            {
                                                nbData.nb_conge != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/absence/conge?status=validation">Demande de validation congé</Link>
                                                    <span className='badge-outline'>{nbData.nb_conge}</span>
                                                </div>
                                            }
                                            {
                                                nbData.nb_permission != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/absence/permission?status=validation">Demande de validation permission</Link>
                                                    <span className='badge-outline'>{nbData.nb_permission}</span>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </>
                            }
                            {
                                (
                                    nbData.nb_fait != 0 || nbData.nb_da != 0 || nbData.nb_equipement != 0 || nbData.nb_sav != 0 || 
                                    nbData.nb_sav_tag != 0 || nbData.nb_flotte != 0 || nbData.nb_prime != 0
                                ) &&
                                <>
                                    <h3 className='sub-title-menu'>OPERATION</h3>
                                    <div>
                                        <div className='palette-container'>
                                            {
                                                nbData.nb_fait != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/fait-marquant?unread=1">Fait marquant non lu</Link>
                                                    <span className='badge-outline'>{nbData.nb_fait}</span>
                                                </div>
                                            }
                                            {
                                                nbData.nb_da != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/da?status=validation">Demande de validation DA</Link>
                                                    <span className='badge-outline'>{nbData.nb_da}</span>
                                                </div>
                                            }
                                            {
                                                nbData.nb_equipement != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/equipement?status=validation">Demande de validation équipement</Link>
                                                    <span className='badge-outline'>{nbData.nb_equipement}</span>
                                                </div>
                                            }
                                            {
                                                nbData.nb_sav != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/sav/autre?status=validation">Demande de validation SAV</Link>
                                                    <span className='badge-outline'>{nbData.nb_sav}</span>
                                                </div>
                                            }
                                            {
                                                nbData.nb_sav_tag != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/sav/tag?status=validation">Demande de validation Tag et Rondier</Link>
                                                    <span className='badge-outline'>{nbData.nb_sav_tag}</span>
                                                </div>
                                            }
                                            {
                                                nbData.nb_flotte != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/flotte?status=validation">Demande de validation flotte</Link>
                                                    <span className='badge-outline'>{nbData.nb_flotte}</span>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </>
                            }
                            {
                                (nbData.nb_plainte > 0 || nbData.nb_recouvrement > 0) &&
                                <>
                                    <h3 className='sub-title-menu'>JURIDIQUE</h3>
                                    <div>
                                        <div className='palette-container'>                                
                                            {
                                                nbData.nb_plainte != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/plainte/unread">Plainte agent</Link>
                                                    <span className='badge-outline'>{nbData.nb_plainte}</span>
                                                </div>
                                            }                            
                                            {
                                                nbData.nb_recouvrement != 0 &&
                                                <div className='palette-item'>
                                                    <Link className='link-no-style' to="/recouvrement/unread">Recouvrement client</Link>
                                                    <span className='badge-outline'>{nbData.nb_recouvrement}</span>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </>
                            }
                        </div>
                        :
                        <div className='center secondary'>
                            <br/>
                            <br/>
                            <h3>Aucune demande pour l'instant</h3>
                        </div>
                    )
                }
            </MenuView>
        :
            <Navigate to="/"/>
    );
}