import React from 'react';
import moment from 'moment'
import matricule from '../util/matricule';

export default function HeaderAbsence({auth, data}) {
    return (
        <div>
            <h3>
                {
                    data.employe ? ((matricule(data)) +' ' + data.employe) : 
                    <div>
                        {data.user_nom} <span className='secondary'>{' <' + data.user_email + '>'}</span>
                    </div>
                }
            </h3>
            {
                data.employe &&
                <p style={{whiteSpace: "pre-line"}}>
                    {
                        data.site ?
                        <>
                            Site: <span className='text'>
                                {data.site}
                            </span>
                        </>
                        :
                        <>
                            Fonction: <span className='text'>
                                {data.fonction}
                            </span>
                        </>
                    }
                </p>
            }
            <p style={{whiteSpace: "pre-line"}}>
                Du: <span className='text capitalize'>
                    {moment(data.depart).format("DD MMM YYYY") + (moment(data.depart).format("HH:mm:ss") == "06:00:00" ? " Jour" : " Nuit")}
                </span>
                <span className='text'> au </span>
                <span className='text capitalize'>
                    {moment(data.retour).format("DD MMM YYYY") + (moment(data.retour).format("HH:mm:ss") == "06:00:00" ? " Jour" : " Nuit")}
                </span>
                <span className='text'>{" (" + moment(data.retour).diff(moment(data.depart), 'hours')/24 + " j)"}</span>
            </p>
            <div className='card-footer'>
                <span>
                    <span>Demandeur : </span>
                    <span className='text'>{data.user_nom} {" <" + data.user_email + ">"}</span>
                </span>
                <span className='secondary'>{moment(data.created_at).from(auth.datetime)}</span>
            </div>
        </div>
    )
}