import React, { useEffect, useState } from 'react'
import useToken from '../util/useToken';
import { useLocation } from 'react-router-dom';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import matricule from '../util/matricule';

export default function AnomalieService24({auth, anomalies, setAnomalies, currentId, setCurrentId}) {
    const [isLoading, toggleLoading] = useState(false);
    const [allDataLoaded, setDataLoaded] = useState(false);
    const locationSearch = useLocation().search;
    const searchItems = [
        { label: 'Site', name: 'site_id', type:'number'},
        { label: 'Date de planning', name: 'date_planning', type: 'dateMonth' },
        { label: 'Utilisateur', name: 'user_id', type:'number'},
    ]

    const updateData = (initial) => {
        let isMounted = true;
        const urlParams = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            urlParams.set("offset", 0)
        }
        else
            urlParams.set("offset", anomalies.length)
        axios.get("/api/anomalie_service24?" + urlParams ,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial) {
                        setAnomalies(res.data.anomalies)
                    }
                    else {
                        const list = anomalies.slice().concat(res.data.anomalies)
                        setAnomalies(list)
                    }
                    setDataLoaded(res.data.anomalies.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => updateData(), 300);
    };
    
    return (
        <div>
            {
                isLoading ? 
                    <LoadingPage /> 
                :
                    <div>
                        <div className="padding-container space-between">
                            <h2>Anomalie Service 24 dans le planning</h2>
                        </div>
                        <SearchBar listItems={searchItems} />
                        {
                            anomalies.length == 0 ?
                                <h3 className="center secondary">Aucun données trouvé</h3> 
                            :
                                <div>
                                    <InfiniteScroll dataLength={anomalies.length} 
                                        next={fetchMoreData} 
                                        hasMore={!allDataLoaded} 
                                        loader={<LoadingPage />}
                                    >
                                        <div className="line-container">
                                            <div className="row-list">
                                                <b className='line-cell-sm'>#</b>
                                                <b className='line-cell-lg'>Agent</b>
                                                <b className="line-cell-sm" style={{textAlign:"center"}}>Nb services</b>
                                            </div>
                                        </div>
                                        {
                                            anomalies.map((ano, index) => (
                                                <>
                                                    {
                                                        ano.service_consecutives > 0 &&
                                                            <div onClick={() => setCurrentId(ano.id)}
                                                                className={`line-container ${currentId && currentId == ano.id ? "selected" : ""}`} 
                                                                key={index}
                                                            >
                                                                <div className="row-list">
                                                                    <span className='line-cell-sm'>{"[" + matricule(ano)+ "]"}</span>
                                                                    <span className='line-cell-lg'>{ano.employe}</span>
                                                                    <span className='line-cell-sm' style={{textAlign:"center"}}>{ano.service_consecutives}</span>
                                                                </div>
                                                            </div>
                                                    }
                                                    
                                                </>
                                                
                                            ))
                                        }
                                    </InfiniteScroll>
                                </div>
                        }
                    </div>
            }
        </div>
    )
}
