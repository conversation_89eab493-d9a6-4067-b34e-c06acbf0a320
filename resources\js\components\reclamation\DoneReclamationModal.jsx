import React, {useEffect, useState } from 'react'
import Input<PERSON><PERSON><PERSON><PERSON><PERSON> from '../input/InputMonth<PERSON>ear'
import InputCheckBox from '../input/InputCheckBox'
import useToken from '../util/useToken'
import Textarea from '../input/Textarea'

export default function DoneReclamationModal({ id, updateData, closeModal }) {
    const [payeSuite, setPayeSuite] = useState(false);
    const [datePaie, setDatePaie] = useState({ year: "", month: "", });
    const [note, setNote] = useState("");
    const [submitDisabled, disableSubmit] = useState(false);
    const [error, setError] = useState('');
    useEffect(() => {
        if (payeSuite) {
            setDatePaie({ year: "", month: "", })
        }
    }, [payeSuite])
    
    const handleSubmit = () => {
        disableSubmit(true)
        setError("")
        let formData = new FormData()
        if (!payeSuite) {
            formData.append("date_paie", datePaie.year + "-" + datePaie.month + "-20")
        }
        formData.append("paye_suite", payeSuite)
        formData.append("note", note)
        axios.post('/api/reclamation/save_done/' + id, formData, useToken())
            .then((res) => {
                if (res.data.success) {
                    if (updateData) updateData()
                    closeModal()
                }
                else if (res.data.error == "EACCES")
                    setError("Une erreur est survenue.")
                else if (res.data.error)
                    setError(res.data.error)
                disableSubmit(false)
            })
            .catch((err) => {
                console.error(err)
                disableSubmit(false)
                setError("Une erreur est survenue.")
            })
    }

    return (
        <div className='modal'>
            <div>
                <h3>Terminer la réclamation</h3>
                <div className='card-container'>
                    <InputCheckBox label="Payer de suite" value={payeSuite} onChange={setPayeSuite} />
                </div>
                {!payeSuite &&
                    <InputMonthYear label="Fiche de paie" value={datePaie} onChange={setDatePaie} />
                }
                <Textarea label="Commentaire"
                    value={note}
                    onChange={(value) => setNote(value)}
                />
                {
                    error &&
                    <div className='container-error'>
                        {error}
                    </div>
                }
                <div className='form-button-container'>
                    <button disabled={submitDisabled} className='btn-primary' onClick={handleSubmit}>Envoyer</button>
                    <button onClick={() => { closeModal() }}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
