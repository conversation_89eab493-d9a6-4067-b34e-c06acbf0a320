import React, { useState } from "react";
import { Link } from "react-router-dom";
import NoteModal from "../input/NoteModal";
import ModalImprimer from "./Fiche/ModalImprimer";

export default function ActionPaie({
    auth,
    paie,
    updateData,
}) {
    const [currentAction, setAction] = useState(null);
    const [showNoteModal, toggleNoteModal] = useState(false);
    const [fichePaie, toggleFichePaie] = useState(false);
    const handleCancelPaie = (id) => {
        setAction({
            header: "Annuler la paie",
            request: "/api/paie/cancel_paie/" + id,
            required:true
        })
        toggleNoteModal(true)
    }
    const handleDone = (id) => { 
        setAction({
            header: "Terminer la traitement de paie",
            request: "/api/paie/save_done/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    return (
        <>
            {["resp_rh"].includes(auth.role) && (
                <div>
                    {fichePaie &&(
                        <ModalImprimer
                            id={paie.id}
                            paie={paie}
                            show={fichePaie}
                            toggleShow={toggleFichePaie}
                            updateData={paie.status != "done"?updateData:""}
                        />
                    )}
                    {showNoteModal && (
                        <NoteModal
                            action={currentAction}
                            updateData={() => updateData(true)}
                            closeModal={() => toggleNoteModal(false)}
                        />
                    )}

                    <div className="action-container">
                        {["demande"].includes(paie.status) && (
                            <span>
                                <Link to={"/paie/do_traite/" + paie.id}>
                                    Traiter
                                </Link>
                            </span>
                        )}
                        {["traite"].includes(paie.status) && (
                            <span>
                                <Link to={"/paie/edit/" + paie.id}>Editer</Link>
                            </span>
                        )}
                        {["demande", "traite"].includes(paie.status) && (
                            <>
                                <span onClick={() => handleDone(paie.id)}>
                                    Terminer
                                </span>
                                {(auth.id == paie.user_id) &&
                                    <span onClick={() => handleCancelPaie(paie.id)}>
                                        Annuler
                                    </span>
                                }
                            </>
                        )}
                        {["draft"].includes(paie.status) && (
                            <span>
                                <Link to={"/paie/send_back/" + paie.id}>
                                    Refaire
                                </Link>
                            </span>
                        )}
                        {(paie.status && paie.status != "draft" && 
                            <span onClick={() => toggleFichePaie(paie.id)}>
                                Imprimer
                            </span>
                        )}                        
                    </div>
                </div>
            )}
        </>
    );
}
