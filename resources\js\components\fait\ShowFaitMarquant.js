import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import useToken from '../util/useToken';
import ActionFaitMarquant from './ActionFaitMarquant';
import ShowHeader from '../view/ShowHeader';
import Tab from '../layout/Tab';
import LoadingPage from '../loading/LoadingPage';
import moment from 'moment';

export default function ShowFaitMarquant({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [fait, setFait] = useState(null)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/fait_marquant/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                const fait = res.data
                console.log(fait)
                setFait(fait)
                const newUser = []
                if (auth.id != fait.user_id)
                    newUser.unshift({ id: fait.user_id, address: fait.user_email, name: fait.user_nom })
                if(fait.superviseur && !newUser.map(u => u.id).includes(fait.superviseur.id))
                    newUser.push({ id: fait.superviseur.id, address: fait.superviseur.email, name: fait.superviseur.name })
                if(fait.manager && !newUser.map(u => u.id).includes(fait.manager.id))
                    newUser.push({ id: fait.manager.id, address: fait.manager.email, name: fait.manager.name })
                if(fait.resps)
                    fait.resps.forEach(u => {
                        if(!newUser.map(u => u.id).includes(u.id))
                            newUser.push({ id: u.id, address: u.email, name: u.name })
                    });

                setDefautUsers(newUser)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(fait)
    }, [fait])

    useEffect(updateData, [currentId])

    const stayOnSite = (start, end) => {
        if (start == null || end == null)
            return ""
        const diffMinutes = moment(end).diff(moment(start), 'minutes')
        if (diffMinutes < 60) {
            return `${diffMinutes} minutes`
        } else {
            const hours = Math.floor(diffMinutes / 60)
            const minutes = diffMinutes % 60
            if (minutes == 0) {
                return `${hours} heure`
            } else if (minutes == 1) {
                return (`${hours} heure ${minutes} minute`)
            } else {
                return (`${hours} heure ${minutes} minutes`)
            }
        }
    }

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                fait &&
                <>
                    <ShowHeader label="Fait marquant" id={fait.id} closeDetail={() => setCurrentId()} size={size}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                {
                                    auth.role != 'superviseur' &&
                                    <span className={"badge-outline badge-outline-" + (fait.send_email ? "cyan" : fait.seen ? "green": "purple")}>
                                        { fait.send_email ? "Message envoyé" : fait.seen ? "Lu" : "Non lu" }
                                    </span>
                                }
                                {
                                    fait.start != null && fait.end != null &&
                                    <span className='badge-outline badge-outline-green'>
                                        {stayOnSite(fait.start, fait.end)}
                                    </span>
                                }
                                {
                                    fait.user_role != "room" && (
                                        (fait.start == null && fait.end == null) ||
                                        (fait.start == null && fait.end != null) ||
                                        (fait.start != null && fait.end == null)
                                    ) && (
                                        <span className='badge-outline badge-outline-red'>
                                            ndf : {fait.start == null && fait.end == null ? "0/2" : "1/2"}
                                        </span>
                                    )
                                }
                                {
                                    fait.nb_pj > 0 &&
                                    <span className="badge-outline">
                                        Pièce jointe : {fait.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {fait.site}
                        </h3>
                        <div>
                            Objet: <span className='text'>{fait.objet}</span>
                        </div>
                        <p className='text' style={{whiteSpace: "pre-line"}}>
                            {fait.commentaire}
                        </p>
                        <div>
                            Superviseur : <span className='text'>{fait.user_nom} {" <" + fait.user_email + ">"}</span>
                        </div>
                        <span>
                            {fait.date_visite &&
                                <span>Descente du : </span>
                            }
                            <span className='text'>
                                {fait.date_visite && moment(fait.date_visite).format('LL')}
                                {fait.start && fait.end && (
                                    <> de {moment(fait.start).format('HH[h]mm')} à {moment(fait.end).format('HH[h]mm')}</>
                                )}
                                {/* {fait.start && !fait.end && <> de {moment(fait.start).format('HH[h]mm')}</>}
                                {!fait.start && fait.end && <> à {moment(fait.end).format('HH[h]mm')}</>} */}
                            </span>
                            {fait.start != null && fait.end != null &&
                                <span>  ({fait.start != null && fait.end != null && stayOnSite(fait.start, fait.end)})</span>
                            }
                        </span>
                        <div>
                            Le: <span className='text'>{moment(fait.created_at).format("DD MMMM YYYY")}</span>
                        </div>
                        <div className='card-action'>
                            <ActionFaitMarquant showDetail auth={auth} fait={fait} updateData={updateData}/>
                        </div> 
                    </div>
                        <Tab auth={auth} name="fait_marquant_id" value={fait.id} updateData={updateData} defautUsers={defautUsers} />
                </>
            }
        </div>
    } </>
}