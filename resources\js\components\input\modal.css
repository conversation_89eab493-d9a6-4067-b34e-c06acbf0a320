.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999 !important;
    min-height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal>div {
    padding: 20px;
    width: 580px;
    max-height: 80vh;
    overflow-y: auto;
    background-color: white;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.modal>div>form>div.form-body {
    max-height: 70vh;
    overflow-y: auto;
}

.search-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.search-container>input {
    flex-grow: 1;
    padding: 10px;
}

.search-container>button {
    padding: 11px;
    border: 1px solid #073570;
    background-color: #073570;
    color: white;
}

.list-container {
    align-self: stretch;
    overflow: auto;
}

.list-container>ul {
    list-style-type: none;
    padding: 5px 0px;
    margin: 0px 5px;
}

.list-container>ul>li {
    padding: 10px 10px;
    border-bottom: 1px #ccc solid;
}


.list-container>ul>li:hover {
    background-color: #eee;
}

.list-container>ul>li.default {
    background-color: #ccc;
}
#scrollableList{
    max-height: 40vh;
    overflow-y: auto;
}