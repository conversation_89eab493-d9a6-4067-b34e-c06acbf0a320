import React, { useEffect, useState } from 'react';
import {Link, useParams} from 'react-router-dom'
import axios from 'axios';

import useToken from '../util/useToken';
import matricule from '../util/matricule';
import ActionEquipement from './ActionEquipement';
import Tab from '../layout/Tab';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';
import moment from 'moment';

export default function ShowEquipement({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [equipement, setEquipement] = useState([])
    const [isLoading, toggleLoading] = useState(false)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/equipement/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setEquipement(res.data)
                const newUser = []
                if (auth.id != res.data.user_id)
                    newUser.unshift({id:res.data.user_id, address:res.data.user_email, name:res.data.user_name})
                setDefautUsers(newUser)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(equipement)
    }, [equipement]);

    useEffect(() => {
        updateData()
    }, [currentId]);

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                equipement &&
                <>
                    <ShowHeader size={size} closeDetail={() => setCurrentId()} label={equipement.type_demande} id={equipement.id}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + equipement.status_color}>
                                    {equipement.status_description}
                                </span> {
                                    equipement.nb_pj > 0 &&
                                    <span className='badge-outline'>
                                        <Link className='link-no-style' to={"#article"}>
                                            Pièce jointe : {equipement.nb_pj}
                                        </Link> 
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {
                                equipement.site ?
                                    <div>
                                        {equipement.site}
                                    </div>
                                : equipement.employe ?
                                    <div>
                                        {matricule(equipement)} {equipement.employe} 
                                    </div>
                                : equipement.personal ?
                                    <div>
                                        {equipement.user_nom + " <" + equipement.user_email + ">"}
                                    </div>
                                : 
                                    <div className='danger'>
                                        Type de demande non trouvé
                                    </div>
                            }
                        </h3>
                        {
                            (equipement.site && equipement.employe) &&
                            <div>
                                Employe: {matricule(equipement)} {equipement.employe} 
                            </div>
                        }
                        {
                            (equipement.recruiting == 1) &&
                            <div>
                                <span  className='cyan'>
                                    Employe en cours de recrutement
                                </span>
                            </div>
                        }
                        {
                            equipement.demande &&
                            <p>
                                Demande : <span className='text'>{equipement.demande}</span>
                            </p>
                        }
                        {
                            equipement.detail &&
                            <p>
                                Détail : <span className='text'>{equipement.detail}</span>
                            </p>
                        }
                        <p>
                            Motif : <span className='text'>{equipement.motif}</span>
                        </p>
                        {
                            (equipement.employe || equipement.site) &&
                            <div>
                                Demandeur : <span className='text'> 
                                    {equipement.user_nom} {' <' + equipement.user_email + '>'}
                                </span>
                            </div>
                        }
                        <p>
                            Le : <span className='text'>{moment(equipement.created_at).format("DD MMMM YYYY")}</span>
                        </p>
                        <div className='card-action'>
                            <ActionEquipement auth={auth} equipement={equipement} updateData={updateData} toggleLoading={toggleLoading}/>
                        </div>
                    </div>
                    <Tab auth={auth} name="equipement_id" value={equipement.id} updateData={updateData} defautUsers={defautUsers} />
                </>
            }
        </div>
    }</>
}