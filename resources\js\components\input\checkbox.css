.checkbox-container {
  display: block;
  position: relative;
  padding-left: 25px;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: #eee;
}

.checkbox-container:hover input ~ .checkmark {
  background-color: #ccc;
}

.checkbox-container input:checked ~ .checkmark {
  background-color: #073570;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}
  
.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}
  
.checkbox-container .checkmark:after {
  left: 5px;
  top: 4px;
  width: 2px;
  height: 5px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.checkbox-form-container{
    padding: 10px;
    border: solid 1px #ddd;
    display: flex;
    flex-wrap: wrap;
}
.checkbox-card{
    margin: 5px;
    padding: 5px;
    width: calc(50% - 10px);
    min-width: calc(50% - 10px);
}