import moment from 'moment'
import React, { useEffect } from 'react'
import { IoMdClose } from 'react-icons/io'

export default function ModalAgent({ planning, closeModal, handleDeleteEmploye, setShowAgent }) {
    useEffect(() => {
        if (planning?.employes?.length < 1) {
            closeModal()
        }
    }, [planning?.employes])
    
    return (
        <div className='modal'>
            <div>
                <h2> {(moment(planning.service).format('DD MMM ')).toUpperCase()} {moment(planning.service).format('HH:mm:ss') == '06:00:00' ? 'JOUR' : 'NUIT' }</h2>
                <div>
                    {
                        planning.employes?.map((agent) =>
                            <div className='table line-container space-between secondary' key={agent.id}>
                                <span>
                                    [{ agent.matricule }] - {agent.nom}
                                </span>
                                {
                                    handleDeleteEmploye && moment(planning.service).isAfter(moment()) &&
                                        <IoMdClose size={20} onClick={() => handleDeleteEmploye(agent, planning.id, setShowAgent)}/>
                                }
                            </div>
                        )
                    }
                </div>
                <div className='form-button-container'>
                    <button type='button' onClick={() => closeModal()}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
