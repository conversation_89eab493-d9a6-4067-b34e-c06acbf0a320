import axios from 'axios'
import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import moment from 'moment';

export default function InputPointage({employeId, value, onChange, required}) {
    const [modalOpen, toggleModal] = useState(false)
    const [pointages, setPointages] = useState(null)
    
    useEffect(() => {
        let isMounted = true
        axios.get('/api/pointage/employe_input/' + employeId, useToken())
        .then((res) => {
            if(res.data){
                let dataList = []
                res.data.forEach(ptg => {
                    dataList.push({
                        id: ptg.id,
                        date_pointage: ptg.date_pointage,
                        sanction_id: ptg.sanction_id,
                        site: {
                            id: ptg.site_id,
                            nom: ptg.site,
                        }
                    })
                });
                if(isMounted) setPointages(dataList)
            }
        })
        return () => {isMounted = false}
    }, [employeId])
    
    return <div>
        <div className='input-container'>
            <label>Date du service {required && <span className='danger'>*</span>}</label>
            <input
                type="text" 
                value={
                    value ? 
                        (
                            moment(value.date_pointage).format("dddd DD MMM YYYY") + " " + 
                            (moment(value.date_pointage).format("HH:mm:ss") == "07:00:00" ? "JOUR" : "NUIT")
                        )
                    : ""}
                readOnly
                onClick={() => {toggleModal(true)}}
                />
        </div>
        {
            modalOpen &&
            <div className='modal'>
                <div>
                    <h2>Pointage</h2>
                    {
                        pointages &&
                        <div className='list-container'>
                            {
                                <ul>
                                    {
                                        pointages.map(ptg => {
                                            return <li key={ptg.id} className={ptg.sanction_id ? "danger" : ""} onClick={() => {toggleModal(false);onChange(ptg)}}>
                                                {
                                                    moment(ptg.date_pointage).format("dddd DD MMM YYYY") + " " + 
                                                    (moment(ptg.date_pointage).format("HH:mm:ss") == "07:00:00" ? "JOUR" : "NUIT")
                                                }<br/>
                                                <span className='secondary'>{ptg.site.nom}</span>
                                            </li>
                                        })
                                    }
                                </ul>
                            }
                        </div>
                    }
                    <div className='form-button-container'>
                        <button onClick={() => {toggleModal(false)}}>Annuler</button>
                    </div>
                </div>
            </div>
        }
    </div>;
}