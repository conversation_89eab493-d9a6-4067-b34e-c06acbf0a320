import axios from 'axios';
import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import matricule from '../util/matricule';

import "./employe.css"
import moment from 'moment';
moment.locale('fr')

export default function NotificationEmploye({clickItem}) {
    const [notifications, setNotifications] = useState([])

    const getNotification = (isMounted) => {
        axios.get("/api/employe/notification", useToken())
        .then((res) => {
            if(isMounted){
                setNotifications(res.data.employes)
            }
        })
    }

    useEffect(() => {
        let isMounted = true
        const tid = setTimeout(() => getNotification(isMounted), 100);
        return () => {
            isMounted = false
            clearTimeout(tid)
        };
    }, [])

    useEffect(() => {
        let isMounted = true
        const tid = setTimeout(() => getNotification(isMounted), 15000);
        return () => {
            isMounted = false
            clearTimeout(tid)
        };
    }, [notifications])

    return <>
        {
            notifications.length > 0 &&
            <div id="notifEmpContainer">
                <div id="notifEmpHeader">
                    <h4>Notification</h4>
                    <span>{notifications.length}</span>
                </div>
                <div id="notificationEmpList">
                    {
                        notifications.map(agent => {
                            return <div key={agent.id} className="card-container secondary" onClick={() => {clickItem(agent.id)}}>
                                    {
                                        agent.confirmation ?
                                            (matricule(agent)) + ' ' + agent.nom.substr(0, 20) + (agent.nom.length > 20 ? '.' : '') + " doit être confirmé."
                                        :
                                            (matricule(agent)) + ' ' + agent.nom.substr(0, 20) + " est inactif " + (agent.last_date_pointage ? moment(agent.last_date_pointage).from(moment()) : "") + "."
                                    }
                                    
                                </div>
                        })
                    }
                </div>
            </div>
        }
    </>
}