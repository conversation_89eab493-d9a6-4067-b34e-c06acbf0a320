import React, { useEffect, useState } from 'react';
import { FiSearch } from 'react-icons/fi';
import { CgClose } from 'react-icons/cg'

import InputDateModal from './InputDateModal';
import InputSite from './InputSite';
import InputTypeEquipement from './InputTypeEquipement';
import InputArticle from './InputArticle';
import UserModal from '../modal/UserModal';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import { useLocation, useNavigate } from 'react-router-dom';
import moment from 'moment/moment';
import "moment/locale/fr";
moment.locale("fr");
import StatusModal from '../modal/StatusModal';
import TextModal from '../modal/TextModal';
import InputAgent from './InputAgent';
import InputMonthYearModal from './InputMonthYearModal';
import InputAgenceModal from './InputAgenceModal';
import TypeUserModal from '../modal/TypeUserModal';
import TypeObjetModal from '../modal/TypeObjetModal';
import ServiceModal from '../modal/ServiceModal';
import EtapeModal from '../juridique/EtapeModal';
import InputDateService from './InputDateService';
import TypeAnomalieModal from '../modal/TypeAnomalieModal';
import TypeMvtModal from '../modal/TypeMvtModal';
import StatusRecrutementModal from '../modal/StatusRecrutementModal';
import TypeServiceEquipement from '../modal/TypeServiceEquipement';

export default function SearchBar({listItems, hasEmploye}) {
    const location = useLocation()
    const locationPathname = location.pathname
    const locationSearch = location.search
    const navigate = useNavigate()
    const [showOption, toggleOption] = useState(false)
    const [showId, toggleId] = useState(false)
    const [showReference, toggleReference] = useState(false)
    const [showTextModal, toggleTextModal] = useState(false)
    const [showNom, toggleNom] = useState(false)
    const [showMatricule, toggleMatricule] = useState(false)
    const [showStatus, toggleStatus] = useState(false)
    const [showEtape, toggleEtape] = useState(false)
    const [showSuperviseur, toggleSuperviseur] = useState(false)
    const [showUser, toggleUser] = useState(false)
    const [showSite, toggleSite] = useState(false)
    const [showEmploye, toggleEmploye] = useState(false)
    const [showAgence, toggleAgence] = useState(false)
    const [showDate, toggleDate] = useState(false)
    const [showType, toggleType] = useState(false)
    const [showArticle, toggleArticle] = useState(false)
    const [showDaItem, toggleDaItem] = useState(false)
    const [showDaRef, toggleDaRef] = useState(false)
    const [showObjet, toggleObjet] = useState(false)
    const [showAccount, toggleAccount] = useState(false)
    const [currentItem, setCurrentItem] = useState(false)
    const [selectedItems, setSelectedItems] = useState([])
    const [showMonthYear, toggleMonthYear] = useState(false)
    const [showTypeUser, toggleTypeUser] = useState(false)
    const [showService, toggleService] = useState(false)
    const [paramsSuperviseur, setParamsSuperviseur] = useState()
    const [showTypeObjet, toggleTypeObjet] = useState(false)
    const [paramsMonthYear, setParamsMonthYear] = useState('')
    const [showDateService, toggleDateService] = useState(false)
    const [roles, setRoles] = useState([])
    const [showTypeAnomalie, toggleTypeAnomalie] = useState(false)
    const [showTypeMvt, toggleTypeMvt] = useState(false)
    const [showStatusRecrutement, toggleStatusRecrutement] = useState(false)
    const [showTypeServiceEquipement, toggleServiceEquipement] = useState(false)

    useEffect(() => {
        let isMounted = true
        axios.get('/api/search' + locationSearch, useToken())
        .then((res) => {
            const {status, user, superviseur, employe, agence, site, type_equipement, type_service, article, service, etape} = res.data
            if(isMounted){
                const items = []
                if((new URLSearchParams(locationSearch)).get("consigne"))
                    items.push({name: "consigne", description: "Envoyé"})
                if((new URLSearchParams(locationSearch)).get("unread"))
                    items.push({ name: "unread", description: "Non lu" })
                if((new URLSearchParams(locationSearch)).get("not_found_resp"))
                    items.push({ name: "not_found_resp", description: "Site Sans manager" })
                if((new URLSearchParams(locationSearch)).get("not_found_resp_part"))
                    items.push({ name: "not_found_resp_part", description: "Responsable non defini" })
                if((new URLSearchParams(locationSearch)).get("late_date"))
                    items.push({ name: "late_date", description: "En retard" })
                if((new URLSearchParams(locationSearch)).get("unread_sup"))
                    items.push({ name: "unread_sup", description: "Non lu par le superviseur" })
                listItems.forEach(item => {
                    const searchParamValue = new URLSearchParams(locationSearch).get(item.name)
                    if(searchParamValue) {
                        if(item.name == 'actif'){
                            item.description = "Actif"
                            items.push(item)
                        }
                        if(item.name == 'archive'){
                            item.description = "Archive"
                            items.push(item)
                        }
                        if(item.name == 'sal_forfait'){
                            item.description = "sal_forfait"
                            items.push(item)
                        }
                        if(item.name == 'a_recuperer'){
                            item.description = "A récuperer"
                            items.push(item)
                        }
                        if(item.name == 'sans_employe'){
                            item.description = "Sans employé"
                            items.push(item)
                        }
                        if(item.name == 'pour_validation'){
                            item.description = "Pour validation"
                            items.push(item)
                        }
                        if(item.name == 'valider'){
                            item.description = "Valider"
                            items.push(item)
                        }
                        if(item.name == 'sup_ndf'){
                            item.description = "Sup non defini"
                            items.push(item)
                        }
                        if(item.name == 'resp_sup_ndf'){
                            item.description = "Manager non defini"
                            items.push(item)
                        }
                        if(item.name == 'site_principale_archive'){
                            item.description = item.label
                            items.push(item)
                        }
                        if(item.name == 'recouvrement'){
                            item.description = "Recouvrement"
                            items.push(item)
                        }
                        if(item.name == 'plainte'){
                            item.description = "Plainte"
                            items.push(item)
                        }
                        if(item.name == 'status'){
                            item.value = status.name
                            item.description = status.description
                            items.push(item)
                        }
                        if(item.name == 'etape_id'){
                            item.value = etape.id
                            item.description = etape.nom
                            items.push(item)
                        }
                        if(item.name == 'service_id'){
                            item.value = service.id
                            item.description = service.designation
                            items.push(item)
                        }
                        if(item.name == 'user_id' || item.name == 'resp_sup_id'){
                            item.value = user.id
                            item.description = user.name + " <" + user.email + ">"
                            items.push(item)
                        }
                        if(item.name == 'superviseur_id'){
                            item.value = superviseur.id
                            item.description = superviseur.name + " <" + superviseur.email + ">"
                            items.push(item)
                        }
                        if(item.name == 'employe_id'){
                            item.value = employe.id
                            item.description = (matricule(employe)) + " " + employe.nom
                            items.push(item)
                        }
                        if(item.name == 'agence_id'){
                            item.value = agence.id
                            item.description = agence.nom
                            items.push(item)
                        }
                        if(item.name == 'site_id'){
                            item.value = site.idsite
                            item.description = site.nom
                            items.push(item)
                        }
                        if(item.name == 'type_equipement'){
                            item.value = type_equipement.name
                            item.description = type_equipement.designation
                            items.push(item)
                        }
                        if(item.name == 'article'){
                            item.value = article.name
                            item.description = article.designation
                            items.push(item)
                        }
                        if(item.name == 'date_pointage'){
                            item.value = searchParamValue
                            item.description = moment(searchParamValue).format("DD MMM YYYY")
                             + " " + (moment(searchParamValue).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR")
                            items.push(item)
                        }
                        if(item.name == 'created_at'){
                            if(moment(searchParamValue, "MMMM", true).isValid()) {
                                let parsedDate = moment(searchParamValue, "MMMM");
                                item.value = parsedDate.format("MM")
                                item.description = parsedDate.format("MMMM")
                                items.push(item)
                            } else if (moment(searchParamValue, "YYYY-MM-DD", true).isValid()) {
                                let parsedDate = moment(searchParamValue, "YYYY-MM-DD");
                                item.value = parsedDate.format("YYYY-MM-DD")
                                item.description = parsedDate.format("DD MMM YYYY")
                                items.push(item)
                            }
                        }
                        if(item.name == 'da_item'){
                            item.value = (new URLSearchParams(locationSearch)).get("da_item")
                            item.description = (new URLSearchParams(locationSearch)).get("da_item")
                            items.push(item)
                        }
                        if(item.name == 'da_ref'){
                            item.value = (new URLSearchParams(locationSearch)).get("da_ref")
                            item.description = (new URLSearchParams(locationSearch)).get("da_ref")
                            items.push(item)
                        }
                        if(item.name == 'objet'){
                            item.value = (new URLSearchParams(locationSearch)).get("objet")
                            item.description = (new URLSearchParams(locationSearch)).get("objet")
                            items.push(item)
                        }
                        if(item.name == 'type_service'){
                            item.value = (new URLSearchParams(locationSearch)).get("type_service")
                            item.description = (new URLSearchParams(locationSearch)).get("type_service")
                            items.push(item)
                        }
                        if(item.name == 'account'){
                            item.value = (new URLSearchParams(locationSearch)).get("account")
                            item.description = (new URLSearchParams(locationSearch)).get("account")
                            items.push(item)
                        }
                        if(['id', 'nom', 'matricule', 'reference', 'fait', 'debiteur', 'contrat', 'facture', 'agent', 'police']
                            .includes(item.name))
                        {
                            item.value = searchParamValue
                            item.description = searchParamValue
                            items.push(item)
                        }
                        if (item.name == 'date_planning') {
                            item.value = searchParamValue
                            item.description = moment(searchParamValue).format("MMM YYYY")
                            items.push(item)
                        }
                        if (item.name == 'date_paie'){
                            item.value = searchParamValue
                            item.description = moment(searchParamValue).format("MMM YYYY")
                            items.push(item)
                        }
                        if (item.name == 'type_user') {
                            item.value = searchParamValue
                            item.description = searchParamValue
                            items.push(item)
                        }
                        if (item.name == 'conge_fictif') {
                            item.value = searchParamValue
                            item.description = searchParamValue
                            items.push(item)
                        }
                        if (item.name == 'type_note') {
                            item.value = (new URLSearchParams(locationSearch)).get("type_note")
                            item.description = searchParamValue
                            items.push(item)
                        }
                        if (item.name == 'to_do') {
                            item.description = "A faire"
                            items.push(item)
                        }
                        if (item.name == 'message_content') {
                            item.value = (new URLSearchParams(locationSearch)).get("message_content")
                            item.description = searchParamValue
                            items.push(item)
                        }
                        // if(item.name == 'type_mouvement') {
                        //     item.value = (new URLSearchParams(locationSearch)).get("type_mouvement")
                        //     item.description = searchParamValue
                        //     items.push(item)
                        // }
                        if (item.name == "type_mvt") {
                            item.value = (new URLSearchParams(locationSearch)).get("type_mvt")
                            item.description = searchParamValue
                            items.push(item)
                        }
                        if(item.name == 'date_service') {
                            item.value = (new URLSearchParams(locationSearch)).get("date_service")
                            item.description = moment(searchParamValue).format("DD MMM YYYY")
                            + " " + (moment(searchParamValue).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR")
                            items.push(item)
                        }
                        if(item.name == 'type_anomalie') {
                            item.value = (new URLSearchParams(locationSearch)).get("type_anomalie")
                            item.description = searchParamValue
                            items.push(item)
                        }
                        if (item.name == "status_recrutement") {
                            item.value = (new URLSearchParams(locationSearch)).get("status_recrutement")
                            item.description = searchParamValue
                            items.push(item)
                        }
                        if (item.name == "employe") {
                            item.value = (new URLSearchParams(locationSearch)).get("employe")
                            item.description = searchParamValue
                            items.push(item)
                        }
                        if (item.name == "cin_text") {
                            item.value = (new URLSearchParams(locationSearch)).get("cin_text")
                            item.description = searchParamValue
                            items.push(item)
                        }
                    }
                })
                setSelectedItems(items)
            }
        })
        return () => { isMounted = false };
    }, [locationSearch])

    const  handleRemoveSearchItem = (e, name) => {
        e.stopPropagation()
        let params = new URLSearchParams(locationSearch)
        params.delete(name)
        navigate(locationPathname + "?" + params)
    }

    const handleAddSearchItem = () => {
        toggleOption(false)
    }

    const handleClickItem = (item) => {
        setCurrentItem(item)
        if(item.name == 'id') toggleId(true)
        else if(item.name == 'reference') toggleReference(true)
        else if(item.name == 'nom') toggleNom(true)
        else if(item.name == 'matricule') toggleMatricule(true)
        else if(item.name == 'status') toggleStatus(true)
        else if(item.name == 'etape_id') toggleEtape(true)
        else if(item.name == "type_service") toggleServiceEquipement(true)
        else if(item.name == 'consigne') {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("consigne", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == 'unread') {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("unread", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == 'not_found_resp') {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("not_found_resp", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == 'unread_sup') {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("unread_sup", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == 'un_finished') {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("un_finished", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == 'late_date') {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("late_date", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "actif") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("actif", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "archive") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("archive", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "sal_forfait") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("sal_forfait", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "a_recuperer") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("a_recuperer", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "sans_employe") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("sans_employe", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "pour_validation") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("pour_validation", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "valider") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("valider", 1)
            navigate(location.pathname + "?" + params)
        }  
        else if(item.name == "sup_ndf") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("sup_ndf", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "site_principale_archive") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("site_principale_archive", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "resp_sup_ndf") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("resp_sup_ndf", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "recouvrement") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("recouvrement", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == "plainte") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("plainte", 1)
            navigate(location.pathname + "?" + params)
        }
        else if (item.type == 'superviseur_id') {
            setParamsSuperviseur('superviseur_id')
            toggleSuperviseur(true)
        }
        else if (item.name == 'resp_sup_id') {
            toggleUser(true)
            setRoles(['resp_sup', 'resp_op'])

        }
        else if (item.name == 'date_planning') {
            setParamsMonthYear('date_planning')
            toggleMonthYear(true)
        }
        else if (item.name == 'conge_fictif') {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("conge_fictif", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == 'service_id') toggleService(true)
        else if(item.name == 'superviseur_id') toggleSuperviseur(true)
        else if(item.name == 'user_id') toggleUser(true)
        else if(item.name == 'employe_id') toggleEmploye(true)
        else if(item.name == 'agence_id') toggleAgence(true)
        else if(item.name == 'site_id') toggleSite(true)
        else if(item.name == 'type_equipement') toggleType(true)
        else if(item.name == 'article') toggleArticle(true)
        else if(item.name == 'da_item') toggleDaItem(true)
        else if(item.name == 'da_ref') toggleDaRef(true)
        else if(item.name == 'objet') toggleObjet(true)
        else if(item.name == 'account') toggleAccount(true)
        else if(item.name == 'type_user') toggleTypeUser(true)
        else if(item.type == 'string') toggleTextModal(true)
        else if(item.type == 'date') toggleDate(true)
        else if (item.type == 'dateMonth') {
            setParamsMonthYear('date_paie')
            toggleMonthYear(true)
        }
        else if(item.name == 'type_note') toggleTypeObjet(true)
        else if(item.name == "to_do") {
            toggleOption(false)
            let params = new URLSearchParams(location.search)
            params.set("to_do", 1)
            navigate(location.pathname + "?" + params)
        }
        else if(item.name == 'date_service') toggleDateService(true)
        else if(item.name == 'type_anomalie') toggleTypeAnomalie(true)
        else if(item.name == 'type_mvt') toggleTypeMvt(true)
        else if(item.name == 'status_recrutement') toggleStatusRecrutement(true)
    }

    return <div id="searchBarContainer">
        {
            showStatus && <StatusModal hasEmploye={hasEmploye} useLink onChange={handleAddSearchItem} closeModal={() => toggleStatus(false)}/>
        }
        {
            showEtape && <EtapeModal useLink onChange={handleAddSearchItem} closeModal={() => toggleStatus(false)}/>
        }
        {
            showService && <ServiceModal useLink onChange={handleAddSearchItem} closeModal={() => toggleService(false)}/>
        }
        {
            showMonthYear && <InputMonthYearModal useLink onChange={handleAddSearchItem} closeModal={() => toggleMonthYear(false)} paramsMonthYear={paramsMonthYear} />
        }
        {
            showSuperviseur && <UserModal useLink role="superviseur" paramsSuperviseur={paramsSuperviseur} onChange={handleAddSearchItem} closeModal={() => toggleSuperviseur(false)}/>
        }
        {
            showUser && <UserModal useLink roles={roles} onChange={handleAddSearchItem} closeModal={() => toggleUser(false)}/>
        }
        {
            showSite && <InputSite useLink onChange={handleAddSearchItem} closeModal={() => toggleSite(false)} hideInput/>
        }
        {
            showEmploye && <InputAgent useLink onChange={handleAddSearchItem} closeModal={() => toggleEmploye(false)} hideInput/>
        }
        {
            showAgence && <InputAgenceModal useLink onChange={handleAddSearchItem} closeModal={() => toggleAgence(false)} hideInput/>
        }
        {
            showType && <InputTypeEquipement useLink onChange={handleAddSearchItem} closeModal={() => toggleType(false)} hideInput/>
        }
        {
            showArticle && <InputArticle useLink onChange={handleAddSearchItem} closeModal={() => toggleArticle(false)} hideInput/>
        }
        {
            showDate && <InputDateModal useLink onChange={handleAddSearchItem} closeModal={() => toggleDate(false)}/>
        }
        {
            showId && <TextModal param="id" label="ID"  closeModal={() => toggleId(false)}/>
        }
        {
            showReference && <TextModal param="reference" label="Référence" closeModal={() => toggleReference(false)}/>
        }
        {
            showTextModal && <TextModal param={currentItem.name} label={currentItem.label} closeModal={() => toggleTextModal(false)}/>
        }
        {
            showNom && <TextModal param="nom" label="Nom" closeModal={() => toggleNom(false)}/>
        }
        {
            showMatricule && <TextModal param="matricule" label="Matricule" closeModal={() => toggleMatricule(false)}/>
        }
        {
            showDaItem && <TextModal param="da_item" label="Article" closeModal={() => toggleDaItem(false)}/>
        }
        {
            showDaRef && <TextModal param="da_ref" label="Référence paiement" closeModal={() => toggleDaRef(false)}/>
        }
        {
            showObjet && <TextModal param="objet" label="Objet" closeModal={() => toggleObjet(false)}/>
        }
        {
            showAccount && <TextModal param="account" label="Nom ou email" closeModal={() => toggleAccount(false)}/>
        }
        {
            showTypeUser && <TypeUserModal param="type" label="Type" closeModal={() => toggleTypeUser(false)}/>
        }
        {
            showTypeObjet && <TypeObjetModal useLink onChange={handleAddSearchItem} closeModal={() => toggleTypeObjet(false)}/>
        }
        {
            showDateService && <InputDateService useLink onChange={handleAddSearchItem} closeModal={() => toggleDateService(false)}/>
        }
        {
            showTypeAnomalie && <TypeAnomalieModal useLink onChange={handleAddSearchItem} closeModal={() => toggleTypeAnomalie(false)}/>
        }
        {
            showTypeMvt && <TypeMvtModal useLink onChange={handleAddSearchItem} closeModal={() => toggleTypeMvt(false)}/>
        }
        {
            showStatusRecrutement && <StatusRecrutementModal useLink onChange={handleAddSearchItem} closeModal={() => toggleStatusRecrutement(false)}/>
        }
        {
            showTypeServiceEquipement && <TypeServiceEquipement useLink onChange={handleAddSearchItem} closeModal={() => toggleServiceEquipement(false)} />
        }
        {
            !showOption ?
                <div id="searchBar" onClick={() => toggleOption(true)}>
                    <div id="searchValue">
                    {
                        selectedItems.length > 0 ?
                            selectedItems.map(item => <div className='adress-item' key={item.name}>
                                    <div>
                                        {
                                            item.value ?
                                                <>{item.label} <span className='secondary'>{' : ' + item.description}</span></>
                                            :
                                                <>{item.description}</>
                                        }
                                        <CgClose className='pointer' onClick={(e) => handleRemoveSearchItem(e, item.name)}/>
                                    </div>
                                </div>
                            )
                        :
                        <div id="searchValue">
                            {selectedItems.length == 0 && <div className='adress-item'>Rechercher</div>}
                        </div>
                    }
                    </div>
                    <FiSearch size={20}/>
                </div>
            :
                <div id="searchContainer">
                    <div id="titleSeachList">
                        <h3>Filtré par :</h3>
                        <CgClose size={20} onClick={() => toggleOption(false)}/>
                    </div>
                    <ul className='list-detail'>
                        {
                            listItems.map((item) => (
                                <li key={item.name}>
                                    <span onClick={() => handleClickItem(item)}>{item.label}</span>
                                </li>
                            ))
                        }
                    </ul>
                </div>
        }
    </div>
}
