import React, { useState } from 'react';
import TransfertArticleModal from '../../modal/TransfertArticleModal';
import RetournerArticleModal from '../../modal/RetournerArticleModal';

export default function ActionLigneEquipement({article, updateData}) {
    const [showTransferArticleModal, toggleTransfertArticleModal] = useState(false)
    const [showRetournerArticleModal, toggleRetournerArticleModal] = useState(false)
    const [currentAction, setAction] = useState(null)

    const handleTransfererArticle = (id) => {
        setAction({
            request: "/api/mouvement_article/do_transfert/" + id
        })
        toggleTransfertArticleModal(true)
    }

    const handleRetournerArticle = (id) => {
        setAction({
            request: "/api/mouvement_article/do_back/" + id
        })
        toggleRetournerArticleModal(true)
    }

    const closeModal = () => {
        if (showRetournerArticleModal) {
            toggleRetournerArticleModal(false)
        } else if (showTransferArticleModal) {
            toggleTransfertArticleModal(false)
        }
    }

    return <div>
        {
            showTransferArticleModal &&
            <TransfertArticleModal
                action={currentAction}
                updateData={() => updateData()}
                closeModal={closeModal}
            />
        }
        {
            showRetournerArticleModal &&
            <RetournerArticleModal
                action={currentAction}
                updateData={() => updateData()}
                closeModal={closeModal}
            />
        }
        {/* <div className="action-container">
            {article.status === 'retourné' ? (
                <span onClick={() => handleTransfererArticle(article.id)}>Transferer</span>
            ) : (
                <span onClick={() => handleRetournerArticle(article.id)}>Retourner</span>
            )}
        </div> */}
    </div>
}
