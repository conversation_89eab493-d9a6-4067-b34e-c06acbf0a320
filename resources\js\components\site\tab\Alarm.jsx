import React, { useEffect, useState } from 'react';
import InputDate from '../../input/InputDate';
import LoadingPage from '../../loading/LoadingPage';
import axios from 'axios';
import useToken from '../../util/useToken';
import moment from 'moment';
import InputSelect from '../../input/InputSelect';
import InputDateTime from '../../input/InputDateTime';
import matricule from '../../util/matricule';
import { FiSearch } from 'react-icons/fi';
import { useLocation } from 'react-router-dom';

export default function Alarm({ auth, value, updateData, data }) {
    const [isLoading, toggleLoading] = useState(false)
    const locationSearch = new URLSearchParams(useLocation().search);
    const [typeSearch, setTypeSearch] = useState(locationSearch.get('date-alarm') ? { label: 'Date et heure', value: 'datetime' } : { label: 'Date', value: 'date' })
    const [date, setDate] = useState(new Date(locationSearch.get('date-alarm') ?? moment()))
    const [alarms, setAlarms] = useState([])
    const [headerTitle, setHeaderTitle] = useState("")
    const types = [{ label: 'Date', value: 'date' }, { label: 'Date et heure', value: 'datetime' }]
    const [error, setError] = useState('')

    const getData = () => {
        setError('')
        let isMounted = true;
        toggleLoading(true)
        if (typeSearch.value) {
            axios.get('/api/site/alarm/' + value + '?' + (typeSearch.value == 'date' ? 'date=' + moment(date).format('YYYY-MM-DD') : 'date_time=' + moment(date).format('YYYY-MM-DD HH:mm')), useToken())
                .then((res) => {
                    if (isMounted) {
                        if (res.data.error)
                            console.error(res.data.error)
                        else if (res.data.alarms || res.data.test_periodiques) {
                            const tab2Modified = res.data.test_periodiques.map(obj => ({ ...obj, codeTevent: 605 }));
                            const mergedArray = [...res.data.alarms, ...tab2Modified];
                            mergedArray.sort((a, b) => new Date(b.dtarrived) - new Date(a.dtarrived));
                            setAlarms(mergedArray)
                            updateHeaderTitle()
                        }
                        toggleLoading(false)
                    }
                })
                .catch((e) => {
                    console.error(e)
                })
        }
        else {
            toggleLoading(false)
            setError("Veuillez choisir un type de recherche")
        }
        return () => { isMounted = false };
    }

    const updateHeaderTitle = () => {
        if (typeSearch.value == "date")
            setHeaderTitle("Du " + moment(date).format("DD MMM YYYY"))
        else
            setHeaderTitle("Le " + moment(date).format("DD MMM YYYY") + " de " + moment(date).subtract(1, 'hour').format("HH:mm") + " à " + moment(date).add(1, 'hour').format("HH:mm"))
    }

    useEffect(() => {
        getData()
    }, [])

    return (
        <>
            {
                isLoading ?
                    <LoadingPage />
                    :
                    <>
                        <div className='line-container'>
                            <div className='search-pointage'>
                                <div style={{ width: "60%" }} className='space-between'>
                                    <InputSelect options={types} selected={typeSearch} setSelected={setTypeSearch} />
                                    {typeSearch.value == "date" ?
                                        <InputDate value={date} onChange={setDate} noLabel />
                                        :
                                        <InputDateTime value={date} onChange={setDate} />
                                    }
                                    <button onClick={() => getData()} className='btn btn-primary'><FiSearch size={20} /></button>
                                </div>
                            </div>
                            {
                                error &&
                                <div className='container-error'>
                                    {error}
                                </div>
                            }
                        </div>
                        {alarms.length == 0 ?
                            <h4 className='center secondary'>Aucun données trouvé</h4>
                            :
                            <div>
                                <h3>{headerTitle}</h3>
                                <div className="line-container">
                                    <div className="row-employe">
                                        <b className='line-cell-sm'>Heure</b>
                                        <b className='line-cell-md'>Evènement</b>
                                        <b className=''>Agent</b>
                                    </div>
                                </div>
                                {
                                    alarms.map((alarm, index) =>
                                        <div key={index} className='table line-container secondary'>
                                            <div className='row-employe'>
                                                <span className='line-cell-sm'>
                                                    {moment(alarm.dtarrived).format('HH:mm:ss')}
                                                </span>
                                                <span className='line-cell-md'>{"[" + alarm.codeTevent + "] " + (alarm.codeTevent == 605 ? " Test periodique" : alarm.code_description)}</span>
                                                <span>{alarm.employe_id ? (matricule(alarm) + " " + alarm.nom) : ""}</span>
                                            </div>
                                        </div>
                                    )
                                }
                            </div>
                        }
                    </>
            }
        </>
    );
};