import React, { useEffect, useState } from 'react'
import {use<PERSON>ara<PERSON>,Link} from 'react-router-dom'
import axios from 'axios'

import useToken from '../../util/useToken';
import matricule from '../../util/matricule';
import moment from 'moment';
import { FiMoreVertical } from 'react-icons/fi';
import LoadingPage from '../../loading/LoadingPage';

export default function ItemEmploye() {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [employe, setEmploye] = useState(null)
    const [sanctions, setSanctions] = useState(null)
    const [primes, setPrimes] = useState(null)
    const [conges, setConges] = useState(null)
    const [pj, setPj] = useState(null)

    const [activeFile, setActiveFile] = useState(null)
    const [showFile, setShowFile] = useState(false)

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/employe/list_detail/' + params.id, useToken())
        .then((res) => {
            if(isMounted){
                setEmploye(res.data.employe)
                setSanctions(res.data.sanctions)
                setPrimes(res.data.primes)
                setConges(res.data.conges)
                setPj(res.data.piece_jointes)
                toggleLoading(false)
            }
        })
        .catch(e => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(updateData,[params])

    const deletePj = (id) => {
        let isMounted = true
        toggleLoading(true)
        axios.delete('/api/piece_jointe/destroy/employe/' + id + '/' + params.id, useToken())
        .then((res) => {
            if(isMounted){
                updateData()
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    return (
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="block">
                <h2>
                    {
                        params.type == "sa_employe" ? "Sanctions de l'employe: " 
                        : 
                        params.type == "pr_employe" ? "Primes de l'employe: "
                        :
                        params.type == "cg_employe" ? "Congés de l'employe: "
                        :
                        params.type == "pj_employe" ? "Piece jointes de l'employe: "
                        :
                        "Non défini"
                    }
                </h2>
                { matricule(employe)} {employe.nom}
            </div>
            {params.type == "sa_employe" &&
                sanctions.map((sanc) => (
                    <div className="card-container" key={sanc.id}>
                        <div className='badge-container'>
                            <span className={'badge-outline badge-outline-' + sanc.status_color}>
                                {sanc.status_description}
                            </span>
                            <Link className='link-no-style' to={"/sanction/show/" + sanc.id}>
                                <FiMoreVertical size={20} color="#888"/>
                            </Link>
                        </div>
                        <h3>
                            Objet: <span className='text'>{sanc.objet}</span>
                        </h3>               
                        Site: <span className='text'>{sanc.site}</span>             
                        <p style={{whiteSpace: "pre-line"}}>
                            Motif: <span className='text'>{sanc.motif}</span>
                        </p>
                        <p style={{whiteSpace: "pre-line"}}>
                            Demandeur: {sanc.user_nom} {' <' + sanc.user_email + '>'}
                        </p>
                    </div>
                ))
            }
            {params.type == "pr_employe" &&
                primes.map((pr) => (
                    <div className="card-container" key={pr.id}>
                        <div className='badge-container'>
                            <span className={'badge-outline badge-outline-' + pr.status_color}>
                                {pr.status_description}
                            </span>
                            <Link className='link-no-style' to={"/prime/show/" + pr.id}>
                                <FiMoreVertical size={20} color="#888"/>
                            </Link>
                        </div>
                        <h3>
                            Site: <span className='text'>{pr.site}</span>  
                        </h3>               
                                   
                        <p style={{whiteSpace: "pre-line"}}>
                            Motif: <span className='text'>{pr.motif}</span>
                        </p>
                        <p style={{whiteSpace: "pre-line"}}>
                            Demandeur: {pr.user_nom} {' <' + pr.user_email + '>'}
                        </p>
                    </div>
                ))
            }
            {params.type == "cg_employe" &&
                conges.map((cg) => (
                    <div className="card-container" key={cg.id}>
                        <div className='badge-container'>
                            <span className={'badge-outline badge-outline-' + cg.status_color}>
                                {cg.status_description}
                            </span>
                            <Link className='link-no-style' to={"/absence/show/" + cg.id}>
                                <FiMoreVertical size={20} color="#888"/>
                            </Link>
                        </div>
                        <h3>
                            Site: <span className='text'>{cg.site}</span>  
                        </h3>    
                        <p style={{whiteSpace: "pre-line"}}>
                            Du: <span className='text capitalize'>
                                {moment(cg.depart).format("DD MMM YYYY") + (moment(cg.depart).format("HH:mm:ss") == "06:00:00" ? " Jour" : " Nuit")}
                            </span>
                            <span className='text'> au </span>
                            <span className='text capitalize'>
                                {moment(cg.retour).format("DD MMM YYYY") + (moment(cg.retour).format("HH:mm:ss") == "06:00:00" ? " Jour" : " Nuit")}
                            </span>
                        </p>    
                        <p style={{whiteSpace: "pre-line"}}>
                            Durée: <span className='text'>{moment(cg.retour).diff(moment(cg.depart), 'hours')/24 + " jour(s)"}</span>
                        </p>       
                        <p style={{whiteSpace: "pre-line"}}>
                            Motif: <span className='text'>{cg.motif}</span>
                        </p>
                        <p style={{whiteSpace: "pre-line"}}>
                            Demandeur: {cg.user_nom} {' <' + cg.user_email + '>'}
                        </p>
                    </div>
                ))
            }
            {params.type == "pj_employe" &&
                <div className='card-container'>
                    {pj.map((piece,index) => (
                        
                            <div className='pj-container' key={piece.id}>
                                <span className='card-pj-title'>{piece.nature}</span>
                                <div className='card-pj'>
                                    <span title='Cliquer ici pour afficher' className='card-pj-filename' onClick={() => {setActiveFile(piece.id);setShowFile(!showFile)}}>{piece.path}</span>
                                </div>
                                {(piece.id == activeFile && showFile) &&
                                    <iframe
                                        src={'/uploads/' + piece.path}
                                        width="100%"
                                        height="375"
                                        />
                                }
                            </div> 
                        
                    ))}
                </div>
            }
        </div>
    )
}
