import axios from 'axios';
import React, { useEffect, useState } from 'react'
import { useLocation, Link, useNavigate } from 'react-router-dom';
import useToken from '../util/useToken';
import LoadingPage from '../loading/LoadingPage';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import moment from 'moment';
import { upperCase } from 'lodash';

export default function PlanningNonFait({ auth, plannings, setPlannings, currentId ,setCurrentId }) {
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const locationSearch = useLocation().search;
    const hasManagerId = locationSearch.includes('resp_sup_id');
    const hasNotFoundResp = (new URLSearchParams(locationSearch)).get("not_found_resp")
    const navigate = useNavigate();
    const location = useLocation();
    const urlParams = new URLSearchParams(locationSearch);


    const searchItems = [
        { label: 'Site', name: 'site_id', type: 'number' },
        { label: 'Manager', name: 'resp_sup_id', type: 'number' },
        { label: 'Superviseur', name: 'superviseur_id', type: 'superviseur_id' },
        { label: 'Sans Manager', name: 'not_found_resp', type: 'number' },
        { label: 'Date de planning', name: 'date_planning', type: 'dateMonth' },
    ]

    useEffect(() => {
        const datePlanning = urlParams.get('date_planning');

        if (!datePlanning) {
            const defaultDate = moment().format('YYYY-MM');
            urlParams.set('date_planning', defaultDate);

            navigate(`${location.pathname}?${urlParams.toString()}`, { replace: true });
        }
    }, [location, navigate]);

    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true);
            params.set("offset", 0)
        }
        else
            params.set("offset", plannings.length)
        axios.get('/api/site/un_finished?' + params, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.plannings) {
                        if (initial)
                            setPlannings(res.data.plannings)
                        else {
                            const list = plannings.slice().concat(res.data.plannings)
                            setPlannings(list)
                        }
                        setDataLoaded(res.data.plannings.length < 30)
                    }
                    toggleLoading(false);
                }
            })
            .catch((e) => {
                console.error(e)
            })
        return () => { isMounted = false; }
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
        setTimeout(() => updateData(), 300)
    }

    const linkUserWithParam = (id) => {
        const urlParams = new URLSearchParams(locationSearch)
        urlParams.set("resp_sup_id", id)
        return urlParams
    }

    useEffect(() => {
        if(plannings && (!hasNotFoundResp && !hasManagerId)) {
            if(plannings.length == 1){
                let currentPlanning = plannings[0]
                if(currentPlanning.resp_sup_id){
                    navigate("/planning-non-fait?" + linkUserWithParam(currentPlanning.resp_sup_id))
                }
                else {
                    if(locationSearch){
                        navigate("/planning-non-fait" + locationSearch + "&not_found_resp=" + currentPlanning.resp_sup_id)
                    }
                    else
                        navigate("/planning-non-fait?not_found_resp=" + currentPlanning.resp_sup_id)
                }
            }
        }
    }, [plannings])
    return (
        <div>
            {
                isLoading ?
                    <LoadingPage /> 
                :
                    <div>
                        <div className="padding-container space-between">
                            <h2>Planning non fait</h2>
                        </div>
                        <SearchBar listItems={searchItems} />
                        {
                            plannings.length == 0?
                                <h3 className='center secondary'>Aucun données trouvé</h3>
                            :
                                <div>
                                        {
                                            (!(hasManagerId || hasNotFoundResp)) ?
                                                <div>
                                                    <div className="line-container">
                                                        <div className="row-list">
                                                            <b className="line-cell-md">Manager</b>
                                                            <b className="line-cell-sm">Nb</b>
                                                            <b>Email</b>
                                                        </div>
                                                    </div>
                                                    {
                                                        plannings.map((pln, index) => (
                                                            <div className="line-container" key={index}>
                                                                <Link className='link-no-style' to={"/planning-non-fait" + (locationSearch? (locationSearch + '&'):'?') +(pln.resp_sup_id ? ("resp_sup_id=" + pln.resp_sup_id) : "not_found_resp=1") }>
                                                                    <div className={"row-list " + (pln.resp_sup_id ? "" : "danger")}>
                                                                        <span className="line-cell-md"> {pln.resp_sup_id ? pln.resp_sup : "Manager non defini"} </span>
                                                                        <span className="line-cell-sm"> {pln.nb_planning} </span>
                                                                        <span> {pln.resp_sup_email} </span>
                                                                    </div>
                                                                </Link>
                                                            </div>
                                                        ))
                                                    }
                                                </div>
                                            : 
                                                <InfiniteScroll
                                                    dataLength={plannings.length}
                                                    next={fetchMoreData}
                                                    hasMore={!allDataLoaded}
                                                    loader={<LoadingPage />}
                                                >
                                                    <div className="line-container">
                                                        <div className="row-list">
                                                            <b className="line-cell-sm">Date</b>
                                                            <b className="line-cell-sm">H.Facturés</b>
                                                            <b className="line-cell-sm">Horaire</b>
                                                            <b className="line-cell-md">Site</b>
                                                        </div>
                                                    </div>
                                                    {
                                                        plannings.map((pln, index) => 
                                                            <div onClick={() => setCurrentId(pln.idsite)}
                                                                className={`line-container ${currentId && currentId == pln.idsite ? "selected" : ""}`}
                                                                key={index}
                                                            >
                                                                <div className="row-list">
                                                                    <span className="line-cell-sm">{upperCase(moment(pln.date_planning ?? urlParams.get('date_planning')).format("MMM YYYY"))}</span>
                                                                    <span className="line-cell-sm">{pln.total_hour}</span>
                                                                    <span className="line-cell-sm">{pln.horaire}</span>
                                                                    <span className="line-cell-lg">{pln.site}</span>
                                                                </div>
                                                            </div>
                                                        )
                                                    }
                                                </InfiniteScroll>
                                        }
                                </div>
                                
                        }
                    </div>

            }
        </div>
    )
}
