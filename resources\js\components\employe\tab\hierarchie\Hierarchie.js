import React, { useEffect, useState } from 'react'

import useToken from '../../../util/useToken'
import matricule from '../../../util/matricule';
import LoadingPage from '../../../loading/LoadingPage'
import InputEmploye from '../../../input/InputEmploye'
import ConfirmModal from '../../../modal/ConfirmModal'
import { IoMdClose } from 'react-icons/io'

export default function Hierarchie({auth, value}) {
    const [showEmployeModal, toggleEmployeModal] = useState(false);
    const [currentEmploye, setCurrentEmploye] = useState(null);
    const [isLoading, toggleLoading] = useState(true)
    const [employes, setEmployes] = useState([])
    const [error, setError] = useState("")

    const addEmploye = (emp) => {
        axios.post('/api/hierarchie/add/' + value, {"employe_id" : emp.id}, useToken())
        .then((res) => {
            if(res.data.error)
                setError(res.data.error)
            else if(res.data.success){
                updateData(true)
            }
        })
    }

    const removeEmploye = () => {
        axios.post("/api/hierarchie/remove/" + currentEmploye.id, null , useToken())
        .then((res) => {
            if(res.data.error)
                console.error(res.data.error)
            else if(res.data.success){
                setCurrentEmploye(null)
                updateData(true)
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }

    const updateData = (isMounted) => {
        axios.get('/api/hierarchie/employe/' + value, useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else if(res.data.employes){
                    setEmployes(res.data.employes)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }

    useEffect(() => {
        let isMounted = true;
        updateData(isMounted)
        return () => { isMounted = false };
    }, []);

    return <>
        {
            showEmployeModal &&
            <InputEmploye hideInput 
                closeModal={() => toggleEmployeModal(false)} 
                onChange={(emp) => {addEmploye(emp)}}
                urlSearch="/api/employe/hierarchie_search"/>
        }
        {
            currentEmploye && 
            <ConfirmModal msg="Enlever de l'hierarchie?" 
                confirmAction={removeEmploye}
                closeModal={() => setCurrentEmploye(null)}
            />
        }
        {
            isLoading ?
                <LoadingPage/>
            : error ?
                <div className='card-container'>
                    <div className='header-pointage'>
                        <b>
                            {error}
                        </b>
                        <div className='action-container'>
                            <span>
                                <span onClick={() => setError("")}>Ok</span>
                            </span>
                        </div>
                    </div>
                </div>
            :
                <>
                    <div className='line-container'>
                        <div className='header-pointage'>
                            <h3>
                                Employé
                            </h3>
                            {
                                ["rh", "resp_rh", "admin"].includes(auth.role) &&
                                <div className='action-container'>
                                    <span>
                                        <span onClick={() => toggleEmployeModal(true)}>Ajouter</span>
                                    </span>
                                </div>
                            }
                        </div>
                    </div>
                    {
                        employes.map(e => 
                            <div key={e.id} className='line-container'>
                            <div className='pointage-container'>
                                <div>
                                    {(matricule(e)) + " " + e.nom}<br/>
                                    <span className='secondary'>
                                    {e.fonction}
                                    </span>
                                </div>
                                {
                                    ["rh", "resp_rh", "admin"].includes(auth.role) &&
                                    <div style={{width:20, minWidth:20, maxWidth:20}}>
                                        <span>
                                            <IoMdClose onClick={() => setCurrentEmploye(e)} size={20}/>
                                        </span>
                                    </div>
                                }
                            </div>
                            </div>
                        )
                    }
                </>
        }
    </>
}