import React, { useEffect, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import useToken from '../util/useToken'
import LoadingPage from '../loading/LoadingPage'
import ShowHeader from '../view/ShowHeader'
import moment from 'moment'
import parse from 'html-react-parser';
import DOMPurify from 'dompurify';
import { RiArrowDownSLine, RiArrowUpSLine } from "react-icons/ri";
import { CgClose } from 'react-icons/cg'

export default function ShowMessageSent({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const locationSearch = useLocation().search
    const urlParams = new URLSearchParams(locationSearch)
    const [messages, setMessages] = useState([])
    const [isLoading, toggleLoading] = useState(false)
    const [visibleMessageId, setVisibleMessageId] = useState(null)

    const toggleContentVisibility = (id) => {
        if (visibleMessageId === id) {
            setVisibleMessageId(null); 
        } else {
            setVisibleMessageId(id);
        }
    };

    const unFollowAll = (id) => {
        const user_id = messages[0].user_id
        axios.post('/api/message/unfollow_all_by_user', {user_id: user_id}, useToken())
        .then((res) => {
            if(res.data.success)
                setCurrentItem({    
                    nb: 0,
                    max_note_id: '',
                    name: null,
                    email: null,
                    id: currentId,
                    id: currentId,
                    objet: "",
                    ms_id: '',
                    created_at: ""
                })
                setCurrentId()
        })
    }

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        let paramData = new URLSearchParams()
        // if(unread)
        //     paramData.set("unread", 1)
        axios.get('/api/message_sent_unread/show/' + (currentId ? currentId : params.id) + "?" + urlParams, useToken())
        .then((res) => {
            if(isMounted){
                setMessages(res.data.messages)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }
    useEffect(() => {updateData()}, [currentId])

    const downloadAllFiles = (pjs) => {
        pjs.forEach(pj => {
            const link = document.createElement('a');
            link.href = "/uploads/" + pj.path;
            link.setAttribute('download', pj.nature); // This is optional, but it sets the file name
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
    };

    return (
        <>{
            isLoading ?
                <LoadingPage/>
            :
            <div>
                {auth.role == 'admin' ?
                    <ShowHeader size={size} label="Message envoyé" id={currentId} closeDetail={() => setCurrentId()}/>
                    :
                    <div className='space-between' style={{padding: 10}}>
                        <div className='action-container' onClick={() => unFollowAll()}>
                            <span >Ne plus suivre tous</span>
                        </div>
                        <CgClose className='secondary' size={30} onClick={() => setCurrentId()}/>
                </div>
                }
                {
                    messages.map(ms => (
                        <div key={ms.id} className="card-container message-log">
                            {
                                auth.role == 'admin' ?
                                <>
                                    <div className='message-user'>
                                        <span 
                                        // style={{color: (auth.role == 'admin' && ms.sender_id == auth.id) ? '#073570' : ''}}
                                        >
                                            {auth.role == 'admin' ? 
                                                ms.sender + " <" + ms.sender_email + ">"
                                            :
                                                ms.name + " <" + ms.email + ">"
                                            }
                                        </span>
                                        <span>{moment(ms.created_at).format("DD MMM YY à HH:mm")}</span>
                                    </div>
                                    <h3>
                                        <span onClick={() => toggleContentVisibility(ms.id)} className='button-arrow space-between'>
                                            <span>{ms.objet.length > 40 ? ms.objet.slice(0, 37) + '...' : ms.objet}</span>
                                            <span>{visibleMessageId === ms.id ? <RiArrowDownSLine size={25}/> : <RiArrowUpSLine size={25}/>}</span> 
                                        </span>
                                    </h3>

                                </>
                                :
                                <>
                                    <div className='message-user' style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                        <span onClick={() => toggleContentVisibility(ms.id)}>
                                            <h3 style={{ margin: 0, display: 'inline', textAlign: 'bottom' }}>
                                                {ms.objet.length > 40 ? ms.objet.slice(0, 37) + '...' : ms.objet}
                                                <span className='pointer' style={{ verticalAlign: 'middle' }}>
                                                    {visibleMessageId === ms.id ? <RiArrowDownSLine size={20} /> : <RiArrowUpSLine size={20} />}
                                                </span>
                                            </h3>
                                        </span>
                                        <span>{moment(ms.created_at).format("DD MMM YY à HH:mm")}</span>
                                    </div>
                                </>
                            }
                            {visibleMessageId === ms.id && (
                                <>
                                    <div className="message-content">
                                        {parse(DOMPurify.sanitize(ms.content))}
                                    </div>
                                    {
                                        ms.pieces?.length > 0 &&
                                        <div className='message-pj'>
                                            {
                                                ms.pieces.map(pj => (
                                                <span key={pj.id} className='pj-link'>
                                                    <a className='link-no-style' target="_blank" href={"/uploads/" + pj.path} key={pj.id}>
                                                        {pj.nature}
                                                    </a><span> </span>
                                                    </span> 
                                                ))
                                            }
                                        </div>
                                    }
                                    {
                                        ms.pieces?.length > 1 && 
                                        <div className='action-container'>
                                            <span onClick={() => downloadAllFiles(ms.pieces)}>Télécharger tous</span>
                                        </div>
                                    }
                                </>
                            )}
                        </div>
                    ))
                }
            </div>
        }</>
    )
}
