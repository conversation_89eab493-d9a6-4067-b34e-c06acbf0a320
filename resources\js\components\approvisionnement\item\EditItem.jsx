import React, { useEffect, useState } from 'react';
import InputText from '../../input/InputText';
import Textarea from '../../input/Textarea';
import InputCheckBox from '../../input/InputCheckBox';
import useToken from '../../util/useToken';

export default function EditItem({currentItem, currentApproId, items, setItems, closeModal, updateData}) {
    const [designation, setDesignation] = useState("")
    const [unite, setUnite] = useState("")
    const [quantite, setQuantite] = useState(0)
    const [prix, setPrix] = useState("")
    const [priceOnly,setPriceOnly] = useState(false)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(true)

    const handleChangePriceOnly = (e) => {
        setPriceOnly(e.target.checked)
    }

    const handleCancel = (e) => {
        closeModal()
    }

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)

        const data = {
            designation: designation,
            unite: unite,
            quantite: quantite,
            prix : prix,
            price_only : priceOnly
        }
        if(currentApproId){
            if(currentItem){
                axios.post("/api/approvisionnement/edit_item/" + currentItem.id, data, useToken())
                .then((res) => {
                    disableSubmit(false)
                    if(res.data.success){
                        updateData()
                        closeModal()
                    }                
                    else if(res.data.error == "EACCES")
                        setError("Une erreur est survenue.")
                    else if(res.data.error)
                        setError(res.data.error)
                })
                .catch((e) => {
                    console.error(e)
                    setError("Erreur d'envoie, réessayez.")
                    disableSubmit(false)
                })
            }
            else {
                axios.post("/api/approvisionnement/item/add/" + currentApproId, data, useToken())
                .then((res) => {
                    disableSubmit(false)
                    if(res.data.success){
                        updateData()
                        closeModal()
                    }                
                    else if(res.data.error == "EACCES")
                        setError("Une erreur est survenue.")
                    else if(res.data.error)
                        setError(res.data.error)
                })
                .catch((e) => {
                    console.error(e)
                    setError("Erreur d'envoie, réessayez.")
                    disableSubmit(false)
                })
            }
        }
        else {
            if(currentItem){
                const newItems = []
                items.forEach(item => {
                    if(item.index == currentItem.index)
                        newItems.push(data)
                    else
                        newItems.push(item)
                });
                setItems(newItems)
            }
            else {
                const newItems = [...items]
                newItems.unshift(data)
                setItems(newItems)
            }
            closeModal()
        }
    }

    useEffect(() => {
        if(priceOnly)
            setQuantite("1")
    }, [priceOnly])

    useEffect(() => {
        disableSubmit(
            (!priceOnly && (!designation || !unite || !/^\d+(\.\d+)?$/.test(quantite) || quantite == 0)) 
            || (priceOnly && (!designation || !/^\d+$/.test(prix) || prix == 0))
        )
    }, [designation, unite, quantite, prix, priceOnly])

    useEffect(() => {
        if(currentItem){
            setDesignation(currentItem.designation ? currentItem.designation : '')
            setUnite(currentItem.unite ? currentItem.unite : '')
            setQuantite(currentItem.quantite ? currentItem.quantite : 0)
            setPrix(currentItem.prix ? currentItem.prix : "")
            setPriceOnly(currentItem.price_only ? currentItem.price_only : false)
        }
    }, [])

    return (
        <div className='modal'>
            <div style={{maxHeight: 'none'}}>
                <div className="title-container" >
                    <h2>{(currentItem && currentItem.index) ? "Modifer la demande" : "Ajouter une demande"}</h2>
                </div>
                <form onSubmit={handleSubmit}>
                    <div className='form-body'>
                    <Textarea
                        required
                        label="Désignation"
                        value={designation}
                        onChange={setDesignation}/>
                    <div className='field-container'>
                        <InputCheckBox label="Prix seulement" checked={priceOnly} onChange={setPriceOnly}/>
                    </div>
                    {
                        !priceOnly &&
                        <InputText 
                            type="number"
                            min="0"
                            required
                            label="Quantité"
                            value={quantite} 
                            onChange={setQuantite}/>
                    }
                    {
                        !priceOnly &&
                        <InputText 
                            required
                            label="Unité"
                            placeholder="pièce, paquet, mètre, litre ..."
                            value={unite} 
                            onChange={setUnite}/>
                    }
                    <InputText 
                        type="number"
                        min="0"
                        required={priceOnly}
                        label={(priceOnly ? "Prix" : "Prix unitaire") + " (Ar)"}
                        value={prix} 
                        onChange={setPrix}/>
                    {
                        error &&
                        <div className='container-error'>
                            {error}
                        </div>
                    }
                    </div>
                    <div className="form-button-container">
                        <button className='primary' disabled={submitDisabled} type="submit"> {(currentItem && currentItem.index) ? "Modifier" : "Ajouter" }</button>
                        <button className='secondary' onClick={handleCancel}> Annuler </button>
                    </div>
                </form>
            </div>
        </div>
    )
}
