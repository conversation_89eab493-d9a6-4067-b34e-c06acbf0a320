const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const { sendMail } = require('../auth')

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == 'task')
const destination_vg = (emails) => {
    return ["<EMAIL>","<EMAIL>","<EMAIL>"].concat(emails.map(e => e.email))
}

const destination_test = ["<EMAIL>"]

function getDate(){
    return moment().format("YYYY-MM-DD")
}

const sqlGetGroupSites = `
    SELECT 
        s1.nom AS nom_site_reference,
        s1.group_planning_id,
        COUNT(s2.idSite) AS nb_sites,
        s1.superviseur_id as sup,
        s1.resp_sup_id as manager
    FROM sites s1
    JOIN sites s2 ON s1.group_planning_id = s2.group_planning_id
    WHERE s1.group_planning_id IS NOT NULL and s1.pointage = 1 and (s1.soft_delete is null OR s1.soft_delete = 0)
    AND s1.idSite = (
        SELECT MIN(s3.idSite)
        FROM sites s3
        WHERE s3.group_planning_id = s1.group_planning_id
    )
    GROUP BY s1.group_planning_id, s1.nom, s1.superviseur_id, s1.resp_sup_id
    HAVING COUNT(s2.idSite) > 1
`

const sqlSitesOnGroup = `
    SELECT s.idSite as id, s.nom, u1.name as manager, u1.email as email_manager, u2.name as superviseur, u2.email email_sup, s.group_pointage_id FROM sites s
    LEFT JOIN users u1 ON u1.id = s.resp_sup_id
    LEFT JOIN users u2 ON u2.id = s.superviseur_id 
    WHERE s.group_planning_id = ?
`

const sqlSelectOperationEmail = `SELECT u.email FROM users u 
    WHERE u.role in ('resp_op')
    and u.email not in ('<EMAIL>', '<EMAIL>', '<EMAIL>')
`

const sqlUpdateLastGroupeSiteExport = `UPDATE params p SET p.value = ? WHERE p.key = 'last_group_site_export'`

const sqlSelectLastGroupSiteExport = `SELECT value FROM params p WHERE p.key = 'last_group_site_export'`

const getDetails = (group_planning_id) => {
    return new Promise((resolve, reject) => {
        pool.query(sqlSitesOnGroup, [group_planning_id], (err, sites) => {
            if (err) {
                console.error(err);
                reject(err);
            } else {
                resolve(sites);
            }
        });
    });
};

const getResponsable = (responsable_id) => {
    return new Promise((resolve, reject) => {
        pool.query("select u.name, u.email from users u where u.id = ?", [responsable_id], (err, responsable) => {
            if (err) {
                console.error(err)
                reject(err)
            } else {
                resolve(responsable)
            }
        })
    })
}

const getRegion = (group_pointage_id) => {
    let region = ""
    if (group_pointage_id) {
        if (group_pointage_id == 1)
            region = "PROVINCE"
        if (group_pointage_id == 2)
            region = "TAMATAVE"
        if (group_pointage_id == 3)
            region = "TANA"
        return region
    }
    return ""
} 

const generateGroupSiteExcelFile = async (workbook, data) => {
    const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}

    const fontBold = {
        bold: true
    }

    const fontPrimary = { color: { argb: '336666' } }
	const fontDanger = { color: { argb: 'e91e63' } }

    const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    const worksheet = workbook.addWorksheet("Groupes")
    worksheet.getColumn('A').width = 50
    worksheet.getColumn('B').width = 20

    worksheet.getCell('A1').value = "Sites"
    worksheet.getCell('A1').border = borderStyle
    worksheet.getCell('A1').font = fontBold
    worksheet.getCell('A1').alignmentStyle = alignmentStyle
    worksheet.getCell('B1').value = "Nombre"
    worksheet.getCell('B1').border = borderStyle
    worksheet.getCell('B1').font = fontBold
    worksheet.getCell('B1').alignmentStyle = alignmentStyle
    let line = 2
    for(const site of data) {
        worksheet.getCell('A' + line).value = site.nom_site_reference
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).alignmentStyle = alignmentStyle
        worksheet.getCell('B' + line).value = site.nb_sites
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).alignmentStyle = alignmentStyle

        const sheetName = site.nom_site_reference.replace(/[\\/*?:[\]]/g, '_');
        const detailsWorkSheet = workbook.addWorksheet(sheetName)

        detailsWorkSheet.getColumn('A').width = 15
        detailsWorkSheet.getColumn('B').width = 50
        detailsWorkSheet.getColumn('C').width = 30

        detailsWorkSheet.mergeCells('A1:C1')
        detailsWorkSheet.mergeCells('A2:C2')
        
        detailsWorkSheet.getCell('A4').value = "Id"
        detailsWorkSheet.getCell('A4').border = borderStyle
        detailsWorkSheet.getCell('A4').font = fontBold
        detailsWorkSheet.getCell('A4').alignmentStyle = alignmentStyle
        detailsWorkSheet.getCell('B4').value = "Nom"
        detailsWorkSheet.getCell('B4').border = borderStyle
        detailsWorkSheet.getCell('B4').font = fontBold
        detailsWorkSheet.getCell('B4').alignmentStyle = alignmentStyle
        detailsWorkSheet.getCell('C4').value = "Region"
        detailsWorkSheet.getCell('C4').border = borderStyle
        detailsWorkSheet.getCell('C4').font = fontBold
        detailsWorkSheet.getCell('C4').alignmentStyle = alignmentStyle

        const manager = await getResponsable(site.manager)
        const superviseur = await getResponsable(site.sup)

        detailsWorkSheet.getCell('A1').value = (Array.isArray(manager) && manager.length > 0) ? ` Manager : ${manager[0].name} <${manager[0].email}>` : 'Manager : '
        detailsWorkSheet.getCell('A1').font = fontBold
        detailsWorkSheet.getCell('A1').alignmentStyle = alignmentStyle
        detailsWorkSheet.getCell('A2').value = (Array.isArray(superviseur) && superviseur.length > 0) ?  ` Superviseur : ${superviseur[0].name} <${superviseur[0].email}>` : 'Superviseur : '
        detailsWorkSheet.getCell('A2').font = fontBold
        detailsWorkSheet.getCell('A2').alignmentStyle = alignmentStyle

        const details = await getDetails(site.group_planning_id)
        let lineDetails = 5
        for (const detail of details) {
            detailsWorkSheet.getCell('A' + lineDetails).value = detail.id
            detailsWorkSheet.getCell('A' + lineDetails).border = borderStyle
            detailsWorkSheet.getCell('B' + lineDetails).value = detail.nom
            detailsWorkSheet.getCell('B' + lineDetails).border = borderStyle
            detailsWorkSheet.getCell('B' + lineDetails).alignmentStyle = alignmentStyle
            detailsWorkSheet.getCell('C' + lineDetails).value = getRegion(detail.group_pointage_id)
            detailsWorkSheet.getCell('C' + lineDetails).border = borderStyle
            detailsWorkSheet.getCell('C' + lineDetails).alignmentStyle = alignmentStyle
            lineDetails++
        }
        line ++
    }
}

const doExportGroupSite = async (date) => {
    console.log("do export site with same group_planning_id")
    pool.query(sqlSelectOperationEmail, [], async(err, emails) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb email: " + emails.length)
            pool.query(sqlGetGroupSites, [], async(err, sites) => {
                if (err)
                    console.error(err)
                else if (sites.length == 0) {
                    console.log("Sites is empty")
                } else {
                    const workbookGroupSites = new Excel.Workbook()
                    await generateGroupSiteExcelFile(workbookGroupSites, sites)
                    const groupSiteBuffer = await workbookGroupSites.xlsx.writeBuffer()
                    sendMail(
                        pool,
                        isTask ? destination_vg(emails) : destination_test,
                        "Liste des sites groupés", 
                        "Veuillez trouver ci joint la liste des sites groupés à ce jour",
                        [
                            {
                                filename: "groupe_de_site.xlsx",
                                content: groupSiteBuffer
                            }
                        ],
                        (response) => {
                            if(response && isTask){
                                pool.query(sqlUpdateLastGroupeSiteExport, [date], (e, r) =>{
                                    if(e)
                                        console.error(e)
                                    else
                                        console.log("update last group site export: " + r)
                                    process.exit(1)
                                })
                            }
                            else
                                process.exit(1)
                        }
                        , isTask
                    )
                }
            })
        }
    })
}

if(process.argv[2] == "test") {
    console.log("send test...")
    doExportGroupSite()
} else if(isTask){
    let date = getDate()
    console.log("send task...")
    pool.query(sqlSelectLastGroupSiteExport, [], (err, result) => {
        if(err)
            console.error(err)
        else if(result && result[0].value == date) {
            console.log("export list group_site already done!")
            process.exit(1)
        }
        else {
            if (moment(date).day() === 1 && moment().isAfter(moment().set({hour: 8, minute: 0}))) {
                console.log("exporting ...")
                doExportGroupSite(date)
            } else {
                console.log("not monday...")
                process.exit(1)
            }
        }
    })
} else
    console.log("please specify command!")