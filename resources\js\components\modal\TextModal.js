import React, { useEffect, useState } from 'react';

import InputText from '../input/InputText';
import { useLocation, useNavigate } from 'react-router-dom';

export default function TextModal({label, param, closeModal}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [value, setValue] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleOk = () => {
        let params = new URLSearchParams(location.search)
        params.set(param, value)
        navigate(location.pathname + "?" + params)
        closeModal()
    }
    
    useEffect(() => {
        disableSubmit(!value)
    }, [value])

    useEffect(() => {
        if(param == "cin_text") {
            disableSubmit(/(^[0-9]{3} [0-9]{3} [0-9]{3} [0-9]{3}$)/u.test(value) ? false : true)
        }
    }, [param, value])

    return <div className='modal'>
        <div>
            <div className='input-container'>
                <InputText
                    required
                    type="text"
                    label={label ? label : "Champ"}
                    value={value}
                    onChange={setValue}
                    onEnter={handleOk}/>
            </div>
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Ok</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
    
}
