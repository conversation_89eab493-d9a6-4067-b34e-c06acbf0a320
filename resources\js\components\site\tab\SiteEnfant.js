import React, { useEffect, useState } from 'react';
import LoadingPage from '../../loading/LoadingPage';
import axios from 'axios';
import useToken from '../../util/useToken';
import InputSite from '../../input/InputSite';

export default function SiteEnfant({ auth, value, updateData, site }) {
    const [isLoading, toggleLoading] = useState(false)
    const [childs, setChilds] = useState([])
    const [showAddModal, toggleAddModal] = useState(false)
    const [child, setChild] = useState(null)
    const [showSite, toggleSite] = useState(false)
    const [error, setError] = useState("")

    useEffect(() => {
        getChilds()
    }, [])

    const getChilds = () => {
        toggleLoading(true)
        axios.get('/api/site/childs/' + site.id, useToken())
        .then((res) => {
            setChilds(res.data.sites)
            toggleLoading(false)
        })
    }
    const addChild = (child) => {
        toggleLoading(true)
        console.log(child)
        console.log(site.id)
        axios.post('/api/site/add_child/' + site.id, {child: child.id}, useToken())
        .then((res) => {
            if(res.data.error) {
                console.error(res.data.error)
                setError(res.data.error)
                toggleLoading(false)
            } else {
                getChilds()
                setChild({})
                getChilds()
                toggleAddModal(false)
            }
            toggleLoading(false)
        })
    }

    return <>
                {
                    isLoading ?
                        <LoadingPage />
                        :
                        <>
                            {(["resp_op", "room"].includes(auth.role) || auth.id == site.resp_sup_id) &&
                                <div className='tab-list-action'>
                                    <div className='action-container'>
                                        <span>
                                            <span onClick={() => toggleAddModal(true)}>
                                                Ajouter un enfant
                                            </span>
                                        </span>
                                    </div>
                                </div>
                            }
                            {
                                childs.map(ch => 
                                    <div key={ch.idsite} className='card-container'>
                                        <span className='secondary'>{ch.nom}</span> 
                                    </div>
                                )
                            }
                            
                            {
                                showAddModal &&
                                <div className='modal' style={showSite ? {display: 'none'} : {}}>
                                    <div>
                                    <div className='input-container'>
                                        <label>Ajouter site enfant <span className='danger'>*</span></label>
                                        <input
                                            type="text" 
                                            value={child? child.nom : ""}
                                            readOnly
                                            // disabled={disabled}
                                            onClick={() => {toggleSite(true)}}
                                            />
                                    </div>
                                        {error && <div className='danger'>{error}</div>}
                                        <div className='form-button-container'>
                                            <button className='btn btn-primary' onClick={() => addChild(child)}>Ajouter</button>
                                            <button onClick={() => toggleAddModal(false)}>Annuler</button>
                                        </div>
                                    </div>
                                </div>
                            }
                            {showSite && <InputSite value={child} label="Ajouter site enfant" onChange={setChild} hideInput closeModal={() => toggleSite(false)} updateData={updateData} site={site} withoutDelete required/>}
                        </>
                }
            </>
};