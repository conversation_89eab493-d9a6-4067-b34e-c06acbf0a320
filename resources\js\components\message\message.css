.message-log {
    color: #555;
}
.message-content
, .message-content > p
, .message-content > ul
, .message-content > ul > li
, .message-content > ul > li > ul > li
, .message-content > table
, .message-content > table > tbody > tr > td {
    font-family: "CallingCode" !important;
    overflow-wrap: break-word;
}

.message-content > table {
    border: 1px #888 solid;
    padding-bottom: 10px;
    border-collapse: collapse;
    width: 100%;
}

.message-content > table > thead th,
.message-content > table > tbody td,
.message-content > table > tfoot td{
    padding: 9px;
    border: 1px #888 solid;
}
.message-content > p {
    padding-bottom: 10px;
}
.message-content > ul {
    padding-left: 10px;
    padding-bottom: 10px;
    list-style-position: inside
}
.message-content > ul > li > ul {
    padding-left: 10px;
    padding-bottom: 10px;
    list-style-position: inside
}
.message-content > ul > li {
    padding-bottom: 10px;
}
.message-content > ul > li > ul > li {
    padding-bottom: 5px;
}
.message-content > table > tbody > tr > td {
    padding: 5px;
}
.message-user{
    padding-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: top;
    width: 100%;
}
.message-pj{
    padding: 10px 0px;
}
.pj-link{
    display: inline-block;
    background-color: #ccc;
    color: white;
    padding: 3px;
    border: 1px whitesmoke solid;
    border-radius: 5px;
    margin-right: 7px;
}
.text-wrap{
    white-space: break-spaces;
}
.display-flex{
    display: flex;
}
.margin-left-10{
    margin-left: 10px;
}
.button-arrow{
    display: flex;
    margin-top: 10px;
}

@media print {
    .no-print {
        display: none;
    }
    .print-only {
        display: block !important;  /* Afficher les éléments uniquement pour l'impression */
    }
    @page {
        margin-top: 1cm; /* Ajoute une marge de 5cm en haut de chaque page */
    }

    body {
        margin: 0; /* Réinitialise les marges du body pour éviter des marges supplémentaires */
    }
}