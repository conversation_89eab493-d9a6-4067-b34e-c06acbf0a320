import React from 'react'
import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'

export default function PrimePaie({ primeState, setPrimeState }) {
  return (
    <div>
        <DualContainer>
            <InputText type="number" placeholder="Prime" value={primeState.prime} label="Prime" disabled />
            <InputText type="number"
                label="Prime Exceptionnelle"
                value={primeState.primeExceptionnelle}
                onChange={(newPrExc) => setPrimeState({ ...primeState, primeExceptionnelle: parseFloat(newPrExc) })}
            />
        </DualContainer>
        <DualContainer>
            <InputText type="number"
                label="Prime Ancienneté"
                value={primeState.primeAnc}
                onChange={(newPrAnc) => setPrimeState({ ...primeState, primeAnc: parseFloat(newPrAnc) })}
            />
            <InputText type="number"
                label="Prime Entretien"
                value={primeState.primeEntret}
                onChange={(newPrEntret) => setPrimeState({ ...primeState, primeEntret: parseFloat(newPrEntret) })}
            />
        </DualContainer>
        <DualContainer>
            <InputText type="number"
                label="Prime Assid"
                value={primeState.primeAssid}
                onChange={(newPrAssid) => setPrimeState({ ...primeState, primeAssid: parseFloat(newPrAssid) })}
            />
            <InputText type="number"
                label="Prime Responsabilité"
                value={primeState.primeResp}
                onChange={(newPrResp) => setPrimeState({ ...primeState, primeResp: parseFloat(newPrResp) })}
            />
        </DualContainer>
        <DualContainer>
            <InputText type="number"
                label="Idm de deplacement"
                value={primeState.idmDepl}
                onChange={(newIdmDepl) => setPrimeState({ ...primeState, idmDepl: parseFloat(newIdmDepl) })}
            />
            <InputText type="number"
                label="Prime Div"
                value={primeState.primeDiv}
                onChange={(newPrDiv) => setPrimeState({ ...primeState, primeDiv: parseFloat(newPrDiv) })}
            />
        </DualContainer>
        <DualContainer>
            <InputText type="number" label="Part Variable" value={primeState.partVariable} disabled />
            <InputText type="number"
                label="Perdiem"
                value={primeState.perdiem}
                onChange={(newPerdiem) => setPrimeState({ ...primeState, perdiem: parseFloat(newPerdiem) })}
            />
        </DualContainer>
    </div>
  )
}
