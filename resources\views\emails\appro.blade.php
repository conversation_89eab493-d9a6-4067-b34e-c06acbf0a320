@if ($note != null)
<p style="white-space: pre-line;">
    {{$note}}
</p>
@endif
<h3>DA-{{$appro->year}}/{{$appro->reference}}</h3>
<p style="white-space: pre-line;">
    Motif: {{$appro->objet}} <br/>
    Service: {{$appro->service}} <br/>
    Demandeur: {{$appro->user_nom . ' <' . $appro->user_email . '>'}} <br/>
</p>
<h3>Demande</h3>
<p style="white-space: pre-line;">
    @foreach ($items as $item)
        <b>{{$item->designation}}</b><br/>
        @if ($item->price_only)
            Prix : {{$item->prix}}
        @else
            Quantité : {{$item->quantite}} {{$item->unite}}
            @if ($item->prix)
                <br/>Prix unitaire : {{$item->prix}}
            @endif
        @endif
        <br/>
    @endforeach
</p>
<br/>
<a href={{env('APP_URL'). "/da/show/". $appro->id}}>Cliquez ici pour voir les détails ou répondre</a>