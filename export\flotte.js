const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const {sendMail} = require("../auth")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == "task")
const destination_vg = ["og<PERSON><PERSON><EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
    "<EMAIL>", "<EMAIL>","<EMAIL>", "<EMAIL>", "<EMAIL>", 
    "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectLastFlotteExport = "SELECT value FROM params p WHERE p.key = 'last_flotte_export'"

const sqlSelectStatus = "SELECT name, description FROM status order by ordre"

function sqlSelectFlotte(dateString){
    const begin = moment(dateString).subtract(7, "day").format("YYYY-MM-DD") + " 06:00:00"
    const end = moment(dateString).format("YYYY-MM-DD") + " 06:00:00"
    console.log(begin)
    console.log(end)
	return "SELECT fl.id, fl.site_id, fl.objet, fl.commentaire, fl.created_at, fl.status, " +
        "s.nom as 'site', u.name as 'user_nom', u.email as 'user_email', h.note " +
        "from flottes fl " +
        "left join sites s on s.idsite = fl.site_id " +
        "left join users u on u.id = fl.user_id " +
        "left join historiques h on h.id = fl.note_id " +
        "where (fl.status not in ('done', 'draft') " + 
        "or (fl.status in ('done', 'draft') and fl.updated_at > '" + begin +"' and fl.updated_at <= '" + end +"')) " +
        "order by fl.created_at"
}

function sqlUpdateLastFlotteExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_flotte_export'"
}

function generateFlotteExcelFile(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    data.forEach(stat => {
        const worksheet = workbook.addWorksheet(stat.description)
        worksheet.getColumn('A').width = 40
        worksheet.getColumn('B').width = 40
        worksheet.getColumn('C').width = 50
        worksheet.getColumn('D').width = 40
        worksheet.getColumn('E').width = 40
        worksheet.getColumn('F').width = 20

        worksheet.getCell('A1').value = stat.description + " (" + stat.flottes.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:F1')

        let line = 3
        worksheet.getCell('A' + line).value = "Site"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontBold
        worksheet.getCell('B' + line).value = "Objet"
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).font = fontBold
        worksheet.getCell('C' + line).value = "Commentaire"
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).font = fontBold
        worksheet.getCell('D' + line).value = "Note"
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).font = fontBold
        worksheet.getCell('E' + line).value = "Demandeur"
        worksheet.getCell('E' + line).border = borderStyle
        worksheet.getCell('E' + line).font = fontBold
        worksheet.getCell('F' + line).value = "Créé"
        worksheet.getCell('F' + line).border = borderStyle
        worksheet.getCell('F' + line).font = fontBold
        line++

        stat.flottes.forEach(flotte => {
            worksheet.getCell('A' + line).value = flotte.site ? capitalizeFirstLetter(flotte.site) : ''
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = flotte.objet
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = flotte.commentaire
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('C' + line).alignment = alignmentStyle
            worksheet.getCell('D' + line).value = flotte.note
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('E' + line).value = flotte.user_nom + " <" + flotte.user_email + ">"
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('F' + line).value = moment(flotte.created_at).format("DD-MM-YY HH:mm")
            worksheet.getCell('F' + line).border = borderStyle
            line++
        })
    })
}

function doFlotteExport(dateString){
	console.log("doFlotteExport")
    pool.query(sqlSelectStatus, [], async (err, status) => {
        if(err)
            console.error(err)
        else {
            pool.query(sqlSelectFlotte(dateString), [], async (err, flottes) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb flotte: " + flottes.length)
                    const flotteByStatus = []
                    status.map(stat => {
                        stat.flottes = []
                        flottes.map(flotte => {
                            if(stat.name == flotte.status)
                                stat.flottes.push(flotte)
                        })
                        if(stat.flottes.length > 0){
                            flotteByStatus.push(stat)
                        }
                    })
                    const workbookFlotte = new Excel.Workbook()
                    const header = "Flotte " + moment(dateString).format("DD MMMM YYYY")
                    generateFlotteExcelFile(workbookFlotte, header, flotteByStatus)
                    const flotteBuffer = await workbookFlotte.xlsx.writeBuffer()
                    sendMail(
                        pool,
                        isTask ? destination_vg : destination_test,
                        header, 
                        "Veuillez trouver ci-joint le rapport des demandes de Flotte du " + moment(dateString).subtract(7, "days").format("DD MMMM YYYY") + " au " + moment(dateString).format("DD MMMM YYYY") + "<br/>"
                        + "<ul>"
                        + (flotteByStatus.map(stat => "<li>" + stat.description + ": " + stat.flottes.length + "</li>").join(""))
                        + "</ul>"
                        ,
                        [
                            {
                                filename: header + ".xlsx",
                                content: flotteBuffer
                            },
                        ],
                        (response) => {
                            if(response && isTask){
                                pool.query(sqlUpdateLastFlotteExport(dateString), [], (e, r) =>{
                                    if(e)
                                        console.error(e)
                                    else
                                        console.log("update last diag export: " + r)
                                    process.exit(1)
                                })
                            }
                            else
                                process.exit(1)
                        },
                        isTask
                    )
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2])){
    console.log("send test...")
    doFlotteExport(process.argv[2])
}
else if(isTask){
    if(moment().day() == 3 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
        let date_vigilance = moment().format("YYYY-MM-DD")
        pool.query(sqlSelectLastFlotteExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && result[0].value == date_vigilance) {
                console.log("export list flotte already done!")
                process.exit(1)
            }
            else {
                console.log("exporting ...")
                doFlotteExport(date_vigilance)
            }
        })
    }
    else {
        console.log("Not wednesday, skip Flotte")
    }
}
else
    console.log("please specify command!")