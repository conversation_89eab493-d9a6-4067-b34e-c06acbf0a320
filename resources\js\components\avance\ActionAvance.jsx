import React, { useState } from 'react'
import NoteModal from '../input/NoteModal';
import DoneAvanceModal from './DoneAvanceModal';
import { Link } from 'react-router-dom';

export default function ActionAvance({ auth, avance, updateData }) {
	const [showNoteModal, toggleNoteModal] = useState(false)
	const [currentAction, setAction] = useState(null)
	const [toDone, setToDone] = useState(false)
	const [showDoneModal, toggleDoneModal] = useState(false)

	const handleCancel = (id) => {
		setAction({
		header: "Annuler la demande",
		request: "/api/avance/cancel/" + id,
		required : true
		})
		toggleNoteModal(true);
	}
	return <>
		{
			showNoteModal &&
			<NoteModal action={currentAction} 
				updateData={() => updateData(true)}
				closeModal={() => toggleNoteModal(false)} 
				toDone={toDone}
				setToDone={setToDone} />
		}
		{
			showDoneModal &&
			<DoneAvanceModal avance={avance}
				updateData={() => updateData(true)}
				closeModal={() => toggleDoneModal(false)}/>
		}
		{ 
			["resp_rh"].includes(auth.role) &&
			<div className="action-container">
				{ 
					avance.status == "demande" &&
					<span>
						<Link to={"/avance/do_traite/" + avance.id}>Traiter</Link> 
					</span>
				}
				{
					avance.status == "traite" &&
					<span>
						<Link to={"/avance/edit/" + avance.id}>Editer</Link>
					</span>
				}
				{
					["traite", "demande"].includes(avance.status) &&
					<span onClick={() => toggleDoneModal(true)}>
						Terminer
					</span>
				}
				{
					(["traite", "demande"].includes(avance.status) && auth.id == avance.user_id) &&
					<span onClick={()=>handleCancel(avance.id)}>Annuler</span>
				}
			</div>
		}
		<div className="action-container">
		{
			(
				["superviseur", "resp_sup"].includes(auth.role) &&
				avance.status == "demande" &&
				auth.id == avance.user_id
			) &&
			<>
				<span> <Link to={"/avance/edit/" + avance.id}>Editer</Link> </span>  
				{(["demande"].includes(avance.status) && auth.id == avance.user_id) &&
					<span onClick={() => handleCancel(avance.id)}>Annuler</span>
				}
			</>
		}
		{
			(avance.status == "draft" && auth.id == avance.user_id) && 
			<span><Link to={"/avance/send_back/" +avance.id}>Renvoyer</Link></span>
		}
		</div>
	</>
}
