<?php

namespace App\Http\Controllers;

use App\Models\Site;
use App\Models\Absence;
use App\Models\HoraireEffectif;
use App\Models\Sanction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SiteController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    protected function searchIndex($request)
    {
        $searchArray = [];
        if ($request->id)
            $searchArray[] = "s.idsite = '" . $request->id . "'";
        else if ($request->site_id)
            $searchArray[] = "s.idsite = '" . $request->site_id . "'";
        else {
            if ($request->actif)
                $searchArray[] = " (s.soft_delete is null or s.soft_delete = 0) ";
            if ($request->sup_ndf)
                $searchArray[] = " (s.superviseur_id is null or s.superviseur_id = 0) and (s.soft_delete is null or s.soft_delete = 0) AND (s.group_planning_id is null or s.group_planning_id = s.idsite) ";
            if ($request->resp_sup_ndf)
                $searchArray[] = " (s.resp_sup_id is null or s.resp_sup_id = 0) AND (s.soft_delete is null or s.soft_delete = 0) AND (s.group_planning_id is null or s.group_planning_id = s.idsite) ";
            if ($request->created_at)
                $searchArray[] = " s.created_at > '" . $request->created_at . " 00:00:00' and " .
                    "s.created_at <= '" . $request->created_at . " 23:59:59' ";
            if ($request->nom) {
                $searchArray[] = SiteController::search_by_name_adress($request->nom);
            }
            if ($request->user_id || $request->superviseur_id)
                $searchArray[] = " s.superviseur_id = " . ($request->user_id ?? $request->superviseur_id) . " ";
            if ($request->resp_sup_id)
                $searchArray[] = " s.resp_sup_id = " . $request->resp_sup_id . " ";
        }

        $query_where = "";
        $query_and = "";
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " ";
        }
        if($request->nom){
            $query_where = $query_where . " order by exact_nom_match DESC, s.idsite desc limit " . $request->offset . ", 30";
            $query_and = $query_and . " order by exact_nom_match DESC, s.idsite desc limit " . $request->offset . ", 30";
        }
        else{
            $query_where = $query_where . " order by s.idsite desc limit " . $request->offset . ", 30";
            $query_and = $query_and . " order by s.idsite desc limit " . $request->offset . ", 30";
        }


        return compact('query_where', 'query_and');
    }

    public function index(Request $request)
    {
        $search = self::searchIndex($request);
        if($request->site_principale_archive){
            $sites = DB::select("SELECT s. idsite as 'id', s.soft_delete, s.pointeuse, s.nom, s.adresse, u.name as 'user', coalesce(ur.email, u.email) as 'email',
                resp_sup.name as 'resp_sup_name', resp_sup.email as 'resp_sup_email'
                FROM sites s 
                LEFT JOIN sites child ON child.group_planning_id = s.idsite AND child.idsite != s.idsite AND (child.soft_delete = 0 or child.soft_delete IS NULL)
                LEFT JOIN users u on u.id = s.superviseur_id
                LEFT JOIN users ur on ur.id = u.real_email_id
                LEFT JOIN users resp_sup on resp_sup.id = s.resp_sup_id 
                WHERE s.pointage = 1 AND s.soft_delete = 1 AND s.group_planning_id is not null
                GROUP BY s.group_planning_id, s.idsite
                HAVING count(child.idsite) > 0 order by s.idsite desc limit " . $request->offset . ", 30 " );
        }
        else if (in_array($request->user()->role, ["rh", "resp_rh", "validateur", "resp_sup", "resp_op", "access", "room"])) {
            $sites = DB::select("SELECT s.idsite as 'id', s.soft_delete, s.pointeuse, s.nom, s.adresse, u.name as 'user', coalesce(ur.email, u.email) as 'email',
                resp_sup.name as 'resp_sup_name', resp_sup.email as 'resp_sup_email', (s.nom = ?) as exact_nom_match
                FROM sites s
                LEFT JOIN sites parent_site ON parent_site.idsite = s.group_planning_id
                LEFT JOIN users u on (u.id = s.superviseur_id or u.id = parent_site.superviseur_id)
                LEFT JOIN users ur on ur.id = u.real_email_id
                LEFT JOIN users resp_sup on (resp_sup.id = s.resp_sup_id or resp_sup.id = parent_site.resp_sup_id)
                WHERE s.pointage = 1 and s.idsite != 1363 " . $search['query_and'], [$request->nom]);
        } else {
            $sites = DB::select("SELECT s.idsite as 'id', s.soft_delete, s.pointeuse, s.nom, s.adresse, u.name as 'user', coalesce(ur.email, u.email) as 'email',
                resp_sup.name as 'resp_sup', resp_sup.email as 'resp_sup_email', (s.nom = ?) as exact_nom_match
                FROM sites s
                LEFT JOIN sites parent_site ON parent_site.idsite = s.group_planning_id
                LEFT JOIN users u on (u.id = s.superviseur_id or u.id = parent_site.superviseur_id) 
                LEFT JOIN users ur on ur.id = u.real_email_id
                LEFT JOIN users resp_sup on (resp_sup.id = s.resp_sup_id or resp_sup.id = parent_site.resp_sup_id)
                WHERE (s.superviseur_id  = ? or s.resp_sup_id = ? or parent_site.superviseur_id  = ? or parent_site.resp_sup_id = ?) and s.pointage = 1 and s.idsite != 1363 " . $search['query_and'], [$request->nom, $request->user()->id, $request->user()->id, $request->user()->id, $request->user()->id]);
        }
        return response()->json(compact('sites'));
    }

    public function show($id)
    {
        $site = DB::select("SELECT s.idsite as 'id', s.nom, s.heure_contrat, s.adresse, s.pointeuse, s.pointage, s.soft_delete, s.nb_agent_night_planning, s.nb_agent_day_planning,
            s.superviseur_id, u.name as 'user', coalesce(ur.email, u.email) as 'email',
            resp_sup.name as 'resp_sup_name', resp_sup.email as 'resp_sup_email', resp_sup.id as 'resp_sup_id',
            parent_site.idsite as 'parent_site_id', parent_site.nom as 'parent_site_name', s.group_planning_id ,
            parent_site.pointeuse as 'parent_pointeuse', parent_site.pointage as 'parent_pointage',parent_site.adresse as 'parent_adresse'
            FROM sites s
            LEFT JOIN sites parent_site ON parent_site.idsite = s.group_planning_id 
            LEFT JOIN users u on (u.id = s.superviseur_id or u.id = parent_site.superviseur_id)
            LEFT JOIN users ur on ur.id = u.real_email_id
            LEFT JOIN users resp_sup on (resp_sup.id = s.resp_sup_id or resp_sup.id = parent_site.resp_sup_id)
            WHERE s.idsite = ? ", [$id])[0];
        if($site)
            $site->horaires = HoraireEffectif::where('site_id', $id)->first();
        if($site->group_planning_id){
            $childs = DB::select("SELECT s.idsite as 'id', s.nom, s.adresse FROM sites s WHERE s.group_planning_id = ? AND (s.soft_delete = 0 or s.soft_delete IS NULL) AND s.idsite != ? ",[$id, $id]);
            $site->childs = $childs;
        }
        return response()->json($site);
    }

    public static function search_by_name_adress($value)
    {
        return " ( s.nom like '%" . $value . "%' or s.adresse like '%" . $value . "%' ) ";
        // $search_term = '+' . implode(' +', explode(' ', $value));
        // return "
        //     ( REPLACE(s.nom, '  ', ' ') like REPLACE('%" . $value . "%' , '  ', ' ') 
        //         or ( REPLACE(s.adresse, '  ', ' ') like REPLACE('%" . $value . "%', '  ', ' ')) 
        //         or (MATCH(s.nom) AGAINST ( '$search_term' IN BOOLEAN MODE) or MATCH(s.adresse) AGAINST ('$search_term' IN BOOLEAN MODE))
        //     )
        // ";
    }

    public function search(Request $request)
    {
        $search_condition = SiteController::search_by_name_adress($request->value);
        if($request->without_soft_delete){
            $search_condition .= " AND (s.soft_delete is null or s.soft_delete = 0)";
        }
        $sites = DB::select("SELECT s.idsite as 'id', s.nom, s.adresse, s.pointeuse, s.soft_delete,  
            (s.nom = ?) as exact_nom_match,
            (s.adresse = ?) as exact_adresse_match
            FROM sites s
            WHERE $search_condition
            ORDER BY exact_nom_match DESC , s.idsite desc
            LIMIT ?, 30", [
                $request->value,
                $request->value,
                $request->offset,
            ]
        );
        return response(compact('sites'));
    }

    public function search_have_pointeuse(Request $request, $id)
    {
        $search_condition = SiteController::search_by_name_adress($request->value);
        $sites = DB::select("SELECT s.idsite as 'id', s.nom, s.adresse, s.pointeuse, s.pointage
            FROM sites s
            WHERE $search_condition
            AND (s.soft_delete is null or s.soft_delete = 0)
            AND s.pointage = 1 
            AND s.idsite <> ?
            order by s.idsite LIMIT ?, 30", [$id,
            $request->offset,
        ]);
        return response(compact('sites'));
    }

    public function search_by_resp_sup(Request $request)
    {
        $search_condition = SiteController::search_by_name_adress($request->value);
        $sites = DB::select("SELECT s.idsite as 'id', s.nom, s.adresse, g.nom as 'group'
            FROM sites s
            LEFT JOIN group_pointage_sites g ON g.id = s.group_pointage_id
            -- LEFT JOIN plannings pl ON pl.site_id = s.idsite and pl.date_planning = ?
            WHERE (s.resp_sup_id = ? or s.superviseur_id = ?) 
            AND $search_condition
            AND (s.soft_delete is null or s.soft_delete = 0)
            AND s.pointage = 1 
            AND (s.group_planning_id is null or s.group_planning_id = s.idsite)
            order by s.idsite desc LIMIT ?, 30", [
            $request->user()->id,
            $request->user()->id,
            $request->offset,
        ]);
        return response(compact('sites'));
    }

    public function search_site_planning(Request $request)
    {
        $search_condition = SiteController::search_by_name_adress($request->value);
        $sites = DB::select("SELECT s.idsite as 'id', s.nom, s.adresse, s.pointeuse, s.pointage, s.soft_delete
            FROM sites s
            WHERE $search_condition
            AND (s.group_planning_id is null or s.group_planning_id = s.idsite)
            AND (s.soft_delete is null or s.soft_delete = 0)
            AND s.pointage = 1
            AND s.idsite != 1363
            order by s.idsite LIMIT ?, 30", [
            $request->offset,
        ]);
        return response(compact('sites'));
    }

    public function search_with_pointage(Request $request)
    {
        $sites = DB::select("SELECT s.idsite as 'id', s.nom, s.adresse, g.nom as 'group'
            FROM sites s
            LEFT JOIN group_pointage_sites g ON g.id = s.group_pointage_id
            WHERE s.nom like ? and s.pointage = 1
            AND (s.soft_delete is null or s.soft_delete = 0)
            order by s.idsite desc LIMIT ?, 30", [
            '%' . $request->value . '%',
            $request->offset,
        ]);
        return response(compact('sites'));
    }

    public function childs($id, Request $request){
        $sites = DB::select("SELECT s.idsite, s.nom, s.adresse, s.pointeuse, s.pointage, s.soft_delete 
            FROM sites s
            WHERE s.group_planning_id = ? AND (s.soft_delete is null or s.soft_delete = 0) AND s.idsite !=  s.group_planning_id ",[ $id]);
        return response(compact('sites'));
    }
    public function add_child($id, Request $request){
        $auth = $request->user();
        $site = Site::find($id);
        if(in_array($auth->role, ['resp_op' ]) || $site->resp_sup_id == $auth->id){
            $child = Site::find($request->child);
            if($child->group_planning_id){
                if($child->group_planning_id == $child->idsite){
                    return response()->json(['error' => "Ce site a déjà des enfants"]);
                }
                else{
                    return response()->json(['error' => "Ce site a déjà un site de regroupement"]);
                }
                if($child->soft_delete == 1){
                    return response()->json(['error' => "Ce site enfant est déjà archivé"]);
                }
            }
            
            if($site->group_planning_id && $site->group_planning_id != $site->group_planning_id){
                return response()->json(['error' => "Ce site a déjà un enfant d'une autre site"]);
            }
            if($site->soft_delete == 1){
                return response()->json(['error' => "Ce site est déjà archivé"]);
            }
            $child->group_planning_id = $site->idsite;
            $child->save();
            if($site->group_planning_id){
                $site->group_planning_id = $site->idsite;
                $site->save();
            }
            return response()->json(['success' => 'Site enfant ajouté avec succès']);
        }
        return response()->json(['error' => 'EACCES']);
    }

    public function set_superviseur($id, Request $request){
        if(in_array($request->user()->role, ['rh', 'resp_rh', 'resp_sup', 'resp_op'])){
            $site = Site::find($id);
            $site->superviseur_id = $request->superviseur_id;
            Sanction::where('user_id', 191)->where('site_id', $id)->where('status', 'demande')->update(['superviseur_id' => $request->superviseur_id]);
            Absence::where('type_absence', 'mis_a_pied')->where('site_id', $id)->where('status', 'demande')->update(['superviseur_id' => $request->superviseur_id]);
            return $site->save();
        }
        return response()->json(['error' => 'EACCES']);
    }

    public function edit($id, Request $request){
        if(in_array($id, [1358, 58])){
            return response()->json(["error" => "Ce site n'est pas modifiable"]);
        }
        if(in_array($request->user()->role, ['rh', 'resp_rh', 'resp_sup', 'resp_op', 'room'])){
            if(!$request->regroup_planning){
                $validator = Validator::make(
                    $request->all(),
                    [
                        'superviseur_id' => 'required|exists:users,id',
                        'resp_sup_id' => 'required|exists:users,id',
                    ]
                );

                if ($validator->fails()) {
                    return response()->json(['error' => $validator->errors()->first()]);
                }
            }
            $site = Site::find($id);
            $old_site = clone $site;
            $site->superviseur_id = $request->superviseur_id;
            $site->resp_sup_id = $request->resp_sup_id;
            if($old_site->group_planning_id && $old_site->group_planning_id != $request->parent_site_id){
                $same_regroup = DB::select("SELECT * FROM sites where group_planning_id = $old_site->group_planning_id and idsite!= $id AND idsite != group_planning_id", []);
                if(count($same_regroup) == 0){
                    $old_parent = Site::find($old_site->group_planning_id);
                    $old_parent->group_planning_id = null;
                    $old_parent->save();
                }
            }
            if ($request->regroup_planning) {
                if(!$request->parent_site_id){
                    return response()->json(['error' => "Vous devez sélectionner un site de regroupement"]);
                }
                $site->group_planning_id = $request->parent_site_id;
                $parent_site = Site::find($request->parent_site_id);
                if ($parent_site->soft_delete == 1) {
                    return response()->json(['error' => "Ce site de regroupement est déjà archivé"]);
                }
                if ($id == $request->parent_site_id) {
                    return response()->json(['error' => "Vous ne pouvez pas regrouper un site avec lui-même"]);
                }
                $parent_site->group_planning_id = $request->parent_site_id;
                $parent_site->save();
            }
            else{
                $site->group_planning_id = null;
            }
            $site->heure_contrat = $request->heure_contrat;
            Sanction::where('user_id', 191)->where('site_id', $id)->where('status', 'demande')->update(['superviseur_id' => $request->superviseur_id]);
            Absence::where('type_absence', 'mis_a_pied')->where('site_id', $id)->where('status', 'demande')->update(['superviseur_id' => $request->superviseur_id]);
            if ($site->save()) {
                HistoriqueController::update_site($request, $old_site, "Modification");
                return response()->json(['success' => 'Site modifié avec succès', 'id' => $site->idsite]);
            };
        }
        return response()->json(['error' => 'EACCES']);
    }

    public function index_unfinished_planning(Request $request) {
        $auth = $request->user();
        if ($request->offset || $request->offset > -1)
            $orderBy = " order by pln.date_planning DESC limit " . $request->offset . ", 30";
        else 
            $orderBy = " order by pln.date_planning DESC";

        if($request->superviseur_id)
            $groupBy = " GROUP BY pln.id ";
        else{
            $groupBy = " GROUP BY sup.id, sup.name, sup.email ";
            $orderBy = " ORDER BY latest_date_planning DESC";
        }
        if (in_array($auth->role, ['superviseur', 'resp_sup', 'resp_op']) || in_array($auth->id, [1, 55, 221])) {
            $date_planning = $request->date_planning;
            $sql = '';
            if ($request->resp_sup_id || $request->not_found_resp) {
                $sql = "SELECT  u.name as 'resp_sup', u.id as 'resp_sup_id', u.email as 'resp_sup_email',
                    s.nom as 'site', s.idsite, s.total_hour, s.nb_agent_night, s.nb_agent_day,
                    hr.nom as 'horaire'
                    FROM sites s
                    LEFT JOIN users u ON u.id = s.resp_sup_id
                    LEFT JOIN plannings pl ON pl.site_id = s.idsite and pl.date_planning = '$date_planning'
                    LEFT JOIN horaires hr ON hr.id = s.horaire_pointage_id
                    WHERE (s.soft_delete is null or s.soft_delete = 0) 
                    AND s.pointage = 1
                    AND pl.id is null 
                    AND (s.group_planning_id IS NULL OR s.group_planning_id = s.idsite) AND s.idsite != 1363 ";

                if($request->user()->role == "resp_op") {
                    $regions = RegionUsersController::getRegions($request);
                    $sql .= " AND s.group_pointage_id in ($regions)";
                }
                if ($request->site_id) {
                    $sql .= " AND s.idsite = '$request->site_id' ";
                }
                if ($auth->role == "resp_sup") {
                    $sql .= " AND s.resp_sup_id = " . $auth->id;
                } else if ($request->not_found_resp) {
                    $sql .= " AND s.resp_sup_id is null";
                }
                // else{
                //     $sql .= " AND (s.resp_sup_id = $auth->id or s.superviseur_id = $auth->id) ";
                // }
                // return $sql . ($this->searchIndex($request))['query_and'];

                $plannings = DB::select($sql . ($this->searchIndex($request))['query_and'], []);
            } else {
                $sql = "SELECT count(s.idsite) 'nb_planning', u.name as 'resp_sup', u.id as 'resp_sup_id', u.email as 'resp_sup_email'
                    FROM sites s
                    LEFT JOIN users u ON u.id = s.resp_sup_id
                    LEFT JOIN plannings pl ON pl.site_id = s.idsite and pl.date_planning = '$date_planning'
                    WHERE (s.soft_delete is null or s.soft_delete = 0) 
                    AND s.pointage = 1
                    AND pl.id is null
                    AND (s.group_planning_id IS NULL OR s.group_planning_id = s.idsite) 
                    AND s.idsite != 1363 ";
                if($request->user()->role == "resp_op") {
                    $regions = RegionUsersController::getRegions($request);
                    $sql .= " AND s.group_pointage_id in ($regions)";
                }
                if ($request->site_id) {
                    $sql .= " AND s.idsite = '$request->site_id' ";
                }
                if (!in_array($auth->role, ["resp_op", "validateur", "access"])) {
                    $sql .= " AND (s.resp_sup_id = $auth->id or s.superviseur_id = $auth->id)";
                } else if ($request->superviseur_id) {
                    $sql .= " AND s.superviseur_id = $request->superviseur_id ";
                }
                $plannings = DB::select($sql . " group by u.id", []);
            }
            return response(compact('plannings'));
        }
        return response(['error' => "EACCES"]);
    }

    public function show_unfinished_planning(Request $request, $id)
    {
        $auth = $request->user();
        $date_planning = $request->date_planning;
        $planning = DB::select("SELECT  u.name as 'resp_sup', u.email as 'resp_sup_email',u.id as 'resp_sup_id', 
                    u2.name as 'superviseur', u2.id as 'superviseur_id', u2.email as 'superviseur_email',
                    s.nom as 'site', s.idsite, s.total_hour, s.nb_agent_night, s.nb_agent_day,
                    hr.nom as 'horaire'
                    FROM sites s
                    LEFT JOIN users u ON u.id = s.resp_sup_id
                    LEFT JOIN users u2 ON u2.id = s.superviseur_id
                    LEFT JOIN plannings pl ON pl.site_id = s.idsite and pl.date_planning = '$date_planning'
                    LEFT JOIN horaires hr ON hr.id = s.horaire_pointage_id
                    WHERE (s.soft_delete is null or s.soft_delete = 0) 
                    -- AND pl.id is null
                    -- AND s.superviseur_id is not null
                    AND s.idsite = ?", [$id])[0];
        $last_plannings = DB::select("SELECT * FROM plannings pl WHERE pl.site_id = ? ORDER BY pl.date_planning DESC", [$id]);
        $planning->last_plannings = $last_plannings;
        return response()->json($planning);
    }

    public function export(Request $request)
    {
        $auth = $request->user();
        $condition = "";
        if (in_array($auth->role, ['superviseur', 'resp_sup', 'resp_op', 'validateur'])) {
            if (in_array($auth->role, ['superviseur']))
                $condition .= " AND st.superviseur_id = $auth->id ";
            if (in_array($auth->role, ['resp_sup']))
                $condition .= " AND (st.superviseur_id or st.resp_sup_id) = $auth->id ";
            $sites = DB::select(
                "SELECT st.idsite, st.nom, st.adresse, st.total_hour, st.nb_agent_night, st.nb_agent_day,
                sec.id as 'secteur_id', sec.nom as 'secteur', sec.heure_contrat as 'sec_heure',
                sup.name as 'superviseur', sup.id as 'superviseur_id', sup.email as 'superviseur_email', 
                resp.name as 'manager', resp.id as 'manager_id', resp.email as 'manager_email'
                FROM sites st 
                    LEFT JOIN users sup ON sup.id = st.superviseur_id
                    LEFT JOIN users resp ON resp.id = st.resp_sup_id
                    LEFT JOIN secteurs sec ON sec.id = st.secteur_id
                WHERE
                    st.pointage = 1 
                    and st.idsite != 1363
                    and (st.soft_delete is null or st.soft_delete = 0) " . $condition,
                []
            );
            return response(compact('sites'));
        }
        return response(['error' => "EACCES"]);
    }

    public function alarm(Request $request, $id){
        $date_begin = (new \DateTime($request->date))->format('Y-m-d 00:00:00');
        $date_end = (new \DateTime($request->date))->format('Y-m-d 23:59:59');
        if($request->date_time){
            $date_begin = (new \DateTime($request->date_time))->sub(new \DateInterval('PT1H'))->format('Y-m-d H:i:s');
            $date_end = (new \DateTime($request->date_time))->add(new \DateInterval('PT1H'))->format('Y-m-d H:i:s');
        }
        // if($request->pointeuse_id){
        //     $alarms = DB::select("SELECT a.*, emp.id AS 'employe_id', emp.nom, emp.societe_id, 
        //         emp.numero_employe, emp.num_emp_saoi, emp.num_emp_soit, emp.numero_stagiaire, ev.description AS 'code_description'
        //         FROM alarms a 
        //         LEFT JOIN events ev ON ev.code = a.codeTevent
        //         LEFT JOIN employes emp ON emp.id = a.employe_id
        //         LEFT JOIN sites st ON st.idsite = ?
        //         WHERE a.site_id = ? AND a.pointeuse_id = st.pointeuse_id AND (a.dtarrived BETWEEN ? AND ?)", 
        //         [$id, $date_begin, $date_end]
        //     );
        // }
        // else{
        $alarms = DB::select("SELECT a.*, emp.id AS 'employe_id', emp.nom, emp.societe_id, 
            emp.numero_employe, emp.num_emp_saoi, emp.num_emp_soit, emp.numero_stagiaire, ev.description AS 'code_description'
            FROM alarms a 
            LEFT JOIN events ev ON ev.code = a.codeTevent
            LEFT JOIN employes emp ON emp.id = a.employe_id
            WHERE a.site_id = ? AND (a.dtarrived BETWEEN ? AND ?)", 
            [$id, $date_begin, $date_end]
        );
        // }
        $test_periodiques = DB::select("SELECT * 
            FROM test_periodiques 
            WHERE site_id = ? AND (dtarrived BETWEEN ? AND ?)", [$id, $date_begin, $date_end]);
        return response(compact('alarms', 'test_periodiques', 'date_begin', 'date_end'));
    }

    public function store_horaire(Request $request, $id){
        $auth = $request->user();
        $site = Site::find($id);
        if(in_array($auth->role, ["room", "resp_op"]) || $auth->id == $site->resp_sup_id){
            $status = "edit";
            $horaire = HoraireEffectif::where('site_id', $id)->first();
            $old = null;
            if(!$horaire){
                $horaire = new HoraireEffectif();
                $status = "add";
                $horaire->site_id = $id;
                $horaire->created_at = (new \DateTime())->format('Y-m-d H:i:s');
            }
            else{
                $old = clone $horaire;
            }
            $horaire->day_1 = $request->day[0] ?? 0;
            $horaire->day_2 = $request->day[1]?? 0;
            $horaire->day_3 = $request->day[2]?? 0;
            $horaire->day_4 = $request->day[3]?? 0;
            $horaire->day_5 = $request->day[4]?? 0;
            $horaire->day_6 = $request->day[5]?? 0;
            $horaire->day_0 = $request->day[6]?? 0;
            $horaire->day_ferie = $request->day[7]?? 0;
            $horaire->night_1 = $request->night[0]?? 0;
            $horaire->night_2 = $request->night[1]?? 0;
            $horaire->night_3 = $request->night[2]?? 0;
            $horaire->night_4 = $request->night[3]?? 0;
            $horaire->night_5 = $request->night[4]?? 0;
            $horaire->night_6 = $request->night[5]?? 0;
            $horaire->night_0 = $request->night[6]?? 0;
            $horaire->night_ferie = $request->night[7]?? 0;
            $horaire->updated_at = (new \DateTime())->format('Y-m-d H:i:s');
            if($horaire->save()){
                HistoriqueController::action_horaire_effectif($request, $horaire, $status, $old);
                return response()->json(['success' => 'Horaires mis à jour avec succès']);
            }
        }
        else
            return response()->json(['error' => "EACCESS"]);
    }
}
