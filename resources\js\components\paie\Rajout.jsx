import React from 'react'
import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'

export default function Rajout({rajout, setRajout}) {
    return (
        <div>
            <DualContainer>
                <InputText type="number"
                    label="Rappel"
                    value={rajout.rappel}
                    onChange={(value) => setRajout({ ...rajout, rappel: parseFloat(value) })}
                />
                <InputText type="number"
                    label=" All.Fam.Cnaps"
                    value={rajout.allFamCnaps}
                    onChange={(value) => setRajout({ ...rajout, allFamCnaps: parseFloat(value) })}
                />
            </DualContainer>
            <DualContainer>
                <InputText type="number"
                    label="Remb FraisFixe"
                    value={rajout.rembFraisFixe}
                    onChange={(value) => setRajout({ ...rajout, rembFraisFixe: parseFloat(value) })}
                />
            </DualContainer>
        </div>
    )
}
