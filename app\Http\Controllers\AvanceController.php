<?php

namespace App\Http\Controllers;

use App\Models\Avance;
use App\Models\Employe;
use App\Models\Historique;
use App\Models\Pointage;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AvanceController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    private static $attributeNames = array(
        "type_avance_id" => "Type d'avance",
    );

    public function search(Request $request)
    {
        $searchArray = [];
        if ($request->id) {
            $searchArray[] = " avc.id = " . $request->id . " ";
        } else {
            if ($request->created_at) {
                $searchArray[] =  " avc.created_at > '$request->created_at 00:00:00' and avc.created_at <= '$request->created_at 23:59:59' ";
            }
            if ($request->employe_id) {
                $searchArray[] = " avc.employe_id = '$request->employe_id' ";
            }
            if ($request->status) {
                $searchArray[] = " avc.status = '$request->status' ";
            }
            if ($request->date_paie) {
                list($year, $month) = explode('-', $request->date_paie);
                $searchArray[] = " YEAR(avc.date_paie) = '" . $year . "' AND MONTH(avc.date_paie) = '" . $month . "' ";
            }
        }
        $query_where = "";
        $query_and = "";
        if ($request->offset || $request->offset > -1) {
            $orderBy = " order by avc.id DESC limit " . $request->offset . ", 30";
        } else {
            $orderBy = " order by avc.id DESC";
        }
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . "";
        }
        $query_where = $query_where . " " . $orderBy;
        $query_and = $query_and . " " . $orderBy;
        return compact("query_where", "query_and");
    }

    public function index(Request $request){
        if (in_array($request->user()->role,["resp_rh", "validateur"])) {
            $sql = "SELECT avc.id, avc.montant, avc.created_at, avc.status, avc.motif, s.nom as 'site',
                avc.employe_id, avc.date_paie, avc.type_avance_id as 'type_id', avc.user_id, avc.group_id, avc.montant_total,
                stat.description as 'status_description', stat.color as 'status_color', s.nom as 'site',
                emp.nom as 'employe', emp.societe_id,emp.numero_stagiaire,emp.numero_employe,emp.num_emp_soit, emp.num_emp_saoi,
                u.name as 'user_nom', u.email as 'user_email', avct.name as avc_type, avct.description as type_description,
                (select p.id FROM paies p where status='done' and p.date_paie = avc.date_paie and p.employe_id = avc.employe_id limit 1) as 'paie_id'
                FROM avances avc 
                LEFT JOIN employes emp on emp.id = avc.employe_id
                LEFT JOIN sites s on s.idsite = emp.real_site_id
                LEFT JOIN users u on u.id = avc.user_id
                LEFT JOIN type_avances avct on avc.type_avance_id = avct.id
                LEFT JOIN `status` stat on stat.name = avc.status";
            $avances = DB::select($sql.($this->search($request))['query_where']);
        }
        elseif (in_array($request->user()->role,["superviseur","resp_sup", "resp_op"])) {
            $result = DB::select("SELECT group_id from avances where user_id = ? and group_id is not null",[$request->user()->id]);
            $groupIds = array_map(function ($resultItem) {
                return (string)$resultItem->group_id;
            }, $result);
            
            $avances = DB::select("SELECT avc.id, avc.montant, avc.created_at, avc.status, avc.motif, s.nom as 'site',
                avc.employe_id, avc.date_paie, avc.type_avance_id as 'type_id',avc.user_id,avc.group_id,
                stat.description as 'status_description', stat.color as 'status_color',
                emp.nom as 'employe', emp.societe_id,emp.numero_stagiaire,emp.numero_employe,emp.num_emp_soit, emp.num_emp_saoi,
                u.name as 'user_nom', u.email as 'user_email', avct.name as avc_type, avct.description as type_description,
                (select p.id FROM paies p where status='done' and p.date_paie = avc.date_paie and p.employe_id = avc.employe_id limit 1) as 'paie_id'
                FROM avances avc 
                LEFT JOIN employes emp on emp.id = avc.employe_id
                LEFT JOIN sites s on s.idsite = emp.real_site_id
                LEFT JOIN users u on u.id = avc.user_id
                LEFT JOIN `status` stat on stat.name = avc.status
                LEFT JOIN type_avances avct on avc.type_avance_id = avct.id
                WHERE (avc.group_id in (?) or (avc.user_id = ? ))".$this->search($request)["query_and"], [ implode(',', $groupIds), $request->user()->id]
            );
        }
        return response(compact('avances'));
    }

    public function show(Request $request, $id){
        if (in_array($request->user()->role, ["resp_rh", "validateur"])) {
            $avance = DB::select("SELECT avc.id, avc.montant, avc.created_at, avc.status, avc.motif, s.nom as 'site',
                avc.employe_id, avc.date_paie, avc.type_avance_id as 'type_id', avc.user_id,
                stat.description as 'status_description', stat.color as 'status_color',
                emp.nom as 'employe', emp.societe_id, emp.numero_stagiaire, emp.numero_employe, emp.num_emp_soit, emp.num_emp_saoi,
                u.name as 'user_nom', u.email as 'user_email', avct.name as avc_type, avct.description as type_description,
                (select p.id FROM paies p where status='done' and p.date_paie = avc.date_paie and p.employe_id = avc.employe_id limit 1) as 'paie_id'
                FROM avances avc 
                LEFT JOIN employes emp on emp.id = avc.employe_id
                LEFT JOIN sites s on s.idsite = emp.real_site_id
                LEFT JOIN users u on u.id = avc.user_id
                LEFT JOIN type_avances avct on avc.type_avance_id = avct.id
                LEFT JOIN `status` stat on stat.name = avc.status
                WHERE avc.id = ?",[$id]
            )[0];
            $pieces = DB::select("SELECT pj.id FROM piece_jointes pj where pj.avance_id = ?",[$id]);
            $avance->nb_pj = count($pieces);
            return response()->json($avance);
        }
        else if (in_array($request->user()->role, ["superviseur", "resp_sup", "resp_op"])) {
            $result = DB::select("SELECT group_id from avances where user_id = ? and group_id is not null", [$request->user()->id]);
            $groupIds = array_map(function ($resultItem) {
                return (string)$resultItem->group_id;
            }, $result);

            $avance = DB::select("SELECT avc.id, avc.montant, avc.created_at, avc.status, avc.motif, s.nom as 'site',
                avc.employe_id, avc.date_paie, avc.type_avance_id as 'type_id', avc.user_id,
                stat.description as 'status_description', stat.color as 'status_color',
                emp.nom as 'employe', emp.societe_id,emp.numero_stagiaire,emp.numero_employe,emp.num_emp_soit, emp.num_emp_saoi,
                u.name as 'user_nom', u.email as 'user_email', avct.name as avc_type, avct.description as type_description,
                (select p.id FROM paies p where status='done' and p.date_paie = avc.date_paie and p.employe_id = avc.employe_id limit 1) as 'paie_id'
                FROM avances avc 
                LEFT JOIN employes emp on emp.id = avc.employe_id
                LEFT JOIN sites s on s.idsite = emp.real_site_id
                LEFT JOIN users u on u.id = avc.user_id
                LEFT JOIN type_avances avct on avc.type_avance_id = avct.id
                LEFT JOIN `status` stat on stat.name = avc.status
                WHERE avc.id = ? and (avc.group_id in (?) or (avc.user_id = ? ))",[$id, implode(',', $groupIds), $request->user()->id]
            )[0];
            $pieces = DB::select("SELECT pj.id FROM piece_jointes pj where pj.avance_id = ?", [$id]);
            $avance->nb_pj = count($pieces);
            return response()->json($avance);
        }
    }
    
    public function get_to_done(Request $request){
        if (in_array($request->user()->role, ['resp_rh'])) {   
            $avances = DB::select("SELECT * from avances 
            where (`status` = 'demande' or `status` = 'traite')
            and date_paie = ?
            ",[$request->date_paie]);
            return response(compact('avances'));
        }
        return response(['error' => "EACCES"]);
    }

    public function done_multiple(Request $request){
        if (in_array($request->user()->role, ['resp_rh'])) {
            Avance::whereIn('id', $request->ids_avc)->update(['status' => 'done', 'updated_at' => new \DateTime()]);
            for ($i=0; $i < count($request->ids_avc); $i++) { 
                $dataHistoriques[]=
                [
                    "objet" => "Avance terminée",
                    "note" => "Terminaison groupée",
                    "user" => $request->user()->name . ' < ' . $request->user()->email . '>',
                    "user_id" => $request->user()->id,
                    "avance_id" => $request->ids_avc[$i],
                    "created_at" => new \DateTime(),
                ];
            }
            Historique::insert($dataHistoriques);
            return response(['success' => "Terminaison groupé avec succés"]);
        }
        return response(['error' => "EACCES"]);
    }

    protected function validateAvance($request)
    {
        $auth = $request->user();
        $validator = Validator::make($request->all(), [
            "employe_id" => ["required"],
            "montant" => ["required", "numeric", "min:5000"],
            "type_avance_id" => ["required"],
            "date_paie" => ["required", Rule::unique('avances')->where(function ($query) use ($request) {
                return $query->where('date_paie', $request->date_paie)->where('employe_id', $request->employe_id);
            })]
        ],["date_paie.unique" => "La demande d'avance pour cet agent ce mois de  " .
             (new DateTime($request->date_paie))->format('m/y') . " a déjà été envoyée."
        ]
        )->setAttributeNames(self::$attributeNames);
        if ($validator->fails())
            return ["error" => $validator->errors()->first()];
        
        if (in_array($auth->role, ["superviseur", "resp_sup", "resp_op"])) {
            $currentDate = new \DateTime();
            $dayOfMonth = (int) $currentDate->format('d');
            if ($dayOfMonth < 1 || $dayOfMonth > 13)
                return ["error" => "La demande doit envoyer entre 1er et 13em jour du mois"];
            if (((int) $request->montant) > 40000)
                return ["error" => "Montant plafond 40 000Ar"];
            if ((int) $request->type_avance_id != 1)
                return ["error" => "Choisissez l'avance 15ème"];
        }
        return ['error' => ''];
    }

    public function store(Request $request){
        $auth = $request->user();
        if (in_array($auth->role,["resp_rh", "superviseur", "resp_sup", "resp_op"])) {
            $employe = Employe::find($request->employe_id);
            if(!$employe->sal_forfait){
                $last_pointage = Pointage::where('employe_id', $request->employe_id)
                    ->orderBy('id', 'desc')
                    ->first();
                if ($last_pointage) {
                    $date_pointage = new \DateTime($last_pointage->date_pointage);
                    $now = new DateTime();
                    $one_month_ago = $now->sub(new \DateInterval('P1M'));
                    if ($date_pointage < $one_month_ago) {
                        return ["error" => "Cette agent n'a pas de pointage dans les 30 derniers jours"];
                    }
                }
                else{
                    return ["error" => "Cette agent n'a aucune pointage"];
                }
            }
            $avance = new Avance();
            $validator = $this->validateAvance($request);
            if ($validator['error']) {
                return \response($validator);
            }
            
            $avance->fill($request->all());
            $avance->user_id = $request->user()->id;
            $avance->created_at = new \DateTime();
            $avance->updated_at = new \DateTime();
            $avance->status = 'demande';
            if ($avance->save()) {
                HistoriqueController::new_avance($request, $avance);
                return response()->json(["success" => "Avance bien enregistré"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function getType(Request $request){
        if (in_array($request->user()->role, ["resp_rh", "superviseur", "resp_sup", "resp_op"])) {
            if (in_array($request->user()->role, ["resp_rh"]))
                $type = DB::select("SELECT * FROM type_avances");
            else
                $type = DB::select("SELECT * FROM type_avances where `name` = 'avance15'" );
            return response(compact('type'));
        }
        return response(["error" => "EACCES"]);
    }

    public function do_traite(Request $request, $id){
        $avance = Avance::find($id);
        $old_avance = clone $avance;
        if(in_array($request->user()->role, ["resp_rh"]) && $avance->status == "demande"){
            $avance->type_avance_id = $request->type_avance_id;
            $avance->employe_id = $request->employe_id;
            $avance->montant = $request->montant;
            $avance->motif = $request->motif;
            $validator = $this->validateAvance($request);
            if($validator['error']){
                return \response($validator);
            }
            $avance->status = "traite";
            $avance->update($request->all());
            $avance->updated_at = new \DateTime();
            if ($avance->save()) {
                HistoriqueController:: update_avance($request, $old_avance, "Avance en cours de traitement");
                return response(["success"=> "Avance en cours de traitement", "id"=>$avance->id]);
            }
            return response(["error", "Erreur d'envoi, réessayer"]);
        }
        return response(["error", "EACCES"]);
    }

    public function edit(Request $request, $id){
        $avance = Avance::find($id);
        $old_avance = clone $avance;
        if(in_array($request->user()->role, ["resp_rh"]) || (in_array($request->user()->role, ["superviseur", "resp_sup", "resp_op"]) && $request->user()->id == $avance->user_id)){
            $validator = $this->validateAvance($request);
            if($validator['error']){
                return \response($validator);
            }
            $avance->update($request->all());
            $avance->updated_at = new \DateTime();
            if ($avance->save()) {
                HistoriqueController::update_avance($request, $old_avance, "Modification");
                return response(["success" => "Modification succes","id" =>$avance->id]);
            }
            return response(["error", "Erreur d'envoi, réessayer"]);
        }
        return response(["error", "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $avance = Avance::find($id);
        $old_avance = clone $avance;
        if ($request->user()->id == $avance->user_id && $avance->status == "draft") {
            $validator = $this->validateAvance($request);
            if ($validator['error']) {
                return \response($validator);
            }
            $avance->status = "demande";
            $avance->update($request->all());
            $avance->updated_at = new \DateTime();
            if ($avance->save()) {
                HistoriqueController::update_avance($request, $old_avance, 'Renvoie de demande d\'avance');
                return response(["success" => "Renvoie de demande d'avance reussi", "id" => $avance->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayer"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_avance(Request $request, $id){
        $avance = Avance::find($id);
        if (($request->user()->id == $avance->user_id) && 
            ((in_array($avance->status, ["demande","traite"])&&(in_array($request->user()->role, ["resp_rh"]))) || 
            ($avance->status == "demande" && $request->user()->role == "superviseur"))) 
        {
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails()) {
                return response(['error' => $validator->errors()->first()]);
            }
            $avance->status = 'draft';
            $avance->updated_at = new \DateTime();
            $avance->note_id = HistoriqueController::action_avance($request, "Avance annulé", $id);
            if ($avance->save()) {
                return response(["success" => "Avance annulé", "id" => $avance->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done(Request $request, $id){
        $avance = Avance::find($id);
        if (in_array($request->user()->role, ["resp_rh"]) && $avance->status != "draft") {
            $verifyDatePaie = DB::select("SELECT * FROM paies WHERE date_paie = '$request->date_paie' AND (status = 'traite' or status = 'done') AND employe_id = $avance->employe_id");
            if (count($verifyDatePaie) > 0) 
                return (["error" => "Date de paie invalide"]);
            
            if($request->mensualite){
                $validator = Validator::make($request->all(), [
                    'date_paie' => 'required',
                    'repartitions' => 'required',
                ]);
            }
            else {
                $validator = Validator::make($request->all(), [
                    'date_paie' => 'required',
                ]);
            }
            if ($validator->fails()) 
                return \response()->json(["error" => $validator->errors()->first()]);
            $avance->status = "done";
            $avance->date_paie = $request->date_paie;
            if($request->mensualite){
                $total = $avance->montant;
                $avance->montant = $request->mensualite;
                $avance->group_id = $avance->id;
                $avance->montant_total = $total;
                $repartitions = json_decode($request->repartitions, true);
                foreach ($repartitions as $rep) {
                    $new_avance = new Avance();
                    $new_avance->type_avance_id = $avance->type_avance_id;
                    $new_avance->employe_id = $avance->employe_id;
                    $new_avance->motif = $avance->motif;
                    $new_avance->group_id = $avance->id;
                    $new_avance->date_paie = $rep['date_paie'];
                    $new_avance->montant = $rep['montant'];
                    $new_avance->montant_total = $total;
                    $new_avance->user_id = $request->user()->id;
                    $new_avance->status = 'done';
                    $new_avance->created_at = new \DateTime();
                    $new_avance->updated_at = new \DateTime();
                    $new_avance->save();
                    $paie = DB::select("SELECT * FROM paies WHERE date_paie = '$new_avance->date_paie' AND (status = 'demande') AND employe_id = $avance->employe_id");
                    if (count($paie) > 0) {
                        $new_request = clone $request;
                        $new_request->merge(["date_paie" => $new_avance->date_paie]);
                        // $new_request->employe_id = $new_avance->employe_id;
                        // $new_request->paie_id = $paie[0]->id;
                        // PaieController::RecalculePaie($new_request);
                        $paieController = new PaieController();
                        $recalculPaie = $paieController->GeneratePaie($new_request, $avance->employe_id);
                    }
                    HistoriqueController::new_repartition_avance($request, $new_avance);
                }
            }
            $avance->updated_at = new \DateTime();
            $avance->note_id = HistoriqueController::action_avance($request,"Avance terminé", $id);
            if ($avance->save()) {
                $paie = DB::select("SELECT * FROM paies WHERE date_paie = '$avance->date_paie' AND (status = 'demande') AND employe_id = $avance->employe_id");
                if (count($paie) > 0) {
                    $new_request = clone $request;
                    // $new_request->employe_id = $avance->employe_id;
                    $new_request->merge(["date_paie" => $avance->date_paie]);
                    // $new_request->paie_id = $paie[0]->id;
                    // PaieController::RecalculePaie($new_request);
                    $paieController = new PaieController();
                    $recalculPaie = $paieController->GeneratePaie($new_request, $avance->employe_id);
                    // return $recalculPaie;
                    $recalculPaie = $recalculPaie->getContent();
                    $responseData = json_decode($recalculPaie, true);
                    if (isset($responseData["success"])) {
                        return response(["success" => "Avance terminé avec modification de paie", "id" => $avance->id]);
                    }
                    if (isset($responseData["error"])) {
                        return response(["success" => "Avance terminé mais modification de paie echoué", "id" => $avance->id]);
                    }
                }
                return response(["success" => "Avance terminée", "id" => $avance->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
        
}
