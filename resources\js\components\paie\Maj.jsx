import React from 'react'
import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'

export default function Maj({maj, setMaj}) {
    return (
        <div>
            <DualContainer>
                <InputText label="Heure Férié" value={maj.hFerie} onChange={(value) => setMaj({ ...maj, hFerie: value })} />
                <InputText label="Hm Dim" value={maj.hmDim} onChange={(value) => setMaj({ ...maj, hmDim: value })} />
            </DualContainer>
            <DualContainer>
                <InputText label="H maj" value={maj.hMaj} onChange={(value)=>setMaj({ ...maj, hMaj: value })} />
            </DualContainer>
        </div>
    )
}
