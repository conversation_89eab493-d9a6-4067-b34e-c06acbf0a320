import axios from 'axios'
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useToken from '../util/useToken';

export default function InputTypeEquipement({value, onChange, required, hideInput, closeModal, useLink}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [modalOpen, toggleModal] = useState(false)
    const [typeEquipements, setTypeEquipements] = useState(null)
    
    const handleSelectTypeEquipement = (type) => {
        if(useLink) {
            let params = new URLSearchParams(location.search)
            params.set("type_equipement", type.name)
            navigate(location.pathname + "?" + params)
        }
        toggleModal(false)
        onChange(type)
        if(closeModal) closeModal()
    }

    const handleCloseModal = () => {
        toggleModal(false)
        if(closeModal) closeModal()
    }

    useEffect(() => {
        let isMounted = true
        axios.get('/api/type_equipement', useToken())
        .then((res) => {
            if(isMounted) setTypeEquipements(res.data)
        })
        return () => {isMounted = false}
    }, [])

    return <div>
        {
            !hideInput &&
            <div className='input-container'>
                <label>Type de demande {required && <span className='danger'>*</span>}</label>
                <input
                    type="text"
                    value={value ? value.description : ""}
                    readOnly
                    onClick={() => {toggleModal(true)}}
                    />
            </div>
        }
        {
            (hideInput || modalOpen) &&
            <div className='modal'>
                <div>
                    <h2>Type de demande</h2>
                    {
                        typeEquipements &&
                        <div className='list-container'>
                            {
                                <ul>
                                    {
                                        typeEquipements.map(type => {
                                            return <li key={type.name} onClick={() => handleSelectTypeEquipement(type)}>
                                                {type.description}<br/>
                                            </li>
                                        })
                                    }
                                </ul>
                            }
                        </div>
                    }
                    <div className='form-button-container'>
                        <button onClick={handleCloseModal}>Annuler</button>
                    </div>
                </div>
            </div>
        }
    </div>;
}