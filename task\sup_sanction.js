const moment = require('moment')
const mysql = require('mysql2')

moment.locale('fr')
const auth = require("../auth")

const db_config_admin = auth.db_config_admin
const pool_admin = mysql.createPool(db_config_admin)

const sqlSelectSanction = "SELECT s.id, s.site_id, st.superviseur_id FROM sanctions s "
+ "left join sites st on st.idsite = s.site_id "
+ "where s.user_id = 191 and s.superviseur_id is null and st.superviseur_id is not null "
+ (process.argv[2] == "reverse" ? " order by s.id desc" : "")
const sqlUpdateSupSanction = "UPDATE sanctions set superviseur_id = ? where id = ? "

function updateData(sanctions, index){
    if(index < sanctions.length){
        const sanction = sanctions[index]
        console.log("---\nsanction_id: " + sanction.id + "\nsuperviseur_id: " + sanction.superviseur_id)
        pool_admin.query(sqlUpdateSupSanction, [sanction.superviseur_id, sanction.id], async (err, res) => {
            if(err){
                console.log("err update sanction")
                console.error(err)
                waitBeforeUpdate(sanctions, index)
            }
            else
                waitBeforeUpdate(sanctions, index+1)
        })
    }
    else {
        console.log("all sanctions done !")
        process.exit(0)
    }
}

function waitBeforeUpdate(sanctions, index) {
    setTimeout(() => {
        updateData(sanctions, index)
    }, 100)
}

pool_admin.query(sqlSelectSanction, [], async (err, sanctions) => {
    if(err){
        console.log("err select site")
        console.error(err)
        process.exit(0)
    }
    else {
        if(sanctions.length > 0){
            console.log("sanction superviseur to update : " + sanctions.length)
            updateData(sanctions, 0)
        }
        else {
            console.log("no sanction to update...")
            process.exit(0)
        }
    }
})