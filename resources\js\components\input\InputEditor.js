
import { Editor } from "@tinymce/tinymce-react";

export default function InputEditor({content, setContent}) {

    return <Editor
        apiKey="qwvak9yr74y0oseq024ox1imnjjihzey9ba6071fel9tx62q"
        onEditorChange={setContent}
        value = {content}
        init={{
        height: 500,
        menubar: false,
        plugins: [
            "advlist",
            "autolink",
            "lists",
            "link",
            "image",
            "charmap",
            "preview",
            "anchor",
            "searchreplace",
            "visualblocks",
            "code",
            "fullscreen",
            "insertdatetime",
            "media",
            "table",
            "code",
            "help",
            "wordcount",
        ],
        toolbar:
            "undo redo | blocks | " +
            "bold italic underline forecolor | alignleft aligncenter " +
            "alignright alignjustify | bullist numlist outdent indent | " +
            "removeformat | help | " +
            "table tableinsertrowafter tabledeleterow tabledelete ",
        }}
    />
}