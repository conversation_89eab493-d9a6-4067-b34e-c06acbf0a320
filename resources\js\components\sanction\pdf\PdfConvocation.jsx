import {useEffect,useState} from 'react'
import {Page,Text,Image,View,Document,StyleSheet} from '@react-pdf/renderer'
import Logo1 from './dirickx_madagascar.png'
import Logo2 from './dirickx_guard.jpg'
import moment from 'moment'
import matricule from '../../util/matricule'

const getMatricule = matricule;

export default function PdfConvocation({sanction,motif,dateConvocation,genre,societeSt}) {
    const [societe, setSociete] = useState("")
    const [rcs, setRcs] = useState("")
    const [stat, setStat] = useState("")
    const [adresse, setAdresse] = useState("")
    const [code, setCode] = useState("")
    const [Logo, setLogo] = useState("")
    const [fonction, setFonction] = useState("Agent de sécurité")
    const [matricule, setMatricule] = useState("")

    useEffect(() => {
        if(sanction.societe_id == 2){
            setSociete('« Sûreté Océan Indien Tamatave Sarl »')
            setRcs('Toamasina 20 11B00100 –')
            setStat('47597  31 2011 0 00658 – NIF 3000538020')
            setAdresse('Villa Herihasina I Derrière cite VohitsaraBetainomby Toamasina –')
            setCode('Tamatave 501 –')
            setLogo(Logo1)
        }else if(societeSt == "SOIT"){
            setSociete('« Sûreté Océan Indien Tamatave Sarl »')
            setRcs('Toamasina 20 11B00100 –')
            setStat('47597  31 2011 0 00658 – NIF 3000538020')
            setAdresse('Villa Herihasina I Derrière cite VohitsaraBetainomby Toamasina –')
            setCode('Tamatave 501 –')
            setLogo(Logo1)
        }else{
            setSociete('DIRICKX GUARD SARL')
            setRcs('Antananarivo 2015 B 00494')
            setStat('80105 11 2015 0 10514 NIF : 4001991253')
            setAdresse('Adresse : Lot 23 Talatamaty')
            setLogo(Logo2)
        }

        setMatricule(getMatricule(sanction))

        switch(sanction.fonction_id) {
            case 1:
                return setFonction("Agent de sécurité");
            case 2:
                return setFonction("Chef de poste");
            case 3:
                return setFonction("Conducteur de chien");
            case 4:
                return setFonction("Superviseur de site");
            case 5:
                return setFonction("Intervention");
            case 6:
                return setFonction("Motard");
            case 7:
                return setFonction("Contrôleur");
            case 8:
                return setFonction("Chauffeur");
            case 9:
                return setFonction("Femme de ménage");
            default:
                return setFonction("");
        }

    }, [])

    const styles = StyleSheet.create({
        body: {
            paddingTop: 35,
            paddingBottom: 65,
            paddingHorizontal: 60,
        },
        image: {
            margin: 15,
            width: '45%' 
        },
        header: {
            marginHorizontal: 15,
            marginVertical: 0,
            fontSize: 11,
            fontWeight: 700,
            fontFamily: 'Times-BoldItalic',
        },
        date: {
            textTransform: 'capitalize',
            fontFamily: 'Times-Roman',
            textAlign: 'center',
            fontSize: 12,
            paddingVertical: 3,
        },
        span : {
            textTransform: 'none',
            fontFamily: 'Times-Roman',
            fontSize: 12,
        },
        employe: {
            fontFamily: 'Times-Roman',
            textAlign: 'center',
            fontSize: 12,
            paddingVertical: 3,
        },
        underline: {
            textDecoration: 'underline',
            textDecorationThickness: 1,
            fontFamily: 'Times-Bold',
            fontSize: 12,
            textTransform: 'uppercase',
        },
        bold: {
            fontFamily: 'Times-Bold',
            fontSize: 12,
        },
        objet: {
            fontFamily: 'Times-Bold',
            textAlign: 'left',
            fontSize: 12,
            paddingTop: 0,
            paddingBottom: 8,
            marginHorizontal: 15,
        },
        text: {
            marginHorizontal: 15,
            marginVertical: 3,
            lineHeight: 1.5,
            fontSize: 12,
            textAlign: 'justify',
            fontFamily: 'Times-Roman',
        },
        container: {
            paddingHorizontal: 30,
            paddingVertical: 15,
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between'
        },
        left:{
            fontFamily: 'Times-Roman',
            fontSize: 12,
            textDecoration: 'underline',
            textDecorationThickness: 1,
        },
        right: {
            fontFamily: 'Times-Roman',
            fontSize: 12,
            textDecoration: 'underline',
            textDecorationThickness: 1,
        }
    })

    return (
        <Document>
            <Page size='A4' style={styles.body}>
                <Image style={styles.image} src={Logo} />
                <Text style={styles.header}>{societe}</Text>
                <Text style={styles.header}>RCS {rcs}</Text>
                <Text style={styles.header}>STAT {stat}</Text>
                <Text style={styles.header}>{adresse}</Text>
                <Text style={styles.header}>{code}</Text>     

                
                <Text style={styles.employe}>{genre}</Text>
                <Text style={styles.employe}>{sanction.employe}</Text>
                <Text style={styles.employe}>{fonction}</Text>
                <Text style={styles.employe}>Matricule {matricule}</Text>
                <Text style={styles.date}>Antananarivo, le {moment().format('DD MMMM YYYY')}</Text>

                <Text style={styles.text}>Convocation à un entretien préalable en vue d’un éventuel licenciement pour faute lourde.</Text>
                <Text style={styles.objet}><Text style={styles.underline}>OBJET</Text> : Engagement de la procédure prévue par l’article 42 du Code de Travail</Text>
                
                <Text style={styles.text}>
                    En application de l’article 42 de la Loi n° 2024-014 du 14 Aout 2024, 
                    portant Code de Travail disposant que : « l’employeur envisageant 
                    de licencier un travailleur doit respecter le droit à la défense 
                    de celui-ci », nous vous prions de vous présenter à notre Bureau 
                    le <Text style={styles.bold}>{moment(dateConvocation).format('DD MMMM YYYY')} à {moment(dateConvocation).format('HH')}h{moment(dateConvocation).format('mm') + ' '}</Text>  
                    afin d’avoir avec vous un entretien relatif à ce propos. 
                    Vous pouvez à cette occasion vous faire assister par une personne de votre choix.
                </Text>
                <Text style={styles.text}>
                    Le motif qui contraint la Société {societe} à envisager une mesure 
                    de licenciement pour faute lourde à votre égard est :
                </Text>
                <Text style={styles.text}>
                    {motif}
                </Text>
                <Text style={styles.text}>
                    Dans l’attente de l’éventuelle décision de licenciement qui pourrait être prise 
                    à l’issue de cet entretien, et afin de préserver les intérêts de nos clients et 
                    ceux de l’entreprise, nous vous prions de ne pas vous présenter à votre poste de travail 
                    dès réception de la présente. 
                </Text>
                <View style={styles.container}>
                    <Text style={styles.left}>{sanction.societe_id == 2 || sanction.societe_id == 1 ? 'L\'employé' : 'Le Stagiaire'}</Text>
                    <Text style={styles.right}>La Direction</Text>
                </View>
                    
            </Page>
        </Document>
    )
}
