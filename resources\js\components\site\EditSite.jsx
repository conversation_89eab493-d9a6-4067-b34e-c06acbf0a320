import React, { useEffect, useState } from 'react';
import { useLocation, useParams } from "react-router-dom";
import Notification from '../notification/Notification'
import axios from 'axios';
import useToken from '../util/useToken';
import InputText from '../input/InputText';
import InputUser from '../input/InputUser';
import InputSite from '../input/InputSite';
import InputCheckBox from '../input/InputCheckBox';
import InputSelect from '../input/InputSelect';

export default function EditSite({ auth, title }) {
    const [site, setSite] = useState({ site: '' })
    const [error, setError] = useState("")
    const [respSup, setRespSup] = useState(null)
    const [superviseur, setSuperviseur] = useState(null)
    const [notification, setNotification] = useState(null)
    const [parentSite, setParentSite] = useState({})
    const [regroupPlanning, setRegroupPlanning] = useState(false)
    const [childSites, setChildSites] = useState([])
    const [desableSubmit, setDesableSubmit] = useState(false)
    const [isParent, setIsParent] = useState(false)
    const [heureContrat, setHeureContrat] = useState()
    const hc = [240,280,312];
    const queryParams = new URLSearchParams(useLocation().search);
    const lastParams = queryParams.get('last_params') ? decodeURIComponent(queryParams.get('last_params')) : null;
    const { id } = useParams()

    const getSite = () => {
        axios.get("/api/site/show/" + id, useToken())
            .then(res => {
                setSite(res.data)
                if (res.data.parent_site_id) {
                    setParentSite({
                        adresse: res.data.parent_adresse,
                        id: res.data.parent_site_id,
                        nom: res.data.parent_site_name,
                        pointeuse: res.data.parent_pointeuse,
                        pointage: res.data.parent_pointage
                    })
                    if(res.data.childs){
                        setChildSites(res.data.childs)
                    }
                    if(res.data.parent_site_id != id){
                        setRegroupPlanning(true)
                    }
                    else{
                        setIsParent(true)
                        setRegroupPlanning(false)
                    }
                }
                setHeureContrat(res.data.heure_contrat)
                console.log(res.data.heure_contrat)
                if (res.data.resp_sup_id) setRespSup({
                    id: res.data.resp_sup_id,
                    name: res.data.resp_sup_name,
                    email: res.data.resp_sup_email
                })
                if (res.data.superviseur_id) setSuperviseur({
                    id: res.data.superviseur_id,
                    name: res.data.user,
                    email: res.data.email
                })
            }).catch(e => {
                console.error(e)
            })
    }

    useEffect(() => {
        getSite()
    }, [])

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        if(!regroupPlanning && !superviseur?.id){
            setError("Vous devez sélectionner un superviseur.")
        }
        else if (!regroupPlanning && !respSup?.id){
            setError("Vous devez sélectionner un manager.")
        }
        else {
            setDesableSubmit(true)
            let data = new FormData()
            if(!regroupPlanning){
                data.append("superviseur_id", superviseur.id)
                data.append("resp_sup_id", respSup.id)
            }
            else
                data.append("regroup_planning", 1)
            if (regroupPlanning && parentSite.id) {
                data.append("parent_site_id", parentSite.id)
            }
            axios.post('/api/site/edit/' + id, data, useToken())
                .then(res => {
                    if (res.data.success)
                        setNotification(res.data)
                    else if (res.data.error == "EACCES")
                        setError("Vous n'avez pas la permission de modifier ce site.")
                    else if (res.data.error)
                        setError(res.data.error)
                    setDesableSubmit(false)
                })
                .catch(e => {
                    console.error(e)
                    setDesableSubmit(false)
                    setError("Erreur d'envoie, réessayez.")
                })
        }
    }

    return (
        <div id='content'>
            {notification ?
                <Notification next={lastParams ? lastParams : notification.id ? "/site?id=" + notification.id : "/site"}
                    message={notification.success}
                />
                :
                <div>
                    <div className='title-container'>
                        <h2>{title}</h2>
                    </div>
                    <InputText label="Nom" value={site?.nom ?? ''} disabled />
                    {["rh", "resp_rh"].includes(auth.role) &&
                        <InputSelect label="Heure contrat" setSelected={setHeureContrat} selected={heureContrat} options={hc}/>
                    }
                    { (isParent || !regroupPlanning ) &&
                        <>
                            <InputUser label='Superviseur' value={superviseur} onChange={setSuperviseur} required roles={['superviseur', 'resp_sup', 'resp_op']} />
                            <InputUser label='Manager' value={respSup} onChange={setRespSup} required roles={['resp_sup', 'resp_op']} />
                        </>
                    }
                    {
                        parentSite?.id != id ?
                            <div style={{marginTop: 20}}>
                                <InputCheckBox name="Grouper" label="Grouper le planning de ce site sur" checked={regroupPlanning} onChange={setRegroupPlanning} />
                                {regroupPlanning && <InputSite label="Site de regroupement" value={parentSite} onChange={setParentSite} actionUrl={'/api/site/search_have_pointeuse/'+id} />}
                            </div>
                            :
                            <>
                                <div style={{marginTop: 20}}>
                                    <label>Site de regroupement</label>
                                    <div className="card-container">
                                        {childSites.map((ch) => 
                                            <div className="space-between">
                                                <span className='secondary' style={{marginTop: 15}} key={ch.id}>{ch.nom}</span>
                                            </div>
                                            )
                                        }
                                    </div>
                                </div>
                            </>
                    }
                    {
                        error &&
                        <div className='container-error'>
                            {error}
                        </div>
                    }
                    <div className="input-container-btn">
                        <button className="primary" onClick={handleSubmit} disabled={desableSubmit} >Envoyer</button>
                    </div>
                </div>
            }
        </div>
    )
}
