<?php

/**
 * Integration Example: How to call the reclamation notification script
 * from Laravel when a reclamation is created
 * 
 * Add this code to your ReclamationController.php in the store() method
 * after the reclamation is successfully saved.
 */

// Example 1: Simple background execution
if($reclamation->save()) {
    HistoriqueController::new_reclamation($request, $reclamation);
    
    // Send email notification in background
    $scriptPath = base_path('task/send_reclamation_notification.js');
    $command = "node {$scriptPath} {$reclamation->id}";
    exec($command . " > /dev/null 2>&1 &");
    
    return response(["success" => "Réclamation enregistré", "id" => $reclamation->id]);
}

// Example 2: With error logging
if($reclamation->save()) {
    HistoriqueController::new_reclamation($request, $reclamation);
    
    // Send email notification with logging
    $scriptPath = base_path('task/send_reclamation_notification.js');
    $logPath = storage_path('logs/reclamation_notifications.log');
    $command = "node {$scriptPath} {$reclamation->id} >> {$logPath} 2>&1 &";
    exec($command);
    
    return response(["success" => "Réclamation enregistré", "id" => $reclamation->id]);
}

// Example 3: Using Laravel Jobs (recommended for production)
if($reclamation->save()) {
    HistoriqueController::new_reclamation($request, $reclamation);
    
    // Dispatch job to send notification
    dispatch(new SendReclamationNotificationJob($reclamation->id));
    
    return response(["success" => "Réclamation enregistré", "id" => $reclamation->id]);
}

/**
 * Laravel Job Class Example
 * Create this file: app/Jobs/SendReclamationNotificationJob.php
 */
/*
<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendReclamationNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reclamationId;

    public function __construct($reclamationId)
    {
        $this->reclamationId = $reclamationId;
    }

    public function handle()
    {
        try {
            $scriptPath = base_path('task/send_reclamation_notification.js');
            $command = "node {$scriptPath} {$this->reclamationId}";
            
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0) {
                Log::info("Reclamation notification sent successfully", [
                    'reclamation_id' => $this->reclamationId,
                    'output' => implode("\n", $output)
                ]);
            } else {
                Log::error("Failed to send reclamation notification", [
                    'reclamation_id' => $this->reclamationId,
                    'return_code' => $returnCode,
                    'output' => implode("\n", $output)
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Exception while sending reclamation notification", [
                'reclamation_id' => $this->reclamationId,
                'error' => $e->getMessage()
            ]);
        }
    }
}
*/

// Example 4: Synchronous execution with response
if($reclamation->save()) {
    HistoriqueController::new_reclamation($request, $reclamation);
    
    try {
        $scriptPath = base_path('task/send_reclamation_notification.js');
        $command = "node {$scriptPath} {$reclamation->id}";
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0) {
            return response([
                "success" => "Réclamation enregistré et notifications envoyées", 
                "id" => $reclamation->id
            ]);
        } else {
            // Log error but don't fail the reclamation creation
            Log::error("Failed to send reclamation notifications", [
                'reclamation_id' => $reclamation->id,
                'output' => implode("\n", $output)
            ]);
            
            return response([
                "success" => "Réclamation enregistré (notifications en cours)", 
                "id" => $reclamation->id
            ]);
        }
    } catch (\Exception $e) {
        // Log error but don't fail the reclamation creation
        Log::error("Exception while sending reclamation notifications", [
            'reclamation_id' => $reclamation->id,
            'error' => $e->getMessage()
        ]);
        
        return response([
            "success" => "Réclamation enregistré", 
            "id" => $reclamation->id
        ]);
    }
}

/**
 * Alternative: Create a dedicated method in ReclamationController
 */
/*
public function sendNotifications($reclamationId)
{
    try {
        $scriptPath = base_path('task/send_reclamation_notification.js');
        $command = "node {$scriptPath} {$reclamationId}";
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        return response([
            'success' => $returnCode === 0,
            'output' => implode("\n", $output),
            'reclamation_id' => $reclamationId
        ]);
    } catch (\Exception $e) {
        return response([
            'success' => false,
            'error' => $e->getMessage(),
            'reclamation_id' => $reclamationId
        ], 500);
    }
}
*/

?>
