import React, { Fragment } from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";

const styles = StyleSheet.create({
    row: {
        flexDirection: "row",
        // borderBottomColor: "#bff0fd",
        // borderBottomWidth: 1,

        alignItems: "center",
        // height: 23,
        fontSize: 7,
        lineHeight: 1,
        // fontStyle: "bold",
    },
    rowWithBorder: {
        flexDirection: "row",
        // borderBottomColor: "#bff0fd",
        borderBottomWidth: 1,
        borderTopWidth: 1,

        alignItems: "center",
        // height: 23,
        fontSize: 7,
        lineHeight: 1,

        // fontStyle: "bold",
    },
    leftRow: {
        flexDirection: "row",
        // borderBottomColor: "#bff0fd",
        borderBottomWidth: 1,

        alignItems: "center",
        // height: 23,
        fontSize: 7,
        lineHeight: 1,

        // fontStyle: "bold",
    },
    // border: {
    //     borderBottomWidth: 1,
    // },

    rubrique: {
        width: "29%",
        textAlign: "left",
        borderRightWidth: 1,
        paddingTop: 6,
        paddingBottom: 6,
        marginLeft: 2,
    },
    base: {
        width: "12%",
        borderRightWidth: 1,
        borderRightColor: "black",

        textAlign: "left",
        padding: 6,
    },
    taux: {
        width: "10%",
        borderRightWidth: 1,
        textAlign: "left",
        padding: 6,
    },
    deduire: {
        width: "12%",
        borderRightWidth: 1,
        textAlign: "left",
        padding: 6,
    },
    apayer: {
        width: "15%",
        borderRightWidth: 1,
        textAlign: "left",
        padding: 6,
    },
    chargesPatronale: {
        width: "22%",
        textAlign: "left",
        // padding: 6,
    },
    charges: {
        flexDirection: "row",
    },
    patronaleTaux: {
        width: "50%",
        borderRightWidth: 1,
        padding: 6,
    },
    patronaleMontant: {
        width: "45%",
        padding: 6,
    },
});

const TableRow = ({ items }) => {
    const rows = (
        <View>{items && <View>


            <View style={styles.row}>
                <Text style={styles.rubrique}>
                    Salaire de base et majoration
                </Text>
                <Text style={styles.base}>{items.sal_base}</Text>
                <Text style={styles.taux}> {" "}</Text>
                <Text style={styles.deduire}>{" "}</Text>
                <Text style={styles.apayer}>{" "}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Nombre de jour travaillé</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "} </Text>
                <Text style={styles.deduire}>{" "}</Text>
                <Text style={styles.apayer}>240000</Text><View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={[styles.rubrique, { borderBottomWidth: 1, borderTopWidth: 1 }]}>Total brut</Text>
                <Text style={[styles.base, { borderBottomWidth: 1, borderTopWidth: 1 }]}>{" "}</Text>
                <Text style={[styles.taux, { borderBottomWidth: 1, borderTopWidth: 1 }]}>{" "}</Text>
                <Text style={[styles.deduire, { borderBottomWidth: 1, borderTopWidth: 1 }]}>{" "}</Text>
                <Text style={[styles.apayer, { borderBottomWidth: 1, borderTopWidth: 1 }]}>{items.salaire_brut}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Retenue CNAPS</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>1,00%</Text>
                <Text style={styles.deduire}> {items.cnaps}</Text>
                <Text style={styles.apayer}>{" "}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>13,00%</Text>
                        <Text style={styles.patronaleMontant}>{items.cnaps_pat}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Retenue SALFA</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>1,00%</Text>
                <Text style={styles.deduire}> {items.salfa}</Text>
                <Text style={styles.apayer}>{" "}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{"5,00%"}</Text>
                        <Text style={styles.patronaleMontant}>{items.salfa_pat}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Net imposable</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "}</Text>
                <Text style={styles.deduire}>{" "}</Text>
                <Text style={styles.apayer}>{items.net_imposable}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Retenue IRSA</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>20,00%</Text>
                <Text style={styles.deduire}>{" "}</Text>
                <Text style={styles.apayer}>{items.irsa}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Perdiem</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "}</Text>
                <Text style={styles.deduire}>{" "}</Text>
                <Text style={styles.apayer}>{items.perdiem}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Prime</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "}</Text>
                <Text style={styles.deduire}>{" "}</Text>
                <Text style={styles.apayer}>{items.prime}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Prime d’ancienneté</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "}</Text>
                <Text style={styles.deduire}>{" "}</Text>
                <Text style={styles.apayer}> {items.prime_anc}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Rappel</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "}</Text>
                <Text style={styles.deduire}>{" "}</Text>
                <Text style={styles.apayer}>{items.rappel}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Parts variable</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "}</Text>
                <Text style={styles.deduire}>{" "}</Text>
                <Text style={styles.apayer}>{items.part_variable}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Autre déduction</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "}</Text>
                <Text style={styles.deduire}>{items.autre_deduction}</Text>
                <Text style={styles.apayer}>{" "}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Avance sur salaire</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "}</Text>
                <Text style={styles.deduire}>{items.avance_15e}</Text>
                <Text style={styles.apayer}>{" "}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.row}>
                <Text style={styles.rubrique}>Avance special</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "}</Text>
                <Text style={styles.deduire}>{items.avance_special}</Text>
                <Text style={styles.apayer}>{" "}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
            <View style={styles.leftRow}>
                <Text style={styles.rubrique}>Allfamcnaps</Text>
                <Text style={styles.base}>{" "}</Text>
                <Text style={styles.taux}>{" "}</Text>
                <Text style={styles.deduire}>{" "}</Text>
                <Text style={styles.apayer}>{items.all_fam_cnaps}</Text>
                <View style={styles.chargesPatronale}>
                    <View style={styles.charges}>
                        <Text style={styles.patronaleTaux}>{" "}</Text>
                        <Text style={styles.patronaleMontant}>{" "}</Text>
                    </View>
                </View>
            </View>
        </View>}
        </View>
    );
    return <Fragment>{rows}</Fragment>;
};

export default TableRow;
