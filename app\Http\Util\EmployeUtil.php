<?php

namespace App\Http\Util;

use App\Models\Employe;

class EmployeUtil
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }

    public static function getEmployeById($id){
        $employe = Employe::select('societe_id', 'numero_stagiaire', 'numero_employe', 'num_emp_soit', 'num_emp_saoi', 'nom')
            ->find($id);
        if($employe->societe_id == 1)
            $matricule = 'DGM-' . $employe->numero_employe;
        else if($employe->societe_id == 2)
            $matricule = 'SOIT-' . $employe->num_emp_soit;
        else if($employe->societe_id == 3)
            $matricule = 'ST-' . $employe->numero_stagiaire;
        else if($employe->societe_id == 4)
            $matricule = 'SM';
        else if ($employe->societe_id == 5)
            $matricule = 'TMP';
        else if($employe->societe_id == 6)
            $matricule = 'SAOI-' . $employe->num_emp_saoi;
        else
            $matricule = 'Ndf';
        return $matricule . ' ' . $employe->nom;
    }

    public static function getEmploye($employe){
        if($employe->societe_id == 1)
            $matricule = 'DGM-' . $employe->numero_employe;
        else if($employe->societe_id == 2)
            $matricule = 'SOIT-' . $employe->num_emp_soit;
        else if($employe->societe_id == 3)
            $matricule = 'ST-' . $employe->numero_stagiaire;
        else if($employe->societe_id == 4)
            $matricule = 'SM';
        else if ($employe->societe_id == 5)
            $matricule = 'TMP';
        else if ($employe->societe_id == 6) {
            $matricule = 'SAOI-'. $employe->num_emp_saoi;
        }
        else
            $matricule = 'Ndf';
        return $matricule . ' ' . (isset($employe->nom) ? $employe->nom : $employe->employe);
    }
}