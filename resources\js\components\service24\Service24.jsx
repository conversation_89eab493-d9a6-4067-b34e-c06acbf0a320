import React, { useEffect, useState } from 'react'
import LoadingPage from '../loading/LoadingPage'
import { Link, useLocation } from 'react-router-dom'
import SearchBar from '../input/SearchBar'
import InfiniteScroll from 'react-infinite-scroll-component'
import useToken from '../util/useToken'
import matricule from '../util/matricule';
import StatusLabel from '../input/StatusLabel'
import './service24.css'
import moment from 'moment';
import ConfirmMultiple from './ConfirmMultiple'
moment.locale('fr')

export default function Service24({ auth, currentId, setCurrentId, services, setServices }) {
    const locationSearch = useLocation().search;
    const [allDataLoaded, setDataLoaded] = useState(false);
    const [isLoading, toggleLoading] = useState(false)
    const [confirmMultiple, toggleConfirmMultiple] = useState(false)
    const searchItems = [
        { label: "Employé", name: "employe_id", type: "number" },
        { label: "Référence", name: "id", type: "number" },
        { label: "Date de création", name: "created_at", type: "date" },
        { label: "Status", name: "status", type: "string" },
        { label: "Site", name: "site_id", type: "number" },
    ]
    if (auth.role != 'superviseur')
        searchItems.push(
            { label: 'Demandeur', name: 'user_id', type: 'number' },
        )

    const updateData= (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true);
            setDataLoaded(true);
            setCurrentId(null);
            params.set("offset", 0);
        } else params.set("offset", services.length);

        axios.get('/api/service24?' + params, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.error)
                        console.error(res.data.error);
                    else {
                        if (initial)
                            setServices(res.data.services);
                        else {
                            const list = services.slice().concat(res.data.services);
                            setServices(list);
                        }
                        setDataLoaded(res.data.services.length < 30);
                    }
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e);
            })
        return () => {
            isMounted = false;
        }
    }

    useEffect(() => {
        let isMounted = true;
        if (isMounted) updateData(true);
        return () => {
            isMounted = false;
        };
    }, [locationSearch]);
    
    const fetchMoreData = () => {
        setTimeout(() => {
            updateData();
        }, 300);
    };

    return (
        <div>
            {
                isLoading ? <LoadingPage />
            : 
                <div>    
                    <div className='padding-container space-between'>
                        <h2>
                            Service 24    
                        </h2>
                        {
                            ['superviseur', 'resp_sup', 'resp_op'].includes(auth.role) &&
                            <Link className='btn btn-primary' to='/service24/add'>Nouveau</Link>    
                        }    
                    </div>
                    <SearchBar listItems={searchItems} />
                    {
                        confirmMultiple &&
                        <ConfirmMultiple closeModal={() => toggleConfirmMultiple(false)} updateData={updateData} />
                    }
                    {
                         ['validateur'].includes(auth.role) &&
                        <div className='header-action action-container'>
                            <span onClick={() => toggleConfirmMultiple(true)}>Confirmer par lot</span>
                        </div>
                    }
                    {
                        confirmMultiple &&
                        <ConfirmMultiple closeModal={() => toggleConfirmMultiple(false)} updateData={updateData} />
                    }
                    {
                        (services && services.length == 0) ?
                            <h3 className="center secondary">Aucun données trouvé</h3>
                        :   
                            <InfiniteScroll dataLength={services.length}
                                next={fetchMoreData}
                                hasMore={!allDataLoaded}
                                loader={<LoadingPage />}
                            >
                                <div className='line-container'>
                                    <div className="row-list">
                                        <b className='line-cell-md'>Agent</b>
                                        <b className="status-line"><StatusLabel color="grey" /></b>
                                        <b className="date-service">Date</b>
                                        {/* <b className="date-service">Durée</b> */}
                                        <b className="motif">Motif</b>
                                    </div>
                                </div>
                                {services.map((sc) => (
                                        <div key={sc.id}
                                            className={`line-container ${currentId && currentId == sc.id ? 'selected' : ""}`}
                                            onClick={()=>setCurrentId(sc.id)}
                                        >
                                            <div>
                                                <div className="row-service">
                                                    <span className="line-cell-md">{matricule(sc)} {sc.employe}</span>
                                                    <span className="status-line"><StatusLabel color={sc.status_color} /></span>
                                                    <span className="date-service">{sc.date_pointage ? moment(sc.date_pointage).format("DD/MM/YY") + (moment(sc.date_pointage).format("HH:mm:ss") == "18:00:00" ? " NUIT" : " JOUR") : ""}</span>
                                                    {/* <span className="date-service">{moment(sc.begin_pointage).format("DD/MM/YY") + (moment(sc.begin_pointage).format("HH:mm:ss") == "06:00:00" ? " JOUR" : " NUIT")}</span> */}
                                                    {/* <span className="date-service">{moment(sc.end_pointage).diff(moment(sc.begin_pointage), 'hours') / 24}</span> */}
                                                    <span className="motif">{sc.motif}</span>
                                                </div>
                                            </div>
                                        </div>
                                ))       
                                }
                            </InfiniteScroll>
                    }    
                </div>        
            }
        </div>
    )
}
