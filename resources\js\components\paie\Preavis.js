import React from 'react'
import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'

export default function Preavis({ preavis, setPreavis, nprv, setNprv}) {
    return (
        <div>
            <DualContainer>
                <InputText label="Nprv Deductible"
                    value={nprv.nprvPreavisDeductible}
                    onChange={(newPrDeduct) => setNprv({ ...nprv, nprvPreavisDeductible: newPrDeduct })}
                />
                <InputText label="Deductible" value={preavis.preavisDeductible} disabled />
            </DualContainer>
            <DualContainer>
                <InputText label="Nprv Payer"
                    value={nprv.nprvPreavisPayer}
                    onChange={(newPrPayer) => setNprv({ ...nprv, nprvPreavisPayer: newPrPayer })}
                />
                <InputText label="Payer" value={preavis.preavisPayer} disabled />
            </DualContainer>
            <DualContainer>
                <InputText label="Nprv Licenciement"
                    value={nprv.nprvLicenciement}
                    onChange={(newNprvLicenciement) => setNprv({ ...nprv, nprvLicenciement: newNprvLicenciement })}
                />
                <InputText label="Idm Licenciement" value={preavis.idmLicenciement} disabled />
            </DualContainer>
            <DualContainer>
                <InputText label="Preavis moins"
                    value={preavis.preavisMoins}
                    onChange={(newPrMoins) => setPreavis({ ...preavis, preavisMoins: newPrMoins })}
                />
            </DualContainer>
        </div>
    )
}
