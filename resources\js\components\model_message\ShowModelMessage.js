import React, { useEffect, useRef, useState } from 'react'
import ShowHeader from '../view/ShowHeader'
import parse from 'html-react-parser';
import useToken from '../util/useToken';
import LoadingPage from '../loading/LoadingPage';
import useClickOutside from '../util/useClickOutside';
import ActionModelMessage from './ActionModelMessage';

export default function ShowModelMessage({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const [isLoading, toggleLoading] = useState(false)
    const [model, setModel] = useState({})
    const [copies, setCopies] = useState([])
    const [users, setUsers] = useState([])
    const [showAllReceveirs, toggleAllReceveirs] = useState(false)
    const [showAllCc, toggleAllCc] = useState(false)

    const receiverRef = useRef(null)
    const ccRef = useRef(null)

    useClickOutside(receiverRef, () => {
        toggleAllReceveirs(false)
    })
    useClickOutside(ccRef, () => {
        toggleAllCc(false)
    })

    const updateData = () => { 
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/model_message/' + currentId, useToken())
        .then((res) => {
            if (isMounted) {
                if (res.data.model) {
                    setModel(res.data.model)
                }
                const toUsers = []
                const copyUsers = []
                let userTmp = res.data.model.users
                if (userTmp.length > 0) {
                    userTmp.forEach(ur => {
                        const existingInUsers = toUsers.find(dest => dest.address === ur.user_email);
                        const existingInCopies = copyUsers.find(cp => cp.address === ur.user_email);
                        if (!existingInUsers && !existingInCopies) {
                            if (ur.follow) {
                                toUsers.push({ id: ur.user_id, address: ur.user_email, name: ur.user_name })
                            }
                            else
                                copyUsers.push({ id: ur.user_id, address: ur.user_email, name: ur.user_name })
                        }
                    });
                }
                setUsers(toUsers)
                setCopies(copyUsers)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => isMounted = false;
        
    }

    useEffect(() => {
        updateData()
    }, [currentId])
    
    return (
        <div>
            {
                isLoading ?
                    <LoadingPage />
                :
                <div>
                    {
                        (model) &&
                        <>
                            <ShowHeader size={size} label="Model de courrier" id={model.id} closeDetail={() => setCurrentId()} />
                            <div className='card-container'>
                                <h3><span>{model.type ? model.type : model.objet}</span></h3>
                                {
                                    model.access && 
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Type: <span className='text'>
                                            {
                                                model.access == 'all' ? 'Pour tout le monde'
                                                :model.access == 'me' ? 'Pour moi seul'
                                                :'Pour le même service'
                                            }
                                        </span>
                                    </p>
                                }
                                {model.objet &&
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Objet : <span className="text nowrap">
                                            {model.objet}
                                        </span>
                                    </p>
                                }
                                {model.content &&
                                    <div className='display-flex text nowrap'>
                                        Contenu : <span className="text">
                                            <div className='message-content'>
                                                {model.content && parse(model.content)}
                                            </div>
                                        </span>
                                    </div>
                                }
                                {users?.length > 0 &&
                                    <div className='display-flex text nowrap'>
                                        Destinataires : <span className="text nowrap">
                                            <div>
                                                { users.lenght == 1 ?
                                                    <div>test   </div>
                                                    :
                                                    showAllReceveirs ?
                                                        <div ref={receiverRef} className='text-wrap'>
                                                            {(users.map(u => u.name).join(", "))}
                                                        </div>
                                                        :
                                                        users.slice(0, 5).map(u => u.name).join(", ")
                                                }
                                                {
                                                    users.length > 5 &&
                                                    <span className='action-container'>
                                                        <span className={!showAllReceveirs && "margin-left-10"} onClick={() => toggleAllReceveirs(true)}>{showAllReceveirs ? null : (" +" + (users.length - 5))}</span>
                                                    </span>
                                                }
                                            </div>
                                        </span>
                                    </div>
                                }
                                { copies?.length > 0 &&
                                    <div className='display-flex text nowrap'>
                                        Utilisateur en copie : <span className="text nowrap">
                                            <div>
                                                {showAllCc ?
                                                    <div ref={ccRef} className='text-wrap'>
                                                        {(copies.map(u => u.name).join(", "))}
                                                    </div>
                                                    :
                                                    copies.slice(0, 5).map(u => u.name).join(", ")
                                                }
                                                {
                                                    copies.length > 5 &&
                                                    <span className='action-container'>
                                                        <span className={!showAllCc && "margin-left-10"} onClick={() => toggleAllCc(true)}>{showAllCc ? null : (" +" + (copies.length - 5))}</span>
                                                    </span>
                                                }
                                            </div>
                                        </span>
                                    </div>
                                }
                                <div className='card-header'>
                                    <ActionModelMessage auth={auth} model={model}/>
                                </div>
                            </div>
                            
                        </>
                    }
                </div>
                    
            }
        </div>
    )
}
