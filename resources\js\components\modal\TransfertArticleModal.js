import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
export default function TransfertArticleModal({action, updateData, closeModal}) {
    const [error, setError] = useState()
    const [agents, setAgents] = useState([])
    const [disabled, setDisabled] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [searchValue, setSearchValue] = useState("")
    const [selectedAgent, setSelectedAgent] = useState(null)

    const handleSelectAgent = (ag) => {
        setSelectedAgent(ag)
    }

    const handleConfirmModal = (e) => {
        e.preventDefault()
        axios.post(action.request, selectedAgent, useToken())
        .then((res) => {
            if (res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if (res.data.error)
                setError(res.data.error)
            else {
                if (closeModal) closeModal()
                if (updateData) updateData(true)
            }
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    const handleSearch = (initial, e) => {
        if(e) e.preventDefault()
        const params = new URLSearchParams()
        if(initial){
            setDataLoaded(true)
            setAgents([])
            params.set("offset", 0)
        }
        else
            params.set("offset", agents.length)
            params.set("value", searchValue.replace('+', '%2B'))
        axios.get('/api/mouvement_article/get_actif_agent?' + params, useToken())
        .then((res) => {
            if(res.data){
                if(initial)
                    setAgents(res.data.agents)
                else {
                    const list = agents.slice().concat(res.data.agents)
                    setAgents(list)
                }
                setDataLoaded(res.data.agents.length < 30)
            }
        })
        .catch((e) => {
            console.error(e)
            setDisabled(false)
        })
    }

    const fetchMoreData = () => {
        setTimeout(() => {
            handleSearch()
        }, 300);
    }

    useEffect(() => {
        handleSearch(true)
    }, [])

    return <div className="modal">
        <div>
            <h2>
                Transférer un article
            </h2>
            <div className="search-container">
                <input type="text" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} placeholder="Nom de l'agent"/>
                <button onClick={(e) => handleSearch(true, e)} disabled={disabled}>Rechercher</button>
            </div>
            <div id="scrollableList">
                <InfiniteScroll
                    dataLength={agents.length}
                    next={fetchMoreData}
                    hasMore={!allDataLoaded}
                    loader={<LoadingPage/>}
                    scrollableTarget="scrollableList"
                >
                    <div className='list-container'>
                        <ul>
                            {
                                agents.map(ag => {
                                    return <li key={ag.id} onClick={() => handleSelectAgent(ag)}
                                    className={(selectedAgent && selectedAgent.id == ag.id) ? "selected" : ""}
                                    >
                                        {ag.nom}<br/>
                                        <span className='secondary'>{ag.poste}</span>
                                    </li>
                                })
                            }
                        </ul>
                    </div>
                </InfiniteScroll>
            </div>
            {
                error &&
                <div className="danger"><br/>{error}</div>
            }
            <div className="form-button-container">
                {
                    selectedAgent &&
                    <button onClick={handleConfirmModal} className='primary'>Confirmer</button>
                }
                <button onClick={closeModal}>Annuler</button>
            </div>
        </div>
    </div>
}
