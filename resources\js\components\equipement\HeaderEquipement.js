import moment from 'moment';
import React from 'react';
import matricule from '../util/matricule';
moment.locale('fr')

export default function HeaderEquipement({auth, data}) {
    return (
        <div>
            <h3>
                {
                    data.site ?
                        <div>
                            {data.site}
                        </div>
                    : data.employe ?
                        <div>
                            {matricule(data)} {data.employe} 
                        </div>
                    : data.personal ?
                        <div>
                            {data.user_nom} <span className='secondary'>{" <" + data.user_email  + ">"}</span>
                        </div>
                    : 
                        <div className='danger'>
                            Type de demande non trouvé
                        </div>
                }
            </h3>
            {
                (data.site && data.employe) &&
                <div>
                    Employe: <span className='text'>{
                        matricule(data)} {data.employe} 
                    </span>
                </div>
            }
            {
                (data.recruiting == 1) &&
                <div>
                    <span  className='cyan'>
                        Employe en cours de recrutement
                    </span>
                </div>
            }
            <div>
                {
                    (data.type == "other") ?
                        <span className='text'>{data.demande}</span>
                    : (data.articles && (data.articles.length > 1 || (data.articles.length == 1 && data.articles[0] != data.type)))?
                        <span>
                            {data.type_demande}
                            : <span className='text'>{data.articles.join(', ')}</span>
                        </span>
                    :
                        <span className='text'>{data.type_demande}</span>
                }
            </div>
            <div className='card-footer'>
                <span>
                    <span>Demandeur : </span>
                    <span className='text'>{data.user_nom} {" <" + data.user_email + ">"}</span>
                </span>
                
                <span className='secondary'>{moment(data.created_at).from(auth.datetime)}</span>
            </div>
        </div>
    )
}