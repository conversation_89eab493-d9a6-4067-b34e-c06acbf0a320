import React, { useState } from 'react'
import InputAgent from '../input/InputAgent'
import axios from 'axios'
import useToken from '../util/useToken'

export default function AssignerAgent({closeModal, id, agent, setAgent, updateData}) {
    const [error, setError] = useState('')
    const handleAssign = () => {
        axios.post('/api/anomalie_planning/assign/' + id, { remplacant_id: agent.id }, useToken())
        .then((res) => {
            if(res.data.success){
                closeModal()
                updateData()
                setError('')
            }
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
        })
    }
    return (
        <div className='modal'>
            <div>
                <h2>Remplacer</h2>
                <InputAgent label="Agent remplaçant" value={agent} onChange={setAgent} required/>
                {
                    error &&
                    <div className='container-error'>
                        {error}
                    </div>
                }
                <div className='form-button-container'>
                    <button type='button' className='btn-primary' onClick={handleAssign}>Remplacer</button>
                    <button type='button' className='btn' onClick={closeModal}>Fermer</button>
                </div>
            </div>
        </div>
    )
}
