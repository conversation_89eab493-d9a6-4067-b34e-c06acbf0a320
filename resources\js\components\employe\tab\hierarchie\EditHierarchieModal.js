import React, { useState } from 'react';

import Textarea from './Textarea';
import axios from 'axios';
import useToken from '../util/useToken';

export default function NoteModal({ action, updateData, closeModal, required }) {
    const [note, setNote] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleOk = () => {
        disableSubmit(true)
        setError("")
        let formData = new FormData()
        formData.append("note", note)
        axios.post(action ? action.request : "/api/notification/add", formData, useToken())
            .then((res) => {
                disableSubmit(false)
                if (res.data.success) {
                    updateData()
                }
                else if (res.data.error == "EACCES")
                    setError("Une erreur est survenue.")
                else if (res.data.error)
                    setError(res.data.error)
            })
            .catch((e) => {
                console.error(e)
                disableSubmit(false)
                setError("Erreur d'envoie, réessayez.")
            })
    }

    return <div className='modal'>
        <div>
            <h3>
                {label}
            </h3>
            <div className='input-container'>
                
            </div>
            <Textarea
                value={note}
                label="Commentaire"
                onChange={(value) => setNote(value)}
                required={action ? action.required : required} />
            {
                error &&
                <div className='container-error'>
                    {error}
                </div>
            }
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Envoyer</button>
                <button onClick={() => { closeModal() }}>Annuler</button>
            </div>
        </div>
    </div>
}