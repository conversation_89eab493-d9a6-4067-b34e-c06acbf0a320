const moment = require('moment')
const mysql = require('mysql')
const nodemailer = require("nodemailer")
const hbs = require('nodemailer-express-handlebars')
const path = require('path')
const { email_config } = require("../auth")
const matricule = require("../resources/js/components/util/matricule")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == 'task')
const destination_vg = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

function getDayOrNightExport(){
	let beginDay = moment().set({hour:6, minute:10, second:0})
	let endDay = moment().set({hour:18, minute:10, second:0})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
	else {
		if(moment().isBefore(beginDay))
			return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 06:00:00"
		return moment().format("YYYY-MM-DD") + " 06:00:00"
	}
}

const sqlSelectLastControlroomExport = "SELECT value FROM params p WHERE p.key = 'last_controlroom_export'"

function sqlSelectFaitMarquant(dateString){
    const begin = dateString
    const end = moment(dateString).add(12, "hours").format("YYYY-MM-DD HH:mm:ss")
	return "SELECT fm.id, fm.site_id, fm.objet, fm.commentaire, fm.created_at, " +
        "s.nom as 'site', u.name as 'user_nom', u.email as 'user_email', u.role " +
        "from fait_marquants fm " +
        "left join sites s on s.idsite = fm.site_id " +
        "left join users u on u.id = fm.user_id " +
        "where u.role = 'room' and fm.created_at > '" + begin +"' and fm.created_at <= '" + end +"' " +
        "order by fm.created_at"
}

function sqlSelectSanction(dateString){
    const begin = dateString
    const end = moment(dateString).add(12, "hours").format("YYYY-MM-DD HH:mm:ss")
	return "SELECT sanc.id, sanc.employe_id, sanc.site_id, sanc.created_at, sanc.date_pointage, sanc.mesure, " +
        "sanc.motif, sanc.objet, sanc.montant, sanc.status, u.name as 'user_nom', u.email as 'user_email', " +
        "s.nom as 'site', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.nom as 'employe' " +
        "from sanctions sanc " +
        "left join employes a on a.id = sanc.employe_id " +
        "left join sites s on s.idsite = sanc.site_id " +
        "left join users u on u.id = sanc.user_id " +
        "where sanc.status != 'draft' and u.role = 'room' " +
        "and sanc.created_at > '" + begin +"' and sanc.created_at <= '" + end +"' " +
        "order by sanc.created_at"
}

function sqlSelectSav(dateString){
    const begin = dateString
    const end = moment(dateString).add(12, "hours").format("YYYY-MM-DD HH:mm:ss")
	return "SELECT sav.id, sav.site_id, sav.motif, sav.date_sav, sav.technicien, sav.created_at, sav.status, sav.mesure, " +
        "s.nom as 'site', u.name as 'user_nom', u.email as 'user_email' " +
        "from sav " +
        "left join sites s on s.idsite = sav.site_id " +
        "left join users u on u.id = sav.user_id " +
        "where sav.status != 'draft' and u.role = 'room' " + 
        "and sav.created_at > '" + begin +"' and sav.created_at <= '" + end +"' " +
        "order by sav.created_at"
}

function sqlUpdateLastControlroomExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_controlroom_export'"
}

function doControlroomExport(dateString){
	console.log("doControlroomExport")
    pool.query(sqlSelectFaitMarquant(dateString), [], async (err, faitMarquants) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb fait_marquant: " + faitMarquants.length)
            pool.query(sqlSelectSanction(dateString), [], async (err, sanctions) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb sanction: " + sanctions.length)
                    pool.query(sqlSelectSav(dateString), [], async (err, sav) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Nb sav: " + sav.length)
                            const header = "Rapport Controlroom du " + moment(dateString).format("DD MMMM YYYY") + (moment(dateString).format("HH:mm:ss") == "06:00:00" ? " JOUR" : " NUIT")
                            sendMail(
                                pool,
                                isTask ? destination_vg : destination_test,
                                header, 
                                faitMarquants,
                                sanctions,
                                sav,
                                (response) => {
                                    if(response && isTask){
                                        pool.query(sqlUpdateLastControlroomExport(dateString), [], (e, r) =>{
                                            if(e)
                                                console.error(e)
                                            else
                                                console.log("update last diag export: " + r)
                                            process.exit(1)
                                        })
                                    }
                                    else
                                        process.exit(1)
                                }
                            )
                        }
                    })
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && ["06:00:00", "18:00:00"].includes(process.argv[3])){
    console.log("send test...")
    doControlroomExport(process.argv[2] + ' ' + process.argv[3])
}
else if(isTask){
    let date_vigilance = getDayOrNightExport()
    pool.query(sqlSelectLastControlroomExport, [], (err, result) => {
        if(err)
            console.error(err)
        else if(result && result[0].value == date_vigilance) {
            console.log("export list controlroom already done!")
            process.exit(1)
        }
        else {
            console.log("exporting ...")
            doControlroomExport(date_vigilance)
        }
    })
}
else
    console.log("please specify command!")