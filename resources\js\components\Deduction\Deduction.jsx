import React, { useEffect, useState } from "react";
import SearchBar from "../input/SearchBar";
import LoadingPage from "../loading/LoadingPage";
import { Link, useLocation } from "react-router-dom";
import InfiniteScroll from "react-infinite-scroll-component";
import axios from "axios";
import useToken from "../util/useToken";
import StatusLabel from "../input/StatusLabel";
import "./deduction.css";
import showAmount from "../util/numberUtil";
import matricule from "../util/matricule";

export default function Deduction({
    auth,
    deductions,
    setDeductions,
    currentId,
    setCurrentId,
}) {
    const [isLoading, toggleLoading] = useState(false);
    const [allDataLoaded, setDataLoaded] = useState(false);
    const searchItems = [
        { label: "Date de création", name: "created_at", type: "date" },
        { label: "Status", name: "status", type: "number" },
        { label: "Réference", name: "id", type: "number" },
        { label: "Employé", name: "employe_id", type: "string" },
    ];

    const locationSearch = useLocation().search;
    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true);
            setDataLoaded(true);
            setCurrentId(null);
            params.set("offset", 0);
        } else params.set("offset", deductions.length);
        axios
            .get("/api/deduction?" + params, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.error) {
                        console.error(res.data.error);
                    } else {
                        if (initial) setDeductions(res.data.deductions);
                        else {
                            const list = deductions.slice().concat(res.data.deductions);
                            setDeductions(list);
                        }
                        setDataLoaded(res.data.deductions.length < 30);
                    }
                    toggleLoading(false);
                }
            })
            .catch((e) => {
                console.error(e);
            });
        return () => {
            isMounted = false;
        };
    };
    useEffect(() => updateData(true), [locationSearch]);
    const fetchMoreData = () => {
        setTimeout(() => {
            updateData();
        }, 300);
    };
    return (
        <>
            {isLoading ? (
                <LoadingPage />
            ) : (
                <div>
                    <div className="padding-container space-between">
                        <h2>Déduction</h2>
                        {["resp_rh"].includes(auth.role) && 
                            <Link to="/deduction/add" className="btn btn-primary">
                                Nouveau
                            </Link>
                        }
                    </div>
                    <SearchBar listItems={searchItems} />
                    {
                        deductions.length == 0 ?
                            <h3 className="center secondary">
                                Aucun données trouvé
                            </h3>
                        :
                            <InfiniteScroll
                                dataLength={deductions.length}
                                next={fetchMoreData}
                                hasMore={!allDataLoaded}
                                loader={<LoadingPage />}
                            >
                                <div className="line-container">
                                    <div className="row-list">
                                        <b className="line-cell-lg">Agent</b>
                                        <b className="status-line">
                                            <StatusLabel color={"grey"} />
                                        </b>
                                        <b className="montant">Montant</b>
                                        <b className="motif">Motif</b>
                                    </div>
                                </div>
                                {deductions.map((ded) => (
                                    <div
                                        onClick={() => setCurrentId(ded.id)}
                                        className={`line-container ${
                                            currentId && currentId == ded.id
                                                ? "selected"
                                                : ""
                                        }`}
                                        key={ded.id}
                                    >
                                        <div>
                                            <div className="row-deduction">
                                                <span className="line-cell-lg">
                                                    [{matricule(ded)}] {ded.employe}
                                                </span>
                                                <span className="status-line">
                                                    <StatusLabel color={ded.status_color} done={ded.paie_id}/>
                                                </span>
                                                <span className="montant">{showAmount(ded.montant)}</span>
                                                <span className="motif">{ded.motif}</span>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </InfiniteScroll>
                    }
                </div>
            )}
        </>
    );
}
