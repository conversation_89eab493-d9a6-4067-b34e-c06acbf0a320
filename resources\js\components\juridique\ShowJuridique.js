import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import useToken from '../util/useToken';
import ActionJuridique from './ActionJuridique';
import Tab from '../layout/Tab';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';
import showAmount from '../util/numberUtil';

export default function ShowJuridique({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [juridique, setJuridique] = useState()
    const [isLoading, toggleLoading] = useState(false)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/juridique/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setJuridique(res.data)
                const newUser = []
                if (auth.id != res.data.user_id) 
                    newUser.unshift({ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom})
                setDefautUsers(newUser) 
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(juridique)
    }, [juridique])

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                juridique &&
                <>
                    <ShowHeader size={size} label="Juridique" id={juridique.id} closeDetail={() => setCurrentId()}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + juridique.status_color}>
                                    {juridique.status_nom}
                                </span> {
                                    juridique.nb_pj > 0 &&
                                    <span className='badge-outline'>
                                        Pièce jointe : {juridique.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {juridique.debiteur}
                        </h3>
                        <div>
                            Référence : <span className='text'>{juridique.reference}</span>
                        </div>
                        <div>
                            Agence : <span className='text'>{juridique.agence}</span>
                        </div>
                        <div>
                            Débiteur : <span className='text'>{juridique.debiteur}</span>
                        </div>
                        <div>
                            Contrat : <span className='text'>{juridique.contrat}</span>
                        </div>
                        <div>
                            Facture(s) : <span className='text'>{juridique.facture}</span>
                        </div>
                        <div>
                            Montant : <span className='text'>{showAmount(juridique.montant)}</span>
                        </div>
                        {
                            auth.role == "juridique" &&
                            <div className='card-action'>
                                <ActionJuridique auth={auth} juridique={juridique} updateData={updateData} toggleLoading={toggleLoading}/>
                            </div>
                        }
                    </div>
                    <Tab auth={auth} name="juridique_id" value={juridique.id} data={juridique} updateData={updateData} defautUsers={defautUsers}/>
                </>
            }
        </div>
    }</>
}