import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import moment from 'moment';
import StatusLabel from '../input/StatusLabel';
import { GoReply } from "react-icons/go";

export default function Message({messages, setMessages, setCurrentId, currentId}) {
    const locationSearch = useLocation().search
    const params = new URLSearchParams(locationSearch)
    const sent = params.get("sent")
    const unread = params.get("unread")
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'Non lu', name: 'unread', type:'string'},
        {label: 'A faire', name: 'to_do', type: 'number'},
        {label: 'Objet', name: 'objet', type:'string'},
        {label: 'Utilisateur', name: 'user_id', type:'number'},
        {label: 'Service', name: 'service_id', type:'integer'},
        {label: 'Date', name: 'created_at', type: 'date' },
        {label: 'Contenu', name: 'message_content', type: 'string'},
    ]

    const handleSeenAll = (lastId) => {
        let formData = new FormData()
        formData.append("last_id", lastId)
        params.forEach((value, key) => {
            formData.append(key, value)
        })
        axios.post('/api/message/seen_all', formData, useToken())
        .then((res) => {
            if(res.data.error)
                console.error(res.data.error)
            else {
                updateData(true)
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }
        
    const updateData = (initial) => {
        let isMounted = true;
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            params.set("offset", 0)
        }
        else
            params.set("offset", messages.length)
        
        axios.get('/api/message?' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setMessages(res.data.messages)
                    else {
                        const list = messages.slice().concat(res.data.messages)
                        setMessages(list)
                    }
                    setDataLoaded(res.data.messages.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };
    const unfollowAll = () => { 
        axios.post('/api/message/unfollow_all', null, useToken())
        .then((res) => {
            if(res.data.error)
                console.error(res.data.error)
            else {
                updateData(true)
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>{sent ? "Envoyé" : "Courrier entrant"}</h2>
                <Link className='btn btn-primary' to="/message/add">Nouveau message</Link>
            </div>
            <SearchBar listItems={searchItems}/>
            {
                (unread && messages.length > 0) &&
                <div className='header-action action-container'>
                    {
                        sent ?
                            <span>
                                <span onClick={() => unfollowAll()}>Ne plus suivre tous</span>
                            </span>
                        : (
                            params.size > 1 && 
                            <span>
                                <span onClick={() => handleSeenAll(messages[0].id)}>Marquer tous comme lu</span>
                            </span>
                        )
                    }
                </div>
            }
            {
                messages.length == 0 ? 
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                    <InfiniteScroll
                        dataLength={messages.length}
                        next={fetchMoreData}
                        hasMore={!allDataLoaded}
                        loader={<LoadingPage/>}
                    >
                        <div className="line-container">
                            <div className="row-list">
                                <b className="line-cell-lg">Objet</b>
                                <b className="status-line">
                                    <StatusLabel color={"grey"} />
                                </b>
                                <b className="line-cell-sm">Date</b>
                                <b>
                                    {
                                        sent ? 
                                            "Déstinataire"
                                        :
                                            "Expéditeur"
                                    }
                                </b>
                            </div>
                        </div> 
                        {
                            messages.map((ms) => (
                                <div className={`line-container ${currentId && currentId == ms.id ? "selected" : ""}`} 
                                    key={ms.id} 
                                    onClick={()=>setCurrentId(ms.id)}
                                >
                                    <div className="row-list">
                                        <span className={"line-cell-lg " + ((sent || ms.seen) ? "secondary" : "")}>{ms.objet}</span>
                                        <span className="status-line">
                                            {
                                                ms.replied ?
                                                    <GoReply color={(!sent && ms.to_do) ? "#ff9800" : "#00bcd4"}/>
                                                :
                                                    <StatusLabel color={(!sent && ms.to_do) ? "orange" : (sent || ms.seen) ? "grey" : "purple"} />
                                            }
                                        </span>
                                        <span className={"line-cell-sm " + ((sent || ms.seen) ? "secondary" : "")}>{moment(ms.created_at).format("DD/MM/YY HH:mm")}</span>
                                        <span className={(sent || ms.seen) ? "secondary" : ""}>
                                            {
                                                (sent && !unread) ?
                                                    (
                                                        (ms.notes && ms.notes.length > 0) &&
                                                        (
                                                            ms.notes.length > 1 ?
                                                                ms.notes.map(n => n.name).join(', ')
                                                            :
                                                                ms.notes[0].name + " <" + ms.notes[0].email + ">"
                                                        )
                                                    )
                                                :
                                                    ms.name + " <" + ms.email + ">"
                                            }
                                        </span>
                                    </div>
                                </div> 
                            ))
                        }
                    </InfiniteScroll>
            }
        </div>
    } </>
}