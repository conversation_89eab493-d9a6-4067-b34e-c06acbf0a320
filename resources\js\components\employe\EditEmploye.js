import React, { useEffect, useState } from 'react';
import {useParams, useLocation} from 'react-router-dom'
import axios from 'axios';
import moment from 'moment';
import "../layout/tab.css"

import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import showAmount from '../util/numberUtil';
import InputCheckBox from '../input/InputCheckBox';
import DualContainer from '../container/DualContainer';
import InputImmatriculation from '../input/InputImmatriculation';
import InputFonction from '../input/InputFonction';
import InputAgence from '../input/InputAgence';
import InputSelect from '../input/InputSelect';
import ButtonSubmit from '../input/ButtonSubmit';
import InputDate from '../input/InputDate';
import InputService from '../input/InputService';
import InputEmploye from '../input/InputEmploye';
import SalForfaitModal from "../modal/SalForfaitModal";
import EditCritereModal from './critere/EditCritereModal';
import { AiTwotoneEdit } from 'react-icons/ai';
import { IoMdClose } from 'react-icons/io';
import InputSuggestion from '../input/InputSuggestion';
import ConfirmModal from '../modal/ConfirmModal';

export default function EditEmploye({auth, title, action}) {
    const categories = ["1A", "1B", "2A", "2B", "3A", "3B", "4A", "4B", "5A", "5B", "HC"]
    const banques =  ["", "ACCES BANQUE", "BAOBAB BANQUE", "SIPEM BANQUE", "BOA", "BMOI", "BFV"].sort()
    const mobileMoneys = ["", "MVOLA", "AIRTEL MONEY", "ORANGE MONEY"].sort()
    const modePaiements = [{value: 'VIR', label: 'Virement bancaire'}, {value: 'MOB', label: 'Mobile money'}]
    const suggestionSalBase = [{ label: '210 000', value: 210000 }, { label: '238 000', value: 238000 }, { label: '262 680', value: 262680 }]
    const getModePaiment = (value) => {
        let selectedMode = null
        modePaiements.forEach(mode => {
            if(mode.value == value)
                selectedMode = mode;
        });
        return selectedMode;
    }

    const params = useParams()
    const location = useLocation()
    const recrutement = location.state?.recrutement
    const idsPj = location.state?.idsPj
    const [ignoreName, setIgnoreName] = useState(false)
    const [nom, setNom] = useState("")
    const [responsable, setResponsable] = useState(null)
    const [categorieProf, setCategorieProf] = useState("");
    const [societe, setSociete] = useState(null) 
    const [fonction, setFonction] = useState(null)
    const [service, setService] = useState(null)
    const [titre, setTitre] = useState("")
    const [agence, setAgence] = useState(null);
    const [partVariable, setPartVariable] = useState(false);
    const [interim, setInterim] = useState(false);
    const [salForfait, setSalForfait] = useState(false);
    const [salBase, setSalBase] = useState('');
    const [heureContrat, setHeureContrat] = useState('');
    const [heureConvenu, setHeureConvenu] = useState('');
    const [modePaiement, setModePaiement] = useState(getModePaiment("VIR"))
    const [nomPaiement, setNomPaiement] = useState('')
    const [numeroMobile, setNumeroMobile] = useState('')
    const [numeroCompte, setNumeroCompte] = useState('')
    const [codeBanque, setCodeBanque] = useState('')
    const [codeGuichet, setCodeGuichet] = useState('')
    const [rib, setRib] = useState('')
    const [cinText, setCinText] = useState('')
    const [perdiem, setPerdiem] = useState('');
    const [indemDepl, setIndemDepl] = useState('');
    const [primeAnc, setPrimeAnc] = useState('');
    const [numStagiaire, setNumStagiaire] = useState('');
    const [dateEmbauche, setDateEmbauche] = useState(null);
    const [numEmploye, setNumEmploye] = useState('');
    const [dateConfirmation, setDateConfirmation] = useState(null);
    const [numEmpSoit, setNumEmpSoit] = useState('');
    const [dateConfSoit, setDateConfSoit] = useState(null);
    const [numEmpSaoi, setNumEmpSaoi] = useState('')
    const [dateConfSaoi, setDateConfSaoi] = useState(null);
    const [cin, setCin] = useState(false);
    const [cv, setCv] = useState(false);
    const [photo, setPhoto] = useState(false);
    const [residence, setResidence] = useState(false);
    const [reperage, setReperage] = useState(false);
    const [bulletin, setBulletin] = useState(false);
    const [bonneConduite, setBonneConduite] = useState(false);
    const [disabledSubmit, setDisabledSubmit] = useState(false);
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [activeMenu, setActiveMenu] = useState("contrat")

    const [showDeleteModal, toggleDeleteModal] = useState(false)
    const [showEditModal, toggleEditModal] = useState(false) 
    const [currentCritere, setCurrentCritere] = useState(null)
    const [criteres, setCriteres] = useState([])
    const [respPart, setRespPart] = useState()
    const [showSalForfaitModal, toggleSalForfaitModal] = useState(false)
    const [comfirmDefaultPv, toggleComfirmDefaultPv] = useState(false)
    const [doublon, setDoublon] = useState(false)

    const defaultCritereInterv = [
        { designation: 'Tenues', montant: '20000', edition: 1, index: 1 },
        { designation: 'Respect de la position en poste', montant: '20000', edition: 1, index: 2 },
        { designation: 'Delai d\'intervention', montant: '20000', edition: 1, index: 3 },
        { designation: 'Respect des procedures', montant: '20000', edition: 1, index: 4},
        { designation: 'Appreciation generale', montant: '20000', edition: 1, index:5 },
    ]

    const defaultCritereCp = [
        { designation: 'Respect des consignes', montant: '10000', edition: 1, index: 1 },
        { designation: 'Tenues', montant: '10000', edition: 1, index: 2 },
        { designation: 'Passation', montant: '10000', edition: 1, index: 3 },
        { designation: 'Satisfaction Client', montant: '10000', edition: 1, index: 4 },
    ]

    const handleFonctionChange = (fonc) => {
        if(params.id && fonc && (!fonction || fonction.sal_forfait != fonc.sal_forfait)){
            toggleSalForfaitModal(true)
        }
        setFonction(fonc)
    }

    const defaultPv = () => {
        if (fonction) {
            setPartVariable(true)
            if(!params.id)
                setSalForfait(fonction.sal_forfait ? true : false)
            const newCriteres = []
            if ([2, 5, 6].includes(fonction.id)) {
                criteres.forEach(old_cr => {
                    if (old_cr.employe_id) {
                        old_cr.soft_delete = 1
                        old_cr.edition = 1
                    }
                    if (old_cr.id) newCriteres.push(old_cr);
                })
                let currentCritereres = [...defaultCritereInterv];
                if (fonction.id == 2)
                    currentCritereres = [...defaultCritereCp];  
                currentCritereres.forEach(cr => {
                    const correspond = newCriteres.find(old_cr => old_cr.designation === cr.designation && parseInt(old_cr.montant) === parseInt(cr.montant));
                    if (correspond) {
                        const index = newCriteres.findIndex(obj => obj.designation === correspond.designation && (parseInt(obj.montant) === parseInt(correspond.montant)));
                        if (index != -1) {
                            newCriteres[index].soft_delete = null;
                        }
                    }
                    else {
                        newCriteres.push(cr);
                    }
                })
                setCriteres(newCriteres);
            }
        }   
    }

    useEffect(() => {
        if (fonction && [2, 5, 6].includes(fonction.id)) {
            toggleComfirmDefaultPv(true)
        }
    }, [fonction])

    useEffect(() => {
        if(recrutement) {
            setNom(recrutement.nom)
            setCin(recrutement.cin)
            setCinText(recrutement.cin_text)
            setCv(recrutement.cv)
            setPhoto(recrutement.photo)
            setResidence(recrutement.residence)
            setReperage(recrutement.plan_reperage)
            setBulletin(recrutement.bulletin_n3)
            setBonneConduite(recrutement.bonne_conduite)
        }
    }, [recrutement])
    
    const confirmEdit = (designation, montant) => {
        const newCriteres = []
        let newValue = {designation: designation, montant: montant, edition: 1}
        criteres.forEach(cr => {
            if (currentCritere && currentCritere.index == cr.index) {
                if(currentCritere.id){
                    newValue.id = currentCritere.id
                }
                newCriteres.push(newValue)
            }
            else
                newCriteres.push(cr)
        })
        if(!currentCritere)
            newCriteres.unshift(newValue)
        newCriteres.forEach
        setCriteres(newCriteres.map((c, idx) => {c.index = idx; return c}))
        toggleEditModal(false)
    }

    const confirmDelete = (e) => {
        e.preventDefault()
        const newCriteres = []
        criteres.forEach((cr, index) => {
            if(currentCritere.index == index){
                if(cr.id){
                    cr.edition = 1
                    cr.soft_delete = 1
                    newCriteres.push(cr)
                }
            }
            else
                newCriteres.push(cr)
        })
        setCriteres(newCriteres.map((c, idx) => {c.index = idx; return c}))
        toggleDeleteModal(false)
    }

    const handleDeleteCritere = (critere) => {
        setCurrentCritere(critere)
        toggleDeleteModal(true)
    }

    const handleEditCritere = (critere) => {
        setCurrentCritere(critere)
        toggleEditModal(true)
    }

    const handleModePaiement = (mode) => {
        setModePaiement(mode)
        if(modePaiement && modePaiement.value != mode.value)
            setNomPaiement("")
    }

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        setDisabledSubmit(true)
        let data = new FormData()
        
        if(modePaiement && modePaiement.value.trim())
            data.append("mode_paiement", modePaiement.value)
        if(nomPaiement.trim())
            data.append("nom_paiement", nomPaiement)
        if(numeroMobile.trim())
            data.append("numero_mobile", numeroMobile)
        if(numeroCompte.trim())
            data.append("numero_compte", numeroCompte)
        if(codeBanque.trim())
            data.append("code_banque", codeBanque)
        if(codeGuichet.trim())
            data.append("code_guichet", codeGuichet)
        if(rib.trim())
            data.append("rib", rib)
        if(cinText.trim())
            data.append("cin_text", cinText)
        data.append("cin", cin ? 1 : 0)
        data.append("cv", cv ? 1 : 0)
        data.append("photo", photo ? 1 : 0)
        data.append("residence", residence ? 1 : 0)
        data.append("plan_reperage", reperage ? 1 : 0)
        data.append("bulletin_n3", bulletin ? 1 : 0)
        data.append("bonne_conduite", bonneConduite ? 1 : 0)
        data.append("part_variable", partVariable ? 1 : 0)
        data.append("interim", interim ? 1 : 0)
        if(responsable)
            data.append("responsable_id", responsable.id)
        if (respPart)
            data.append("resp_part_id", respPart.id)
        if(titre && titre.trim())
            data.append("titre", titre)
        if(nom && nom.trim())
            data.append("nom", nom.replace(/ +/g, " ").trim())
        if(doublon)
            data.append("doublon", doublon)
        if(heureContrat)
            data.append("nb_heure_contrat", heureContrat)
        if(heureConvenu)
            data.append("nb_heure_convenu", heureConvenu)
        if(agence)
            data.append("agence_id", agence.id)
        if(fonction)
            data.append("fonction_id", fonction.id)
        if(salForfait)
            data.append("sal_forfait", 1)
        if(salBase)
            data.append("sal_base", salBase)
        if(societe)
            data.append("societe_id", societe.id)
        if(service)
            data.append("service_id", service.id)
        if(numStagiaire && numStagiaire.toString().trim())
            data.append("numero_stagiaire", numStagiaire)
        if(numEmploye && numEmploye.toString().trim())
            data.append("numero_employe", numEmploye)
        if(numEmpSoit && numEmpSoit.toString().trim())
            data.append("num_emp_soit", numEmpSoit)
        if(dateEmbauche)
            data.append("date_embauche", moment(dateEmbauche).format('YYYY-MM-DD'))
        if(dateConfirmation)
            data.append("date_confirmation", moment(dateConfirmation).format('YYYY-MM-DD'))
        if(dateConfSoit)
            data.append("date_conf_soit", moment(dateConfSoit).format('YYYY-MM-DD'))
        if (numEmpSaoi && numEmpSaoi.toString().trim())
            data.append("num_emp_saoi", numEmpSaoi)
        if (dateConfSaoi)
            data.append("date_conf_saoi", moment(dateConfSaoi).format('YYYY-MM-DD'))
        if(indemDepl)
            data.append("idm_depl", indemDepl)
        if(perdiem)
            data.append("perdiem", perdiem)
        if(primeAnc)
            data.append("prime_anc", primeAnc)
        if(categorieProf)
            data.append("categorie", categorieProf)
        if(criteres.length > 0 && criteres.filter(cr => !cr.soft_delete).length > 0)
        data.append("criteres", JSON.stringify(criteres))
        if(recrutement) {
            data.append("isFromRecrutement", true)
            data.append("recrutement_id", recrutement.id)
        }
        if(idsPj) {
            data.append("idsPj", JSON.stringify(idsPj))
        } 
        axios.post(action + (params.id ? params.id : ""), data, useToken())
        .then(res => {
            setDisabledSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .finally(()=>{
            setDisabledSubmit(false)
        })
    }

    useEffect(() => {
        if(societe){
            if(societe.id == 1)
                setActiveMenu("dgm")
            else if(societe.id == 2)
                setActiveMenu("soit")
            else if(societe.id == 3)
                setActiveMenu("stagiaire")
            else if (societe.id == 6)
                setActiveMenu("saoi")
            else
                setActiveMenu("contrat")
        }
        else
            setActiveMenu("contrat")
    }, [societe]);

    useEffect(() => {
        let isMounted = true
        if(params.id){
            axios.get('/api/employe/show_detail/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    const employe = res.data
                    setNom(employe.nom)
                    setIgnoreName(employe.ignore_name ? true : false)
                    setResponsable(employe.responsable ? {'id': employe.responsable_id, 'nom': employe.responsable} : null)
                    // setRespUser(employe.resp_user_nom ? { 'id': employe.resp_user_id, name: employe.resp_user_nom, 'email': employe.resp_user_email} : null)
                    setModePaiement(employe.mode_paiement ? getModePaiment(employe.mode_paiement) : '')
                    setNomPaiement(employe.nom_paiement ? employe.nom_paiement : '')
                    setNumeroMobile(employe.numero_mobile ? employe.numero_mobile : '')
                    setNumeroCompte(employe.numero_compte ? employe.numero_compte : '')
                    setCodeBanque(employe.code_banque ? employe.code_banque : '')
                    setCodeGuichet(employe.code_guichet ? employe.code_guichet : '')
                    setRib(employe.rib ? employe.rib : '')
                    setCategorieProf(employe.categorie)
                    setSociete(employe.societe)
                    setAgence(employe.agence)
                    setFonction(employe.fonction)
                    setService(employe.service)
                    setTitre(employe.titre ? employe.titre : "")
                    setCinText(employe.cin_text ? employe.cin_text : "")
                    setPartVariable(employe.part_variable ? employe.part_variable : "")
                    setRespPart(employe.resp_part_variable ? { 'id': employe.resp_part_id, 'nom': employe.resp_part_variable } : null)
                    setSalBase(employe.sal_base ? employe.sal_base : "")
                    setHeureContrat(employe.nb_heure_contrat ? employe.nb_heure_contrat : "")
                    setHeureConvenu(employe.nb_heure_convenu ? employe.nb_heure_convenu : "")
                    setPerdiem(employe.perdiem ? employe.perdiem : "")
                    setIndemDepl(employe.idm_depl ? employe.idm_depl : "")
                    setPrimeAnc(employe.prime_anc ? employe.prime_anc : "")
                    setNumStagiaire(employe.numero_stagiaire ? employe.numero_stagiaire : "")
                    setDateEmbauche(employe.date_embauche ? moment(employe.date_embauche).toDate() : null)
                    setNumEmploye(employe.numero_employe ? employe.numero_employe : "")
                    setDateConfirmation(employe.date_confirmation ? moment(employe.date_confirmation).toDate() : null)
                    setNumEmpSoit(employe.num_emp_soit ? employe.num_emp_soit : "")
                    setDateConfSoit(employe.date_conf_soit ? moment(employe.date_conf_soit).toDate() : null)
                    setNumEmpSaoi(employe.num_emp_saoi ? employe.num_emp_saoi : "")
                    setDateConfSaoi(employe.date_conf_saoi ? moment(employe.date_conf_saoi).toDate() : null)
                    setCin(employe.cin ? 1 : 0)
                    setCv(employe.cv ? 1 : 0)
                    setPhoto(employe.photo ? 1 : 0)
                    setResidence(employe.residence ? 1 : 0)
                    setReperage(employe.plan_reperage ? 1 : 0)
                    setBulletin(employe.bulletin_n3 ? 1 : 0)
                    setBonneConduite(employe.bonne_conduite ? 1 : 0)
                    setCriteres(employe.criteres.map((c, idx) => {c.index = idx; return c}))
                    setInterim(employe.interim == 1)
                    setSalForfait(employe.sal_forfait)
                    toggleComfirmDefaultPv(false)
                }
            })
        }
        return () => { isMounted = false };
    }, [])

    return (
        <div id="content">
            <div>
                {
                    showSalForfaitModal &&
                    <SalForfaitModal onChange={setSalForfait} closeModal={() => toggleSalForfaitModal(false)}/>
                }
                {
                    notification ? 
                        <Notification next={notification.id ? "/employe?id=" + notification.id : "/employe?actif=1"} message={notification.success}/>
                    :
                    <form onSubmit={handleSubmit}>
                        <div className="title-container">
                            <h2>{title}</h2>
                        </div>
                        <InputText
                            disabled={ignoreName}
                            required
                            label="Nom"
                            value={nom} 
                            onChange={setNom}/>
                        { auth.role == "admin" &&
                            <InputCheckBox label="Doublon" onChange={setDoublon} checked={doublon} name="doublon"/>
                        }
                        <DualContainer>
                            <InputFonction
                                required
                                currentSelect={fonction} 
                                setCurrentSelect={handleFonctionChange}/>
                            <InputImmatriculation
                                required
                                currentSelect={societe} 
                                setCurrentSelect={setSociete}/>
                        </DualContainer>
                        <div className='tab-container'>
                            <div className='tab-list'>
                                <div onClick={() => setActiveMenu('contrat')} className={activeMenu == 'contrat' ? 'active' : ''}>
                                    Contrat
                                </div>
                                <div onClick={() => setActiveMenu('hierarchie')} className={activeMenu == 'hierarchie' ? 'active' : ''}>
                                    Hiérarchie
                                </div>
                                {auth.role == "resp_rh" &&
                                    <div onClick={() => setActiveMenu('part_variable')} className={activeMenu == 'part_variable' ? 'active' : ''}>
                                        Part variable
                                    </div>
                                }
                                <div onClick={() => setActiveMenu('paiement')} className={activeMenu == 'paiement' ? 'active' : ''}>
                                    Paiement
                                </div>
                                {
                                    (societe && societe.id == 3) &&
                                    <div onClick={() => setActiveMenu('stagiaire')} className={activeMenu == 'stagiaire' ? 'active' : ''}>
                                        Stagiaire
                                    </div>
                                }
                                {
                                    (societe && [1, 2].includes(Number.parseInt(societe.id))) &&
                                    <div onClick={() => setActiveMenu('dgm')} className={activeMenu == 'dgm' ? 'active' : ''}>
                                        Dirickx Guard
                                    </div>
                                }
                                {
                                    (societe && [1, 2].includes(Number.parseInt(societe.id))) &&
                                    <div onClick={() => setActiveMenu('soit')} className={activeMenu == 'soit' ? 'active' : ''}>
                                        SOIT
                                    </div>
                                }
                                {
                                    (societe && societe.id == 6) &&
                                    <div onClick={()=>setActiveMenu('saoi')} className={activeMenu == 'saoi'? 'active' : ''}>
                                        SAOI
                                    </div>
                                }
                                <div onClick={() => setActiveMenu('document')} className={activeMenu == 'document' ? 'active' : ''}>
                                    Document
                                </div>
                            </div>
                            <div className='tab-content'>
                                {
                                    activeMenu == "paiement" &&
                                    <>
                                        <div className='card-container'>
                                            <InputSelect required
                                                label="Mode de paiement"
                                                selected={modePaiement}
                                                setSelected={handleModePaiement}
                                                options={modePaiements}/>
                                            {
                                                modePaiement &&
                                                <InputSelect
                                                    label={modePaiement.value == "VIR" ? "Banque" : modePaiement.value == "MOB" ? "Opérateur" : ""}
                                                    selected={nomPaiement}
                                                    setSelected={setNomPaiement}
                                                    options={modePaiement.value == "VIR" ? banques : modePaiement.value == "MOB" ? mobileMoneys : []}/>
                                            }
                                            {
                                                (modePaiement && modePaiement.value == "VIR") &&
                                                <>
                                                    <DualContainer>
                                                        <InputText label="Numéro de compte" required 
                                                            value={numeroCompte} 
                                                            onChange={setNumeroCompte}/>
                                                        <InputText label="Code banque" required 
                                                            value={codeBanque} 
                                                            onChange={setCodeBanque}/>
                                                    </DualContainer>
                                                    <DualContainer>
                                                        <InputText label="Code guichet" required 
                                                            value={codeGuichet} 
                                                            onChange={setCodeGuichet}/>
                                                        <InputText label="RIB" required 
                                                            value={rib} 
                                                            onChange={setRib}/>
                                                    </DualContainer>
                                                </>
                                            }
                                            {
                                                (modePaiement && modePaiement.value == "MOB") &&
                                                <InputText label="Numéro mobile" required 
                                                    value={numeroMobile} 
                                                    onChange={setNumeroMobile}/>
                                            }
                                            <InputText label="Numéro CIN" 
                                                value={cinText} 
                                                onChange={setCinText}
                                                required
                                            />
                                        </div>
                                    </>
                                }
                                {
                                    activeMenu == "hierarchie" &&
                                    <>
                                        <div className='card-container'>
                                            <div className='field-container'>
                                                <InputCheckBox label="Visite de poste par interim" onChange={setInterim} checked={interim} name="interim"/>
                                            </div>
                                            <InputEmploye label="Responsable hiérarchique"
                                                hideMatricule
                                                required={interim}
                                                value={responsable} 
                                                onChange={setResponsable}/>
                                            {/* <InputUser label="Responsable hiérarchique"
                                                required={interim}
                                                value={respUser}
                                                onChange={setRespUser}/> */}
                                        </div>
                                    </>
                                }
                                {
                                    activeMenu == "part_variable" && 
                                    // auth.role == "resp_rh" &&
                                    <>
                                        {
                                            showEditModal &&
                                            <EditCritereModal 
                                                confirmEdit={confirmEdit}
                                                currentCritere={currentCritere}
                                                closeModal={() => toggleEditModal(false)} 
                                            />
                                        }
                                        {
                                            showDeleteModal &&
                                            <div className='modal'>
                                                <div style={{width: '300px'}}>
                                                    <h3>Supprimé ce critère ?</h3>
                                                    <div className='form-button-container' style={{diplay: 'flex',justifyContent: 'center'}}>
                                                        <button className='btn-primary' onClick={(e) => confirmDelete(e)} style={{marginLeft: '0'}}>Confirmer</button>
                                                        <button onClick={() => toggleDeleteModal(false)}>Annuler</button>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                        <div className='card-container'>
                                            <div className='space-between'>
                                                <div className='field-container'>
                                                    <InputCheckBox label="Part variable" onChange={setPartVariable} checked={partVariable} name="part_varaible"/>
                                                </div>
                                                {
                                                    partVariable &&
                                                    <div className='action-container'>
                                                        <span onClick={() => handleEditCritere()}>
                                                            Ajouter
                                                        </span>
                                                    </div>
                                                }
                                            </div>
                                            {/* <InputUser label="Responsable part variable"
                                                required={partVariable}
                                                value={respPartUser}
                                                onChange={setRespPartUser} /> */}
                                            <InputEmploye label="Responsable part variable"
                                                hideMatricule
                                                value={respPart}
                                                onChange={setRespPart}
                                                required={partVariable}/>
                                        </div>
                                        <div>
                                            {
                                                partVariable &&
                                                <>
                                                    {
                                                        criteres.filter(c => !c.soft_delete).map((cr, index) => (
                                                            <div key={index} className="card-container">
                                                                <div className='space-between'>
                                                                    <div>
                                                                        {cr.designation}
                                                                        <br/>
                                                                        <span className='secondary'>
                                                                            {showAmount(cr.montant)}
                                                                        </span>
                                                                    </div>
                                                                    <div>
                                                                        <span onClick={() => handleEditCritere(cr)}>
                                                                            <AiTwotoneEdit size={20}/>
                                                                        </span>
                                                                        <span onClick={() => handleDeleteCritere(cr)}>
                                                                            <IoMdClose size={20}/>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        ))
                                                    }
                                                </>
                                            }
                                        </div>
                                    </>
                                }
                                {
                                    activeMenu == "contrat" &&
                                    <div className='card-container'>
                                        <InputCheckBox onChange={setSalForfait} label="Salaire forfaitaire" checked={salForfait}/>
                                        {
                                            (fonction && fonction.id == 12) &&
                                            <DualContainer>
                                                <InputService
                                                    required
                                                    currentSelect={service} 
                                                    setCurrentSelect={setService}/>
                                                <InputText label="Titre" required 
                                                    name="titre"
                                                    value={titre} 
                                                    onChange={setTitre}/>
                                            </DualContainer>
                                        }
                                        <DualContainer>
                                            <InputAgence
                                                required
                                                currentSelect={agence} 
                                                setCurrentSelect={setAgence}/>
                                            <InputSuggestion label="Salaire de base" required
                                                name="sal_base"
                                                value={salBase}
                                                setValue={setSalBase}
                                                suggestions={suggestionSalBase}
                                                type='number'/>
                                        </DualContainer>
                                        <DualContainer>
                                            <InputSelect required
                                                label="Nb heure contrat"
                                                selected={heureContrat}
                                                setSelected={setHeureContrat}
                                                options={[240, 312]}/>
                                            <InputSelect
                                                label="Nb heure convenu"
                                                selected={heureConvenu}
                                                setSelected={setHeureConvenu}
                                                options={[240, 312]}/>
                                        </DualContainer>
                                        <DualContainer>
                                            <InputSelect label="Catégorie professionnelle" 
                                                required
                                                selected={categorieProf} 
                                                setSelected={setCategorieProf} 
                                                options={categories}/>
                                            <InputText label="Perdiem" 
                                                value={perdiem} 
                                                onChange={setPerdiem} 
                                                type="number"/>
                                        </DualContainer>
                                        <DualContainer>
                                            <InputText label="Indemnité de déplacement" 
                                                value={indemDepl} 
                                                onChange={setIndemDepl} 
                                                type="number"/>
                                            <InputText label="Prime d'ancienneté" 
                                                value={primeAnc} 
                                                onChange={setPrimeAnc} 
                                                type="number"/>
                                        </DualContainer>
                                    </div>
                                }
                                {
                                    activeMenu == "stagiaire" &&
                                    <div className='card-container'>
                                        <DualContainer>
                                            <InputText label="Matricule stagiaire" 
                                                value={numStagiaire} 
                                                type="number"
                                                required={societe && societe.id == 3}
                                                onChange={setNumStagiaire}/>
                                            <InputDate label="Date embauche"
                                                required={societe && societe.id == 3} 
                                                value={dateEmbauche} 
                                                onChange={setDateEmbauche}/>
                                        </DualContainer>
                                    </div>
                                }
                                {
                                    activeMenu == "dgm" &&
                                    <div className='card-container'>
                                        <DualContainer>
                                            <InputText label="Matricule DGM" 
                                                value={numEmploye} 
                                                type="number"
                                                required={societe && societe.id == 1}
                                                onChange={setNumEmploye}/>
                                            <InputDate label="Date confirmation" 
                                                value={dateConfirmation} 
                                                required={societe && societe.id == 1}
                                                onChange={setDateConfirmation}/>
                                        </DualContainer>
                                    </div>
                                }
                                {
                                    activeMenu == "soit" &&
                                    <div className='card-container'>
                                        <DualContainer>
                                            <InputText label="Matricule SOIT" 
                                                value={numEmpSoit} 
                                                type="number"
                                                required={societe && societe.id == 2}
                                                onChange={setNumEmpSoit}/>
                                            <InputDate label="Date confirmation" 
                                                value={dateConfSoit} 
                                                required={societe && societe.id == 2}
                                                onChange={setDateConfSoit}/>
                                        </DualContainer>
                                    </div>
                                }
                                { 
                                    activeMenu == 'saoi' &&
                                    <div className="card-container">
                                        <DualContainer>
                                            <InputText label="Matricule SAOI"
                                                value={numEmpSaoi}
                                                type="number"
                                                required={societe && societe.id== 6}
                                                onChange={setNumEmpSaoi}/>
                                            <InputDate label="Date confirmation"
                                                value={dateConfSaoi}
                                                required={societe && societe.id== 6}
                                                onChange={setDateConfSaoi}/>
                                        </DualContainer>
                                    </div>
                                }
                                {
                                    activeMenu == "document" &&
                                    <div className='card-container'>
                                        <InputCheckBox label="CIN" onChange={setCin} checked={cin} name="cin"/>
                                        <div className='field-container'>
                                            <InputCheckBox label="CV" onChange={setCv} checked={cv} name="cv"/>
                                        </div>
                                        <div className='field-container'>
                                            <InputCheckBox label="Photo" onChange={setPhoto} checked={photo} name="photo"/>
                                        </div>
                                        <div className='field-container'>
                                            <InputCheckBox label="Certificat de résidence" onChange={setResidence} checked={residence} name="residence"/>
                                        </div>
                                        <div className='field-container'>
                                            <InputCheckBox label="Plan de repérage" onChange={setReperage} checked={reperage} name="reperage"/>
                                        </div>
                                        <div className='field-container'>
                                            <InputCheckBox label="Bulletin N3" onChange={setBulletin} checked={bulletin} name="bulletin"/>
                                        </div>
                                        <div className='field-container'>
                                            <InputCheckBox label="Certificat de bonne conduite" onChange={setBonneConduite} checked={bonneConduite} name="bonne_conduite"/>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                        }
                        <ButtonSubmit disabled={disabledSubmit} label="Enregistrer"/>
                        {
                            comfirmDefaultPv && auth.role == "resp_rh" &&
                                <ConfirmModal 
                                    msg="Voulez-vous charger le part variable par defaut ?" 
                                    confirmAction={() => { defaultPv(), toggleComfirmDefaultPv(false) }} 
                                    closeModal={() => toggleComfirmDefaultPv(false)}/>
                        }
                    </form>
                }
            </div>
        </div>
    )
}