import React, { useEffect, useState } from 'react';
import UpdateStockModal from '../modal/UpdateStockModal';
import ApprovisionnementModal from '../modal/ApprovisionnementModal';
export default function ActionStock({auth, stock, updateData}) {
    const [currentAction, setAction] = useState(null);
    const [showUpdateStockModal, toggleUpdateStockModal] = useState(false);
    const [showApprovisionnementModal, toggleApprovisionnementModal] = useState(false);

    useEffect(() => {
        setAction({
            header: "Approvisionnement",
            request : "/api/article/approvisionnement/" + stock.name,
            required: true
        })
    }, [showApprovisionnementModal])

    useEffect(() => {
        setAction({
            header: "Mise à jour du stock",
            request : "/api/article/update_stock/" + stock.name,
            required: true
        })
    }, [showUpdateStockModal])

    return <div>
        <div className="action-container">
            {
                showApprovisionnementModal &&
                <ApprovisionnementModal
                    action={currentAction}
                    data={stock}
                    closeModal={() => {toggleApprovisionnementModal(false)}}
                    updateData={updateData}
                />
            }
            {
                showUpdateStockModal &&
                <UpdateStockModal
                    action={currentAction}
                    data={stock}
                    closeModal={() => {toggleUpdateStockModal(false)}}
                    updateData={updateData}
                />
            }
            <span onClick={() => {toggleApprovisionnementModal(true)}}>
                Approvisionner
            </span>
            <span onClick={() => {toggleUpdateStockModal(true)}}>
                Mettre à jour le stock
            </span>
        </div>
    </div>
}