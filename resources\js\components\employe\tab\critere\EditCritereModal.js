import React, { useEffect, useState } from 'react';
import useToken from '../../../util/useToken';
import InputText from '../../../input/InputText';

export default function EditCritereModal({action, currentCritere, currentEmployeId, closeModal, updateData}) {
    const [designation, setDesignation] = useState("")
    const [montant, setMontant] = useState(0)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(true)

    const handleCancel = (e) => {
        closeModal()
    }

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)

        var formData = new FormData();
        formData.append("designation", designation)
        if(montant)
            formData.append("montant", montant)
        formData.append("employe_id", currentEmployeId)

        axios.post(action, formData, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success){
                updateData(true)
                closeModal()
            }                
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }

    useEffect(() => {
        if(currentCritere){
            setDesignation(currentCritere.designation)
            setMontant(currentCritere.montant)
        }
    }, [])

    useEffect(() => {
        disableSubmit(!(designation && designation.trim() && montant))

    }, [designation, montant]);

    return (
        <div className='modal'>
            <div style={{maxHeight: 'none'}}>
                <div className="title-container" >
                    <h2>{currentCritere ? "Modifer le critère" : "Ajouter un critère"}</h2>
                </div>
                <form onSubmit={handleSubmit}>
                    <div className='form-body'>
                        <InputText required
                            label="Désignation"
                            value={designation}
                            onChange={setDesignation}/>
                        <InputText required
                            type="number"
                            label="Montant"
                            value={montant}
                            onChange={setMontant}/>
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                        }
                    </div>
                    <div className="form-button-container">
                        <button className='primary' disabled={submitDisabled} type="submit"> {(currentCritere && currentCritere.index) ? "Modifier" : "Ajouter" }</button>
                        <button className='secondary' onClick={handleCancel}> Annuler </button>
                    </div>
                </form>
            </div>
        </div>
    )
}
