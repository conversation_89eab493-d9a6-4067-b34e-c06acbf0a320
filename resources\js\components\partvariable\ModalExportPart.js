import React, { useEffect, useState } from 'react'
import InputMont<PERSON><PERSON>ear from '../input/InputMonthYear'
import useToken from '../util/useToken'
import LoadingPage from '../loading/LoadingPage'
import PartExcel from './PartExcel'
import moment from 'moment'
import InputCheckBox from '../input/InputCheckBox'
import InputSelect from '../input/InputSelect'

export default function ModalExportPart({closeModal}) {
    const [datePaie, setDatePaie] = useState({ year: '', month: '' })
    const [partVariables, setPartVariables] = useState([])
    const [showNbResults, setshowNbResults] = useState(false)
    const [isGroupByUser, toggleGroupByUser] = useState(false)
    const [isGroupByFonction, toggleGroupByFonction] = useState(false)
    const [isDetail, toggleIsDetail] = useState(false)
    const typeList = [ "Fonction", "Responsable"]
    const [type, setType] = useState("Fonction")

    const onSearch = () => {
        setshowNbResults(false)
        let isMounted = true
        let date_paie = datePaie.year + '-' + datePaie.month + '-20';
        const params = new URLSearchParams()
        params.set('date_paie', date_paie)
        axios.get('/api/part_variable/get_part_export?' + params, useToken())
            .then((res) => {
                if (isMounted) {
                    let res_pvs = res.data.pvs
                    res_pvs.forEach(pv => {
                        if (pv.criteres.length > 0) {
                            pv.montant_total = pv.criteres.reduce((sum, item) => sum + item.montant, 0);
                        }
                    });
                    setPartVariables(res_pvs);
                    setshowNbResults(true)
                }
            
        })
        return ()=>{ isMounted = false};
    }

    useEffect(() => {
        if (datePaie && datePaie.year.trim() && datePaie.month.trim())
            onSearch()
    }, [datePaie])

    useEffect(() => {
        if (type.toLowerCase()=='fonction'){
            toggleGroupByUser(false)
            toggleGroupByFonction(true)
        }else{
            toggleGroupByUser(true)
            toggleGroupByFonction(false)
        }
    }, [type])

    return (
        <div className='modal'>
            <div>
                <h3>Export Part variable</h3>
                <InputMonthYear setDefaultDate label="Date Paie" value={datePaie} onChange={setDatePaie} defaultDate={moment()} required />
                <InputSelect label='Grouper par' selected={type}
                    setSelected={setType}
                    options={typeList}
                    required
                />
                {
                    showNbResults ? 
                        <div style={{ marginBottom: 10, marginTop: 10 }}>
                            {partVariables.length + " élement(s) trouvée(s)"}
                        </div>
                    :
                        <LoadingPage /> 
                }
                <InputCheckBox label='Détailler les critères' checked={isDetail} onChange={toggleIsDetail}/>
                <div className="form-button-container" style={{ marginTop: 50 }}>
                    {
                        showNbResults && partVariables.length > 0 &&
                            <PartExcel partVariables={partVariables}
                                datePaie={datePaie.year.toString() + "-" + datePaie.month}
                                isGroupByUser={isGroupByUser}
                                isGroupByFonction={isGroupByFonction}
                                isDetail={isDetail}
                                closeModal={closeModal}
                            />
                    }
                    <button type="button" onClick={closeModal}>
                        Annuler
                    </button>
                </div>
            </div>
        </div>
    )
}
