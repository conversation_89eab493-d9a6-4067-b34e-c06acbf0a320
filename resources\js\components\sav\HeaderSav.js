import React from 'react';
import moment from 'moment'

export default function HeaderSav({auth, data}) {
    return (
        <div>
            <h3>{data.site}</h3>
            <p style={{whiteSpace: "pre-line"}}>
                Motif: <span className='text'>{data.motif}</span>
            </p>
            {
                data.date_sav &&
                <div>
                    Date prévu: <span className='text'>{moment(data.date_sav).format("DD MMM YYYY")}</span>
                </div>
            }
            <div className='card-footer'>
                <span>
                    <span>Demandeur : </span>
                    <span className='text'>{data.user_nom} {" <" + data.user_email + ">"}</span>
                </span>
                <span className='secondary'>{moment(data.created_at).from(auth.datetime)}</span>
            </div>
        </div>
    )
}