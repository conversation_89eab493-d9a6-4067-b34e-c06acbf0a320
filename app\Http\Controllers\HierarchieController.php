<?php

namespace App\Http\Controllers;

use App\Models\Employe;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HierarchieController extends Controller
{
    function employe($id, Request $request){
        $employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi, f.libelle as 'fonction'
            FROM employes e
            LEFT JOIN fonctions f on f.id = e.fonction_id
            WHERE f.has_hierarchie = 1 and e.responsable_id = ?", [$id]);
        return response(compact('employes'));
    }

    function add($id, Request $request){
        if(in_array($request->user()->role, ["admin", "rh", "resp_rh"])){
            $responsable_id = $id;
            while ($responsable_id != $request->employe_id && $responsable_id != null) {
                $responsable = Employe::find($responsable_id);
                if($responsable)
                    $responsable_id = $responsable->responsable_id;
                else
                    $responsable_id = null;
            } 
            if($responsable_id == $request->employe_id)
                return \response()->json(['error' => "Erreur, hiérarchie en boucle"]);

            $employe = Employe::find($request->employe_id);
            $employe->responsable_id = $id;
            $employe->save();
            return response(["success" => "Employé hierarchie ajouté"]);
        }
        return response(["error" => "EACCES"]);
    }

    function remove($id, Request $request){
        if(in_array($request->user()->role, ["admin", "rh", "resp_rh"])){
            $employe = Employe::find($id);
            $employe->responsable_id = null;
            $employe->save();
            return response(["success" => "Employé supprimé de l'hierarchie"]);
        }
        return response(["error" => "EACCES"]);
    }
}
