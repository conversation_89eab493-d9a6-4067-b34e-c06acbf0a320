import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Link, Navigate } from 'react-router-dom';
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';

export default function Tech({auth}) {
    const [nbData, setData] = useState(null) 

    useEffect(() => {        
        let isMounted = true
        axios.get('/api/tech', useToken())
        .then(res => {
            setData(res.data)
        })
        .catch(err => {
            console.error(err)
        })
        return () => { isMounted = false }
    }, [])
    return (
        auth.role == "tech" ?
            <MenuView title="">
                {
                    nbData &&
                    (
                        (nbData.nb_sav != 0 || nbData.nb_sav_tag != 0 || nbData.nb_flotte != 0) ?
                        <div>
                            <h3 className='sub-title-menu'>TECH</h3>
                            <div className='palette-container'>
                                {
                                    nbData.nb_sav != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sav/autre?status=demande">SAV à traité</Link>
                                        <span className='badge-outline'>{nbData.nb_sav}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_sav_tag != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/sav/tag?status=demande">Tag et Rondier à traité</Link>
                                        <span className='badge-outline'>{nbData.nb_sav_tag}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_flotte != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/flotte?status=demande">Flotte à traité</Link>
                                        <span className='badge-outline'>{nbData.nb_flotte}</span>
                                    </div>
                                }
                            </div>
                        </div>
                        :
                        <div className='center secondary'>
                            <br/>
                            <br/>
                            <h3>Aucune demande pour l'instant</h3>
                        </div>                        
                    )
                }
            </MenuView>
        : 
            <Navigate to="/"/>
    );
}