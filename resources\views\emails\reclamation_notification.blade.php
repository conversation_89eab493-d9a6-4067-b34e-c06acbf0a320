<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Nouvelle Réclamation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .info-row {
            margin-bottom: 10px;
            padding: 5px 0;
        }
        .label {
            font-weight: bold;
            color: #495057;
        }
        .value {
            color: #212529;
        }
        .button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2 style="margin: 0; color: #dc3545;">🔔 Nouvelle Réclamation</h2>
        <p style="margin: 5px 0 0 0; color: #6c757d;">Une nouvelle réclamation a été créée dans le système</p>
    </div>

    <div class="content">
        <div class="info-row">
            <span class="label">Employé:</span>
            <span class="value">[{{ $reclamation['matricule'] }}] {{ $employe_display }}</span>
        </div>

        <div class="info-row">
            <span class="label">Site:</span>
            <span class="value">{{ $reclamation['site_nom'] }}</span>
        </div>

        <div class="info-row">
            <span class="label">Date de service:</span>
            <span class="value">{{ $reclamation['formatted_date_pointage'] }}</span>
        </div>

        @if($reclamation['type'])
        <div class="info-row">
            <span class="label">Type:</span>
            <span class="value">{{ $reclamation['type'] }}</span>
        </div>
        @endif

        <div class="info-row">
            <span class="label">Motif:</span>
            <span class="value">{{ $reclamation['motif'] }}</span>
        </div>

        <div class="info-row">
            <span class="label">Nombre d'heures réclamées:</span>
            <span class="value">{{ $reclamation['nb_heure'] }} heures</span>
        </div>

        @if($reclamation['superviseur_nom'])
        <div class="info-row">
            <span class="label">Superviseur assigné:</span>
            <span class="value">{{ $reclamation['superviseur_nom'] }} &lt;{{ $reclamation['superviseur_email'] }}&gt;</span>
        </div>
        @endif

        <div class="info-row">
            <span class="label">Demandeur:</span>
            <span class="value">{{ $reclamation['creator_nom'] }} &lt;{{ $reclamation['creator_email'] }}&gt;</span>
        </div>

        <div class="info-row">
            <span class="label">Date de création:</span>
            <span class="value">{{ $reclamation['formatted_created_at'] }}</span>
        </div>

        <div class="info-row">
            <span class="label">Statut:</span>
            <span class="value">{{ ucfirst($reclamation['status']) }}</span>
        </div>

        <a href="{{ $view_url }}" class="button">📋 Voir les détails de la réclamation</a>
    </div>

    <div class="footer">
        <p>Cet email a été envoyé automatiquement par le système de gestion DRX Admin.</p>
        <p>Veuillez ne pas répondre directement à cet email.</p>
    </div>
</body>
</html>
