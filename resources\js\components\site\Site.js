import React, { useEffect, useState } from 'react';
import { useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import StatusLabel from '../input/StatusLabel';
import ModalExportSite from './ModalExportSite';

export default function Site({auth, sites, setSites, currentId, setCurrentId}) {
    const locationSearch = useLocation().search
    const searchParams = new URLSearchParams(locationSearch);
    const isAlarm = searchParams.get('date-alarm')
    const siteId = searchParams.get('id')
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [showExport, toggleShowExport] = useState(false)

    let searchItems = [
        {label: 'Actif', name: 'actif', type:'number'},
        {label: 'Référence', name: 'id', type:'number'},
        {label: 'Nom', name: 'nom', type:'string'},
        {label: 'Status', name: 'status', type: 'number'},
        {label: 'Superviseur non défini', name: 'sup_ndf', type:'number'},
        {label: 'Manager non défini', name: 'resp_sup_ndf', type:'number'},
        {label: 'Pricipale archivé', name: 'site_principale_archive', type:'number'},
    ]
    if(auth.role != "superviseur")
        searchItems.push({label: 'Superviseur', name: 'superviseur_id', type:'superviseur_id'})
        
    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            params.set("offset", 0)
        }
        else
            params.set("offset", sites.length)
        axios.get('/api/site?' + params,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setSites(res.data.sites)
                    else {
                        const list = sites.slice().concat(res.data.sites)
                        setSites(list)
                    }
                    setDataLoaded(res.data.sites.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch])
    useEffect(() => {
        if(sites.length == 1 && siteId && isAlarm){
            setCurrentId(sites[0].id)
        }
    }, [sites])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>Site</h2>
                {/* {   
                    ['resp_sup', 'resp_op'].includes(auth.role) && 
                    <Link className='btn btn-primary' to="/site/add">Nouvelle site</Link>
                } */}
            </div>
            <SearchBar listItems={searchItems}/>
            {
                sites.length == 0 ? 
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                    <div>
                        {
                            ['resp_sup', 'resp_op', 'validateur'].includes(auth.role) && 
                            <div className="action-container">
                                <span style={{ paddingLeft: 10 }} onClick={() => toggleShowExport(true)}>Exporter</span>
                            </div>
                        }
                        {
                            showExport &&
                            <div>
                                <ModalExportSite auth={auth} closeModal={() => toggleShowExport(false)} />
                            </div>
                        }
                        <InfiniteScroll
                            dataLength={sites.length}
                            next={fetchMoreData}
                            hasMore={!allDataLoaded}
                            loader={<LoadingPage/>}
                        >
                            <div className="line-container ">
                                <div className='row-employe'>
                                    <b className='line-cell-lg'>Site</b>
                                    <b className='line-cell-sm'>Adresse</b>
                                    <b className="status-line">
                                        <StatusLabel color={"grey"} />
                                    </b>
                                    <b>Manager</b>
                                </div>
                            </div>
                            {
                                sites.map((s) => (
                                    <div className={`table line-container ${currentId && currentId == s.id ? 'selected' : ''}`}  key={s.id}>
                                        <div className="row-employe" onClick={() => setCurrentId(s.id)}>
                                            <span className='line-cell-lg'>
                                                { s.nom }
                                            </span>
                                            <span className='line-cell-sm'>
                                                { s.adresse }
                                            </span>
                                            <span className="status-line">
                                                <StatusLabel color={s.soft_delete ? "pink" : s.pointeuse ? "cyan" : "grey"} />
                                            </span>
                                            <span>
                                                { s.resp_sup_name }
                                                { s.resp_sup_email ? ' <' + s.resp_sup_email + '>' : '' }
                                            </span>
                                        </div>
                                    </div>
                                ))
                            }
                        </InfiniteScroll>
                    </div>
            }
        </div>
    } </>
}