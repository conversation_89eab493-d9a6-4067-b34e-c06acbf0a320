import React, { useEffect, useState } from 'react';
import { Link, useLocation, useParams  } from "react-router-dom";
import moment from 'moment'

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import StatusLabel from '../input/StatusLabel';
import matricule from '../util/matricule';

export default function Absence({auth, absences, setAbsences, currentId, setCurrentId}) {
    const params = useParams();
    const locationSearch = useLocation().search
    const [currentType, setCurrentType] = useState()
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'Réf<PERSON><PERSON>ce', name: 'id', type:'number'},
        {label: 'Status', name: 'status', type:'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Demandeur', name: 'user_id', type:'number'},
        {label: 'Employe', name: 'employe_id', type:'number'},
        {label: 'Site', name: 'site_id', type:'number'},
    ]
    if ((currentType == 'mis_a_pied' || params.type == 'mis_a_pied')&& auth.role != 'superviseur')
        searchItems.push({ label: 'Superviseur', name: 'superviseur_id', type: 'superviseur_id' })
    else if(auth.role != 'superviseur' )
        searchItems.push({ label: 'Superviseur', name: 'superviseur_id', type: 'number' })
    if (currentType && currentType.name == 'conge')
        searchItems.push({ label: 'Fictif', name: 'conge_fictif', type: 'number' })
    useEffect(() => {
        let isMounted = true
        if(params.type){
            axios.get('/api/type_absence/show/' + params.type, useToken())
            .then((res) => {
                if (isMounted) {
                    if(!currentType || currentType.name != res.data)
                        setCurrentType(res.data)
                }
            })
        }
        return () => { isMounted = false };
    }, [params.type])

    const updateData = (initial) => {
        let isMounted = true
        if(currentType){
            const searchParams = new URLSearchParams(locationSearch)
            if(initial){
                toggleLoading(true)
                setDataLoaded(true)
                setCurrentId(null)
                searchParams.set("offset", 0)
            }
            else
                searchParams.set("offset", absences.length)
            axios.get('/api/absence/' + currentType.name + "?" + searchParams
            , useToken())
            .then((res) => {
                if(isMounted) {
                    if(res.data.error)
                        console.error(res.data.error)
                    else {
                        if(initial)setAbsences(res.data.absences)
                        else {
                            const list = absences.slice().concat(res.data.absences)
                            setAbsences(list)
                        }
                        setDataLoaded(res.data.absences.length < 30)
                    }
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e)
            })
        }
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch, currentType])

    const fetchMoreData = () => {
        setTimeout(() => {
            updateData()
        }, 300);
    };

    const formatNumber = (nb) => {
        const roundedNumber = Math.round(nb * 10) / 10; // Arrondi à une décimale
        const formattedNumber = roundedNumber % 1 === 0 ? roundedNumber.toFixed(0) : roundedNumber.toFixed(1);
        return formattedNumber;
    }

    return (
        (!currentType || isLoading) ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>
                    {
                        currentType && currentType.designation
                    }
                </h2>
                {   
                    (currentType.name != "mis_a_pied" || ['rh', 'resp_rh'].includes(auth.role)) &&
                        <Link className='btn btn-primary' to={("/absence/add") + (params.type?("/"+params.type):"")}>
                            Nouveau
                        </Link>
                }
            </div>
            <SearchBar listItems={searchItems}/>
            {
                absences.length == 0 ? 
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                    <InfiniteScroll
                        dataLength={absences.length}
                        next={fetchMoreData}
                        hasMore={!allDataLoaded}
                        loader={<LoadingPage/>}
                    >
                        <div className="line-container">
                            <div className="row-list">
                                <b className="nom-employe" style={{ width: "40%", minWidth: "40%" }}>Agent</b>
                                <b className="status-line">
                                    <StatusLabel color={"grey"} />
                                </b>
                                {currentType.name == 'mis_a_pied' ?
                                    <>
                                        <b className="nb-jour">Jour</b>
                                        <b className='superviseur'>{auth.role == "superviseur" ? "Site" : "Superviseur"}</b>
                                    </>
                                    :
                                    <>
                                        <b className="date-depart">Départ</b>
                                        <b className="nb-jour">Jour</b>
                                        <b className="site">Site</b>    
                                    </>
                                }    
                            </div>
                        </div>    
                        {absences.map((abs) => (
                            (   abs.superviseur_id == auth.id ||
                                (["rh", "resp_rh", "access", "validateur","resp_sup", "resp_op"].includes(auth.role)) ||
                                currentType.name != "mis_a_pied"
                            ) &&
                            
                            <div onClick={()=>setCurrentId(abs.id)} className={`line-container ${
                                            currentId && currentId == abs.id? "selected": "" }`} key={abs.id}>
                                <div>
                                    <div className="row-list">
                                        <span className="nom-employe" style={{ width: "40%", minWidth: "40%" }}>
                                            {matricule(abs)}{" "}
                                            {abs.employe}
                                        </span>
                                        <span className="status-line">
                                                <StatusLabel color={abs.status_color} />
                                        </span>
                                        {currentType.name == "mis_a_pied" ?
                                            <>
                                                <span className="nb-jour">{abs.depart ? (" " + moment(abs.retour).diff(moment(abs.depart), 'hours') / 24 ): abs.nb_jour}</span>    
                                                <span className="superviseur">
                                                    {auth.role == "superviseur" ? abs.site : (abs.sup_nom + "<" + abs.sup_email + ">")}
                                                </span>   
                                            </>
                                            :
                                            <>
                                                <span className="date-depart">{abs.depart && (moment(abs.depart).format("DD/MM/YY") + (moment(abs.depart).format("HH:mm:ss") == "06:00:00" ? " JOUR" : " NUIT"))}</span>
                                                {/* <span className="nb-jour">{abs.depart ? (" " + moment(abs.retour).diff(moment(abs.depart), 'hours') / 24) : abs.nb_jour}</span> */}
                                                <span className="nb-jour">{abs.depart ? (" " + formatNumber(moment(abs.retour).diff(moment(abs.depart), 'hours') / 24) ): abs.nb_jour}</span>
                                                    {/* <span className="nb-jour">{abs.depart ? (" " + moment.duration(moment(abs.depart).diff(moment(abs.retour)))).asDays() : abs.nb_jour}</span> */}
                                                <span className="site-employe">{ abs.site }</span>
                                            </>

                                        }
                                    </div>
                                </div>
                            </div>
                        ))}       
                            
                        {/* {
                            absences.map((cg) => (
                                <div className={`card-container ${currentId && currentId == cg.id ? 'selected' : ''}`} key={cg.id}>
                                    <div className='badge-container'>
                                        <span>
                                            <span className={'badge-outline badge-outline-' + cg.status_color}>
                                                {cg.status_description}
                                            </span> {
                                                    cg.nb_pj > 0 &&
                                                    <span className='badge-outline'>
                                                        Pièce jointe : {cg.nb_pj}
                                                    </span>
                                            }
                                        </span>
                                        <span className='pointer' onClick={() => setCurrentId(cg.id)}>
                                            <FiMoreVertical size={20} color="#888"/>
                                        </span>
                                    </div>
                                    <HeaderAbsence auth={auth} data={cg}/>
                                </div>
                            ))
                        } */}
                            
                    </InfiniteScroll>
            }
        </div>
    )
}