<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\HistoriqueController;
use App\Http\Controllers\MailController;
use Illuminate\Support\Facades\DB;
use Hash;

class UserAuthController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }
    
    public function auth(Request $request){
        $user = $request->user();
        $user->datetime = (new \DateTime())->format("Y-m-d H:i:s");
        $user->notification = count(DB::select("SELECT id FROM notifications 
            WHERE (seen = 0 or seen is null) and receiver_id = ?", [$request->user()->id]));
        $user->nb_pv = count(DB::select("SELECT id FROM employes WHERE resp_part_id = ?", [$request->user()->employe_id]));
        $user->note_message = count(DB::select("SELECT id FROM note_messages 
            WHERE (seen = 0 or seen is null) and user_id = ?", [$request->user()->id]));
        $services = Service::orderBy('designation')->get();
        $notes = DB::select("SELECT u.service_id, coalesce(count(n.id), 0) as 'nb_unread' FROM note_messages n 
            LEFT JOIN messages m ON m.id = n.message_id 
            LEFT JOIN users u ON u.id = m.user_id 
            WHERE (n.seen is null or n.seen = 0) and n.user_id = ? 
            GROUP BY u.service_id", [$request->user()->id]);
        foreach ($services as $service) {
            foreach ($notes as $n) {
                if($n->service_id == $service->id)
                    $service->nb_unread = $n->nb_unread;
            }
        }
        $consigne_non_lu = DB::select("SELECT coalesce(count(n.id), 0) as 'unread' FROM note_messages n
            LEFT JOIN messages ms ON ms.id = n.message_id
            WHERE (n.seen is null or n.seen = 0) and n.follow = 1 and ms.user_id = ?", [$request->user()->id])[0]->unread;
        $user->services = $services;
        $user->consigne_non_lu = $consigne_non_lu;
        $user->region = DB::select("
            SELECT GROUP_CONCAT(region_id ORDER BY region_id SEPARATOR ', ') AS regions 
            FROM region_users 
            WHERE user_id = ?", 
            [$request->user()->id]
        )[0]->regions ?? null;
        return response($user); 
    }

    public function register(Request $request){
        $auth = $request->user();
        if(in_array($auth->role, ["admin"])){
            $user = new User();
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'email' => 'required|email|unique:users',
                'role' => 'required',
                'email_password' => 'required',
                'real_email_id' => 'numeric',
                //'employe_id' => 'required'
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $user->password = 123456;

            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = bcrypt('123456');
            $user->role = $request->role;
            $user->email_password = $request->email_password;
            $user->real_email_id = $request->real_email_id;
            //$user->employe_id = $request->employe_id;
            $user->must_change_password = 1;
            if($user->save()){
                $user_id = $user->id;
                HistoriqueController::new_user($request, $user);             
                return response(["success" => "Utilisateur bien enregistré", "id" => $user->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function reset_password(Request $request, $id){
        $user = User::find($id);
        $auth = $request->user();
        if($auth->role == "admin" && $user->type != "parent"){
            if($request->blocked){
                $validator = Validator::make($request->all(), [
                    'note' => 'required',
                ]);    
            }
            else {
                $validator = Validator::make($request->all(), [
                    'note' => 'required',
                    'employe_id' => 'required'
                ]);    
            }    
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            $charactersLength = strlen($characters);
            $randomString = '';
            for ($i = 0; $i < 6; $i++) {
                $randomString .= $characters[random_int(0, $charactersLength - 1)];
            }
            $user->password = bcrypt($randomString);
            $request->email = $user->email;
            $request->password = $randomString;
            $user->must_change_password = 1;
            $user->blocked = $request->blocked;
            if($request->blocked){
                $user->employe_id = null;
            }
            else {
                $user->employe_id = $request->employe_id;
            }
            if($user->save()){
                HistoriqueController::action_user($request, "Mot de passe réinitialisé", $id);
                if($user->type == "fictif") {
                    $emails = [];
                    $emails[] = ['address' => "<EMAIL>", 'name' => 'Aro'];
                    $emails[] = ['address' => "<EMAIL>", 'name' => 'Lova'];
                    $emails[] = ['address' => "<EMAIL>", 'name' => 'Armel'];
                }
                else {
                    $users = DB::select("SELECT us.name, coalesce(ur.email, us.email) as 'email'  
                        from users us
                        left join users ur on ur.id = us.real_email_id 
                        where us.id = ?", [$id]);
                    $emails = [];
                    $u = $users[0];
                    $emails[] = ['address' => $u->email, 'name' => $u->name];
                }
                if(!$request->blocked){
                    MailController::user($request, $user->id, "Réinitialisation Mot de passe", $emails);
                    foreach($user->tokens as $token) {
                        $token->revoke();
                    }
                }
                return response(["success" => "Mot de passe réinitialisé", "id" => $user->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function login(Request $request)
    {
        $data = $request->validate([
            'email' => 'email|required',
            'password' => 'required'
        ]);

        if (!auth()->attempt($data) || auth()->user()->blocked){// || auth()->user()->type != "parent") {
            return response(['error' => 'Information incorrect, réessayer']);
        }

        $token = auth()->user()->createToken('API Token')->accessToken;

        return response(['user' => auth()->user(), 'token' => $token]);

    }

    public function logout(Request $request){
        $accessToken = auth()->user()->token();
        $token= $request->user()->tokens->find($accessToken);
        $token->revoke();
        return response(['message' => 'You have been successfully logged out.'], 200);
    }

    public function change_password(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|min:6',
            'new_password' => 'required|confirmed|min:6',
        ]);
        if ($validator->fails())
            return response(['error' => $validator->errors()->first()]);
        else if($request->current_password == $request->new_password)
            return response(["error" => "Le nouveau mot de passe doit être different du mot de passe actuel."]);
        else if(!password_verify($request->current_password, $request->user()->password))
            return response(["error" => "Mot de passe actuel incorrecte."]);
        else {
            foreach ($request->user()->tokens as $token) {
                $token->revoke();
            }
            User::where('id', $request->user()->id)->update([
                "must_change_password" => null, 
                "password" => bcrypt($request->new_password)
            ]);
        }
        return true;
    }
}