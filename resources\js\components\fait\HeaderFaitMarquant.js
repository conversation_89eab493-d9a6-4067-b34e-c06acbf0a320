import moment from 'moment';
import React from 'react';

export default function HeaderFaitMarquant({auth, data}) {
    return (
        <div>
            <h3>
                {data.site}
            </h3>
            <div>
                Objet: <span className='text'>{data.objet}</span>
            </div>
            <p className='text' style={{whiteSpace: "pre-line"}}>
                {data.commentaire}
            </p>
            <span>
                <span>{
                    data.user_role == "superviseur" ? "Superviseur" 
                    : data.user_role == "room" ? "Opérateur"
                    : "Utilisateur"} : </span>
                <span className='text'>{data.user_nom} {" <" + data.user_email + ">"}</span>
            </span>
            <div className='card-footer'>
                <span>
                    {
                        data.date_visite &&
                        <span>Descente du : </span>
                    }
                    <span className='text'>
                        {data.date_visite && moment(data.date_visite).format('LL')}
                        {data.start && data.end && (
                            <> de {moment(data.start).format('HH[h]mm')} à {moment(data.end).format('HH[h]mm')}</>
                        )}
                    </span>
                </span>
                <span className='secondary'>{moment(data.created_at).from(auth.datetime)}</span>
            </div>
        </div>
    )
}