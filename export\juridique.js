const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const matricule = require("../resources/js/components/util/matricule")

moment.locale('fr')

const {db_config_admin, sendMail} = require("../auth")
const pool = mysql.createPool(db_config_admin)

const isTask = (process.argv[2] == "task")
const destination_rh = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

function generateRecouvrementExcelFile(workbook, data){
    const borderStyle = {
        top: {style:'thin'},
        left: {style:'thin'},
        bottom: {style:'thin'},
        right: {style:'thin'}
    }
    const fontHeader = {
        size: 16,
        bold: true
    }
    const fontBold = {
        bold: true
    }
    data.forEach(et => {
        const worksheet = workbook.addWorksheet(et.nom)
        worksheet.getColumn('A').width = 10
        worksheet.getColumn('B').width = 40
        worksheet.getColumn('C').width = 40
        worksheet.getColumn('D').width = 20
        worksheet.getColumn('E').width = 50
        worksheet.getColumn('F').width = 100

        worksheet.getCell('A1').value = et.nom + " (" + et.recouvrements.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:F1')

        worksheet.getCell('A2').value = "Réference"
        worksheet.getCell('A2').border = borderStyle
        worksheet.getCell('A2').font = fontBold
        worksheet.getCell('B2').value = "Débiteur"
        worksheet.getCell('B2').border = borderStyle
        worksheet.getCell('B2').font = fontBold
        worksheet.getCell('C2').value = "Contrat"
        worksheet.getCell('C2').border = borderStyle
        worksheet.getCell('C2').font = fontBold
        worksheet.getCell('D2').value = "Montant"
        worksheet.getCell('D2').border = borderStyle
        worksheet.getCell('D2').font = fontBold
        worksheet.getCell('E2').value = "Facture"
        worksheet.getCell('E2').border = borderStyle
        worksheet.getCell('E2').font = fontBold
        worksheet.getCell('F2').value = "Dernière suivi"
        worksheet.getCell('F2').border = borderStyle
        worksheet.getCell('F2').font = fontBold

        let line = 3
        et.recouvrements.forEach(rec => {
            worksheet.getCell('A' + line).value = rec.reference
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = rec.debiteur
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = rec.contrat
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('D' + line).value = rec.montant
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('E' + line).value = rec.facture
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('F' + line).value = rec.commentaire
            worksheet.getCell('F' + line).border = borderStyle
            line++
        })
    });
}

const sqlSelectDateExport = "SELECT value FROM params p WHERE p.key = 'last_recouvrement_juridique'"
const sqlUpdateLastExport = "UPDATE params p SET p.value = DATE_FORMAT(NOW(), '%Y-%m-%d') WHERE p.key = 'last_recouvrement_juridique'"

const sqlSelectRecouvrement = "SELECT j.id, j.etape_id, j.reference, j.debiteur, j.contrat, j.facture, j.montant, " +
    "j.created_at, s.commentaire " +
    "FROM juridiques j " +
	"LEFT JOIN suivi_juridiques s ON s.id = j.last_suivi_id " +
    "ORDER BY j.created_at "
const sqlSelectEtapeContentieux = "SELECT e.id, e.nom FROM etape_contentieux e "

function doExportRh(){
	console.log("export rh report...")
    pool.query(sqlSelectEtapeContentieux, [], async (err, etapes) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb etape: " + etapes.length)
            pool.query(sqlSelectRecouvrement, [], async (err, recouvrements) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb recouvrement: " + recouvrements.length)
                    etapes.forEach(et => {
                        et.recouvrements = []
                        recouvrements.forEach(rec => {
                            if(rec.etape_id == et.id)
                                et.recouvrements.push(rec)
                        });
                    });
                    const workbook = new Excel.Workbook()
                    generateRecouvrementExcelFile(workbook, etapes)
                    const recouvrementBuffer = await workbook.xlsx.writeBuffer()
                    sendMail(
                        pool,
                        isTask ? destination_rh : destination_test,
                        "Rapport de recouvrement juridique " + moment().format("DD-MM-YYYY"), 
                        "<p>Veuillez trouver ci-joint les recouvrements juridiques classé par niveau de traitement.</p>" +
                        "<ul>" +
                            etapes.map(et => "<li>" + et.nom + " : " + et.recouvrements.length + "</li>").join(" ") +
                        "</ul>",
                        [
                            {
                                filename: "Recouvrement juridique " + moment().format("DD-MM-YYYY") + ".xlsx",
                                content: recouvrementBuffer
                            }
                        ],
                        (response) => {
                            if(response && process.argv[2] == 'task')
                                pool.query(sqlUpdateLastExport, [], (e, r) =>{
                                    console.error(e)
                                    console.log("update last rh export: " + r)
                                    process.exit(1)
                                })
                            else
                                process.exit(1)
                        }, 
                        isTask
                    )
                }
            })
        }
    })
}

if(process.argv[2] == 'test'){
    console.log("send test...")
    doExportRh()
}
else if(isTask){
    if(moment().day() == 5 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
        pool.query(sqlSelectDateExport, [], (err, lastExport) => {
            if(err)
                console.error(err)
            else if(lastExport && moment().format("YYYY-MM-DD") == lastExport[0].value){
                console.log("export rh already done!")
                process.exit()
            }
            else
                doExportRh()
        })
    }
    else {
        console.log("Not Monday, skip export RH.")
    }
}
else
    console.log("please specify command!")