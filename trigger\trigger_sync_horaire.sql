drop trigger IF EXISTS before_updated_horaire;

DELIMITER |
CREATE TRIGGER before_updated_horaire
BEFORE UPDATE
ON horaire_effectifs FOR EACH ROW
BEGIN
    if(NEW.site_id != OLD.site_id or NEW.day_1 != OLD.day_1 or NEW.night_1 != OLD.night_1 or NEW.day_2 != OLD.day_2 or NEW.night_2  != OLD.night_2 or
    NEW.day_3 != OLD.day_3 or NEW.night_3 != OLD.night_3 or NEW.day_4 != OLD.day_4 or NEW.night_4 != OLD.night_4 or NEW.day_5 != OLD.day_5 or 
    NEW.night_5 != OLD.night_5 or NEW.day_6 != OLD.day_6 or NEW.night_6 != OLD.night_6 or NEW.day_0 != OLD.day_0 or NEW.night_0 != OLD.night_0 or 
    NEW.day_ferie  != OLD.day_ferie  or NEW.night_ferie != OLD.night_ferie)
        then
            set NEW.admin_updated_at = now();
	end if;
END
| DELIMITER ;