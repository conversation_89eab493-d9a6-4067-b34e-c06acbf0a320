import React, { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom';
import InputSelect from '../input/InputSelect';

export default function TypeAnomalieModal({closeModal, useLink}) {
    const navigate = useNavigate()
    const location = useLocation()

    const [selectType, setSelectType] = useState()
    const handleOk = () => {
        if (useLink) {
            let params = new URLSearchParams(location.search);
            params.set("type_anomalie", selectType.value);
            navigate(location.pathname + "?" + params);
        }
        closeModal();
    };
    return (
        <div className='modal'>
            <div>
                <h3>Type d'anomalie</h3>
                <InputSelect value={selectType} setSelected={setSelectType} 
                    options={[
                        {label: "Incohérence", value: "incoherence"},
                        {label: "Manque", value: "manque"},
                        {label: "Surplus", value: "surplus"},
                    ]}
                />
                <div className='form-button-container'>
                    <button className='btn-primary' onClick={handleOk}>OK</button>
                    <button onClick={closeModal}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
