import React, { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom';
import InputSelect from './InputSelect';
import InputDate from './InputDate';
import moment from 'moment';

export default function InputDateService({closeModal, useLink}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [selectedDate, setDate] = useState()
    const [selectService, setSelectService] = useState({})
    const handleOk = () => {
        if (useLink && selectService?.value?.trim()) {
            let params = new URLSearchParams(location.search);
            params.set("date_service", moment(selectedDate).format("YYYY-MM-DD") + selectService.value);
            navigate(location.pathname + "?" + params);
        }
        closeModal();
    };
    return <div className='modal'>
        <div>
            <InputDate label="Date" value={selectedDate} onChange={setDate} required/>
            <InputSelect
                required
                label="Service"
                selected={selectService} 
                setSelected={setSelectService}
                options = {[
                    {label: "JOUR", value: " 06:00:00"},
                    {label: "NUIT", value: " 18:00:00"},
                ]}
            />
            <div className='form-button-container'>
                <button disabled={!selectedDate} className='btn-primary' onClick={handleOk}>OK</button>
                <button onClick={closeModal}>Annuler</button>
            </div>
        </div>
    </div>
}
