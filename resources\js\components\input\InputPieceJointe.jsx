import React, { useState } from 'react';
import axios from 'axios';

import InputText from './InputText';
import useToken from '../util/useToken';

export default function InputPieceJointe({title, value, onChange, required, disabled}) {
    const [modalOpen, toggleModal] = useState(false)
    const [pj, setPj] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const data = new FormData()
        data.append('nature',  title)
        data.append('pj',  pj)
        
        axios.post("/api/piece_jointe/add/da", data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.error)
                setError(res.data.error)
            else {
                toggleModal(false)
                onChange(res.data)
            }
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }

    return (
        <div>
            <div className='input-container'>
                <label>Pièce jointe {required && <span className='danger'>*</span>}</label>
                <input
                    type="text" 
                    value={value ? value.nature + ' <' + value.path + '>' : ''}
                    readOnly
                    disabled={disabled}
                    onClick={() => {toggleModal(true)}}
                    />
            </div>
            {
                modalOpen &&
                <div className='modal'>
                    <div>
                        <div className="title-container" >
                            <h2>{title}</h2>
                        </div>
                        <form onSubmit={handleSubmit} encType="multipart/form-data">
                            <div>
                                <InputText
                                    required 
                                    label="Pièce jointe"                                                
                                    onChange={setPj}
                                    type="file"
                                    accept=".jpg,.png"/>
                            </div>
                            {
                                error &&
                                <div className='container-error'>
                                    {error}
                                </div>
                            }
                            <div className="form-button-container">
                                <button className='primary' disabled={submitDisabled} type="submit"> Ok </button>
                                <button className='secondary' onClick={() => toggleModal(false)}> Annuler </button>
                            </div>
                        </form>
                    </div>
                </div>
            }
        </div>
    )
}