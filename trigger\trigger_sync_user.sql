drop trigger IF EXISTS before_update_admin_user;

DELIMITER |
CREATE TRIGGER before_update_admin_user
BEFORE UPDATE
ON users FOR EACH ROW
BEGIN
    if(NEW.name != OLD.name or NEW.email != OLD.email or NEW.role != OLD.role 
        or coalesce(NEW.blocked, 0) != coalesce(OLD.blocked, 0) or NEW.flotte != OLD.flotte
    ) THEN
        BEGIN
            SET NEW.admin_updated_at = now();
        END;
    end if;
END
| DELIMITER ;