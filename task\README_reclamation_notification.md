# Reclamation Email Notification System

This Node.js script automatically sends email notifications to relevant supervisors and operators when a new reclamation is created in the system.

## Overview

The script identifies and notifies the following user roles when a reclamation is created:
- **resp_sup** (Responsible Supervisor) - assigned to the specific site
- **superviseur** (Supervisor) - assigned to the specific site  
- **resp_op** (Responsible Operator) - assigned to the region containing the site
- **op** (Operator) - all operators receive notifications for all reclamations

## Files Created

1. **`task/send_reclamation_notification.js`** - Main notification script
2. **`task/test_reclamation_notification.js`** - Test script for verification
3. **`resources/views/emails/reclamation_notification.blade.php`** - Email template (optional, script uses built-in HTML)

## Usage

### Command Line Execution

```bash
# Send notification for a specific reclamation ID
node task/send_reclamation_notification.js <reclamation_id>

# Example
node task/send_reclamation_notification.js 1234
```

### Programmatic Usage

```javascript
const { sendReclamationNotification } = require('./task/send_reclamation_notification');

// Send notification
try {
    await sendReclamationNotification(reclamationId);
    console.log('Notification sent successfully');
} catch (error) {
    console.error('Failed to send notification:', error);
}
```

### Integration with Reclamation Creation

To automatically send notifications when a reclamation is created, add this to your ReclamationController:

```php
// In ReclamationController.php store method, after successful save
if($reclamation->save()) {
    HistoriqueController::new_reclamation($request, $reclamation);
    
    // Send email notification
    $command = "node " . base_path('task/send_reclamation_notification.js') . " " . $reclamation->id;
    exec($command . " > /dev/null 2>&1 &"); // Run in background
    
    return response(["success" => "Réclamation enregistré", "id" => $reclamation->id]);
}
```

## Email Content

The notification email includes:
- Employee information (name and matricule)
- Site name
- Date and time of service (JOUR/NUIT)
- Reclamation type
- Reason/motif
- Number of hours claimed
- Assigned supervisor (if any)
- Requester information
- Creation date and time
- Current status
- Direct link to view reclamation details

## Configuration

The script uses existing configuration from:
- **Database**: `auth.js` - `db_config_admin`
- **Email**: `auth.js` - `email_config`

### Environment Variables

Set the following environment variable for proper URL generation:
```bash
APP_URL=https://admin.dirickxguard.mg
```

## Database Dependencies

The script queries the following tables:
- `reclamations` - Main reclamation data
- `employes` - Employee information
- `sites` - Site information and supervisor assignments
- `users` - User accounts and email addresses
- `region_users` - Regional operator assignments
- `pointage_reclamations` - To calculate hours claimed

## Testing

Use the test script to verify functionality:

```bash
# Test with an existing reclamation ID
node task/test_reclamation_notification.js 1234
```

## Logging

The script provides detailed logging including:
- Reclamation details found
- Number of users to notify
- Email sending status for each recipient
- Summary of successful/failed sends
- Error details for failed sends

## Error Handling

The script handles various error scenarios:
- Reclamation not found
- No users to notify
- Email sending failures
- Database connection issues

## Dependencies

Required Node.js packages (already included in package.json):
- `moment` - Date formatting
- `mysql2` - Database connectivity
- `nodemailer` - Email sending

## Security Considerations

- Email credentials are stored in `auth.js`
- Database credentials are stored in `auth.js`
- Script validates reclamation existence before sending
- Only sends to active, non-blocked users
- Uses real email addresses when available (via `real_email_id`)

## Performance

- Sends emails in parallel using Promise.all()
- Uses connection pooling for database queries
- Minimal database queries (3 total per execution)
- Lightweight HTML email template

## Troubleshooting

### Common Issues

1. **"Reclamation not found"**
   - Verify the reclamation ID exists in the database
   - Check database connection

2. **"No users found to notify"**
   - Verify site has assigned supervisors
   - Check if users have valid email addresses
   - Ensure users are not blocked

3. **Email sending failures**
   - Verify email configuration in `auth.js`
   - Check SMTP server connectivity
   - Validate recipient email addresses

### Debug Mode

Add debug logging by modifying the script:
```javascript
// Add at the top of the file
const DEBUG = true;

// Use throughout the script
if (DEBUG) console.log('Debug info:', data);
```

## Future Enhancements

Potential improvements:
- Email template customization per role
- Configurable notification rules
- Email delivery status tracking
- Retry mechanism for failed sends
- Batch processing for multiple reclamations
