const moment = require('moment');
const mysql = require('mysql2');
const nodemailer = require('nodemailer');

moment.locale('fr');

const { db_config_admin, email_config } = require("../auth");
const pool = mysql.createPool(db_config_admin);

// SQL queries
const sqlGetReclamationDetails = `
    SELECT
        r.id,
        r.employe_id,
        r.site_id,
        r.superviseur_id,
        r.motif,
        r.type,
        r.date_pointage,
        r.agent_not_registered,
        r.created_at,
        r.status,
        e.nom as employe_nom,
        e.numero_employe,
        e.numero_stagiaire,
        e.num_emp_soit,
        e.num_emp_saoi,
        e.societe_id,
        s.nom as site_nom,
        u.name as superviseur_nom,
        u.email as superviseur_email,
        creator.name as creator_nom,
        creator.email as creator_email
    FROM reclamations r
    LEFT JOIN employes e ON e.id = r.employe_id
    LEFT JOIN sites s ON s.idSite = r.site_id
    LEFT JOIN users u ON u.id = r.superviseur_id
    LEFT JOIN users creator ON creator.id = r.admin_user_id
    WHERE r.id = ?
`;

const sqlGetSiteUsers = `
    SELECT DISTINCT
        u.id,
        u.name,
        u.email,
        u.role,
        COALESCE(ur.email, u.email) as real_email
    FROM (
        -- Get resp_sup for the site
        SELECT s.resp_sup_id as user_id FROM sites s WHERE s.idSite = ?
        UNION
        -- Get superviseur for the site
        SELECT s.superviseur_id as user_id FROM sites s WHERE s.idSite = ?
        UNION
        -- Get resp_op for the region
        SELECT ru.user_id FROM sites s
        LEFT JOIN region_users ru ON ru.region_id = s.group_pointage_id
        LEFT JOIN users u_temp ON u_temp.id = ru.user_id AND u_temp.role = 'resp_op'
        WHERE s.idSite = ? AND u_temp.id IS NOT NULL
        UNION
        -- Get all op users (they see all reclamations)
        SELECT u_op.id as user_id FROM users u_op WHERE u_op.role = 'op'
    ) user_ids
    LEFT JOIN users u ON u.id = user_ids.user_id
    LEFT JOIN users ur ON ur.id = u.real_email_id
    WHERE u.id IS NOT NULL
    AND (u.blocked IS NULL OR u.blocked = 0)
    AND COALESCE(ur.email, u.email) IS NOT NULL
    AND u.role IN ('resp_sup', 'superviseur', 'resp_op', 'op')
`;

const sqlGetPointageCount = `
    SELECT COUNT(*) as count
    FROM pointage_reclamations pr
    WHERE pr.reclamation_id = ?
`;

// Email configuration
const createTransporter = () => {
    return nodemailer.createTransport({
        host: email_config.host,
        port: email_config.port,
        secure: email_config.secure,
        auth: email_config.auth,
        tls: email_config.tls
    });
};

// Helper functions
const getEmployeeMatricule = (reclamation) => {
    if (reclamation.numero_employe) return reclamation.numero_employe;
    if (reclamation.numero_stagiaire) return reclamation.numero_stagiaire;
    if (reclamation.num_emp_soit) return reclamation.num_emp_soit;
    if (reclamation.num_emp_saoi) return reclamation.num_emp_saoi;
    return 'N/A';
};

const formatDate = (date) => {
    return moment(date).format('DD/MM/YYYY HH:mm');
};

const formatServiceDate = (date) => {
    const serviceMoment = moment(date);
    const timeStr = serviceMoment.format('HH:mm') === '18:00' ? 'NUIT' : 'JOUR';
    return `${serviceMoment.format('DD/MM/YYYY')} ${timeStr}`;
};

// Database query helper
const queryDatabase = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        pool.query(sql, params, (error, results) => {
            if (error) {
                reject(error);
            } else {
                resolve(results);
            }
        });
    });
};

// Email template generator
const generateEmailHTML = (emailData) => {
    const { reclamation, employe_display, view_url } = emailData;

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Nouvelle Réclamation</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 5px;
                margin-bottom: 20px;
            }
            .content {
                background-color: #ffffff;
                padding: 20px;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
            .info-row {
                margin-bottom: 10px;
                padding: 5px 0;
            }
            .label {
                font-weight: bold;
                color: #495057;
            }
            .value {
                color: #212529;
            }
            .button {
                display: inline-block;
                background-color: #007bff;
                color: white;
                padding: 10px 20px;
                text-decoration: none;
                border-radius: 5px;
                margin-top: 20px;
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #dee2e6;
                font-size: 12px;
                color: #6c757d;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h2 style="margin: 0; color: #dc3545;">🔔 Nouvelle Réclamation</h2>
            <p style="margin: 5px 0 0 0; color: #6c757d;">Une nouvelle réclamation a été créée dans le système</p>
        </div>

        <div class="content">
            <div class="info-row">
                <span class="label">Employé:</span>
                <span class="value">[${reclamation.matricule}] ${employe_display}</span>
            </div>

            <div class="info-row">
                <span class="label">Site:</span>
                <span class="value">${reclamation.site_nom}</span>
            </div>

            <div class="info-row">
                <span class="label">Date de service:</span>
                <span class="value">${reclamation.formatted_date_pointage}</span>
            </div>

            ${reclamation.type ? `
            <div class="info-row">
                <span class="label">Type:</span>
                <span class="value">${reclamation.type}</span>
            </div>
            ` : ''}

            <div class="info-row">
                <span class="label">Motif:</span>
                <span class="value">${reclamation.motif}</span>
            </div>

            <div class="info-row">
                <span class="label">Nombre d'heures réclamées:</span>
                <span class="value">${reclamation.nb_heure} heures</span>
            </div>

            ${reclamation.superviseur_nom ? `
            <div class="info-row">
                <span class="label">Superviseur assigné:</span>
                <span class="value">${reclamation.superviseur_nom} &lt;${reclamation.superviseur_email}&gt;</span>
            </div>
            ` : ''}

            <div class="info-row">
                <span class="label">Demandeur:</span>
                <span class="value">${reclamation.creator_nom} &lt;${reclamation.creator_email}&gt;</span>
            </div>

            <div class="info-row">
                <span class="label">Date de création:</span>
                <span class="value">${reclamation.formatted_created_at}</span>
            </div>

            <div class="info-row">
                <span class="label">Statut:</span>
                <span class="value">${reclamation.status.charAt(0).toUpperCase() + reclamation.status.slice(1)}</span>
            </div>

            <a href="${view_url}" class="button">📋 Voir les détails de la réclamation</a>
        </div>

        <div class="footer">
            <p>Cet email a été envoyé automatiquement par le système de gestion DRX Admin.</p>
            <p>Veuillez ne pas répondre directement à cet email.</p>
        </div>
    </body>
    </html>
    `;
};

// Email sending function
const sendEmailToUser = async (transporter, user, emailData) => {
    const { reclamation, employe_display } = emailData;

    const htmlContent = generateEmailHTML(emailData);

    const mailOptions = {
        from: `"DRX Admin System" <${email_config.auth.user}>`,
        to: user.real_email,
        subject: `Nouvelle Réclamation : [${reclamation.matricule}] ${employe_display}`,
        html: htmlContent
    };

    try {
        await transporter.sendMail(mailOptions);
        console.log(`✅ Email sent to ${user.name} (${user.real_email}) - Role: ${user.role}`);
        return { success: true, user: user.name, email: user.real_email };
    } catch (error) {
        console.error(`❌ Failed to send email to ${user.name} (${user.real_email}):`, error.message);
        return { success: false, user: user.name, email: user.real_email, error: error.message };
    }
};

// Main notification function
const sendReclamationNotification = async (reclamationId) => {
    try {
        console.log(`Starting notification process for reclamation ID: ${reclamationId}`);

        // Get reclamation details
        const reclamationResult = await queryDatabase(sqlGetReclamationDetails, [reclamationId]);
        if (reclamationResult.length === 0) {
            throw new Error(`Reclamation with ID ${reclamationId} not found`);
        }

        const reclamation = reclamationResult[0];
        console.log(`Found reclamation for ${reclamation.employe_nom || reclamation.agent_not_registered}`);

        // Get pointage count (number of hours)
        const pointageResult = await queryDatabase(sqlGetPointageCount, [reclamationId]);
        const nbHeures = pointageResult[0].count * 12; // Each pointage = 12 hours

        // Get site users to notify
        const siteUsers = await queryDatabase(sqlGetSiteUsers, [
            reclamation.site_id,
            reclamation.site_id,
            reclamation.site_id
        ]);
        console.log(`Found ${siteUsers.length} users to notify`);

        if (siteUsers.length === 0) {
            console.log('No users found to notify for this site');
            return;
        }

        // Prepare email data
        const emailData = {
            reclamation: {
                ...reclamation,
                nb_heure: nbHeures,
                formatted_date_pointage: formatServiceDate(reclamation.date_pointage),
                formatted_created_at: formatDate(reclamation.created_at),
                matricule: getEmployeeMatricule(reclamation)
            },
            employe_display: reclamation.employe_nom || `[SM] - ${reclamation.agent_not_registered}`,
            view_url: `${process.env.APP_URL || 'https://admin.dirickxguard.mg'}/reclamation/show/${reclamationId}`
        };

        // Send emails
        const transporter = createTransporter();
        const emailPromises = siteUsers.map(user => sendEmailToUser(transporter, user, emailData));

        const results = await Promise.all(emailPromises);

        // Summary
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;

        console.log(`\n📊 Email Notification Summary:`);
        console.log(`   ✅ Successfully sent: ${successful}`);
        console.log(`   ❌ Failed: ${failed}`);
        console.log(`   📧 Total recipients: ${siteUsers.length}`);

        if (failed > 0) {
            console.log(`\n❌ Failed recipients:`);
            results.filter(r => !r.success).forEach(r => {
                console.log(`   - ${r.user} (${r.email}): ${r.error}`);
            });
        }

        console.log(`\n🎯 Reclamation details:`);
        console.log(`   - ID: ${reclamationId}`);
        console.log(`   - Employee: ${emailData.employe_display}`);
        console.log(`   - Site: ${reclamation.site_nom}`);
        console.log(`   - Hours claimed: ${emailData.reclamation.nb_heure}`);
        console.log(`   - View URL: ${emailData.view_url}`);

    } catch (error) {
        console.error('Error sending reclamation notification:', error);
        throw error;
    }
};

// Command line execution
if (require.main === module) {
    const reclamationId = process.argv[2];

    if (!reclamationId) {
        console.error('Usage: node send_reclamation_notification.js <reclamation_id>');
        process.exit(1);
    }

    sendReclamationNotification(reclamationId)
        .then(() => {
            console.log('Notification process completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            console.error('Notification process failed:', error);
            process.exit(1);
        });
}

// Export for use in other modules
module.exports = { sendReclamationNotification };
