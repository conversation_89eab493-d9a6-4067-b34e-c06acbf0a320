import React, { useEffect, useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import {BsCheck} from 'react-icons/bs';
import LoadingPage from '../../loading/LoadingPage';
import useToken from '../../util/useToken';
import EditPieceJointeModal from '../../piecejointe/EditPieceJointeModal';

export default function Complement({id, auth, currentNature, setCurrentNature, updateData}) {
    const [isLoading, toggleLoading] = useState(true)
    const [employe, setEmploye] = useState(null)
    const [showModal, setShowModal] = useState(false)
    const [actionPjType, setActionPjType] = useState("")
    
    const handleAction = (nature,type) => {
        setCurrentNature(nature)
        setActionPjType(type)
        setShowModal(true)
    }

    useEffect(() => {
        let isMounted = true
        axios.get("/api/employe/complement/" + id ,  useToken())
        .then((res) => {
            if(isMounted) {
                setEmploye(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }, []);

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
            (
                employe &&
                <div className='story-container'>
                    {
                        showModal && 
                        <EditPieceJointeModal name={"employe_id"} value={id} updateData={updateData} closeModal={() => setShowModal(false)} currentNature={currentNature} actionPjType={actionPjType}/>
                    }
                    <div className="complement-item">
                        <span>
                            <span className={employe.cin == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                            CIN
                        </span>
                        {
                            ["resp_rh", "rh"].includes(auth.role) &&
                            (
                                employe.cin == 1 ? 
                                    <div className='action' onClick={() => handleAction("cin","edit")}>Modifier</div>
                                :
                                <div className='action' onClick={() => handleAction("cin","add")}>Ajouter</div>
                            )
                        }
                    </div>
                    <div className="complement-item">
                        <span>
                            <span className={employe.cv == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                            CV
                        </span>
                        {
                            ["resp_rh", "rh"].includes(auth.role) &&
                            (
                                employe.cv == 1 ? 
                                    <div className='action' onClick={() => handleAction("cv","edit")}>Modifier</div>
                                :
                                    <div className='action' onClick={() => handleAction("cv","add")}>Ajouter</div>
                            )
                        }
                    </div>
                    <div className="complement-item">
                        <span>
                            <span className={employe.photo == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                            Photo
                        </span>
                        {["resp_rh", "rh"].includes(auth.role) &&
                            (
                                employe.photo == 1 ? 
                                    <div className='action' onClick={() => handleAction("photo","edit")}>Modifier</div>
                                :
                                    <div className='action' onClick={() => handleAction("photo","add")}>Ajouter</div>
                            )
                        }
                    </div>
                    <div className="complement-item">
                        <span>
                            <span className={employe.residence == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                            Résidence
                        </span>
                        {["resp_rh", "rh"].includes(auth.role) &&
                            (
                                employe.residence == 1 ? 
                                    <div className='action' onClick={() => handleAction("residence","edit")}>Modifier</div>
                                :
                                    <div className='action' onClick={() => handleAction("residence","add")}>Ajouter</div>
                                
                            )
                        }
                    </div>
                    <div className="complement-item">
                        <span>
                            <span className={employe.plan_reperage == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                            Plan de repérage
                        </span>
                        {["resp_rh", "rh"].includes(auth.role) &&
                            (
                                employe.plan_reperage == 1 ? 
                                    <div className='action' onClick={() => handleAction("plan_reperage","edit")}>Modifier</div>
                                :
                                    <div className='action' onClick={() => handleAction("plan_reperage","add")}>Ajouter</div>
                                
                            )
                        }   
                    </div>
                    <div className="complement-item">
                        <span>
                            <span className={employe.bulletin_n3 == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                            Bulletin N°3
                        </span>
                        {["resp_rh", "rh"].includes(auth.role) &&
                            (
                                employe.bulletin_n3 == 1 ? 
                                    <div className='action' onClick={() => handleAction("bulletin_n3","edit")}>Modifier</div>
                                :
                                    <div className='action' onClick={() => handleAction("bulletin_n3","add")}>Ajouter</div>
                                
                            )
                        }     
                    </div>
                    <div className="complement-item">
                        <span>
                            <span className={employe.bonne_conduite == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                            Bonne conduite
                        </span>
                        {["resp_rh", "rh"].includes(auth.role) &&
                            (
                                employe.bonne_conduite == 1 ? 
                                    <div className='action' onClick={() => handleAction("bonne_conduite","edit")}>Modifier</div>
                                :
                                    <div className='action' onClick={() => handleAction("bonne_conduite","add")}>Ajouter</div>
                                
                            )
                        }                            
                    </div>
                </div>
            )
        }
    </>
}