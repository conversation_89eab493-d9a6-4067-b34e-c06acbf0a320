import axios from 'axios'
import React, { useState } from 'react';
import { IoMdClose } from 'react-icons/io';
import useToken from '../util/useToken';
import matricule from '../util/matricule';

export default function InputEmployeMultiple({employes, setEmployes, required}) {
    const [modalOpen, toggleModal] = useState(false)
    const [data, setData] = useState(null)
    const [disabled, setDisabled] = useState(false)
    const [searchValue, setSearchValue] = useState("")

    const handleShowModal = (e) => {
        e.preventDefault()
        toggleModal(true)
    }

    const handleRemoveEmploye = (employe) => {
        const newEmployes = employes.filter(ag => ag.id!= employe.id)
        setEmployes(newEmployes)
    }

    const handleSelectEmploye = (ag) => {
        if(!employes.map(a => a.id).includes(ag.id)){
            toggleModal(false)
            const newEmployes = [...employes]
            newEmployes.unshift(ag)
            setEmployes(newEmployes)
            setData(null)
            setSearchValue("")
        }
    }

    const handleCloseModal = () => {
        toggleModal(false)
    }
    
    const handleSearch = (e) => {
        e.preventDefault()
        setDisabled(true)
        setData(null)
        axios.get('/api/employe/search?value=' + searchValue, useToken())
        .then((res) => {
            if(res.data){
                let dataList = []
                if(res.data.employes){
                    res.data.employes.forEach(ag => {
                        dataList.push({
                            id: ag.id,
                            matricule: matricule(ag),
                            date_embauche: ag.date_embauche,
                            nom: ag.nom,
                            site: {
                                id: ag.site_id,
                                nom: ag.site,
                            }
                        })
                    });
                }
                setData({
                    count: res.data.count,
                    employes: dataList
                })
                setDisabled(false)
            }
        })
        .catch((e) => {
            console.error(e)
            setDisabled(false)
            setData(null)
        })
    }
    return <div>
        {
            employes.length == 0 ?
                <div className='input-container'>
                    <label>Employé {required && <span className='danger'>*</span>}</label>
                    <input
                        type="text" 
                        readOnly
                        onClick={handleShowModal}
                        />
                </div>
            :
            <div className="item-header">
                <h3>Employe <span className='danger'>*</span></h3>
                <button onClick={handleShowModal} className="btn">
                    Ajouter
                </button>
            </div>
        }
        {
            employes.map(ag => (
                <div key={ag.id} className="card-container">
                    <div className='item-container'>
                        <span>
                            {ag.matricule} 
                            <span className='secondary'>
                                {' ' + ag.nom}
                            </span>
                        </span>
                        <div>
                            <span onClick={() => handleRemoveEmploye(ag)}>
                                <IoMdClose size={20}/>
                            </span>
                        </div>
                    </div>
                </div>
            ))
        }
        {
            modalOpen &&
            <div className='modal'>
                <div>
                    <h2>Employe</h2>
                    <div className='search-container'>
                        <input type="text" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} placeholder="Nom de l'employe ou matricule"/>
                        <button onClick={handleSearch} disabled={disabled}>Rechercher</button>
                    </div>
                    {
                        data &&
                        <div className='list-container'>
                            {
                                data.count == 0 ?
                                    <h1 className='center fade'>Données introuvable</h1>
                                : data.count > 10 ?
                                    <h1 className='center fade'>Veuillez specifé votre recherche</h1>
                                :
                                <ul>
                                    {
                                        data.employes.map(ag => {
                                            return <li key={ag.id} className={employes.map(a => a.id).includes(ag.id) ? "danger" : ""}  onClick={() => handleSelectEmploye(ag)}>
                                                {ag.matricule} {ag.nom}<br/>
                                                <span className='secondary'>{ag.site.nom}</span>
                                            </li>
                                        })
                                    }
                                </ul>
                            }
                        </div>
                    }
                    <div className='form-button-container'>
                        <button onClick={handleCloseModal}>Annuler</button>
                    </div>
                </div>
            </div>
        }
    </div>;
}