@if ($note != null)
<p style="white-space: pre-line;">
    {{$note}}
</p>
@endif
<h3>De<PERSON>e de sanction</h3>
<p style="white-space: pre-line;">
    Employe: {{$employe}}<br/>
    Site : {{$sanction->site}} <br/>
    @if($sanction->absence == true)
        Sans pointage <br/>
    @else
        Date du service : {{$sanction->date_service}} <br/>
    @endif
    @if($sanction->objet != null)
        Objet : {{$sanction->objet}} <br/>
    @endif
    @if($sanction->montant > 0)
        Montant : {{$sanction->montant}}Ar<br/>
    @endif
    Motif: {{$sanction->motif}} <br/>
    @if($sanction->superviseur_id != $sanction->user_id)
        Superviseur responsable: {{$sanction->sup_nom . ' <' . $sanction->sup_email . '>'}} <br/>
    @endif
    Demandeur: {{$sanction->user_nom . ' <' . $sanction->user_email . '>'}} <br/>
</p>

@if(count($last_sanctions) > 0)
    <h4>Sanction déjà reçu par l'employe</h4>
@endif

@foreach ($last_sanctions as $sanc)
    <b>{{$sanc->objet}}</b><br/>
    Motif: {{$sanc->motif}}<br/>
    @if($sanc->absence == true)
        Sans pointage <br/>
    @else
        Date du service: {{$sanc->date_service}}<br/>
    @endif
    <br/>
@endforeach
<br/>
<br/>
<a href={{env('APP_URL'). "/sanction/show/". $sanction->id}}>Cliquez ici pour voir les détails ou répondre</a>