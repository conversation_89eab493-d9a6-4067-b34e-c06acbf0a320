
const mysql = require('mysql')
const moment = require('moment')

const {db_config_ipbx} = require("../auth")
const poolOvh = mysql.createPool(db_config_ipbx)

const sqlSelectCrd = "SELECT * FROM cdr where datetime > '2024-10-18 05:50:00'"
poolOvh.query(sqlSelectCrd, [], (err, result) => {
    if(err)
        console.error(err)
    else {
        console.log(moment(result[0].datetime).format("HH:mm:ss"))
        //console.log(action + " | " + from + " | " + to + " | " + status + " | " + call_id)
        //console.log(parsedData)
    }
})