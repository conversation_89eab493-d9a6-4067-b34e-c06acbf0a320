import React, { useEffect, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import useToken from '../util/useToken'
import LoadingPage from '../loading/LoadingPage'
import ShowHeader from '../view/ShowHeader'
import moment from 'moment'
import { upperCase } from 'lodash'
import Tab from '../layout/Tab'
import { useNavigate } from 'react-router-dom'

export default function ShowPlanningNonFait({ auth, currentId, setCurrentId, size }) {
    const queryParams = new URLSearchParams(location.search);
    const date_planning = queryParams.get('date_planning');
    const [isLoading, toggleLoading] = useState(false)
    const [planning, setPlanning] = useState()
    const [lastPlannings, setLastPlannings] = useState()
    const params = useParams()
    const navigate = useNavigate()
    const lastParams = (new URLSearchParams(useLocation().search)).toString()
    
    const updateData = () => { 
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/site/show_unfinished/' + (currentId ? currentId : params.id) + '?date_planning=' + date_planning, useToken())
        .then((res) => {
            if (isMounted) {
                if(!res.data)
                    setCurrentId()
                else{
                    setPlanning(res.data)
                    setLastPlannings(res.data.last_plannings)
                }
            }
            toggleLoading(false)
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
    }
    useEffect(() => {
        updateData()
    }, [currentId])

    return (
        <div>
            {
                isLoading ?
                    <LoadingPage />
                :
                    <>
                        {planning &&
                            <div>
                                <ShowHeader size={size} label="Planning non fait" id={currentId} closeDetail={() => setCurrentId()} />
                                <div className="card-container">
                                    <h3><span>{planning.site}</span></h3>
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Date: <span className='text'>{upperCase(moment().format("MMM YYYY")) }</span>
                                    </p>
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Type de contrat: <span className='text'>{planning.horaire ?? 'Non defini' }</span>
                                    </p>
                                    {
                                        planning.nb_agent_day > 0 &&
                                            <p style={{ whiteSpace: "pre-line" }}>
                                                Nb. Agent Jour: <span className='text'>{planning.nb_agent_day ?? 'Non defini'}</span>
                                            </p>
                                    }
                                    {
                                        planning?.nb_agent_night > 0 && 
                                            <p style={{ whiteSpace: "pre-line" }}>
                                                Nb. Agent Nuit: 
                                                    <span className='text'>
                                                        {planning.nb_agent_night ?? 'Non defini' }
                                                    </span>
                                            </p>
                                    }
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Heure Facturés: <span className='text'>{planning.total_hour ?? 'Non defini' }</span>
                                    </p>
        
                                    {
                                        planning.superviseur_id && 
                                            <p style={{ whiteSpace: "pre-line" }}>
                                                Superviseur : <span className='text'>{planning.superviseur + ' <' + planning.superviseur_email + '>'}</span>
                                            </p>
                                    }
                                    {
                                        planning.resp_sup_id &&
                                            <p style={{ whiteSpace: "pre-line" }}>
                                                Manager : <span className='text'>{planning.resp_sup + ' <' + planning.resp_sup_email + '>'}</span>
                                            </p>
                                    }
                                    {planning.resp_sup_id ?
                                        <div className="card-action">
                                            <div className="action-container">
                                                <span  onClick={() => navigate('/planning/add?date=' + date_planning + '&site_id=' + (currentId ? currentId : params.id) + ('&last_params=' + encodeURIComponent("/planning-non-fait?" + lastParams)))}>
                                                    Créer
                                                </span>
                                            </div>
                                        </div>
                                        :
                                        <div className="card-action">
                                            <div className="action-container">
                                                <span  onClick={() => {
                                                        const searchParams = new URLSearchParams(lastParams);
                                                        searchParams.delete("not_found_resp");
                                                        searchParams.set("site_id", currentId ? currentId : params.id);
                                                        navigate('/site/edit/' + (currentId ? currentId : params.id) + ('?last_params=' + encodeURIComponent("/planning-non-fait?" + searchParams.toString())))
                                                    }}
                                                >
                                                    Modifier le site
                                                </span>
                                            </div>
                                        </div>
                                    }
                                    <Tab auth={auth} data={lastPlannings} name={"last_planning"} size={size} />
                                </div>

                            </div>
                        }
                    </>
            }
        </div> 
  )
}
