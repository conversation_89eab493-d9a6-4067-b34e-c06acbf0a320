import React, { useEffect, useRef, useState } from 'react';

import { CgClose } from 'react-icons/cg';
import useToken from '../util/useToken';

export default function InputMultipleUser({label, users, setUsers, required}) {
    const ref = useRef(null);
    const [search, setSearch] = useState('')
    const [searchUsers, setSearchUsers] = useState([])
    const [defaultUser, setDefaultUser] = useState(null)
    const [timeoutId, setTimeoutId] = useState(null)
    
    const removeUser = (e, id) => {
        e.stopPropagation()
        const clearUsers = []
        users.map(u => {
            if(u.id != id)
                clearUsers.push(u)
        })
        setUsers(clearUsers);
    }

    useEffect(() => {
        if(timeoutId)
            clearTimeout(timeoutId)
        let isMounted = true
        const newTimeout = setTimeout(() => {
            if (search) {
                axios.get('/api/user/modal?search=' + search, useToken())
                    .then((res) => {
                    if(isMounted) {
                        setSearchUsers(res.data)
                        if(res.data.length > 0)
                            setDefaultUser(res.data[0])
                        else
                            setDefaultUser(null)
                    }
                })
            }
            else setSearchUsers([])
        }, 500);
        setTimeoutId(newTimeout)

        return () => {isMounted = false}
    }, [search])

    const handleAddUser = (u) => {
        if(!users.map(u => u.id).includes(u.id))
            users.push({id: u.id, address: u.email, name: u.name})
        setUsers(users)
        setSearchUsers([])
        setSearch('')
    }

    const handlePressEnter = (e) => {
        e.stopPropagation()
        if(e.key === 'Enter' && defaultUser){
            handleAddUser(defaultUser)
            e.preventDefault()
        }
        else if(e.key == "ArrowUp"){
            searchUsers.forEach((us, i) => {
                if(defaultUser.id == us.id){
                    if(i == 0)
                        setDefaultUser(searchUsers[searchUsers.length - 1])
                    else
                        setDefaultUser(searchUsers[i - 1])
                }
            })
        }
        else if(e.key == "ArrowDown"){
            searchUsers.forEach((us, i) => {
                if(defaultUser.id == us.id){
                    if(i == searchUsers.length - 1)
                        setDefaultUser(searchUsers[0])
                    else
                        setDefaultUser(searchUsers[i + 1])
                }
            })
        }
        else if (e.key === 'Backspace') {
            const tmpUsers = [...users];
            if (search == '') {
                tmpUsers.pop();
                setUsers(tmpUsers);
            }
        }
    }

    return <div onClick={() => setSearchUsers([])}>
        <div className='input-container'>
            <label>
                {label ? label : "Destinataire"} {required && <span className='danger'>*</span>}
            </label>
            <div id="mailReceiver" className='scrollable-div custom-scroll' onClick={() => ref.current.focus()}>
                {
                    users.map(u => <div className='adress-item' key={u.id}>
                            <div>
                                {u.name} <span className='secondary'>{' <' + u.address + '> '}</span>
                                <CgClose className='pointer' onClick={(e) => removeUser(e, u.id)}/>
                            </div>
                        </div>
                    )
                }
                <input ref={ref} type="text" value={search} onKeyDown={handlePressEnter} onChange={e => setSearch(e.target.value)}/>
            </div>
            {
                searchUsers.length > 0 &&
                <div className='list-container'>
                    <ul>
                        {
                            searchUsers.slice(0, 5).map((us, index) => {
                                return <li key={us.id} className={(defaultUser && us.id == defaultUser.id) ? 'default' : ''} onClick={() => handleAddUser(us)}>
                                    {us.name}<br/>
                                    <span className='secondary'>{us.email}</span>
                                </li>
                            })
                        }
                    </ul>
                </div>
            }
        </div>
    </div>
}