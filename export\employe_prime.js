const moment = require('moment')
const mysql = require('mysql')
const nodemailer = require("nodemailer")
const Excel = require("exceljs")
const { email_config } = require("../auth")
const matricule = require("../resources/js/components/util/matricule.js")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const destination_vg = {
    to: "ogros<PERSON><EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,"
        + "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,"
        + "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>,<EMAIL>,"
        + "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, "
        + "<EMAIL>,<EMAIL>,<EMAIL>",
}
const destination_test = {
    to: "<EMAIL>",
}

let transporter = nodemailer.createTransport(email_config)
    
function sendMail(destination, subject, text, attachements, callback){
	const message = {
		from: email_config.auth.user,
		to: destination.to,
		cc: destination.cc,
		subject: subject,
		html: "<p>Bonjour,</p>" + 
			"<p>" + text + "</p>" +
			"<p>Cordialement,</p>",
		attachments: attachements
	};
	transporter.sendMail(message , (err, info) => {
		if(err){
			console.error(err)
            process.exit(1)
        }
		else {
            console.log(info)
		    callback()
        }
	})
}

function getDayExport(){
	let beginDay = moment().set({hour:6, minute:10, second:0})
	if(moment().isBefore(beginDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD")
	return moment().format("YYYY-MM-DD")
}

function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectLastSanctionExport = "SELECT value FROM params p WHERE p.key = 'last_sanction_export'"

const sqlSelectSanction = (year, month) => {
    const end = year + "-" + month + "-20 00:00:00"
    const begin = moment(end).subtract(1, "month").format("YYYY-MM-DD HH:mm:ss")
    return "SELECT employe_id from sanctions " +
        "where date_pointage > '" + begin + "' and date_pointage < '" + end + "' " +
        "and status != 'draft' " +
        "group by employe_id"
}

const sqlSelectPointage = (year, month, ids) => {
    const end = year + "-" + month + "-20 00:00:00"
    const begin = moment(end).subtract(1, "month").format("YYYY-MM-DD HH:mm:ss")
    return "SELECT (COUNT(id)*12) as 'nb_heure', employe_id from pointages " +
        "where employe_id not in (" + ids.join(',') + ") and (soft_delete = 0 or soft_delete is null) " +
        "and date_pointage > '" + begin + "' and date_pointage < '" + end + "' " +
        "group by employe_id order by COUNT(id) desc"
}

const sqlSelectEmploye = (ids) => "SELECT e.id, e.nom, e.real_site_id, e.societe_id, e.date_embauche, e.date_confirmation, e.date_conf_soit, s.pointeuse " +
    "from employes e " +
    "left join sites s on s.idsite = e.real_site_id " +
    "where e.id not in (" + ids.join(',') + ") and (e.soft_delete = 0 or e.soft_delete is null)"

function sqlUpdateLastSanctionExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_sanction_export'"
}
function numberWithSpaces(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
}

function generateSanctionExcelFile(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    data.forEach(stat => {
        const worksheet = workbook.addWorksheet(stat.description)
        worksheet.getColumn('A').width = 50
        worksheet.getColumn('B').width = 50
        worksheet.getColumn('C').width = 20
        worksheet.getColumn('D').width = 40
        worksheet.getColumn('E').width = 50
        worksheet.getColumn('F').width = 30
        worksheet.getColumn('G').width = 10
        worksheet.getCell('A1').value = stat.description + " (" + stat.sanctions.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:G1')

        let line = 3
        
        worksheet.getCell('A' + line).value = "Employe"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontBold
        worksheet.getCell('B' + line).value = "Site"
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).font = fontBold
        worksheet.getCell('C' + line).value = "Date du service"
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).alignment = alignmentStyle
        worksheet.getCell('C' + line).font = fontBold
        worksheet.getCell('D' + line).value = "Motif"
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).font = fontBold
        worksheet.getCell('E' + line).value = "Demandeur"
        worksheet.getCell('E' + line).border = borderStyle
        worksheet.getCell('E' + line).font = fontBold
        worksheet.getCell('F' + line).value = "Objet"
        worksheet.getCell('F' + line).border = borderStyle
        worksheet.getCell('F' + line).font = fontBold
        worksheet.getCell('G' + line).value = "Montant"
        worksheet.getCell('G' + line).border = borderStyle
        worksheet.getCell('G' + line).font = fontBold
        line++

        stat.sanctions.forEach(sanc => {
            worksheet.getCell('A' + line).value = (
                matricule(sanc)
            ) + " " + sanc.employe
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = capitalizeFirstLetter(sanc.site)
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = sanc.date_pointage ? moment(sanc.date_pointage).format("DD-MM-YY") : ""
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('C' + line).alignment = alignmentStyle
            worksheet.getCell('D' + line).value = sanc.motif
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('E' + line).value = sanc.user_nom + " <" + sanc.user_email + ">"
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('F' + line).value = sanc.objet
            worksheet.getCell('F' + line).border = borderStyle
            worksheet.getCell('G' + line).value = sanc.montant ? numberWithSpaces(sanc.montant) + " Ar" : ""
            worksheet.getCell('G' + line).border = borderStyle
            line++
        })
    })
}

function doSanctionExport(year, month){
	console.log("doSanctionExport")
    pool.query(sqlSelectSanction(year, month), [], async (err, sanctions) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb sanction: " + sanctions.length)
            pool.query(sqlSelectPointage(year, month, sanctions.map(s => s.employe_id)) , [] , async (err, pointages) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb pointage: " + pointages.length)
                    pool.query(sqlSelectEmploye(sanctions.map(s => s.employe_id)), [], async (err, employes) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Nb employes total: " +  employes.length)
                            employes.forEach(e => {
                                pointages.forEach(p => {
                                    if(e.id == p.employe_id)
                                        e.nb_heure = p.nb_heure 
                                })
                            })
                            const siteHasPrime = []
                            const employeToPrime = []
                            const employeToPrimeTotal = []
                            const employeToPrimePointeuse = []
                            employes.forEach(e => {
                                let minDate = null
                                const dateList = [e.date_embauche, e.date_conf_soit, e.date_confirmation]
                                dateList.forEach(dt => {
                                    if(dt && (!minDate || moment(minDate).isAfter(moment(dt))))
                                        minDate = dt
                                });

                                if(minDate && moment(minDate).isBefore(moment().subtract(6, "month")) 
                                    && e.nb_heure >= 300
                                ){
                                    if(!siteHasPrime.includes(e.real_site_id)){
                                        employeToPrime.push(e)
                                        if(e.pointeuse)
                                            employeToPrimePointeuse.push(e)
                                    }
                                    siteHasPrime.push(e.real_site_id)
                                    employeToPrimeTotal.push(e)
                                }
                            })
                            console.log("-------------\n" + moment(year + "-" + month + "-01").format("MMM YYYY"))
                            console.log("Nb employe total à primé: " + employeToPrimeTotal.length)
                            console.log("Nb employe par site à primé: " + employeToPrime.length)
                            console.log("Nb employe par site avec pointeuse: " + employeToPrimePointeuse.length)
                        }
                    }
                    )
                }
            })
        }
    })
}

if(/^\d{4}$/.test(process.argv[2]) && /^\d{2}$/.test(process.argv[3])){
    console.log("send test...")
    doSanctionExport(process.argv[2], process.argv[3])
}
else if(process.argv[2] == 'task'){
    if(moment().day() == 3 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
        let date_vigilance = moment().format("YYYY-MM-DD")
        pool.query(sqlSelectLastSanctionExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && result[0].value == date_vigilance) {
                console.log("export list sanction already done!")
                process.exit(1)
            }
            else {
                console.log("exporting ...")
                doSanctionExport(date_vigilance)
            }
        })
    }
    else {
        console.log("Not wednesday, skip Sanction")
    }
}
else
    console.log("please specify command!")