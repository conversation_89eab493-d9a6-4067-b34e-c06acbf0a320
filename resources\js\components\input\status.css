.status-label{
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.status-pink{
    border: 3px #e91e63 solid;
    color: #e91e63;
}
.status-cyan{
    border: 3px #00bcd4 solid;
    color: #00bcd4;
}
.status-purple{
    border: 3px #9c27b0 solid;
    color: #9c27b0
}
.status-teal {
    border: 3px #009688 solid;
    color: #009688;
}
.status-lime{
    border: 3px #cddc39 solid;
    color: #cddc39;
}
.status-grey{
    border:3px #666 solid;
    color: #666;
}
.status-orange{
    border: 3px #ff9800 solid;
    color: #ff9800;
}
.status-green{
    border: 3px #8bc34a solid;
    color: #8bc34a;
}
.status-yellow{
    border: 3px #fdd835 solid;
    color: #fdd835;
}
.status-brown{
    border: 3px #795548 solid;
    color: #795548;
}
.status-indigo {
    border: 3px #3f51b5 solid;
    color: #3f51b5;
}

.status-line{
    min-width: 50px;
    max-width: 50px;
    width: 50px;
    text-align: center;
}

.status-icon-enter{
    color: #4caf50;
    width: 17px;
    height: 17px;
}

.status-icon-exit{
    color: #ef5350;
    margin-left: 8px;
    width: 17px;
    height: 17px;
}

.enter {
    color: #4caf50;
}

.exit {
    color: #ef5350;
}