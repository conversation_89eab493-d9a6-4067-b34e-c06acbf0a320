import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import useToken from '../util/useToken';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';
import matricule from '../util/matricule';         
import showAmount from '../util/numberUtil';

export default function ShowPartNonFait({auth, currentId, setCurrentId, setCurrentItem, size}){
    const params = useParams()
    const [partNonFait, setPartNonFait] = useState()
    const [isLoading, toggleLoading] = useState(false)

    const updateData = () =>{
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/employe/show_pv_not_done/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setPartNonFait(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(updateData, [currentId])

    return (
        <>{
            isLoading ?
                <LoadingPage/>
            :
                <div>
                    {
                        partNonFait &&
                        <>
                            <ShowHeader size={size} label="Part non fait" id={partNonFait.employe_id} closeDetail={() => setCurrentId()}/>
                            <div className="card-container">
                                <h3>
                                    {matricule(partNonFait)} {partNonFait.employe} 
                                </h3>
                                <p>
                                    <span className='text'>
                                        {partNonFait.fonction.toLowerCase() == 'autre' ? ("Titre : " + partNonFait.titre) : ("Fonction : " + partNonFait.fonction)}
                                    </span>
                                </p>
                                <p>
                                    Agence : <span className='text'>{partNonFait.agence}</span>
                                </p>

                                <div className="tab-container">
                                    <div className="tab-list">
                                        <div className='active'>Critères</div>
                                    </div>
                                    <div className="tab-content">
                                        {
                                            partNonFait.critere_parts.map((critere, index) => 
                                                <div className='card-container' key={index}>
                                                    <div className='pointage-container secondary'>
                                                        {critere.designation.trim()}
                                                        <br/>
                                                        <span style={{minWidth: '100px', textAlign: 'right'}} className='secondary'>
                                                            {showAmount(critere.montant)}
                                                        </span>
                                                    </div>
                                                </div>
                                            )
                                        }
                                    </div>
                                </div> 
                            </div>
                        </>
                    }
                </div>
        }
        </>
    );
};