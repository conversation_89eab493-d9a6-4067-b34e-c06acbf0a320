<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class TrimInputRequest
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $request->merge(
            request()->collect()->filter(function($value) {
                if(is_array($value))
                    return count($value) != 0;
                else{
                    $vtrim = trim($value);
                    return null !== $vtrim && '' !== $vtrim && 0 !== $vtrim;
                }
            })->toArray()
        );
        return $next($request);
    }
}
