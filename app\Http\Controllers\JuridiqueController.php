<?php

namespace App\Http\Controllers;

use App\Models\Juridique;
use App\Models\SuiviJuridique;
use App\Models\EtapeContentieux;
use App\Models\Agence;
use App\Models\Site;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class JuridiqueController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    private static $attributeNames = array(
        'reference' => 'Référence ',
        'agence_id' => "Agence",
        'police' => 'Police/Gendarme compétent',
        'agent' => 'Agent(s) concerné',
        'fait' => 'Rappelle des faits',
        'debiteur' => "Débiteur",
        'contrat' => "Contrat",
        'facture' => "Facture(s)",
        'montant' => "Montant",
        'suivi' => 'Chose faite'
    );

    protected static function search(Request $request){
        $searchArray = [];
        
        if($request->id)
            $searchArray[] = "j.id = '". $request->id ."'";
        else {
            if($request->reference)
                $searchArray[] = "j.reference like '%". $request->reference ."%'";
            if($request->agence_id)
                $searchArray[] = "j.agence_id = ". $request->agence_id;
            if($request->late_date)
                $searchArray[] = "j.etape_id != 11 and j.limit_date < CURDATE() and (j.next_suivi is null or j.next_suivi < CURDATE())";
            if($request->debiteur)
                $searchArray[] = "j.debiteur like '%". $request->debiteur ."%'";
            if($request->agent)
                $searchArray[] = "j.agent like '%". $request->agent ."%'";
            if($request->site_id)
                $searchArray[] = "j.site_id like '%". $request->site_id ."%'";
            if($request->fait)
                $searchArray[] = "j.fait like '%". $request->fait ."%'";
            if($request->etape_id)
                $searchArray[] = "j.etape_id = ". $request->etape_id;
            if($request->created_at)
                $searchArray[] = " j.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "j.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " j.user_id = " . $request->user_id . " ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by j.updated_at desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by j.updated_at desc limit ". $request->offset . ", 30";

        return compact('query_where', 'query_and');
    }

    public static function index(Request $request){
        $search = JuridiqueController::search($request);
        $juridiques = DB::select("SELECT j.id, j.reference, j.debiteur, j.contrat, j.montant,
            e.color as 'status_color', e.duration as 'status_duration', j.created_at
            FROM juridiques j
            LEFT JOIN etape_contentieux e on e.id = j.etape_id
            ". $search["query_where"]
            , []);
        return response(compact('juridiques'));
    }

    public static function show($id) {
        $juridique =  DB::select("SELECT j.id, j.reference, j.agence_id, j.debiteur, j.contrat, j.created_at, j.fait_id,
            j.facture, j.montant, e.color as 'status_color', e.duration as 'status_duration', e.nom as 'status_nom', e.order,
            (SELECT COUNT(*) FROM piece_jointes pj WHERE pj.juridique_id = j.id) as nb_pj,
            j.user_id, u.name as 'user_nom', u.email as 'user_email', agc.nom as 'agence'
            FROM juridiques j
            LEFT JOIN agences agc on agc.id = j.agence_id
            LEFT JOIN etape_contentieux e on e.id = j.etape_id
            LEFT JOIN users u on u.id = j.user_id
            WHERE j.id = ?"
            , [$id])[0];
        return response()->json($juridique);
    }

    public static function detail($id) {
        $juridique = Juridique::find($id);
        if($juridique) {
            $juridique->agence = Agence::find($juridique->agence_id);
            $juridique->site = Site::find($juridique->site_id);
        }
        return response()->json($juridique);
    }

    protected static function validateAndSetJuridique($request, $juridique){
        $auth = $request->user();
        $juridique->user_id = $auth->id;
        $juridique->reference = $request->reference;
        $juridique->agence_id = $request->agence_id;
        if($juridique->id){
            $validator = Validator::make($request->all(), [
                'reference' => 'required|unique:juridiques,reference,' . $juridique->id,
                'agence_id' => 'required',
            ]);
        }
        else {
            $validator = Validator::make($request->all(), [
                'reference' => 'required|unique:juridiques',
                'agence_id' => 'required',
                'suivi' => 'required',
            ]);
        }
        if ($validator->fails()){
            return $validator->setAttributeNames(self::$attributeNames);
        }

        $validator = Validator::make($request->all(), [
            'debiteur' => 'required',
            'contrat' => 'required',
            'facture' => 'required',
            'montant' => 'required|numeric',
        ]);
        $juridique->debiteur = $request->debiteur;
        $juridique->contrat = $request->contrat;
        $juridique->facture = $request->facture;
        $juridique->montant = $request->montant;
        
        return $validator->setAttributeNames(self::$attributeNames);
    }

    public static function store(Request $request){
        if(in_array($request->user()->role, ['juridique'])){
            $juridique = new Juridique();
            $validator = JuridiqueController::validateAndSetJuridique($request, $juridique);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $juridique->etape_id = 1;
            $juridique->limit_date = new \DateTime();
            $juridique->next_suivi = (new \DateTime())->add(new \DateInterval('P10D'));
            $juridique->created_at = new \DateTime();
            $juridique->updated_at = new \DateTime();
            
            if($juridique->save()){
                $suivi = new SuiviJuridique();
                $suivi->juridique_id = $juridique->id;
                $suivi->commentaire = $request->suivi;
                $suivi->user_id = $request->user()->id;
                $suivi->created_at = new \DateTime();
                $suivi->updated_at = new \DateTime();
                $suivi->save();
                $juridique->last_suivi_id = $suivi->id;
                $juridique->save();
                HistoriqueController::new_juridique($request, $juridique);
                return response(["success" => "Dossier bien enregistré"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public static function edit($id, Request $request){
        if(in_array($request->user()->role, ['juridique'])){
            $juridique = Juridique::find($id);
            $validator = JuridiqueController::validateAndSetJuridique($request, $juridique);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $juridique->updated_at = new \DateTime();
            
            if($juridique->save()){
                HistoriqueController::update_juridique($request, $juridique, "Dossier modifié");
                return response(["success" => "Dossier bien enregistré"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public static function update_status($id, Request $request){
        $juridique = Juridique::find($id);
        if(in_array($request->user()->role, ['juridique'])){
            $old_etape = EtapeContentieux::find($juridique->etape_id);
            $new_etape = EtapeContentieux::find($request->etape_id);
            if($old_etape->order < $new_etape->order){
                $juridique->etape_id = $request->etape_id;
                $juridique->limit_date = (new \DateTime())->add(new \DateInterval('P'. $new_etape->duration .'D'));
                $juridique->updated_at = new \DateTime();
                $juridique->save();
                HistoriqueController::update_juridique($request, $juridique, "Statut modifié");
                return response(["success" => "Statut modifié"]);
            }
        }
        return response(["error" => "EACCES"]);
    }
}
