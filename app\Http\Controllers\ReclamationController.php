<?php

namespace App\Http\Controllers;

use App\Models\Reclamation;
use App\Models\PointageReclamation;
use App\Models\Pointage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ReclamationController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    private static $attributeNames = array(
        'employe_id' => 'Agent',
        'motif' => 'Motif',
        'pointages' => 'Pointage'
    );

    protected static function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = "r.id = '". $request->id ."'";
        else {
            if($request->employe_id)
                $searchArray[] = " r.employe_id = " . $request->employe_id . " ";
            if($request->employe){
                $searchArray[] = " (r.agent_not_registered like '%" . $request->employe . "%' 
                    or e.nom like '%" . $request->employe . "%' or e.numero_stagiaire like '%" . $request->employe . "%' or e.numero_employe like '%" . $request->employe . "%'
                    or e.num_emp_soit like '%" . $request->employe . "%' or e.num_emp_saoi like '%" . $request->employe . "%' ) ";
            }
            // if ($request->employe) {
            //     $columns = ['r.agent_not_registered', 'e.nom', 'e.numero_stagiaire', 'e.numero_employe', 'e.num_emp_soit', 'e.num_emp_saoi'];
            //     $searchArray[] = "(" . implode(" OR ", array_map(fn($col) => "$col LIKE '%{$request->employe}%'", $columns)) . ")";
            // }            
            if($request->service)
                $searchArray[] = " r.motif like '%" . $request->motif . "%' ";
            if($request->date_service){
                $date_service = $request->date_service;
                if((new \DateTime($request->date_service))->format("H:i:s") == "06:00:00")
                    $date_service = (new \DateTime($request->date_service))->format("Y-m-d") . " 07:00:00";
                $searchArray[] = " r.date_pointage = '" . $date_service . "' ";
            }
            if($request->status)
                $searchArray[] = "r.status = '". $request->status ."'";
            if($request->created_at)
                $searchArray[] = " r.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "r.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " r.superviseur_id = " . $request->user_id . " ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . ($request->offset_date ? " and r.id <= '". $request->offset_date . "'" : "") . " order by r.id desc limit ". $request->offset . ", 30";
        $query_and = $query_and . ($request->offset_date ? " and r.id <= '". $request->offset_date . "'" : "") . " order by r.id desc limit ". $request->offset . ", 30";

        return compact('query_where', 'query_and');
    }

    public static function index(Request $request){
        $auth = $request->user();
        $search = self::search($request);
        if(in_array($auth->role, ["rh", "resp_rh", "validateur"])){
            $reclamations = DB::select("SELECT r.id, r.employe_id, r.updated_at, r.date_pointage, agent_not_registered, r.type,
                e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                stat.color as 'status_color'
                FROM reclamations r
                LEFT JOIN employes e on e.id = r.employe_id
                LEFT JOIN `status` stat on stat.name = r.status
                " . $search['query_where'], []);
        } else if (in_array($auth->role, ["resp_op"])) {
            $regions = RegionUsersController::getRegions($request);
            $reclamations = DB::select("SELECT r.id, r.employe_id, r.site_id, r.updated_at, r.date_pointage, agent_not_registered, r.type,
                e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                stat.color as 'status_color'
                FROM reclamations r
                LEFT JOIN employes e on e.id = r.employe_id
                LEFT JOIN sites s on s.idSite = r.site_id
                LEFT JOIN `status` stat on stat.name = r.status
                WHERE s.group_pointage_id in($regions)
                " . $search['query_and'], []);
        } else if (in_array($auth->role, ["superviseur", "resp_sup"])) {
            $column = $auth->role == "superviseur" ? "s.superviseur_id" : "s.resp_sup_id";
            $reclamations = DB::select("SELECT r.id, r.employe_id, r.site_id, r.updated_at, r.date_pointage, agent_not_registered, r.type,
                e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                stat.color as 'status_color'
                FROM reclamations r
                LEFT JOIN employes e on e.id = r.employe_id
                LEFT JOIN sites s on s.idSite = r.site_id
                LEFT JOIN `status` stat on stat.name = r.status
                WHERE $column = ?
                " . $search['query_and'], [$auth->id]);
        }
        else
            return response(["error" => "EACCES"]);
        if($request->offset == 0 && count($reclamations) > 0){
            $offset_date = $reclamations[0]->updated_at;
            return response(compact('reclamations', 'offset_date'));
        }
        return response(compact('reclamations'));
    }

    public static function show($id){
        $reclamation = DB::select("SELECT r.id, r.employe_id, r.type, r.updated_at, r.superviseur_id as 'user_id', r.date_pointage,
            e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
            us.name as 'user_nom', us.email as 'user_email', r.agent_not_registered, st.name as 'status', st.color as 'status_color', st.description as 'status_description'
            FROM reclamations r
            LEFT JOIN status st on st.name = coalesce(r.status, 'demande')
            LEFT JOIN employes e on e.id = r.employe_id
            LEFT JOIN users us on us.id = r.superviseur_id
            WHERE r.id = ?", [$id])[0];
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj where pj.reclamation_id = ? and pj.created_at >= '2025-02-10 00:00:00'", [$id]);
        $reclamation->nb_pj = count($pieces);
        // $reclamation->nb_heure = PointageReclamation::where('reclamation_id', $id)->count() * 12;
        return response()->json($reclamation);
    }

    protected static function validateAndSetReclamation($request, $reclamation){
        $auth = $request->user();
        if($auth->role == "superviseur"){
            $reclamation->superviseur_id = $auth->id;
        }
        else {
            $reclamation->superviseur_id = $request->superviseur_id;
        }
        $reclamation->admin_user_id = $auth->id;
        $reclamation->employe_id = $request->employe_id;
        $reclamation->motif = $request->motif;
        if(in_array($auth->role, ["rh", "resp_rh"]))
            $reclamation->status = "traite";
        else if($reclamation->status != "draft")
            $reclamation->status = "demande";

        if($auth->role != "superviseur"){
            $validator = Validator::make($request->all(), [
                'employe_id' => 'required',
                'motif' => 'required',
                'site_id' => 'required',
                'date_pointage' => 'required',
                'superviseur_id' => 'required',
            ]);
        }
        else {
            $validator = Validator::make($request->all(), [
                'employe_id' => 'required',
                'motif' => 'required',
                'site_id' => 'required',
                'date_pointage' => 'required',
            ]);
        }
        return $validator->setAttributeNames(self::$attributeNames);
    }

    public static function store(Request $request) {
        if(in_array($request->user()->role, ["rh", "resp_rh", "superviseur", "resp_sup", "resp_op" ])){
            if(!(in_array((new \DateTime($request->date_pointage))->format("H:i:s"), ["07:00:00", "18:00:00"]))){
                return ['error' => "Service incorrect"];
            }
            $reclamation = new Reclamation();
            if ($request->date_pointage) {
                $dt_ptg = new \DateTime($request->date_pointage);
                $current_date = new \DateTime();
                if ($dt_ptg > $current_date->sub(new \DateInterval('PT12H'))) {
                    return ['error' => "La date de pointage doit être une service déjà passée."];
                }
            }
            else
                return ['error' => "Veuillez ajouter au moins un pointage"];
            $validator = self::validateAndSetReclamation($request, $reclamation);
            if($validator->fails()){
                return ['error' => $validator->errors()->first()];
            }
            if($reclamation->save())
                HistoriqueController::new_reclamation($request, $reclamation);
                return response(["success" => "Réclamation enregistré", "id" => $reclamation->id]);
        }
        return response(["error" => "EACCES"]);
    }

    public static function update($id, Request $request) {
        $reclamation = Reclamation::find($id);
        if(
            (in_array($request->user()->role, ["rh", "resp_rh"]) && in_array($reclamation->status, ["demande", "traite"]))
            || ($request->user()->id == $reclamation->user_id && in_array($reclamation->status, ["draft"]))
        ){
            
            $validator = self::validateAndSetReclamation($request, $reclamation);
            if ($validator['error'])
                return $validator;
            if($validator->fails())
                return ['error' => $validator->errors()->first()];
            if($reclamation->employe_id != $request->employe_id)
                PointageReclamation::where('reclamation_id', $id)
                    ->update(['employe_id' => $request->employe_id, "updated_at" => new \Datetime()]);
            $reclamation->save();
            return response(["success" => "Réclamation modifié", "id" => $reclamation->id]);
        }
        return response(["error" => "EACCES"]);
    }

    public function do_traite(Request $request, $id){
        $reclamation = Reclamation::find($id);
        $old_reclamation = clone $reclamation;
        if(in_array($request->user()->role, ["rh", "resp_rh"]) && $reclamation->status == "demande"){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            
            $reclamation->status = "traite";
            $reclamation->updated_at = new \DateTime();

            if($reclamation->save()){
                HistoriqueController::action_reclamation($request, "Réclamation en cours de traitement", $id);
                return response(["success" => "Réclamation bien envoyée", "id" => $reclamation->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $reclamation = Reclamation::find($id);
        $auth = $request->user();
        if(in_array($auth->role, ["rh", "resp_rh"])){
            if($reclamation->status == "draft"){
                $reclamation->status = "demande";
                $reclamation->updated_at = new \DateTime();

                if($reclamation->save()){
                    HistoriqueController::action_reclamation($request, "Reclamation renvoyé", $id);
                    return response(["success" => "Renvoie de la demande réussi", "id" => $reclamation->id]);
                }
                return response(["error" => "Erreur d'envoi, réessayez"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_reclamation(Request $request, $id){
        $reclamation = Reclamation::find($id);
        if((in_array($request->user()->role, ['rh', 'resp_rh'])) && in_array($reclamation->status, ['demande', 'traite'])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $reclamation->status = 'draft';
            $reclamation->updated_at = new \DateTime();

            HistoriqueController::action_reclamation($request, "Reclamation annulé", $id);
            if($reclamation->save()){
                PointageReclamation::where('reclamation_id', $reclamation->id)->delete();
                return response(["success" => "Reclamation annulé", "id" => $reclamation->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done(Request $request, $id){
        $reclamation = Reclamation::find($id);
        if(in_array($request->user()->role, ['rh', 'resp_rh']) && in_array($reclamation->status, ['traite', 'demande'])){
            if (!$request->paye_suite) {
                $verifyDatePaie = DB::select("SELECT * FROM paies WHERE date_paie ='$request->date_paie' and  (status = 'traite' or status = 'done') and employe_id = '$request->employ_id'");
                if (count($verifyDatePaie) > 0) {
                    return (["error" => "Date de paie invalide"]);
                }
                $validator = Validator::make($request->all(), [
                    'note' => 'required',
                    'date_paie' => 'required',
                ]);
            }
            else {
                $validator = Validator::make($request->all(), [
                    'note' =>'required',
                ]);
            }
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $reclamation->status = 'done';
            $reclamation->date_paie = $request->date_paie;
            $reclamation->updated_at = new \DateTime();

            HistoriqueController::action_reclamation($request, "Réclamation terminé", $id);
            if($reclamation->save()){
                if (!$request->paye_suite) {
                    $paie = DB::select("SELECT * FROM paies WHERE date_paie = '$request->date_paie' and employe_id = '$reclamation->employe_id' and `status`='demande'");
                    if (count($paie) > 0) {
                            $paieController = New PaieController();
                            $recalculPaie = $paieController->GeneratePaie($request, $reclamation->employe_id);
                            $recalculPaie = $recalculPaie->getContent();
                            $responseData = json_decode($recalculPaie, true);
                            if (isset($responseData["success"])) {
                                return response(["success" => "Réclamation terminé avec modification de paie", "id" => $reclamation->id]);
                            }
                            if (isset($responseData["error"])) {
                                return response(["success" => "Réclamation terminé mais modification de paie echoué", "id" => $reclamation->id]);
                            }
                    }
                }
                return response(["success" => "Réclamation terminé", "id" => $reclamation->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
}
