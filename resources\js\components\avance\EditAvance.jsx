    import React, { useEffect, useState } from 'react'
    import { useParams } from 'react-router-dom';
    import Notification from '../notification/Notification';
    import InputEmploye from '../input/InputEmploye';
    import InputText from '../input/InputText';
    import ButtonSubmit from '../input/ButtonSubmit';
    import axios from 'axios';
    import useToken from '../util/useToken';
    import InputTypeAvance from '../input/InputTypeAvance';
    import Textarea from '../input/Textarea';
    import LoadingPage from '../loading/LoadingPage';
    import matricule from '../util/matricule';
    import InputMonthYear from '../input/InputMonthYear';
    import moment from 'moment';

    export default function EditAvance({ auth, title, action }) {
        const [notification, setNotification] = useState(null);
        const [isLoading, toggleLoading] = useState(false);
        // const [avance, setAvance] = useState(null);
        const [montant, setMontant] = useState(40000);
        const [error, setError] = useState("");
        const [employe, setEmploye] = useState(null);
        const [motif, setMotif] = useState("");
        const [disabledSubmit, setDisabledSubmit] = useState(false);
        const [type, setType] = useState(null);
        const [datePaie, setDatePaie] = useState()
        const params = useParams();
        
        useEffect(() => {
            let futureMonth = moment().add(1, 'M')
            if (parseInt(moment().format('dd')) > 20) {
                setDatePaie({
                    year: futureMonth().format("YYYY"),
                    month: futureMonth().format("MM"),
                });
            }
            setDatePaie({
                year: moment().format("YYYY"),
                month: moment().format("MM"),
            });
        }, [])
        
        useEffect(() => {
            if (type && type.name == "avance15" && montant > 40000) {
                setMontant(40000)
                setError("Le montant plafond est 40 000 Ar");
            }
        }, [montant, type])

        const handleSubmit = (e) => {
            e.preventDefault();
            setDisabledSubmit(true);
            let data = {}
            if(employe)
                data.employe_id = employe.id;
            data.montant = parseFloat(montant);
            if(type)
                data.type_avance_id = type.id;
            data.motif = motif;
            if (datePaie)
                data.date_paie = moment(datePaie.year + "-" + datePaie.month + "-20").format("YYYY-MM-DD");
            axios.post(action + (params.id ? params.id : ""), data, useToken()).then((response) => { 
                
                if (response.data.error) {
                    console.error(response.data.error);
                    setError(response.data.error)
                }
                else if (response.data.success) {
                    setNotification(response.data);
                }
            })
            .finally(() => {
                setDisabledSubmit(false);
            });
        }
        
        const getAvance = () => {
            toggleLoading(true)
            let isMounted = true;
            axios.get("/api/avance?id=" + params.id, useToken()).then((res) => {
                if (isMounted) {
                    if (res.data.avances) {
                        let avc = res.data.avances[0];
                        setMontant(avc.montant);
                        let emp = {
                            id: avc.employe_id,
                            nom: avc.employe,
                            matricule: matricule(avc)
                        };
                        let type_avc = {
                            id: avc.type_id,
                            name: avc.avc_type,
                            description: avc.type_description
                        }
                        setEmploye(emp);
                        setMotif(avc.motif)
                        if (res.data.avances[0].status =="draft") {
                            setMontant(0);
                            setType(null);
                        }
                        else
                            setType(type_avc);
                    }
                    else console.error("une erreur se produit");
                    toggleLoading(false);
                }
            })
        }

    useEffect(() => params.id && getAvance(), []);

    return (
        <div id="content">
            {
                isLoading ? 
                    <LoadingPage/> 
                : 
                    <div>
                        {
                            notification ? 
                                <Notification next={notification.id ? "/avance?id=" + notification.id : "/avance"} message={notification.success} /> 
                            :
                                <form onSubmit={handleSubmit}> 
                                    <div className="title-container">
                                        <h2>{ title }</h2>
                                    </div>
                                    {auth.role == "resp_rh" &&
                                        <InputMonthYear label="Date de paie" value={datePaie} onChange={ setDatePaie } />
                                    }
                                    <InputEmploye label="Employé" onChange={setEmploye} value={employe} required/>
                                    <InputText type="number" onChange={setMontant} value={montant} required label="Montant"/>
                                    <InputTypeAvance currentSelect={type} setCurrentSelect={setType} required />
                                    <Textarea label="Motif" value={motif} onChange={(value)=>setMotif(value)} />      
                                    <div>
                                        {
                                            error && <div className="container-error">{error}</div>
                                        }
                                    </div>
                                    <ButtonSubmit label="Enregistrer" disabled={disabledSubmit} />
                                </form>
                        }
                    </div>
            }
        </div>
    )
    }
