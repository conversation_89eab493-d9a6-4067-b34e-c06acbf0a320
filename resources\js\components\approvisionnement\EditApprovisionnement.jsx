import React, { useEffect, useState } from 'react';
import axios from 'axios';
import Item from './item/Item';

import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import InputText from '../input/InputText';

export default function EditApprovisionnement({title, action, auth}) {
    const [objet, setObjet] = useState("")
    const [items, setItems] = useState([])
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    useEffect(() => {
        disableSubmit(!objet.trim() || !items.length)
    }, [objet, items])

    const handleSubmitAll = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)

        const data = {
            objet: objet,
            items: items,
        }
        axios.post(action, data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }

    return (
        <div id="content">
            {
                notification ? 
                    <Notification next={notification.id ? "/da?id=" + notification.id : "/"} message={notification.success}/>
                :
                <div>
                    <div className="title-container" >
                        <h2>{title}</h2>
                    </div>
                    <InputText
                        required
                        label="Objet"
                        value={objet}
                        onChange={setObjet}/>
                    {
                    <Item 
                        auth={auth}
                        items={items}
                        setItems={setItems}/>
                    }
                    {
                        error &&
                        <div className='container-error'>
                            {error}
                        </div>
                    }
                    {
                        items.length > 0 &&
                        <div className="input-container-btn">
                            <button className='primary' disabled={submitDisabled} onClick={handleSubmitAll}>
                                Envoyer
                            </button>
                        </div>
                    }
                </div>
            }
        </div>
    )
}