import React, { useEffect, useState } from 'react';
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import InputSite from '../input/InputSite';
import Textarea from '../input/Textarea';
import InputText from '../input/InputText';
import removeDuplicateBreak from '../util/stringUtil';
import InputMultipleFile from '../input/InputMultipleFile';
import DualContainer from '../container/DualContainer';
import InputDateTime from '../input/InputDateTime';
// import InputEditor from '../input/InputEditor';
import moment from 'moment';
import LoadingPage from '../loading/LoadingPage';

export default function EditFaiMarquant({auth}) {
    const [site, setSite] = useState(null)
    const [commentaire, setCommentaire] = useState("")
    const [objet, setObjet] = useState("")
    const [notification, setNotification] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    const [files, setFiles] = useState([])
    const [dateVisite, setDateVisite] = useState()
    const [vigilances, setVigilances] = useState([]);
    const [start, setStart] = useState("");
    const [end, setEnd] = useState("");
    const [showVigilance, toggleVigilance] = useState(false);
    const canRefresh = true;
    const [refresh, setRefresh] = useState(false);

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const commentaireStr = removeDuplicateBreak(commentaire)
        const data = new FormData()
        data.append('site_id', site ? site.id : "")
        data.append('objet', objet)
        data.append('commentaire', commentaireStr)
        data.append('date_visite', dateVisite ? moment(dateVisite).format("YYYY-MM-DD HH:mm:ss") : "")
        data.append('start', start ?? "")
        data.append('end', end ?? "")
        files.forEach((file, index) => {
            data.append(`files[${index}]`, file);
        });
        axios.post('/api/fait_marquant/add', data,
            useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.error)
                setError(res.data.error)
            else
                setNotification(res.data)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }

    useEffect(() => {
        let isMounted = true
        toggleVigilance(false)
        if (site && site.id && site.pointeuse == 1 && dateVisite) {
            axios.get('/api/alarm?site_id=' + site.id 
                + '&date_visite=' + moment(dateVisite).format('YYYY-MM-DD HH:mm:ss'), useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.vigilances && res.data.vigilances.length > 0) {
                        setVigilances(res.data.vigilances)
                        let first = res.data.vigilances[0]
                        setStart(first.dtarrived)
                        setEnd(res.data.endVisite[0].date)
                        setRefresh(false)
                    } else {
                        setVigilances([])
                        setStart("")
                        setEnd("")
                        setRefresh(false)
                    }
                    toggleVigilance(true)
                }
            })
        }
        else toggleVigilance(true)
        return () => { isMounted = false };
    }, [site, dateVisite, refresh])

    return (
        <div id="content">
            {
                notification ? 
                    <Notification next={"/fait-marquant?id=" + notification.id} message={notification.success}/>
                :
                <div>
                    <div className="title-container">
                        <h2>Nouveau fait marquant</h2>
                    </div>
                    <form onSubmit={handleSubmit} encType='multipart/form-data'>
                        <InputSite
                            withoutDelete
                            required
                            value={site} 
                            onChange={(value) => setSite(value)}/>
                        <InputText
                            required
                            label="Objet"
                            value={objet}
                            onChange={(value) => {setObjet(value)}}/>
                        {
                            site && site.id && site.pointeuse == 1 && !["room"].includes(auth.role) && (
                                <>
                                    <InputDateTime
                                        required
                                        label="Date de visite"
                                        dateFormat="dd/MM/yyyy hh:mm"
                                        value={dateVisite}
                                        onChange={(dt) =>{setDateVisite(dt)}}
                                        canRefresh={canRefresh}
                                        setRefresh={setRefresh}
                                    />
                                    {
                                        showVigilance && dateVisite &&
                                        <div className='right'>
                                        {
                                            vigilances.length > 0 ?
                                                <div className='secondary'>
                                                    Empreinte faite : {vigilances.map(v => moment(v.dtarrived).format("HH[h]mm")).join(', ')}
                                                </div>
                                            :
                                            <div className='pink'>
                                                Aucune empreinte trouvé pour la date et heure mentionnée
                                            </div>
                                        }
                                        </div>
                                    }
                                    {
                                        showVigilance && vigilances && vigilances.length > 0 &&
                                        <DualContainer>
                                            <InputText
                                                label="Début"
                                                disabled={true}
                                                value={start && moment(start).format("HH[h]mm")}
                                            />
                                            <InputText
                                                label="Fin"
                                                disabled={true}
                                                value={end ? moment(end).format("HH[h]mm") : ""}
                                            />
                                        </DualContainer>
                                    }
                                </>
                            )
                        }
                        {
                            showVigilance ?
                                <Textarea
                                    required
                                    label={["superviseur", "resp_sup", "resp_op"].includes(auth.role) ? "Commentaire" : "Mesure prise"}
                                    value={commentaire}
                                    onChange={(value) => {setCommentaire(value)}}/>
                                :
                                    <LoadingPage/>

                        }
                            {/* <InputEditor content={commentaire} setContent={setCommentaire} /> */}
                        <InputMultipleFile files={files} setFiles={setFiles} />
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                        }
                        <ButtonSubmit disabled={submitDisabled}/>
                    </form>
                </div>
            }
        </div>
    )
}