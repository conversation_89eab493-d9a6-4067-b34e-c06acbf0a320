import React, { useEffect, useState } from 'react';

import axios from 'axios';
import useToken from '../util/useToken';
import InputMonthYear from '../input/InputMonthYear';
import InputCheckBox from '../input/InputCheckBox';
import InputText from '../input/InputText';
import Textarea from '../input/Textarea';
import moment from 'moment';
import showAmount from '../util/numberUtil';
import LoadingPage from '../loading/LoadingPage';

export default function DoneDeductionModal({ deduction , updateData, closeModal}) {
    const [date, setDate] = useState()
    const [mensuel, setMensuel] = useState(false)
    const [montant, setMontant] = useState('');
    const [repartitions, setRepartitions] = useState([])
    const [waitRepartition, setWaitRepartition] = useState(false)
    const [note, setNote] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    useEffect(() => {
        let isMounted = true
        const total = deduction.montant
        const mensualite = montant 
        if(isMounted && date && mensuel && mensualite && mensualite < total && (total / mensualite) < 12) {
            setWaitRepartition(true)
            setTimeout(() => {
                let reste = total - mensualite
                let currentPaie = moment(date.year + "-" + date.month + "-20")
                const repartitions = [{label: currentPaie.format("MMM YYYY"), date_paie : currentPaie.format("YYYY-MM-DD"), montant: mensualite}]
                while (reste > mensualite) {
                    reste = reste - mensualite
                    currentPaie = currentPaie.add(1, "month")
                    repartitions.push({label: currentPaie.format("MMM YYYY"), date_paie : currentPaie.format("YYYY-MM-DD"), montant: mensualite})
                }
                currentPaie = currentPaie.add(1, "month")
                repartitions.push({label: currentPaie.format("MMM YYYY"), date_paie : currentPaie.format("YYYY-MM-DD"), montant: reste})
                setWaitRepartition(false)
                setRepartitions(repartitions)
            }, 200)
        }
        else {
            setWaitRepartition(false)
            setRepartitions([])
        }

        return () => {isMounted = false};
    }, [date, mensuel, montant]);

    const handleOk = () => {
        disableSubmit(true)
        setError("")
        let formData = new FormData()
        formData.append("date_paie", date.year + "-" + date.month + "-20")
        if(mensuel){
            formData.append("mensualite", montant)
            if(repartitions.length > 1)
                formData.append("repartitions", JSON.stringify(repartitions.slice(1)))
        }
        formData.append("note", note)
        axios.post("/api/deduction/save_done/" + deduction.id, formData, useToken())
        .then((res) => {
            disableSubmit(false)
            if (res.data.success) {
                closeModal()
                updateData()
            }
            else if (res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if (res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    return <div className='modal'>
        <div>
            <h3>Terminer la déduction</h3>
            <InputMonthYear label="Fiche de paie" 
                required
                value={date}
                onChange={setDate}
            />
            <div className='checkbox-input-container'>
                <InputCheckBox label="Paiement mensuel" 
                    checked={mensuel}
                    onChange={setMensuel}
                />
            </div>
            {
                mensuel &&
                    <InputText label="Mensualité"
                        type="number"
                        value={montant}
                        onChange={setMontant}
                    />
            }
            {
                waitRepartition && <LoadingPage/>
            }
            {
                repartitions.length > 0 && 
                    <>
                        <div className='checkbox-input-container'>
                            <b>Répartition</b>
                        </div>
                        {
                            repartitions.map((rep) => (
                                <div key={rep.date_paie} className='card-container'>
                                    {showAmount(rep.montant)} <span className='secondary'>({rep.label})</span>
                                </div>
                            ))
                        }
                    </>
            }
            <Textarea label="Commentaire"
                value={note}
                onChange={(value) => setNote(value)}
            />
            {
                error &&
                <div className='container-error'>
                    {error}
                </div>
            }
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>
                    Envoyer
                </button>
                <button onClick={() => { closeModal() }}>Annuler</button>
            </div>
        </div>
    </div>
}