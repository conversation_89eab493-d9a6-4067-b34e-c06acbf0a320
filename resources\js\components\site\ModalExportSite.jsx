import React, { useEffect, useState } from 'react'
import InputCheckBox from '../input/InputCheckBox'
import './checkbox.css'
import useToken from '../util/useToken';
import ExcelSite from './ExcelSite';

export default function ModalExportSite({auth, closeModal}) {
    const [sites, setSites] = useState([])
    const [dataReformat, setDataReformat] = useState([])
    const [isLoading, toggleLoading] = useState(false)
    const [type, setType] = useState('')
    const [error, setError] = useState('')
    const [toggleState, setToggleState] = useState({
        manager: true,
        superviseur: false,
        secteur: false
    });

    const handleChangeType = (type) => {
        setToggleState({
            manager: type === 'manager',
            superviseur: type === 'superviseur',
            secteur: type === 'secteur'
        });
    };

    const getSite = () => {
        toggleLoading(true)
        axios.get('/api/site/export', useToken())
        .then((res) => {
            if (res.data.sites){
                if(res.data.sites.length == 0)
                    setError("Aucun site trouver")
                setSites(res.data.sites)
                toggleLoading(false)
            }
            else
                console.error(res.data.error)
        })
        .catch((e) => {
            console.error(e)
        })
    }
    useEffect(() => {
        getSite()
    }, [])

    useEffect(() => {
        let result = [];
        let groupingKey = '';
        let sortingKey = '';
    
        if (toggleState.manager) {
            groupingKey = 'manager_email';
            sortingKey = 'superviseur_id';
            setType('manager');
        } else if (toggleState.superviseur) {
            groupingKey = 'superviseur_email';
            sortingKey = 'manager_id';
            setType('superviseur');
        } else if (toggleState.secteur) {
            groupingKey = 'secteur';
            sortingKey = 'manager_id';
            setType('secteur');
        }
    
        if (groupingKey) {
            const groupedData = sites.reduce((acc, item) => {
                const key = item[groupingKey] || null;
                if (!acc[key]) {
                    acc[key] = [];
                }
                acc[key].push(item);
                return acc;
            }, {});
            result = Object.keys(groupedData)
                .sort((a, b) => {
                    if (a === 'null' || a === 'undefined') return -1;
                    if (b === 'null' || b === 'undefined') return 1;
                    return 0;
                })
                .reduce((sortedAcc, key) => {
                    sortedAcc[key] = groupedData[key].sort((a, b) => a[sortingKey] - b[sortingKey]);
                    return sortedAcc;
                }, {});
        }
    
        setDataReformat(result);
    }, [sites, toggleState]);
    
    

    return ( 
        <div className="modal">
            <div>
                <h3>Export Site</h3>
                <label>Groupé par :</label>
                {
                    !isLoading &&
                    <div className="card-container">
                        <div className='checkbox-align'>
                            <InputCheckBox label="Manager" checked={toggleState.manager} onChange={() => handleChangeType("manager")} />
                            <InputCheckBox label="Superviseur" checked={toggleState.superviseur} onChange={() => handleChangeType("superviseur")} />
                            <InputCheckBox label="Secteur" checked={toggleState.secteur} onChange={() => handleChangeType("secteur")} />
                        </div>
                    </div>
                }
                {
                    error &&
                    <div className="container-error">
                        { error }
                    </div>
                }
                <div className="form-button-container">
                    {
                        sites.length > 0 &&
                            <ExcelSite auth={auth} sites={dataReformat} type={type} closeModal={closeModal}/>
                    }
                    <button className='btn' onClick={closeModal} >
                        Fermer
                    </button>
                </div>
            </div>
        </div>
    )
}
