const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const { sendMail } = require('../auth')
const matricule = require('../resources/js/components/util/matricule')
moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const destination_vg = (emails) => {
    return ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
        "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
        "<EMAIL>"].concat(emails.map(e => e.email))
}

const destination_test = ["<EMAIL>"]

function sqlUpdateLastAnomalieExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_anomalie_service_export'"
}

const border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
}

const sqlSelectAnomalieExport = "SELECT value FROM params p WHERE p.key = 'last_anomalie_service_export'"

const sqlSelectOperationEmail = `SELECT u.email FROM users u 
    WHERE (u.blocked = 0 or u.blocked is null) AND u.role in ('resp_sup', 'resp_op') 
    AND u.email not in ('<EMAIL>', '<EMAIL>', '<EMAIL>') `;

function getDayOrNightExport(){
    let beginDay = moment().set({hour:7, minute:10, second:0})
    let endDay = moment().set({hour:19, minute:10, second:0})
    if(moment().isAfter(beginDay) && moment().isBefore(endDay))
        return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
    else {
        if(moment().isBefore(beginDay))
            return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 06:00:00"
        return moment().format("YYYY-MM-DD") + " 06:00:00"
    }
}

const getLastService = () => {
    const now = moment();
    const currentHour = now.hour();
    if (currentHour >= 6 && currentHour < 18) {
        const start = moment().subtract(1, 'day').set({ hour: 18, minute: 0, second: 0 });
        return start.format('YYYY-MM-DD HH:mm:ss')
    } else {
        const start = moment().set({ hour: 7, minute: 0, second: 0 });
        return start.format('YYYY-MM-DD HH:mm:ss')
    }
}

const sqlSelectReclamation = (dateString) => {
    let date = moment(dateString);
    if(date.hour() == 6){
        date = date.set({ hour: 7 });
    }
    date = date.format('YYYY-MM-DD HH:mm:ss');
    const sql = `SELECT r.id, r.date_pointage, r.employe_id as 'agent_id', r.type, r.superviseur_id, st.nom as site, r.agent_not_registered, st.idsite, st.group_pointage_id,
        ag.nom as agent, ag.societe_id, ag.numero_stagiaire, ag.numero_employe, ag.num_emp_soit, 
        ag.num_emp_saoi, adu.name as superviseur, adu.email as superviseur_email 
        FROM reclamations r 
        LEFT JOIN sites st ON st.idsite = r.site_id 
        LEFT JOIN employes ag ON ag.id = r.employe_id 
        LEFT JOIN users adu ON adu.id = r.superviseur_id 
        LEFT JOIN pointages ptg ON ptg.employe_id = r.employe_id AND ptg.date_pointage=r.date_pointage 
        WHERE r.date_pointage = '${date}'
        AND ptg.id is null 
        AND r.date_pointage <= '${getLastService()}' 
        ORDER BY r.date_pointage DESC `;
    return sql;
}

const sqlGetGroupPlanningId = (idsite) => {
    let sql = "SELECT idsite, group_planning_id FROM sites WHERE group_planning_id is not null";
    if(idsite.length > 0)
        sql = "SELECT idsite, group_planning_id FROM sites WHERE idsite IN (" + idsite.join(",") + ")";
    return sql;
}

const sqlSelectAnomalie = (dateString) => {
    const date_ptg_service = (moment(dateString).format('HH:mm:ss') == "18:00:00" ? moment(dateString).format('YYYY-MM-DD HH:mm:ss') : moment(dateString).add(1, 'hours').format('YYYY-MM-DD HH:mm:ss'))
    const date_pln_pointage =  moment(dateString).format('YYYY-MM-DD HH:mm:ss')
    const date_pl = moment(dateString).format('YYYY-MM')

    const sql = `SELECT COALESCE(st.group_planning_id, st.idsite) AS idsite_group, parent_site.nom AS site, 
            parent_site.group_planning_id AS parent_group_id, COUNT(DISTINCT ptg.id) AS ptgCount, cm_ptg.comment as 'room_comment', 
            COUNT(DISTINCT pl_ptg.id) AS agent_pl, GROUP_CONCAT(DISTINCT ptg.employe_id) AS 'pointages', 
            cm_pl.comment, GROUP_CONCAT(DISTINCT pl_ptg.agent_id) AS 'pl_pointages', parent_user.id AS 'superviseur_id',
            parent_user.name AS 'superviseur', parent_user.email AS 'superviseur_email', 
            parent_user.flotte AS 'superviseur_flotte', 
            parent_resp.id AS 'resp_sup_id', 
            parent_resp.name AS 'resp_sup', 
            parent_resp.email AS 'resp_sup_email' 
        FROM sites st 
        LEFT JOIN sites parent_site ON parent_site.idsite = COALESCE(st.group_planning_id, st.idsite)
        LEFT JOIN pointages ptg ON ptg.site_id = st.idsite AND (ptg.soft_delete IS NULL OR ptg.soft_delete = 0) AND ptg.date_pointage = '${date_ptg_service}'
        LEFT JOIN plannings pl ON pl.site_id = st.idsite AND pl.date_planning = '${date_pl}'
        LEFT JOIN planning_pointages pl_ptg ON pl_ptg.planning_id = pl.id AND pl_ptg.date_pointage = '${date_pln_pointage}'
        LEFT JOIN comment_plannings cm_pl ON cm_pl.id = (SELECT max(id) FROM comment_plannings WHERE planning_id = pl.id AND date_pointage = '${date_pln_pointage}') 
        LEFT JOIN comment_pointages cm_ptg ON cm_ptg.site_id = parent_site.idsite AND cm_ptg.date_pointage = '${date_ptg_service}'
        LEFT JOIN users parent_user ON parent_user.id = parent_site.superviseur_id 
        LEFT JOIN users parent_resp ON parent_resp.id = parent_site.resp_sup_id 
        WHERE 
            st.pointage = 1 
            AND (st.group_planning_id IS NULL OR st.group_planning_id = parent_site.idsite) 
        GROUP BY  
            idsite_group, cm_pl.id, cm_ptg.id, parent_site.nom, parent_site.group_planning_id, parent_user.id,
            parent_user.name, parent_user.email, parent_user.flotte, parent_resp.id, parent_resp.name ,
            parent_resp.email 
        HAVING 
            IFNULL(GROUP_CONCAT(DISTINCT ptg.employe_id ORDER BY ptg.employe_id ASC), '') 
            != IFNULL(GROUP_CONCAT(DISTINCT pl_ptg.agent_id ORDER BY pl_ptg.agent_id ASC), '')  
        ORDER BY 
            parent_site.nom ASC `;
    return sql;
}

const sqlSelectEmployes = (ids) => {
    return "SELECT * FROM employes WHERE id IN (" + ids.join(",") + ")";
}

const sqlIsFerie = "SELECT * FROM jour_feries WHERE date = ? ";

const sqlSelectHoraireEffectifPointage = (date_service, ferie) => {
    let horaire = 'day';
    if(moment(date_service).format("HH:mm:ss") == "18:00:00"){
        horaire= 'night'
    }
    let weekday = moment(date_service).isoWeekday();
    let field = `${horaire}_${weekday == 7 ? 0 : weekday }`; 
    if(ferie){
        field = `${horaire}_ferie`
    }
    return `SELECT st.idsite, st.nom AS 'site', COUNT(ptg.id), COUNT(rec.id), COUNT(ptg.id) + COUNT(rec.id) AS 'nb_ptg',
            he.${field} AS 'current_effectif', u.name AS 'resp_sup', u.email AS 'resp_sup_email', st.resp_sup_id,
            GROUP_CONCAT(DISTINCT child.idsite) AS 'all_sites'
        FROM sites st
        LEFT JOIN users u ON u.id = st.resp_sup_id
        LEFT JOIN sites child ON child.group_planning_id = st.idsite OR child.idsite = st.idsite
        LEFT JOIN reclamations rec 
            ON rec.site_id = child.idsite AND rec.date_pointage = ?
        LEFT JOIN horaire_effectifs he ON he.site_id = COALESCE(st.group_planning_id, st.idsite)
        LEFT JOIN pointages ptg 
            ON ptg.site_id = child.idsite 
            AND (ptg.soft_delete IS NULL OR ptg.soft_delete = 0) 
            AND ptg.date_pointage = ?
        WHERE 
            (st.soft_delete = 0 OR st.soft_delete IS NULL) 
            AND st.pointage = 1 
            AND (st.group_planning_id IS NULL OR st.group_planning_id = st.idsite)
        GROUP BY st.idsite, st.nom, ${field}
        HAVING nb_ptg <> current_effectif`
}

const sqlSelectHoraireEffectifPlanning = (date_service, ferie) => {
    let horaire = 'day';
    if(moment(date_service).format("HH:mm:ss") == "18:00:00") horaire= 'night';
    let field = `${horaire}_${moment(date_service).weekday()}`;
    if(ferie) field = `${horaire}_ferie`;
    return `SELECT st.idsite, st.nom as 'site', he.day_1, he.day_2, he.day_3, he.day_4, he.day_5,
            he.day_6, he.day_0, he.day_ferie, he.night_1, he.night_2, he.night_3, he.night_4, he.night_5, 
            he.night_6, he.night_0, he.night_ferie, count(pl_ptg.id) as 'nb_ptg', he.${field} as 'current_effectif', u.name as 'resp_sup', u.email as 'resp_sup_email', st.resp_sup_id
            FROM sites st 
            LEFT JOIN users u on u.id = st.resp_sup_id
            LEFT JOIN horaire_effectifs he ON he.site_id = st.idsite 
            LEFT JOIN plannings pl on pl.site_id = st.idsite AND pl.date_planning = ?
            LEFT JOIN planning_pointages pl_ptg ON pl_ptg.planning_id = pl.id AND pl_ptg.date_pointage = ?
            WHERE (st.soft_delete = 0 or st.soft_delete is null) AND st.pointage = 1 AND (st.group_planning_id is null or st.group_planning_id = st.idsite) GROUP BY st.idsite, st.nom 
            HAVING count(pl_ptg.id) <> he.${field} `;
}

function stringToArray(data) {
    if (!data || data.trim() === "") {
        return []
    }
    const ptgAgents = data.split(',').map(id => parseInt(id, 10));
    return ptgAgents;
}

function getEmploye(data, employes){
    let employesResult = [];
    data.map(id => {
        const employe = employes.find(e => e.id == id);
        employesResult.push({
            id: employe.id,
            agent_id: employe.id,
            societe_id: employe.societe_id,
            numero_employe: employe.numero_employe,
            num_emp_soit: employe.num_emp_soit,
            numero_stagiaire: employe.numero_stagiaire,
            numEmpSaoi: employe.num_emp_saoi,
            nom : employe.nom,
        })
    })
    return employesResult;
}

function reformatData(anomalies, reclamations, groupPlanningIdRec, employes){
    let datas = [];
    let countRec = 0;
    for (let j= 0; j < reclamations.length; j++) {
        const reclamation = reclamations[j];
        const matchingGroup = groupPlanningIdRec.find(r => r.idsite == reclamation.idsite);
        if (matchingGroup) {
            countRec++;
        }
        reclamation.group_planning_id = matchingGroup 
            ? (matchingGroup.group_planning_id ? matchingGroup.group_planning_id : matchingGroup.idsite)
            : reclamation.idsite;
    }
    console.log("nombre de réclamations trouvées : " + countRec);
    for (let i = 0; i < anomalies.length; i++) {
        const anomalie = anomalies[i];
        anomalie.reclamations = reclamations.filter(r => r.group_planning_id == anomalie.idsite_group);      
        let plPtgIds = stringToArray(anomalies[i].pl_pointages);
        let ptgIds = stringToArray(anomalies[i].pointages);
        const duplicateIds = plPtgIds.filter(id => ptgIds.includes(id));

        ptgIds = ptgIds.filter(id => !duplicateIds.includes(id));
        plPtgIds = plPtgIds.filter(id => !duplicateIds.includes(id));

        const nonPointes = ptgIds.filter(id => !plPtgIds.includes(id)).length;
        const nonPlannings = plPtgIds.filter(id => !ptgIds.includes(id)).length;

        const manque = (Number(anomalie.agent_pl) - (Number(anomalie.ptgCount) + Number(anomalie.reclamations.length))) > 0 ? (Number(anomalie.agent_pl) - (Number(anomalie.ptgCount) + Number(anomalie.reclamations.length))) : 0;
        const surplus = ((Number(anomalie.ptgCount) + Number(anomalie.reclamations.length)) - Number(anomalie.agent_pl)) > 0 ? ((Number(anomalie.ptgCount) + Number(anomalie.reclamations.length)) - Number(anomalie.agent_pl)) : 0;
        const countReclamation = anomalie.reclamations.length;
        let incoherence = 0
        
        const countOccurrences = (array) =>
            array.reduce((acc, id) => {
                acc[id] = (acc[id] || 0) + 1;
                return acc;
            }, {});
        
        const plPtgAgentCounts = countOccurrences(plPtgIds);
        const ptgAgentCounts = countOccurrences(ptgIds);

        for (const [id, count] of Object.entries(plPtgAgentCounts)) {
            if (ptgAgentCounts[id] !== undefined) {
                const difference = count - ptgAgentCounts[id];
                if (difference > 0) {
                    incoherence += difference;
                }
            } else {
                incoherence += count;
            }
        }

        for (const [id, count] of Object.entries(ptgAgentCounts)) {
            if (plPtgAgentCounts[id] !== undefined) {
                const difference = count - plPtgAgentCounts[id];
                if (difference > 0) {
                    incoherence += difference;
                }
            } else {
                incoherence += count;
            }
        }
        const data = {
            idsite: anomalie.idsite,
            site : anomalie.site,
            agentPlaning: anomalie.agent_pl,
            agentPointe: anomalie.ptgCount,
            nbAgentDay: anomalie.nb_agent_day,
            nbAgentNight: anomalie.nb_agent_night,
            superviseurId: anomalie.superviseur_id,
            superviseur: anomalie.superviseur,
            superviseurEmail: anomalie.superviseur_email,
            superviseurFlotte: anomalie.superviseur_flotte,
            respSupId: anomalie.resp_sup_id,
            respSup: anomalie.resp_sup,
            respSupEmail: anomalie.resp_sup_email,
            incoherence: incoherence,
            manque: manque,
            surplus: surplus,
            countReclamation: countReclamation,
            nonPlannings: nonPlannings,
            nonPointes: nonPointes,
            planningPtgAgents: getEmploye(plPtgIds, employes),
            ptgAgents: getEmploye(ptgIds, employes),
            comment_room: anomalie.room_comment,
            comment: anomalie.comment,
            date_ptg_comment: anomalie.ptg_comment,
            reclamations: anomalie.reclamations, 
        };
        if(!(data.manque > 0 || data.surplus > 0) && data.incoherence > 0){
            const reclamationAgentIds = data.reclamations.map(r => r.agent_id).filter(id => id); 
            data.planningPtgAgents = data.planningPtgAgents.filter(agent => !reclamationAgentIds.includes(agent.id));
            data.reclamations = data.reclamations.filter(r => !data.planningPtgAgents.find(agent => agent.id === r.agent_id));
            if (data.planningPtgAgents.length > 0) {
                datas.push(data);
            }
        }
        else
            datas.push(data);
    }
    datas.sort((a, b) => {
        if (a.respSupId === b.respSupId) {
            return a.site.localeCompare(b.site);
        }
        return a.respSupId - b.respSupId;
    });
    return datas;
}
function reclamationByType(reclamations){
    return reclamations.reduce((acc, item) => {
        const type = item.type;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(item);
        return acc;
    }, {});
}
function generateReclamationExport(reclamations, workbook){
    const groupedByType = reclamationByType(reclamations);
    for (const type in groupedByType) {
        const title = type == "sm" ? "Agent non enregistré" : (type == "service24" ? "Manque de demande service24" : (type == "mis_a_pied" ? "Agent mis à pied" : (type == "archive" ? "Agent archivé" : "")));
        const worksheetReclamation = workbook.addWorksheet(title)
        type == "sm" ? worksheetReclamation.mergeCells('A1:C1') : worksheetReclamation.mergeCells('A1:D1');
        worksheetReclamation.getCell('A1').value = title.toUpperCase() + "(" + groupedByType[type].length + ")";
        worksheetReclamation.getCell('A1').font = { bold: true, size: 14, color: { argb: "FFFFFFFF" } };
        worksheetReclamation.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };
        worksheetReclamation.getCell('A1').fill = { 
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: "327666" } 
        };
        const headerReclmation =  ["Matricule", "Agent", "Site", "Superviseur"]
        if (type == "sm")
            headerReclmation.splice(0, 1);
        const rowHeader = worksheetReclamation.addRow(headerReclmation);
        rowHeader.eachCell({ includeEmpty: false }, (cell) => {
            cell.border = border
        })
        groupedByType[type].forEach((rec, index) => {
            const row = [
                rec.agent_id ? matricule(rec) : "[SM] - ",
                rec.agent_id ? rec.agent : rec.agent_not_registered,
                rec.site,
                ![0, null].includes(rec.superviseur_id) ? (rec.superviseur + (rec.superviseur_email ? " <" + rec.superviseur_email + ">" : "")) : ""
            ]
            if (type == "sm")
                row.splice(0, 1);
            const addedRow = worksheetReclamation.addRow(row);
            addedRow.eachCell({ includeEmpty: false }, (cell) => {
                cell.border = border
            })
            if (index % 2 !== 0) {
                addedRow.eachCell((cell) => {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'F2F2F2' }
                    };
                });
            }
        })
        worksheetReclamation.getColumn('A').width = type == "sm" ? 60 : 15;
        worksheetReclamation.getColumn('B').width = 60;
        worksheetReclamation.getColumn('C').width = 50;
        worksheetReclamation.getColumn('D').width = 60;
        worksheetReclamation.getRow(2).font = { bold: true };
    }
}

function generateAnomalieExport(anomalies, workbook){
    const manque = []
    const surplus = []
    const anomalieIncoherence = []
    anomalies.forEach(anomalie => {
        if(anomalie.manque > 0){
            manque.push(anomalie)
        }
        else if(anomalie.surplus > 0){
            surplus.push(anomalie)
        }
        else{
            anomalieIncoherence.push(anomalie)
        }
    });
    
    const worksheetManque = workbook.addWorksheet("MANQUE")
    const worksheetSurplus = workbook.addWorksheet("SURPLUS")

    worksheetManque.mergeCells('A1:H1');
    worksheetManque.getCell('A1').value = "MANQUE D'EFFECTIF (" + manque.reduce((acc, item) => acc + item.manque, 0) + ")";
    worksheetManque.getCell('A1').font = { bold: true, size: 14, color: { argb: "FFFFFFFF" } };
    worksheetManque.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };
    worksheetManque.getCell('A1').fill = { 
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: "327666" } 
    };

    worksheetManque.mergeCells('A2:A3');
    worksheetManque.mergeCells('B2:E2');
    worksheetManque.mergeCells('F2:G2');
    worksheetManque.mergeCells('H2:H3');

    worksheetManque.getCell('A2').value = "SITE";
    worksheetManque.getCell('B2').value = "NB AGENT";
    worksheetManque.getCell('B3').value = "Planning";
    worksheetManque.getCell('C3').value = "Pointés";
    worksheetManque.getCell('D3').value = "Réclamations";
    worksheetManque.getCell('E3').value = "Manque";
    worksheetManque.getCell('F2').value = "COMMENTAIRE";
    worksheetManque.getCell('F3').value = "Control room";
    worksheetManque.getCell('G3').value = "Manager";
    worksheetManque.getCell('H2').value = "MANAGER";

    ['A2', 'B2', 'B3', 'C3', 'D3', 'F2', 'E3', 'F3', 'G3', 'H2'].forEach(col => {
        worksheetManque.getCell(col).alignment = { horizontal: 'center', vertical: 'middle' };
        worksheetManque.getCell(col).font = { bold: true };
        worksheetManque.getCell(col).border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
        }
    });

    worksheetManque.views = [
        { state: 'frozen', ySplit: 3 }
    ];

    let line = 4
    manque.forEach(anomalie => {
        worksheetManque.getCell('A' + line).value = anomalie.site
        worksheetManque.getCell('B' + line).value = anomalie.agentPlaning
        worksheetManque.getCell('C' + line).value = anomalie.agentPointe
        worksheetManque.getCell('D' + line).value = anomalie.countReclamation
        worksheetManque.getCell('E' + line).value = anomalie.manque
        worksheetManque.getCell('F' + line).value = anomalie.comment_room
        worksheetManque.getCell('G' + line).value = anomalie.comment ?? ' '
        worksheetManque.getCell('H' + line).value = anomalie.respSup ? (anomalie.respSup + (anomalie.respSupEmail ? " <" + anomalie.respSupEmail + ">" : "")) : ""
        worksheetManque.getCell('B' + line).alignment = { vertical: 'middle', horizontal: 'center' };
        worksheetManque.getCell('C' + line).alignment = { vertical: 'middle', horizontal: 'center' };
        worksheetManque.getCell('D' + line).alignment = { vertical: 'middle', horizontal: 'center' };
        worksheetManque.getCell('E' + line).alignment = { vertical: 'middle', horizontal: 'center' };
        ['A' + line, 'B' + line, 'C' + line, 'D' + line, 'E' + line, 'F' + line, 'G' + line, 'H' + line].forEach(col => {
            worksheetManque.getCell(col).border = border;
        });
        line++
    })

    worksheetManque.getColumn('A').width = 50
    worksheetManque.getColumn('B').width = 20
    worksheetManque.getColumn('C').width = 20
    worksheetManque.getColumn('D').width = 20
    worksheetManque.getColumn('E').width = 20
    worksheetManque.getColumn('F').width = 60
    worksheetManque.getColumn('G').width = 60
    worksheetManque.getColumn('H').width = 50

    worksheetSurplus.mergeCells('A1:H1');
    worksheetSurplus.getCell('A1').value = "SURPLUS D'EFFECTIF (" + surplus.reduce((acc, item) => acc + item.surplus, 0) + ")";
    worksheetSurplus.getCell('A1').font = { bold: true, size: 14, color: { argb: "FFFFFFFF" } }; 
    worksheetSurplus.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };
    worksheetSurplus.getCell('A1').fill = { 
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: "327666" } 
    }; 

    worksheetSurplus.mergeCells('A2:A3');
    worksheetSurplus.mergeCells('B2:E2');
    worksheetSurplus.mergeCells('F2:G2');
    worksheetSurplus.mergeCells('H2:H3');

    worksheetSurplus.getCell('A2').value = "SITE";
    worksheetSurplus.getCell('B2').value = "NB AGENT";
    worksheetSurplus.getCell('B3').value = "Planning";
    worksheetSurplus.getCell('C3').value = "Pointés";
    worksheetSurplus.getCell('D3').value = "Réclamations";
    worksheetSurplus.getCell('E3').value = "Surplus";
    worksheetSurplus.getCell('F2').value = "COMMENTAIRE";
    worksheetSurplus.getCell('F3').value = "Control room";
    worksheetSurplus.getCell('G3').value = "Manager";
    worksheetSurplus.getCell('H2').value = "MANAGER";

    ['A2', 'B2', 'B3', 'C3', 'D3', 'E3', 'F2', 'F3', 'G3', 'H2'].forEach(col => {
        worksheetSurplus.getCell(col).alignment = { horizontal: 'center', vertical: 'middle' };
        worksheetSurplus.getCell(col).font = { bold: true };
        worksheetSurplus.getCell(col).border = border;
    });

    worksheetSurplus.views = [
        { state: 'frozen', ySplit: 3 }
    ];

    line = 4
    surplus.forEach(anomalie => {
        worksheetSurplus.getCell('A' + line).value = anomalie.site
        worksheetSurplus.getCell('B' + line).value = anomalie.agentPlaning
        worksheetSurplus.getCell('C' + line).value = anomalie.agentPointe
        worksheetSurplus.getCell('D' + line).value = anomalie.countReclamation
        worksheetSurplus.getCell('E' + line).value = anomalie.surplus
        worksheetSurplus.getCell('F' + line).value = anomalie.comment_room
        worksheetSurplus.getCell('G' + line).value = anomalie.comment ?? ' '
        worksheetSurplus.getCell('H' + line).value = anomalie.respSup ? (anomalie.respSup + (anomalie.respSupEmail ? " <" + anomalie.respSupEmail + ">" : "")) : ""
        worksheetSurplus.getCell('B' + line).alignment = { vertical: 'middle', horizontal: 'center' };
        worksheetSurplus.getCell('C' + line).alignment = { vertical: 'middle', horizontal: 'center' };
        worksheetSurplus.getCell('E' + line).alignment = { vertical: 'middle', horizontal: 'center' };
        worksheetSurplus.getCell('D' + line).alignment = { vertical: 'middle', horizontal: 'center' };
        ['A' + line, 'B' + line, 'C' + line, 'D' + line, 'E' + line, 'F' + line, 'G' + line, 'H' + line].forEach(col => {
            worksheetSurplus.getCell(col).border = border;
        });
        line++
    })


    worksheetSurplus.getColumn('A').width = 50
    worksheetSurplus.getColumn('B').width = 20
    worksheetSurplus.getColumn('C').width = 20
    worksheetSurplus.getColumn('D').width = 20
    worksheetSurplus.getColumn('E').width = 20
    worksheetSurplus.getColumn('F').width = 60
    worksheetSurplus.getColumn('G').width = 60
    worksheetSurplus.getColumn('H').width = 50

    const worksheetIncoherence = workbook.addWorksheet("INCOHERENCE")

    worksheetIncoherence.mergeCells('A1:F1');
    worksheetIncoherence.getCell('A1').value = "INCOHÉRENCES SUR LES AGENTS EN POSTE (" + anomalieIncoherence.reduce((acc, item) => acc + item.planningPtgAgents.length ,0) + ")";
    worksheetIncoherence.getCell('A1').font = { bold: true, size: 14, color: { argb: "FFFFFFFF" } };
    worksheetIncoherence.getCell('A1').fill = { 
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: "327666" } 
    };

    worksheetIncoherence.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };

    worksheetIncoherence.mergeCells('A2:A3');
    worksheetIncoherence.mergeCells('D2:E2');
    worksheetIncoherence.mergeCells('B2:C2');
    worksheetIncoherence.mergeCells('F2:F3');

    worksheetIncoherence.getCell('A2').value = "SITE";
    worksheetIncoherence.getCell('B2').value = "AGENT";
    worksheetIncoherence.getCell('B3').value = "Planning";
    worksheetIncoherence.getCell('C3').value = "Pointés";
    worksheetIncoherence.getCell('D2').value = "COMMENTAIRE";
    worksheetIncoherence.getCell('D3').value = "Control room";
    worksheetIncoherence.getCell('E3').value = "Manager";
    worksheetIncoherence.getCell('F2').value = "MANAGER";

    ['A2', 'B2', 'B3', 'C3', 'D2', 'D3', 'E3', 'F2'].forEach(col => {
        worksheetIncoherence.getCell(col).alignment = { horizontal: 'center', vertical: 'middle' };
        worksheetIncoherence.getCell(col).font = { bold: true };
        worksheetIncoherence.getCell(col).border = border;
    });

    worksheetIncoherence.views = [
        { state: 'frozen', ySplit: 3 }
    ];

    worksheetIncoherence.getColumn('A').width = 50
    worksheetIncoherence.getColumn('B').width = 50
    worksheetIncoherence.getColumn('C').width = 50
    worksheetIncoherence.getColumn('D').width = 60
    worksheetIncoherence.getColumn('E').width = 60
    worksheetIncoherence.getColumn('F').width = 50

    line = 5
    let lastColor = 'FFD3D3D3';
    anomalieIncoherence.forEach(anomalie => {
        const reclamationAgentIds = anomalie.reclamations.map(r => r.agent_id).filter(id => id); 
        anomalie.planningPtgAgents = anomalie.planningPtgAgents.filter(agent => !reclamationAgentIds.includes(agent.id));
        anomalie.reclamations = anomalie.reclamations.filter(r => !anomalie.planningPtgAgents.find(agent => agent.id === r.agent_id));
        if (anomalie.planningPtgAgents.length === 0) {
            return; 
        }
        lastColor = lastColor === 'FFD3D3D3' ? 'F0F0F0' : 'FFD3D3D3';
        worksheetIncoherence.getCell('A' + line).value = anomalie.site
        worksheetIncoherence.getCell('E' + line).value = anomalie.comment ?? ' '
        worksheetIncoherence.getCell('D' + line).value = anomalie.comment_room
        worksheetIncoherence.getCell('F' + line).value = anomalie.respSup ? (anomalie.respSup + (anomalie.respSupEmail ? " <" + anomalie.respSupEmail + ">" : "")) : ""
        worksheetIncoherence.getCell('A' + line).border = border;
        worksheetIncoherence.getCell('D' + line).border = border;
        worksheetIncoherence.getCell('E' + line).border = border;
        worksheetIncoherence.getCell('F' + line).border = border;
        let numberLinePl = line
        anomalie.planningPtgAgents.forEach(agent => {
            worksheetIncoherence.getCell('B' + numberLinePl).value = "[" + matricule(agent) + "] " + agent.nom 
            worksheetIncoherence.getCell(`B${numberLinePl}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lastColor } };
            worksheetIncoherence.getCell(`B${numberLinePl}`).border = border;
            numberLinePl++
        })
        let numberLinePtg = line
        const pointageAndReclamation = anomalie.ptgAgents.concat(anomalie.reclamations.map(r => {
            return {
                agent_id: r.agent_id,
                societe_id: r.societe_id,
                numero_employe: r.numero_employe,
                num_emp_soit: r.num_emp_soit,
                numero_stagiaire: r.numero_stagiaire,
                numEmpSaoi: r.num_emp_saoi,
                nom : r.agent_id ? r.agent : r.agent_not_registered,
            }
        }));
        pointageAndReclamation.forEach(agent => {
            worksheetIncoherence.getCell('C' + numberLinePtg).value = (agent.agent_id ? "[" + matricule(agent) + "] " : "[Non enregistré] ") + agent.nom 
            worksheetIncoherence.getCell(`C${numberLinePtg}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lastColor } };
            worksheetIncoherence.getCell(`C${numberLinePtg}`).border = border;
            numberLinePtg++
        })
        let currentLine = numberLinePl > numberLinePtg ? numberLinePl : numberLinePtg
        worksheetIncoherence.mergeCells(`A${line}:A${currentLine - 1}`);
        worksheetIncoherence.mergeCells(`D${line}:D${currentLine - 1}`);
        worksheetIncoherence.mergeCells(`E${line}:E${currentLine - 1}`);
        worksheetIncoherence.mergeCells(`F${line}:F${currentLine - 1}`);
        worksheetIncoherence.getCell(`A${line}`).alignment = { vertical: 'middle'};
        worksheetIncoherence.getCell(`F${line}`).alignment = { vertical: 'middle'};
        worksheetIncoherence.getCell(`F${line}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lastColor } };
        worksheetIncoherence.getCell(`A${line}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lastColor } };
        worksheetIncoherence.getCell(`D${line}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lastColor } };
        worksheetIncoherence.getCell(`E${line}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lastColor } };
        ['A' + line, 'F' + line].forEach(col => {
            worksheetIncoherence.getCell(col).border = border;
        });
        line = currentLine + 1
    })      
}

function generateAnomaliePointagePlanningEffectif(datas, workbook, isPlanning = false){
    let title = isPlanning ? 'PLANNING - EFFECTIF' : 'POINTAGE - EFFECTIF'
    const worksheetPtgEffManque = workbook.addWorksheet("Manque")
    worksheetPtgEffManque.mergeCells('A1:H1');
    worksheetPtgEffManque.getCell('A1').value = `MANQUE ${title} (${datas.reduce((sum, item) => {
        if (item.nb_ptg < item.current_effectif) {
            return sum + (item.current_effectif - item.nb_ptg);
        }
        return sum;
    }, 0)})`;
    worksheetPtgEffManque.getCell('A1').font = { bold: true, size: 14, color: { argb: "FFFFFFFF" } };
    worksheetPtgEffManque.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };
    worksheetPtgEffManque.getCell('A1').fill = { 
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: "327666" } 
    };
    
    worksheetPtgEffManque.mergeCells('A2:A3');
    worksheetPtgEffManque.mergeCells('B2:D2');
    worksheetPtgEffManque.mergeCells('E2:E2');

    worksheetPtgEffManque.getCell('A2').value = "SITE";
    worksheetPtgEffManque.getCell('B2').value = "NB AGENT";
    worksheetPtgEffManque.getCell('B3').value = isPlanning ? "Planning" : "Pointage";
    worksheetPtgEffManque.getCell('C3').value = "Horaire effectif";
    worksheetPtgEffManque.getCell('D3').value = "Manque";
    worksheetPtgEffManque.getCell('E2').value = "MANAGER";
    worksheetPtgEffManque.views = [
        { state: 'frozen', ySplit: 3 }
    ];

    ['A2', 'B2', 'B3', 'C3', 'D3', 'E2'].forEach(col => {
        worksheetPtgEffManque.getCell(col).border = border;
        worksheetPtgEffManque.getCell(col).alignment = { vertical: 'middle', horizontal: 'center' };
    });

    let line = 4
    datas.forEach(data =>{
        if(data.current_effectif > data.nb_ptg){
            worksheetPtgEffManque.getCell('A' + line).value = data.site
            worksheetPtgEffManque.getCell('B' + line).value = data.nb_ptg
            worksheetPtgEffManque.getCell('C' + line).value = data.current_effectif
            worksheetPtgEffManque.getCell('D' + line).value = data.current_effectif - data.nb_ptg
            worksheetPtgEffManque.getCell('E' + line).value = data.resp_sup_id ? `${data.resp_sup}  <${data.resp_sup_email}>` : ""
            worksheetPtgEffManque.getCell('B' + line).alignment = { vertical: 'middle', horizontal: 'center' };
            worksheetPtgEffManque.getCell('C' + line).alignment = { vertical: 'middle', horizontal: 'center' };
            worksheetPtgEffManque.getCell('D' + line).alignment = { vertical: 'middle', horizontal: 'center' };
            worksheetPtgEffManque.getCell('E' + line).alignment = { vertical: 'middle', horizontal: 'center' };
            ['A' + line, 'B' + line, 'C' + line, 'D' + line, 'E' + line].forEach(col => {
                worksheetPtgEffManque.getCell(col).border = border;
            });
            line++
        }
    })

    const worksheetPtgEffPlus = workbook.addWorksheet("Surplus")
    worksheetPtgEffPlus.mergeCells('A1:H1');
    worksheetPtgEffPlus.getCell('A1').value = `SURPLUS ${title} (${datas.reduce((sum, item) => {
        if (item.nb_ptg > item.current_effectif) {
            return sum + (item.nb_ptg - item.current_effectif);
        }
        return sum;
      }, 0)})`;
    worksheetPtgEffPlus.getCell('A1').font = { bold: true, size: 14, color: { argb: "FFFFFFFF" } };
    worksheetPtgEffPlus.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };
    worksheetPtgEffPlus.getCell('A1').fill = { 
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: "327666" } 
    };

    worksheetPtgEffPlus.mergeCells('A2:A3');
    worksheetPtgEffPlus.mergeCells('B2:D2');
    worksheetPtgEffPlus.mergeCells('E2:E2');
    worksheetPtgEffPlus.getCell('A2').value = "SITE";
    worksheetPtgEffPlus.getCell('B2').value = "NB AGENT";
    worksheetPtgEffPlus.getCell('B3').value = isPlanning ? "Planning" : "Pointage";
    worksheetPtgEffPlus.getCell('C3').value = "Horaire effectif";
    worksheetPtgEffPlus.getCell('D3').value = "SurPlus";
    worksheetPtgEffPlus.getCell('E2').value = "MANAGER";
    worksheetPtgEffPlus.views = [
        { state: 'frozen', ySplit: 3 }
    ];
    ['A2', 'B2', 'B3', 'C3', 'D3', 'E2'].forEach(col => {
        worksheetPtgEffPlus.getCell(col).border = border;
        worksheetPtgEffPlus.getCell(col).alignment = { vertical: 'middle', horizontal: 'center' };
    });

    line = 4
    datas.forEach(data =>{
        if(data.current_effectif < data.nb_ptg){
            worksheetPtgEffPlus.getCell('A' + line).value = data.site
            worksheetPtgEffPlus.getCell('B' + line).value = data.nb_ptg
            worksheetPtgEffPlus.getCell('C' + line).value = data.current_effectif
            worksheetPtgEffPlus.getCell('D' + line).value = data.nb_ptg - data.current_effectif
            worksheetPtgEffPlus.getCell('E' + line).value = data.resp_sup_id ? `${data.resp_sup}  <${data.resp_sup_email}>` : ""
            worksheetPtgEffPlus.getCell('B' + line).alignment = { vertical: 'middle', horizontal: 'center' };
            worksheetPtgEffPlus.getCell('C' + line).alignment = { vertical: 'middle', horizontal: 'center' };
            worksheetPtgEffPlus.getCell('D' + line).alignment = { vertical: 'middle', horizontal: 'center' };
            worksheetPtgEffPlus.getCell('E' + line).alignment = { vertical: 'middle', horizontal: 'center' };
            ['A' + line, 'B' + line, 'C' + line, 'D' + line, 'E' + line].forEach(col => {
                worksheetPtgEffPlus.getCell(col).border = border;
            });
            line++
        }
    })

    worksheetPtgEffPlus.getColumn('A').width = 50
    worksheetPtgEffPlus.getColumn('B').width = 30
    worksheetPtgEffPlus.getColumn('C').width = 30
    worksheetPtgEffPlus.getColumn('D').width = 40
    worksheetPtgEffPlus.getColumn('E').width = 60
    worksheetPtgEffManque.getColumn('A').width = 50
    worksheetPtgEffManque.getColumn('B').width = 30
    worksheetPtgEffManque.getColumn('C').width = 30
    worksheetPtgEffManque.getColumn('D').width = 40
    worksheetPtgEffManque.getColumn('E').width = 60
}

function doAnomalieExport(dateString ){
    console.log('doAnomalieExport',dateString)
    pool.query(sqlSelectOperationEmail, [], async (err, emails) => {
        if(err)
            console.error(err)
        else {
            console.log("Nb emails: " + emails.length)
            pool.query(sqlSelectAnomalie(dateString), [], async (err, anomalies) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb anomalies: " + anomalies.length)
                    let allAgents = []
                    allAgents = anomalies.map(anomalie => {
                        const planningPtgAgents = stringToArray(anomalie.pl_pointages)
                        const ptgAgents = stringToArray(anomalie.pointages)
                        return {
                            Ids: ptgAgents.concat(planningPtgAgents),
                        }
                    })
                    console.log("allAgents: " + allAgents.length)
                    const IdsConcat = allAgents.flatMap(item => item.Ids);
                    pool.query(sqlSelectEmployes(IdsConcat), [], async (err, employes) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Nb employes: " + employes.length)
                            pool.query(sqlSelectReclamation(dateString), [], async (err, reclamations) => {
                                if(err)
                                    console.error(err)
                                else {
                                    console.log("Nb reclamations: " + reclamations.length)
                                    pool.query(sqlIsFerie, [moment(dateString).format("YYYY-MM-DD")], async(err, isFerie) =>{
                                        if(err)
                                            console.error(err)
                                        else{
                                            const realDatePointage = moment(dateString).format("HH") == "06" ? moment(dateString).format("YYYY-MM-DD") + " 07:00:00" : dateString
                                            pool.query(sqlSelectHoraireEffectifPointage(dateString, isFerie.length > 0 ), [realDatePointage, realDatePointage], async(err, effectifPointage)=>{
                                                if(err)
                                                    console.error(err)
                                                else{
                                                    console.log("Nb Anomalie Effectif-Planning", effectifPointage.length)
                                                    const dateAddOneDay = moment(dateString).clone().add(1, 'day').format("YYYY-MM-DD HH:mm:ss");
                                                    pool.query(sqlSelectHoraireEffectifPlanning(dateAddOneDay, isFerie.length > 0 ), [moment(dateAddOneDay).format("YYYY-MM"), dateAddOneDay], async(err, effectifPlanning)=>{
                                                        if(err)
                                                            console.error(err)
                                                        else{
                                                            console.log("Nb Anomalie Effectif-Planning", effectifPlanning.length)
                                                            pool.query(sqlGetGroupPlanningId(reclamations.map(r => r.idsite)), [], async (err, groupPlanningIdReclamations) => {
                                                                if(err)
                                                                    console.error(err)
                                                                else{
                                                                    dataReformat = reformatData(anomalies, reclamations, groupPlanningIdReclamations, employes)
                                                                    const anomalieManque = []
                                                                    const anomalieSurplus = []
                                                                    const anomalieIncoherence = []
                                                                    dataReformat.forEach(anomalie => {
                                                                        if(anomalie.manque > 0){
                                                                            anomalieManque.push(anomalie)
                                                                        }
                                                                        else if(anomalie.surplus > 0){
                                                                            anomalieSurplus.push(anomalie)
                                                                        }
                                                                        else{
                                                                            anomalieIncoherence.push(anomalie)
                                                                        }
                                                                    });
                                                                    const arrayFile = []

                                                                    const workbookReclamation = new Excel.Workbook()
                                                                    let reformatReclamations = reclamationByType(reclamations)
                                                                    generateReclamationExport(reclamations, workbookReclamation)
                                                                    const headerReclamation = "Réclamation " + moment(dateString).format("DD MMM YYYY") + " " +( moment(dateString).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR");
                                                                    const reclamationBuffer = await workbookReclamation.xlsx.writeBuffer()
                                                                    arrayFile.push({
                                                                        filename: `${headerReclamation}.xlsx`,
                                                                        content: reclamationBuffer
                                                                    })

                                                                    const workbookPointageEffectif = new Excel.Workbook()
                                                                    generateAnomaliePointagePlanningEffectif(effectifPointage, workbookPointageEffectif)
                                                                    const pointageEffectifBuffer = await workbookPointageEffectif.xlsx.writeBuffer()
                                                                    const headerPointageEffectif = `Anomalie pointage-effectif ${moment(dateString).format("DD MMM YYYY")} ${moment(dateString).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR"}`
                                                                    arrayFile.push({
                                                                        filename: `${headerPointageEffectif}.xlsx`,
                                                                        content: pointageEffectifBuffer
                                                                    })

                                                                    const workbookAnomalie = new Excel.Workbook()
                                                                    generateAnomalieExport(dataReformat, workbookAnomalie)
                                                                    const header = "Anomalie planning-pointage " + moment(dateString).format("DD MMM YYYY") + " " +( moment(dateString).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR");
                                                                    const anomalieBuffer = await workbookAnomalie.xlsx.writeBuffer()
                                                                    arrayFile.push({
                                                                        filename: `${header}.xlsx`,
                                                                        content: anomalieBuffer
                                                                    })
                                                                    
                                                                    const workbookPlanningEffectif = new Excel.Workbook()
                                                                    generateAnomaliePointagePlanningEffectif(effectifPlanning, workbookPlanningEffectif, true)
                                                                    const planningEffectifBuffer = await workbookPlanningEffectif.xlsx.writeBuffer()
                                                                    const headerPlanningEffectif = `Anomalie planning-effectif ${moment(dateAddOneDay).format("DD MMM YYYY")} ${moment(dateString).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR"}`
                                                                    const date = `${moment(dateString).format("DD MMM YYYY")} ${moment(dateString).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR"}`
                                                                    arrayFile.push({
                                                                        filename: `${headerPlanningEffectif}.xlsx`,
                                                                        content: planningEffectifBuffer
                                                                    })

                                                                    sendMail(
                                                                        pool,
                                                                        process.argv[2] == 'task' ? destination_vg(emails) : destination_test,
                                                                        `Anomalie planning ${moment(dateString).format("DD MMM YYYY")} ${( moment(dateString).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR")}`,
                                                                        `Veuillez trouver ci-joint le rapport des anomalies : <br/>
                                                                        <ul style="list-style-type:square">
                                                                            <li> Réclamation du ${date}: </li>
                                                                                <ul style="padding-left: 20px">
                                                                                    ${Object.entries(reformatReclamations).map(([type, items]) => (
                                                                                            `<li>${type == "sm" ? "Agent non enregistré" 
                                                                                            : (type == "service24" ? "Manque de demande service24" 
                                                                                            : (type == "mis_a_pied" ? "Agent mis à pied" 
                                                                                            : (type == "archive" ? "Agent archivé" : "")))} : ${items.length}</li>`
                                                                                        )).join('') 
                                                                                    }
                                                                                </ul><br/>
                                                                            <li>Anomalie Control Room (Pointage/Effectif) du ${date}</li>
                                                                                <ul style="padding-left: 20px">
                                                                                    <li>Manque: ${effectifPointage.reduce((sum, item) => {
                                                                                            if (item.nb_ptg < item.current_effectif) {
                                                                                                return sum + (item.current_effectif - item.nb_ptg);
                                                                                            }
                                                                                            return sum;
                                                                                        }, 0)}
                                                                                    </li>
                                                                                    <li>Surplus: ${effectifPointage.reduce((sum, item) => {
                                                                                        if (item.nb_ptg > item.current_effectif) {
                                                                                            return sum + (item.nb_ptg - item.current_effectif);
                                                                                            }
                                                                                            return sum;
                                                                                        }, 0)}
                                                                                    </li>
                                                                                </ul><br/>
                                                                            <li>Anomalie Planning/Pointage du ${date}</li>
                                                                                <ul style = "padding-left: 20px">
                                                                                    <li> Manque : ${anomalieManque.reduce((acc, item) => acc + item.manque, 0)} </li>
                                                                                    <li> Surplus : ${anomalieSurplus.reduce((acc, item) => acc + item.surplus, 0)} </li>
                                                                                    <li> Incoherences : ${anomalieIncoherence.reduce((acc, item) => acc + item.planningPtgAgents.length ,0)} </li>
                                                                                </ul><br/>
                                                                            <li>Anomalie à venir (Planning/Effectif) le ${moment(dateAddOneDay).format('DD MMM YYYY')} ${moment(dateAddOneDay).format("HH") == "18" ? "NUIT" : "JOUR"}</li>
                                                                                <ul style="padding-left: 20px">
                                                                                    <li> Manque: ${effectifPlanning.reduce((sum, item) => {
                                                                                        if (item.nb_ptg < item.current_effectif) {
                                                                                                return sum + (item.current_effectif - item.nb_ptg);
                                                                                            }
                                                                                            return sum;
                                                                                        }, 0)} 
                                                                                    </li>
                                                                                    <li> Surplus : ${effectifPlanning.reduce((sum, item) => {
                                                                                                if (item.nb_ptg > item.current_effectif) {
                                                                                                    return sum + (item.nb_ptg - item.current_effectif);
                                                                                                }
                                                                                                return sum;
                                                                                            }, 0)}
                                                                                    </li>
                                                                                </ul><br/>
                                                                        </ul>`
                                                                        ,
                                                                        arrayFile,
                                                                        (response) => {
                                                                            if(response && process.argv[2] == 'task'){
                                                                                pool.query(sqlUpdateLastAnomalieExport(dateString), [], (e, r) =>{
                                                                                    if(e)
                                                                                        console.error(e)
                                                                                    else
                                                                                        console.log("update last diag export: " + r)
                                                                                    process.exit(1)
                                                                                })
                                                                            }
                                                                            else
                                                                                process.exit(1)
                                                                        }
                                                                        , process.argv[2] == 'task'
                                                                    )
                                                                }
                                                            })
                                                        }
                                                    })
                                                }
                                            })
                                        }
                                    })
                                }
                            })
                        }
                    })
                }
            }
        )}
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && ["06:00:00", "18:00:00"].includes(process.argv[3])){
    console.log("send test..." + process.argv[2] + ' ' + process.argv[3])
    doAnomalieExport(process.argv[2] + ' ' + process.argv[3])
}
else if(process.argv[2] == 'task'){
    let date_anomalie = getDayOrNightExport()
    console.log('date_anomalie',date_anomalie)
    pool.query(sqlSelectAnomalieExport, [], (err, result) => {
        if(err)
            console.error(err)
        else if(result && result[0].value == date_anomalie) {
            console.log("export anomalies already done!")
            process.exit(1)
        }
        else {
            console.log("exporting ...")
            doAnomalieExport(date_anomalie)
        }
    })
}
else
    console.log("please specify command!")
