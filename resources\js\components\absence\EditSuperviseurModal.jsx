import React, { useState } from 'react'
import InputUser from '../input/InputUser'
import useToken from '../util/useToken'

export default function EditSuperviseurModal({ auth, absence, updateData, setCurrentId, closeModal }) {
    const [superviseur, setSuperviseur] = useState()
    const [error, setError] = useState('');
    const handleSUbmit = () => {
        const data = absence
        data.superviseur_id = superviseur.id
        axios.post('/api/absence/edit/' + absence.id, data, useToken())
        .then((res) => {
            if (res.data.success) {
                updateData(true)
                closeModal()
                setCurrentId(null)
            }
            else if (res.data.error) {
                setError(res.data.error)
            }
        })
    }
    return (
        <div className='modal'>
            <div>
                <InputUser role="superviseur" value={superviseur} onChange={setSuperviseur} required />
                {
                    error &&
                    <div className='container-error'>
                        {error}
                    </div>
                }
                <div className='form-button-container'>
                    <button disabled={!superviseur} className='btn-primary' onClick={handleSUbmit}>Envoyé</button>
                    <button onClick={() => closeModal()}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
