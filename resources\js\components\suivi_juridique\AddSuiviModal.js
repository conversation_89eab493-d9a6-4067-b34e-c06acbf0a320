import React, { useState } from 'react';

import axios from 'axios';
import Textarea from '../input/Textarea';
import useToken from '../util/useToken';
import removeDuplicateBreak from '../util/stringUtil';

export default function AddSuiviModal({name, id, closeModal, updateData}) {
    const [note, setNote] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleOk = () => {
        disableSubmit(true)
        setError("")
        const noteStr = removeDuplicateBreak(note)
        let formData = new FormData()
        formData.append(name, id)
        if(noteStr)
            formData.append("commentaire", noteStr)
        axios.post("/api/suivi_juridique/add", formData, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success){
                closeModal()
                if(updateData) updateData()
            }
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    return <div className='modal'>
        <div>
            <h3>
                Nouvel élément
            </h3>
            <div className='input-container'>
                <Textarea
                    value={note} 
                    label="Commentaire" 
                    onChange={setNote} 
                    required/>
            </div>
            {
                error &&
                <div className='container-error'>
                    {error}
                </div>
            }
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Envoyer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
}