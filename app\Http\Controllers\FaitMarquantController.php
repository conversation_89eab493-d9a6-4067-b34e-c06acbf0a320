<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Site;
use App\Models\FaitMarquant;
use App\Models\PieceJointe;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class FaitMarquantController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function detail(Request $request, $id){
        $fait = DB::select("SELECT f.id, f.objet, f.commentaire, f.created_at,f.date_visite, f.end, f.start, st.nom as 'site',
            st.pointage, st.pointeuse, f.user_id, us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', y.id as 'seen', y.send_email
            FROM fait_marquants f
            LEFT JOIN sites st on st.idsite = f.site_id
            LEFT JOIN users us on us.id = f.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN (SELECT s.id, s.fait_marquant_id, s.send_email FROM seen s WHERE s.user_id = ?) y ON y.fait_marquant_id = f.id
            where f.id = ?", [$request->user()->id, $id])[0];       
        $pieces = DB::select("SELECT pj.id, pj.nature, pj.path, pj.user_id, pj.created_at, pj.updated_at
            FROM piece_jointes pj
            WHERE pj.fait_marquant_id = ?
            order by pj.created_at desc", [$id]);
        $fait->nb_pj = count($pieces);
        $historiques = DB::select("SELECT h.id, h.objet, h.note, h.detail, h.created_at, h.user
            FROM historiques h
            where h.fait_marquant_id = ?
            order by h.created_at desc
            limit 10", [$id]);
        return response()->json(compact('fait', 'pieces', 'historiques'));
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = " f.id = " . $request->id . " ";
        else {
            if($request->objet)
                $searchArray[] = " f.objet like '%" . $request->objet . "%' ";
            if ($request->message_content)
                $searchArray[] = " REGEXP_REPLACE(REGEXP_REPLACE(commentaire, '<[^>]+>', ' ', 1, 0, 'n'), '[\r\n]+', ' ') LIKE \"%".$request->message_content."%\"";
            if($request->unread)
                $searchArray[] = " y.id is null ";
            if($request->created_at)
                $searchArray[] = " f.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "f.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " f.user_id = " . $request->user_id . " ";
            if($request->site_id)
                $searchArray[] = " f.site_id = " . $request->site_id . " ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by f.id desc limit ". $request->offset . ", 10";
        $query_and = $query_and . " order by f.id desc limit ". $request->offset . ", 10";
        return compact('query_where', 'query_and');
    }

    function getDateLimit() {
        $current_date = new \DateTime();
        if(new \DateTime >= (new \DateTime)->setTime(6, 0, 0) &&
                new \DateTime < (new \DateTime)->setTime(18, 00, 0)){
            $limit_date = (new \DateTime)->setTime(6, 00, 0)->format('Y-m-d H:i:s');
        }
        else {
            if(new \DateTime < (new \DateTime)->setTime(06, 0,0)){
                $limit_date = (new \DateTime)->sub(new \DateInterval('P1D'))->setTime(18, 0, 0)->format('Y-m-d H:i:s');
            }
            else
                $limit_date = (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
        }
        return $limit_date;
    }

    public function show(Request $request, $id){
        $fait = DB::select("SELECT f.id, f.objet, f.commentaire, f.created_at,f.date_visite, f.end, f.start, st.nom as 'site', st.pointage, st.pointeuse, f.site_id,
            f.user_id, us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', us.role as 'user_role', y.id as 'seen', y.send_email,
            st.superviseur_id, st.resp_sup_id, st.group_pointage_id
            FROM fait_marquants f
            LEFT JOIN sites st on st.idsite = f.site_id
            LEFT JOIN users us on us.id = f.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN (SELECT s.id, s.fait_marquant_id, s.send_email FROM seen s WHERE s.user_id = ?) y ON y.fait_marquant_id = f.id
            where f.id = ?", [$request->user()->id, $id])[0];
        if($fait->superviseur_id)
            $fait->superviseur = DB::select("SELECT u.id, u.name, u.email FROM users u WHERE u.id = ?", [$fait->superviseur_id])[0];
        if($fait->resp_sup_id)
            $fait->manager = DB::select("SELECT u.id, u.name, u.email FROM users u WHERE u.id = ?", [$fait->resp_sup_id])[0];
        if($fait->group_pointage_id){
            $regionId = 0;
            if($fait->group_pointage_id == 3) 
                $regionId = 1;
            else if($fait->group_pointage_id == 1) 
                $regionId = 2;
            else 
                $regionId = 3;
            $fait->resps = DB::select("SELECT u.id, u.name, u.email FROM region_users ru
                LEFT JOIN users u ON u.id = ru.user_id
                WHERE u.role = 'resp_op' and ru.user_id not in (1, 221, 220, 252) and ru.region_id = ?", [$regionId]);
        }
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj
            WHERE pj.fait_marquant_id = ?
            order by pj.created_at desc", [$id]);
        $fait->nb_pj = count($pieces);
        return response()->json($fait);
    }

    public function index(Request $request){
        $search = $this->search($request);
        $faits = [];
        if(in_array($request->user()->role, ['superviseur'])){
            $faits = DB::select("SELECT f.id, f.objet, f.commentaire, f.created_at, f.date_visite, f.end, f.start, st.nom as 'site',
                st.pointage, st.pointeuse, f.user_id, us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', us.role as 'user_role', y.id as 'seen', y.send_email
                FROM fait_marquants f
                LEFT JOIN sites st on st.idsite = f.site_id
                LEFT JOIN users us on us.id = f.user_id
                LEFT JOIN users ur on ur.id = us.real_email_id
                LEFT JOIN (SELECT s.id, s.fait_marquant_id, s.send_email FROM seen s WHERE s.user_id = ?) y ON y.fait_marquant_id = f.id
                where f.user_id = ? " . $search['query_and']
                , [$request->user()->id, $request->user()->id]);
        }
        else if(in_array($request->user()->role, ['room'])){
            $faits = DB::select("SELECT f.id, f.objet, f.commentaire, f.created_at, f.end, f.start, st.nom as 'site',
                st.pointage, st.pointeuse, f.user_id, us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', us.role as 'user_role', seen.id as 'seen_id', f.user_id
                FROM fait_marquants f
                LEFT JOIN sites st on st.idsite = f.site_id
                LEFT JOIN users us on us.id = f.user_id
                LEFT JOIN users ur on ur.id = us.real_email_id
                LEFT JOIN seen on seen.fait_marquant_id = f.id
                where f.created_at >= ? ". $search['query_and']
                , [$this->getDateLimit()]);
        }
        else if (in_array($request->user()->role, ["resp_op"])) {
            $regions = RegionUsersController::getRegions($request);
            $faits = DB::select("SELECT f.id, f.objet, f.commentaire, f.created_at,f.date_visite, f.end, f.start, st.nom as 'site',
            st.pointage, st.pointeuse, f.user_id, us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', us.role as 'user_role', y.id as 'seen', y.send_email
            FROM fait_marquants f
            LEFT JOIN sites st on st.idsite = f.site_id
            LEFT JOIN users us on us.id = f.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN (SELECT s.id, s.fait_marquant_id, s.send_email FROM seen s WHERE s.user_id = ?) y ON y.fait_marquant_id = f.id
            where (us.created_at is null or f.created_at > us.created_at)
            and group_pointage_id IN ($regions)" . $search['query_and']
            , [$request->user()->id]);
        }
        else{
            $faits = DB::select("SELECT f.id, f.objet, f.commentaire, f.created_at,f.date_visite, f.end, f.start, st.nom as 'site',
                st.pointage, st.pointeuse, f.user_id, us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', us.role as 'user_role', y.id as 'seen', y.send_email
                FROM fait_marquants f
                LEFT JOIN sites st on st.idsite = f.site_id
                LEFT JOIN users us on us.id = f.user_id
                LEFT JOIN users ur on ur.id = us.real_email_id
                LEFT JOIN (SELECT s.id, s.fait_marquant_id, s.send_email FROM seen s WHERE s.user_id = ?) y ON y.fait_marquant_id = f.id
                where (us.created_at is null or f.created_at > us.created_at) " . $search['query_and']
                , [$request->user()->id]);
        }
        $ids = array_column($faits, 'id');
        if(count($ids) > 0){
            $pieces = DB::select("SELECT id, fait_marquant_id FROM piece_jointes WHERE fait_marquant_id in (" . implode(",", array_column($faits, "id")) . ")");
            foreach ($faits as $item) {
                $nb_pj = 0;
                foreach ($pieces as $pj) {
                    if($item->id == $pj->fait_marquant_id)
                        $nb_pj += 1;
                }
                $item->nb_pj = $nb_pj;
            }

            $seens = DB::select("SELECT s.id, s.fait_marquant_id, s.send_email FROM seen s WHERE s.user_id = ? and s.fait_marquant_id in (" . implode(', ', $ids) . ")", [$request->user()->id]);
            foreach ($faits as $f) {
                foreach ($seens as $s) {
                    if($f->id == $s->fait_marquant_id){
                        $f->seen = $s->id;
                        $f->send_email = $s->send_email;
                    }
                }
            }
        }
        return response(compact('faits'));
    }

    public function store(Request $request){
        if(in_array($request->user()->role, ["superviseur", "resp_sup", "resp_op"])){
    
            $rules = [
                'site_id' => 'required',
                'objet' => 'required',
                'commentaire' => 'required',
                'date_visite' => 'nullable',
                'start' => 'nullable',
                'end' => 'nullable',
            ];
    
            $validator = Validator::make($request->all(), $rules);
    
            if ($validator->fails()) {
                return response(['error' => $validator->errors()->first()]);
            }
    
            $site = Site::find($request->site_id);
    
            if (!$site) {
                return response(['error' => 'Le site spécifié est introuvable.']);
            }
    
            if ($site->pointeuse == 1) {
                $validator = Validator::make($request->all(), [
                    'date_visite' => 'required',
                ]);
    
                if ($validator->fails()) {
                    return response(['error' => $validator->errors()->first()]);
                }
            }
    
            $user = $request->user();
            $fait = new FaitMarquant();
            $fait->site_id = $request->site_id;
            $fait->objet = $request->objet;
            $fait->date_visite = $request->date_visite;
            $fait->start = $request->start;
            $fait->end = $request->end;
            $fait->commentaire = $request->commentaire;
            $fait->user_id = $user->id;
            $fait->created_at = new \DateTime();
            $fait->updated_at = new \DateTime();
    
            if($fait->save()) {
                if ($request->file('files')) {
                    foreach ($request->file('files') as $key => $file) {
                        $pj_date = date("Y-m-d_His", time());
                        $file_ext = $file->extension();
                        $original_name = $file->getClientOriginalName();
                        $name_len = strlen($original_name);
                        if ($name_len > 15) {
                            if (substr($original_name, -strlen('.' . $file_ext)) === ('.' . $file_ext)) {
                                $new_length = $name_len - strlen('.' . $file_ext);
                                $original_name = substr_replace($original_name, "", $new_length);
                            }
                            $original_name = substr($original_name, 0, 15) . '.' . $file_ext;
                        }
                        $file_name = 'fait-marquant_' . $fait->id . "_" . $pj_date . "_" . $key . "." . $file_ext;
                        $file->storeAs('uploads', $file_name, 'public');
                        $piece_jointe = new PieceJointe();
                        $piece_jointe->path = $file_name;
                        $piece_jointe->nature = $original_name;
                        $piece_jointe->user_id = $request->user()->id;
                        $piece_jointe->created_at = new \DateTime();
                        $piece_jointe->fait_marquant_id = $fait->id;
                        $piece_jointe->save();
                    }
                }
                HistoriqueController::new_fait_marquant($request, $fait->id);
                return response(["success" => "Fait marquant bien enregistré", "id" => $fait->id]);
            }
            return response("Erreur d'envoi, réessayez");
    
        } else if (in_array($request->user()->role, ["room"])) {
            $validator = Validator::make($request->all(), [
                'site_id' => 'required',
                'objet' => 'required',
                'commentaire' => 'required',
                'date_visite' => 'nullable',
                'start' => 'nullable',
                'end' => 'nullable',
            ]);
    
            if ($validator->fails()) {
                return response(['error' => $validator->errors()->first()]);
            }
    
            $user = $request->user();
            $fait = new FaitMarquant();
            $fait->site_id = $request->site_id;
            $fait->objet = $request->objet;
            $fait->commentaire = $request->commentaire;
            $fait->user_id = $user->id;
            $fait->created_at = new \DateTime();
            $fait->updated_at = new \DateTime();
    
            if($fait->save()) {
                if ($request->file('files')) {
                    foreach ($request->file('files') as $key => $file) {
                        $pj_date = date("Y-m-d_His", time());
                        $file_ext = $file->extension();
                        $original_name = $file->getClientOriginalName();
                        $name_len = strlen($original_name);
                        if ($name_len > 15) {
                            if (substr($original_name, -strlen('.' . $file_ext)) === ('.' . $file_ext)) {
                                $new_length = $name_len - strlen('.' . $file_ext);
                                $original_name = substr_replace($original_name, "", $new_length);
                            }
                            $original_name = substr($original_name, 0, 15) . '.' . $file_ext;
                        }
                        $file_name = 'fait-marquant_' . $fait->id . "_" . $pj_date . "_" . $key . "." . $file_ext;
                        $file->storeAs('uploads', $file_name, 'public');
                        $piece_jointe = new PieceJointe();
                        $piece_jointe->path = $file_name;
                        $piece_jointe->nature = $original_name;
                        $piece_jointe->user_id = $request->user()->id;
                        $piece_jointe->created_at = new \DateTime();
                        $piece_jointe->fait_marquant_id = $fait->id;
                        $piece_jointe->save();
                    }
                }
                HistoriqueController::new_fait_marquant($request, $fait->id);
                return response(["success" => "Fait marquant bien enregistré", "id" => $fait->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }    
}