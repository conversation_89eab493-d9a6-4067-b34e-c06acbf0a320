import React, { useState } from 'react';

import { Link } from 'react-router-dom';
import NoteModal from '../input/NoteModal';

export default function ActionPlainte({auth, juridique, updateData}) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [currentAction, setAction] = useState(null)
    
    const handleDone = (id) => {
        setAction({
            header: "Terminer le dossier juridique",
            request: "/api/juridique/save_done/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    const handleCancel = (id) => {
        setAction({
            header: "Annuler le dossier juridique",
            request: "/api/juridique/cancel/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction} 
                updateData={() => updateData()} 
                closeModal={() => toggleNoteModal(false)}/> 
        }
        <div className='action-container'>
            {
                (["demande", "traite"].includes(juridique.status) && auth.role == "juridique") &&
                <span>
                    <Link to={"/plainte/edit/" + juridique.id}>Modifier</Link>
                </span>
            }
            {
                (["traite"].includes(juridique.status) && auth.role == "juridique") && 
                <span onClick={() => handleDone(juridique.id)}>Terminer</span>
            }
            {
                (["traite"].includes(juridique.status) && auth.role == "juridique") && 
                <span onClick={() => handleCancel(juridique.id)}>Annuler</span>
            }
        </div>
    </div>
}