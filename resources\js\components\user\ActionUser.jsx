import React, { useState } from 'react';

import { Link } from 'react-router-dom';
import ResetPasswordModal from './ResetPasswordModal';
import SpecifierRegionModal from './SpecifierRegionModal';

export default function ActionUser({auth, user, updateData}) {
    const [showConfirmationModal, toggleConfirmationModal] = useState(false)
    const [showSpecRegionModal, toggleSpecRegionModal] = useState(false)
    
    return <div>
        {
            showConfirmationModal && 
            <ResetPasswordModal 
                id={user.id}
                user={user}
                updateData={() => updateData()} 
                closeModal={() => toggleConfirmationModal(false)}/> 
        }
        {
            showSpecRegionModal &&
            <SpecifierRegionModal
                id={user.id}
                updateData={() => updateData()}
                closeModal={() => toggleSpecRegionModal(false)}
            />
        }
        <div className='action-container'>
            {
                (["admin"].includes(auth.role)) && 
                <span>
                    <Link to={"/user/edit/" + user.id}>Editer</Link>
                </span>
            }
            {
                (["admin"].includes(auth.role)) &&
                <span onClick={() => toggleConfirmationModal(true)}>Réinitialiser</span>
            }
            {
                user.role == "resp_op" &&
                <span onClick={() => toggleSpecRegionModal(true)}>Spécifier région</span>
            }
        </div>
    </div>
}