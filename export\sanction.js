const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const matricule = require("../resources/js/components/util/matricule");
const { sendMail } = require('../auth');

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = process.argv[2] == 'task'

const destination_vg = ["ogros<PERSON><EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectLastSanctionExport = "SELECT value FROM params p WHERE p.key = 'last_sanction_export'"

const sqlSelectStatus = "SELECT name, description FROM status order by ordre"

function sqlSelectSanction(dateString){
    const begin = moment(dateString).subtract(7, "day").format("YYYY-MM-DD") + " 06:00:00"
    const end = dateString + " 06:00:00"
    console.log(begin)
    console.log(end)
	return "SELECT sanc.id, sanc.employe_id, sanc.site_id, sanc.created_at, sanc.date_pointage, " +
        "sanc.motif, sanc.objet, sanc.montant, sanc.status, u.name as 'user_nom', u.email as 'user_email', " +
        "s.nom as 'site', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.nom as 'employe' " +
        "from sanctions sanc " +
        "left join employes a on a.id = sanc.employe_id " +
        "left join sites s on s.idsite = sanc.site_id " +
        "left join users u on u.id = sanc.user_id " +
        "where (sanc.status not in ('done', 'draft') " + 
        "or (sanc.status in ('done', 'draft') and sanc.updated_at > '" + begin +"' and sanc.updated_at <= '" + end +"')) " +
        "order by sanc.created_at"
}

function sqlUpdateLastSanctionExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_sanction_export'"
}
function numberWithSpaces(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
}

function generateSanctionExcelFile(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    data.forEach(stat => {
        const worksheet = workbook.addWorksheet(stat.description)
        worksheet.getColumn('A').width = 50
        worksheet.getColumn('B').width = 50
        worksheet.getColumn('C').width = 20
        worksheet.getColumn('D').width = 40
        worksheet.getColumn('E').width = 50
        worksheet.getColumn('F').width = 30
        worksheet.getColumn('G').width = 10
        worksheet.getCell('A1').value = stat.description + " (" + stat.sanctions.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:G1')

        let line = 3
        
        worksheet.getCell('A' + line).value = "Employe"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontBold
        worksheet.getCell('B' + line).value = "Site"
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).font = fontBold
        worksheet.getCell('C' + line).value = "Date du service"
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).alignment = alignmentStyle
        worksheet.getCell('C' + line).font = fontBold
        worksheet.getCell('D' + line).value = "Motif"
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).font = fontBold
        worksheet.getCell('E' + line).value = "Demandeur"
        worksheet.getCell('E' + line).border = borderStyle
        worksheet.getCell('E' + line).font = fontBold
        worksheet.getCell('F' + line).value = "Objet"
        worksheet.getCell('F' + line).border = borderStyle
        worksheet.getCell('F' + line).font = fontBold
        worksheet.getCell('G' + line).value = "Montant"
        worksheet.getCell('G' + line).border = borderStyle
        worksheet.getCell('G' + line).font = fontBold
        line++

        stat.sanctions.forEach(sanc => {
            worksheet.getCell('A' + line).value = (
                matricule(sanc)
            ) + " " + sanc.employe
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = capitalizeFirstLetter(sanc.site)
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = sanc.date_pointage ? moment(sanc.date_pointage).format("DD-MM-YY") : ""
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('C' + line).alignment = alignmentStyle
            worksheet.getCell('D' + line).value = sanc.motif
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('E' + line).value = sanc.user_nom + " <" + sanc.user_email + ">"
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('F' + line).value = sanc.objet
            worksheet.getCell('F' + line).border = borderStyle
            worksheet.getCell('G' + line).value = sanc.montant ? numberWithSpaces(sanc.montant) + " Ar" : ""
            worksheet.getCell('G' + line).border = borderStyle
            line++
        })
    })
}

function doSanctionExport(dateString){
	console.log("doSanctionExport")
    pool.query(sqlSelectStatus, [], async (err, status) => {
        if(err)
            console.error(err)
        else {
            pool.query(sqlSelectSanction(dateString), [], async (err, sanctions) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb sanction: " + sanctions.length)
                    const sanctionByStatus = []
                    status.map(stat => {
                        stat.sanctions = []
                        sanctions.map(sanc => {
                            if(stat.name == sanc.status)
                                stat.sanctions.push(sanc)
                        })
                        if(stat.sanctions.length > 0){
                            sanctionByStatus.push(stat)
                        }
                    })
                    const workbookSanction = new Excel.Workbook()
                    const header = "Sanction " + moment(dateString).format("DD MMMM YYYY")
                    generateSanctionExcelFile(workbookSanction, header, sanctionByStatus)
                    const sanctionSiteBuffer = await workbookSanction.xlsx.writeBuffer()
                    sendMail(
                        pool,
                        isTask ? destination_vg : destination_test,
                        header, 
                        "Veuillez trouver ci-joint le rapport des demandes de sanctions du " + moment(dateString).subtract(7, "days").format("DD MMMM YYYY") + " au " + moment(dateString).format("DD MMMM YYYY") + "<br/>"
                        + "<ul>"
                        + (sanctionByStatus.map(stat => "<li>" + stat.description + ": " + stat.sanctions.length + "</li>").join(""))
                        + "</ul>"
                        ,
                        [
                            {
                                filename: header + ".xlsx",
                                content: sanctionSiteBuffer
                            },
                        ],
                        (response) => {
                            if(response && isTask){
                                pool.query(sqlUpdateLastSanctionExport(dateString), [], (e, r) =>{
                                    if(e)
                                        console.error(e)
                                    else
                                        console.log("update last diag export: " + r)
                                    process.exit(1)
                                })
                            }
                            else
                                process.exit(1)
                        },
                        isTask
                    )
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2])){
    console.log("send test...")
    doSanctionExport(process.argv[2])
}
else if(isTask){
    if(moment().day() == 3 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
        let date_vigilance = moment().format("YYYY-MM-DD")
        pool.query(sqlSelectLastSanctionExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && result[0].value == date_vigilance) {
                console.log("export list sanction already done!")
                process.exit(1)
            }
            else {
                console.log("exporting ...")
                doSanctionExport(date_vigilance)
            }
        })
    }
    else {
        console.log("Not wednesday, skip Sanction")
    }
}
else
    console.log("please specify command!")