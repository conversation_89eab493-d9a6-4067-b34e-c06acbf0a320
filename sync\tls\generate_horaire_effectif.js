const moment = require('moment')
const mysql = require('mysql')
moment.locale('fr')
// const {db_config_ovh, db_config_admin} = require("../../auth")
const db_config_ovh = {
	host: "127.0.0.1",
	user: "admin",
	port: "3306",
	database: "last_tls",
	password: ""
}

const db_config_admin = {
	host: "127.0.0.1",
	user: "admin",
	port: "3306",
	database: "admin",
	password: ""
}

const db_config_tls = db_config_ovh

const pool_tls = mysql.createPool(db_config_tls);
const pool_admin = mysql.createPool(db_config_admin);

const getSite = "SELECT s.idsite, s.nom, s.horaire_pointage_id, s.nb_agent_day_planning, s.nb_agent_night_planning FROM sites s where s.pointage = 1 "
const getHoraire = "SELECT * from horaires "
const insertHoraireEffectif = "INSERT INTO horaire_effectifs (site_id, day_1, night_1, day_2, night_2, day_3, night_3, day_4, night_4, day_5, night_5, day_6, night_6, day_0, night_0, day_ferie, night_ferie) "
    + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) "
    + " ON DUPLICATE KEY UPDATE day_1 = VALUES(day_1), night_1 = VALUES(night_1), day_2 = VALUES(day_2), night_2 = VALUES(night_2), day_3 = VALUES(day_3), night_3 = VALUES(night_3), day_4 = VALUES(day_4), night_4 = VALUES(night_4), day_5 = VALUES(day_5), night_5 = VALUES(night_5), day_6 = VALUES(day_6), night_6 = VALUES(night_6), day_0 = VALUES(day_0), night_0 = VALUES(night_0), day_ferie = VALUES(day_ferie), night_ferie = VALUES(night_ferie)"
function insertData(sites, horaires, index){
    const remainingSites = sites.length - index - 1;
    const estimatedTimeLeftMs = remainingSites * 200;
    const minutes = Math.floor(estimatedTimeLeftMs / 60000);
    const seconds = Math.ceil((estimatedTimeLeftMs % 60000) / 1000);
    console.log(`⏳ Temps restant estimé: ${minutes} min ${seconds} sec`);
    if(index < sites.length){
        const currentSite = sites[index]
        const nb_day = currentSite.nb_agent_day_planning ?? 0
        const nb_night = currentSite.nb_agent_night_planning ?? 0
        const currentHoraire = horaires.find(h => h.id == currentSite.horaire_pointage_id)
        if(currentHoraire){
            const jours = ["1", "2", "3", "4", "5", "6", "0", "ferie"];
            const params = [currentSite.idsite, 
                ...jours.flatMap(j => [
                    currentHoraire[`day_${j}`] == 1 ? nb_day : 0, 
                    currentHoraire[`night_${j}`] == 1 ? nb_night : 0
                ])
            ];  
            pool_admin.query(insertHoraireEffectif, params, async (err, result) => {
                if(err){
                    console.error(err)
                    console.log(params.length)
                }
                else{
                    console.log(index + " done | ", (((index +1) / sites.length) * 100).toFixed(2), "% done")
                    setTimeout(() => {
                        insertData(sites, horaires, index + 1)
                    }, 200)
                    
                }
            })
        }
        else{
            setTimeout(() => {
                console.log(index + " done | ", "--- ",(((index +1) / sites.length) * 100).toFixed(2), "% done")
                insertData(sites, horaires, index + 1)
            }, 200)
        }
    }
    else{
        console.log('Insert data done')
        process.exit(1)
    }
}

function main (){
    pool_admin.query(getSite, [], async (err, sites) => {
        if(err){
            console.error(err)
        }
        else{
            console.log('nb_sites', sites.length)
            pool_tls.query(getHoraire, [], async(err, horaires) =>{
                if(err){
                    console.error(err)
                    process.exit(1)
                }
                else{
                    console.log('nb_horaires', horaires.length)
                    insertData(sites, horaires, 0)
                }
            })
        }
    })
}

main()
