const moment = require('moment')
const mysql = require('mysql2')

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const sqlSelectDoubonPointage = "SELECT min(id) as id ,date_pointage, employe_id " +
    "FROM pointages " +
    "GROUP BY date_pointage, employe_id " +
    "HAVING COUNT(*) > 1 order by id"
function sqlDeleteDoublon() {
	return "DELETE FROM pointages WHERE id != ? and employe_id = ? and date_pointage = ?"
}

const sqlDeleteAdemcoBeforeTwoMonth = "DELETE FROM alarms WHERE dtarrived < '"
    + moment().subtract(60, "day").format("YYYY-MM-DD HH:mm:ss") + "' "
    + "LIMIT 5000"
    
function clearPointageById(pointages, index){
    if(index < pointages.length){
        setTimeout(() => {
            const currentP = pointages[index]
            pool.query(sqlDeleteDoublon(), [currentP.id, currentP.employe_id, currentP.date_pointage], async (err, r) => {
                if(err)
                    console.log(err)
                else
                    console.log("clear doublon pointage success, id: " + currentP.id)
                clearPointageById(pointages, index+1)
            })
        }, 100)
    }
    else {
        console.log("clear doublon pointage done!")
        process.exit(1)
    }
}

pool.query(sqlSelectDoubonPointage, [], async (err, pointages) => {
	if(err)
		console.error(err)
	else {
        if(pointages.length > 0)
            clearPointageById(pointages, 0)
        else
            pool.query(sqlDeleteAdemcoBeforeTwoMonth, [], async (err) => {
                if(err)
                    console.error(err)
                else {
                    console.log("delete vigilance before 2 month done!")
                    process.exit()
                }
            })
    }
})