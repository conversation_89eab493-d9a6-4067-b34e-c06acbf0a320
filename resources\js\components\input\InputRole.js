import React, { useState } from 'react';
import RoleModal from '../modal/RoleModal';

export default function InputRole({value, onChange, required}) {
    const [modalOpen, toggleModal] = useState(false)
    
    const handleSelectRole = (ro) => {
        toggleModal(false)
        onChange(ro)
    }

    const handleCloseModal = () => {
        toggleModal(false)
    }

    return <div>
        <div className='input-container'>
            <label>
                Roles
                {required && <span className='danger'>*</span>}
            </label>
            <input
                type="text"
                value={value ? value.name : ""}
                readOnly
                onClick={() => {toggleModal(true)}}
                />
        </div>
        {
            modalOpen &&
            <RoleModal closeModal={handleCloseModal} onChange={handleSelectRole}/>
        }
    </div>;
}