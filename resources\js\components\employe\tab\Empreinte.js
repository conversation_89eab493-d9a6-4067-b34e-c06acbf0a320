import React, { useEffect, useState } from 'react'
import moment from 'moment'

import './pointage.css'
import useToken from '../../util/useToken'
import LoadingPage from '../../loading/LoadingPage'
import InputSelect from '../../input/InputSelect'
import InputDate from '../../input/InputDate'
import InputDateTime from '../../input/InputDateTime'
import { FiSearch } from 'react-icons/fi'

export default function Empreinte({value}) {
    const [isLoading, toggleLoading] = useState(true)
    const [label, setLabel] = useState("")
    const [empreintes, setEmpreintes] = useState([])
    const [type, setType] = useState({label: "Date", value:"date"})
    const [date, setDate] = useState(new Date())
    
    const getData = () => {
        let isMounted = true;
        toggleLoading(true)
        axios.get('/api/alarm/employe/' + value + '?' + (type.value =='date' ? ( 'date=' + moment(date).format("YYYY-MM-DD")) : ('datetime=' + moment(date).format("YYYY-MM-DD HH:mm"))),  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else if(res.data.empreintes){
                    setLabel(
                        type.value == "date" ? "Du " + moment(res.data.begin).format("DD MMM YYYY") :
                        type.value == "datetime" ? ("Le " + moment(res.data.begin).format("DD MMM YYYY") + " de " + moment(res.data.begin).format("HH:mm") + " à " + moment(res.data.end).format("HH:mm")) : " " 
                    )
                    setEmpreintes(res.data.empreintes)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    };

    useEffect(() => {
        getData()
    }, []);

    useEffect(() => {
        if(!type.value)
            setType({label: "Date et heure", value:"datetime"})
    }, [type])

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <>
                    <div className='line-container'>
                        <div className='header-pointage'>
                            <h3>
                                {label}
                            </h3>
                            <div className='search-pointage'>
            
                                <InputSelect options={[{label: "Date", value:"date"}, {label: "Date et heure", value:"datetime"}]} selected={type} setSelected={setType}/>
                                {
                                    type.value === "date" ? 
                                    <InputDate noLabel value={date} onChange={setDate}/> 
                                    :
                                    <InputDateTime value={date} onChange={setDate}/>
                                }
                                <div className='input-container'>
                                <button onClick={() => getData()} className='btn btn-primary'><FiSearch size={20}/></button> 
                                </div>
                            </div>
                        </div>
                    </div>
                    {
                        empreintes.length == 0 ?
                        <h4 className='center secondary'>Aucun données trouvé</h4>
                        :
                        <div>
                            <div className="line-container">
                                <div className="row-employe">
                                    <b className='line-cell-sm'>Heure</b>
                                    <b className=''>Site</b>
                                </div>
                            </div>
                            {
                                empreintes.map(e => 
                                    <div key={e.idademco} className='line-container secondary'>
                                        <div className='row-employe'>
                                            <span className='line-cell-sm'>{moment(e.dtarrived).format("HH:mm:ss") }</span>
                                            <span className=''>{e.site}</span>
                                        </div>
                                    </div>
                            )}
                        </div>
                    }
                </>
        }
    </>
}