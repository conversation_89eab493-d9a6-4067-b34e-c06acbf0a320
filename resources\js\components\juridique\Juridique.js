import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import LoadingPage from '../loading/LoadingPage';
import InfiniteScroll from 'react-infinite-scroll-component';
import StatusLabel from '../input/StatusLabel';
import showAmount from '../util/numberUtil';

export default function Juridique({auth, juridiques, setJuridiques, currentId, setCurrentId}) {
    const locationSearch = useLocation().search
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'ID', name: 'id', type:'number'},
        {label: 'Status', name: 'etape_id', type:'number'},
        {label: 'En retard', name: 'late_date', },
        {label: 'Réference juridique', name: 'reference', type:'string'},
        {label: 'Agence', name: 'agence_id', type:'number'},
        {label: "Débiteur", name: "debiteur", type: "string"},
        {label: "Contrat", name: "contrat", type: "string"},
        {label: "Facture", name: "facture", type: "string"},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Utilisateur', name: 'user_id', type:'number'}
    ]
        
    const updateData = (initial) => {
        let isMounted = true;
        const urlParams = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            urlParams.set("offset", 0)
        }
        else
            urlParams.set("offset", juridiques.length)

        axios.get("/api/juridique?" + urlParams ,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setJuridiques(res.data.juridiques)
                    else {
                        const list = juridiques.slice().concat(res.data.juridiques)
                        setJuridiques(list)
                    }
                    setDataLoaded(res.data.juridiques.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }
    
    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        : 
            <div>
                <div className="padding-container space-between">
                    <h2>Recouvrement</h2>
                    {   
                        ['juridique'].includes(auth.role) && 
                        <Link className='btn btn-primary' to="/recouvrement/add">Nouveau</Link>
                    }
                </div>
                <SearchBar hasEmploye listItems={searchItems}/>
                {
                    juridiques.length == 0 ? 
                        <h3 className='center secondary'>Aucun données trouvé</h3>
                    :
                        <InfiniteScroll
                            dataLength={juridiques.length}
                            next={fetchMoreData}
                            hasMore={!allDataLoaded}
                            loader={<LoadingPage/>}
                        >
                            <div className="line-container ">
                                <div className='row-employe'>
                                    <b className='sujet-juridique'>Débiteur</b>
                                    <b className="status-line"><StatusLabel color="grey"/></b>
                                    <b  className='line-cell-sm'>Montant</b>
                                    <b>Contrat</b>
                                </div>
                            </div>
                            {
                                juridiques.map((j) => (
                                    <div className={`table line-container ${currentId && currentId == j.id ? 'selected' : ''}`} key={j.id}>
                                        <div className={"row-employe"} onClick={() => setCurrentId(j.id)}>
                                            <span className='sujet-juridique'>{j.debiteur}</span>
                                            <span className="status-line">
                                                <StatusLabel color={j.status_color}/>
                                            </span>
                                            <span className='line-cell-sm'>{showAmount(j.montant)}</span>
                                            <span>{j.contrat}</span>
                                        </div>
                                    </div>
                                ))
                            }
                        </InfiniteScroll>
                }
            </div>
    } </>
}