import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';
import numberUtil from '../util/numberUtil'

import useToken from '../util/useToken';
import ActionSav from './ActionSav';
import Tab from '../layout/Tab';
import ShowHeader from '../view/ShowHeader';
import LoadingPage from '../loading/LoadingPage';
import moment from 'moment';

export default function ShowSav({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [currentType, setCurrentType] = useState()
    const [isLoading, toggleLoading] = useState(true)
    const [sav, setSav] = useState(null)
    const [defautUsers, setDefautUsers] = useState([])

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/sav/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setSav(res.data)
                setCurrentType(res.data.type)
                const newUser = []
                if (auth.id != res.data.user_id) {
                    newUser.unshift({id:res.data.user_id, address:res.data.user_email, name:res.data.user_nom})
                }
                setDefautUsers(newUser)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(sav)
    }, [sav]);

    useEffect(updateData, [currentId])

    return (
        (isLoading || !currentType) ?
            <LoadingPage/>
        :
        <div>
            {
                sav &&
                <>
                    <ShowHeader size={size} closeDetail={() => setCurrentId()} label={"SAV" + (currentType.name != "autre" ? " - " + currentType.designation : "")} id={sav.id}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + sav.status_color}>
                                    {sav.status_description}
                                </span> {
                                    sav.nb_pj > 0 &&
                                    <span className='badge-outline'>
                                        Pièce jointe : {sav.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {sav.site} 
                        </h3>
                        {
                            sav.objet &&
                            <div>
                                Objet : <span className='text'>{sav.objet}</span>
                            </div>
                        }
                        {
                            sav.montant &&
                            <div>
                                Montant : <span className='text'>{numberUtil(sav.montant)}</span>
                            </div>
                        }
                        <p>
                            Motif : <span className='text'>{sav.motif}</span>
                        </p>
                        {
                            sav.mesure &&
                            <p>
                                Mesure prise : <span className='text'>{sav.mesure}</span>
                            </p>
                        }
                        {
                            sav.user_id != sav.superviseur_id &&
                            <div>
                                Superviseur responsable : <span className='text'> 
                                    {sav.sup_nom} {' <' + sav.sup_email + '>'}
                                </span>
                            </div>
                        }
                        <div>
                            Demandeur : <span className='text'> 
                                {sav.user_nom} {' <' + sav.user_email + '>'}
                            </span>
                        </div>
                        <div>
                            Le : <span className='text'> 
                                {moment(sav.created_at).format("DD MMMM YYYY")}
                            </span>
                        </div>
                        <div className='card-action'>
                            <ActionSav auth={auth} sav={sav} currentType={currentType.name} updateData={updateData} toggleLoading={toggleLoading}/>
                        </div>
                    </div>
                    <Tab auth={auth} name="sav_id" value={sav.id} updateData={updateData} defautUsers={defautUsers}/>
                </>
            }
        </div>
    )
}