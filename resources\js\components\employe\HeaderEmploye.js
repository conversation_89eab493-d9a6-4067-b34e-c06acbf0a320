import React from 'react';
import moment from 'moment'
import matricule from '../util/matricule';

export default function HeaderEmploye({auth, data}) {
    return (
        <div>
            <h3>
                <div> 
                    {matricule(data)} {data.nom}
                </div>
                
            </h3>
            {
                data.site &&
                <p style={{whiteSpace: "pre-line"}}>
                    Site: <span className='text'>{data.site}</span>
                </p>
            }            
            <p style={{whiteSpace: "pre-line"}}>
                Date d'embauche: <span className='text'>{moment(data.date_embauche).format("dddd DD MMM YYYY")}</span>
            </p>
        </div>
    )
}