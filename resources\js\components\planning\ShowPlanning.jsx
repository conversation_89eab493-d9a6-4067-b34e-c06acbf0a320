import React, { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import useToken from '../util/useToken'
import LoadingPage from '../loading/LoadingPage'
import ShowHeader from '../view/ShowHeader'
import moment from 'moment'
import { upperCase } from 'lodash'
import ActionPlanning from './ActionPlanning'
import Tab from '../layout/Tab'

export default function ShowPlanning({ auth, currentId, setCurrentId, setCurrentItem, size }) {
    const [isLoading, toggleLoading] = useState(false)
    const [planning, setPlanning] = useState()
    const [defautUsers, setDefautUsers] = useState();
    const params = useParams()
    const updateData = () => { 
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/planning/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if (isMounted) {
                if(!res.data)
                    setCurrentId()
                else{
                    setPlanning(res.data)
                    if(auth.id == res.data.planning.user_id){
                        if(auth.id != res.data.planning.superviseur_id && res.data.planning.superviseur_id)
                            setDefautUsers([{id: res.data.planning.superviseur_id, address:res.data.planning.superviseur_email, name:res.data.planning.superviseur}])
                    }
                    else
                        setDefautUsers([{id: res.data.planning.user_id, address:res.data.planning.user_email, name:res.data.planning.user_nom}])
                }
            }
            toggleLoading(false)
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
    }
    useEffect(() => {
        updateData()
    }, [currentId])

    return (
        <div>
            {
                isLoading ?
                    <LoadingPage />
                :
                    <>
                        {planning?.planning &&
                            <div>
                                <ShowHeader size={size} label="Planning" id={currentId} closeDetail={() => setCurrentId()} />
                                <div className="card-container">
                                    <div className="badge-container">
                                        <span>
                                            <span className={'badge-outline badge-outline-' + planning?.planning.status_color}>
                                                {planning?.planning.status_description}
                                            </span>{
                                                planning?.planning.nb_pj > 0 && 
                                                    <span className='badge-outline'>
                                                        Pièce jointe : {planning?.planning.nb_pj}
                                                    </span>
                                            }
                                        </span>
                                    </div>
                                    <h3><span>{planning.planning.site}</span></h3>
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Date: <span className='text'>{upperCase(moment(planning.planning.date_planning).format("MMM YYYY")) }</span>
                                    </p>
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Type de contrat: <span className='text'>{planning.planning.horaire ?? 'Non defini' }</span>
                                    </p>
                                    {
                                        planning.planning?.nb_agent_day > 0 &&
                                            <p style={{ whiteSpace: "pre-line" }}>
                                                Nb. Agent Jour: <span className='text'>{planning.planning.nb_agent_day ?? 'Non defini'}</span>
                                            </p>
                                    }
                                    {
                                        planning.planning?.nb_agent_night > 0 && 
                                            <p style={{ whiteSpace: "pre-line" }}>
                                                Nb. Agent Nuit: 
                                                    <span className='text'>
                                                        {planning.planning.nb_agent_night ?? 'Non defini' }
                                                    </span>
                                            </p>
                                    }
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Heure Facturés: <span className='text'>{planning.planning.total_hour ?? 'Non defini' }</span>
                                    </p>
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Heure planifiée: <span className='text'>{planning.pointages.length *12 }</span>
                                    </p>
                                    <p style={{ whiteSpace: "pre-line" }}>
                                        Utilisateur : <span className='text'>{planning.planning.user_name + ' <' + planning.planning.user_email + '>'}</span>
                                    </p>
                                    <div className="card-action">
                                        <ActionPlanning auth={auth} planning={planning} />
                                    </div>
                                </div>
                                <Tab auth={auth} name="planning_id" value={currentId} updateData={updateData} data={planning} defautUsers={defautUsers} />
                                {/* <div className="tab-container">
                                    <div className="tab-list">
                                        <div className='active'>Plan</div>
                                    </div>
                                    <div className="tab-content">
                                        <PointagePlanning auth={auth} name='planning_id' pointages={planning.pointages} planning={planning.planning} />
                                    </div>
                                </div> */}

                            </div>
                        }
                    </>
            }
        </div> 
  )
}
