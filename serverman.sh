#!/bin/sh
DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$DIR"
while true 
do 
	echo "--------------------"
	node task/clear_doublon_pointage.js
	#node task/update_real_site_agent.js
	node task/sup_sanction.js
	node export/rh.js task
	node export/visite_poste.js task
	node export/fait_marquant.js task
#	node export/controlroom.js task
	node export/absence.js task
	node export/equipement.js task
	node export/sanction.js task
	node export/sav.js task
	node export/flotte.js task
	node export/approvisionnement.js task
	node export/juridique.js task
	node export/visite_hebdomadaire.js task
	node export/visite_mensuel.js task
	node export/visite_sg_mada.js task
	node export/visite_natema.js task
	node export/mis_a_pied.js task
	node export/visite_camusat.js task
	node export/part_variable.js task
	node export/anomalie_service.js task
	node export/reminder_part.js task
	node export/duree_vp_hebdo.js task
	sleep 500
done 
