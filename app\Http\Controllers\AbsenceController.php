<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Absence;
use App\Models\Employe;
use App\Models\Notification;
use App\Http\Util\EmployeUtil;
use App\Models\Message;
use App\Models\NoteMessage;
use App\Models\Pointage;
use App\Models\PointageReclamation;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class AbsenceController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function show($id){
        $absence = DB::select("SELECT cg.id, cg.type_absence, cg.employe_id, cg.depart, cg.retour, cg.motif, cg.status,
            cg.employe_temporaire, cg.employe_remplacant_id, cg.created_at, cg.absence_sans_motif,
            stat.description as 'status_description', stat.color as 'status_color', cg.user_id, f.libelle as 'fonction',
            cg.site_id, cg.nb_jour, cg.superviseur_id, cg.date_pointage, cg.type_mis_a_pied, st.nom as 'site', us.name as 'user_nom',
            us.email as 'user_email', us.date_embauche, 
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi, a.date_embauche, a.fonction_id,
            arp.nom as 'arp_employe', arp.societe_id as 'arp_societe_id', arp.numero_stagiaire as 'arp_numero_stagiaire', 
            arp.numero_employe as 'arp_numero_employe', arp.num_emp_soit as 'arp_num_emp_soit', arp.num_emp_saoi as 'arp_num_emp_saoi',
            sup.name as 'sup_nom', coalesce(ur.email, sup.email) as 'sup_email', cg.type_mis_a_pied
            FROM absences cg
            LEFT JOIN employes a ON a.id = cg.employe_id
            LEFT JOIN employes arp ON arp.id = cg.employe_remplacant_id
            LEFT JOIN sites st ON st.idsite = cg.site_id
            LEFT JOIN fonctions f ON f.id = a.fonction_id
            LEFT JOIN users us on us.id = cg.user_id
            LEFT JOIN users sup on sup.id = cg.superviseur_id
            LEFT JOIN users ur on ur.id = sup.real_email_id
            LEFT JOIN `status` stat on stat.name = cg.status
            WHERE cg.id = ?", [$id])[0];
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj
            WHERE pj.absence_id = ?
            order by pj.created_at desc", [$id]);
        $absence->nb_pj = count($pieces);
        return response()->json($absence);
    }
    public function conge_done($id){
        $absence = DB::select("SELECT cg.id, cg.employe_id FROM absences cg WHERE cg.id = ?", [$id])[0];
        if($absence->employe_id){
            $employe = Employe::find($absence->employe_id);
            $conges = DB::select("SELECT depart, retour FROM absences ab 
                WHERE type_absence = 'conge' and ab.status = 'done' and ab.employe_id = ?", [$absence->employe_id]);
        }
        return response()->json(compact('employe', 'conges'));
    }
    
    public function detail($id){
        $absence = DB::select("SELECT cg.id, cg.type_absence, cg.employe_id, cg.depart, cg.retour, cg.motif, cg.status, cg.employe_temporaire, cg.employe_remplacant_id, cg.created_at,
            stat.description as 'status_description', stat.color as 'status_color', cg.user_id, f.libelle as 'fonction',
            cg.site_id, st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', us.date_embauche, 
            a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.date_embauche, a.fonction_id,
            arp.nom as 'arp_employe', arp.societe_id as 'arp_societe_id', arp.numero_stagiaire as 'arp_numero_stagiaire', arp.numero_employe as 'arp_numero_employe', 
            arp.num_emp_soit as 'arp_num_emp_soit'
            FROM absences cg
            LEFT JOIN employes a ON a.id = cg.employe_id
            LEFT JOIN employes arp ON arp.id = cg.employe_remplacant_id
            LEFT JOIN sites st ON st.idsite = cg.site_id
            LEFT JOIN fonctions f ON f.id = a.fonction_id
            LEFT JOIN users us on us.id = cg.user_id
            LEFT JOIN `status` stat on stat.name = cg.status
            WHERE cg.id = ?", [$id])[0];
        if($absence->employe_id)
            $conges = DB::select("SELECT depart, retour FROM absences ab 
                WHERE type_absence = 'conge' and ab.status = 'done' and ab.employe_id = ?", [$absence->employe_id]);
        else
            $conges = DB::select("SELECT depart, retour FROM absences ab 
                WHERE type_absence = 'conge' and ab.status = 'done' and ab.user_id = ?", [$absence->user_id]);

        $pieces = DB::select("SELECT pj.id, pj.nature, pj.path, pj.user_id, pj.created_at, pj.updated_at
            FROM piece_jointes pj
            WHERE pj.absence_id = ?
            order by pj.created_at desc", [$id]);
        $historiques = DB::select("SELECT h.id, h.objet, h.note, h.detail, h.created_at, h.user
            FROM historiques h
            where h.absence_id = ?
            order by h.created_at desc
            limit 10", [$id]);
        return response()->json(compact('absence', 'pieces', 'historiques', 'conges'));
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = "cg.id = '". $request->id ."'";
        else {
            if($request->status)
                $searchArray[] = "stat.name = '". $request->status ."'";
            if($request->created_at)
                $searchArray[] = " cg.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "cg.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " cg.user_id = " . $request->user_id . " ";
            if($request->employe_id)
                $searchArray[] = " cg.employe_id = " . $request->employe_id . " ";
            if($request->site_id)
                $searchArray[] = " cg.site_id = " . $request->site_id . " ";
            if($request->superviseur_id)
                $searchArray[] = " cg.superviseur_id = " . $request->superviseur_id . " ";
            if($request->last_id)
                $searchArray[] = " cg.id <= " . $request->last_id . " ";
            if($request->conge_fictif)
                $searchArray[] = " cg.fictif = " . $request->conge_fictif . " ";
            if($request->date_paie){
                list($year, $month) = explode('-', $request->date_paie);
                $dateBefore = "";
                if ($month == 1) {
                    $dateBefore = $year - 1 . "-12";
                } else {
                    $dateBefore = $year . "-" . str_pad($month - 1, 2, '0', STR_PAD_LEFT);
                }
                $searchArray[] = " cg.depart >'".$dateBefore."-20 00:00:00' 
                and cg.depart <'".$year."-".$month. "-19 23:59:00' and 
                cg.status = 'done' and cg.employe_id = '" .$request->employe_id."' ";
            }
        }

        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        if ($request->user()->role == 'resp_sup') {
            $query_where = $query_where . " order by 
                CASE 
                    WHEN cg.superviseur_id = " . $request->user()->id . " THEN 0 
                    WHEN cg.user_id = " . $request->user()->id . " THEN 1
                    ELSE 2 END
                , cg.id desc limit ". $request->offset . ", 30";
            $query_and = $query_and . " order by 
                CASE 
                    WHEN cg.superviseur_id = " . $request->user()->id . " THEN 0 
                    WHEN cg.user_id = " . $request->user()->id . " THEN 1
                    ELSE 2 END
                , cg.id desc limit ". $request->offset . ", 30";
        }
        else{
            $query_where = $query_where . " order by cg.id desc limit ". $request->offset . ", 30";
            $query_and = $query_and . " order by cg.id desc limit ". $request->offset . ", 30";
        }
        return compact('query_where', 'query_and');
    }

    public function index(Request $request, $type){
        $auth = $request->user();
        $auth_employe = Employe::find($auth->employe_id);
        $user_service_id = $auth_employe->service_id;
        $search = $this->search($request);
        if ($type=='mis_a_pied' && in_array($auth->role,['rh', 'resp_rh', 'validateur', 'resp_sup', 'resp_op'])) {
            $absences = DB::select(
                "SELECT cg.id, cg.employe_id, cg.depart, cg.retour, cg.motif, cg.status, cg.employe_temporaire, cg.employe_remplacant_id, cg.created_at,
                stat.description as 'status_description', stat.color as 'status_color', cg.user_id, f.libelle as 'fonction',
                cg.site_id, st.nom as 'site', st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', 
                a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi, a.fonction_id,
                arp.nom as 'arp_employe', arp.societe_id as 'arp_societe_id', arp.numero_stagiaire as 'arp_numero_stagiaire',
                arp.numero_employe as 'arp_numero_employe', arp.num_emp_soit as 'arp_num_emp_soit',cg.nb_jour,
                sup.name as 'sup_nom',  coalesce(ur.email, sup.email) as 'sup_email'
                FROM absences cg
                LEFT JOIN employes a ON a.id = cg.employe_id
                LEFT JOIN employes arp ON arp.id = cg.employe_remplacant_id
                LEFT JOIN sites st ON st.idsite = cg.site_id
                LEFT JOIN fonctions f ON f.id = a.fonction_id
                LEFT JOIN users us on us.id = cg.user_id
                LEFT JOIN users sup on sup.id = cg.superviseur_id
                LEFT JOIN users ur on ur.id = sup.real_email_id
                LEFT JOIN `status` stat on stat.name = cg.status
                WHERE cg.type_absence = ? " . $search['query_and'],
                [$type]);
        }
        else if ($type=='mis_a_pied' && in_array($auth->role,['superviseur'])) {
            $absences = DB::select(
                "SELECT cg.id, cg.employe_id, cg.depart, cg.retour, cg.motif, cg.status, cg.employe_temporaire, cg.employe_remplacant_id, cg.created_at,
                stat.description as 'status_description', stat.color as 'status_color', cg.user_id, f.libelle as 'fonction',
                cg.site_id, st.nom as 'site', st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', 
                a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi, a.fonction_id,
                arp.nom as 'arp_employe', arp.societe_id as 'arp_societe_id', arp.numero_stagiaire as 'arp_numero_stagiaire', 
                arp.numero_employe as 'arp_numero_employe', arp.num_emp_soit as 'arp_num_emp_soit', cg.nb_jour, cg.superviseur_id,
                sup.name as 'sup_nom', coalesce(ur.email, sup.email) as 'sup_email'
                FROM absences cg
                LEFT JOIN employes a ON a.id = cg.employe_id
                LEFT JOIN employes arp ON arp.id = cg.employe_remplacant_id
                LEFT JOIN sites st ON st.idsite = cg.site_id
                LEFT JOIN fonctions f ON f.id = a.fonction_id
                LEFT JOIN users us on us.id = cg.user_id
                LEFT JOIN users sup on sup.id = cg.superviseur_id
                LEFT JOIN users ur on ur.id = sup.real_email_id
                LEFT JOIN `status` stat on stat.name = cg.status
                WHERE cg.type_absence = ? and cg.superviseur_id = ? " . $search['query_and'],
                [$type, $auth->id]);
        }
         else if(in_array($auth->role, ['rh', 'resp_rh', 'validateur', 'access', 'daf'])) {
            $absences = DB::select(
                "SELECT cg.id, cg.employe_id, cg.depart, cg.retour, cg.motif, cg.status, cg.employe_temporaire, cg.employe_remplacant_id, cg.created_at,
                stat.description as 'status_description', stat.color as 'status_color', cg.user_id, f.libelle as 'fonction',
                cg.site_id, st.nom as 'site', st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', 
                a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi, a.fonction_id,
                arp.nom as 'arp_employe', arp.societe_id as 'arp_societe_id', arp.numero_stagiaire as 'arp_numero_stagiaire', arp.num_emp_saoi as 'arp_num_emp_saoi',
                arp.numero_employe as 'arp_numero_employe', arp.num_emp_soit as 'arp_num_emp_soit'
                FROM absences cg
                LEFT JOIN employes a ON a.id = cg.employe_id
                LEFT JOIN employes arp ON arp.id = cg.employe_remplacant_id
                LEFT JOIN sites st ON st.idsite = cg.site_id
                LEFT JOIN fonctions f ON f.id = a.fonction_id
                LEFT JOIN users us on us.id = cg.user_id
                LEFT JOIN `status` stat on stat.name = cg.status
                WHERE cg.type_absence = ? " . $search['query_and'],
                [$type]
            );
        }
        else if($type != 'mis_a_pied' && (in_array($auth->role, ['superviseur','resp_sup', 'resp_op']) || $user_service_id)){
            $result = DB::select("SELECT group_id from absences where user_id = ? and group_id is not null", [$auth->id]);
            $groupIds = array_map(function ($resultItem) {
                return (string)$resultItem->group_id;
            }, $result);
            if (count($groupIds) > 0) {
                $implodeIds = implode(',', $groupIds);
            }
            else 
                $implodeIds="\"\"";
            $absences = DB::select(
                "SELECT cg.id, cg.employe_id, cg.depart, cg.retour, cg.motif, cg.status, cg.employe_temporaire, cg.employe_remplacant_id, cg.created_at,
                stat.description as 'status_description', stat.color as 'status_color', cg.user_id, f.libelle as 'fonction',
                cg.site_id, st.nom as 'site', st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', 
                a.nom as 'employe', a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi, a.fonction_id,
                arp.nom as 'arp_employe', arp.societe_id as 'arp_societe_id', arp.numero_stagiaire as 'arp_numero_stagiaire', arp.numero_employe as 'arp_numero_employe', 
                arp.num_emp_soit as 'arp_num_emp_soit'
                FROM absences cg
                LEFT JOIN employes a ON a.id = cg.employe_id
                LEFT JOIN employes arp ON arp.id = cg.employe_remplacant_id
                LEFT JOIN sites st ON st.idsite = cg.site_id
                LEFT JOIN fonctions f ON f.id = a.fonction_id
                LEFT JOIN users us on us.id = cg.user_id
                LEFT JOIN `status` stat on stat.name = cg.status
                WHERE (cg.group_id in (" . $implodeIds . ") OR (cg.user_id = ?)) 
                AND cg.type_absence = ? ". $search['query_and']
                , [$auth->id, $type]);
        }
        else 
            return response(["error" => "EACCES"]);
        return response(compact('absences'));
    }

    protected function validateAndSetAbsence($request, $absence){
        $absence->type_absence = $request->type_absence;
        $absence->employe_id = $request->employe_id;
        if($request->employe_id)
            $absence->site_id = Employe::find($request->employe_id)->real_site_id;

        $absence->employe_temporaire = $request->employe_temporaire;
        $absence->employe_remplacant_id = $request->employe_remplacant_id;                        
        $absence->depart = $request->depart;
        $absence->retour = $request->retour;
        $absence->motif = $request->motif;
        
        if($request->is_tmp) {
            $validator = Validator::make($request->all(), [
                'employe_id' => 'required',
                // 'employe_temporaire' => 'required',
                'depart' => 'required',
                'retour' => 'required',
                'motif' => 'required'                
            ]);
            $absence->personal = false;
        }else{
            $validator = Validator::make($request->all(), [
                'employe_id' => 'required',
                'depart' => 'required',
                'retour' => 'required',
                'motif' => 'required'               
            ]);
            $absence->personal = false;
        }
        if ($validator->fails())
            return ['error' => $validator->errors()->first()];
        
        $depart =new \DateTime($request->depart);
        $retour =new \DateTime($request->retour);
        if(!(
            $depart < $retour
            && $depart > (new \DateTime())->sub(new \DateInterval('P7D'))
            && in_array($depart->format("H:i:s"), ["06:00:00", "18:00:00"])
            && in_array($retour->format("H:i:s"), ["06:00:00", "18:00:00"])
        ))
            return ["error" => "Date incorrecte"];
        if(!in_array($request->type_absence, ["conge", "permission","mis_a_pied"]))
            return ["error" => "Type d'absence incorrect"];
        return ['error' => ''];
    }
    
    protected function validateAndSetMisePied(Request $request, $absence, $to_edit = false){
        $absence->type_absence = $request->type_absence;
        $absence->employe_id = $request->employe_id;
        if($request->site_id)
            $absence->site_id = $request->site_id;
        // else if ($request->employe_id)
        //     $absence->site_id = Employe::find($request->employe_id)->real_site_id;

        $absence->employe_temporaire = $request->employe_temporaire;
        $absence->employe_remplacant_id = $request->employe_remplacant_id;
        $absence->depart = $request->depart;
        $absence->retour = $request->retour;
        $absence->motif = $request->motif;
        $absence->date_pointage = $request->date_pointage;
        $absence->superviseur_id = $request->superviseur_id;
        $absence->type_mis_a_pied = $request->type_mis_a_pied;
        $absence->absence_sans_motif = $request->absence_sans_motif;
        $absence->nb_jour = $request->nb_jour;
        if (in_array($request->user()->role, ["rh", "resp_rh"])) {
            $validateMisePied = [];
            if ($absence->absence_sans_motif) {
                $absence->date_pointage = null;
                $validateMisePied = [
                    'employe_id' => 'required',
                    'motif' => 'required',
                    'type_mis_a_pied' => 'required',
                    'nb_jour' => 'required',
                    'superviseur_id' => 'required',
                ];
            }
            else {
                $validateMisePied = [
                    'employe_id' => 'required',
                    'motif' => 'required',
                    'date_pointage' => 'required',
                    'type_mis_a_pied' => 'required',
                    'nb_jour' => 'required',
                    'superviseur_id' => 'required',
                ];
            }
            
            $validator = Validator::make($request->all(),$validateMisePied);
            // $request->personal = false;
        }
        if ($absence->id && in_array($request->user()->role, ['rh', 'resp_rh'])){
            if ($absence->absence_sans_motif)
                $absence->date_pointage = null;
            if ($to_edit) {
                $validator = Validator::make($request->all(), [
                    'employe_id' => 'required',
                ]);
            }
            else {
                $validator = Validator::make($request->all(), [
                    'employe_id' => 'required',
                    'depart' => 'required',
                    'retour' => 'required',
                ]);
            }
            $absence->personal = false;
        }
        if ($validator->fails())
            return ['error' => $validator->errors()->first()];
        
        if(in_array($request->user()->role, ["rh", "resp_rh"])){
            $site = Site::find($request->site_id);
            if ($site) {
                $site->superviseur_id = $request->superviseur_id;
                $site->save();
            }
        }

        $depart = new \DateTime($request->depart);
        $retour = new \DateTime($request->retour);
        $currentDateTime = new \DateTime();
        $twentyFourHoursAgo = $currentDateTime->modify('-24 hours');
        if (!(
            $depart < $retour
            && $depart > $twentyFourHoursAgo
            && in_array($depart->format("H:i:s"), ["06:00:00", "18:00:00"])
            && in_array($retour->format("H:i:s"), ["06:00:00", "18:00:00"])
            ) && 
            ($request->depart && $request->retour)
        ){
            return ["error" => "Sélectionner une date à partir de " . $twentyFourHoursAgo->format('Y-m-d H:i:s')];
        }

        if (!in_array($request->type_absence, ["conge", "permission", "mis_a_pied"]))
            return ["error" => "Type d'absence incorrect"];
        return ['error' => ''];
    }

    public function store(Request $request){
        $absence = new Absence();
        $employe = Employe::find($request->employe_id);
        $auth = $request->user();
        if($employe != null){
            if ($request->type_absence == "mis_a_pied") {
                if (in_array($auth->role,["rh", "resp_rh"])) {
                    $validator = $this->validateAndSetMisePied($request, $absence);
                    if($validator['error'])
                        return response($validator);
                    $absence->status = "demande";
                    $absence->user_id = $auth->id;
                    $absence->nb_jour = $request->nb_jour;
                    $absence->created_at = new \DateTime();
                    $absence->updated_at = new \DateTime();
                    if ($absence->save()) {
                        HistoriqueController::new_absence($request, $absence);
                        return response(["success" => "Demande bien envoyé", "id" => $absence->id]);
                    }
                }
                return response(["error" => "EACCES"]);
            }
            else if(
                $request->type_absence != 'conge' || ($request->type_absence == 'conge' && in_array($employe->societe_id, ["1","2","6"]))
            ){
                $employe = Employe::find($request->employe_id);
                $user_service_id = (Employe::find($request->user()->employe_id))->service_id;
                if (($employe->service_id && $user_service_id && $employe->service_id == $user_service_id)
                    || in_array($auth->role, ["superviseur", "resp_sup", 'resp_op'])
                ) {
                    $validator = $this->validateAndSetAbsence($request, $absence);
                    if($validator['error'])
                        return response($validator);
                    $absence->status = "demande";
                    $absence->user_id = $auth->id;
                    $absence->created_at = new \DateTime();
                    $absence->updated_at = new \DateTime();
                    if($absence->save()){
                        HistoriqueController::new_absence($request, $absence);
                        return response(["success" => "Demande bien envoyé", "id" => $absence->id]);
                    }
                }
            }
            else if($request->type_absence == 'conge' && !in_array($employe->societe_id, ["1","2", "6"]))
                return response(["error" => "Demande de congé non permis pour les stagiaires, temporaire et sans matricule"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $absence = Absence::find($id);
        $old_absence = clone $absence;
        $auth = $request->user();
        if($auth->id == $request->user()->id and $absence->status == "draft"){
            if ($absence->type_absence == "mis_a_pied")
                $validator = $this->validateAndSetMisePied($request, $absence);
            else
                $validator = $this->validateAndSetAbsence($request, $absence);
            if($validator['error'])
                return response($validator);
            $request->merge(['site_id' => DB::select("SELECT real_site_id FROM employes
                                            Where id = $request->employe_id")[0]->real_site_id]);
            $absence->status = "demande";
            $absence->user_id = $auth->id;
            $absence->created_at = new \DateTime();
            $absence->updated_at = new \DateTime();
            if($absence->save()){
                HistoriqueController::update_absence($request, $old_absence, "Renvoie de la demande");
                return response(["success" => "Demande bien renvoyé", "id" => $absence->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_absence(Request $request, $id){
        $absence = Absence::find($id);
        if(
            (in_array($absence->status, ['demande']) && $request->user()->id == $absence->user_id)
             || (in_array($absence->status, ['demande', 'traite']) && in_array($request->user()->role, ['rh', 'resp_rh']))
        ){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $absence->status = 'draft';
            $absence->updated_at = new \DateTime();

            $absence->note_id = HistoriqueController::action_absence($request, "Demande annulé", $id);
            if($absence->save()){
                if($absence->type_absence == "mis_a_pied") {
                    Notification::create([
                        'historique_id' => $absence->note_id,
                        'receiver_id' => $absence->superviseur_id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                else if($absence->user_id != $request->user()->id && !in_array($request->user()->role, ["superviseur", "resp_sup", 'resp_op'])) {
                    Notification::create([
                        'historique_id' => $absence->note_id,
                        'receiver_id' => $absence->user_id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                return response(["success" => "Demande annulé", "id" => $absence->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done(Request $request, $id){
        $absence = Absence::find($id);
        $old_absence = clone $absence;
        if(in_array($request->user()->role, ['rh', 'resp_rh']) && in_array($absence->status, ['demande', 'traite'])){
            if ($absence->type_absence == "mis_a_pied"){
                $validator = Validator::make($request->all(),[
                    'note' => 'required',
                ]);
            }
            else {
                $verifyDatePaie = DB::select("SELECT * FROM paies  WHERE date_paie = '$request->date_paie' 
                    AND (status = 'traite' or status = 'done') AND employe_id = $absence->employe_id"
                );
                $validator = Validator::make($request->all(), [
                    'note' => 'required',
                    'date_paie' => 'required',
                ]);
                // if(new \DateTime($absence->depart) < new \DateTime()){
                //     return (["error" => "Date de départ déjà dépassée."]);
                // };
                if (count($verifyDatePaie)>0) 
                    return (["error" => "Date de paie invalide"]);
            }
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $repartitions = json_decode($request->repartitions, true);

            foreach ($repartitions as $i=>$rep) {
                if ($i == 0) {
                    $absence->depart = $rep['depart'];
                    $absence->retour = $rep['retour'];
                    if(count($repartitions)> 1)
                        $absence->group_id = $absence->id;
                }
                else {
                    $new_conge = new Absence();
                    $new_conge->type_absence = $absence->type_absence;
                    $new_conge->employe_id = $absence->employe_id;
                    $new_conge->site_id = $absence->site_id;
                    $new_conge->employe_remplacant_id = $absence->employe_remplacant_id;
                    $new_conge->employe_temporaire = $absence->employe_temporaire;
                    $new_conge->motif = $absence->motif;
                    $new_conge->group_id = $absence->id;
                    $new_conge->depart = $rep['depart'];
                    $new_conge->retour = $rep['retour'];
                    $new_conge->user_id = $request->user()->id;
                    $new_conge->status = 'done';
                    $new_conge->date_paie = $rep['date_paie'];
                    $new_conge->created_at = new \DateTime();
                    $new_conge->updated_at = new \DateTime();
                    $new_conge->save();
                    HistoriqueController::new_repartition_absence($request, $new_conge);
                    if (in_array($absence->type_absence, ["conge", "permission"])) {
                        $paie = DB::select("SELECT id FROM paies WHERE date_paie = '$request->date_paie' AND (status = 'demande') AND employe_id = $absence->employe_id");
                        if (count($paie) > 0) {
                            $request->merge(['employe_id' => $absence->employe_id]);
                            $request->merge(['date_paie' => $absence->date_paie]);
                            $request->merge(['paie_id' => $paie[0]->id]);
                            PaieController::RecalculePaie($request);
                            // $request->merge(['recalcul' => 1]);
                            // $paieController = new PaieController();
                            // $paieController->generate_paie($request, $absence->employe_id);
                        }
                    }
                }
            }

            $absence->status = 'done';
            $absence->date_paie = $request->date_paie;
            $absence->updated_at = new \DateTime();
            $note = "";
            if ($absence->type_absence == 'mis_a_pied') 
                $note = "Mise à pied terminé";
            else $note = "Congé validé";
            $absence->note_id = HistoriqueController::action_absence($request, $note, $id);
            if($absence->save()){
                if (in_array($absence->type_absence, ["conge", "permission"])) {
                    $paie = DB::select("SELECT id FROM paies WHERE date_paie = '$absence->date_paie' AND (status = 'demande') AND employe_id = $absence->employe_id");
                    if (count($paie) > 0) {
                        $request->merge(['employe_id' => $absence->employe_id]);
                        $request->merge(['date_paie' => $absence->date_paie]);
                        $request->merge(['paie_id' => $paie[0]->id]);
                        PaieController::RecalculePaie($request);
                        // $request->merge(['recalcul' => 1]);
                        // $paieController = new PaieController();
                        // $paieController->generate_paie($request, $absence->employe_id);
                        
                    }
                }
                $employe = Employe::find($old_absence->employe_id);
                $nb_abs = 0;
                $diffHours = Carbon::parse($old_absence->retour)->diffInHours(Carbon::parse($old_absence->depart));
                if ($diffHours) {
                    $nb_abs = $diffHours / 24;
                }
                $employe->nb_conge_pris = ($employe->nb_conge_pris ?? 0) + $nb_abs;
                $employe->save();
                return response(["success" => "Congé validé", "id" => $absence->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function request_validation(Request $request, $id){
        $absence = Absence::find($id);
        if(in_array($request->user()->role, ['rh', 'resp_rh']) && in_array($absence->status, ['demande', 'traite'])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $absence->status = 'validation';
            $absence->updated_at = new \DateTime();

            $absence->note_id = HistoriqueController::action_absence($request, "Requête de validation", $id);
            if($absence->save()){
                return response(["success" => "Demande de validation envoyé", "id" => $absence->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function reply_validation(Request $request, $id){
        $absence = Absence::find($id);
        if($request->user()->role == 'validateur' && $absence->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $absence->status = 'traite';
            $absence->updated_at = new \DateTime();

            $absence->note_id = HistoriqueController::action_absence($request, "Réponse à la demande", $id);
            if($absence->save()){
                $users = DB::select("SELECT id from users where role = 'rh' or role = 'resp_rh'", []);
                foreach ($users as $u) {
                    Notification::create([
                        'historique_id' => $absence->note_id,
                        'receiver_id' => $u->id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                return response(["success" => "Réponse de la demande envoyé", "id" => $absence->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function cancel_validation(Request $request, $id){
        $absence = Absence::find($id);
        if(in_array($request->user()->role, ['rh', 'resp_rh']) && $absence->status == 'validation'){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $absence->status = 'traite';
            $absence->updated_at = new \DateTime();

            $absence->note_id = HistoriqueController::action_absence($request, "Demande de validation annulé", $id);
            if($absence->save()){
                return response(["success" => "Demande de validation annulé", "id" => $absence->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function note(Request $request, $id){
        $absence = Absence::find($id);
        if(
            (in_array($request->user()->role, ['rh', 'resp_rh']) && in_array($absence->status, ['demande', 'traite']))
        ){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $absence->status = "traite";
            $absence->updated_at = new \DateTime();
            $absence->note_id = HistoriqueController::action_absence($request, "Note", $id);
            if($absence->save())
                return response(["success" => "Note ajouté avec succès", "id" => $absence->id]);
        }
        return response(["error" => "EACCES"]);
    }

    public function edit(Request $request, $id){
        $absence = Absence::find($id);
        $old_absence = clone $absence;
        if (in_array($request->user()->role,["rh", "resp_rh"]) && in_array($absence->status,["demande"])) {
            $validator = $this->validateAndSetMisePied($request, $absence, true);
            // return $absence;
            if ($validator['error'])
                return response($validator);
            if ($request->employe_id)
                $request->merge(['site_id' => DB::select("SELECT real_site_id FROM employes
                                                Where id = $request->employe_id")[0]->real_site_id]);
            $absence->updated_at = new \DateTime();
            if ($absence->save()) {
                $objet = "Modification de mise à pied";
                HistoriqueController::update_absence($request, $old_absence, $objet);
                return response(["success" => "Demande bien renvoyé", "id" => $absence->id]);
            }
        }
        else if ($request->user()->role == 'superviseur' &&
            $absence->status == 'demande'
        ) {
            $validator = Validator::make($request->all(), [
                'superviseur_id' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            if ($absence->superviseur_id == $request->superviseur_id)
                return response(['error' => 'Aucune modification effectué']);

            $absence->superviseur_id = $request->superviseur_id;
            $absence->updated_at = new \DateTime();
            if ($absence->save()) {
                $objet = "Modification de mise à pied";
                HistoriqueController::update_absence($request, $old_absence, $objet);
                return response(["success" => "Demande bien renvoyé", "id" => $absence->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function getResponsable(Request $request, $id=null){
        $resp = DB::select(
            "SELECT emp.responsable_id, resp.responsable_id as chef_hierarchique, resp_resp.responsable_id as resp_chef 
            FROM employes emp
            LEFT JOIN employes resp ON resp.id = emp.responsable_id
            LEFT JOIN employes resp_resp ON resp_resp.id = resp.responsable_id
            LEFT JOIN users u on u.employe_id = emp.id
            WHERE u.id = ?",
            [$id ?? $request->user()->id]
        )[0];
        $ids = [$resp->responsable_id, $resp->chef_hierarchique, $resp->resp_chef];
        $userIds = DB::select("SELECT id FROM users WHERE employe_id in (?, ?, ?)", $ids);
        $respUserIds = [];
        if (count($userIds) > 0) {
            foreach ($userIds as $user) {
                if ($user->id) {
                    $respUserIds[] = $user->id;
                }
            }
        }
        return response()->json(compact('respUserIds'));
    }

    public function ApplyMisePied(Request $request,$id){
        $absence = Absence::find($id);
        $auth = $request->user();
        // $respIds = $this->getResponsable($request, $absence->superviseur_id);
        // $respData = json_decode($respIds->getContent(), true);
        // in_array($auth->id, $respData['respUserIds'])
        if (in_array($auth->role ,["rh", "resp_rh"]) && $absence->status == 'demande') {
            $old_absence = clone $absence;
            $validator = $this->validateAndSetMisePied($request,$absence);
            if ($validator['error'])
                return response($validator);
            if (new \DateTime() > new \DateTime($request->depart)) {
                $pointage = Pointage::where('date_pointage', '>=', new \DateTime($request->depart))
                    ->where('employe_id', $absence->employe_id)
                    ->where(function ($query) {return $query->whereNull('soft_delete')->orWhere('soft_delete', 0);})
                    ->first();

                if ($pointage != null)
                    return response(["error" => "L'agent a déjà pointé le " . $pointage['date_pointage'] . " dans TLS"]);
                
                $reclamation = PointageReclamation::where('date_pointage', '>=', new \DateTime($request->depart))
                    ->where('employe_id', $absence->employe_id)
                    ->first();
                
                if ($reclamation != null) 
                    return response(["error" => "L'agent a déjà une réclamation de pointage le " . $reclamation['date_pointage']]);
            }
            if ($absence->employe_id != $old_absence->employe_id) {
                return response(["error" => "Une erreur est survenue"]);
            }
            $endDate = Carbon::now()->copy()->addDays(14);
            $depart = Carbon::parse($request->depart);
            if($depart > $endDate){
                return response(["error" => "Choisissez une date de depart plus proche"]);
            }
            $absence->status = "done";  
            $absence->updated_at = new \DateTime();
            $request->merge(['site_id' => $absence->site_id]);
            $date = \DateTime::createFromFormat('Y-m-d H:i:s', $request->date_pointage);
            if (!$date || $date->format('Y-m-d H:i:s') !== $request->date_pointage) {
                $request->merge(['date_pointage' => null]);
            }
            if ($absence->save()) {
                $this->message_apply($request, $absence);
                HistoriqueController::update_absence($request, $old_absence, "Application de mise à pied");
                return response(["success" => "Mise à pied appliqué", "id" => $absence->id]);
            }
            return response(["error" => "Une erreur est survenue"]);
        }
        return response(["error" => "EACCES"]);
    }

    protected static function message_apply(Request $request, $absence){
        $message = new Message();
        $employe = EmployeUtil::getEmployeById($absence->employe_id);
        $message->objet = 'Mise à pied: '. $employe;
        $message->user_id = $request->user()->id;
        $message->created_at = new \DateTime();
        $message->updated_at = new \DateTime();

        $depart = new \DateTime($absence->depart);
        if ($depart->format('H:i:s') == "18:00:00")
            $depart = $depart->format('d/m/Y') . ' NUIT';
        else
            $depart = $depart->format('d/m/Y') . ' JOUR';

        $retour = new \DateTime($absence->retour);
        if ($retour->format('H:i:s') == "18:00:00")
            $retour = $retour->format('d/m/Y') . ' NUIT';
        else
            $retour = $retour->format('d/m/Y') . ' JOUR';

        $message->content = '<p><b>'.$employe . '</b></p>' .
            '<p><b>Depart: </b>' . $depart . '<p/>' .
            '<b>Retour: </b>'. $retour . '' .
            '<p><b>Motif: </b>' . $absence->motif . '</p>' .
            '<p><b>Ref: </b>' . $absence->id . '</p>';
        
        if ($message->save()) {
            $note = new NoteMessage();
            $note->message_id = $message->id;
            $note->user_id = $absence->superviseur_id;
            $note->follow = 1;
            $note->created_at = new \DateTime();
            $note->updated_at = new \DateTime();
            $note->save();
        }
    }

    public function storeFictif(Request $request){
        if (in_array($request->user()->role, ["rh", "resp_rh"]) && $request->fictif && $request->type_absence == 'conge') {
            $employe = Employe::find($request->employe_id);
            if (!in_array($employe->societe_id, [1, 2]) ) {
                return response(["error"=> "Choisir un employé confirmé"]);
            }
            $validator = Validator::make($request->all(), [
                'employe_id' =>'required',
                'nb_jour' =>'required | numeric | min: 0.5',
                'motif' =>'required',
            ]);
            if ($validator->fails())
                return ['error' => $validator->errors()->first()];
            $cg_fictif = new Absence();
            $cg_fictif->employe_id = $request->employe_id;
            $cg_fictif->depart = (new \DateTime("2000-01-01 06:00:00"))->format('Y-m-d H:i:s');
            $cg_fictif->retour = Carbon::createFromFormat('Y-m-d H:i:s', '2000-01-01 06:00:00')->addHours($request->nb_jour * 24);
            $cg_fictif->fictif = $request->fictif;
            // $cg_fictif->retour = (new DateTime('2000-01-01 06:00:00'))->modify('+' . $request->nb_jour . ' days');
            $cg_fictif->status = 'done';
            $cg_fictif->user_id = $request->user()->id;
            $cg_fictif->type_absence = $request->type_absence;
            $cg_fictif->motif = $request->motif;
            $cg_fictif->created_at = new \DateTime();
            $cg_fictif->updated_at = new \DateTime();
            if($cg_fictif->save()){
                $employe->nb_conge_pris = ($employe->nb_conge_pris ?? 0) + $request->nb_jour;
                $employe->save();
                HistoriqueController::new_absence_fictif($request, $cg_fictif);
                return response(["success" => "Congé fictif enregistré", "id" => $cg_fictif->id]);
            };
        }
        return response(["error" => "EACCES"]);
    }
}
