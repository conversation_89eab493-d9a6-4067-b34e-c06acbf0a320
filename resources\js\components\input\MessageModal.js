import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import InputMultipleUser from './InputMutipleUser'
import Textarea from './Textarea'
import useToken from '../util/useToken'

export default function MessageModal({value, name, defautUsers, updateData, closeModal }) {
    const [copyUsers, setCopyUsers] = useState([])
    const [users, setUsers] = useState(defautUsers ? defautUsers : []) 
    const [content, setContent] = useState('')
    const [submitDisabled, disableSubmit] = useState(false)
    const [error, setError] = useState('')
    const [faitMarquant, setFaitMarquant] = useState(null)

    const replaceChevrons = (text) => {
        return text.replace(/</g, '&lt;').replace(/>/g, '&gt;');
    }

    const handleSubmit = () => {
        disableSubmit(true)
        let data = new FormData()
        data.append('content', '<p>' + replaceChevrons(content) + '</p>')
        users.forEach((u, index) => {
            data.append(`receivers[${index}]`, u.id);
        })
        copyUsers.forEach((u, index) => {
            data.append(`copies[${index}]`, u.id);
        })
        if (name == "fait_marquant_id") {
            data.append("fait_marquant_id", value);
        }
        else if (name) {
            data.append(name, value);
        }
        axios.post("/api/message/add", data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success){
                if (updateData) updateData()
                closeModal()
            }
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    useEffect(() => {
        let isMounted = true
        if (name == "fait_marquant_id") {
            axios.get('/api/fait_marquant/show/' + value, useToken())
            .then((res) => {
                if(isMounted){
                    setFaitMarquant(res.data)
                }
            })
            .catch((e) => {
                console.error(e)
            })
        }
        return () => { isMounted = false }
    }, [])

    return <div className='modal'>
        <div>
            <h3>
                Discussion interne
            </h3>
            <InputMultipleUser label="Destinataire" users={users} setUsers={setUsers} />
            <InputMultipleUser label="Copies" users={copyUsers} setUsers={setCopyUsers} />
            <Textarea
                value={content}
                label="Commentaire"
                onChange={(value) => setContent(value)}
                required />
            <div className='action-container'>
                <span>
                    <Link to={'/message/add?' + name + '=' + (value ? value : '')} >Plus d'options</Link>
                </span>
            </div>
            {
                error &&
                <div className='container-error'>
                    {error}
                </div>
            }
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleSubmit}>Envoyer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
            {
                faitMarquant &&
                <div className='card-container'>
                    <h3>
                        {faitMarquant.site}
                    </h3>
                    <div>
                        Objet: <span className='text'>{faitMarquant.objet}</span>
                    </div>
                    <p className='text' style={{whiteSpace: "pre-line"}}>
                        {faitMarquant.commentaire}
                    </p>
                </div>
            }
        </div>
    </div>
}