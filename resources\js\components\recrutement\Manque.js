import React, { useEffect, useState } from 'react';
import moment from 'moment';
import LoadingPage from '../loading/LoadingPage';
import useToken from '../util/useToken';

export default function Manque({data}) {
    const [isLoading, toggleLoading] = useState(true)
    const [manques, setManques] = useState([])

    useEffect(() => {
        let isMounted = true
        axios.get("/api/recrutement/manque/" + data.numero_stagiaire ,  useToken())
        .then((res) => {
            if(isMounted) {
                setManques(res.data.manques)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false};
    }, []);

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <div>
                    {
                        manques.map((m) => (
                            
                            <div key={m.id} className='line-container'>
                                <div>
                                    {
                                        moment(m.date_service).format("DD MMM YYYY") 
                                        + " " + (moment(m.date_service).format("HH:mm:ss") == "07:00:00" ? "JOUR" : "NUIT")
                                    }
                                </div>
                                <div className='secondary'>
                                    {m.manque.split(',').map(h => <span key={h} className='badge-outline'>{h}</span>)}
                                </div>
                            </div>
                        ))
                    }
                </div>
        }
    </>
}