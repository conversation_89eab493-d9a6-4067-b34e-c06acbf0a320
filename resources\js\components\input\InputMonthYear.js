
import React, {useEffect} from 'react'
import InputMonth from './InputMonth'
import InputYear from './InputYear'
import moment from 'moment';
import '../employe/tab/pointage.css'
export default function InputMonthYear({ label, value, onChange, required, setDefaultDate, defaultDate, disable }) {
    useEffect(() => {
        if (defaultDate) {
            onChange({ year: defaultDate.format("YYYY"), month: defaultDate.format("MM") })
        }
        else if(setDefaultDate){
            let currentDate = moment().subtract(1, "month")
            if(moment().isAfter(moment().set("date", 20)) && moment().isBefore(moment().add(1, "month").set("date", 1)))
                currentDate = moment()
            onChange({ year: currentDate.format("YYYY"), month: currentDate.format("MM") })
        }
    }, []);
    return (
        <div className='search-pointage'>
            <div style={{ width: "60%" }}>
                <InputMonth label={"Mois " + (label ? "(" + label + ")" : "")} 
                    selected={value ? value.month : null} 
                    setSelected={(newMonth) => onChange({ ...value, month: ("0" + newMonth).slice(-2)})}
                    disable={disable}
                    required={required}/>
            </div>
            <div style={{ width: "40%" }}>
                <InputYear label="Années" 
                    selected={value ? value.year : null} 
                    setSelected={(newYear) => onChange({...value, year: String(newYear)})} 
                    disable={disable}
                    required={required} />
            </div>
        </div>
    )
}