import React, { useEffect, useState } from 'react';
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import InputSite from '../input/InputSite';
import Textarea from '../input/Textarea';
import moment from 'moment';
import InputDateTime from '../input/InputDateTime';
import removeDuplicateBreak from '../util/stringUtil';
import InputEmploye from '../input/InputEmploye';
import InputCheckBox from '../input/InputCheckBox';
import LoadingPage from '../loading/LoadingPage';
import './visite.css'
import InputText from '../input/InputText';
import DualContainer from '../container/DualContainer';
import ModalInfo from '../modal/ModalInfo';

export default function EditVisitePoste() {
    const [site, setSite] = useState(null)
    const [interim, setInterim] = useState(false)
    const [employe, setEmploye] = useState(null);
    const [authEmploye, setAuthEmploye] = useState(null)
    const [dateVisite, setDateVisite] = useState()
    const [compteRendu, setMotif] = useState("")
    const [notification, setNotification] = useState("")
    const [vigilances, setVigilances] = useState([]);
    const [start, setStart] = useState("");
    const [end, setEnd] = useState("");
    const [showVigilance, toggleVigilance] = useState(false);
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    const canRefresh = true;
    const [refresh, setRefresh] = useState(false);
    const [isCompleted, setIsCompleted] = useState(false)
    const [lastVisite, setLastVisite] = useState(null)
    const message = "Les visites sur le même site doivent être espacées d'au moins une heure";
    const [showModalInfo, toggleModalInfo] = useState(false)
    const [dayVisite, setDayVisite] = useState(false)
    const [nightVisite, setNightVisite] = useState(false)

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const compteRenduStr = removeDuplicateBreak(compteRendu)
        const data = {
            site_id: (site ? site.id : ""),
            employe_id: (employe ? employe.id : ""),
            interim: (interim ? 1 : 0),
            date_visite: dateVisite ? moment(dateVisite).format("YYYY-MM-DD HH:mm:ss") : "", 
            compte_rendu: compteRenduStr,
            start: start ?? "",
            end: end ?? "",
        }
        if (
            (nightVisite && moment().format("HH:mm:ss") > "08:00:00") || 
            (dayVisite && moment().format("HH:mm:ss") > "20:00:00")
          ) {
            toggleModalInfo(true);
        }
        axios.post('/api/visite_poste/add', data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.error)
                setError(res.data.error)
            else
                setNotification(res.data)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }

    useEffect(() => {
        let isMounted = true
        toggleVigilance(false)
        if(site && site.id && site.pointeuse == 1 && dateVisite && (!interim || (interim && employe && employe.id))){
            axios.get('/api/alarm?site_id=' + site.id + (interim ? '&employe_id=' + employe.id : '')
                 + '&date_visite=' + moment(dateVisite).format("YYYY-MM-DD HH:mm:ss"), useToken())
            .then((res) => {
                if(isMounted){
                    if (res.data.vigilances && res.data.vigilances.length > 0) {
                        setVigilances(res.data.vigilances)
                        let first = res.data.vigilances[0]
                        setStart(first.dtarrived)
                        setEnd(res.data.endVisite[0].date)
                        setRefresh(false)
                        setIsCompleted(res.data.isCompleted)
                    } else {
                        setVigilances([])
                        setStart("")
                        setEnd("")
                        setRefresh(false)
                        setIsCompleted(res.data.isCompleted)
                    }
                    toggleVigilance(true)
                }
            })
        }
        else toggleVigilance(true)
        return () => { isMounted = false };
    }, [site, dateVisite, interim, employe, refresh])

    useEffect(() => {
        let isMounted = true
        if(site && site.id && site.pointeuse == 1 && dateVisite && (moment(dateVisite).hour() != 0 && moment(dateVisite).minute() != 0) && (!interim || (interim && employe && employe.id))){
            axios.get('/api/visite_poste/get_last_visite?site_id=' + site.id + (interim ? '&employe_id=' + employe.id : ''), useToken())
        .then((res) => {
            if(isMounted){
                if (res.data) {
                    setLastVisite(res.data)
                }
            }
        })
        }
    }, [site, dateVisite, interim, employe])

    useEffect(() => {
        let isMounted = true
        axios.get('/api/employe/auth', useToken())
        .then((res) => {
            if(isMounted){
                let employe = res.data
                if(employe.id) {
                    employe.matricule = matricule(employe)
                    setAuthEmploye(employe)
                }
            }
        })
        return () => { isMounted = false };
    }, [])

    useEffect(() => {
        if (lastVisite && dateVisite && moment(dateVisite).diff(moment(lastVisite), 'hours') < 1) {
            setError(message)
            disableSubmit(true)
        }
    }, [lastVisite])

    useEffect(() => {
        if (dateVisite) {
            let heureVisite = moment(dateVisite).hour();
            if (heureVisite >= 6 && heureVisite <= 18) {
                setDayVisite(true);
                setNightVisite(false);
            } else {
                setNightVisite(true);
                setDayVisite(false);
            }
        }
    }, [dateVisite]);

    
    return (
        <div id="content">
            {
                showModalInfo &&
                <ModalInfo
                dayVisite={dayVisite}
                nightVisite={nightVisite}
                closeModal={() => toggleModalInfo(false)}/>
            }  
            {
                notification ? 
                    <Notification next={"/visite-poste?id=" + notification.id} message={notification.success}/>
                :
                <div>
                    <div className="title-container">
                        <h2>Nouvelle visite de poste</h2>
                    </div>     
                    <form onSubmit={handleSubmit}>
                        <InputSite
                            withoutDelete
                            required
                            value={site} 
                            onChange={(value) => setSite(value)}/>
                        {
                            (site && site.pointeuse == 1) &&
                            <div className='field-container'>
                                <InputCheckBox label="Confirmation par interim" checked={interim} onChange={setInterim}/>
                            </div>
                        }
                        {
                            interim == 1 &&
                            <InputEmploye value={employe} onChange={setEmploye} urlSearch="/api/employe/interim"/>
                        }
                        <InputDateTime
                            required
                            label="Date de visite"
                            dateFormat="dd/MM/yyyy hh:mm"
                            value={dateVisite}
                            onChange={(dt) =>{setDateVisite(dt)}}
                            canRefresh={canRefresh}
                            setRefresh={setRefresh}/>
                        {
                            (site && site.id && site.pointeuse == 1 && dateVisite && (!interim || (interim && employe && employe.id)) && showVigilance) &&  
                            <div className='right'>
                                {
                                    vigilances.length > 0 ? (
                                        isCompleted ? 
                                        <div className='secondary'>
                                            Empreinte faite : {vigilances.map(v => moment(v.dtarrived).format("HH[h]mm")).join(', ')}
                                        </div>
                                        :
                                        <div className='secondary'>
                                            Empreinte faite : {vigilances.map(v => moment(v.dtarrived).format("HH[h]mm")).join(', ')}
                                        </div>
                                    )
                                    :
                                    <div className='pink'>
                                        Aucune empreinte trouvé pour la date et heure mentionnée
                                    </div>
                                }
                            </div>
                        }
                        {site && site.id && site.pointeuse == 1 && dateVisite && (!interim || (interim && employe && employe.id)) && showVigilance && vigilances.length > 0 &&
                            <DualContainer>
                                <InputText
                                    label="Début"
                                    disabled={true}
                                    value={start && moment(start).format("HH[h]mm")}
                                />
                                <InputText
                                    label="Fin"
                                    disabled={true}
                                    value={end ? moment(end).format("HH[h]mm") : ""}
                                />
                            </DualContainer>
                        }
                        {
                            showVigilance ?
                                <Textarea
                                    required
                                    label="Compte rendu"
                                    value={compteRendu}
                                    onChange={(value) => {setMotif(value)}}/>
                            :
                                <LoadingPage/>
                        }
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                        }
                        <ButtonSubmit disabled={submitDisabled}/>
                    </form>
                </div>
            }
        </div>
    )
}