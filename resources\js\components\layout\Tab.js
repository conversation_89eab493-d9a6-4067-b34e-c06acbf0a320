import React, { useEffect, useState } from 'react'

import PieceJointe from '../piecejointe/PieceJointe'
import LastSanction from '../sanction/LastSanction'
import Tracabilite from '../story/Tracabilite'
import Complement from '../employe/item/Complement'
import ArticleEquipement from '../equipement/article/ArticleEquipement'
import DroitConge from '../absence/DroitConge'
import Pointage from '../employe/tab/Pointage'
import Salaire from '../paie/Salaire'
import SuiviJuridiqueTab from '../suivi_juridique/SuiviJuridiqueTab'
import FaitMarquantTab from '../juridique/fait/FaitMarquantTab'
import Hierarchie from '../employe/tab/hierarchie/Hierarchie'
import EmployeSite from '../site/tab/EmployeSite'
import PointageSite from '../site/tab/PointageSite'
import CriterePartVariable from '../employe/tab/critere/CriterePartVariable'
import CritereMensuelTab from '../partvariable/mensuel/CritereMensuelTab'
import PointagePlanning from '../planning/PointagePlanning'
import ItemTab from '../approvisionnement/item/ItemTab'
import LigneEquipement from '../dotation/article/LigneEquipement'
import DotationTab from '../dotation/tab/DotationTab'
import "./tab.css"
import MouvementTab from '../Stock/tab/MouvementTab'
import LastPlanning from '../planning_non_fait/LastPlanning'
import Empreinte from '../employe/tab/Empreinte'
import SanctionEmploye from '../employe/tab/SanctionEmploye'
import ComplementDoc from '../recrutement/ComplementDoc'
import Emplacement from '../recrutement/Emplacement'
import Formation from '../recrutement/Formation'
import Alarm from '../site/tab/Alarm'
import HoraireSite from '../site/tab/HoraireSite'
import SiteEnfant from '../site/tab/SiteEnfant'

export default function Tab({ auth, name, value, data, updateData, currentNature, setCurrentNature, defautUsers }) {
    const [activeMenu, setActiveMenu] = useState("")
    useEffect(() => {
        setActiveMenu(
            (name == 'avance_id') ? 'tracabilite'
            : (name == "planning_id") ? 'pointage_planning'
            : (name == "deduction_id") ? 'tracabilite'
            : (name == "paie_id") ? "salaire"
            // : (name == "reclamation_id") ? "pointage_reclamation" 
            : (name == "employe_id") ? "complement" 
            : (name == "recrutement_id") ? "complement_doc"
            : (name == "juridique_id") ? "suivi_juridique" 
            : (name == "plainte_id") ? "suivi_juridique" 
            : (name == "equipement_id") ? "article_equipement" 
            : (name == "sanction_id") ? "sanction" 
            : (name == "approvisionnement_id") ? "item_da"
            : (name == "absence_id" && data.type_absence != "mis_a_pied") ? "droit_conge" 
            : (name == "part_variable_id") ? "critere_mensuel"
            : (name == 'service24_id') ? 'tracabilite'
            : (name == "site_id") ? ((data.isAlarm || (data.group_planning_id && data.group_planning_id != data.id))? "alarm" : "horaire_site")
            : (name == "dotation_id") ? "article"
            : (name == "stock_id") ? "mouvement"
            : (name == "last_planning") ? "last_planning"
            : "tracabilite")
    }, []);

    return <div className='tab-container'>
        <div className='tab-list'>
            {/* {
                name == "reclamation_id" &&
                <div onClick={() => setActiveMenu('pointage_reclamation')} 
                    className={activeMenu == 'pointage_reclamation' ? 'active' : ''}>
                    Pointage
                </div>
            } */}
            {
                ["juridique_id", "plainte_id"].includes(name) &&
                <div onClick={() => setActiveMenu('suivi_juridique')} 
                    className={activeMenu == 'suivi_juridique' ? 'active' : ''}>
                    Suivis
                </div>
            }
            {/*
                (name == "juridique_id" && data.fait_id) &&
                <div onClick={() => setActiveMenu('fait_marquant')} className={activeMenu == 'fait_marquant' ? 'active' : ''}>
                    Fait marquant
                </div>
            */}
            {
                name == "employe_id" &&
                <div onClick={() => setActiveMenu('complement')} className={activeMenu == 'complement' ? 'active' : ''}>
                    Complément
                </div>
            }
            {
                name == "recrutement_id" &&
                <div onClick={() => setActiveMenu('complement_doc')} className={activeMenu == 'complement_doc' ? 'active' : ''}>
                    Complément
                </div>
            }
            {
                name == "recrutement_id" &&
                <div onClick={() => setActiveMenu('emplacement')} className={activeMenu == 'emplacement' ? 'active' : ''}>
                    Emplacement
                </div>
            }
            {
                name == "recrutement_id" &&
                <div onClick={() => setActiveMenu('formation')} className={activeMenu == 'formation' ? 'active' : ''}>
                    Formation
                </div>
            }
            {/*
                name == "recrutement_id" &&
                <div onClick={() => setActiveMenu('manque')} className={activeMenu == 'manque' ? 'active' : ''}>
                    Manque
                </div>
            */}
            {
                (name == "site_id") &&
                !(data.group_planning_id && data.group_planning_id != data.id) &&
                <div onClick={() => setActiveMenu('horaire_site')} className={activeMenu == 'horaire_site' ? 'active' : ''}>
                    Horaire
                </div>
            }
            {
                (name == "site_id") &&
                (data.group_planning_id && data.group_planning_id == data.id) &&
                <div onClick={() => setActiveMenu('child_site')} className={activeMenu == 'child_site' ? 'active' : ''}>
                    Site groupé
                </div>
            }
            {
                (name == "site_id") &&
                <div onClick={() => setActiveMenu('agent')} className={activeMenu == 'agent' ? 'active' : ''}>
                    Employé
                </div>
            }
            {
                (name == "site_id") &&
                <div onClick={() => setActiveMenu('pointage_site')} className={activeMenu == 'pointage_site' ? 'active' : ''}>
                    Pointage
                </div>
            }
            {
                (name == "site_id") &&
                <div onClick={() => setActiveMenu('alarm')} className={activeMenu == 'alarm' ? 'active' : ''}>
                    Alarme
                </div>
            }
            {
                (name == "employe_id" && !data.sal_forfait) &&
                <div onClick={() => setActiveMenu('pointage')} className={activeMenu == 'pointage' ? 'active' : ''}>
                    Pointage
                </div>
            }
            {
                (name == "employe_id") &&
                <div onClick={() => setActiveMenu('empreinte')} className={activeMenu == 'empreinte' ? 'active' : ''}>
                    Empreinte
                </div>
            }
            {
                name == "employe_id" &&
                <div onClick={() => setActiveMenu('dotation')} className={activeMenu == 'dotation' ? 'active' : ''}>
                    Dotation
                </div>
            }
            {
                name == "employe_id" &&
                <div onClick={() => setActiveMenu('sanction_employe')} className={activeMenu == 'sanction_employe' ? 'active' : ''}>
                    Sanction
                </div>
            }
            {
                (
                    name == "employe_id" && data.part_variable == 1 &&
                    (['validateur', 'resp_rh'].includes(auth.role) ||
                        ((auth.employe_id == data.resp_part_id || auth.employe_id == data.resp_resp_id) && auth.employe_id))
                ) &&
                <div onClick={() => setActiveMenu('critere_part')} className={activeMenu == 'critere_part' ? 'active' : ''}>
                    Part variable
                </div>
            }
            {
                (name == "employe_id" && data.sal_forfait == 1) &&
                <div onClick={() => setActiveMenu('hierarchie')} className={activeMenu == 'hierarchie' ? 'active' : ''}>
                    Sous hiérarchie
                </div>
            }
            {
                name == "approvisionnement_id" &&
                <div onClick={() => setActiveMenu('item_da')} className={activeMenu == 'item_da' ? 'active' : ''}>
                    Article
                </div>
            }
            {
                name == "absence_id" && data.type_absence != "mis_a_pied" &&
                <div onClick={() => setActiveMenu('droit_conge')} className={activeMenu == 'droit_conge' ? 'active' : ''}>
                    Droit
                </div>
            }
            {
                name == "sanction_id" &&
                <div onClick={() => setActiveMenu('sanction')} className={activeMenu == 'sanction' ? 'active' : ''}>
                    Sanction
                </div>
            }
            {
                name == "equipement_id" &&
                <div onClick={() => setActiveMenu('article_equipement')} className={activeMenu == 'article_equipement' ? 'active' : ''}>
                    Article
                </div>
            }
            {
                name == "equipement_id" &&
                <div onClick={() => setActiveMenu('dotation')} className={activeMenu == 'dotation' ? 'active' : ''}>
                    Dotation
                </div>
            }
            {
                name == "planning_id" &&
                <div onClick={() => setActiveMenu('pointage_planning')} className={activeMenu == 'pointage_planning' ? 'active' : ''}>
                    Planning
                </div>
            }
            {
                name == "paie_id" &&
                <>
                    <div onClick={() => setActiveMenu('salaire')} className={activeMenu == 'salaire' ? 'active' : ''}>
                        Salaire
                    </div>
                    <div onClick={() => setActiveMenu('prime')} className={activeMenu == 'prime' ? 'active' : ''}>
                        Prime
                    </div>
                    <div onClick={() => setActiveMenu('deduction')} className={activeMenu == 'deduction' ? 'active' : ''}>
                        Déduction
                    </div>
                    <div onClick={() => setActiveMenu('avance')} className={activeMenu == 'avance' ? 'active' : ''}>
                        Avance
                    </div>
                    <div onClick={() => setActiveMenu('reclamation')} className={activeMenu == 'reclamation' ? 'active' : ''}>
                        Réclamation
                    </div>
                </>
            }
            {
                name == "dotation_id" && 
                <>
                    <div onClick={() => setActiveMenu('article')} className={activeMenu == 'article' ? 'active' : ''}>
                        Article
                    </div>
                </>
            }
            {
                name == "stock_id" &&
                <>
                    <div onClick={() => setActiveMenu('mouvement')} className={activeMenu == 'mouvement' ? 'active' : ''}>
                        Mouvement
                    </div>
                </>
            }
            {
                name == "last_planning" &&
                <>
                    <div onClick={() => setActiveMenu('last_planning')} className={activeMenu == 'last_planning' ? 'active' : ''}>
                        Planning
                    </div>
                </>
            }
            {
                name == "part_variable_id" &&
                <div onClick={() => setActiveMenu('critere_mensuel')} className={activeMenu == 'critere_mensuel' ? 'active' : ''}>
                    Critère
                </div>
            }
            <div onClick={() => setActiveMenu('tracabilite')} className={activeMenu == 'tracabilite' ? 'active' : ''}>
                Traçabilité
            </div>
            <div onClick={() => setActiveMenu('pj')} className={activeMenu == 'pj' ? 'active' : ''}>
                PJ
            </div>
        </div>
        <div className='tab-content'>
            {/* {
                (activeMenu == "pointage_reclamation") &&
                <PointageTab reclamation={data} auth={auth} updateData={updateData}/>
            } */}
            {
                (activeMenu == "agent") &&
                <EmployeSite site={data} value={value} auth={auth} updateData={updateData}/>
            }
            {
                (activeMenu == "pointage_site") &&
                <PointageSite site={data} value={value} auth={auth} updateData={updateData}/>
            }
            {
                (activeMenu == "suivi_juridique") &&
                <SuiviJuridiqueTab name={name} id={value}/>
            }
            {
                (activeMenu == "fait_marquant") &&
                <FaitMarquantTab id={data.fait_id}/>
            }
            {
                (activeMenu == "salaire" || activeMenu == "prime" || activeMenu == "deduction" || activeMenu == "avance" || activeMenu == "reclamation") &&
                <Salaire id={value} updateData={updateData} auth={auth} type={activeMenu}/>
            }
            {
                (activeMenu == "item_da") &&
                <ItemTab approId={value} auth={auth} updateData={updateData}/>
            }
            {
                (activeMenu == "droit_conge") && data.type_absence != "mis_a_pied" &&
                <DroitConge id={value}/>
            }
            {
                activeMenu == "article_equipement" &&
                <ArticleEquipement auth={auth} id={value} updateData={updateData}/>
            }
            {
                activeMenu == "article" && 
                <LigneEquipement auth={auth} value={value} updateData={updateData}/>
            }
            {
                activeMenu == "sanction" && 
                <LastSanction id={value}/>
            }
            {
                activeMenu == "sanction_employe" &&
                <SanctionEmploye id={value}/>
            }
            {
                activeMenu == "complement" &&
                <Complement id={value} name={name} auth={auth} currentNature={currentNature} setCurrentNature={setCurrentNature} updateData={updateData} />
            }
            {
                activeMenu == "complement_doc" &&
                <ComplementDoc id={value} name={name} auth={auth} currentNature={currentNature} setCurrentNature={setCurrentNature} updateData={updateData} />
            }
            {
                activeMenu == "emplacement" &&
                <Emplacement data={data} />
            }
            {
                activeMenu == "formation" &&
                <Formation data={data} />
            }
            {/*
                activeMenu == "manque" &&
                <Manque data={data}/>
            */}
            {
                activeMenu == "pj" &&
                <PieceJointe name={name} value={value} updateData={updateData} />
            }
            {
                activeMenu == "tracabilite" &&
                <Tracabilite auth={auth} name={name} value={value} updateData={updateData} defautUsers={defautUsers} />
            }
            {
                activeMenu == "pointage" &&
                <Pointage auth={auth} name={name} value={value} updateData={updateData} />
            }
            {
                activeMenu == "empreinte" &&
                <Empreinte auth={auth} name={name} value={value} updateData={updateData} defautUsers={defautUsers} />
            }
            {
                activeMenu == "dotation" &&
                <DotationTab auth={auth} name={name} value={value} updateData={updateData} />
            }
            {
                activeMenu == "mouvement" &&
                <MouvementTab auth={auth} name={name} value={value} updateData={updateData} />
            }
            {
                activeMenu == "hierarchie" &&
                <Hierarchie auth={auth} name={name} value={value} updateData={updateData} />
            }
            {
                activeMenu == "critere_part" &&
                <CriterePartVariable auth={auth} name={name} value={value} updateData={updateData} />
            }
            {
                activeMenu == "critere_mensuel" &&
                <CritereMensuelTab auth={auth} name={name} value={value} partVariable={data} updateData={updateData} />
            }
            {
                activeMenu == "pointage_planning" &&
                <PointagePlanning auth={auth} name={name} value={value} pointages={data.pointages} planning={data.planning} />
            }
            {
                activeMenu == "last_planning" &&
                <LastPlanning auth={auth} name={name} value={value} planning={data.planning} lastPlannings={data} />
            }
            {
                activeMenu == "alarm" &&
                <Alarm auth={auth} name={name} value={value} site={data} updateData={updateData} />
            }
            {
                activeMenu == "horaire_site" &&
                <HoraireSite auth={auth} name={name} value={value} site={data} updateData={updateData} />
            }
            {
                activeMenu == "child_site" &&
                <SiteEnfant auth={auth} name={name} value={value} site={data} updateData={updateData} />
            }
        </div>
    </div>
}