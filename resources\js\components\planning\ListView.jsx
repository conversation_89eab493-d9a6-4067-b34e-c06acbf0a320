import moment from 'moment'
import React, { useState } from 'react'
import { HiOutlineUserAdd } from 'react-icons/hi'
import { IoMdClose } from 'react-icons/io'
import { MdContentCopy, MdModeComment, MdOutlineModeComment } from 'react-icons/md'
import CommentModal from './CommentModal'
import CalendarView from './CalendarView'
import { GrTrash } from 'react-icons/gr'
import ReplaceAgent from './ReplaceAgent'
import { LiaUserEditSolid } from 'react-icons/lia'

export default function ListView({ plannings, 
    handleDeleteEmploye, 
    setShowEmploye, 
    setCurrentPlId, 
    handleAddComment, 
    setCurrentDatePtg, 
    contrat, 
    toDuplicate,
    datePlanning,
    addMultipleCase,
    setAddMultipleCase,
    currentUserToAdd,
    setCurrentUserToAdd,
    showMessage,
    site,
    handleCountHours,
    handleToggleSubmit,
    horaires
}) {

    const [currentPlanning, setCurrentPlanning] = useState([])
    const [showComment, setShowComment] = useState(false)
    const [currentEmployeToCopy, setCurrentEmployeToCopy] = useState([]) 
    const [showReplaceAgentModal, setShowReplaceAgentModal] = useState(false)

    const getEffectif = (planning) => {
        const dayIndex = moment(planning.service).isoWeekday() - 1
        const isDay = moment(planning.service).format("HH") == "06"
        const effectif = planning.employes.length
        const quota = isDay ? horaires.day[dayIndex] : horaires.night[dayIndex]
        if (effectif > quota) {
            return ({status: 'surplus',content:'Surplus :' + (effectif - quota)})
        }
        else if (effectif < quota) {
            return ({status: 'lack',content:'Manque :' + (quota - effectif)})
        }
        return ({status: 'normal',content:''})
    }

    const isContinuosService = (planning, employe) =>{
        // const nextPlanning = plannings.find(p => moment(p.service).format('YYYY-MM-DD HH:mm:ss') == moment(planning.service).add(12, 'hours').format('YYYY-MM-DD HH:mm:ss'))
        const prevPlanning = plannings.find(p => moment(p.service).format('YYYY-MM-DD HH:mm:ss') == moment(planning.service).subtract(12, 'hours').format('YYYY-MM-DD HH:mm:ss'))
        if(prevPlanning?.employes?.some(e => e.id == employe.id))
            return true
        return false
    }
    const handleDeleteAll = (pl) =>{
        pl.employes.map(emp => {
            handleDeleteEmploye(emp, pl.id)
        })
    }

    return (
        <div>
            {
                showComment &&
                <CommentModal planning={currentPlanning} handleAddComment={handleAddComment} closeModal={() => { setShowComment(false) }} />
            }
            {
                plannings?.map((planning, index) =>
                <div className="card-container" key={index}>
                    <div>
                        <div className='space-between'>
                            <span>
                                {
                                    (planning.nom).toUpperCase() + " " + planning.day + " " + (moment(planning.service).format('HH:mm:ss') == "06:00:00" ? " JOUR" : " NUIT")
                                }
                            </span>
                            <span >
                                <span className='space-between icon-list'>
                                    <span className={'effectif ' + ((getEffectif(planning)).status == 'surplus' ? ' color-surplus' : (getEffectif(planning)).status == 'lack' ? ' color-lack' : '')}>
                                        {(getEffectif(planning)).content}
                                    </span>
                                    {   moment().isBefore(moment(planning.service).add(12, 'hours')) &&
                                        <>
                                            <span  onClick={() => { setShowComment(true), setCurrentPlanning(planning) }} title="Commentaire">
                                                {planning.comment.comment_content?.trim() == "" ?
                                                    <MdOutlineModeComment size={20}/>
                                                    :
                                                    <MdModeComment size={20} color='#073570'/>
                                                }
                                            </span>
                                        </>
                                    }
                                    {   moment().isBefore(moment(planning.service)) && 
                                        <>
                                            <span title="Ajouter un agent">
                                                <HiOutlineUserAdd size={20} onClick={() => { setCurrentDatePtg(moment(planning.service).format('YYYY-MM-DD HH:mm:ss')), setShowEmploye(true), setCurrentPlId(planning.id) }} />
                                            </span>
                                        </>
                                    }
                                    { planning.employes.length > 0 && 
                                        <span title="Copier tous">
                                            <MdContentCopy size={20} onClick={() => {setCurrentEmployeToCopy(planning.employes)}} />
                                        </span>
                                    }
                                    {   moment().isBefore(moment(planning.service)) && 
                                        <>
                                            {planning.employes.length > 0 &&
                                                <span title="Supprimer tous">
                                                    <GrTrash size={20} onClick={ () => handleDeleteAll(planning)}/>
                                                </span>
                                            }
                                        </>
                                    }
                                </span>
                            </span>
                        </div>
                        {
                            planning.employes?.length > 0 &&
                            <div style={{padding: '20px 10px'}}>
                                {
                                    planning.employes.map((employe, index) =>
                                        <div className='space-between secondary' key={ index}>
                                            <span className={'name-employe ' + (employe.soft_delete == 1 ? "color-orange" : "")} 
                                                title={employe.soft_delete == 1 ? "Agent en archive": ""} 
                                                style={window.innerWidth <= 400 ? { width: '80%' } : null}
                                            >
                                                <span style={{width: '85%'}}>{"[" + employe.matricule + "] " + employe?.nom }</span>
                                                {isContinuosService(planning, employe) && <span className='badge-outline badge-outline-24' style={{marginLeft:20}}>24</span>}
                                            </span>
                                            <span className='icon-list'>
                                                {moment(planning.service).isAfter(moment()) &&
                                                    <span title="Remplacer agent">
                                                        <LiaUserEditSolid size={19} onClick={() => {setCurrentEmployeToCopy({ ...employe, planning_id: planning.id }), setShowReplaceAgentModal(true)}} />
                                                    </span>
                                                }
                                                <span title="copier">
                                                    <MdContentCopy size={18} onClick={() => {setCurrentEmployeToCopy([employe])}} />
                                                </span>
                                                {moment(planning.service).isAfter(moment()) &&
                                                    <span title="Supprimer">
                                                        <IoMdClose size={18} onClick={() => { handleDeleteEmploye(employe, planning.id) }} />
                                                    </span>
                                                }
                                            </span>
                                        </div>
                                    )
                                }
                            </div>
                        }
                    </div>
                </div>)
            }
            {currentEmployeToCopy.length > 0 && (typeof currentEmployeToCopy == 'object') &&
                <div className='modal' >
                    <div style={{maxHeight: '96vh', width: 700}}>
                        <CalendarView plannings={plannings}
                            datePlanning={datePlanning}
                            addUser={setCurrentPlId}
                            setShowEmploye={setShowEmploye}
                            handleDeleteEmploye={handleDeleteEmploye}
                            handleAddComment={handleAddComment}
                            addMultipleCase={addMultipleCase}
                            setAddMultipleCase={setAddMultipleCase}
                            currentUserToAdd={currentUserToAdd}
                            setCurrentUserToAdd={setCurrentUserToAdd}
                            contrat={contrat}
                            showMessage={showMessage}
                            horaires={horaires}
                            site={site}
                            handleCountHours={handleCountHours}
                            setCurrentDatePtg={setCurrentDatePtg}
                            handleToggleSubmit={handleToggleSubmit}
                            currentEmployeToCopy={currentEmployeToCopy}
                        />
                        <div className="form-button-container">
                            <button type="button" onClick={() => setCurrentEmployeToCopy([])}>
                                Fermer
                            </button>
                        </div>
                    </div>
                </div>
            }
            {
                showReplaceAgentModal &&
                <ReplaceAgent plannings={plannings} currentAgent={currentEmployeToCopy} closeModal={() => setShowReplaceAgentModal(false)}/>
            }
        </div>
    )
}
