import { useEffect, useState } from 'react';
import {useLocation, useParams} from 'react-router-dom'
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import InputMultipleUser from '../input/InputMutipleUser';
import InputMultipleFile from '../input/InputMultipleFile';
import LoadingScreen from '../loading/LoadingScreen';
import matricule from '../util/matricule';
import ModelModal from './ModelModal';
import moment from 'moment';
import parse from 'html-react-parser';
import "./message.css"
import Textarea from '../input/Textarea';
import { removeChevron } from '../util/stringUtil';

export default function EditMessage({ auth, title}) {
    const params = useParams()
    const [pjFiles, setPjFiles] = useState([])
    const [showLoading, toggleLoading] = useState(null)
    const [users, setUsers] = useState([])
    const [copyUsers, setCopyUsers] = useState([])
    const [objet, setObjet] = useState("")
    const [content, setContent] = useState("")
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    const [reply, setReply] = useState(false)
    const [disableObjet, setDisableObjet] = useState(false)
    const [modelId, setModelId] = useState('')
    const [showModel, toggleModel] = useState(false)
    const locationSearch = useLocation().search
    const [realMessageId, setRealMessageId] = useState(null)
    const [clearButton, toggleClearButton] = useState(false)
    const [messages, setMessages] = useState([])
    const [note, setNote] = useState(null)
    const paramsSearch = new URLSearchParams(locationSearch)
    const isSent = (new URLSearchParams(locationSearch)).get('sent')
    const isUnread = (new URLSearchParams(locationSearch)).get('unread')
    const lastParams = paramsSearch.get('last_params') ? decodeURIComponent(paramsSearch.get('last_params')) : null;

    const replaceChevrons = (text) => {
        return text.replace(/</g, '&lt;').replace(/>/g, '&gt;');
    }

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        let data = new FormData()
        if(objet)
            data.append("objet", objet)
        if(content){
            let currentContent = replaceChevrons(content)
            data.append("content", currentContent.split('\n').map(t => "<p>" + t + "</p>").join(""))
        }
        pjFiles.forEach((file, index) => {
            data.append(`files[${index}]`, file);
        })
        users.forEach((u, index) => {
            data.append(`receivers[${index}]`, u.id);
        })
        copyUsers.forEach((u, index) => {
            data.append(`copies[${index}]`, u.id);
        })
        if (reply) {
            data.append('reply', reply ? 1 : null);
            data.append('message_id', realMessageId)
        }
        const searchParams = new URLSearchParams(locationSearch)
        const paramsObject = {};
        for (const [name, value] of searchParams) {
            paramsObject[name] = value;
        }
        const lengthParams = Object.keys(paramsObject).length;
        if (lengthParams > 0) {
            const firstKey = Object.keys(paramsObject)[0];
            data.append(firstKey, paramsObject[firstKey])
        }

        axios.post("/api/message/add", data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setNotification({success: "Une erreur est survenue."})
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    const getRealMessageId = (dataMessage) => {
        const searchParams = new URLSearchParams(locationSearch)
        let sent = searchParams.get('sent')
        let unread = searchParams.get('unread')
        const currentMessageId = ((sent && unread) || !sent) ? dataMessage.message_id : params.id
        setRealMessageId(currentMessageId)
    }
    
    const getMessage = () => {
        let isMounted = true;
        const searchParams = new URLSearchParams(locationSearch)
        axios.get('/api/message/show/' + params.id + '?' + searchParams, useToken()).then((res) => {
            if(isMounted){
                setMessages(res.data.messages)
                setNote(res.data.note)
                const data = res.data;
                getRealMessageId(data.note)
                let lastObjet = data.note.objet
                const firstThreeChars = lastObjet.substring(0, 4).trim().toLowerCase()
                if (firstThreeChars == 're :') 
                    setObjet(data.note.objet)
                else
                    setObjet('Re : ' + data.note.objet)        
                setReply(true)
                setDisableObjet(true)
                const isReplyAll = ((new URLSearchParams(locationSearch)).get("reply-all")) ? true : false
                const cpUsers = [];
                const destinataires = [];
                if (auth.id != data.note.user_id) {
                    if (data.note.follow)
                        destinataires.push({ id: data.note.user_id, address: data.note.email, name: data.note.name })
                    else
                        cpUsers.push({ id: data.note.user_id, address: data.note.email, name: data.note.name })
                }
                else if (data.note.to.length == 1) {
                    const user = data.note.to[0];
                    destinataires.push({ id: user.user_id, address: user.email, name: user.name })
                }

                setUsers([])
                if ((data.copies).length > 0 && isReplyAll) {
                    (data.copies).forEach(cp => {
                        if (cp.follow) {
                            const existingIdIndex = destinataires.find(dest => dest.address === cp.user_email);
                            if (!existingIdIndex) {
                                destinataires.push({ id: cp.user_id, address: cp.user_email, name: cp.user_name });
                            }
                        }
                        else if (isReplyAll) {
                            const existingIdIndexCp = cpUsers.find(cpu => cpu.address === cp.user_email);
                            if (!existingIdIndexCp) {
                                cpUsers.push({id: cp.user_id, address: cp.user_email, name: cp.user_name})
                            }
                        }
                    });
                }
                if (destinataires.length < 1 && cpUsers.length == 1) {
                    setUsers(cpUsers)
                }
                else {
                    setUsers(destinataires)
                    setCopyUsers(cpUsers)
                }
            }
        })
    }

    const getFaitMarquant = () => {
        const fait = (new URLSearchParams(locationSearch).get('fait_marquant_id'));
        if (fait) {
            axios.get('/api/fait_marquant/show/' + fait, useToken())
            .then((res) => {
                setObjet(res.data.site);
                setDisableObjet(true)
                if (res.data.user_id != auth.id) {
                    setUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
                }
            })
        }
    }
    const getDefaultMessage = () => {
        const searchParams = new URLSearchParams(locationSearch)
        if (searchParams.get('approvisionnement_id')) {
            axios.get('/api/approvisionnement/show/' + searchParams.get('approvisionnement_id'), useToken())
            .then((res) => {
                setObjet('DA : ' + res.data.objet);
                setDisableObjet(true)
                if (res.data.user_id != auth.id)
                    setUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
            })
        }
        else if (searchParams.get('absence_id')){
            axios.get('/api/message/get_absence/' + searchParams.get('absence_id'), useToken())
            .then((res) => {
                const absence = res.data
                let employe = matricule(absence) + absence.employe ;
                setObjet(absence.type + ' : ' + employe);
                setDisableObjet(true)
                const destinataire = []
                if (res.data.type_name == 'mis_a_pied') {
                    destinataire.unshift({ id: res.data.superviseur_id, address: res.data.sup_email, name: res.data.sup_nom })
                }
                if (auth.id != res.data.user_id)
                    destinataire.unshift({ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom })
                setUsers(destinataire)
            })
        }
        else if (searchParams.get('avance_id')) {
            axios.get('/api/avance/show/' + searchParams.get('avance_id'), useToken())
            .then((res) => { 
                const avance = res.data
                let employe = matricule(avance) + avance.employe;
                setObjet("Avance : " + employe);
                setDisableObjet(true)
                if (res.data.user_id != auth.id)
                    setUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
            })
        }
        else if (searchParams.get('sanction_id')) {
            axios.get('/api/sanction/show/' + searchParams.get('sanction_id'), useToken())
            .then((res) => { 
                const sanction = res.data
                let employe = matricule(sanction) + sanction.employe;
                setObjet("Sanction : " + employe);
                setDisableObjet(true)
                if (res.data.user_id != auth.id)
                    setUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
            })
        }
        else if (searchParams.get('prime_id')) {
            axios.get('/api/prime/show/' + searchParams.get('prime_id'), useToken())
            .then((res) => {
                const prime = res.data
                let employe = matricule(prime) + prime.employe;
                setObjet("Prime : " + employe);
                setDisableObjet(true)
                if (res.data.user_id != auth.id)
                    setUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
            })
        }
        else if (searchParams.get('sav_id')) {
            axios.get('/api/sav/show/' + searchParams.get('sav_id'), useToken())
            .then((res) => {
                const sav = res.data
                setObjet("SAV : " + sav.site)
                setDisableObjet(true)
                if (res.data.user_id != auth.id)
                    setUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
            })
        }
        else if (searchParams.get('flotte_id')) {
            axios.get('/api/flotte/show/' + searchParams.get('flotte_id'), useToken())
            .then((res) => { 
                const flotte = res.data
                setObjet("Flotte : " + flotte.site)
                setDisableObjet(true)
                if (res.data.user_id != auth.id)
                    setUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
            })
        }
        else if (searchParams.get('juridique_id')) {
            axios.get('/api/juridique/show/' + searchParams.get('juridique_id'), useToken())
            .then((res) => { 
                const juridique = res.data
                if (juridique.recouvrement)
                    setObjet("Recouvrement : " + juridique.debiteur)
                else
                    setObjet("Plainte : " + juridique.site)
                setDisableObjet(true)
                if (res.data.user_id != auth.id)
                    setUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
            })
        }
        else if (searchParams.get('reclamation_id')) {
            axios.get('/api/reclamation/show/' + searchParams.get('reclamation_id'), useToken())
            .then((res) =>{
                const reclamation = res.data
                let employe = matricule(reclamation) + reclamation.employe;
                setObjet("Réclamation : " + employe)
                setDisableObjet(true)
                if (res.data.user_id != auth.id)
                    setUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
            })
        }
        else if (searchParams.get('part_variable_id')) {
            axios.get('/api/part_variable/show/' + searchParams.get('part_variable_id'), useToken())
            .then((res) =>{
                const pv = res.data
                let employe = matricule(pv) + pv.employe;
                setObjet("Part Variable : " + employe)
                setDisableObjet(true)
                if (res.data.user_id != auth.id)
                    setUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
            })
        }       
        else if (searchParams.get('equipement_id')) {
            axios.get('/api/equipement/show/' + searchParams.get('equipement_id'), useToken())
            .then((res) =>{
                const eq = res.data
                setObjet("Equipement : " + eq.site)
                setDisableObjet(true)
                if (eq.user_id != auth.id)
                    setUsers([{ id: eq.user_id, address: eq.user_email, name: eq.user_nom }])
            })
        }       
        else if (searchParams.get('visite_poste_id')) {
            axios.get('/api/visite_poste/show/' + searchParams.get('visite_poste_id'), useToken())
            .then((res) =>{
                const visite = res.data
                setObjet("Visite de poste : " + visite.site)
                setDisableObjet(true)
                if (visite.user_id != auth.id)
                    setUsers([{ id: visite.user_id, address: visite.user_email, name: visite.user_nom }])
            })
        }
        else if (searchParams.get('deduction_id')) {
            axios.get('/api/deduction/show/' + searchParams.get('deduction_id'), useToken())
            .then((res) =>{
                const deduction = res.data
                let employe = matricule(deduction) + deduction.employe;
                setObjet("Déduction : " + employe)
                setDisableObjet(true)
                if (deduction.user_id != auth.id)
                    setUsers([{ id: deduction.user_id, address: deduction.user_email, name: deduction.user_nom }])
            })
        }
        else if (searchParams.get('satisfaction_id')) {
            axios.get('/api/satisfaction/show/' + searchParams.get('satisfaction_id'), useToken())
            .then((res) =>{
                const satisfaction = res.data.satisfaction
                setObjet("Satisfaction : " + satisfaction.site_nom)
                setDisableObjet(true)
                if (satisfaction.user_id != auth.id)
                    setUsers([{ id: satisfaction.user_id, address: satisfaction.user_email, name: satisfaction.user_nom }])
            })
        }
        else if (searchParams.get('employe_id')) {
            axios.get('/api/employe/show/' + searchParams.get('employe_id'), useToken())
            .then((res) =>{
                const employe = res.data
                const emp = matricule(employe) + " " + employe.nom
                setObjet("Employé : " + emp)
                setDisableObjet(true)
            })
        }
        else if (searchParams.get('paie_id')) {
            axios.get('/api/paie/show/' + searchParams.get('paie_id'), useToken())
            .then((res) => { 
                const paie = res.data
                const employe = matricule(paie) + " " + paie.employe
                setObjet("Paie : " + employe)
                setDisableObjet(true)
                if (paie.user_id != auth.id)
                    setUsers([{ id: paie.user_id, address: paie.user_email, name: paie.user_nom }])
            })
        }
        else if (searchParams.get('service24_id')) {
            axios.get('/api/service24/show/' + searchParams.get('service24_id'), useToken())
            .then((res) => { 
                const service = res.data
                const employe = matricule(service) + " " + service.employe
                setObjet("Service 24 : " + employe)
                setDisableObjet(true)
                if (service.user_id != auth.id)
                    setUsers([{ id: service.user_id, address: service.user_email, name: service.user_nom }])
            })
        }
    }

    const removeHtmlTags = (html) => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        return doc.body.textContent || "";
    };

    const getModel = () => {
        let isMounted = true
        axios.get('/api/model_message/' + modelId, useToken())
        .then((res) => {
            if (isMounted) {
                const data = res.data.model
                if (data) {
                    if (!objet.trim())
                        setObjet(data.objet)
                    
                    if (!content.trim())
                        setContent(removeHtmlTags(data.content))
                    
                    let userTmp = data.users
                    const toUsers = [...users]
                    const cpUsers = [...copyUsers]
                    if (userTmp.length > 0) {
                        userTmp.forEach(ur => {
                            const existingInUsers = toUsers.find(dest => dest.address === ur.user_email);
                            const existingInCopies = cpUsers.find(cp => cp.address === ur.user_email);
                            if (!existingInUsers && !existingInCopies) {
                                if (ur.follow) {
                                    toUsers.push({ id: ur.user_id, address:ur.user_email, name:ur.user_name })
                                }
                                else
                                    cpUsers.push({ id:ur.user_id, address:ur.user_email, name:ur.user_name })
                            }
                        });
                    }
                    setUsers(toUsers)
                    setCopyUsers(cpUsers)
                }
            }
        })
        return () => isMounted = false;
    }
    
    useEffect(() => {
        modelId && getModel();
    }, [modelId])
    
    useEffect(() => {
        params.id && getMessage();
        getFaitMarquant();
        getDefaultMessage();
        getModel();
    }, [])

    useEffect(() => {
        if (users.length > 0 || copyUsers.length > 0)
            toggleClearButton(true);
        else
            toggleClearButton(false);
    }, [users.length, copyUsers.length])

    return (
        <div id="content">
            {
                notification ? 
                    <Notification next={lastParams ? "/message?" + lastParams  : (notification.id ? "/message?id=" + notification.id : "/message")} message={notification.success}/>
                :
                <div>
                    <div className="title-container">
                        <h2>{title ? title : "Nouveau message"}</h2>
                    </div>
                    <div className='card-container' style={{ padding: '0px 10px'}} >
                        <div className='action-container' style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>
                            <span>
                                <InputMultipleFile files={pjFiles} setFiles={setPjFiles} hideFile />
                            </span>
                            <span onClick={() => toggleModel(true)}>Charger un model</span>
                            {clearButton &&
                                <span onClick={() => { setUsers([]), setCopyUsers([]) }}>Vider les destinataires</span>
                            }
                            
                        </div>
                    </div>
                    {
                        showModel &&
                        <ModelModal closeModal={() => toggleModel(false)} setModelId={setModelId} />
                    }
                    {
                        showLoading ?
                            <LoadingScreen/>
                        :
                            <>
                                <div>
                                    <form onSubmit={handleSubmit}>
                                        <InputMultipleUser required label="Destinataire" users={users} setUsers={setUsers} />
                                        <InputMultipleUser label="En copie" users={copyUsers} setUsers={setCopyUsers} />
                                        <InputText required label="Objet" value={objet} onChange={setObjet} disabled={ disableObjet } />
                                        {/*<InputEditor content={content} setContent={setContent}/>*/}
                                        <Textarea value={content} onChange={setContent} row="10"/>
                                        <InputMultipleFile files={pjFiles} setFiles={setPjFiles} hideInput/>
                                        {
                                            error &&
                                            <div className='container-error'>
                                                {error}
                                            </div>
                                        }
                                        <ButtonSubmit disabled={submitDisabled}/>
                                    </form>
                                </div>
                                {note &&
                                    <div className="card-container message-log">
                                        <div className='message-user'>
                                            <span>
                                                {(isSent && isUnread) ?
                                                    (note.sender_name + " <" + note.sender_email + ">") : note.name + " <" + note.email + ">"
                                                }
                                            </span>
                                            <span style={{ textAlign: "right", width: "130px", minWidth: "130px" }}>
                                                {moment(note.created_at).format("DD MMM YY à HH:mm")}
                                            </span>
                                        </div>
                                        <div className="message-content">
                                            {parse(note.content)}
                                        </div>
                                        {
                                            note.pieces.length > 0 &&
                                            <div className='message-pj'>
                                                {
                                                    note.pieces.map(pj => (
                                                        <span key={pj.id} className='pj-link'>
                                                            <a className='link-no-style' target="_blank" href={"/uploads/" + pj.path} key={pj.id}>
                                                                {pj.nature}
                                                            </a><span> </span>
                                                        </span>
                                                    ))
                                                }
                                            </div>
                                        }
                                    </div>
                                }
                                {
                                    messages.map(ms => (
                                        <div key={ms.id} className="card-container message-log">
                                            <div className='message-user'>
                                                <span>
                                                    {ms.name + " <" + ms.email + ">"}
                                                </span>
                                                <span>{moment(ms.created_at).format("DD MMM YY à HH:mm")}</span>
                                            </div>
                                            <div className="message-content">
                                                {parse(ms.content)}
                                            </div>
                                            {
                                                ms.pieces.length > 0 &&
                                                <div className='message-pj'>
                                                    {
                                                        ms.pieces.map(pj => (
                                                            <span key={pj.id} className='pj-link'>
                                                                <a className='link-no-style' target="_blank" href={"/uploads/" + pj.path} key={pj.id}>
                                                                    {pj.nature}
                                                                </a><span> </span>
                                                            </span>
                                                        ))
                                                    }
                                                </div>
                                            }
                                        </div>
                                    ))
                                }
                               
                            </>
                    }
                </div>
            }
        </div>
    )
}