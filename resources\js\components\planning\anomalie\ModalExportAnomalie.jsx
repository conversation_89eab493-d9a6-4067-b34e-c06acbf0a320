import React, { useEffect, useState } from 'react'
import InputDate from '../../input/InputDate';
import AnomalieExcel from './AnomalieExcel';
import LoadingPage from '../../loading/LoadingPage';
import { useLocation } from 'react-router-dom';
import axios from 'axios';
import moment from 'moment';
import useToken from '../../util/useToken';

export default function ModalExportAnomalie({ closeModal }) {
    const locationSearch = new URLSearchParams(useLocation().search);
    const [anomalies, setAnomalies] = useState([]);
    const [isLoading, toggleLoading] = useState(false);
    const [exportButton, toggleExportButton] = useState(false)
    const [numberData, setNumberData] = useState(0)
    const [dateAnomalie, setDateAnomalie] = useState(moment(locationSearch.get('created_at')).toDate());

    const onSearch = () => {
        let isMounted = true;
        toggleLoading(true)
        toggleExportButton(false)
        axios.get("/api/anomalie?created_at=" + moment(dateAnomalie).format('YYYY-MM-DD'), useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.error)
                        console.error(res.data.error)
                    else {
                        setAnomalies(res.data.anomalies)
                        setNumberData(res.data.anomalies.length)
                        toggleExportButton(true)
                    }
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e)
            })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if (dateAnomalie) onSearch()
    }, [dateAnomalie])

    return (
        <div className='modal'>
            <div>
                <h3>Export Anomalie</h3>
                <div style={{ marginBottom: 50 }}>
                    <InputDate label="Date" value={dateAnomalie} onChange={setDateAnomalie} required />
                </div>
                {isLoading ?
                    <LoadingPage />
                    :
                    <div >
                        {numberData + " élement(s) trouvée(s)"}
                    </div>
                }
                <div className="form-button-container">
                    {/* {searchButton && */}
                    {/* <button onClick={() => onSearch()} className='btn btn-primary' >Chercher</button> */}
                    {/* } */}
                    {exportButton && <AnomalieExcel anomalies={anomalies} dateAnomalie={dateAnomalie} />}
                    <button type="button" onClick={closeModal}>
                        Annuler
                    </button>
                </div>
            </div>
        </div>
    )
}
