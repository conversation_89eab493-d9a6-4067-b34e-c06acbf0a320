import { calculIrsa } from "./calculIrsa";
// import { prorata } from "./calculProrata";


export function calculPaie({ heureTravaille,
    heureContrat, salBase, charge,
    setCharge, preavis, primeState, resultat, setResultat, conge, heureSup, maj, setheureSup, nprv, rappel, deduction, setPreavis }) {
    const prorata = (value) => {
        let div_hcht = heureTravaille / heureContrat
        let diff_hcht = heureTravaille - heureContrat
        if (diff_hcht >= 0 || diff_hcht == -12) {
            return parseFloat(value);
        }
        else {
            return parseFloat(value * div_hcht);
        }
    }
    useEffect(() => {
        calcul();
    }, [heureTravaille,
        heureContrat, salBase, charge, preavis, primeState, resultat, setResultat, conge, heureSup, maj,  nprv, rappel, deduction ]);
    const calcul = () => {
        let diffHcHt = heureTravaille - heureContrat;
        let divSalBaseHc = salBase / heureContrat;
        let hsfr = 0;
        let newSalaireMensuel = 0
        let newHs30 = 0

        if (diffHcHt == -12) newSalaireMensuel = salBase
        else if (heureTravaille > heureContrat) {
            hsfr = diffHcHt
            newSalaireMensuel = salBase
        }
        else {
            newSalaireMensuel = ((salBase / heureContrat) * heureTravaille);
        }
        newHs30 = hsfr >= 33.6 ? 33.6 : hsfr
        const newMh_s30 = divSalBaseHc * (newHs30) * 0.3
        const newHs50 = hsfr - newHs30
        const newMh_s50 = divSalBaseHc * (newHs50) * 0.5
        const newMmaj_ferie = divSalBaseHc * maj.hFerie
        const newMmaj_dim = divSalBaseHc * maj.hmDim * 0.4
        const newMmaj = (maj.hMaj / heureContrat) * heureTravaille

        setheureSup((curr) => {
            return {
                ...curr,
                mh_s30: newMh_s30,
                hs50: newHs50,
                mh_s50: newMh_s50,
            }
        })

        let tot_maj = newMh_s30 + newMh_s50 + newMmaj_dim + newMmaj_ferie + newMmaj
        const newCongePayer = (salBase / 30) * conge.soldeConge;

        const newPrDeductible = (salBase / 30) * nprv.nprvPreavisDeductible;
        const newPreavisPayer = (salBase / 30) * nprv.nprvPreavisPayer;
        const newIdmLicenciement = (salBase / 30) * nprv.nprvLicenciement;

        const newTotalProrataGrap = prorata(primeState.primeExceptionnelle) + prorata(primeState.primeDiv)
            + prorata(primeState.idmDepl) + prorata(primeState.primeAssid) + prorata(primeState.primeResp)
            + prorata(primeState.primeEntret) + prorata(primeState.primeAnc);
        const newSalBrut = newSalaireMensuel + tot_maj + newTotalProrataGrap + rappel
            + newCongePayer - newPrDeductible + newPreavisPayer + newIdmLicenciement
        const newCnaps = newSalBrut / 100
        const newSalfa = newSalBrut / 100

        const netImposable = newSalBrut - newSalfa - newCnaps
        const newIrsa = calculIrsa(netImposable)
        const newNetAPayer = netImposable - newIrsa - deduction.retenueFormation - deduction.autreDeduction - deduction.avanceSpeciale -
            deduction.avance15 - preavisMoins + allFamCnaps + rembFraisFixe + avanceSpecialeEmbauche + primeState.prime
            + prorata(primeState.perdiem) + primeState.partVariable

        const newSalfaPatronale = (newNetAPayer + deduction.avance15) * 0.05;
        const newCnapsPatronale = (newNetAPayer + deduction.avance15) * 0.13;
        const newMasseSalariale = newNetAPayer + newCnapsPatronale + newSalfaPatronale + newSalfa + newCnaps + newIrsa + deduction.avance15 - 0
        setCharge(() => {
            return {
                ...charge,
                irsa: newIrsa,
                salfa: newSalfa,
                cnaps: newCnaps,
                salfaPatronale: newSalfaPatronale,
                cnapsPatronale: newCnapsPatronale,
            }
        })
        setResultat({
            ...resultat,
            salaireMensuel: newSalaireMensuel,
            salaireBrut: newSalBrut,
            totalProrataGrap: newTotalProrataGrap,
            netAPayer: newNetAPayer,
            masseSalariale: newMasseSalariale
        })
        setPreavis({
            ...preavis,
            preavisDeductible: newPrDeductible,
            preavisPayer: newPreavisPayer,
            idmLicenciement: newIdmLicenciement
        })
        setheureSup({
            ...heureSup,
            hs30: newHs30,
            hs50: newHs50,
            mh_s30: newMh_s30,
            mh_s50: newMh_s50,
        })
        setConge({ ...conge, payer: newCongePayer })

    }
}