import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';

export default function Recrutement({auth, recrutements, setRecrutements, currentId, setCurrentId}) {
        const locationSearch = useLocation().search;
        const [isLoading, toggleLoading] = useState(false)
        const [allDataLoaded, setDataLoaded] = useState(false)

        let searchItems = [
            {label: 'Id', name: 'id', type:'number'},
            {label: 'Actif', name: "actif", type:"number"},
            {label: 'Archive', name: "archive", type:"number"},
            {label: 'Nom', name: 'nom', type:'string'},
            {label: 'CIN', name: 'cin_text', type:'string'},
            {label: 'Status', name: 'status_recrutement', type:'select'},
            {label: 'Date de création', name: 'created_at', type:'date'},
        ]

        const updateData = (initial) => {
            let isMounted = true;
            const params = new URLSearchParams(locationSearch)
            if(initial){
                toggleLoading(true)
                setDataLoaded(true)
                params.set("offset", 0)
            }
            else
                params.set("offset", recrutements.length)
            axios.get('/api/recrutement' + '?' + params,  useToken())
            .then((res) => {
                if(isMounted) {
                    if(res.data.error)
                        console.error(res.data.error)
                    else {
                        if(initial)
                            setRecrutements(res.data.recrutements)
                        else {
                            const list = recrutements.slice().concat(res.data.recrutements)
                            setRecrutements(list)
                        }
                        setDataLoaded(res.data.recrutements.length < 30)
                    }
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e)
            })
            return () => { isMounted = false };
        }

        useEffect(() => {updateData(true)}, [locationSearch])

        const fetchMoreData = () => {
            setTimeout(() => {
              updateData()
            }, 300);
        };
      

    return <>{
        isLoading ?
            <LoadingPage/>
        :
            <div>
                <div className='padding-container space-between'>
                    <h2>
                        Recrutement
                    </h2>
                    <Link className='btn btn-primary' to="/recrutement/add">Nouveau</Link>
                </div>
                <SearchBar listItems={searchItems}/>
                {
                    recrutements.length == 0 ?
                        <h3 className='center secondary'>Aucun données trouvé</h3>
                    :
                        <div>
                            <InfiniteScroll
                                dataLength={recrutements.length}
                                next={fetchMoreData}
                                hasMore={!allDataLoaded}
                                loader={<LoadingPage/>}
                            >
                                <div className="line-container ">
                                    <div className='row-employe'>
                                        <b className='line-cell-xg'>Nom</b>
                                        <b className='line-cell-sm center'>Formation</b>
                                        <b>Crée par</b>
                                    </div>
                                </div>
                                {
                                    recrutements.map((r) => (
                                        <div className={`table line-container ${currentId && currentId == r.id ? 'selected' : ''}`} key={r.id}>
                                            <div className={"row-employe " + (r.soft_delete ? "brown" : r.status == "Recruté" && "cyan")} onClick={() => setCurrentId(r.id)}>
                                                <span className='line-cell-xg'>{r.nom}</span>
                                                <span className={`line-cell-sm center ${r.status !== "Recruté" ? (r.nb_manque_vigilance > 0 ? 'pink' : r.nb_pointage == 0 ? 'secondary' : '') : 'cyan'}`}>
                                                {
                                                    isNaN(r.nb_pointage - r.nb_manque_vigilance) || isNaN(r.nb_pointage) || r.nb_pointage === 0? 
                                                        '0/0'
                                                    : 
                                                        `${r.nb_pointage - r.nb_manque_vigilance}/${r.nb_pointage}`}
                                                </span>
                                                <span>
                                                    {r.recruteur ? `${r.recruteur}<${r.recruteur_email}>` : ''}
                                                </span>
                                            </div>
                                        </div>
                                    ))
                                }
                            </InfiniteScroll>
                        </div>
                }
            </div>
    }</>
}