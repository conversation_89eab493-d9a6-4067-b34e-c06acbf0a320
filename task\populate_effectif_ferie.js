const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const sqlSelectHoraireEffectif = "SELECT h.id, h.site_id, s.nom, h.day_0, h.day_1, h.day_2, h.day_3, h.day_4, h.day_5, h.day_6, h.day_ferie, " +
    " h.night_0, h.night_1, h.night_2, h.night_3, h.night_4, h.night_5, h.night_6, h.night_ferie " +
    "FROM horaire_effectifs h " +
    "LEFT JOIN sites s ON s.idsite = h.site_id " +
    "WHERE s.soft_delete is null or s.soft_delete = 0 "
const sqlUpdateHoraireFerie  = (horaire) => "UPDATE horaire_effectifs set " + horaire + "_ferie = ? WHERE id = ?"
    
function updateData(effectifs, index){
    if(index < effectifs.length){
        setTimeout(() => {
            const effectif = effectifs[index]
            pool.query(sqlUpdateHoraireFerie(effectif.horaire), [effectif.nb, effectif.id], async (err, r) => {
                if(err)
                    console.log(err)
                else
                    console.log("update " + effectif.horaire + "_ferie, id: " + effectif.id)
                    updateData(effectifs, index+1)
            })
        }, 100)
    }
    else {
        console.log("update effectif done!")
        process.exit(1)
    }
}
function onlyUnique(value, index, array) {
    return array.indexOf(value) === index;
}

pool.query(sqlSelectHoraireEffectif, [], async (err, horaires) => {
    if(err)
        console.error(err)
    else {
        const effectifMultiple = []
        const effectifUnique = []
        horaires.forEach(h => {
            const horaireDayFilter = [h.day_0, h.day_1, h.day_2, h.day_3, h.day_4, h.day_5, h.day_6].filter(onlyUnique)
            if(horaireDayFilter.length == 1)
                effectifUnique.push({'id' : h.id, 'horaire': 'day', 'nb' : horaireDayFilter[0]});
            else
                effectifMultiple.push({'id' : h.id, 'site': h.nom, 'horaire': horaireDayFilter})

            const horaireNightFilter = [h.night_0, h.night_1, h.night_2, h.night_3, h.night_4, h.night_5, h.night_6].filter(onlyUnique)
            if(horaireNightFilter.length == 1)
                effectifUnique.push({'id' : h.id, 'horaire': 'night', 'nb' : horaireNightFilter[0]});
            else
                effectifMultiple.push({'id' : h.id, 'site': h.nom, 'horaire': horaireNightFilter})
        });
        effectifMultiple.forEach(e => {
            console.log(e.site)
        })
        console.log(effectifUnique.filter(e => e.nb != 0).length)
        updateData(effectifUnique.filter(e => e.nb != 0), 0)
    }
})