import React from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";

const borderColor = "";
const styles = StyleSheet.create({
    container: {
        flexDirection: "row",
        // borderBottomColor: "#bff0fd",
        // backgroundColor: "#bff0fd",
        // borderBottomWidth: 1,
        borderTopWidth: 1,
        borderBottomWidth: 1,
        alignItems: "center",
        height: 25,
        textAlign: "center",
        fontStyle: "bold",
        flexGrow: 1,
    },
    rubrique: {
        width: "29%",
        borderRightColor: borderColor,
        borderRightWidth: 1,
        padding: 7,
        marginLeft: 2,
    },
    base: {
        width: "12%",
        borderRightColor: borderColor,
        borderRightWidth: 1,
        padding: 7,
    },
    taux: {
        width: "10%",
        borderRightColor: borderColor,
        borderRightWidth: 1,
        padding: 7,
    },
    deduire: {
        width: "12%",
        borderRightWidth: 1,
        paddingTop: 7,
        paddingBottom: 7,
    },
    apayer: {
        width: "15%",
        borderRightWidth: 1,
        padding: 7,
    },
    chargesPatronale: {
        width: "22%",
    },
    charges: {
        flexDirection: "row",
    },
    patronaleTaux: {
        width: "45%",
    },
    patronaleMontant: {
        width: "45%",
    },
});

const TableHeader = () => (
    <View style={styles.container}>
        <Text style={styles.rubrique}>RUBRIQUES</Text>
        <Text style={styles.base}>BASE</Text>
        <Text style={styles.taux}>TAUX</Text>
        <Text style={styles.deduire}>A DEDUIRE</Text>
        <Text style={styles.apayer}>A PAYER</Text>
        <View style={styles.chargesPatronale}>
            <View>
                <Text>CHARGES PATRONALES</Text>
            </View>
            <View style={styles.charges}>
                <Text style={styles.patronaleTaux}>Taux</Text>
                <Text style={styles.patronaleMontant}>Montant</Text>
            </View>
        </View>
    </View>
);

export default TableHeader;
