import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import NoteModal from "../input/NoteModal";
import DoneDeductionModal from "./DoneDeductionModal";

export default function ActionDeduction({ auth, deduction, updateData }) {
    const [showNoteModal, toggleNoteModal] = useState(false);
    const [currentAction, setAction] = useState(null);
    const [toDone, setToDone] = useState(false);
    const [showDoneModal, toggleDoneModal] = useState(false);

    const handleCancelDeduction = (id) => { 
        setAction({
            header: "Annuler la deduction",
            request: "/api/deduction/cancel_deduction/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    return (
        <div>
            {showNoteModal && (
                <NoteModal
                    action={currentAction}
                    updateData={() => updateData(true)}
                    closeModal={() => toggleNoteModal(false)}
                    toDone={toDone}
                    setToDone={setToDone}
                />
            )}
            {showDoneModal && 
                <DoneDeductionModal deduction={deduction}
                    updateData={() => updateData(true)}
                    closeModal={() => toggleDoneModal(false)}
                />
            }
            <div>
                {auth.role == "resp_rh" && (
                    <div className="action-container">
                        {["traite"].includes(deduction.status) && (
                            <span>
                                <Link to={"/deduction/edit/" + deduction.id}>
                                    Editer
                                </Link>
                            </span>
                        )}
                        {["demande"].includes(deduction.status) && (
                            <span>
                                <Link to={"/deduction/do_traite/" + deduction.id}>
                                    Traiter
                                </Link>
                            </span>
                        )}
                        {["traite", "demande"].includes(deduction.status) && (
                            <span onClick={() => toggleDoneModal(true)}>
                                Terminer
                            </span>
                        )}
                        {["draft"].includes(deduction.status) && (
                            <span>
                                <Link to={"/deduction/send_back/" + deduction.id}>Renvoyer</Link>
                            </span>
                        )}
                        {deduction.status != "done" && deduction.status != "draft" && 
                            <span onClick={() =>handleCancelDeduction(deduction.id)} >
                                Annuler
                            </span>    
                        }
                    </div>
                )}
            </div>
        </div>
    );
}
