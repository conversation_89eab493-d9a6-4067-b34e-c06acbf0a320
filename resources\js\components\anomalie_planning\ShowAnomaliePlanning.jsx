import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import LoadingPage from '../loading/LoadingPage'
import ShowHeader from '../view/ShowHeader'
import useToken from '../util/useToken'
import "../layout/tab.css"
import ActionAnomaliePlanning from './ActionAnomaliePlanning'
import matricule from '../util/matricule'
import AssignerAgent from './AssignerAgent'

export default function ShowAnomaliePlanning({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const [isLoading, toggleLoading] = useState(false)
    const [anomalies, setAnomalies] = useState()
    const [employe, setEmploye] = useState()
    const [agent, setAgent] = useState()
    const [showAssignModal, toggleAssignModal] = useState(false)
    const [currentIdPointage, setCurrentIdPointage] = useState()
    const params = useParams()
    const locationSearch = new URLSearchParams(useLocation().search); 

    const updateData = () => { 
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/anomalie_planning/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if (isMounted) {
                if(!res.data)
                    setCurrentId()
                else{
                    setAnomalies(res.data.anomalie)
                    setEmploye(res.data.employe)
                    setCurrentItem({
                        id: res.data.employe.id,
                        societe_id: res.data.employe.societe_id,
                        numero_stagiaire: res.data.employe.numero_stagiaire,
                        numero_employe: res.data.employe.numero_employe,
                        num_emp_soit: res.data.employe.num_emp_soit,
                        num_emp_saoi: res.data.employe.num_emp_saoi,
                        nb: res.data.anomalie.length,
                        nom: res.data.employe.nom,
                        site: res.data.employe.site,
                        resp: res.data.employe.resp,
                        resp_email: res.data.employe.resp_email
                    })
                    if (res.data.anomalie.length == 0){
                        setCurrentId()
                    }
                }
            }
            toggleLoading(false)
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
    }
    useEffect(() => {
        updateData()
    }, [currentId])

    return (
        <div>
            {
                isLoading ?
                    <LoadingPage />
                :
                    <>
                        {
                            anomalies &&
                                <div>
                                    <ShowHeader size={size} label="Anomalie planning" id={currentId} closeDetail={() => setCurrentId()} />
                                    <div className="card-container">

                                        <h3>
                                            <div>{matricule(employe) + ' ' + employe.nom}</div>
                                        </h3>
                                    
                                        {employe.idsite &&
                                            <p style={{ whiteSpace: "pre-line" }}>
                                                Site: <span className='text'>{employe.site  }</span><br/>
                                            </p>
                                        }

                                        <p style={{ whiteSpace: "pre-line" }}>
                                            Motif d'archivage: <span className='text'>{employe.observation}</span><br/>
                                        </p>
                                        <p style={{ whiteSpace: "pre-line" }}>
                                            Date de sortie: <span className='text'>{moment(employe.date_sortie).format('DD/MM/YYYY')}</span><br/>
                                        </p>
                                        <div className='card-action'>
                                            <ActionAnomaliePlanning anomalies={anomalies} auth={auth} employe={employe} updateData={updateData} />
                                        </div>
                                        {
                                            showAssignModal && 
                                            <AssignerAgent closeModal={() => toggleAssignModal(false)} id={currentIdPointage} agent={agent} setAgent={setAgent} updateData={updateData}/>
                                        }
                                        <div className="tab-container">
                                            <div className="tab-list">
                                                { anomalies.length > 0 &&
                                                    <div className='active'>Pointage</div>
                                                }
                                            </div>
                                            <div className="tab-content">
                                                {
                                                    anomalies.map((ano, index) => {
                                                        return (
                                                            <div key={index}>
                                                                <span className="anomalie-container">
                                                                    <span>
                                                                        <span>{moment(ano.date_pointage).format('DD/MM/YYYY')} - {moment(ano.date_pointage).format('HH') == '06' ? 'JOUR' : 'NUIT'}</span><br/>
                                                                        <span className='secondary'>{ano.site}</span><br/>
                                                                    </span>
                                                                    {['resp_op', 'resp_sup'].includes(auth.role) &&
                                                                        <div className='action-container'>
                                                                            <span onClick={() => {setCurrentIdPointage(ano.id), toggleAssignModal(true)}}>Remplacer</span>
                                                                        </div>
                                                                    }
                                                                </span>
                                                            </div>
                                                        )
                                                })}
                                            </div>
                                        </div>
                                    
                                    </div>
                                </div>
                        }
                    </>
            }
        </div>
    )
}
