import React, { useState, lazy } from 'react';
import { Link } from 'react-router-dom';
import NoteModal from '../input/NoteModal';

const EditPdf = lazy(() => import('./EditPdf'))

export default function ActionSanction({auth, sanction, updateData}) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [currentAction, setAction] = useState(null)
    const [showPrintModal, setShowPrintModal] = useState(false)

    const handleCancelConvocation = (id) => {
        setAction({
            header: "Annuler la convocation",
            request: "/api/sanction/cancel_convocation/" + id,
            required: true
        })
        toggleNoteModal(true)

    }
    const handleCancelSanction = (id) => {
        setAction({
            header: "Annuler la sanction",
            request: "/api/sanction/cancel_sanction/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleCancelValidation = (id) => {
        setAction({
            header: "Annuler la demande de validation",
            request: "/api/sanction/cancel_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleRequestValidation = (id) => {
        setAction({
            header: "Demande de validation",
            request: "/api/sanction/request_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleReplyValidation = (id) => {
        setAction({
            header: "Réponse à la demande",
            request: "/api/sanction/reply_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    
    const handleDone = (id) => {
        setAction({
            header: "Terminer le traitement de la sanction",
            request: "/api/sanction/save_done/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleConfirmSanction = (id) => {
        setAction({
            header: "Confirmé la demande",
            request: "/api/sanction/confirm_sanction/" + id,
            required: false
        })
        toggleNoteModal(true)
    }

    const handleCancelConfirmation = (id) => {
        setAction({
            header: "Annuler la demande",
            request: "/api/sanction/cancel_confirmation/" + id,
            required: false
        })
        toggleNoteModal(true)
    }

    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction} 
                updateData={() => updateData(true)} 
                closeModal={() => toggleNoteModal(false)}/> 
        }
        {
            showPrintModal &&
            <EditPdf 
                header="Impression sanction"
                sanction={sanction}
                setShowPrintModal={setShowPrintModal}/>
        }
        <div className='action-container'>
            {
                (["confirmation"].includes(sanction.status) && auth.role == "room") && 
                <span onClick={() => handleConfirmSanction(sanction.id)}>Confirmer</span>
            }
            {
                (["confirmation"].includes(sanction.status) && auth.role == "room") && 
                <span onClick={() => handleCancelConfirmation(sanction.id)}>Annuler</span>
            }
            {
                (["traite","done"].includes(sanction.status) && ["rh", "resp_rh"].includes(auth.role)) && 
                <span onClick={() => setShowPrintModal(true)}>Imprimer</span>
            }
            {
                (['demande', 'traite'].includes(sanction.status) && ["rh", "resp_rh"].includes(auth.role)) &&
                <span>
                    <Link to={"/sanction/do_convocation/" + sanction.id}>Convoquer</Link>
                </span>
            }
            {
                (["convocation", "traite"].includes(sanction.status) && ["rh", "resp_rh"].includes(auth.role)) && 
                <span onClick={() => handleDone(sanction.id)}>Terminer</span>
            }
            {
                (["convocation"].includes(sanction.status) && ["superviseur", "resp_sup", "resp_op", "rh", "resp_rh"].includes(auth.role)) &&
                <span>Imprimer</span>
            }
            {
                (['traite'].includes(sanction.status) && ["rh", "resp_rh"].includes(auth.role)) &&
                <span onClick={() => handleRequestValidation(sanction.id)}>Validation</span>
            }
            {
                (["convocation"].includes(sanction.status) && ["rh", "resp_rh"].includes(auth.role)) &&
                <span onClick={() => handleCancelConvocation(sanction.id)}>Annuler la convocation</span>
            }
            {
                (["demande"].includes(sanction.status) && ["rh", "resp_rh"].includes(auth.role)) && 
                <span>
                    <Link to={"/sanction/do_traite/" + sanction.id}>Traiter</Link>
                </span>
            }
            {
                (["traite"].includes(sanction.status) && ["rh", "resp_rh"].includes(auth.role)) && 
                <span>
                    <Link to={"/sanction/edit/" + sanction.id}>Editer</Link>
                </span>
            }
            {
                (["validation"].includes(sanction.status) && ["validateur"].includes(auth.role)) &&
                <span onClick={() => handleReplyValidation(sanction.id)}>Répondre</span>
            }
            {
                (["validation"].includes(sanction.status) && ["rh", "resp_rh"].includes(auth.role)) &&
                <span onClick={() => handleCancelValidation(sanction.id)}>Annuler la demande de validation</span>
            }
            {
                (
                    (['demande', 'traite'].includes(sanction.status) && ["rh", "resp_rh"].includes(auth.role)) || 
                    (['demande'].includes(sanction.status) && auth.id == sanction.user_id && ["superviseur","resp_sup","resp_op"].includes(auth.role))
                ) &&
                <span onClick={() => handleCancelSanction(sanction.id)}>Annuler la sanction</span>
            }
            {
                (["draft"].includes(sanction.status) && auth.id == sanction.user_id) && 
                <span>
                    <Link to={"/sanction/send_back/" + sanction.id}>{["rh", "resp_rh"].includes(auth.role) ? "Refaire le traitement" : "Renvoyer"}</Link>
                </span>
            }
            {  (["done"].includes(sanction.status)) && (["rh", "resp_rh"].includes(auth.role) ) &&
                <span>
                    <Link to={"add/mis_a_pied/" + sanction.id}>Generer mise à pied</Link> 
                </span>
            }
        </div>
    </div>
}