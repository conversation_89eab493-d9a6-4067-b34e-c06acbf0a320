<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Paie extends Model
{
    use HasFactory;
    protected $table = 'paies';
    protected $fillable = [
        'sal_base',
        'idm_depl',
        'perdiem',
        'part_variable',
        'prime_anc',
        'sal_forfait',
        'employe_id',
        'nb_heure_contrat',
        'nb_heure_convenu',
        'heure_ferie',
        'hm_dim',
        'h_maj',
        'prime_exceptionnelle',
        'prime_div',
        'prime_assid',
        'prime_resp',
        'prime_entret',
        'rappel',
        'mbrut',
        's_conge',
        'np_rv',
        'idm_licenciement',
        'ded_impot',
        'retenue_formation',
        'autre_deduction',
        'avance_special',
        'avance_15e',
        'preavis_moins',
        'all_fam_cnaps',
        'remb_frais_fixe',
        'avance_speciale_embauche',
        'prime',
        'irsa_pat',
        'mode_payement',
        'banque',
        'numero_tel',
        'numero_compte',
        'code_banque',
        'code_guichet',
        'rib',
        'cin',
        'numero_stagiaire',
        'numero_employe',
        'nom',
        'site_id',
        'real_site_id',
        'societe_id',
        'last_update',
        'user_id',
        'date_stagiaire',
        'date_embauche',
        'last_date_pointage',
        'fonction_id',
        'employes_type_id',
        'date_confirmation',
        'date_conf_soit',
        'num_emp_soit',
        'agence_id',
        'nb_heure_travaille',
        'repport_notification',
        
        'created_at',
        'pointeuse_id',
        'updated_at',
        'date_paie',
        'class',
        'salaire_brut',
        'net_a_payer',
        'masse_salariale',
        'salaire_mensuel',
        'irsa',
        'salfa',
        'cnaps',
        'cnaps_pat',
        'salfa_pat',
        'nprv_preavis_deductible',
        'nprv_licenciement',
        'nprv_preavis_payer',
        'net_imposable',
        'conge_reste',
        'conge_pris',
        'conge_paye_id',
        'status'
    ];
    
}
