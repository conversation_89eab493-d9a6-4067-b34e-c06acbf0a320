import React, { useEffect, useState } from 'react'

import useToken from '../../util/useToken'
import LoadingPage from '../../loading/LoadingPage'
import matricule from '../../util/matricule';

export default function EmployeSite({value}) {
    const [isLoading, toggleLoading] = useState(true)
    const [employes, setEmployes] = useState([])

    useEffect(() => {
        let isMounted = true;
        toggleLoading(true)
        axios.get('/api/employe/site/' + value,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else if(res.data.employes){
                    setEmployes(res.data.employes)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }, []);

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <>
                    {
                        employes.map(e => 
                            <div key={e.id} className='card-container'>
                                {matricule(e)} <span className='secondary'>{e.nom}</span> 
                            </div>
                        )
                    }
                </>
        }
    </>
}