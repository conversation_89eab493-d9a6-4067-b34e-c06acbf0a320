import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Link, Navigate } from 'react-router-dom';
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';

export default function Daf({auth}) {
    const [nbData, setData] = useState(null) 

    useEffect(() => {        
        let isMounted = true
        axios.get('/api/daf', useToken())
        .then(res => {
            setData(res.data)
        })
        .catch(err => {
            console.error(err)
        })
        return () => { isMounted = false }
    }, [])

    return (
        ["daf", "compta"].includes(auth.role) ?
            <MenuView title="">
                {
                    nbData && 
                    <div>
                        <h3 className='sub-title-menu'>DA</h3>
                        <div>
                            <div className='palette-container'>
                                {
                                    (nbData.nb_da_validation != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/da?status=validation">En attente de validation</Link>
                                        <span className='badge-outline'>{nbData.nb_da_validation}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_da_valide != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/da?status=demande">Validé par DG</Link>
                                        <span className='badge-outline'>{nbData.nb_da_valide}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_da_traite != 0) &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/da?status=traite">En cours de traitement</Link>
                                        <span className='badge-outline'>{nbData.nb_da_traite}</span>
                                    </div>
                                }
                                {
                                    (nbData.nb_da_validation == 0 && nbData.nb_da_valide == 0 && nbData.nb_da_traite == 0) &&
                                    <div>
                                        <br/>
                                        <br/>
                                        <div className='secondary center'>Aucune demande pour l'instant</div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
            </MenuView>
        :
            <Navigate to="/"/>
    );
}