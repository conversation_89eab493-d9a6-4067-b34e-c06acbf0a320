import React, { useEffect, useState } from 'react'
import LoadingPage from '../loading/LoadingPage';
import { Link, useLocation } from 'react-router-dom';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import axios from 'axios';
import useToken from '../util/useToken';
import StatusLabel from '../input/StatusLabel';
import showAmount from '../util/numberUtil';
import matricule from '../util/matricule';
import DoneAllAvanceModal from './DoneAllAvanceModal';
import ExportAvance from './ExportAvance';

export default function Avance({ auth, avances, setAvances, currentId, setCurrentId }) {
    const [isLoading, toggleLoading] = useState(false);
    const [allDataLoaded, setDataLoaded] = useState(false);
    const [doneAll, toggleDoneAll] = useState(false);
    const [showExport, toggleExport] = useState(false);
    const locationSearch = useLocation().search;
    const searchItems = [
        { label: "Reférence", name: "id", type: "number" },
        { label: "Employé", name:"employe_id", type: "number" },
        { label: "Status", name: "status", type: "string" },
        { label: "Date de création", name: "created_at", type: "date"},
        { label: "Date de paie", name: "date_paie", type:"dateMonth"},
    ]
    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true);
            setDataLoaded(true);
            params.set("offset", 0);
        } else params.set("offset", avances.length);
        axios.get("/api/avance?" + params, useToken()).then((res) => {
            if (isMounted) {
                if (res.data.error)
                    console.error("Error: " + res.data.error);
                else {
                    if (initial) setAvances(res.data.avances);
                    else {
                        const list = avances.slice().concat(res.data.avances);
                        setAvances(list);
                    }
                    setDataLoaded(res.data.avances.length < 30)
                }
                toggleLoading(false);
            }
        }).catch(e => {
            console.error(e);
        });
        return () => {
            isMounted = false;
        }
    }
    useEffect(() => updateData(true), [locationSearch]);
    const fetchMoreData = () => {
        setTimeout(() => updateData(), 300)
    }
    return (
        <>
            {
                isLoading ? 
                    <LoadingPage /> 
                : 
                    <div>
                        <div className="padding-container space-between">
                            <h2>Avance</h2>
                            {["resp_rh", "superviseur", "resp_sup", "resp_op"].includes(auth.role) && (
                                <Link to={"/avance/add"} className="btn btn-primary">Nouveau</Link>
                            )}
                        </div>
                        <SearchBar listItems={searchItems} />
                        {
                            avances.length == 0 ? 
                                <h3 className="center secondary">Aucun données trouvé</h3> 
                                : <div>
                                    <div className='action-container'>
                                        {auth.role == 'resp_rh' && <>
                                            <span onClick={() => toggleDoneAll(true)}>Terminer</span>
                                            <span onClick={() => toggleExport(true)}>Exporter</span>
                                        </>}
                                    </div>

                                    {doneAll && auth.role == 'resp_rh' && <DoneAllAvanceModal closeModal={() => toggleDoneAll(false)} updateData={updateData} />}
                                    {showExport && <ExportAvance closeModal={() => toggleExport(false)} />}
                                    
                                    <InfiniteScroll dataLength={avances.length} next={fetchMoreData} hasMore={!allDataLoaded} loader={<LoadingPage />}>
                                        <div className="line-container">
                                            <div className="row-list">
                                                <b className='matricule-employe'>Employé</b>
                                                <b className="line-cell-md"></b>
                                                <b className="status-line"><StatusLabel color={"grey"} /></b>
                                                <b className="line-cell-sm">Montant</b>
                                                <b className="">{auth.role == "resp_rh" ? "Demandeur" : "Site"}</b>
                                            </div>
                                        </div>
                                        {
                                            avances.map((avc) => (
                                                <div onClick={() => setCurrentId(avc.id)} 
                                                    className={`line-container ${currentId && currentId == avc.id ? "selected" : ""}`} 
                                                    key={avc.id}
                                                >
                                                    <div className="row-list">
                                                        <span className='matricule-employe'>{matricule(avc)}</span>
                                                        <span className="line-cell-md">
                                                            {avc.employe}
                                                        </span>
                                                        <span className="status-line">
                                                            <StatusLabel color={avc.status_color} done={avc.paie_id}/>
                                                        </span>
                                                        <span className="line-cell-sm">{showAmount(avc.montant)}</span>
                                                        <span className="">{auth.role == "resp_rh" ? avc.user_nom + "<" + avc.user_email + ">" : avc.site}</span>
                                                    </div>
                                                </div>
                                            ))
                                        }
                                    </InfiniteScroll>
                                </div>
                        }
                    </div>
            }
        </>
    )
}
