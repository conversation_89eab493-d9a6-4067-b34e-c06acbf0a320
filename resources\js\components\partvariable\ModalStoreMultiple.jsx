import moment from 'moment'
import React, { useEffect, useState } from 'react'
import Input<PERSON>onth<PERSON>ear from '../input/InputMonthYear'
import InputUser from '../input/InputUser'
import axios from 'axios'
import useToken from '../util/useToken'
import InfiniteScroll from 'react-infinite-scroll-component'
import LoadingPage from '../loading/LoadingPage'
import matricule from '../util/matricule'
import InputCheckBox from '../input/InputCheckBox'
import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'
import { IoMdClose } from 'react-icons/io'

export default function ModalStoreMultiple({ updateData, closeModal }) {
    const [datePaie, setDatePaie] = useState(null)
    const [respPart, setRespPart] = useState()
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [employes, setEmployes] = useState([])
    const [employeToAdd, setEmployeToAdd] = useState([])
    const [showConfirm, toggleConfirm] = useState(false)
    const [searchValue, setSearchValue] = useState('')

    useEffect(() => {
        if (employeToAdd.length == 0) {
            toggleConfirm(false)
        }
    }, [employeToAdd])
    
    const getSousHierarchie = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams();
        if (initial){
            setDataLoaded(true)
            params.set("offset", 0)
        }
        else {
            params.set("offset", employes.length)
        }
        
        if (respPart?.id)
            params.set("employe_id", respPart.id)

        if (datePaie)
            params.set("date_paie", datePaie.year + '-' + datePaie.month + '-20')
        if (searchValue.trim()) {
            params.set("value", searchValue)
        }
        axios.get('/api/employe/sous_hierarchie_user?' + params, useToken())
        .then((res) => {
            if (res.data.employes) {
                if (isMounted) {
                    let dataList = []
                    res.data.employes.forEach(ag => {
                        dataList.push({
                            id: ag.id,
                            warning: ag.danger ? "danger" : "",
                            matricule: ag.societe_id ? (matricule(ag)) : '',
                            date_embauche: ag.date_embauche,
                            nom: ag.nom,
                            montant: 0,
                            isAdd: employeToAdd.find(e => e.id === ag.id) ? true : false,
                            site: {
                                id: ag.site_id,
                                nom: ag.site,
                            }
                        })
                    });
                    if (initial)
                        setEmployes(dataList)
                    else {
                        const list = employes.slice().concat(dataList)
                        setEmployes(list)
                    }
                    setDataLoaded(res.data.employes.length < 30)
                }
            }
            else setEmployes([])
        })
        return () => { isMounted = false };
    }
    
    useEffect(() => {
        let timeoutId = null
        if (searchValue !== '') {
            timeoutId = setTimeout(() => {
                getSousHierarchie(true)
            }, 500)
        } else 
            getSousHierarchie(true)
        return () => {if (timeoutId) clearTimeout(timeoutId)}
    }, [searchValue, datePaie, respPart])

    const fetchMoreData = () => {
        setTimeout(() => {
            getSousHierarchie()
        }, 300);
    }
    
    const handleSelectEmploye = (employe) => {
        const newEmployes = [...employes];
        const empAdd = [...employeToAdd];
        const currentEmploye = newEmployes.find(e => e.id === employe.id);
        if (currentEmploye) {
            if (currentEmploye.warning.trim() === '') {
                const isExisting = empAdd.findIndex(e => e.id === employe.id)
                if (isExisting !== -1) empAdd.splice(isExisting, 1);
                else  empAdd.push(employe);

                newEmployes.forEach(ag => {
                    if (ag.id === employe.id) ag.isAdd = !ag.isAdd;
                })
            } else {
                newEmployes.forEach(ag => {
                    if (ag.id === employe.id) ag.isAdd = false
                });
            }
        }
        setEmployeToAdd(empAdd)
        setEmployes(newEmployes)
        return () => { isMounted = false };
    }

    const onSubmit = () => { 
        let data = new FormData()
        employeToAdd.forEach((emp, index) => {
            data.append(`employe_ids[${index}]`, emp.id);
        })
        data.append('date_paie', datePaie.year + '-' + datePaie.month + '-20')
        axios.post('/api/part_variable/store_multiple', data, useToken()).then((res) => {
            if (res.data.success) {
                updateData(true)
                closeModal()
            }
        })
    }
    return (
        <div className='modal'>
            <div>
                <div>
                    <InputMonthYear
                        defaultDate={moment()}
                        setDefaultDate
                        label='Date paie'
                        value={datePaie}
                        onChange={setDatePaie}
                        required />
                </div>
                <DualContainer>
                    <InputUser label="Responsable"
                        value={respPart}
                        onChange={setRespPart}
                        isRespPart
                        required />
                    <InputText label="Nom / matricule" value={searchValue} onChange={setSearchValue}/>
                </DualContainer>
                <div id="scrollableList">
                    <InfiniteScroll
                        dataLength={employes.length}
                        hasMore={!allDataLoaded}
                        next={fetchMoreData}
                        loader={<LoadingPage />}
                        scrollableTarget="scrollableList"
                    >
                        <div className='list-container'>
                            <ul>
                                {
                                    employes.map(ag => (
                                        <li key={ag.id} onClick={() => handleSelectEmploye(ag)}>
                                            <span style={{ marginRight: 30 }}>
                                                <InputCheckBox checked={ag.isAdd} disabled/>
                                            </span>
                                            <span className={ag.warning}>{ag.matricule + ' - ' + ag.nom.slice(0, 45)}</span><br />
                                        </li>
                                    ))
                                }
                            </ul>
                        </div>
                    </InfiniteScroll>
                </div>
                <div className='form-button-container'>
                    {
                        employeToAdd.length > 0 &&
                        <button className='btn btn-primary' onClick={() => toggleConfirm(true)} >Voir</button>
                    }
                    <button className='btn' onClick={() => closeModal()}>Annuler</button>
                </div>
                {showConfirm &&
                    <div className='modal'>
                    <div>
                        <h2>Création Multiple</h2>
                        <div>
                            <div className='list-container'>
                                <ul>
                                    {
                                        employeToAdd.map(ag => (
                                            <li key={ag.id} className='space-between'>
                                                <span className={ag.warning}>{ag.matricule + ag.nom.slice(0, 45)}</span><br />
                                                <span>
                                                    <IoMdClose size={20} onClick={() => handleSelectEmploye(ag)} />
                                                </span>
                                            </li>
                                        ))
                                    }
                                </ul>
                            </div>
                        </div>
                        <div className='form-button-container'>
                            {
                                employeToAdd.length > 0 &&
                                <button className='btn btn-primary' onClick={() => onSubmit()} >Confirmer</button>
                            }
                            <button className='btn' onClick={() => toggleConfirm(false)}>Annuler</button>
                        </div>
                    </div>
                    </div>
                }
            </div>
        </div>
    )
}
