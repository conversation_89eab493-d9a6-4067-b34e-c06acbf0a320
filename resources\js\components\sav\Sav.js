import React, { useEffect, useState } from 'react';
import { Link, useLocation, useParams  } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import { FiMoreVertical } from 'react-icons/fi';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingPage from '../loading/LoadingPage';
import HeaderSav from './HeaderSav';

export default function Sav({auth, sav, setSav, currentId, setCurrentId}) {
    const params = useParams()
    const locationSearch = useLocation().search;
    const [currentType, setCurrentType] = useState()
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    let searchItems = [
        {label: 'Référence', name: 'id', type:'number'},
        {label: 'Status', name: 'status', type:'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Demandeur', name: 'user_id', type:'number'},
        {label: 'Site', name: 'site_id', type:'number'},
    ]
    if(auth.role != 'superviseur')
        searchItems.push({label: 'Superviseur', name: 'superviseur_id', type:'number'})

    useEffect(() => {
        let isMounted = true
        if(params.type){
            axios.get('/api/type_sav/show/' + params.type, useToken())
            .then((res) => {
                if(isMounted){
                    setCurrentType(res.data)
                }
            })
        }
        return () => { isMounted = false };
    }, [params])

    const updateData = (initial) => {
        let isMounted = true;
        const searchParams = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            searchParams.set("offset", 0)
        }
        else
            searchParams.set("offset", sav.length)
        axios.get('/api/sav/' + params.type + '?' + searchParams,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setSav(res.data.sav)
                    else {
                        const list = sav.slice().concat(res.data.sav)
                        setSav(list)
                    }
                    setDataLoaded(res.data.sav.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch, params.type])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return (
        (isLoading || !currentType) ?
            <LoadingPage/>
        :
        <div>
            <div className="padding-container space-between">
                <h2>
                    {
                        currentType.name == 'tag' ? 
                            'Tag et rondier' 
                        : currentType.name == 'biometrique' ? 
                            'Pointeuse biometrique' 
                        :
                            'SAV'
                    }
                </h2>
                {   
                    ['superviseur', 'resp_sup', 'resp_op', 'tech', 'electronique', 'room'].includes(auth.role) && 
                    <Link className='btn btn-primary' to="/sav/add">Nouvelle SAV</Link>
                }
            </div>
            <SearchBar listItems={searchItems}/>
            {
                sav.length == 0 ? 
                    <h3 className='center secondary'>Aucun données trouvé</h3>
                :
                <InfiniteScroll
                    dataLength={sav.length}
                    next={fetchMoreData}
                    hasMore={!allDataLoaded}
                    loader={<LoadingPage/>}
                >
                    {
                        sav.map((s) => (
                            <div className={`card-container ${currentId && currentId == s.id ? 'selected' : ''}`} key={s.id}>
                                <div className='badge-container'>
                                    <span>
                                        <span className={'badge-outline badge-outline-' + s.status_color}>
                                            {s.status_description}
                                        </span> {
                                            s.nb_pj > 0 &&
                                            <span className='badge-outline'>
                                                Pièce jointe : {s.nb_pj}
                                            </span>
                                        }
                                    </span>
                                    <span className='pointer' onClick={() => setCurrentId(s.id)}>
                                        <FiMoreVertical size={20} color="#888"/>
                                    </span>
                                </div>
                                <HeaderSav auth={auth} data={s}/>
                            </div>
                        ))
                    }
                </InfiniteScroll>
            }
        </div>
    )
}