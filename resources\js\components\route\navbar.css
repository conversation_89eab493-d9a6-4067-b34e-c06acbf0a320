#navbar{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 70px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #ddd;
    background-color: white;
    z-index: 1;
}
#navbar > div{
    display: flex;
    align-items: center;
}
#logo{
    width: 50px;
    padding-right: 20px;
}
#profilContainer {
    text-align: right;
}
#content{
    display: flex;
    justify-content: center;
    padding: 70px 10px;
}
#content > div {
    width: 720px;
    max-width: 720px;
}
.notification-header{
    display: flex;
    padding: 10px 10px 10px ;
    border-left: 1px solid #eee;
    align-items: center;
}
.nb-notification{
    float: right;
    padding: 0px 2px;
}
.new-notification {
    animation: colors 2s infinite;
}
@keyframes colors {
    from {background-color: #e91e63; color: white}  
    to {background-color: white; color: #444}  
}