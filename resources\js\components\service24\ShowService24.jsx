import React, { useEffect, useState } from 'react'
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import LoadingPage from '../loading/LoadingPage';
import ShowHeader from '../view/ShowHeader';
import moment from 'moment';
import ActionService24 from './ActionService24';
import Tab from '../layout/Tab';
export default function ShowService24({ auth, currentId, setCurrentId, setCurrentItem, size}) {
    const [isLoading, setIsLoading] = useState(false)
    const [service, setService] = useState();
    const [defautUsers, setDefautUsers] = useState()

    const getService = () => {
        setIsLoading(true);
        let isMounted = true;
        axios.get('/api/service24/show/' + currentId, useToken())
        .then((res) => {
            if (isMounted) {
                setService(res.data);
                if (auth.id != res.data.user_id)
                    setDefautUsers([{ id: res.data.user_id, address: res.data.user_email, name: res.data.user_nom }])
                setIsLoading(false);
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }
    
    useEffect(() => getService(), [currentId])

    useEffect(() => {
        if (setCurrentItem) setCurrentItem(service)
    }, [service]);
    
    return <>{
        isLoading ?
            <LoadingPage />
        :
            <div>
                {
                    service &&
                    <>
                        <ShowHeader label="Service 24" id={service.id} closeDetail={() => setCurrentId()} size={size} />
                        <div className='card-container'>
                            <div className="badge-container">
                                <span>
                                    <span className={'badge-outline badge-outline-' + (service.status_color)}>
                                        {service.status_description}
                                    </span> {
                                        service.nb_pj > 0 &&
                                        <span className='badge-outline'>
                                            Pièce jointe : {service.nb_pj}
                                        </span>
                                    }
                                </span>
                            </div>
                            <h3>
                                <div>
                                    {matricule(service)} {service.employe}
                                </div>
                            </h3>
                            {service.date_pointage &&
                                <p style={{ whitespace: 'pre-line' }}>
                                    Date de service: <span className="text">
                                        {moment(service.date_pointage).format("DD/MM/YY") + (moment(service.date_pointage).format("HH:mm:ss") == "18:00:00" ? " NUIT": " JOUR" )}
                                    </span>
                                </p>
                            }
                            <p style={{ whitespace: 'pre-line' }}>
                                Motif: <span className="text">
                                    {service.motif}
                                </span>
                            </p>
                            <p style={{ whitespace: 'pre-line' }}>
                                Demandeur: <span className="text">
                                    {service.user_nom} {' <' + service.user_email + '>'}
                                </span>
                            </p>
                            <div className="card-action">
                                <ActionService24 auth={auth}  service={service} updateData={getService} />
                            </div>
                        </div>
                        <Tab auth={auth} name="service24_id" value={service.id} updateData={getService} defautUsers={defautUsers} />
                    </>
                }
            </div>
            }
        </>
}
