import React, { useEffect, useState } from 'react';
import {useParams,useLocation} from 'react-router-dom'
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';

export default function EditPrieceJointe({title, action}) {
    //const params = useParams()
    const location = useLocation()
    const params = new URLSearchParams(location.search)
    const type = params.get("type")
    const id = params.get("id")
    const piece = params.get("piece")   
    
    const [nature, setNature] = useState("")
    const [pj, setPj] = useState("")
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const data = new FormData()
        data.append('nature',  nature)
        data.append('pj',  pj)
        data.append('type',  type)
        data.append('id',  id)
        
        //axios.post(action + (params.type ? "/" + params.type : "") + (params.id ? "/" + params.id : ""), data, useToken())
        axios.post(action, data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }

    useEffect(() =>{
        setNature(piece)
    },[piece])

    return (
        <div>
            {
                notification ? 
                    <Notification next={id ? "/" + type + "/show/" + id : "/"} message={notification.success}/>
                :
                <div>        
                    <div className="title-container" >
                        <h2>{title}</h2>
                    </div>
                    <form onSubmit={handleSubmit} encType="multipart/form-data">
                        <div>
                            <InputText 
                                required
                                label="Nature"
                                value={nature || ""} 
                                onChange={setNature}
                                disabled={piece}/>
                            <InputText
                                required 
                                label="Pièce jointe"                                                
                                onChange={setPj}
                                type="file"
                                accept=".jpg,.png,.pdf"/>
                        </div>
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                        }
                        <ButtonSubmit disabled={submitDisabled}/>
                    </form>
                </div>
            }
        </div>
    )
}