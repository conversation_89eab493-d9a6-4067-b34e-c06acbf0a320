import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Navigate, Outlet, useLocation } from "react-router-dom";
import LoadingScreen from '../loading/LoadingScreen';

import useToken from '../util/useToken';
import moment from 'moment';

export default function GuestRoute({auth, setAuth}) {
    const {pathname} = useLocation()
    const [isLoading, setLoading] = useState(true)
    const [tentative, setTentative] = useState(0)
    const [error, setError] = useState("")

    const getAuth = (isMounted) => {
        if(localStorage.getItem("token")){
            axios.get('/api/auth', useToken())
            .then((res) => {
                if(isMounted){
                    setAuth(res.data)
                    setTentative(0)
                    setLoading(false)
                    const currentDateTime = moment(res.data.datetime)
                    if(currentDateTime.isBefore(moment().subtract(3, 'minutes')) && currentDateTime.isAfter(moment().add(3, 'minutes'))){
                        setError("Regler l'heure de l'appareil: " + currentDateTime.format("DD-MM-YY HH:mm"))
                    }
                }
            })
            .catch((e) => {
                console.error(e)
                if(e.response.status == 401){
                    localStorage.removeItem("token")
                    window.location.reload()
                }
                if(tentative > 1){
                    setLoading(false)
                    setError("Erreur de chargement")
                }
                else {
                    setTentative(tentative+1)
                }
            })
        }
        else 
            setLoading(false)
    }
    
    useEffect(() => {
        let isMounted = true
        getAuth(isMounted)
        return () => {
            isMounted = false
        };
    }, [])
    
    useEffect(() => {
        setError("")
        let isMounted = true
        const tid = setTimeout(() => getAuth(isMounted), 15000);
        return () => {
            isMounted = false
            clearTimeout(tid)
        };
    }, [tentative, auth])
    

    if(isLoading)
        return <LoadingScreen/>
    
    return (
        error ?
            <div className='main-error'>
                <h2 className='secondary' style={{whiteSpace: "pre-line"}}>
                    {error}
                </h2>
                <br/>
                <a className='btn-outline' href='/'>Réessayer</a>
            </div>
        : (
            pathname == '/login' ? (
                !auth ? 
                    <Outlet/>
                :   
                    <Navigate to="/"/>
            ) 
            : (
                !auth ? 
                    <Navigate to="login"/>
                : 
                    <Outlet/>
            )
        )
    );
}