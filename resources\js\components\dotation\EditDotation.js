import React, { useEffect, useState } from 'react';
import axios from 'axios';
import moment from 'moment';
import InputAgent from '../input/InputAgent';
import { useParams } from 'react-router-dom';
import useToken from '../util/useToken';
import InputDate from '../input/InputDate';
import ButtonSubmit from '../input/ButtonSubmit';
import Notification from '../notification/Notification';
import DualContainer from '../container/DualContainer';
import InputSelect from '../input/InputSelect';

export default function EditDotation({auth, title}) {
    const params = useParams()
    const [employe, setEmploye] = useState(null)
    const [articles, setArticles] = useState([])
    const [dateMouvement, setDateMouvement] = useState(null);
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    const [isLoading, setLoading] = useState(false)
    const [currentType, setCurrentType] = useState(null)
    const [type, setType] = useState(null)
    const typesOptions = ["entré", "sortie"]

    const handleChangeCheckbox = (e) => {
        setArticles(
            articles.map(a => {
                if(a.name == e.target.name)
                    a.checked = e.target.checked
                return a
            })
        )
    }

    const getNameSelectedArticles = (selectedArticles) => {
        let nameSelectedArticles = []
        selectedArticles.map((article) => {
            nameSelectedArticles.push(article.name)
        })
        return nameSelectedArticles
    }

    const handleSubmit = (e) => {
        e.preventDefault()
        let selectedArticles = articles.filter((article) => article.checked)
        const payload = {
            employe: employe,
            articles: getNameSelectedArticles(selectedArticles),
            type: type ? type : null,
            dateMouvement: dateMouvement ? moment(dateMouvement).format('YYYY-MM-DD') : null
        }
        axios.post("/api/mouvement_article/add", payload, useToken())
        .then((res) => {
            if (res.data.success)
                setNotification(res.data)
            else if (res.data.failure)
                setError(res.data.failure)
            else if (res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if (res.data.setError)
                setError(res.data.setError)
        })
        .catch((e) => {
            console.error(e)
            setError("Une erreur est survenue.")
        })
    }

    useEffect(() => {
        let isMounted = true
        setLoading(true)
        if(params.type){
            axios.get('/api/type_equipement/show/' + params.type, useToken())
            .then((res) => {
                if(isMounted){
                    setCurrentType(res.data.type)
                    const articles = res.data.articles.map(a => {
                        a.checked = false
                        return a
                    })
                    setArticles(articles)
                    setLoading(false)
                }
            })
            .catch(e => {
                setLoading(false)
            })
        }
        return () => { isMounted = false };
    }, [])

    useEffect(() => {
        if (employe && articles && dateMouvement && type)
            disableSubmit(false)
        else
            disableSubmit(true)
    }, [employe, articles, dateMouvement, type])

    return (
        <div id='content'>
            {
                notification ?
                    <Notification
                        next={notification.id ? "/dotation?id=" + notification.id : "/dotation"}
                        message={notification.success}/>
                : (
                <div>
                    <form onSubmit={handleSubmit}>
                        <h2>
                            {title}
                        </h2>
                        <InputAgent required value={employe} onChange={setEmploye}/>
                        {
                            articles.length > 1 &&
                            <div className='input-container'>
                                <label>Articles <span className='danger'>*</span></label>
                                <div className='checkbox-form-container'>
                                    {
                                        articles.map(art => (
                                            <div key={art.name} className='checkbox-card'>
                                                <label className="checkbox-container">
                                                    {art.designation}
                                                    <input type="checkbox" name={art.name} checked={art.checked} onChange={handleChangeCheckbox}/>
                                                    <span className="checkmark"></span>
                                                </label>
                                            </div>
                                        ))
                                    }
                                </div>
                            </div>
                        }
                        <DualContainer>
                            <InputSelect
                                required
                                label="Types" 
                                selected={type} 
                                setSelected={setType} 
                                options={typesOptions}
                            />
                            <InputDate
                                required
                                label="Date de mouvement"
                                value={dateMouvement}
                                onChange={setDateMouvement}
                            />
                        </DualContainer>
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                        }
                        <ButtonSubmit disabled={submitDisabled}/>
                    </form>
                </div>

                )
            }
        </div>
    )
}
