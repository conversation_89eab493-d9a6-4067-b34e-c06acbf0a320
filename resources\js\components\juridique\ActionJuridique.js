import React, { useState } from 'react';

import { Link } from 'react-router-dom';
import NoteModal from '../input/NoteModal';
import EtapeModal from './EtapeModal';

export default function ActionJuridique({auth, juridique, updateData}) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [showEtapeModal, toggleEtapeModal] = useState(false)
    const [currentAction, setAction] = useState(null)
    
    const handleDone = (id) => {
        setAction({
            header: "Terminer le dossier juridique",
            request: "/api/juridique/save_done/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    const handleCancel = (id) => {
        setAction({
            header: "Annuler le dossier juridique",
            request: "/api/juridique/cancel/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction} 
                updateData={() => updateData()} 
                closeModal={() => toggleNoteModal(false)}/> 
        }
        {
            showEtapeModal && 
            <EtapeModal 
                juridique={juridique}
                updateData={() => updateData()} 
                closeModal={() => toggleEtapeModal(false)}/> 
        }
        <div className='action-container'>
            {
                (["demande", "traite"].includes(juridique.status) && auth.role == "juridique") &&
                <span>
                    <Link to={"/recouvrement/edit/" + juridique.id}>Modifier</Link>
                </span>
            }
            {
                auth.role == "juridique" &&
                <span>
                    <span onClick={() => toggleEtapeModal(true)}>Changer de status</span>
                </span>
            }
        </div>
    </div>
}