import React from 'react'
import DualContainer from '../container/DualContainer'
import InputText from '../input/InputText'

export default function Conge({ conge, setConge, congePayer, employeId }) {
    return (
        <div>
            <DualContainer>
                <InputText type="number"
                    label="Solde congé"
                    value={conge.soldeConge}
                    onChange={(newCg) => setConge({ ...conge, soldeConge: newCg })}
                    disabled={!employeId}
                />
                <InputText type="number" label="Congé payés" value={congePayer} disabled />
            </DualContainer>
        </div>
  )
}
