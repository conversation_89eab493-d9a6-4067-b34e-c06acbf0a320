import axios from 'axios'
import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import InputText from '../input/InputText';
import { useNavigate } from "react-router-dom";

export default function ValidateEquipementModal({closeModal, updateData, equipement}) {
    const [commentaire, setCommentaire] = useState("ok");
    const [submitDisabled, setSubmitDisabled] = useState(true);
    const navigate = useNavigate();

    const clearParams = () => {
        navigate(location.pathname, { replace: true });
    };

    const handleOk = () => {
        const data = {
            commentaire: commentaire,
        }
        axios.post("/api/equipement/validation/" + equipement.id, data, useToken())
        .then(res => {
            updateData()
            clearParams()
        })
    }

    useEffect(() => {
        setSubmitDisabled(!commentaire)
    }, [commentaire]);

    return <div className='modal'>
        <div>
            <h2>Validation</h2>
            <InputText
                required
                type="text"
                label="Commentaire"
                value={commentaire}
                onChange={setCommentaire}/>
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Enregistrer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
}
