.row-paie{
    display:flex;
    text-overflow: ellipsis;
}
.date-paie{
    width: 100px;
    min-width: 100px;
    max-width: 100px;
}
.salaire-net{
    width: 130px;
    min-width: 130px;
    max-width: 130px;
    text-align: left;
}
.site-employe{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.agence-employe{
    text-align: left;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    /* width: 50px; */
}
/* .site-employe:hover{
    white-space: break-spaces;
    overflow: visible;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto;
} */
.row-paie:hover{
    cursor: pointer;
}
/* .row-paie>span, .row-paie > b{
    padding: 5px;
} */
.nombre-heure{
    width: 70px;
    min-width: 70px;
    max-width: 70px;
    display: inline-block;
}

.salaire>div, .salaire-item{
    width: 100%;
    border-bottom: 1px solid #ddd;
    padding: 15px 0px 20px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
/* .status-paie{
    margin-right: 5px;
} */
.fin-page{
    text-align: center;
    margin-top: 10px;
    justify-content: center;
}
