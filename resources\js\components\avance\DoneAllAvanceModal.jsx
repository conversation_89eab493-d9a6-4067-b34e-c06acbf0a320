import React, { useEffect, useState } from 'react'
import useToken from '../util/useToken';
import InputMonthYear from '../input/InputMonthYear';

export default function DoneAllAvanceModal({closeModal, updateData }) {
    const [datePaie, setDatePaie] = useState({year:"", month:""});
    const [avances, setAvances] = useState();
    const [disabledSearch, setDisabledSearch] = useState(false);
    const [searchButton, toggleSearchButton] = useState(false);
    const [confirmButton, toggleConfirmButton] = useState(false);
    const [showElement, setShowElement] = useState(false);
    const [numberAvances, setNumberAvances] = useState(0);
    const [avanceIds, setAvanceIds] = useState([]);

    useEffect(() => {
        if (datePaie && datePaie.year.toString().trim() && datePaie.month.trim()) {
            toggleSearchButton(true);
            toggleConfirmButton(false);
        }
        else if (avances && avances.length > 0) {
            toggleConfirmButton(true);
            toggleSearchButton(false);
        }
        setDisabledSearch(!(datePaie.year.toString().trim() && datePaie.month.trim()))
    }, [datePaie])

    const onSearch = () => { 
        let date_paie = datePaie.year + '-' + datePaie.month + '-20';
        let avc_ids = [];
        axios.get('/api/avance/get_to_done?date_paie=' + date_paie, useToken())
            .then((res) => {
                for (let i = 0; i < res.data.avances.length; i++) {
                    avc_ids.push((res.data.avances[i].id).toString());
                }
                setAvanceIds(avc_ids);
                setNumberAvances(res.data.avances.length)
                setShowElement(true)
                if (res.data.avances.length > 0) {
                    toggleSearchButton(false)
                    toggleConfirmButton(true)
                }
        })
    }

    const handleDone = () => { 
        const data = {ids_avc: avanceIds};
        axios.post('/api/avance/done_multiple', data, useToken()).then((res) => { 
            if (res.data.success) { 
                closeModal();
                updateData(true);
            }
        })
    }

    return (
        <div className='modal'>
            <div>
                <h2>Terminé</h2>
                <div style={{ marginBottom: 50 }}>
                    <InputMonthYear setDefaultDate label="Date Paie" value={datePaie} onChange={setDatePaie} required />
                </div>
                <div>
                    {showElement &&
                        <div >
                            {numberAvances + " élement(s) trouvé(s)"}
                        </div>
                    }
                </div>
                <div className='form-button-container'>
                    {searchButton && <button onClick={() => onSearch()} className='btn btn-primary' disabled={disabledSearch}>Chercher</button>}
                    {confirmButton &&
                        <button type='button' className='btn btn-primary' onClick={() => handleDone()}>
                            Confirmer
                        </button>
                    }
                    <button type='button' onClick={() => closeModal()}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
