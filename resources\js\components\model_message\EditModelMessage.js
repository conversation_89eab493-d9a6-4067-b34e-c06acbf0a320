import React, { useEffect, useState } from 'react'
import Textarea from '../input/Textarea'
import InputMultipleUser from '../input/InputMutipleUser'
import InputText from '../input/InputText'
import ButtonSubmit from '../input/ButtonSubmit'
import useToken from '../util/useToken'
import { useParams } from 'react-router-dom'
import Notification from '../notification/Notification'
import InputCheckBox from '../input/InputCheckBox'
import "./model.css";
import ServiceModal from '../modal/ServiceModal'

export default function EditModelMessage({ auth, title }) {
    const [content, setContent] = useState("")
    const [objet, setObjet] = useState("")
    const [users, setUsers] = useState([])
    const [copyUsers, setCopyUsers] = useState([])
    const [type, setType] = useState("")
    const [error, setError] = useState('')
    const [notification, setNotification] = useState(null)
    const [submitDisabled, disableSubmit] = useState(false)
    const [meOnly, toggleMeOnly] = useState(true)
    const [forEveryOne, toggleForEveryOne] = useState(false)
    const [forService, toggleForService] = useState(false)
    const [forAccess, setForAccess] = useState('me')
    const [showServiceModal, toggleServiceModal] = useState(false)
    const [service, setService] = useState(null)
    const [clearButton, setclearBoutton] = useState(false)
    const params = useParams();

    const removeHtmlTags = (html) => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        return doc.body.textContent || "";
    };

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        let data = new FormData()
        if (objet)
            data.append("objet", objet)
        if (content)
            data.append("content", content)
        users.forEach((u, index) => {
            data.append(`receivers[${index}]`, u.id);
        })
        copyUsers.forEach((u, index) => {
            data.append(`copies[${index}]`, u.id);
        })
        data.append("access", forAccess)
        data.append('type', type);
        axios.post(params.id ? ("/api/model_message/update/" + params.id) : "/api/model_message/add", data, useToken())
        .then((res) => {
            disableSubmit(false)
            if (res.data.success)
                setNotification(res.data)
            else if (res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if (res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    const getModel = () => {
        let isMounted = true
        axios.get('/api/model_message/' + params.id, useToken())
        .then((res) => {
            if (isMounted) {
                const data = res.data.model
                setObjet(data.objet)
                setContent(removeHtmlTags(data.content))
                setType(data.type)
                handleChangeAccess(data.access)
                let userTmp = data.users
                const toUsers = []
                const copyUsers = []
                if (userTmp.length > 0) {
                    userTmp.forEach(ur => {
                        const existingInUsers = toUsers.find(dest => dest.address === ur.user_email);
                        const existingInCopies = copyUsers.find(cp => cp.address === ur.user_email);
                        if (!existingInUsers && !existingInCopies) {
                            if (ur.follow) {
                                toUsers.push({ id: ur.user_id, address:ur.user_email, name:ur.user_name })
                            }
                            else
                                copyUsers.push({ id:ur.user_id, address:ur.user_email, name:ur.user_name })
                        }
                    });
                }
                setUsers(toUsers)
                setCopyUsers(copyUsers)
            }
        })
    }
    useEffect(() => {
        if (params.id)
            getModel()
    }, [])

    const handleChangeAccess = (access) => { 
        if (access == 'me') {
            toggleMeOnly(true)
            toggleForService(false)
            toggleForEveryOne(false)
        }
        else if (access == 'all') {
            toggleForEveryOne(true)
            toggleMeOnly(false)
            toggleForService(false)
        }
        else if (access =='service') {
            toggleForService(true)
            toggleMeOnly(false)
            toggleForEveryOne(false)
        }
        setForAccess(access)
    }

    const getAllUsers = () => {
        axios.get('/api/model_message/all_users', useToken())
        .then((res) => {
                if (res.data) {
                    let userTmp = res.data
                    const toUsers = [...users]
                    const cpUsers = [...copyUsers]
                    userTmp?.forEach(ur => {
                        const existingInUsers = toUsers.find(dest => dest.address === ur.email);
                        const existingInCopies = cpUsers.find(cp => cp.address === ur.email);
                        if (!existingInUsers && !existingInCopies) {
                            toUsers.push({ id: ur.id, address: ur.email, name: ur.name })
                        }
                    });
                    setUsers(toUsers)
                }
            })
    }
    useEffect(() => {
        let isMounted = true;
        if (service?.id){
            axios.get('/api/model_message/users_by_service/' + service.id, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data) {
                        let userTmp = res.data
                        const toUsers = [...users]
                        const cpUsers = [...copyUsers]
                        userTmp?.forEach(ur => {
                            const existingInUsers = toUsers.find(dest => dest.address === ur.email);
                            const existingInCopies = cpUsers.find(cp => cp.address === ur.email);
                            if (!existingInUsers && !existingInCopies) {
                                toUsers.push({ id: ur.id, address: ur.email, name: ur.name })
                            }
                        });
                        setUsers(toUsers)
                    }
                }
            })
        }
        return () => {isMounted = false}
    }, [service])
    
    useEffect(() => {
        if (users.length > 0 || copyUsers.length > 0)
            setclearBoutton(true)
        else
            setclearBoutton(false)
    }, [users, copyUsers])

    return (
        <div id='content'>
            {notification ? 
                    <Notification next={notification.id ? "/model_message?id=" + notification.id : "/message/model"}
                        message={notification.success}
                    />
                :
                    <div>
                        <div className="title-container">
                            <h2>{title}</h2>
                        </div>
                        {
                            showServiceModal &&
                            <ServiceModal onChange={setService} closeModal={() => toggleServiceModal(false)} />
                        }
                        <div className="card-container">
                            <div className='checkbox-model' >
                                <InputCheckBox label="Moi seul" checked={meOnly} onChange={() => handleChangeAccess("me")}/>
                                <InputCheckBox label="Même service" checked={forService} onChange={() => handleChangeAccess("service")}/>
                                { auth.role == "admin" &&
                                    <InputCheckBox label="Globale" checked={forEveryOne} onChange={() => handleChangeAccess("all")}/>
                                }
                                <div className='action-container'>
                                    <span onClick={getAllUsers}>Tous le monde</span>
                                    <span onClick={() => toggleServiceModal(true)}>Service</span>
                                    {clearButton &&
                                        <span onClick={() => { setUsers([]), setCopyUsers([]) }}>Vider destinataires</span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div>
                            <form onSubmit={handleSubmit}>
                                <InputText required label="Nom du model" value={type} onChange={setType} />
                                <InputMultipleUser label="Destinataire" users={users} setUsers={setUsers} />
                                <InputMultipleUser label="En copie" users={copyUsers} setUsers={setCopyUsers} />
                                <InputText label="Objet" value={objet} onChange={setObjet} />
                                <Textarea label="Contenu" value={content} onChange={setContent} row={10} />
                                {
                                    error &&
                                    <div className='container-error'>
                                        {error}
                                    </div>
                                }
                                <ButtonSubmit disabled={submitDisabled}/>
                            </form>
                        </div>
                    </div>
            }
        </div>
    )
}
