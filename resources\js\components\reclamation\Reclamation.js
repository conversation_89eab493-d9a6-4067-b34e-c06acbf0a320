import React, { useEffect, useState } from 'react';
import { useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import SearchBar from '../input/SearchBar';
import LoadingPage from '../loading/LoadingPage';
import InfiniteScroll from 'react-infinite-scroll-component';
import StatusLabel from '../input/StatusLabel';
import moment from 'moment';

export default function Reclamation({auth, reclamations, setReclamations, currentId, setCurrentId}) {
    const locationSearch = useLocation().search
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [offsetDate, setOffsetDate] = useState("")

    const searchItems = [
        { label: 'ID', name: 'id', type:'number' },
        // {label: 'Réference dossier', name: 'reference', type:'string'},
        { label: "Employe", name: "employe", type:"string" },
        { label: "Date de Service", name: "date_service", type:"date_service" },
        { label: 'Status', name: 'status', type:'string' },
        { label: 'Date de création', name: 'created_at', type:'date' },
        { label: 'Superviseur', name: 'user_id', type:'number' },
    ]

    const updateData = (initial) => {
        let isMounted = true;
        const urlParams = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            urlParams.set("offset", 0)
        }
        else
            urlParams.set("offset", reclamations.length)

        if(offsetDate){
            urlParams.set("offset_date", offsetDate)
        }

        axios.get("/api/reclamation?" + urlParams ,  useToken())
            .then((res) => {
            if(isMounted) {
                if(res.data.error){
                    console.error(res.data.error)
                }
                else {
                    if(initial) {
                        setReclamations(res.data.reclamations)
                        setOffsetDate(res.data.offset_date)
                    }
                    else {
                        const list = reclamations.slice().concat(res.data.reclamations)
                        setReclamations(list)
                    }
                    setDataLoaded(res.data.reclamations.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }
    
    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        : 
            <div>
                <div className="padding-container space-between">
                    <h2>Réclamation</h2>
                    {/* {   
                        ['rh', 'resp_rh', 'superviseur', 'resp_sup', 'resp_op'].includes(auth.role) && 
                        <Link className='btn btn-primary' to="/reclamation/add">Nouveau</Link>
                    } */}
                </div>
                <SearchBar hasEmploye listItems={searchItems}/>
                {
                    reclamations.length == 0 ? 
                        <h3 className='center secondary'>Aucun données trouvé</h3>
                    :
                        <InfiniteScroll
                            dataLength={reclamations.length}
                            next={fetchMoreData}
                            hasMore={!allDataLoaded}
                            loader={<LoadingPage/>}
                        >
                            <div className="line-container ">
                                <div className='row-employe'>
                                    <b className='line-cell-lg'>Agent</b>
                                    <b className="status-line"><StatusLabel color={"grey"} /></b>
                                    <b className='date-service'>Service</b>
                                    <b>Type</b>
                                </div>
                            </div>
                            {
                                reclamations.map((r) => (
                                    <div className={`table line-container ${currentId && currentId == r.id ? 'selected' : ''}`} key={r.id}>
                                        <div className={"row-employe"} onClick={() => setCurrentId(r.id)}>
                                            <span className='line-cell-lg'>
                                                {r.employe_id ? ('[' + matricule(r) + ']' + ' ' + r.employe) : `[SM] - ${r.agent_not_registered}`}
                                            </span>
                                            <span className="status-line">
                                                <StatusLabel color={r.status_color} done={r.paie_id}/>
                                            </span>
                                            <span className='date-service'>
                                                {moment(r.date_pointage).format('DD/MM/YYYY') + ' ' + (moment(r.date_pointage).format('HH:mm') == "18:00" ? "NUIT" : "JOUR")}
                                            </span>
                                            <span>
                                                {r.type == 'sm' ? "Agent non enregistré" : r.type == "service24" ? "Manque de demande service 24" : r.type == "archive" ? "Agent archivé" : r.type == "mis_a_pied" ? "Mis à pied" : ""}
                                            </span>
                                        </div>
                                    </div>
                                ))
                            }
                        </InfiniteScroll>
                }
            </div>
    } </>
}
