import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

export default function TypeObjetModal({onChange,closeModal, useLink}) {
    const types = [
        { 'label': 'Paie', 'value': 'paie' },
        { 'label': 'Part variable', 'value': 'part_variable' },
        { 'label': 'Service 24', 'value': 'service' },
        { 'label': 'Réclamation pointage', 'value': 'reclamation' },
        { 'label': 'Sanction', 'value': 'sanction' },
        { 'label': 'Absence', 'value': 'absence' },
        { 'label': 'Déduction', 'value': 'deduction' },
        { 'label': 'Avance', 'value': 'avance' },
        { 'label': 'Prime', 'value': 'prime' },
        { 'label': 'SAV', 'value': 'sav' },
        { 'label': 'Approvisionnement', 'value': 'da' },
        { 'label': 'Juridique', 'value': 'juridique' },
        { 'label': 'Visite de poste', 'value': 'visite_poste' },
        { 'label': 'Fait marquant', 'value': 'fait_marquant' },
        { 'label': 'Site', 'value': 'site' },
        { 'label': 'Equipement', 'value': 'equipement' },
        { 'label': 'Flotte', 'value': 'flotte' },
        { 'label': 'Employé', 'value': 'employe' },
    ]
    const [typeNote, setTypeNote] = useState(types)
    const [searchValue, setSearchValue] = useState('')
    const navigate = useNavigate()
    const location = useLocation()

    const handleSelect = (value) => {
        if (useLink) {
            let params = new URLSearchParams(location.search)
            params.set("type_note", value)
            navigate(location.pathname + "?" + params)
        }
        onChange(value)
        closeModal()
    }

    useEffect(() => {
        const cloneType = [...types]
        const newTypes = cloneType.filter((tp) => tp.label.toLowerCase().includes(searchValue.toLocaleLowerCase()));
        setTypeNote(newTypes);
    }, [searchValue])
    

    return (
        <div className='modal'>
            <div className='input-container'>
                <h2>Objet</h2>
                <div className='search-container'>
                    <input type="text" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} placeholder="Ex: Approvisionnement" />
                </div>
                {
                    typeNote.map((item, index) => {
                        return (
                            index < 8 &&
                            <div className='table line-container' key={index} onClick={() => handleSelect(item.value)}>
                                <span>{item.label}</span>
                            </div>
                        )
                    })
                }
                { typeNote.length > 8 && 
                    <div className='line-container'>
                        <span>...</span>
                    </div>
                }
                <div className='form-button-container'>
                    <button onClick={closeModal}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
