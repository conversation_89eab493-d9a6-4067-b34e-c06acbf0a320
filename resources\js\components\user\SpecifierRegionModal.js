import { useEffect, useState } from "react"
import DualContainer from "../container/DualContainer"
import InputCheckBox from "../input/InputCheckBox"
import useToken from "../util/useToken"
import axios from "axios"

export default function SpecifierRegionModal({id, updateData, closeModal}) {
    const [isTana, toggleIsTana] = useState(false)
    const [isProvince, toggleIsProvince] = useState(false)
    const [isTamatave, toggleIsTamatave] = useState(false)

    useEffect(() => {
        show()
    }, [])

    const handleOk = () => {
        const payload = {
            isTana: isTana,
            isProvince: isProvince,
            isTamatave: isTamatave
        }
        axios.post("/api/region_users/specify/" + id, payload, useToken())
        .then(res => {
            updateData()
            closeModal()
        })
    }

    const show = () => {
        axios.get("/api/region_users/show/" + id, useToken())
        .then(res => {
            if (res.data) {
                const regionArray = res.data[0].regions.split(',').map(Number)
                if (regionArray.includes(1)) toggleIsTana(true)
                if (regionArray.includes(2)) toggleIsProvince(true)
                if (regionArray.includes(3)) toggleIsTamatave(true)
            }
        })
    }

    return <div className="modal">
        <div className='input-container'>
            <label>Région(s) <span className='danger'>*</span></label>
            <div className='checkbox-form-container'>
                <DualContainer>
                    <InputCheckBox
                        name="TANA"
                        label="TANA"
                        checked={isTana}
                        onChange={() => toggleIsTana(!isTana)}
                    />
                    <InputCheckBox
                        name="PROVINCE"
                        label="PROVINCE"
                        checked={isProvince}
                        onChange={() => toggleIsProvince(!isProvince)}
                    />
                    <InputCheckBox
                        name="TAMATAVE"
                        label="TAMATAVE"
                        checked={isTamatave}
                        onChange={() => toggleIsTamatave(!isTamatave)}
                    />
                </DualContainer>
            </div>
            <div className="form-button-container">
                <button className="btn-primary" onClick={handleOk}>Envoyer</button>
                <button onClick={closeModal}>
                    Annuler
                </button>
            </div>
        </div>
    </div>
}