const moment = require('moment');
const mysql = require('mysql');

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const sqlSelectEquipementDone = 
    "SELECT eq.id, eq.employe_id, eq.site_id, eq.user_id, eq.updated_at, " +
    "GROUP_CONCAT(aeq.article ORDER BY aeq.id) as articles FROM equipements eq " + 
    "LEFT JOIN employes emp ON emp.id = eq.employe_id " +
    "LEFT JOIN article_equipements aeq ON aeq.equipement_id = eq.id " +
    "where eq.status = 'done' " +
    "AND eq.type = 'tenue' " +
    "AND emp.soft_delete != 1 " +
    "GROUP BY eq.id, eq.employe_id, eq.site_id, eq.user_id";

const sqlInsertIntoMouvementEquipement = 
    "INSERT INTO mouvement_equipement(ref, type_mouvement,date_mouvement,employe_id,superviseur_id) " +
    "VALUES (?, ?, ?, ?, ?)";


const sqlInsertIntoLigneEquipement = 
    "INSERT INTO ligne_equipement(article, mouvement_equipement_id, status) " +
    "VALUES (?, ?, ?)";
    
const populateLigneEquipement = (articles, mouvementId, index = 0) => {
    if (index >= articles.length) return

    let article = articles[index].trim()
    pool.query(sqlInsertIntoLigneEquipement, [article, mouvementId, 'attribué'], (err, result) => {
        if (err) {
            console.error(err)
        } else {
            console.log(result)
            setTimeout(() => {
                populateLigneEquipement(articles, mouvementId, index + 1)
            }, 500)
        }
    })
}

const processEquipements = (equipements, index = 0, ref = 1) => {
    if (index >= equipements.length) return

    let eq = equipements[index]
    pool.query(
        sqlInsertIntoMouvementEquipement,
        [ref, "sortie", moment(eq.updated_at).format("YYYY-MM-DD"), eq.employe_id, eq.user_id]
        , (err, result) => {
            if (err) {
                console.error(err)
            } else {
                let articles = eq.articles.split(/\s*,\s*/)
                populateLigneEquipement(articles, result.insertId)
                setTimeout(() => {
                    processEquipements(equipements, index + 1, ref + 1)
                }, 500)
            }
        })
}

const populateMouvementEquipement = () => {
    pool.query(sqlSelectEquipementDone, [], (err, equipements) => {
        if (err) {
            console.error(err)
        } else {
            processEquipements(equipements)
        }
    })
}


populateMouvementEquipement()