import React from 'react';

export default function InputTextg({value, onChange, type, placeholder}) {
    return <div className='input-container-lg'>
        <input 
            autoComplete='false'
            placeholder={placeholder ? placeholder :""} 
            type={type ? type : "text"} 
            value={value} 
            onChange={(e) => {onChange(e.target.value)}}
            />
    </div>
}