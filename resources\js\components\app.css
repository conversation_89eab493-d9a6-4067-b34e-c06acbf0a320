* {
    box-sizing: border-box;
    font-family: "Bahnschrift";
    margin: 0;
    padding: 0;
}

h1 {
    margin: 30px 0px;
}

h2 {
    margin: 20px 0px;
}

h3 {
    margin: 10px 0px;
}

h4 {
    margin: 7px 0px;
}

.capitalize {
    text-transform: capitalize;
}

.center {
    text-align: center;
}

.fade {
    color: #aaa;
}

.text {
    color: #444;
    white-space: pre-line;
}

.text-dotted {
    font-family: "CallingCode";
    text-decoration: underline dotted #073570;
}

.primary {
    color: #073570;
}
.danger {
    color: #b71c1c;
}
.secondary {
    color: #888;
}
.warning {
    color: #827717;
}
.pink {
    color: #e91e63;
}
.cyan {
    color: #00bcd4;
}
.purple {
    color: #9c27b0
}
.teal {
    color: #009688;
}
.lime {
    color: #cddc39;
}
.grey {
    color: #888;
}
.orange {
    color: #ff9800;
}
.green {
    color: #8bc34a;
}
.yellow {
    color: #fdd835
}
.brown {
    color: #795548;
}
.indigo {
    color: #3f51b5;
}

a.link-no-style {
    text-decoration: none;
    color: inherit;
}

.primary-link {
    color: #073570;
    text-decoration: underline;
}

.right {
    text-align: right;
}

.uppercase {
    text-transform: uppercase;
}

.badge-menu {
    padding: 3px 5px;
    border-radius: 10px;
    border: 1px #aaa solid;
    color: #aaa;
    background-color: #073570;
}

.badge-outline {
    padding: 3px 5px;
    border-radius: 10px;
    border: 1px #888 solid;
    color: #888;
    background-color: white;
    margin: 0px 1px 0px 1px;
}

.badge-number-outline {
    font-size: 9pt;
    padding: 1px 5px 0px 5px;
    border-radius: 10px;
    border: 1px #888 solid;
    color: #666;
    background-color: white;
}

.badge-number {
    padding: 3px 5px 0px 5px;
    border-radius: 10px;
    color: whitesmoke;
    background-color: #073570;
}
.badge-outline-pink {
    border: 1px #e91e63 solid;
    color: #e91e63;
}
.badge-outline-cyan {
    border: 1px #00bcd4 solid;
    color: #00bcd4;
}
.badge-outline-purple {
    border: 1px #9c27b0 solid;
    color: #9c27b0
}
.badge-outline-teal {
    border: 1px #009688 solid;
    color: #009688;
}
.badge-outline-lime {
    border: 1px #c0ca33 solid;
    color: #c0ca33;
}
.badge-outline-grey{
    border: 1px #666 solid;
    color: #666;
}
.badge-outline-orange {
    border: 1px #ff9800 solid;
    color: #ff9800;
}
.badge-outline-green {
    border: 1px #8bc34a solid;
    color: #8bc34a;
}
.badge-outline-yellow {
    border: 1px #fdd835 solid;
    color: #fdd835
}
.badge-outline-brown {
    border: 1px #795548 solid;
    color: #795548
}
.badge-outline-indigo {
    border: 1px #3f51b5 solid;
    color: #3f51b5;
}
.badge-outline-blue {
    border: 1px #073570 solid;
    color: #073570
}

.badge-outline-red {
    border: 1px #b71c1c solid;
    color: #b71c1c
}

.badge-outline-enter {
    border: 1px #4caf50 solid;
    color: #4caf50;
}

.badge-outline-exit {
    border: 1px #ef5350 solid;
    color: #ef5350;
}

.badge-outline-usure {
    border: 1px #ef5350 solid;
    color: #ef5350;
}

.article-usee {
    color: #ef5350; 
}

.main-error {
    padding-top: 20%;
    text-align: center;
}

a.btn {
    text-decoration: none;
    color: white;
    background-color: #888;
    border: none;
}

.btn {
    padding: 10px 15px;
    color: white;
    background-color: #888;
    border: none;
}

.btn-outline-secondary {
    color: #888;
    background-color: white;
    border: 1px solid #aaa;
}

.btn-primary {
    background-color: #073570 !important;
}

.btn-outline-primary {
    border: 1px solid #073570;
    background-color: white;
    color: #073570;
}

button:disabled {
    opacity: .7;
}

.vertical-align {
    display: flex;
    align-items: center;
}

.space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.on-hover:hover{
    background-color: #f6f6f6;
}
.block {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    padding: 0px 10px;
}

.padding-container{
    display: flex;
    align-items: center;
    padding: 0px 10px;
}
.header-container {
    padding: 20px 10px;
}

.trace,
.trace>span {
    color: #666;
    font-family: 'CallingCode' !important;
    white-space: pre-line;
}

.layout-container {
    padding-bottom: 30px;
}

.pointer {
    cursor: pointer;
}

.pj-container {
    padding: 5px 0;
    /*border-top: 1px solid #ccc;*/
}

.card-pj {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #eee;
    padding: 12px 10px;
    color: #888888;
    /*box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;*/
}

.card-pj-title {
    text-transform: uppercase;
}

.card-pj-filename {
    max-width: 250px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    cursor: pointer;
}
.nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.card-pj-filename:hover {
    color: #000;
}

.card-pj-action {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-pj-icon {
    /*transform: translateY(3px);*/
    cursor: pointer;
    color: inherit;
    font-size: 18px;
}

.card-pj-icon:last-child {
    margin-left: 5px;
}

.card-pj-icon:hover {
    color: #000;
}

.employe-detail-container {
    display: flex;
    flex-wrap: wrap;
    line-height: 2;
}

.employe-detail-container>.item {
    background: #eee;
    color: #000;
    padding: 10px 12px;
    margin-right: 10px;
    margin-top: 10px;
}

.complement-container {
    padding-top: 30px;
}

.complement-item {
    width: 100%;
    border-bottom: 1px solid #ddd;
    padding: 15px 0px 20px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.complement-item>div {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.complement-item>a {
    text-decoration: none;
    color: #073570;
}

.complement-item>span>.badge-outline {
    margin-left: 5px;
    background: #073570;
    color: #fff;
    border: 1px #073570 solid;
    margin-right: 10px;
}

.complement-item>span>.hide {
    border: 1px #888 solid;
    color: #888;
    background: none;
}

.complement-item>span>.badge-outline>svg {
    transform: translateY(3px);
}

.action {
    color: #073570;
    cursor: pointer;
}

.selected {
    background: #f6f6f6;
}

.pointage-container{
    display: flex;
    justify-items: center;
    justify-content: space-between;
}