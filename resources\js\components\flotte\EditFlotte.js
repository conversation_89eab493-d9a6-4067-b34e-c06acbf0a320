import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import LastFlotte from './LastFlotte';
import Textarea from '../input/Textarea';
import InputSite from '../input/InputSite';
import LoadingScreen from '../loading/LoadingScreen';
import InputCheckBox from '../input/InputCheckBox';

export default function EditFlotte({auth, title, action}) {
    const params = useParams()
    const [me, setMe] = useState(false)
    const [isLoading, toggleLoading] = useState(false)
    const [flotte, setFlotte] = useState(null)
    const [site, setSite] = useState(null)
    const [lastFlotte, setLastFlotte] = useState([])
    const [objet, setObjet] = useState("")
    const [commentaire, setCommentaire] = useState("")
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const data = {
            me: me ? 1 : 0,
            site_id: site ? site.id : '',
            objet: objet,
            commentaire: commentaire,
        }
        
        axios.post(action + (params.id ? "/" + params.id : ""), data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }
    
    //Last Demande Flotte
    useEffect(() => {
        let isMounted = true
        if(site){
            setLastFlotte([])
            axios.get("/api/last_flotte/" + site.id, useToken())
            .then((res) => {
                if(isMounted) setLastFlotte(res.data)
            })
            
        }
        return () => {isMounted = false}
    }, [site])
    useEffect(() => {
        let isMounted = true
        if(params.id){
            toggleLoading(true)
            axios.get('/api/flotte/show/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    const flotte = res.data
                    setFlotte(flotte)
                    if(flotte.objet) setObjet(flotte.objet)
                    if(flotte.commentaire) setCommentaire(flotte.commentaire)
                    if(flotte.site) setSite({
                        id: flotte.site_id,
                        nom: flotte.site
                    })
                    else 
                        setLastFlotte([])
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e)
                toggleLoading(false)
            })
        }
        else
            setLastFlotte([])
        return () => { isMounted = false };
    }, [])
    return (
        <div id="content">
            {
                isLoading ?
                    <LoadingScreen/>
                : notification ? 
                    <Notification next={notification.id ? "/flotte?id=" + notification.id : "/"} message={notification.success}/>
                :
                <div>
                    <div className="title-container">
                        <h2>{title}</h2>
                    </div>
                    {
                        lastFlotte.length > 0 &&
                        <div className='header-container'>
                            <h3>Dernières demande de flotte</h3>
                            <div>
                                <span className='text'>
                                    {site.nom}
                                </span>
                            </div>
                        </div>
                    }
                    {
                        lastFlotte.map((flotte,index) => (
                            <LastFlotte key={index} flotte={flotte}/>
                        ))
                    }
                    {
                        lastFlotte.length > 0 &&
                        <div className="input-container-btn">
                            <button onClick={() => setLastFlotte([])}>Continuer</button>
                        </div>
                    }
                    {
                        lastFlotte.length == 0 &&
                        <form onSubmit={handleSubmit}>
                            {
                                (!params.id || (flotte && auth.id == flotte.user_id)) &&
                                <div>
                                    <div className='card-container'>
                                        <InputCheckBox label="Pour moi même" checked={me} onChange={setMe}/>
                                    </div>
                                    {
                                        !me &&
                                        <InputSite
                                            required
                                            value={site} 
                                            onChange={setSite}/>
                                    }
                                    <InputText 
                                        required
                                        label="Objet"
                                        value={objet} 
                                        onChange={setObjet}/>
                                    <Textarea
                                        required
                                        label="Commentaire"
                                        value={commentaire}
                                        onChange={setCommentaire}/>
                                </div>
                            }
                            {
                                error &&
                                <div className='container-error'>
                                    {error}
                                </div>
                            }
                            <ButtonSubmit disabled={submitDisabled}/>
                        </form>
                    }
                </div>
            }
        </div>
    )
}