<?php

namespace App\Http\Controllers;

use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ServiceController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }

    public static function index(){
        return response()->json(Service::orderBy('designation')->get());
    }

    public static function message(Request $request){
        $services = Service::orderBy('designation')->get();
        $notes = DB::select("SELECT u.service_id, coalesce(count(n.id), 0) as 'nb_unread' FROM note_messages n 
            LEFT JOIN messages m ON m.id = n.message_id 
            LEFT JOIN users u ON u.id = m.user_id 
            WHERE (n.seen is null or n.seen = 0) and n.user_id = ? 
            GROUP BY u.service_id", [$request->user()->id]);
        foreach ($services as $service) {
            foreach ($notes as $n) {
                if($n->service_id == $service->id)
                    $service->nb_unread = $n->nb_unread;
            }
        }
        $consigne_non_lu = DB::select("SELECT coalesce(count(n.id), 0) as 'unread' FROM note_messages n
            LEFT JOIN messages ms ON ms.id = n.message_id
            WHERE (n.seen is null or n.seen = 0) and n.follow = 1 and ms.user_id = ?", [$request->user()->id])[0]->unread;
        
        return response()->json(compact("services", "consigne_non_lu"));
    }
}
