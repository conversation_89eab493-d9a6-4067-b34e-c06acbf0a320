import React, { useState } from 'react';
import axios from 'axios';

import InputText from '../input/InputText';
import useToken from '../util/useToken';

export default function EditPieceJointeModal({name, value, updateData, closeModal, defaultNature, currentNature, actionPjType}) {
    const equipement_id = (name == "equipement_id" ? value : "")
    const sanction_id = (name == "sanction_id" ? value : "")
    const prime_id = (name == "prime_id" ? value : "")
    const absence_id = (name == "absence_id" ? value : "")
    const sav_id = (name == "sav_id" ? value : "")
    const flotte_id = (name == "flotte_id" ? value : "")
    const appro_id = (name == "approvisionnement_id" ? value : "")
    const visite_id = (name == "visite_poste_id" ? value : "")
    const fait_id = (name == "fait_marquant_id" ? value : "")
    const juridique_id = (name == "juridique_id" ? value : "")
    const employe_id = (name == "employe_id" ? value : "")
    const deduction_id = (name == "deduction_id" ? value : "")
    const paie_id = (name == "paie_id" ? value : "")
    const avance_id = (name == "avance_id" ? value : "");
    const service24_id = (name == "service24_id" ? value : "");
    const reclamation_id = (name == "reclamation_id" ? value : "")
    const part_variable_id = (name == "part_variable_id" ? value : "");
    const satisfaction_id = (name == "satisfaction_id" ? value : "")
    const planning_id = (name == "planning_id" ? value : "")
    const dotation_id = (name == "dotation_id" ? value : "")
    const recrutement_id = (name == "recrutement_id" ? value : "")
    const [nature, setNature] = useState(currentNature ? currentNature : "")
    const [pj, setPj] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)
    
    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const formData = new FormData()
        if(currentNature){
            formData.append('nature',  currentNature)
        }else{
            formData.append('nature',  nature)
        }        
        formData.append('pj',  pj)
        if (equipement_id) {
            formData.append("equipement_id", equipement_id)
        }
        else if (sanction_id) {
            formData.append("sanction_id", sanction_id)
        }
        else if (prime_id) {
            formData.append("prime_id", prime_id)
        }
        else if (absence_id) {
            formData.append("absence_id", absence_id)
        }
        else if (sav_id) {
            formData.append("sav_id", sav_id)
        }
        else if (flotte_id) {
            formData.append("flotte_id", flotte_id)
        }
        else if (appro_id) {
            formData.append("approvisionnement_id", appro_id)
        }
        else if (visite_id) {
            formData.append("visite_poste_id", visite_id)
        }
        else if (fait_id) {
            formData.append("fait_marquant_id", fait_id)
        }
        else if (juridique_id) {
            formData.append("juridique_id", juridique_id)
        }
        else if (employe_id) {
            formData.append("employe_id", employe_id)
        }
        else if (deduction_id) {
            formData.append("deduction_id", deduction_id)
        }
        else if (paie_id) {
            formData.append("paie_id", paie_id)
        }
        else if (avance_id) {
            formData.append("avance_id", avance_id)
        }
        else if (service24_id) {
            formData.append("service24_id", service24_id)
        }
        else if (reclamation_id) {
            formData.append("reclamation_id", reclamation_id)
        }
        else if (part_variable_id) {
            formData.append("part_variable_id", part_variable_id)
        }
        else if (satisfaction_id) {
            formData.append("satisfaction_id", satisfaction_id)
        }
        else if (planning_id) {
            formData.append("planning_id", planning_id)
        }
        else if (dotation_id) {
            formData.append("dotation_id", dotation_id)
        }
        else if (recrutement_id) {
            formData.append("recrutement_id", recrutement_id)
        }
        axios.post(actionPjType == "edit" ? "/api/piece_jointe/edit/"+name+"/"+value : "/api/piece_jointe/add", formData, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success){
                closeModal()
                if(updateData) updateData()
            }
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }

    return <div className="modal">
        <div>
            <form onSubmit={handleSubmit} encType="multipart/form-data">
                <div className="title-container" >
                    <h2>Pièce jointe</h2>
                </div>
                <div>
                    <InputText 
                        required
                        label="Nature"
                        value={nature} 
                        onChange={setNature}
                        disabled={currentNature}/>
                    <InputText
                        required 
                        label="Pièce jointe"                                                
                        onChange={setPj}
                        type="file"
                        accept=".jpg,.png,.pdf"/>
                </div>
                {
                    error &&
                    <div className='container-error'>
                        {error}
                    </div>
                }
                <div className='form-button-container'>
                    <button className='btn-primary' type='submit' disabled={submitDisabled}>Envoyer</button>
                    <button onClick={() => closeModal()}>Annuler</button>
                </div>
            </form>
        </div>
    </div>
}