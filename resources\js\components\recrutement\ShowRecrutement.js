import React, { useEffect, useState } from 'react'
import {useParams} from 'react-router-dom'
import axios from 'axios'
import ShowHeader from '../view/ShowHeader'

import useToken from '../util/useToken';
import moment from 'moment';

import Tab from '../layout/Tab';
import LoadingPage from '../loading/LoadingPage';
import ActionRecrutement from './ActionRecrutement';

export default function ShowRecrutement({auth, currentId, setCurrentId, setCurrentItem, size, currentNature, setCurrentNature}) {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [recrutement, setRecrutement] = useState(null)

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/recrutement/show_detail/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setRecrutement(res.data)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(recrutement)
    }, [recrutement]);
    
    useEffect(updateData, [currentId])

    return <>{
        isLoading ?
            <LoadingPage/>
        :
            <div>
                {
                    recrutement &&
                    <>
                        <ShowHeader label="Recrutement" id={recrutement.id} closeDetail={() => setCurrentId()} size={size}/>
                        <div className='card-container'>
                            <div className='badge-container'>
                                <span>
                                    <span className={'badge-outline badge-outline-' + (recrutement.soft_delete == 1 ? "brown" : "cyan")}>
                                        {recrutement.soft_delete == 1 ? "Archive" : recrutement.status}
                                    </span>
                                    {
                                        recrutement.nb_pj > 0 &&
                                        <span className='badge-outline'>
                                            Pièce jointe : {recrutement.nb_pj}
                                        </span>
                                    }
                                </span>
                            </div>
                            <h3>
                                <div> 
                                    {recrutement.nom}
                                </div>
                                <div>
                                    CIN : {recrutement.cin_text}
                                </div>                        
                            </h3>
                            <p style={{whiteSpace: "pre-line"}}>
                                Crée le : <span className='text'>{moment(recrutement.created_at).format("DD/MM/YY HH:mm")}</span>
                            </p>
                            <p style={{whiteSpace: "pre-line"}}>
                                Par : <span className='text'>{recrutement.recruteur ? `${recrutement.recruteur}<${recrutement.recruteur_email}>` : ''}</span>
                            </p>
                            <div className='card-action'>
                                <ActionRecrutement auth={auth} recrutement={recrutement} updateData={updateData}/>
                            </div>
                        </div>
                        <Tab auth={auth} name="recrutement_id" value={recrutement.id} data={recrutement} updateData={updateData} currentNature={currentNature} setCurrentNature={setCurrentNature}/>
                    </>
                }
            </div>
    }</>
}