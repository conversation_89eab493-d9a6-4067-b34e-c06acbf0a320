import React, { useEffect, useState } from 'react';
import axios from 'axios';

import InputText from '../input/InputText';
import useToken from '../util/useToken';
import Textarea from '../input/Textarea';

import InputMultipleUser from '../input/InputMutipleUser';
import LoadingPage from '../loading/LoadingPage';
import moment from 'moment';
import matricule from '../util/matricule'
const getMatricule = matricule;

export default function SendEmailModal({auth, name, value, updateData, closeModal}) {
    const equipement_id = (name == "equipement_id" ? value : "")
    const sanction_id = (name == "sanction_id" ? value : "")
    const prime_id = (name == "prime_id" ? value : "")
    const absence_id = (name == "absence_id" ? value : "")
    const sav_id = (name == "sav_id" ? value : "")
    const flotte_id = (name == "flotte_id" ? value : "")
    const appro_id = (name == "approvisionnement_id" ? value : "")
    const visite_id = (name == "visite_poste_id" ? value : "")
    const fait_id = (name == "fait_marquant_id" ? value : "")
    const juridique_id = (name == "juridique_id" ? value : "")
    const employe_id = (name == "employe_id" ? value : "")
    const part_variable_id = (name == "part_variable_id" ? value : "")
    const deduction_id = (name == "deduction_id" ? value : "")
    const paie_id = (name == "paie_id" ? value : "")
    const service24_id = (name == "service24_id" ? value : "")
    const avance_id = (name == "avance_id" ? value : "");
    const reclamation_id = (name == "reclamation_id" ? value : "")
    const satisfaction_id = (name == "satisfaction_id" ? value : "") 

    const [isLoading, setLoading] = useState(false)
    const [users, setUsers] = useState([])
    const [objet, setObjet] = useState("")
    const [message, setMessage] = useState("") 
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        let formData = new FormData() 
        formData.append("note", message)
        formData.append("user_ids", JSON.stringify(users))
        if(equipement_id){
            formData.append("objet", "Equipement")
            formData.append("equipement_id", equipement_id)  
        }
        else if(visite_id){
            formData.append("objet", "Visite de poste")
            formData.append("visite_id", visite_id)  
        }
        else if(fait_id){
            formData.append("objet", "Fait marquant")
            formData.append("fait_id", fait_id)
        }
        else if(juridique_id){
            formData.append("objet", "Juridique")
            formData.append("juridique_id", juridique_id)
        }
        else if(sanction_id){
            formData.append("objet", "Sanction")
            formData.append("sanction_id", sanction_id)
        }
        else if(prime_id){
            formData.append("objet", "Prime")
            formData.append("prime_id", prime_id)
        }
        else if(absence_id){
            formData.append("objet", "Absence")
            formData.append("absence_id", absence_id)
        }
        else if(sav_id){
            formData.append("objet", "SAV")
            formData.append("sav_id", sav_id)
        }
        else if(flotte_id){
            formData.append("objet", "Flotte")
            formData.append("flotte_id", flotte_id)
        }
        else if(appro_id){
            formData.append("objet", "DA")
            formData.append("appro_id", appro_id)
        }
        else if(employe_id){
            formData.append("objet", "Employé")
            formData.append("employe_id", employe_id)
        }
        else if(juridique_id){
            formData.append("objet", "Juridique")
            formData.append("juridique_id", juridique_id)
        }
        else if (deduction_id) {
            formData.append("objet", "Déduction")
            formData.append("deduction_id", deduction_id)
        }
        else if (part_variable_id) {
            formData.append("objet", "Part variable")
            formData.append("part_variable_id", part_variable_id)
        }
        else if (paie_id) {
            formData.append("objet", "Paie")
            formData.append("paie_id", paie_id)
        }
        else if (service24_id) {
            formData.append("objet", "Service 24")
            formData.append("service24_id", service24_id)
        }
        else if (avance_id) { 
            formData.append("objet", "Avance")
            formData.append("avance_id", avance_id)
        }
        else if (reclamation_id) {
            formData.append("objet", "Reclamation")
            formData.append("reclamation_id", reclamation_id)
        }
        else if (satisfaction_id) {
            formData.append("objet", "Satisfaction")
            formData.append("satisfaction_id", satisfaction_id)
        }
        axios.post("/api/send_email", formData, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success){
                closeModal()
                if(updateData) updateData()
            }
            else if(res.data.error == "EACCES")
                    setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    useEffect(() => {
        let isMounted = true
        if(juridique_id){
            setLoading(true)
            axios.get('/api/juridique/show/' + juridique_id, useToken())
            .then((res) => {
                if(isMounted){
                    const juridique = res.data
                    setObjet((juridique.recouvrement ? "Recouvrement" : "Plainte")
                        + " : " + (juridique.recouvrement ? juridique.debiteur : juridique.site))
                    if(auth.id != juridique.user_id){
                        const users = [{
                            id: juridique.user_id, 
                            address: juridique.user_email, 
                            name: juridique.user_email
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(equipement_id){
            setLoading(true)
            axios.get('/api/equipement/show/' + equipement_id, useToken())
            .then((res) => {
                if(isMounted){
                    const equipement = res.data
                    setObjet("Equipement : " + (equipement.site ? equipement.site : equipement.sup_nom + " (" + equipement.sup_email + ")"))
                    if(auth.id != equipement.user_id){
                        const users = [{
                            id: equipement.user_id, 
                            address: equipement.sup_email, 
                            name: equipement.sup_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(visite_id){
            setLoading(true)
            axios.get('/api/visite_poste/show/' + visite_id, useToken())
            .then((res) => {
                if(isMounted){
                    const visite = res.data
                    setObjet("Visite de poste : " + visite.site)
                    if(auth.id != visite.user_id){
                        const users = [{
                            id: visite.user_id, 
                            address: visite.user_email, 
                            name: visite.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(fait_id){
            setLoading(true)
            axios.get('/api/fait_marquant/show/' + fait_id, useToken())
            .then((res) => {
                if(isMounted){
                    const fait = res.data
                    setObjet("Fait marquant : " + fait.site)
                    if(auth.id != fait.user_id){
                        const users = [{id: fait.user_id, address: fait.user_email, name: fait.user_nom}]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(juridique_id){
            setLoading(true)
            axios.get('/api/juridique/show/' + juridique_id, useToken())
            .then((res) => {
                if(isMounted){
                    const juridique = res.data
                    setObjet((juridique.recouvrement ? "Recouvrement client" : "Plainte agent") + " : " + fait.site)
                    if(auth.id != fait.user_id){
                        const users = [{id: fait.user_id, address: fait.user_email, name: fait.user_nom}]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(sanction_id){
            setLoading(true)
            axios.get('/api/sanction/show/' + sanction_id, useToken())
            .then((res) => {
                if (isMounted) {
                    const sanction = res.data
                    setObjet("Sanction : " + sanction.site)
                    if(auth.id != sanction.user_id){
                        const users = [{
                            id: sanction.user_id, 
                            address: sanction.sup_email, 
                            name: sanction.sup_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(prime_id){
            setLoading(true)
            axios.get('/api/prime/show/' + prime_id, useToken())
            .then((res) => {
                if(isMounted){
                    const prime = res.data
                    setObjet("Prime : " + prime.site)
                    if(auth.id != prime.user_id){
                        const users = [{
                            id: prime.user_id, 
                            address: prime.sup_email, 
                            name: prime.sup_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(absence_id){
            setLoading(true)
            axios.get('/api/absence/show/' + absence_id, useToken())
            .then((res) => {
                if(isMounted){
                    const absence = res.data
                    setObjet((absence.type_absence=="conge" ? "Congé" : absence.type_absence == "mis_a_pied"? "Mise à pied":"Permission")
                        + " : " + (absence.site ? absence.site : absence.user_nom + " (" + absence.user_email + ")"))
                    if(auth.id != absence.user_id){
                        const users = [{
                            id: absence.user_id, 
                            address: absence.user_email, 
                            name: absence.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(sav_id){
            setLoading(true)
            axios.get('/api/sav/show/' + sav_id, useToken())
            .then((res) => {
                if(isMounted){
                    const sav = res.data
                    setObjet("SAV : " + sav.site)
                    if(auth.id != sav.user_id){
                        const users = [{
                            id: sav.user_id, 
                            address: sav.user_email, 
                            name: sav.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(flotte_id){
            setLoading(true)
            axios.get('/api/flotte/show/' + flotte_id, useToken())
            .then((res) => {
                if(isMounted){
                    const flotte = res.data
                    setObjet("Flotte : " + (flotte.site ? flotte.site : flotte.user_nom + " (" + flotte.user_email + ")"))
                    if(auth.id != flotte.user_id){
                        const users = [{
                            id: flotte.user_id, 
                            address: flotte.user_email, 
                            name: flotte.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(appro_id){
            setLoading(true)
            axios.get('/api/approvisionnement/show/' + appro_id, useToken())
            .then((res) => {
                if(isMounted){
                    const da = res.data
                    setObjet("DA : " + da.objet.slice(0, 30))
                    if(auth.id != da.user_id){
                        const users = [{
                            id: da.user_id, 
                            address: da.user_email, 
                            name: da.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(part_variable_id){
            setLoading(true)
            axios.get('/api/part_variable/show/' + part_variable_id, useToken())
            .then((res) => {
                if(isMounted){
                    const employe = res.data
                    setObjet(
                        'Paie: ' + (getMatricule(employe) != 'Ndf'? (getMatricule(employe)  + ' ' + employe.employe): "Ndf")
                        + ' (' + moment(employe.date_paie).format("MMM YYYY") + ')'
                    )
                    const users = [{
                        id: employe.user_id, 
                        address: employe.user_email, 
                        name: employe.user_nom
                    }]
                    setUsers(users)
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if(employe_id){
            setLoading(true)
            axios.get('/api/employe/show/' + employe_id, useToken())
            .then((res) => {
                if(isMounted){
                    const employe = res.data
                    setObjet((getMatricule(employe) != 'Ndf' ? (getMatricule(employe) + ' ' + employe.nom) : "Ndf"))
                    setLoading(false)
                }
            })
            .catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if (deduction_id) {
            setLoading(true)
            axios.get('/api/deduction/show/' + deduction_id, useToken()).then((res) => {
                if (isMounted) {
                    const deduction = res.data
                    setObjet("Deduction : " + deduction.employe)
                    if (auth.id != deduction.user_id) { 
                        const users = [{
                            id: deduction.user_id,
                            address: deduction.user_email,
                            name : deduction.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            }).catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if (paie_id) {
            setLoading(true)
            axios.get('/api/paie/show/' + paie_id, useToken()).then(response => {
                if (isMounted) {
                    const paie = response.data
                    setObjet("Paie : " + paie.employe)
                    if (auth.id!= paie.user_id) { 
                        const users = [{
                            id: paie.user_id,
                            address: paie.user_email,
                            name : paie.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            }).catch(e => {console.error(e)
                setLoading(false)
            })        
        }
        else if (service24_id) {
            setLoading(true)
            axios.get('/api/service24/show/' + service24_id, useToken()).then(response => {
                if (isMounted) {
                    const service24 = response.data
                    setObjet("Service 24 : " + getMatricule(service24) + ' ' + service24.employe)
                    if (auth.id != service24.id) {
                        const users = [{
                            id: service24.user_id,
                            address: service24.user_email,
                            name : service24.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            }).catch(e => {
                console.error(e)
                setLoading(false)
            })
        }
        else if (avance_id) {
            setLoading(true)
            axios.get('/api/avance/show/' + avance_id, useToken()).then(response => { 
                if (isMounted) {
                    const avance = response.data
                    setObjet("Avance : " + getMatricule(avance) + ' '+ avance.employe)
                    if (auth.id != avance.user_id) {
                        const users = [{
                            id: avance.user_id,
                            address: avance.user_email,
                            name : avance.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
        }
        else if (reclamation_id) {
            setLoading(true)
            axios.get('/api/reclamation/show/' + reclamation_id, useToken()).then(response => {
                if (isMounted) {
                    const reclamation = response.data
                    setObjet("Reclamation : " + getMatricule(reclamation) + ' ' + reclamation.employe)
                    if (auth.id != reclamation.user_id) {
                        const users = [{
                            id: reclamation.user_id,
                            address: reclamation.user_email,
                            name : reclamation.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
        }
        else if (satisfaction_id) {
            setLoading(true)
            axios.get('/api/satisfaction/show/' + satisfaction_id, useToken()).then((response) => { 
                if (isMounted) {
                    const satisfaction = response.data.satisfaction
                    setObjet("Satisfaction : " + satisfaction.site_nom )
                    if (auth.id != satisfaction.user_id) {
                        const users = [{
                            id: satisfaction.user_id,
                            address: satisfaction.user_email,
                            name : satisfaction.user_nom
                        }]
                        setUsers(users)
                    }
                    setLoading(false)
                }
            })
        }
        return () => { isMounted = false };
    }, [])

    return <div className='modal'>
        <div>
            <form onSubmit={handleSubmit}>
                <div className="title-container">
                    <h2>Envoi email</h2>
                </div>
                {
                    isLoading ?
                        <LoadingPage/>
                    : <>
                        <InputMultipleUser users={users} setUsers={setUsers}/>
                        <InputText 
                            value={objet} 
                            label="Objet"
                            disabled/> 
                        <Textarea
                            required
                            label="Message"
                            value={message}
                            onChange={setMessage}/>
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                        }
                        <div className='form-button-container'>
                            <button className='btn-primary' type='submit' disabled={submitDisabled}>Envoyer</button>
                            <button onClick={() => closeModal()}>Annuler</button>
                        </div>
                    </>
                }
            </form>
        </div>
    </div>
}