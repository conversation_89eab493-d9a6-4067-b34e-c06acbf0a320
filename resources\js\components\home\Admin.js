import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Link, Navigate } from 'react-router-dom';
import useToken from '../util/useToken';
import MenuView from '../view/MenuView';

export default function Validateur({auth}) {
    const [nbData, setData] = useState(null) 

    useEffect(() => {        
        let isMounted = true
        axios.get('/api/admin', useToken())
        .then(res => {
            setData(res.data)
        })
        .catch(err => {
            console.error(err)
        })
        return () => { isMounted = false }
    }, [])
    return (
        auth.role == "admin" ?
            <MenuView title="">
                {
                    nbData && 
                    <div>
                        <h3 className='sub-title-menu'>ADMIN</h3>
                        <div>
                            <div className='palette-container'>                                
                                {
                                    nbData.nb_users != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/user/must_change_password">Utilisateurs jamais connecté</Link>
                                        <span className='badge-outline'>{nbData.nb_users}</span>
                                    </div>
                                }
                                {
                                    nbData.nb_all_users != 0 &&
                                    <div className='palette-item'>
                                        <Link className='link-no-style' to="/user">Tous les utilisateurs</Link>
                                        <span className='badge-outline'>{nbData.nb_all_users}</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
            </MenuView>
        :
            <Navigate to="/"/>
    );
}