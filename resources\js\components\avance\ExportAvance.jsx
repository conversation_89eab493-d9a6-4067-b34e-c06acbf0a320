import React, { useEffect, useState } from 'react'
import InputMonthYear from '../input/InputMonthYear';
import AvanceExcel from './AvanceExcel';
import useToken from '../util/useToken';

export default function ExportAvance({closeModal}) {
    const [datePaie, setDatePaie] = useState({ year: '', month: '' })
    const [searchButton, toggleSearchButton] = useState(true)
    const [exportButton, toggleExportButton] = useState(false)
    const [showElement, setShowElement] = useState(false)
    const [disabledSearch, toggleSearch] = useState(true)
    const [numberAvance, setNumberAvance] = useState(0)
    const [avances, setAvances] = useState([])
    
    useEffect(() => {
        toggleSearch(!(datePaie.year.toString().trim() && datePaie.month.trim()))
        if (datePaie.year.toString().trim() && datePaie.month.trim()) {
            toggleSearchButton(true)
            toggleExportButton(false)
        }
        else if (avances && avances.length > 0) {
            toggleExportButton(true)
            toggleSearchButton(false)
        }
    }, [datePaie])

    const onSearch = () => {
        let date_paie = datePaie.year + '-' + datePaie.month + '-20';
        axios.get('/api/avance?status=done' + '&date_paie=' + date_paie, useToken())
            .then((res) => {
                setNumberAvance(res.data.avances.length);
                setAvances(res.data.avances);
                setShowElement(true)
                if (res.data.avances.length > 0) {
                    toggleSearchButton(false)
                    toggleExportButton(true)
                }
            })
    }
    
    return (
        <div className='modal'>
            <div>
                <h3>Export Avance</h3>
                <div style={{ marginBottom: 50 }}>
                    <InputMonthYear setDefaultDate label="Date Paie" value={datePaie} onChange={setDatePaie} required />
                </div>
                {showElement &&
                    <div >
                        {numberAvance + " élement(s) trouvée(s)"}
                    </div>
                }
                <div className="form-button-container">
                    {searchButton &&
                        <button onClick={() => onSearch()} className='btn btn-primary' disabled={disabledSearch}>Chercher</button>
                    }
                    {exportButton && <AvanceExcel avances={avances} datePaie={datePaie.year.toString() + "-" + datePaie.month} />}
                    <button type="button" onClick={closeModal}>
                        Annuler
                    </button>
                </div>
            </div>
        </div>
    )
}
