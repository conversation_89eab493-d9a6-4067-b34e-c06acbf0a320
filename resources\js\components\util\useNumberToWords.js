import { useState, useEffect } from 'react';

export default function useNumberToWords(x) {
    const [numberInWords, setNumberInWords] = useState('')

    useEffect(() => {
        const convertNumberToWords = (number) => {
        const words = [
            'zéro', 'un', 'deux', 'trois', 'quatre', 'cinq', 'six', 'sept', 'huit', 'neuf',
            'dix', 'onze', 'douze', 'treize', 'quatorze', 'quinze', 'seize', 'dix-sept', 'dix-huit', 'dix-neuf'
        ]
        const tensWords = [
            '', '', 'vingt', 'trente', 'quarante', 'cinquante', 'soixante', 'soixante-dix', 'quatre-vingt', 'quatre-vingt-dix'
        ]

        const convertIntegerToWords = (integer) => {
            if (integer === 0) {
                return words[0]
            }
            let result = ''
            if (integer >= 1000) {
                result += `${convertIntegerToWords(Math.floor(integer / 1000))} mille `
                integer %= 1000
            }
            if (integer >= 100) {
                result += `${convertIntegerToWords(Math.floor(integer / 100))} cent `
                integer %= 100
            }
            if (integer >= 20) {
                const tens = Math.floor(integer / 10)
                const units = integer % 10

                if (tens === 7 || tens === 9) {
                    result += `${tensWords[tens]}-`
                    integer -= tens * 10
                } else {
                    result += `${tensWords[tens]}`
                    if (units !== 0) {
                    result += `-${words[units]}`
                    }
                    integer = 0
                }
            }
            if (integer > 0) {
                result += `${words[integer]}`
            }

            return result
        }

        const convertNumberToString = (number) => {
            const integerPart = Math.floor(number);
            const decimalPart = Math.round((number - integerPart) * 10);
            let result = '';

            if (integerPart !== 0) {
                result += convertIntegerToWords(integerPart) + ' jour'
                if (integerPart > 1) {
                    result += '(s)'
                }
            } else {
                result += 'zéro jour'
            }
            if (decimalPart !== 0) {
                result += ` et demi`
            }
            return result
        }

        const numberAsString = convertNumberToString(number)
        setNumberInWords(numberAsString)
        }

        convertNumberToWords(x)
    }, [x])

    return numberInWords
}