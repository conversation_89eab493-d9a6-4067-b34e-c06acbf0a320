import React, { useEffect, useState } from 'react'
import Input<PERSON>ont<PERSON><PERSON><PERSON> from '../input/InputMonthYear'
import InputSite from '../input/InputSite'
import useToken from '../util/useToken'
import PlanningExcel from './PlanningExcel'
import InputCheckBox from '../input/InputCheckBox'
import LoadingPage from '../loading/LoadingPage'

export default function ModalExportPlanning({ closeModal, auth }) {
    const [datePlanning, setDatePlanning] = useState({ year: '', month: '' })
    const [showElement, setShowElement] = useState(false)
    const [plannings, setPlannings] = useState([])
    const [site, setSite] = useState(null)
    const [showExport, toggleExport] = useState(false)
    const [groupbySuperviseur, setGroupbySuperviseur] = useState(false)
    const [loading, toggleLoading] = useState(false)

    const onSearch = () => {
        toggleExport(false)
        toggleLoading(true)
        let isMounted = true
        let date_planning = datePlanning.year + '-' + datePlanning.month
        let params = new URLSearchParams()
        params.set('date_planning', date_planning)
        if (site) {
            params.set('site_id', site.id)
        }
        axios.get('/api/planning/get_data_to_print?' + params, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.plannings) {
                        if (res.data.plannings.length > 0) toggleExport(true)
                        setPlannings(res.data.plannings)
                        setShowElement(true)
                    }
                    toggleLoading(false)
                }
            })
            .catch(err => {
                console.error(err)
                toggleLoading(false)
            })
        return () => { isMounted = false; }
    }

    useEffect(() => {
        if (datePlanning.year && datePlanning.month)
            onSearch()
    }, [datePlanning, site])

    return (
        <div className='modal'>
            <div>
                <h3>Export Planning</h3>
                <div>
                    <InputMonthYear setDefaultDate label="Date planning" value={datePlanning} onChange={setDatePlanning} required />
                    <InputSite actionUrl={(auth.role == 'resp_sup' && auth.id != 55) ? "/api/site/search_by_resp_sup" : null} value={site} onChange={setSite} />
                </div>
                {loading ?
                    <LoadingPage />
                    :
                    <>
                        {showElement &&
                            <div style={{ marginBottom: 10, marginTop: 10 }}>
                                {plannings.length + " élement(s) trouvée(s)"}
                            </div>
                        }
                        {
                            ['resp_sup', 'resp_op', 'resp_rh'].includes(auth.role) &&
                            <InputCheckBox label="Grouper par superviseur" value={groupbySuperviseur} onChange={setGroupbySuperviseur} />
                        }
                        <div className="form-button-container">
                            {showExport && <PlanningExcel toggleLoading={()=>toggleLoading(true)} groupbySuperviseur={groupbySuperviseur} plannings={plannings} datePlanning={datePlanning} closeModal={closeModal} />}
                            <button type="button" onClick={closeModal}>
                                Annuler
                            </button>
                        </div>
                    </>
                }
            </div>
        </div>
    )
}
