import React, { useEffect, useState } from 'react';
import { useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import LoadingPage from '../loading/LoadingPage';
import InfiniteScroll from 'react-infinite-scroll-component';
import { FiMoreVertical } from 'react-icons/fi';
import moment from 'moment';
moment.locale('fr')
import "./suivi.css";

export default function SuiviJuiridique({recouvrement, auth, suivis, setSuivis, currentId, setCurrentId}) {
    const locationSearch = useLocation().search
    const urlParams = new URLSearchParams(locationSearch)
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)

    const handleSeenAll = () => {
        toggleLoading(true)
        axios.post('/api/seen/' + (recouvrement ? "juridique_all" : "plainte_all") + "?id=" + suivis[0].id, null, useToken())
        .then((res) => {
            if(res.data.success){
                updateData(true)
            }
            else 
                toggleLoading(false)
        })
        .catch(() => {
            toggleLoading(false)
        })
    }
        
    const updateData = (initial) => {
        let isMounted = true;
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            urlParams.set("offset", 0)
        }
        else
            urlParams.set("offset", suivis.length)

        axios.get("/api/suivi_juridique/unread_" + (recouvrement ? "recouvrement": "plainte") + "?" + urlParams ,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial)
                        setSuivis(res.data.suivis)
                    else {
                        const list = suivis.slice().concat(res.data.suivis)
                        setSuivis(list)
                    }
                    setDataLoaded(res.data.suivis.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }
    
    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    return <> {
        isLoading ?
            <LoadingPage/>
        : 
            <div>
                <div className="padding-container space-between">
                    <h2>{recouvrement ? "Recouvrement" : "Plainte"} non lu</h2>
                    {
                        (urlParams.size == 0 && suivis.length > 0) && 
                        <span className='btn btn-outline-secondary pointer' onClick={handleSeenAll}>Marquer tout comme lu</span>
                    }
                </div>
                {
                    suivis.length == 0 ? 
                        <h3 className='center secondary'>Aucun données trouvé</h3>
                    :
                        <InfiniteScroll
                            dataLength={suivis.length}
                            next={fetchMoreData}
                            hasMore={!allDataLoaded}
                            loader={<LoadingPage/>}
                        >
                            {
                                suivis.map((j) => (
                                    <div className={`card-container ${currentId && currentId == j.id ? 'selected' : ''}`} key={j.id}>
                                        <div className='badge-container'>
                                            <h3>
                                                {
                                                    j.seen ?
                                                        <span className='badge-outline'>Lu</span>
                                                    :
                                                        (recouvrement ? j.debiteur : j.site)
                                                }
                                            </h3>
                                            <span className='pointer' onClick={() => setCurrentId(j.id)}>
                                                <FiMoreVertical size={20} color="#888"/>
                                            </span>
                                        </div>
                                        <div>
                                            {
                                                !recouvrement &&
                                                <p style={{whiteSpace: "pre-line"}}>
                                                    Agent(s) concerné : <span className='text'>{j.agent}</span>
                                                </p>
                                            }
                                            <p style={{whiteSpace: "pre-line"}}>
                                                {
                                                    recouvrement ?
                                                        <>Contrat : <span className='text'>{j.contrat}</span></>
                                                    :
                                                        <>Rappelle des faits : <span className='text'>{j.fait}</span></>
                                                }
                                            </p>
                                            <div className={'citation-container citation-' + j.status_color}>
                                                <p style={{whiteSpace: "pre-line", fontStyle: "italic"}}>
                                                    <span className='text'>{j.commentaire}</span>   
                                                </p>
                                                <div className='card-footer'>
                                                    <span>
                                                        <span></span>
                                                        <span className='text'>{j.user_nom} {" <" + j.user_email + ">"}</span>
                                                    </span>
                                                    <span className='secondary'>{moment(j.created_at).from(auth.datetime)}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))
                            }
                        </InfiniteScroll>
                }
            </div>
    } </>
}