import React, { useState } from 'react';

import { Link } from 'react-router-dom';
import NoteModal from '../input/NoteModal';

export default function ActionFlotte({auth, flotte, updateData}) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [currentAction, setAction] = useState(null)

    const handleCancelFlotte = (id) => {
        setAction({
            header: "Annuler la demande de flotte",
            request: "/api/flotte/cancel_flotte/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleNoteFlotte = (id) => {
        setAction({
            header: "Note",
            request: "/api/flotte/note/" + id,
            required: true
        })
        toggleNoteModal(true)
    }


    const handleCancelValidation = (id) => {
        setAction({
            header: "Annuler la demande de validation",
            request: "/api/flotte/cancel_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleRequestValidation = (id) => {
        setAction({
            header: "Demande de validation",
            request: "/api/flotte/request_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleReplyValidation = (id) => {
        setAction({
            header: "Réponse à la demande",
            request: "/api/flotte/reply_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    
    const handleDone = (id) => {
        setAction({
            header: "Terminer le traitement de la demande de flotte",
            request: "/api/flotte/save_done/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction} 
                updateData={() => updateData(true)} 
                closeModal={() => toggleNoteModal(false)}/> 
        }
        <div className='action-container'>
            {
                (['demande'].includes(flotte.status) && ["tech"].includes(auth.role)) &&
                <span onClick={() => handleNoteFlotte(flotte.id)}>
                    Accuser réception
                </span>
            }
            {
                (['demande','traite'].includes(flotte.status) && ["tech"].includes(auth.role)) &&
                <span onClick={() => handleRequestValidation(flotte.id)}>Validation</span>
            }
            {
                (["validation"].includes(flotte.status) && ["validateur"].includes(auth.role)) &&
                <span onClick={() => handleReplyValidation(flotte.id)}>Répondre</span>
            }
            {
                (["validation"].includes(flotte.status) && ["tech"].includes(auth.role)) &&
                <span onClick={() => handleCancelValidation(flotte.id)}>Annuler la demande de validation</span>
            }
            {
                ((['demande'].includes(flotte.status) && ["tech"].includes(auth.role)) || 
                (['demande'].includes(flotte.status) && auth.id == flotte.user_id && ["superviseur","resp_sup","resp_op"].includes(auth.role))) &&
                <span onClick={() => handleCancelFlotte(flotte.id)}>Annuler la demande</span>
            }
            {
                (["draft"].includes(flotte.status) && auth.id == flotte.user_id && ["superviseur","resp_sup","resp_op"].includes(auth.role)) && 
                <span>
                    <Link to={"/flotte/send_back/" + flotte.id}>Renvoyer</Link>
                </span>
            }
            {
                (["demande","traite"].includes(flotte.status) && ["tech"].includes(auth.role)) && 
                <span onClick={() => handleDone(flotte.id)}>Terminer</span>
            }
        </div>
    </div>
}