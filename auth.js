const { default: axios } = require("axios")
const FormData = require("form-data")
const https = require('https')
const agent = new https.Agent({
    rejectUnauthorized: false
})
exports.db_config_ovh = {
	host: "************",
	port: "3307",
	user: "tls",
	database: "tls_alarm",
	password: "#$NoGGOvH1@tls2023"
}

exports.db_config_zo = {
	host: "*************",
	port: "3371",
	user: "tls",
	database: "tls",
	password: "AdmDir2025"
}

exports.db_config_local_tls = {
	host: "127.0.0.1",
	port: "3306",
	user: "tls",
	database: "tls",
	password: "123456"
}

exports.db_config_ipbx = {
	host: "**************",
	port: "3306",
	user: "api",
	database: "cdr",
	password: "AdmDir2024"
}

exports.db_config_admin = {
	host: "************",
	port: "3306",
	user: "admin",
	database: "admin",
	password: "#$DminMyDrX1@2023"
}

exports.db_config_admin_test = {
	host: "localhost",
	port: "3306",
	user: "root",
	database: "admin_latest",
	password: "admin"
}

exports.db_config_tls_formation = {
	host: "************",
	port: "3306",
	user: "tls",
	database: "tls_alarm",
	password: "Srv$$OvH@tls2023"
}

exports.db_config_tlsf_test = {
	host: "localhost",
	port: "3306",
	user: "root",
	database: "tls_formation",
	password: "admin"
}

exports.db_config_gps = {
	host: "************",
	user: "tls",
	database: "gps",
	password: "#$DminMyDrX3@2023"
}
exports.email_config = {
	host: "ssl0.ovh.net",
	port: 465,
	secure: true,
	auth: {
		user: "<EMAIL>",
		pass: "ArTl$DrXP4$21"
	},
	tls: {
		rejectUnauthorized: false
	}
}
exports.rapport_config = {
	host: "ssl0.ovh.net",
	port: 465,
	secure: true,
	auth: {
		user: "<EMAIL>",
		pass: "ArTl$DrXP4$21"
	},
	tls: {
		rejectUnauthorized: false
	}
}
exports.tokenTest = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************-hV2iveq7L9Zj7NN9PQAU8alYu6TXzjSIhpP-GNUNAwP5Jf2jHNYhMtfDtHi1xlZaYSf2dNWNfEOEC5LsgNXE2R4RtEgzA9fm5cT5dU5afLC7sE4s_bZq1vxwjqTN6fw2MnR2nqFAh2NhZGGMXmnawCzSAa_SO2IkcY6MJxzz8BELRDDOi1lNTtP-M_HBMAs_DkIDhPk1UDu63cOJzBafztN7oxxuJ8jUyd1TqbfGIpoipam60Nie9ChlLO-RSQjmcq1ZraX70pPjEX4Z6sIkormulmR7OUrYp-or5noUOOyKkiHEfU5kXX9UyrAKYz7O9Sm0pr3ueLYOfAChBqenQAsMQEc2EZbJ3FBlqNHd0mweb4xSunTk1avtx7_x2g6MI4caEM8DiKQ2AYyBC44ZQU2qV9KOAY53LH1-GUlkIYculwYPRI5j1FjTE5BFeY1VqPkeHAxdzXXA4d5TM0E6M1steG1dkcWz3OirAqBOVaWjePLm_wTDP0ufFLhIIMcVkjtHb1LWLbPOkICqesKXmYQo5-w5I4fAsVdEEzD-3rSaOAB4NkMk_XN74Md_tQPcwf2TpxGBueRHfsOZRBQw"
exports.tokenAdmin = "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
exports.formDataOption = {
	headers: 
		{
			"Content-Type": 'multipart/form-data',
			'Authorization': 'Bearer ' + this.tokenAdmin
		}
}

exports.sendMail = (poolAdmin, destination, subject, text, attachements, callback, task) => {
	const sqlSelectUserDestination = (emails) => {
		return "SELECT u.id FROM users u " +
			"LEFT JOIN users ur ON ur.id = u.real_email_id " +
			"WHERE u.type in ('fictif', 'unique') " +
			"and (u.email in (" + emails.map(e => "\'" + e + "\'").join(",") + ") " +
			"or ur.email in (" + emails.map(e => "\'" + e + "\'").join(",") + "))"
	}
	poolAdmin.query(sqlSelectUserDestination(destination), [], async (err, receivers) => {
		if(err)
			console.error(err)
		else {
			let data = new FormData()
			data.append("objet", subject)
			data.append("content", text)
			attachements.forEach((file, index) => {
				data.append(`files[${index}]`, file.content, file.filename)
			})
			receivers.forEach((u, index) => {
				data.append(`receivers[${index}]`, u.id)
			})
			axios.post("https://app.dirickx.mg:8001/api/message/add", data, {
				httpsAgent:agent,
				headers: 
					{
						...data.getHeaders(),
						'Authorization': 'Bearer ' +  this.tokenAdmin
					}
			})
			.then(({data}) => {
				console.log(data)
				console.log("pointage export send successfully...")
				callback(true)
			})
			.catch((e) => {
				console.log(e)
				console.log("pointage export send error!")
				callback(false)
			})
		}
	})
}
