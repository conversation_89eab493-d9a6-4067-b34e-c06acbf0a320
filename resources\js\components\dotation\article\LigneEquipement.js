import React, { useEffect, useState } from 'react';
import LoadingPage from '../../loading/LoadingPage';
import useToken from '../../util/useToken';

export default function LigneEquipement({ auth, value, updateData }) {
    const [isLoading, toggleLoading] = useState(true);
    const [articles, setArticles] = useState([]);

    useEffect(() => {
        let isMounted = true;
        axios.get("/api/ligne_equipement/article/" + value.id, useToken())
        .then((res) => {
            if (isMounted) {
                setArticles(res.data);
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e);
        })
        return () => { isMounted = false}
    }, [])

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <div>
                    {
                        articles.map((ac) => (
                            <div key={ac.id} className='article-container'>
                                <span>{ac.nom_article}</span>
                                <div>
                                    <span className='badge-outline'>
                                        {
                                            ac.status == "direct" && value.type == "sortie" ? "transferé"
                                            : ac.status == "direct" && value.type == "entré" ? "retourné"
                                            : ac.status
                                        }
                                    </span>
                                    {
                                        ac.estUsé == 1 &&
                                        <span className='badge-outline'>usé</span>
                                    }
                                    {
                                        ac.estDeduit == 1 &&
                                        <span className='badge-outline'>deduit</span>
                                    }
                                </div>
                            </div>
                        ))
                    }
                </div>
        }
    </>
}
