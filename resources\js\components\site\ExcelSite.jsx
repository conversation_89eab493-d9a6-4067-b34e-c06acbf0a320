import React, { useEffect } from 'react'
import ExcelJS  from 'exceljs';
import { upperCase } from 'lodash';

export default function ExcelSite({auth, sites, type, closeModal}) {
    const workbook = new ExcelJS.Workbook();
    workbook.creator = "ADMIN";

    const createWorksheet = (datas, worksheet) =>{
        const headers = ['NOM', 'ADRESSE', 'HEURE FACTURE', 'AGENT JOUR', 'AGENT NUIT']
        if (type === "manager") {
            headers.push('SUPERVISEUR', 'SECTEUR');
        } 
        else if (type === "superviseur") {
            headers.push('MANAGER', 'SECTEUR');
        } 
        else if (type === "secteur") {
            headers.push('MANAGER', 'SUPERVISEUR');
        }
        
        worksheet.addRow(headers);
        datas.map((data, index) =>{
            if(type == "manager"){
                worksheet.addRow([data.nom, data.adresse, data.total_hour, data.nb_agent_day, data.nb_agent_night, data.superviseur, data.secteur]);
            }
            else if(type == "superviseur"){
                worksheet.addRow([data.nom, data.adresse, data.total_hour, data.nb_agent_day, data.nb_agent_night, data.manager, data.secteur]);
            }
            else if(type == "secteur"){
                worksheet.addRow([data.nom, data.adresse, data.total_hour, data.nb_agent_day, data.nb_agent_night, data.manager, data.superviseur]);
            }
            worksheet.getColumn('A').width = 50
            worksheet.getColumn('B').width = 60
            worksheet.getColumn('C').width = 15
            worksheet.getColumn('D').width = 15
            worksheet.getColumn('E').width = 15
            worksheet.getColumn('F').width = 30
            worksheet.getColumn('G').width = 30
            worksheet.getCell(`C${index}`).alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.getCell(`D${index}`).alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.getCell(`E${index}`).alignment = { horizontal: 'center', vertical: 'middle' };
        })
        worksheet.views = [
            {
                state: 'frozen',
                xSplit: 1,
                ySplit: 1,
            },
        ];
        worksheet.getRow(1).font = { bold: true };
    }
    const excel = () => {
        Object.keys(sites).forEach((key) => {
            const elements = sites[key];
            const worksheet = workbook.addWorksheet(key == "null" ? "NON_DEFINI" : key);
            createWorksheet(elements, worksheet);
        });
    }
    useEffect(() => {
        excel()
    }, [])
    const onSubmit = () => {
        workbook.xlsx.writeBuffer().then((buffer) => {
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = "SITES_BY_" + upperCase(type) + ".xlsx";
            a.click();
        });
        closeModal()
    }
    return (
        <button className="btn btn-primary" onClick={() =>{ excel(), onSubmit() }}>
            Exporter
        </button>
    )
}
