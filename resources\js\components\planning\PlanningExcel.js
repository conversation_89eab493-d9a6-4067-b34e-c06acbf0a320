import React, { useEffect, useState } from 'react'
import ExcelJS from 'exceljs'
import moment from 'moment';
import { forEach, upperCase } from 'lodash';
import matricule from '../util/matricule';

export default function PlanningExcel({ groupbySuperviseur, plannings, datePlanning, closeModal, actionButton, toggleLoading }) {
    const numberDays = moment(`${datePlanning.year}-${datePlanning.month}`, 'YYYY-MM').daysInMonth()
    const workbook = new ExcelJS.Workbook()
    const [reformatPlannings, setReformatPlannings] = useState([])
    const [rfData, setRfData] = useState([])
    workbook.creator = "ADMIN DIRICKX";

    useEffect(() => {
        const allData = []
        plannings.forEach((pl) => {
            let ptg = []
            if (pl.pointages?.length > 0) {
                ptg = initFormPlanning(datePlanning)
                pl.pointages.forEach((pointage) => {
                    const date = moment(pointage.date_pointage, 'YYYY-MM-DD HH:mm:ss');
                    const existing = ptg.find((p) => p.service === date.format('YYYY-MM-DD HH:mm:ss'));
                    if (pl.id == pointage.planning_id) {
                        existing?.employes?.push({
                            id: pointage.agent_id,
                            matricule: matricule(pointage),
                            nom: pointage.nom,
                        })
                    }
                })
            }
            allData.push(
                {
                    id: pl.id,
                    site: pl.site,
                    site_id: pl.idsite,
                    pointages: ptg
                }
            )
        })
        setReformatPlannings(allData)
    }, [plannings])
    
    useEffect(() => {
        const reformatData = []
        plannings.forEach(pl => {
            pl.pointages?.forEach(ptg => {
                const existingUser = reformatData.find(dt => dt.agent_id == ptg.agent_id && dt.site_id == pl.idsite)
                if (existingUser) {
                    existingUser.service.push(ptg.date_pointage)
                }
                else {
                    reformatData.push({
                        agent_id: ptg.agent_id,
                        site_id: pl.idsite,
                        site: pl.site,
                        nom: ptg.nom,
                        contrat: (pl.nb_agent_day_planning > 0 ? (pl.nb_agent_day_planning + 'J / ') : '') + (pl.nb_agent_night_planning > 0 ? (pl.nb_agent_night_planning + 'N ' + (pl.horaire ? ' /' : '')) : '') + (pl.horaire ? (pl.horaire) : ''),
                        matricule: matricule(ptg),
                        service: [ptg.date_pointage],
                        superviseur: pl.superviseur
                    })
                }
            })
        })
        setRfData(reformatData)
    }, [plannings])

    const initFormPlanning = (datePlanning) => {
        if (datePlanning && datePlanning?.year && datePlanning?.month) {
            const planningTemps = []
            for (let i = 1; i <= numberDays; i++) {
                const date = moment(`${datePlanning.year}-${datePlanning.month}-${i}`, 'YYYY-MM-DD');
                planningTemps.push({
                    id: i + "J",
                    day: i,
                    nom: date.format('ddd'),
                    service: (date.clone().set({ hour: 6, minute: 0, second: 0 })).format('YYYY-MM-DD HH:mm:ss'),
                    employes: [],
                })
                planningTemps.push({
                    id: i + "N",
                    day: i,
                    nom: date.format('ddd'),
                    service: (date.clone().set({ hour: 18, minute: 0, second: 0 })).format('YYYY-MM-DD HH:mm:ss'),
                    employes: [],
                })
            }
            return planningTemps
        }
    }


    const toDataPlanning = () => {
        const dataPlanning = [];
        const trierParSuperviseur = (rfData) => {
            return rfData.sort((a, b) => {
                if (a.superviseur < b.superviseur) {
                    return -1;
                }
                if (a.superviseur > b.superviseur) {
                    return 1;
                }
                return 0;
            });
        };
        const regrouperParSuperviseur = (rfData) => {
            return rfData.reduce((acc, current) => {
                const { superviseur } = current;
                if (!acc[superviseur]) {
                    acc[superviseur] = [];
                }
                acc[superviseur].push(current);
                return acc;
            }, {});
        };
    
        const rfBySuperviseur = trierParSuperviseur(rfData);
        if (groupbySuperviseur) {
            const groupedData = regrouperParSuperviseur(rfBySuperviseur);
            for (const superviseur in groupedData) {
                const group = groupedData[superviseur];
                const groupData = [];
                group.forEach(rf => {
                    let data = [];
                    let countIndexHour = 0;
                    rf.service.forEach((dt) => {
                        for (let i = 0; i < numberDays; i++) {
                            if (i == moment(dt).format('D') - 1) {
                                if (moment(dt).format('HH:mm:ss') == "18:00:00") {
                                    countIndexHour += 1;
                                    data[i] = 'Nuit';
                                    const found = rf.service.filter(dat => dat.startsWith(moment(dt).format('YYYY-MM-DD'))).length;
                                    if (found > 1) {
                                        data[i] = 'Service24'
                                    }
                                } else {
                                    countIndexHour += 1;
                                    data[i] = 'Jour';
                                    const found = rf.service.filter(dat => dat.startsWith(moment(dt).format('YYYY-MM-DD'))).length;
                                    if (found > 1) {
                                        data[i] = 'Service24'
                                    }
                                }
                            } else if (data[i] != 'Nuit' && data[i] != 'Jour' && data[i] != 'Service24') {
                                data[i] = '';
                            }
                        }
                    });
                    data.push(countIndexHour * 12);
                    groupData.push([rf.superviseur ?? '', rf.site, rf.contrat, rf.nom, rf.matricule].concat(data));
                });
                dataPlanning.push(groupData);
            }
        }
        else {
            rfBySuperviseur.forEach(rf => {
                let data = []
                let countIndexHour = 0
                rf.service.forEach((dt) => {
                    for (let i = 0; i < numberDays; i++) {
                        if (i == moment(dt).format('D') - 1) {
                            if (moment(dt).format('HH:mm:ss') == "18:00:00") {
                                countIndexHour += 1
                                // const found = rf.service.filter(dat => dat.startsWith(moment(dt).format('YYYY-MM-DD'))).length;
                                if (data[i]) {
                                    data[i] = 'Service24'
                                }
                                else
                                    data[i] = 'Nuit'
                            }
                            else {
                                countIndexHour += 1
                                // const found = rf.service.filter(dat => dat.startsWith(moment(dt).format('YYYY-MM-DD'))).length;
                                if (data[i]) {
                                    data[i] = 'Service24'
                                }
                                else
                                    data[i] = 'Jour'
                            }
                        }
                        else if (data[i] != 'Nuit' && data[i] != 'Jour' && data[i] != 'Service24')
                            data[i] = '';
                    }
                })
                data.push(countIndexHour * 12)
                dataPlanning.push([rf.superviseur ?? '', rf.site, rf.contrat, rf.nom, rf.matricule].concat(data));
                data = []
            });
            let last_sup = null
            dataPlanning.forEach(dt => {
                if (!last_sup) last_sup = dt[0]
            });
        }
        return dataPlanning;
    };


    const excel = (dataPlanning, worksheet) => {

        const headers = ['Site', 'Contrat', 'Agent', 'Matricule']
        const days = ['Site', 'Contrat', 'Agent', 'Matricule']
        let index = 0
        for (let i = 0; i < reformatPlannings.length; i++) {
            if (reformatPlannings[i].pointages.length > 0) {
                index = i
                break
            }
        }
        reformatPlannings[index].pointages.forEach((ptg, index) => {
            if ((index + 1) % 2 > 0) {
                headers.push((ptg.nom.charAt(0)).toUpperCase())
                days.push(ptg.day)
            }
        });

        let lastSite = '';
        let currentRow = 1;
        dataPlanning.forEach((element, index) => {
            element = element.slice(1)
            if (element[0] == lastSite) {
                worksheet.addRow(element);
                currentRow++;
            }
            else {
                let startRow = currentRow;
                if (index > 0) {
                    startRow = currentRow + 3;
                    worksheet.addRow([]);//blank row
                    worksheet.addRow([]); //blank row
                }
                worksheet.addRow(headers);
                worksheet.addRow(days);

                worksheet.mergeCells(`A${startRow}:A${startRow + 1}`);
                worksheet.mergeCells(`B${startRow}:B${startRow + 1}`);
                worksheet.mergeCells(`C${startRow}:C${startRow + 1}`);
                worksheet.mergeCells(`D${startRow}:D${startRow + 1}`);

                worksheet.getCell(`A${startRow}`).alignment = { horizontal: 'center', vertical: 'middle' };
                worksheet.getCell(`B${startRow}`).alignment = { horizontal: 'center', vertical: 'middle' };
                worksheet.getCell(`C${startRow}`).alignment = { horizontal: 'center', vertical: 'middle' };
                worksheet.getCell(`D${startRow}`).alignment = { horizontal: 'center', vertical: 'middle' };

                worksheet.addRow(element);

                currentRow = startRow + 2;
            }
            lastSite = element[0];
        });

        worksheet.eachRow(row => {
            row.eachCell(cell => {
                cell.font = { size: 12 };
                cell.border = {
                    top: { style: 'thin', color: { argb: 'FF000000' } }, //black
                    left: { style: 'thin', color: { argb: 'FF000000' } },
                    bottom: { style: 'thin', color: { argb: 'FF000000' } },
                    right: { style: 'thin', color: { argb: 'FF000000' } }
                };
                if (cell.value == 'Jour') {
                    cell.value = 12
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFFF00' } //yellow
                    };
                }
                else if (cell.value == 'Nuit') {
                    cell.value = 12
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FF808080' } //grey
                    };
                }
                else if (cell.value == 'Service24') {
                    cell.value = 24
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FF27C5F9' } //blue
                    };
                }
            });
        });

        worksheet.getColumn('A').width = 30
        worksheet.getColumn('B').width = 25
        worksheet.getColumn('C').width = 40
        worksheet.getColumn('D').width = 20
        const workSheetLength = worksheet.rowCount
        if (worksheet.rowCount > 3) { //condition s'il y a de ligne a fusionner  à part la ligne d'entête
            let currentValue = ''
            let startRow = 3
            let sumHours = 0 //sommes des heures pour chaque site
            for (let i = 4; i <= (worksheet.rowCount + 4); i++) { //4 = 2 rows blanks + 2 rows for headers
                const cellValue = worksheet.getCell(`A${i}`).value;
                const columnHours = worksheet.getColumn(numberDays + 5).letter // 5 = column de ['site', 'contrat', 'agent', 'matricule'] +  colonne des heures de chaque ligne
                sumHours += worksheet.getCell(`${columnHours}${i - 2}`).value
                if (cellValue !== currentValue) {
                    if (startRow !== i - 1) {
                        let lastSumCell = null;
                        if (worksheet.getCell(`A${i - 1}`).value) {
                            worksheet.mergeCells(`A${startRow}:A${i - 1}`);
                            worksheet.mergeCells(`B${startRow}:B${i - 1}`);
                            lastSumCell = worksheet.getCell(`${columnHours}${i}`)
                        }
                        else
                            lastSumCell = worksheet.getCell(`${columnHours}${i - 2}`)

                        sumHours += worksheet.getCell(`${columnHours}${i - 1}`).value
                        worksheet.getCell(`A${startRow}`).alignment = { horizontal: 'left', vertical: 'middle' }
                        worksheet.getCell(`B${startRow}`).alignment = { horizontal: 'center', vertical: 'middle' }
                        lastSumCell.value = sumHours;
                        lastSumCell.fill = {
                            type: 'pattern',
                            pattern: 'solid',
                            fgColor: { argb: '80FF0000' } //red
                        };
                        if (worksheet.getCell(`A${i - 1}`).value) {
                            startRow = i + 4
                            i += 4
                        }
                        else {
                            startRow = i + 2
                            i += 2
                        }
                    }
                    sumHours = 0
                }
                else if (i > workSheetLength) break
                currentValue = cellValue;
            }
        }
    }

    const createAllExcel = () => {
        if (toggleLoading){
            console.log("setLoading")
            toggleLoading()
        }
        const dataPlanning = toDataPlanning()
        if (groupbySuperviseur) {
            forEach(dataPlanning, (element, index) => {
                excel(element, workbook.addWorksheet(element[0][0]))
            })
        }
        else {
            excel(dataPlanning, workbook.addWorksheet('Planning'))
        }
    }

    const onSubmit = () => {
        workbook.eachSheet((worksheet, sheetId) => {
            for (let i = 5; i <= worksheet.columns.length; i++) {
                const column = worksheet.getColumn(i);
                column.width = 3;
                column.font = { size: 10 }
            }
            worksheet.getColumn(numberDays + 5).width = 12
        });

        workbook.xlsx.writeBuffer().then((buffer) => {
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = "PLANNING_" + (plannings.length == 1 ? plannings[0].site + " " : "") + upperCase(moment(`${datePlanning.year}-${datePlanning.month}`).format("MMM YYYY")) + ".xlsx";
            a.click();
        });
        closeModal()
    }
    return (
        <>
            {
                actionButton ?
                    <span onClick={() => { createAllExcel(), onSubmit() }}>
                        Exporter
                    </span>
                    :
                    <button className="btn btn-primary" onClick={() => { createAllExcel(), onSubmit() }}>
                        Exporter
                    </button>
            }
        </>
    )
}
