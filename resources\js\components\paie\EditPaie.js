
import React, { useState, useEffect } from 'react'
import DualContainer from '../container/DualContainer';
import InputEmploye from '../input/InputEmploye';
import InputText from '../input/InputText';
import ButtonSubmit from '../input/ButtonSubmit';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import PrimePaie from './PrimePaie';
import LoadingPage from '../loading/LoadingPage';
import Resultat from './Resultat';
import Conge from './Conge';
import Charge from './Charge';
import Preavis from './Preavis';
import Deduction from './Deduction';
import Maj from './Maj';
import Notification from '../notification/Notification';
import { useParams } from 'react-router-dom';
import ModalImprimer from './Fiche/ModalImprimer';
import Payement from './Payement';
import Rajout from './Rajout';
import InputSelect from '../input/InputSelect';
import moment from 'moment';
import InputMonthYear from '../input/InputMonthYear';
import "../layout/tab.css"

export default function EditPaie({ action, auth, title, modify, button }) {
    const [toModify, setToModify] = useState(modify?true: false);
    const [notification, setNotification] = useState(null);
    const [employe, setEmploye] = useState(null);
    const [datePaie, setDatePaie] = useState(moment().toDate());
    const [heureContrat, setHeureContrat] = useState(0);
    const [heureTravaille, setHeureTravaille] = useState(0);
    const [activeMenu, setActiveMenu] = useState("prime");
    const [error, setError] = useState("");
    const [disabledSubmit, setDisabledSubmit] = useState(false);
    const [showFiche, toggleShowFiche] = useState(false);
    const [paie, setPaie] = useState(null);
    const [droit, setDroit] = useState(0);
    const banques = ["ACCES BANQUE", "BAOBAB BANQUE", "SIPEM BANQUE", "BOA", "BMOI", "BFV"].sort()
    const mobileMoneys = ["", "MVOLA", "AIRTEL MONEY", "ORANGE MONEY"].sort()
    const modePaiements = [{ value: 'VIR', label: 'Virement bancaire' }, { value: 'MOB', label: 'Mobile money' }]
    const [congePayer, setCongePayer] = useState(0)

    const getModePaiment = (value) => {
        let selectedMode = null
        modePaiements.forEach(mode => {
            if (mode.value == value)
                selectedMode = mode;
        });
        return selectedMode;
    }

    let params = useParams()
    const [nprv, setNprv] = useState({
        nprvPreavisDeductible: 0,
        nprvPreavisPayer: 0,
        nprvLicenciement: 0
    });
    const [preavis, setPreavis] = useState({
        preavisDeductible: 0,
        preavisPayer: 0,
        idmLicenciement: 0,
        preavisMoins: 0
    });

    const [primeState, setPrimeState] = useState({
        prime: 0,
        primeExceptionnelle: 0,
        idmDepl: 0,
        primeEntret: 0,
        primeAnc: 0,
        primeResp: 0,
        primeAssid: 0,
        primeDiv: 0,
        partVariable: 0,
        perdiem: 0,
    });

    const [resultat, setResultat] = useState({
        netImposable: 0,
        salaireMensuel: 0,
        salaireBrut: 0,
        totalProrataGrap: 0,
        netAPayer: 0,
        masseSalariale: 0,
    });

    const [conge, setConge] = useState({
        data:null,
        soldeConge: 0,
        droit: 0
    });
    const [congePris, setCongePris] = useState(0)
    const [charge, setCharge] = useState({
        irsa: 0,
        irsaPatronale: 0,
        cnaps: 0,
        cnapsPatronale: 0,
        salfa: 0,
        salfaPatronale: 0,
    });

    const [heureSup, setheureSup] = useState({
        hs30: 0,
        hs50: 0,
        mh_s30: 0,
        mh_s50: 0,
    });

    const [dataEmploye, setDataEmploye] = useState({});
    const [salBase, setSalBase] = useState(0);
    const [loadHeureTravaille, setLoadHeureTravaille] = useState(false);

    const [deduction, setDeduction] = useState({
        retenueFormation: 0,
        autreDeduction: 0,
        avance15: 0,
        avanceSpeciale: 0,
        avanceSpecialeEmbauche: 0,
    });

    const [rajout, setRajout] = useState({
        rappel: 0,
        allFamCnaps:0,
        rembFraisFixe: 0,
    });
    const [maj, setMaj] = useState({
        hFerie: 0,
        hmDim: 0,
        hMaj: 0
    });
    const [payement, setPayement] = useState({
        type: "",
        numeroTelephone: "",
        banque: "",
        codeBanque: "",
        codeGuichet: "",
        numeroCompte: "",
        mobileMoney:"",
        rib:"",
        cin: "",
    });
    const [showPayement, toggleShowPayement] = useState(false);

    const prorata = (value) => {
        let div_hcht = heureTravaille / heureContrat
        let diff_hcht = heureTravaille - heureContrat
        if (diff_hcht >= 0 || diff_hcht == -12) {
            return parseFloat(value);
        }
        else {
            return parseFloat(value * div_hcht);
        }
    }
    useEffect(() => {
        if (!toModify) {
            if (datePaie.month && datePaie.year && salBase > 0) {
                calculPaie();
        }
        }
    }, [employe, datePaie, heureTravaille, primeState, nprv, heureContrat, conge.soldeConge, salBase, deduction, maj, rajout, preavis.preavisMoins]);

    const calculPaie = () => {
        const data = new FormData();
        data.append('nb_heure_travaille', heureTravaille)
        data.append('nb_heure_contrat', heureContrat)
        data.append('sal_base', salBase)
        data.append('heure_ferie', maj.hFerie)
        data.append('hm_dim', maj.hmDim)
        data.append('h_maj', maj.hMaj)
        data.append('prime_exceptionnelle', maj.hMaj)
        data.append('deduction', deduction.autreDeduction)
        data.append('avance15', deduction.avance15)
        data.append('avc_speciale', deduction.avanceSpeciale)
        data.append('avc_embauche', deduction.avanceSpecialeEmbauche)
        data.append('prime', primeState.prime)
        data.append('part_variable', primeState.partVariable)
        data.append('conge_reste', droit)
        data.append('prime_anc', primeState.primeAnc)
        data.append('idm_depl', primeState.idmDepl)
        data.append('perdiem', primeState.perdiem)
        data.append('prime_assid', primeState.primeAssid)
        data.append('prim_div', primeState.primeDiv)
        data.append('prime_resp', primeState.primeResp)
        data.append('nprv_preavis_deductible', nprv.nprvPreavisDeductible)
        data.append('nprv_preavis_payer', nprv.nprvPreavisPayer)
        data.append('nprv_licenciement', nprv.nprvLicenciement)
        data.append('rappel', rajout.rappel)
        data.append('retenue_formation', deduction.retenueFormation)
        data.append('preavis_moins', preavis.preavisMoins)
        data.append('all_fam_cnaps', rajout.allFamCnaps)
        data.append('remb_frais_fixe', rajout.rembFraisFixe)
        data.append('get_data_only', 1)
        data.append('id', dataEmploye.id)
        data.append('numero_tel', payement.numeroTelephone)
        data.append('banque', payement.banque)
        data.append('code_banque', payement.codeBanque)
        data.append('code_guichet', payement.codeGuichet)
        data.append('numero_compte', payement.numeroCompte)
        data.append('nom', payement.mobileMoney)
        data.append('rib', payement.rib)
        data.append('cin', payement.cin)
        data.append('date_paie', datePaie.year + "-" + datePaie.month + "-20") 
        data.append('numero_employe', dataEmploye.numero_employe) 
        data.append('num_emp_soit', dataEmploye.num_emp_soit) 
        data.append('numero_stagiaire', dataEmploye.numero_stagiaire) 
        data.append('societe_id', dataEmploye.societe_id) 
        data.append('agence_id', dataEmploye.agence_id) 
        data.append('site_id', dataEmploye.site_id) 
        data.append('real_site_id', dataEmploye.real_site_id) 
        data.append('date_embauche', dataEmploye.date_embauche) 
        data.append('nb_heure_convenu', dataEmploye.nb_heure_convenu) 
        data.append('fonction_id', dataEmploye.fonction_id)
        data.append('sal_forfait', dataEmploye.sal_forfait)
        data.append('date_confirmation', dataEmploye.date_confirmation)
        data.append('categorie', dataEmploye.categorie)
        data.append('conge_paye', congePris)
        axios.post('/api/paie/get_only_calcul', data, useToken()).then((res) => {
            if (res.data.error) {
                console.error(res.data.error)
            }
            else {
                console.log('RES', res.data)
                let resultats = res.data
                setheureSup((curr) => {
                    return {
                        ...curr,
                        mh_s30: resultats.mh_s30,
                        hs50: resultats.hs50,
                        mh_s50: resultats.mh_s50,
                    }
                })

                setCharge(() => {
                    return {
                        ...charge,
                        irsa: resultats.irsa,
                        salfa: resultats.salfa,
                        cnaps: resultats.cnaps,
                        salfaPatronale: resultats.salfa_pat,
                        cnapsPatronale: resultats.cnaps_pat,
                    }
                })
                setResultat({
                    ...resultat,
                    netImposable: parseFloat(resultats.net_imposable).toFixed(2),
                    salaireMensuel: resultats.salaire_mensuel,
                    salaireBrut: resultats.salaire_brut,
                    totalProrataGrap: resultats.total_prorata_grat,
                    netAPayer: resultats.net_a_payer,
                    masseSalariale: resultats.masse_salariale
                })
                setPreavis({
                    ...preavis,
                    preavisDeductible: resultats.preavis_deductible,
                    preavisPayer: resultats.preavis_payer,
                    idmLicenciement: resultats.idm_licenciement
                })
                setheureSup({
                    ...heureSup,
                    hs30: resultats.hs30,
                    hs50: resultats.hs50,
                    mh_s30: resultats.mh_s30,
                    mh_s50: resultats.mh_s50,
                })
                setCongePayer(resultats.s_conge)
            }
        })
        // const resultats = calcul_paie ({
        //     heureTravaille: heureTravaille,
        //     heureContrat: heureContrat,
        //     salBase: salBase, 
        //     maj: maj,
        //     conge:conge, 
        //     nprv: nprv,
        //     rajout:rajout, 
        //     deduction:deduction, 
        //     primeState: primeState,
        //     preavis: preavis,
        // })
        // console.log('RESULT', resultats)

        // let diffHcHt = heureTravaille - heureContrat;
        // let divSalBaseHc = salBase / heureContrat;
        // let hsfr = 0;
        // let newSalaireMensuel = 0
        // let newHs30 = 0
        // if (diffHcHt == -12) newSalaireMensuel = salBase
        // else if (heureTravaille > heureContrat) {
        //     hsfr = diffHcHt
        //     newSalaireMensuel = salBase
        // }
        // else {
        //     newSalaireMensuel = ((salBase / heureContrat) * heureTravaille);
        // }
        // newHs30 = hsfr >= 33.6 ? 33.6 : hsfr
        // const newMh_s30 = divSalBaseHc * (newHs30) * 0.3
        // const newHs50 = hsfr - newHs30
        // const newMh_s50 = divSalBaseHc * (newHs50) * 0.5
        // const newMmaj_ferie = divSalBaseHc * maj.hFerie
        // const newMmaj_dim = divSalBaseHc * maj.hmDim * 0.4
        // const newMmaj = (maj.hMaj / heureContrat) * heureTravaille

        // setheureSup((curr) => {
        //     return {
        //         ...curr,
        //         mh_s30: newMh_s30,
        //         hs50: newHs50,
        //         mh_s50: newMh_s50,
        //     }
        // })

        // let tot_maj = newMh_s30 + newMh_s50 + newMmaj_dim + newMmaj_ferie + newMmaj
        // const newCongePayer = (salBase / 30) * conge.soldeConge;

        // const newPrDeductible = (salBase / 30) * nprv.nprvPreavisDeductible;
        // const newPreavisPayer = (salBase / 30) * nprv.nprvPreavisPayer;
        // const newIdmLicenciement = (salBase / 30) * nprv.nprvLicenciement;

        // const newTotalProrataGrap = prorata(primeState.primeExceptionnelle) 
        //                             + prorata(primeState.primeDiv) 
        //                             + prorata(primeState.idmDepl) 
        //                             + prorata(primeState.primeAssid) 
        //                             + prorata(primeState.primeResp) 
        //                             + prorata(primeState.primeEntret) 
        //                             + prorata(primeState.primeAnc);
        // const newSalBrut = newSalaireMensuel + tot_maj + newTotalProrataGrap + rajout.rappel 
        //                     + newCongePayer - newPrDeductible + newPreavisPayer + newIdmLicenciement
        // const newCnaps = (newSalBrut / 100).toFixed(2)
        // const newSalfa = (newSalBrut / 100).toFixed(2)

        // const newNetImposable = (parseFloat(newSalBrut) - parseFloat(newSalfa) - parseFloat(newCnaps)).toFixed(2)
        // const newIrsa = calculIrsa(newNetImposable)
        // const newNetAPayer = (
        //                         newNetImposable - newIrsa - deduction.retenueFormation 
        //                         - deduction.autreDeduction - deduction.avanceSpeciale 
        //                         - deduction.avance15 - preavis.preavisMoins 
        //                         + Number(rajout.allFamCnaps) + rajout.rembFraisFixe 
        //                         - deduction.avanceSpecialeEmbauche + primeState.prime 
        //                         + prorata(primeState.perdiem) + primeState.partVariable
        //                     ).toFixed(2);

        // const newSalfaPatronale = ((Number(newNetAPayer) + Number(deduction.avance15)) * 0.05).toFixed(2);
        // const newCnapsPatronale = Number((Number(newNetAPayer) + Number(deduction.avance15)) * 0.13).toFixed(2);
        // const newMasseSalariale = (Number(newNetAPayer) + Number(newCnapsPatronale) + Number(newSalfaPatronale)
        //     + Number(newSalfa) + Number(newCnaps) + Number(newIrsa) + Number(deduction.avance15))
        // setCharge(() => {
        //     return {
        //         ...charge,
        //         irsa: newIrsa,
        //         salfa: newSalfa,
        //         cnaps: newCnaps,
        //         salfaPatronale: newSalfaPatronale,
        //         cnapsPatronale: newCnapsPatronale,
        //     }
        // })
        // setResultat({
        //     ...resultat,
        //     netImposable: parseFloat(newNetImposable).toFixed(2),
        //     salaireMensuel: (newSalaireMensuel).toFixed(2),
        //     salaireBrut: (newSalBrut).toFixed(2),
        //     totalProrataGrap: (newTotalProrataGrap).toFixed(2),
        //     netAPayer: newNetAPayer,
        //     masseSalariale: (newMasseSalariale).toFixed(2)
        // })
        // setPreavis({
        //     ...preavis,
        //     preavisDeductible: (newPrDeductible).toFixed(2),
        //     preavisPayer: newPreavisPayer.toFixed(2),
        //     idmLicenciement: newIdmLicenciement.toFixed(2)
        // })
        // setheureSup({
        //     ...heureSup,
        //     hs30: newHs30,
        //     hs50: newHs50,
        //     mh_s30: newMh_s30,
        //     mh_s50: newMh_s50,
        // })
        // setCongePayer(newCongePayer)
    }

    const handleSubmit = (e) => {
        e.preventDefault();
        setError("");
        setDisabledSubmit(true)
        let data = { ...dataEmploye }
        if (data.date_confirmation == '0000-00-00') {
            data.date_confirmation = null
        }
        data.employe_id = dataEmploye.id ? dataEmploye.id : employe.id
        data.nom = dataEmploye.nom? dataEmploye.nom : employe.nom
        data.prime = primeState.prime
        data.prime_exceptionnelle = primeState.primeExceptionnelle
        data.idm_depl = primeState.idmDepl
        data.prime_entret = primeState.primeEntret
        data.prime_anc = primeState.primeAnc
        data.prime_resp = primeState.primeResp
        data.prime_assid = primeState.primeAssid
        data.prime_div = primeState.primeDiv
        data.part_variable = primeState.partVariable
        data.perdiem = primeState.perdiem
        data.sal_base = salBase
        data.salaire_brut = parseFloat(resultat.salaireBrut)
        data.masse_salariale = parseFloat(resultat.masseSalariale)
        data.net_a_payer = parseFloat(resultat.netAPayer)
        data.salaire_mensuel = parseFloat(resultat.salaireMensuel)
        data.irsa = charge.irsa
        data.salfa = charge.salfa
        data.cnaps = charge.cnaps
        data.salfa_pat = charge.salfaPatronale
        data.cnaps_pat = charge.cnapsPatronale
        data.irsa_pat = charge.irsaPatronale
        data.date_paie = datePaie.year + "-" + datePaie.month + "-20"
        data.nb_heure_contrat = heureContrat
        data.nb_heure_travaille = heureTravaille
        data.s_conge = conge.soldeConge;
        data.mode_payement = ((payement.type).toLowerCase()) == "mobile money" ? "MOB" 
                            : ((payement.type).toLowerCase()) == "virement bancaire" ? "VIR" 
                            : "";
        data.numero_tel = payement.numeroTelephone;
        data.banque = payement.banque
        data.numero_compte = payement.numeroCompte
        data.code_banque = payement.codeBanque
        data.code_guichet = payement.codeGuichet
        data.rib = payement.rib
        data.avance_special = deduction.avanceSpeciale
        data.avance_15e = deduction.avance15
        data.avance_speciale_embauche = deduction.avanceSpecialeEmbauche
        data.conge_reste = droit
        data.net_imposable = resultat.netImposable
        data.preavis_moins = preavis.preavisMoins
        data.nprv_licenciement = nprv.nprvLicenciement
        data.nprv_preavis_deductible = nprv.nprvPreavisDeductible
        data.nprv_preavis_payer = nprv.nprvPreavisPayer
        data.conge_pris = congePris
        data.cin = payement.cin
        data.autre_deduction = deduction.autreDeduction
        data.retenue_formation =deduction.retenueFormation
        data.rappel = rajout.rappel
        data.all_fam_cnaps = rajout.allFamCnaps
        data.remb_frais_fixe = rajout.rembFraisFixe
        data.heure_ferie = maj.hFerie
        data.hm_dim = maj.hmDim
        data.h_maj = maj.hMaj
        axios.post(action + (params.id ? params.id : ""), data, useToken()).then((res) => {
            if (res.data.success) {
                setNotification(res.data)
                toggleShowFiche(true)
            }
            else if (res.data.error) {
                setError(res.data.error)
            }
        }).finally(() => {
            setDisabledSubmit(false)
        })
    }
 
    const getPaie = async() => {
        try{
            const response = await axios.get('/api/paie?id=' + (params.id) + "&offset=0", useToken());
            if (response) {
                let res = response.data.paies[0]
                
                setPaie(res);
                if (res.status != "draft") {
                    setSalBase(res.sal_base)
                    setPreavis({
                        ...preavis,
                        preavisMoins : res.preavis_moins
                    })
                    setHeureTravaille(res.nb_heure_travaille)
                    setHeureContrat(res.nb_heure_contrat)
                    setCharge({
                        ...charge,
                        irsa: res.irsa,
                        irsaPatronale: res.irsa_pat ? res.irsa_pat : 0,
                        cnaps: res.cnaps,
                        cnapsPatronale: res.cnaps_pat,
                        salfa: res.salfa,
                        salfaPatronale: res.salfa_pat,
                    })
                    setResultat({
                        ...resultat,
                        salaireMensuel: res.salaire_mensuel,
                        salaireBrut: res.salaire_mensuel,
                        netAPayer: res.net_a_payer,
                        masseSalariale: res.masse_salariale,
                    })
                    setPrimeState({
                        ...primeState,
                        prime: res.prime ? res.prime : 0,
                        primeExceptionnelle: res.prime_exceptionnelle ? res.prime_exceptionnelle : 0,
                        idmDepl: res.idm_depl ? res.idm_depl : 0,
                        primeEntret: res.prime_entret ? res.prime_entret : 0,
                        primeAnc: res.prime_anc ? res.prime_anc : 0,
                        primeResp: res.prime_resp ? res.prime_resp : 0,
                        primeAssid: res.prime_assid ? res.prime_assid : 0,
                        primeDiv: res.prime_div ? res.prime_div : 0,
                        partVariable: res.part_variable ? res.part_variable : 0,
                        perdiem: res.perdiem ? res.perdiem : 0
                    })
                    setNprv({
                        ...nprv,
                        nprvPreavisDeductible: res.nprv_preavis_deductible ? res.nprv_preavis_deductible : 0,
                        nprvPreavisPayer: res.nprv_preavis_payer ? res.nprv_preavis_payer : 0,
                        nprvLicenciement: res.nprv_licenciement ? res.nprv_licenciement : 0,
                    })
                    setMaj({
                        ...maj,
                        hFerie: res.heure_ferie ? res.heure_ferie : 0,
                        hmDim: res.hm_dim ? res.hm_dim : 0,
                        hMaj: res.h_maj ? res.h_maj : 0,
                    })
                    setConge({
                        ...conge,
                        soldeConge: res.s_conge,
                    })
                    setCongePayer(res.s_conge ? ((res.sal_base/30)*res.s_conge) : 0)
                    setRajout({
                        ...rajout, 
                        rappel: res.rappel ? res.rappel : 0,
                        allFamCnaps: res.all_fam_cnaps ? res.all_fam_cnaps : 0,
                        rembFraisFixe: res.remb_frais_fixe ? res.remb_frais_fixe : 0
                    }
                    )
                    setDroit(res.conge_reste)
                }
                setCongePris(res.conge_pris)
                const date = moment(res.date_paie);
                setDatePaie(() => {
                    return {
                        year: moment(date).format('YYYY'),
                        month: moment(date).format('MM'),
                    }
                })
                setDeduction({
                    ...deduction,
                    retenueFormation: res.retenue_formation,
                    autreDeduction: res.autre_deduction,
                    avance15: res.avance_15e,
                    avanceSpeciale: res.avance_special,
                    avanceSpecialeEmbauche:res.avance_speciale_embauche
                })
                
                let dataList = {}
                dataList.id = res.employe_id
                dataList.matricule = matricule(res)
                dataList.date_embauche = res.date_embauche
                dataList.nom = res.employe
                dataList.site = { id : res.site_id, nom : res.site_nom }
                setEmploye(dataList)
                if (["demande", "draft"].includes(res.status)) {
                    getEmploye(dataList.id)
                    getHeureTravaille(moment(res.date_paie).format('YYYY-MM'), dataList.id)
                }
                setPayement({
                    ...payement,
                    type: res.mode_payement == "MOB" ? "Mobile money" : res.mode_payement == "VIR"?"Virement bancaire" : "",
                    numeroTelephone: res.numero_tel,
                    banque: res.banque,
                    codeBanque: res.code_banque,
                    codeGuichet: res.code_guichet,
                    numeroCompte: res.numero_compte,
                    rib: res.rib,
                    cin:res.cin
                })
                setToModify(false)
            }
        }
        catch (error) {
            console.error("Erreur lors de la récupération des données paie", error);
        }
    }

    useEffect(() => {
       if (toModify) getPaie();
    }, [toModify]);

    useEffect(() => {
        if (employe && conge.data) {
            let congePaye = 0;
            let cgPris = 0;
            if (conge.data) {
                let cg = conge.data;
                congePaye = (cg.map(c => moment(c.retour).diff(moment(c.depart), 'hours')).reduce((a, b) => a + b, 0) / 24)
                let cgPrisArray = cg.filter(item => item.type_absence =="conge");
                cgPris = (cgPrisArray.map(c => moment(c.retour).diff(moment(c.depart), 'hours')).reduce((a, b) => a + b, 0) / 24)
            }
            else congePaye = 0;
            setConge({ ...conge, soldeConge: congePaye })
            setCongePris(cgPris)
        }
    }, [employe, datePaie, conge.data]);

    useEffect(() => {
        let isMounted = true;
        if (employe && !toModify) {
            axios.get('/api/paie/conge_done/' + employe.id, useToken()).then((res) => {
                if (isMounted) {
                    setDroit(res.data.droit);                 
                }
            })
            .catch((e) => {
                console.error(e)
            })
        }
        return () => {
            isMounted = false;
        };
    }, [employe]);

    const getEmploye = (id) => {
        axios.get('/api/employe/show/' + (id ? id : employe.id), useToken()).then((res) => {
            if (res.data) {
                setDataEmploye(res.data);
                let emp = res.data;
                setSalBase(emp.sal_base ? emp.sal_base : 0)
                setHeureContrat(emp.nb_heure_contrat ? emp.nb_heure_contrat : 0);
                setPrimeState({
                    ...primeState,
                    primeAnc: emp.primeAnc ? emp.primeAnc : 0,
                    idmDepl: emp.idm_depl ? emp.idm_depl : 0,
                    partVariable: emp.part_variable ? emp.part_variable : 0,
                    perdiem: emp.perdiem ? emp.perdiem : 0,
                })
                setPayement({...payement, 
                    type: res.data.mode_paiement == "MOB" ? "Mobile money" 
                            : res.data.mode_paiement == "VIR" ? "Virement bancaire" 
                            :"",
                    numeroTelephone: res.data.mode_paiement == "MOB"? res.data.numero_mobile: '',
                    banque: res.data.mode_paiement == "VIR" ? res.data.nom_paiement: '',
                    codeBanque: res.data.mode_paiement == "VIR" ? res.data.code_banque: '',
                    codeGuichet: res.data.mode_paiement == "VIR" ? res.data.code_guichet:'',
                    numeroCompte: res.data.mode_paiement == "VIR" ? res.data.numero_compte: '',
                    rib: res.data.mode_paiement == "VIR" ? res.data.rib: '',
                    cin: res.data.cin_text,
                })
            }
        })
    }

    const getEmployeCongeDed = (id) => {
        let date = datePaie.year + "-" + (datePaie.month).toString().padStart(2,"0") + "-20";
        axios.get('/api/paie/get_employe?employe_id=' + id + '&date_paie=' +date, useToken()).then((res) => {
            if (res.data) {
                let avance15 = 0;
                let avc_embauche = 0;
                let avc_speciale = 0;
                let autreDed = 0;
                res.data.avance.map((avc) => {
                    if (avc.name == 'avance15')avance15 = avc.montant
                    if (avc.name == 'avance_embauche') avc_embauche = avc.montant
                    if (avc.name == 'avance_speciale') avc_speciale = avc.montant
                })
                if (res.data.deduction.montant) autreDed =  res.data.deduction.montant
                setDeduction({ ...deduction, 
                                avance15: avance15 ,
                                avanceSpecialeEmbauche:avc_embauche,
                                avanceSpeciale:avc_speciale, 
                                autreDeduction: autreDed 
                            })
                if (res.data.congePaye) {
                    setConge({ ...conge, data: res.data.congePaye });
                }
                else setConge({ ...conge, data:[]});
                setPrimeState({ ...primeState, 
                                    prime: res.data.prime.montant ? parseFloat(res.data.prime.montant) : primeState.prime,
                                    partVariable: res.data.partVariable.montant ? parseFloat(res.data.partVariable.montant) : primeState.partVariable
                            },
                );
            }
        })
    }

    useEffect(() => {
        !toModify && employe && getEmploye()
    }, [employe]);

    const getHeureTravaille = (date, id) => {
        setLoadHeureTravaille(true)
        axios.get('/api/paie/get_heure_travaille?date_paie=' + date + "&employe_id=" + (id ? id : employe.id), useToken()).then((res) => {
            if (res) {
                setHeureTravaille(res.data.heures);
                setLoadHeureTravaille(false);
            }
        })
    }

    useEffect(() => {
        const date = datePaie.year + "-" + datePaie.month
        if (datePaie.year && datePaie.month) {
            // employe && getEmploye()
            if (employe && datePaie) {
                if (params.id && paie.status!="traite") {
                    getEmployeCongeDed(employe.id)
                }
                else if (!paie) {
                    getEmployeCongeDed(employe.id)
                }
                // getHeureTravaille(date)
            }
            if(!toModify && employe) getHeureTravaille(date)
        }
    }, [employe, datePaie]);

    return (
        <div id="content">
            <div>
                {
                    notification ? <Notification next={notification.id ? "/paie?id=" + notification.id : "/paie"} message={notification.success} /> 
                        :
                        <form onSubmit={handleSubmit}>
                            <div className="title-container">
                                <h2>{title}</h2>
                            </div>
                            <DualContainer>
                                <InputEmploye required onChange={setEmploye} value={employe} />
                                <InputMonthYear setDefaultDate required value={datePaie} onChange={setDatePaie} />
                            </DualContainer>

                            <DualContainer>
                                {loadHeureTravaille ? 
                                    <div>
                                        <label>Heure travaillé</label>
                                        <LoadingPage />
                                    </div> 
                                    : 
                                    <InputText type="number" 
                                        label="Heure travaillé" 
                                        onChange={(value) => setHeureTravaille(parseFloat(value))} 
                                        value={heureTravaille} 
                                        required 
                                    />
                                }
                                <InputSelect label="Heure Contrat" required selected={heureContrat} setSelected={setHeureContrat} options={[240, 312]} />
                            </DualContainer>

                            <DualContainer>
                                <InputText required type="number" label="Salaire de base" onChange={(value) => setSalBase(parseFloat(value))} value={salBase} />
                            </DualContainer>
                            {showPayement &&
                                <Payement payement={payement} 
                                    setPayement={setPayement} 
                                    showPayement={showPayement} 
                                    toggleShowPayement={toggleShowPayement} 
                                />
                            }
                            <div className="tab-container">
                                <div className="tab-list">
                                    <div onClick={() => setActiveMenu("prime")} className={activeMenu == "prime" ? "active" : ""}>
                                        Primes
                                    </div>
                                    {
                                        <div onClick={()=>setActiveMenu("rajout")} className={activeMenu == "rajout" ? "active" :""}>
                                            Rajout
                                        </div>
                                    }
                                    { 
                                        <div onClick={() => setActiveMenu("deduction")} className={activeMenu == "deduction" ? "active" : ""}>
                                            Déduction
                                        </div>
                                    }
                                    {
                                        <div onClick={() => setActiveMenu("charge")} className={activeMenu == "charge" ? "active" : ""}>
                                            Charges
                                        </div>
                                    }
                                    {
                                        <div onClick={() => setActiveMenu("maj")} className={activeMenu == "maj" ? "active" : ""}>
                                            Maj
                                        </div>
                                    }
                                    {
                                        <div onClick={() => setActiveMenu("conge")} className={activeMenu == "conge" ? "active" : ""}>
                                            Congés
                                        </div>
                                    }
                                    {
                                        <div onClick={() => setActiveMenu("preavis")} className={activeMenu == "preavis" ? "active" : ""}>
                                            Preavis
                                        </div>
                                    }
                                    {
                                        <div onClick={() => setActiveMenu("payement")} className={activeMenu == "payement"? "active" : ""}>
                                            Paiement
                                        </div>
                                    }
                                    {
                                        <div onClick={() => setActiveMenu("resultat")} className={activeMenu == "resultat" ? "active" : ""}>
                                            Salaire
                                        </div>
                                    }
                                </div>
                                <div className="tab-content">
                                    { 
                                        activeMenu == "prime" && <PrimePaie primeState={primeState} setPrimeState={setPrimeState} /> 
                                    }
                                    { 
                                        activeMenu == "deduction" && 
                                            <Deduction deduction={deduction} 
                                                setDeduction={setDeduction} 
                                                employeId={employe ? employe.id : ""} 
                                                datePaie={datePaie}
                                            /> 
                                    }
                                    { 
                                        activeMenu == "charge" && <Charge charge={charge} /> 
                                    }
                                    { 
                                        activeMenu == "resultat" && <Resultat resultat={resultat} setResultat={setResultat} /> 
                                    }
                                    { 
                                        activeMenu == "conge" && 
                                            <Conge conge={conge} 
                                                setConge={setConge}
                                                employeId={employe ? employe.id : ""} 
                                                congePayer={congePayer}
                                                datePaie={datePaie}
                                            /> 
                                    }
                                    { 
                                        activeMenu == "preavis" && 
                                            <Preavis preavis={preavis} setPreavis={setPreavis} nprv={nprv} setNprv={setNprv} /> 
                                    }
                                    { 
                                        activeMenu == "maj" && <Maj maj={maj} setMaj={setMaj} />
                                    }
                                    { 
                                        activeMenu == "rajout" && <Rajout rajout={rajout} setRajout={setRajout} />
                                    }
                                    {
                                        activeMenu == "payement" && 
                                        <Payement payement={payement} 
                                            setPayement={setPayement} 
                                            showPayement={showPayement} 
                                            toggleShowPayement={toggleShowPayement} 
                                            banques={banques}
                                        />
                                    }
                                </div>
                            </div>
                            {error && <div className='container-error'> {error} </div>}
                            {!params.id && <ButtonSubmit disabled={disabledSubmit} label="Enregistrer" />}
                            {
                                params.id && paie && 
                                    <ButtonSubmit disabled={disabledSubmit} 
                                        label = {button ? button
                                                    : paie.status == "demande"?"Traiter"
                                                    : paie.status=="traite"?"Modifier"
                                                    : paie.status=="draft"?"Renvoyer"
                                                    : ""
                                                } 
                                    />
                            }
                        </form>
                }
                {
                    showFiche && params.id 
                        && notification 
                        && paie.status !="draft"
                        && <ModalImprimer id={params.id} show={showFiche} toggleShow={toggleShowFiche} />
                }
            </div>
        </div>
    )
}
