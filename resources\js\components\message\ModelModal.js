import React, { useCallback, useEffect, useState } from 'react'
import InputText from '../input/InputText'
import useToken from '../util/useToken'
import InfiniteScroll from 'react-infinite-scroll-component'
import LoadingPage from '../loading/LoadingPage'
import { debounce } from 'lodash'

export default function ModelModal({closeModal, setModelId}) {
    const [searchValue, setSearchValue] = useState('')
    const [models, setModels] = useState([])
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [debouncedSearchValue, setDebouncedSearchValue] = useState('');

    const getModels = useCallback((initial) => {
        let params = new URLSearchParams();
        if (initial) {
            params.set("offset", 0);
        } else {
            params.set("offset", models.length);
        }
        params.set("nom", debouncedSearchValue);
        axios.get('/api/model_message?' + params, useToken())
            .then((res) => {
                if (res.data.models) {
                    if (initial) {
                        setModels(res.data.models);
                    } else {
                        const list = models.slice().concat(res.data.models);
                        setModels(list);
                    }
                    if (res.data.models.length < 30) {
                        setDataLoaded(true);
                    }
                }
            })
            .catch((e) => {
                console.error(e);
            });
    }, [debouncedSearchValue, models]);

    const debounceUpdateSearchValue = useCallback(
        _.debounce((value) => {
            setDebouncedSearchValue(value);
        }, 300),
        []
    );

    const handleSearchChange = (value) => {
        setSearchValue(value);
        debounceUpdateSearchValue(value);
    };

    useEffect(() => {
        getModels(true);
    }, [debouncedSearchValue]);
    
    const fetchMoreData = () => {
        setTimeout(() => {
            getModels()
        }, 300);
    }

    return (
        <div className='modal'>
            <div>
                <h2>Model</h2>
                <InputText type="text"
                    label="Nom du model"
                    value={searchValue}
                    onChange={handleSearchChange}
                />
                <div id="scrollableList">

                    <InfiniteScroll
                        dataLength={models.length}    
                        next={fetchMoreData}
                        hasMore={!allDataLoaded}
                        loader={<LoadingPage />}
                        scrollableTarget="scrollableList"
                    >
                        <div className='list-container'>
                            <ul>
                                {
                                    models.map((model) => {
                                        return (
                                            model.type &&
                                            <li key={model.id} onClick={() => { setModelId(model.id), closeModal() }}>
                                                <span className='secondary'>{model.type}</span>
                                            </li>
                                        )
                                    })
                                }

                            </ul>
                        </div>
                    </InfiniteScroll>
                </div>
                <div className='form-button-container'>
                    <button onClick={closeModal}>Annuler</button>
                </div>
            </div>
        </div>
  )
}
