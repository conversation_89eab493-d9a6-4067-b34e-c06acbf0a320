import React from "react";
import { Text, View, StyleSheet, Image } from "@react-pdf/renderer";
import dirickxCachet from "./dirickxCachet.jpg";

const styles = StyleSheet.create({
    titleContainer: {
        flexDirection: "row",
    },
    reportTitle: {
        fontSize: 6,
        textAlign: "left",
        textTransform: "uppercase",
    },
    cachet: {
        width: 100,
        height: 55,
        marginLeft: "auto",
        marginRight: "auto",
        bottom: -15,
    },
    imageContainer: {
        position: "relative",
    },
    text: {
        marginTop: 7,
        position: "absolute",
        zIndex: 200,
        flexDirection: "row",
    },
    textSalarie: {
        marginTop: 7,
        marginLeft: 20,
        position: "absolute",
        textAlign: "left",
        zIndex: 200,
    },
});

const FooterDocument = () => (
    <View style={styles.titleContainer}>
        <Text style={styles.reportTitle}>
            DANS VOTRE INTERET ET POUR VOUS AIDER A FAIRE VALOIR VOS DROITS,
            CONSERVEZ CE BULLETIN DE PAIE SANS LIMITATION DE DUREE
        </Text>
        <Text style={styles.textSalarie}>Le_Salarié</Text>
        <View style={styles.imageContainer}>
            <View style={styles.text}>
                <Text>La Societé</Text>
            </View>
            <Image style={styles.cachet} src={dirickxCachet} />
        </View>
    </View>
);

export default FooterDocument;
