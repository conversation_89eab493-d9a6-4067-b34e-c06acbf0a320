import {useState,useRef} from 'react'

import useClickOutside from '../util/useClickOutside'
import moment from 'moment'

export default function InputYear({selected, setSelected, label, required, disable}) {
    const [isActive, setIsActive] = useState(false)
    const selectRef = useRef(null)
    const currentYear = moment().format("YYYY")
    const options = [
        currentYear - 2,
        currentYear - 1,
        currentYear,
	    Number.parseInt(currentYear) + 1,
    ]
    useClickOutside(selectRef, () => setIsActive(false))

    return (
        <div className='input-container'>

            {label && <label>{label} {required && <span className='danger'>*</span>}</label>}
            <select id="month-select" style={{height: "44px"}} value={selected} onChange={(e) => setSelected(e.target.value)} disabled={disable}>
                <option></option>
                {options.map((option, index) => (
                    <option key={index} value={option}>
                        {option}
                    </option>
                ))}
            </select>
            {/* <div className='select-container'>
                <div className={'select-input '} ref={selectRef}>
                    <div className={"select-placeholder " + (isActive ? "active-select" : "") }
                        onClick={(e) => setIsActive(!isActive)}>
                        <span>{selected ? selected : ''}</span>
                        <span>{isActive ? <IoMdArrowDropup/> : <IoMdArrowDropdown/>}</span>
                    </div>
                    {isActive && 
                        <div className="select-content">
                            {options.map((option,index) => (
                                <div 
                                    key={index}
                                    onClick={(e) => {
                                        setSelected(option)
                                        setIsActive(false)
                                    }}
                                    className="select-option"
                                >
                                    {option.label ? option.label : option}
                                </div>
                            ))}
                            
                        </div>
                    }
                </div>
            </div> */}
        </div>
    )
}
