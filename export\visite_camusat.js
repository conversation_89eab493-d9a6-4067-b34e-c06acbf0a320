const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const { sendMail } = require('../auth')

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == 'task')
const destination_vg = ["<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

const sqlSelectSiteCamusat = "SELECT s.idsite, s.nom from sites s " + 
    "WHERE (s.soft_delete is null or s.soft_delete = 0) " +
    "and s.idsite in (4756, 4758) order by idsite" 

const sqlSelectVisitePoste = (dateString, ids) => {
    const begin = dateString
    const end = moment(dateString).add(1, "month").format("YYYY-MM-DD HH:mm:ss")
	return "SELECT vp.id, vp.user_id, vp.site_id, vp.date_visite, vp.compte_rendu, vp.created_at, " +
        "u.name as 'user_nom', coalesce(ur.email, u.email) as 'user_email', " +
        "vp.employe_id, e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit " +
        "from visite_postes vp " +
        "left join users u on u.id = vp.user_id " +
        "left join users ur on ur.id = u.real_email_id " +
        "left join employes e on e.id = vp.employe_id " +
        "where vp.site_id in (" + ids.join(',') + ") and vp.date_visite > '" + begin +"' and vp.date_visite <= '" + end +"' " +
        "order by vp.site_id, vp.created_at"
}

const sqlSelectAlarm = (ids) => "SELECT v.idademco, v.employe_id, v.site_id, v.dtarrived from alarms v " +
    "where v.dtarrived > ? and v.site_id in (" + ids.join(',') + ")" +
    "order by v.dtarrived asc"
    
const sqlSelectLastVisitePosteExport = "SELECT value FROM params p WHERE p.key = 'last_visite_camusat_export'"

const sqlUpdateLastVisitePosteExport = "UPDATE params p SET p.value = ? WHERE p.key = 'last_visite_camusat_export'"

function generateVisitePosteExcelFile(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontSite = { size: 14, bold: true }
	const fontRed = { size: 14, color: { argb: 'FFe91e63' } }

	const fillRed = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'fff44336'}
	}

	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    const worksheet = workbook.addWorksheet("CAMUSAT")

    worksheet.getColumn('A').width = 20
    worksheet.getColumn('B').width = 90
    worksheet.getColumn('C').width = 50
    worksheet.getColumn('D').width = 20
    worksheet.getCell('A1').value = header + " (" + data.filter(s => s.visites.length < 2).length + "/" + data.length + ")"
    worksheet.getCell('A1').font = fontHeader
    worksheet.mergeCells('A1:C1')

    let line = 3
    data.forEach(st => {
        worksheet.getCell('A' + line).value = st.nom
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontSite
        worksheet.mergeCells('A' + line + ':D' + line)
        line++
        
        if(st.visites.length == 0){
            worksheet.getCell('A' + line).value = "Aucune visite effectué" 
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.mergeCells('A' + line + ':D' + line)
            line++
        }
        else {
            st.visites.forEach(vp => {
                worksheet.getCell('A' + line).value = moment(vp.date_visite).format("DD-MM-YY HH:mm")
                worksheet.getCell('A' + line).border = borderStyle
                worksheet.getCell('A' + line).alignment = alignmentStyle
                worksheet.getCell('B' + line).value = vp.compte_rendu
                worksheet.getCell('B' + line).border = borderStyle
                worksheet.getCell('C' + line).value = vp.user_nom + ' <' + vp.user_email + '>'
                worksheet.getCell('C' + line).border = borderStyle
                if(vp.vigilances.length > 0){
                    worksheet.getCell('D' + line).value =  moment(vp.vigilances[0].dtarrived).format("DD-MM-YY HH:mm")
                    worksheet.getCell('D' + line).border = borderStyle
                    worksheet.getCell('D' + line).alignment = alignmentStyle
                }
                else {
                    worksheet.getCell('D' + line).value =  "X"
                    worksheet.getCell('D' + line).border = borderStyle
                    worksheet.getCell('D' + line).alignment = alignmentStyle
                    worksheet.getCell('D' + line).fill = fillRed
                }
                line++
            })
        }
        worksheet.mergeCells('A' + line + ':D' + line)
        line++
        worksheet.mergeCells('A' + line + ':D' + line)
        line++
        
    });
}

function doVisitePosteExport(dateString){
	console.log("do visite camusat")
            pool.query(sqlSelectSiteCamusat, [], async (err, sites) => {
                if(err)
                    console.error(err)
                else if(sites.length > 0){
                    console.log("Nb site: " + sites.length)
                    pool.query(sqlSelectVisitePoste(dateString, sites.map(s => s.idsite)), [], async (err, visites) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Nb visite: " + visites.length)
                            pool.query(sqlSelectAlarm(sites.map(s => s.idsite)), [moment(dateString).subtract(1, "day").format("YYYY-MM-DD HH:mm:ss")], async (err, vigilances) => {
                                if(err)
                                    console.error(err)
                                else {
                                    console.log("Nb vigilance: " + vigilances.length)
                                    visites.forEach(vp => {
                                        vp.vigilances = []
                                        vigilances.forEach(vg => {
                                            if(vg.employe_id == vp.employe_id 
                                                && vg.site_id == vp.site_id 
                                                && moment(vp.date_visite).isAfter(moment(vg.dtarrived).subtract(15, "minutes"))
                                                && moment(vp.date_visite).isBefore(moment(vg.dtarrived).add(15, "minutes"))
                                            ) {
                                                vp.vigilances.push(vg)
                                            }
                                        })
                                    })
                                    sites.forEach(s => {
                                        s.visites = []
                                        visites.forEach(vst => {
                                            let lastVisite = null
                                            let minutes = 0
                                            if(s.visites.length > 0){
                                                lastVisite = s.visites[s.visites.length - 1]
                                                let duration = moment.duration(moment(vst.date_visite).diff(moment(lastVisite.date_visite)))
                                                minutes = duration.asMinutes()
                                            }
                                            if(s.idsite == vst.site_id && (
                                                    lastVisite == null || 
                                                    vst.employe_id != lastVisite.employe_id ||
                                                    (vst.employe_id == lastVisite.employe_id && minutes > 150)
                                                )
                                            ) {
                                                s.visites.push(vst)
                                            }
                                        })
                                    })
                                    const workbookSiteCamusat = new Excel.Workbook()
                                    const header = "Visite de poste CAMUSAT " + moment(dateString).format("MMMM YYYY")
                                    generateVisitePosteExcelFile(workbookSiteCamusat, header, sites)
                                    const siteCamusatBuffer = await workbookSiteCamusat.xlsx.writeBuffer()
                                    sendMail(
                                        pool,
                                        isTask ? destination_vg : destination_test,
                                        header, 
                                        (
                                            sites.length > 0 ?
                                                "<ul>" +
                                                    sites.map(s => "<li>" + s.nom + "</li>").join(" ") +
                                                "</ul>"
                                            :
                                                "<p>Aucune manque de rapport CAMUSAT durant le mois</p>"
                                        ),
                                        [
                                            {
                                                filename: header + ".xlsx",
                                                content: siteCamusatBuffer
                                            },
                                        ],
                                        (response) => {
                                            if(response && isTask){
                                                pool.query(sqlUpdateLastVisitePosteExport, [dateString], (e, r) =>{
                                                    if(e)
                                                        console.error(e)
                                                    else
                                                        console.log("update last diag export: " + r)
                                                    process.exit(1)
                                                })
                                            }
                                            else
                                                process.exit(1)
                                        }
                                        , isTask
                                    )
                                }
                            })
                        }
                    })
                }
                else
                    console.log("Aucun site Camusat")
            })
}

if(/^\d{4}$/.test(process.argv[2]) && /^\d{2}$/.test(process.argv[3])){
    console.log("send test...")
    doVisitePosteExport(process.argv[2] + '-' + process.argv[3] + '-01 00:00:00')
}
else if(process.argv[2] == 'task'){
    if(moment().format("DD") == 1 && moment().isAfter(moment().set({hour: 6, minute: 30}))){
        let date_vigilance = moment().subtract(1, "month").format("YYYY-MM-DD") + " 00:00:00"
        pool.query(sqlSelectLastVisitePosteExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && result[0].value == date_vigilance) {
                console.log("export visite camusat already done!")
                process.exit(1)
            }
            else {
                console.log("exporting ...")
                doVisitePosteExport(date_vigilance)
            }
        })
    }
    else {
        console.log("Not 1 of month, skip export visite camusat.")
    }
}
else
    console.log("please specify command!")