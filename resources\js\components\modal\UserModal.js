import axios from 'axios'
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useToken from '../util/useToken';

export default function UserModal({ role, roles, onChange, closeModal, isRespPart, paramsSuperviseur, useLink}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [searchUser, setSearchUser] = useState("")
    const [users, setUsers] = useState(null)
    
    const handleSelectUser = (us) => {
        if(useLink) {
            let params = new URLSearchParams(location.search)
            if (paramsSuperviseur) 
                params.set("superviseur_id", us.id)
            else if (roles?.includes('resp_sup'))
                params.set("resp_sup_id", us.id)
            else
                params.set("user_id", us.id)
            navigate(location.pathname + "?" + params)
        }
        onChange(us)
        closeModal()
    }

    useEffect(() => {
        let isMounted = true
        const urlParams = new URLSearchParams()
        if(roles?.length > 0) urlParams.set('roles', "'" + roles.join('\',\'') + "'")
        if(isRespPart) urlParams.set('resp_part', 1)
        if(role == 'superviseur') urlParams.set('superviseur', 1)
        if(role == 'parent') urlParams.set('parent', 1)
        axios.get('/api/user/modal' + '?' + urlParams.toString()
            //  + (isRespPart ? '?resp_part=1' : role == 'superviseur' ? '?superviseur=1' : role == 'parent' ? '?parent=1' : '')
            ,useToken())
        .then((res) => {
            if(isMounted) setUsers(res.data)
        })
        return () => {isMounted = false}
    }, [])

    return <div className='modal'>
        <div>
            <h2>
                {role? role : 'Utilisateur'}
            </h2>
            <div className='input-container'>
                <input
                    type="text"
                    value={searchUser}
                    placeholder="Nom ou adresse email"
                    onChange={e => setSearchUser(e.target.value)}
                />
            </div>
            {
                users &&
                <div className='list-container'>
                    {
                        <ul>
                            <li onClick={()=>onChange()}></li>
                            {
                                users.filter(us => {
                                    const nameFound = new RegExp(searchUser.toLowerCase()).test(us.name.toLowerCase())
                                    const emailFound = new RegExp(searchUser.toLowerCase()).test(us.email.toLowerCase())
                                    return nameFound || emailFound
                                }).map(us => {
                                    return <li key={us.id} onClick={() => handleSelectUser(us)}>
                                        {us.name}<br/>
                                        <span className='secondary'>{us.email}</span>
                                    </li>
                                })
                            }
                        </ul>
                    }
                </div>
            }
            <div className='form-button-container'>
                <button onClick={closeModal}>Annuler</button>
            </div>
        </div>
    </div>
}