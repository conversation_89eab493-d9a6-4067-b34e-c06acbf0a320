import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import matricule from '../util/matricule';
import moment from 'moment';
import LastPrime from './LastPrime';
import InputPointage from '../input/InputPointage';
import Textarea from '../input/Textarea';
import InputUser from '../input/InputUser';
import LoadingScreen from '../loading/LoadingScreen';
import InputSite from '../input/InputSite';
import InputAgent from '../input/InputAgent';
import InputDatePointage from '../input/InputDatePointage';

export default function EditPrime({auth, title, action}) {
    const params = useParams()
    const [prime, setPrime] = useState(null)
    const [objet, setObjet] = useState("")
    const [montant, setMontant] = useState(0)
    const [employe, setEmploye] = useState(null)
    const [lastPrimes, setLastPrimes] = useState(null)
    const [pointage, setPointage] = useState(null)
    const [superviseur, setSuperviseur] = useState(null)
    const [site, setSite] = useState(null)
    const [motif, setMotif] = useState("")
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleChangeEmploye = (a) => {
        setEmploye({
            id: a.id,
            nom: a.nom,
            matricule: a.matricule
        })
        setPointage(null)
    }

    useEffect(() => {
        let isMounted = true
        if(employe){
            setLastPrimes(null)
            axios.get("/api/employe/last_prime/" + employe.id, useToken())
            .then((res) => {
                if(isMounted) setLastPrimes(res.data)
            })
        }
        return () => {isMounted = false}
    }, [employe])

    const handleChangePointage = (ptg) => {
        setPointage({
            id: ptg.id,
            date_pointage: ptg.date_pointage,
        })
        setSite(ptg.site)
    }


    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const data = {
            employe_id: employe ? employe.id : '',
            // pointage_id: pointage ? pointage.id : '',
            date_service: pointage,
            site_id: site ? site.id : '',
            objet: objet,
            montant: montant,
            motif: motif,
            superviseur_id: superviseur ? superviseur.id : '',
        }
        axios.post(action + (params.id ? "/" + params.id : ""), data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setNotification({success: "Une erreur est survenue."})
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    useEffect(() => {
        let isMounted = true
        if(params.id){
            axios.get('/api/prime/show/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    const prime = res.data
                    setPrime(prime)
                    if(prime.objet) setObjet(prime.objet)
                    if(prime.montant) setMontant(prime.montant)
                    if(prime.motif) setMotif(prime.motif)
                    if(prime.employe) setEmploye({
                        id: prime.employe_id,
                        nom: prime.employe,
                        matricule: matricule(prime)
                    })
                    else 
                        setLastPrimes([])
                    if(prime.site) setSite({
                        id: prime.site_id,
                        nom: prime.site
                    })
                    if(prime.date_pointage) setPointage({
                        id: prime.pointage_id,
                        date_pointage: prime.date_pointage,
                    })
                    if(prime.sup_nom) setSuperviseur({
                        id: prime.superviseur_id,
                        name: prime.sup_nom,
                        email: prime.sup_email,
                    })
                }
            })
        }
        else 
            setLastPrimes([])
        return () => { isMounted = false };
    }, [])

    return (
        <div id="content">
            {
                notification ? 
                    <Notification next={notification.id ? "/prime?id=" + notification.id : "/prime"} message={notification.success}/>
                :
                <div>
                    <div className="title-container">
                        <h2>{title}</h2>
                    </div>
                    {
                        lastPrimes === null ?
                            <LoadingScreen/>
                        : <div>
                            {
                                lastPrimes.length > 0 &&
                                <div className='header-container'>
                                    <h3>Dernières primes reçu par l'employe</h3>
                                    <div>
                                        <span className='text'>
                                            {employe.matricule} {employe.nom}
                                        </span>
                                    </div>
                                </div>
                            }
                            {
                                lastPrimes.map((pr) => (
                                    <LastPrime key={pr.id} prime={pr}/>
                                ))
                            }
                            {
                                lastPrimes.length > 0 &&
                                <div className="input-container-btn">
                                    <button onClick={() => setLastPrimes([])}>Continuer</button>
                                </div>
                            }
                            {
                                lastPrimes.length == 0 &&
                                <form onSubmit={handleSubmit}>
                                    {
                                        (prime && (auth.id != prime.user_id || ["resp_rh", "validateur"].includes(auth.role)))  &&
                                        <div>
                                            <div className="card-container">
                                                <h3>
                                                    {matricule(prime)} {prime.employe} 
                                                </h3>
                                                <div>
                                                    Site: <span className='text'>{prime.site}</span>
                                                </div>
                                                {
                                                    <div>
                                                        Date du service : <span className='text'>
                                                            {
                                                                moment(prime.date_pointage).format("dddd DD MMM YYYY") 
                                                                + " " + (moment(prime.date_pointage).format("HH:mm:ss") == "07:00:00" ? " JOUR" : " NUIT")
                                                            }
                                                        </span>
                                                    </div>
                                                }
                                                <p>
                                                    Motif: <span className='text'>{prime.motif}</span>
                                                </p>
                                                <div>
                                                    Superviseur responsable: <span className='text'> 
                                                        {prime.user_nom} {' <' + prime.user_email + '>'}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    {
                                        (!params.id || (prime && auth.id == prime.user_id)) &&
                                        <div>
                                            <InputAgent
                                                required
                                                value={employe} 
                                                onChange={handleChangeEmploye}/>
                                            {/* {
                                                (employe) &&
                                                <InputPointage
                                                    // required
                                                    employeId={employe.id}
                                                    value={pointage} 
                                                    onChange={handleChangePointage}/>
                                            } */}
                                            <InputDatePointage 
                                                label="Date du service"
                                                value={pointage}
                                                onChange={(dt) => setPointage(dt)}
                                            />
                                            {
                                                (pointage) &&
                                                <InputSite
                                                    withoutDelete
                                                    label="Site"
                                                    value={site} 
                                                    onChange={setSite}/>
                                            }
                                        </div>
                                    }
                                    {
                                        ["resp_rh"].includes(auth.role) && 
                                        <div>
                                            <InputText 
                                                required
                                                label="Objet"
                                                value={objet} 
                                                onChange={setObjet}/>
                                        </div>
                                    }
                                    {
                                        auth.role == 'validateur' && 
                                        <div>
                                            <InputText 
                                                label="Montant"
                                                value={montant} 
                                                onChange={setMontant}
                                                type="number"/>
                                        </div>
                                    }
                                    {
                                        (!params.id || (prime && auth.id == prime.user_id)) && 
                                        <Textarea
                                            required
                                            label="Motif"
                                            value={motif}
                                            onChange={setMotif}/>
                                    }
                                    {
                                        (['resp_rh'].includes(auth.role) && (!prime || auth.id == prime.user_id)) &&
                                        <InputUser
                                            role="superviseur" 
                                            required
                                            value={superviseur} 
                                            onChange={setSuperviseur}/>
                                    }
                                    {
                                        error &&
                                        <div className='container-error'>
                                            {error}
                                        </div>
                                    }
                                    {
                                        lastPrimes!== null &&
                                        <ButtonSubmit disabled={submitDisabled}/>
                                    }
                                </form>
                            }
                        </div>
                    }
                </div>
            }
        </div>
    )
}