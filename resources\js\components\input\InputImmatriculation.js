import {useState,useRef, useEffect} from 'react'

import useClickOutside from '../util/useClickOutside'

export default function InputImmatriculation({currentSelect, setCurrentSelect,required}) {
    const [showSelect, toggleSelect] = useState(false)
    const [label, setLabel] = useState("")
    const immatriculations = [
        {id: '3', nom: 'ST', designation: 'Stagiaire'},
        {id: '1', nom: 'DGM', designation: 'Dirickx Guard'},
        {id: '2', nom: 'SOIT', designation: 'SOIT'},
        {id: '6', nom: 'SAOI', designation: 'SAOI'},
        {id: '4', nom: 'SM', designation: 'Sans matricule'},
        {id: '5', nom: 'TMP', designation: 'Temporaire'},
    ]
    const selectRef = useRef(null)

    useClickOutside(selectRef, () => {
        toggleSelect(false)
        if(currentSelect && currentSelect.designation)
            setLabel(currentSelect.designation)
        else
            setLabel("")
    })

    useEffect(() => {
        setLabel(currentSelect ? currentSelect.designation: "")
    }, [currentSelect]);

    /*useEffect(() => {
        let isMounted = true
        axios.get("/api/immatriculation", useToken())
        .then((res) => {
            if(isMounted){
                setImmatriculations(res.data)
            }
        })
        return () => {isMounted = false}
    }, [])*/

    return (
        <div ref={selectRef} className='input-select-relative input-container'>
            <label>Immatriculation {required && <span className='danger'>*</span>}</label>
            <input className='select-search' 
                onClick={() => toggleSelect(!showSelect)} 
                value={label} 
                onChange={(e) => {
                    setLabel(e.target.value)
                    if(!e.target.value) setCurrentSelect(null)
                }}/>
            <div className='input-select-relative'>
                {
                    showSelect && 
                    <div className='select-list'>
                        {
                            immatriculations.map((imt, index) => (
                                <div 
                                    key={index}
                                    className="select-item"
                                    onClick={(e) => {
                                        setCurrentSelect(imt)
                                        toggleSelect(false)
                                        setLabel(imt.designation)
                                    }}
                                >
                                    {imt.designation}
                                </div>
                            ))
                        }
                        
                    </div>
                }
            </div>
        </div>
    )
}
