import React, { useEffect, useState } from 'react';
import { Link, useLocation } from "react-router-dom";

import axios from 'axios';
import useToken from '../util/useToken';
import SearchBar from '../input/SearchBar';
import LoadingPage from '../loading/LoadingPage';
import InfiniteScroll from 'react-infinite-scroll-component';
import StatusLabel from '../input/StatusLabel';
import showAmount from '../util/numberUtil';
import matricule from '../util/matricule';
import moment from 'moment';
import ConfirmMultiple from './ConfirmMultiple';
import './part.css'
import ModalExportPart from './ModalExportPart';
import ModalStoreMultiple from './ModalStoreMultiple';

export default function PartVariable({auth, partVariables, setPartVariables, currentId, setCurrentId}) {
    const locationSearch = useLocation().search
    const hasUserId = (new URLSearchParams(locationSearch)).get("user_id")
    const statusValidation = (new URLSearchParams(locationSearch)).get("status") == "validation"
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [offsetDate, setOffsetDate] = useState("")
    const [confirmMultiple, toggleConfirmMultiple] = useState(false)
    const [exportPart, toggleExportPart] = useState(false)
    const [storeMultiple, toggleStoreMultiple] = useState(false)
    
    const searchItems = [
        {label: 'ID', name: 'id', type:'number'},
        {label: "Employé", name: 'employe_id', type:"number"},
        {label: "Date de paie", name: "date_paie", type: "dateMonth"},
        {label: 'Status', name: 'status', type:'string'},
        {label: 'Date de création', name: 'created_at', type:'date'},
        {label: 'Utilisateur', name: 'user_id', type:'number'},
    ]
        
    const updateData = (initial) => {
        let isMounted = true;
        const urlParams = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            urlParams.set("offset", 0)
        }
        else
            urlParams.set("offset", partVariables.length)

        if(offsetDate && !initial){
            urlParams.set("offset_date", offsetDate)
        }

        axios.get("/api/part_variable?" + urlParams ,  useToken())
        .then((res) => {
            if(isMounted) {
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial) {
                        setPartVariables(res.data.part_variables)
                        setOffsetDate(res.data.offset_date)
                    }
                    else {
                        const list = partVariables.slice().concat(res.data.part_variables)
                        setPartVariables(list)
                    }
                    setDataLoaded(res.data.part_variables.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }
    
    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
      setTimeout(() => {
        updateData()
      }, 300);
    };

    const linkUserWithParam = (id) => {
        const urlParams = new URLSearchParams(locationSearch)
        urlParams.set("user_id", id)
        return urlParams
    }

    return <> {
        isLoading ?
            <LoadingPage/>
        : 
            <div>
                <div className="padding-container space-between">
                    <h2>Part variable</h2>
                    <Link className='btn btn-primary' to="/part-variable/add">Nouveau</Link>
                </div>
                <SearchBar hasEmploye listItems={searchItems} />
                {
                    confirmMultiple &&
                    <ConfirmMultiple userId={hasUserId} closeModal={() => toggleConfirmMultiple(false)} updateData={updateData} setOffsetDate={setOffsetDate} />
                }
                {
                    exportPart && <ModalExportPart closeModal={() => toggleExportPart(false)}/>
                }
                {
                    storeMultiple && <ModalStoreMultiple updateData={updateData} closeModal={() => toggleStoreMultiple(false)} />
                }
                {
                    partVariables.length == 0 ? 
                        <h3 className='center secondary'>Aucun données trouvé</h3>
                    :
                        <div>
                            <div className='header-action action-container'>
                                {
                                    (hasUserId && statusValidation && ['validateur'].includes(auth.role)) &&
                                    <span onClick={() => toggleConfirmMultiple(true)}>Confirmer par lot</span>
                                }
                                {/*
                                    auth.role == "validateur" &&
                                    <span onClick={() => toggleStoreMultiple(true)}>Ajout multiple</span>
                                */}
                                {
                                    (['resp_rh', 'validateur'].includes(auth.role) && partVariables.length > 0) &&
                                    <span onClick={() => toggleExportPart(true)}>Exporter</span>
                                }
                            </div>
                            {
                                (!hasUserId && ['resp_rh', 'validateur'].includes(auth.role)) ?
                                    <>
                                        <div className="line-container ">
                                            <div className='row-employe'>
                                                <b className='line-cell-lg'>Nom</b>
                                                <b className='line-cell-xs'>Nb</b>
                                                <b>Email</b>
                                            </div>
                                        </div>
                                        {
                                            partVariables.map((pv, index) => (
                                                <div className={`table line-container ${currentId && currentId == pv.id ? 'selected' : ''}`} key={index}>
                                                    <Link className='link-no-style' to={"/part-variable?" + linkUserWithParam(pv.user_id)}>
                                                        <div className="row-employe">
                                                            <span className='capitalize line-cell-lg'>
                                                                {pv.name}
                                                            </span>
                                                            <span className="line-cell-xs">
                                                                {pv.nb_pv}
                                                            </span>
                                                            <span>
                                                                {pv.email}
                                                            </span>
                                                        </div>
                                                    </Link>
                                                </div>
                                            ))
                                        }
                                    </>
                                :
                                    <InfiniteScroll
                                        dataLength={partVariables.length}
                                        next={fetchMoreData}
                                        hasMore={!allDataLoaded}
                                        loader={<LoadingPage/>}
                                    >
                                        <div className="line-container ">
                                            <div className='row-employe'>
                                                <b className='line-cell-sm'>Paie</b>
                                                <b className='line-cell-sm'>Attribué</b>
                                                <b className="status-line"><StatusLabel color="grey"/></b>
                                                <b>Employé</b>
                                            </div>
                                        </div>
                                        {
                                            partVariables.map((pv, index) => (
                                                <div className={`table line-container ${currentId && currentId == pv.id ? 'selected' : ''}`} key={index}>
                                                    <div className="row-employe" onClick={() => setCurrentId(pv.id)}>
                                                        <span className='capitalize line-cell-sm'>
                                                            {moment(pv.date_paie).format("MMM YYYY")}
                                                        </span>
                                                        <span className={`line-cell-sm ${pv.montant < pv.total ? 'color-pink' : pv.montant > pv.total && 'color-green'}`}>
                                                            {showAmount(pv.montant)}
                                                        </span>
                                                        <span className="status-line">
                                                            <StatusLabel color={pv.status_color} />
                                                        </span>
                                                        <span>
                                                            [{matricule(pv)}] {pv.employe}
                                                        </span>
                                                    </div>
                                                </div>
                                            ))
                                        }
                                    </InfiniteScroll>
                            }
                        </div>
                }
            </div>
    } </>
}