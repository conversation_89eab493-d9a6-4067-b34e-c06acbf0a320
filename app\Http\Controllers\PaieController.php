<?php

namespace App\Http\Controllers;

use App\Models\Employe;
use App\Models\Historique;
use App\Models\Paie;
use App\Models\PartVariable;
use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaieController extends Controller
{
    private static $attributeNames = array(
        'nom' => 'Nom',
        'societe_id' => 'Immatriculation',
        'fonction_id' => 'Fonction',
        'agence_id' => 'Agence',
        'date_embauche' => "Date d'embauche",
        'date_confirmation' => "Date de confirmation DGM",
        'date_conf_soit' => "Date de confirmation SOIT",
        'numero_employe' => "Num. employé DGM",
        'num_emp_soit' => "Num. employé SOIT",
        'numero_stagiaire' => "Num. stagiaire",
        'nb_heure_contrat' => "Nb d'heure contrat",
        'sal_base' => "Salaire de base",
        'idm_depl' => "Indemnité de déplacement",
        'part_variable' => "Part variable",
        'perdiem' => "Perdiem",
        'service_id' => "Service",
        'titre' => "Titre",
        'categorie' => "Catégorie professionelle",
        'nb_heure_travaille' => "Nb heures travaillées",
        'salaire_mensuel' => "Salaire mensuel",
        'masse_salariale' => "Masse Salariale",
        'salaire_brut' => "Salaire brut",
        "irsa" => "Irsa",
        "cnaps" => "Cnaps",
        "salfa" => "Salfa",
        "salfa_pat" => "Salfa Patronale",
        "cnaps_pat" => "Cnaps Patronale",
        "date_paie" => "Date de la paie",
    );

    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }
    public function search(Request $request)
    {
        $searchArray = [];

        if ($request->id) {
            $searchArray[] = " p.id = " . $request->id. " ";
        }
        else {
            if($request->site_id){
                $searchArray[] = " p.site_id=". $request->site_id;
            }
            if($request->created_at){
                $searchArray[] = " p.created_at > '$request->created_at 00:00:00' and p.created_at <= '$request->created_at 23:59:59'";
            }
            if ($request->date_paie) {
                list($year, $month) = explode('-', $request->date_paie);
                $searchArray[] = " YEAR(p.date_paie) = '" . $year . "' AND MONTH(p.date_paie) = '" . $month . "' ";
            }
            if ($request->agence_id) {
                $searchArray[] = " p.agence_id = " . $request->agence_id . " ";
            }
            if ($request->employe_id) {
               $searchArray[] = " p.employe_id= " . $request->employe_id ." ";
            }
            if ($request->status) {
                $searchArray[] = " p.status = '$request->status' ";
            }
        }
        
        $query_where = "";
        $query_and = "";
        $orderBy = "";	
        if ($request->offset || $request->offset > -1) {
            $orderBy = " order by p.id DESC limit " . $request->offset . ", 30";
        }
        else {
            $orderBy = " order by p.id DESC";
        }
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " ";
        }
        $query_where = $query_where . " " . $orderBy;
        $query_and = $query_and . " " . $orderBy;
        return compact("query_where", "query_and");
    }

    public function index(Request $request)
    {
        if (in_array($request->user()->role,["resp_rh", "validateur"])) {
            $sqlSelect = "SELECT p.*, p.id, p.perdiem, p.idm_depl as 'idmDepl', 
                p.part_variable as 'partVariable', p.prime_anc as 'primeAnc', p.societe_id,
                p.nom as 'employe', p.numero_stagiaire, p.numero_employe, p.num_emp_soit, st.nom as 'site',
                p.date_paie, emp.soft_delete, p.nb_heure_travaille, agc.nom as agence, p.net_a_payer,
                p.sal_base,p.salaire_mensuel,p.salaire_brut, p.masse_salariale, p.prime_exceptionnelle, p.idm_licenciement, p.prime_assid,p.prime_resp, p.status,
                stat.description, stat.color, p.employe_id, p.date_embauche, p.site_id, p.nb_heure_contrat  ,
                f.libelle as fonction_designation, agc.code_agence as code_agence, agc.nom as nom_agence,
                st.adresse as adresse_site, st.nom as 'site_nom'
                FROM paies p
                LEFT JOIN fonctions f ON p.fonction_id = f.id
                LEFT JOIN employes emp ON emp.id = p.employe_id
                LEFT JOIN status stat ON stat.name = p.status
                LEFT JOIN agences agc ON agc.id = p.agence_id
                LEFT JOIN sites st ON st.idsite = emp.site_id";
            $search = $this->search($request);
            $sqlSelect = $sqlSelect. $search['query_where'];
            $paies = DB::select($sqlSelect);
            return response(compact('paies'));
        }
        return response(["error" =>"EACCES"]);
    }  

    public function get_to_done_all(Request $request) {
        if (in_array($request->user()->role,["resp_rh"])) {
            $paies = DB:: select("SELECT id, employe_id FROM paies 
                where (`status` = 'demande' or `status` = 'traite') and date_paie = ? ",
                [$request->date_paie]
            );
            return response(compact('paies'));
        }
        return response(["error" => "EACCES"]);
    }

    public function show($id, Request $request){
        if (in_array($request->user()->role, ["resp_rh", "validateur"])) {
            $paie = DB::select("SELECT p.*, p.id, p.sal_base as 'salBase', p.perdiem, p.idm_depl as 'idmDepl', 
                p.part_variable as 'partVariable', p.prime_anc as 'primeAnc', p.societe_id,
                p.nom as 'employe', p.numero_stagiaire, emp.num_emp_saoi, p.fonction_id, p.agence_id,
                p.numero_employe ,p.num_emp_soit, st.nom as 'site',
                p.date_paie, emp.soft_delete, p.nb_heure_travaille, agc.nom as agence, p.net_a_payer,
                p.sal_base,p.salaire_mensuel,p.salaire_brut, p.masse_salariale, p.prime_exceptionnelle, p.idm_licenciement, p.prime_assid,p.prime_resp, p.status,
                stat.description, stat.color, p.employe_id, p.date_embauche, p.site_id, p.real_site_id, p.nb_heure_contrat  ,
                f.libelle as fonction_designation, agc.code_agence as code_agence, agc.nom as nom_agence,
                st.adresse as adresse_site, st.nom as 'site_nom', us.name as 'user_nom', us.email as 'user_email',p.user_id
                FROM paies p
                LEFT JOIN fonctions f ON p.fonction_id = f.id
                LEFT JOIN employes emp ON emp.id = p.employe_id
                LEFT JOIN status stat ON stat.name = p.status
                LEFT JOIN agences agc ON agc.id = p.agence_id
                LEFT JOIN sites st ON st.idsite = emp.site_id
                LEFT JOIN users us ON us.id = p.user_id
                WHERE p.id = ?", [$id])[0];
            $pj = DB::select("SELECT pj.id FROM piece_jointes pj WHERE paie_id = ?", [$id]);
            $paie->nb_pj = count($pj);
            return response()->json($paie);
        }
        return response(['error'=>'EACCES']);
    }

    public function getReclamationByPaieId(Request $request, $id){
        if($request->user()->role == 'resp_rh')
        {
            $paie = Paie::find($id);
            $pointages = DB::select("SELECT p.id, p.reclamation_id, p.date_pointage, p.site_id, s.nom as 'site'
                FROM pointage_reclamations p
                LEFT JOIN sites s on s.idsite = p.site_id
                LEFT JOIN reclamations rec on rec.id = p.reclamation_id
                WHERE rec.date_paie = ? and rec.employe_id = ?
                order by p.date_pointage desc",[$paie->date_paie, $paie->employe_id]);
            return response()->json($pointages);
        }
        return response(["error" => "EACCES"]);
    }

    public function getReclamation(Request $request){
        if($request->user()->role == 'resp_rh'){
            $datePaie = $request->date_paie;
            list($year, $month) = explode('-', $datePaie);
            $datePaie = $year . "-" . str_pad($month, 2, '0', STR_PAD_LEFT);
            $pointages = DB::select("SELECT COUNT(p.id)*12 as heures
                FROM pointage_reclamations p
                LEFT JOIN sites s on s.idsite = p.site_id
                LEFT JOIN reclamations rec on rec.id = p.reclamation_id
                WHERE rec.employe_id = ? AND rec.status = 'done' AND rec.date_paie = ?
               ",[$request->employe_id, $datePaie."-20"])[0];
            return $pointages->heures;
        }
        return response(["error" => "EACCES"]);
    }

    public function getCongePaye(Request $request){
        $sConge = DB::select("SELECT * FROM absences WHERE date_paie = ? and employe_id = ? and absences.status = 'done'",
        [$request->date_paie, $request->employe_id]);
        return response(compact('sConge'));
    }

    public function storePaie(Request $request){
        if (in_array($request->user()->role, ['resp_rh']))
        {
            $paie = new Paie();
            $verifyIfExist = $this->getPaie($request->employe_id, $request->date_paie);
            if($verifyIfExist) 
                return \response()->json(['error' => "Paie déja existe"]);
            $validator = $this->validatePaie($request);
            if($validator->fails()){
                return \response()->json(['error' => $validator->errors()->first()]);
            }
            else {
                $paie->fill($request->all());
                // $paie->agence_id = $request->agence_id;
                // $paie->all_fam_cnaps = $request->all_fam_cnaps;
                // $paie->autre_deduction = $request->autre_deduction;
                // $paie->avance_15e = $request->avance_15e;
                // $paie->avance_special = $request->avance_special;
                // $paie-> = $request->
                $paie->user_id = $request->user()->id;
                $paie->created_at = new \DateTime();
                $paie->updated_at = new \DateTime();
                $paie->status = "demande";
                $employe = Employe::find($request->employe_id);
                if($paie->save()){   
                    if(!$employe->last_date_paie || new \Datetime($employe->last_date_paie) > new \DateTime($paie->date_paie)){
                            $employe->last_date_paie = $paie->date_paie;
                            $employe->save();
                    } 
                    HistoriqueController::new_paie($request, $paie);
                }
            }
            return response()->json(["success" => "Paie bien enregistré", "paie_id" => $paie->id, "employe_id" => $paie->employe_id]);
        }
        return response(["error" => "EACCES"]);
    }

    private function getPaie($employe_id, $datePaie){
        $paie = Paie::where('employe_id', $employe_id)->where('date_paie', $datePaie)->first();
        if($paie !== null) return true;
        else return false;
    }

    protected function validatePaie($request){
        $auth = $request->user();
        if(in_array($auth->role,['resp_rh']) || $auth->id == '191'){    
            $validePayement =[];
            // if ($request->mode_payement == 'VIR') {
            //     $validePayement = [
            //         'banque' => ['required'],
            //         'numero_compte' => ['required', 'regex:/(^[0-9]{11}$)/u'],
            //         'code_banque' => ['required', 'regex:/(^[0-9]{5}$)/u'],
            //         'code_guichet' => ['required', 'regex:/(^[0-9]{5}$)/u'],
            //         'rib' => ['required', 'regex:/(^[0-9]{2}$)/u'],
            //     ];
            // } else if ($request->mode_payement == 'MOB') {
            //     $validePayement = [
            //         'numero_tel' => ['required', 'regex:/(^03[0-9]{1} [0-9]{2} [0-9]{3} [0-9]{2}$)/u'],
            //     ];
            // }
            $validator = Validator::make($request->all(),array_merge([
                'employe_id' => ['required'],
                'sal_base' => ['required'],
                'nb_heure_contrat' => ['required'],
                'nb_heure_travaille' => ['required'],
                "nom" => ["required"],
                "salaire_mensuel" => ["required"],
                "masse_salariale" => ["required"],
                "irsa" => 'required|numeric|min:3000',
                "cnaps" => ['required'],
                "salfa" => ['required'],
                "cnaps_pat" => ["required"],
                "salfa_pat" => ["required"],
                "date_paie" => ["required"],
                "salaire_brut" => ["required"],
                "net_a_payer" => ["required"],
            ], $validePayement))->setAttributeNames(self::$attributeNames);
        }
        return $validator;
    }

    public function cancel_paie(Request $request, $id){
        $paie = Paie::find($id);
        if((in_array($paie->status, ['demande', 'traite']) && $request->user()->role == 'resp_rh')
            && ($paie->user_id == $request->user()->id)){
            $validator = Validator::make($request->all(),[
                'note' => 'required',
            ]);
            if($validator->fails()){
                return response(['error' => $validator->errors()->first()]);
            }
            $paie->status = 'draft';
            $paie->updated_at = new \DateTime();
            $paie->note_id = HistoriqueController::action_paie($request, "Paie annulé", $id);
            if($paie->save()){
                return response(["success" =>"Paie annulé", "id" => $paie->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function conge_done($employe_id, $employe = null, $conges = null){
        if ($employe_id) {
            if(!$employe){
                $employe = Employe::find($employe_id);
            }
            if(!$conges){
                $conges = DB::select("SELECT depart, retour, type_absence FROM absences ab 
                    WHERE type_absence = 'conge' and ab.status = 'done' and ab.employe_id = ?", [$employe_id]);
            }
            $date_begin = null;
            if ($employe->societe_id == 1) $date_begin = $employe->date_confirmation;
            elseif ($employe->societe_id == 2) $date_begin = $employe->date_conf_soit;

            if ($date_begin == null || $date_begin=='0000-00-00')
                $droit = 0;
            else if ($date_begin) {
                $droit = ((Carbon::parse(new DateTime())->diffInDays(Carbon::parse(new DateTime($date_begin)))) / 30.5 * 2.5);
                if(count($conges) > 0) {
                    if($date_begin){
                        $droit = $droit - (
                            array_reduce($conges, function($carry, $c){
                                try{
                                    $diffHours = Carbon::parse($c->retour)->diffInHours(Carbon::parse($c->depart));
                                }
                                catch(Exception $e){
                                    $diffHours = Carbon::parse($c['retour'])->diffInHours(Carbon::parse($c['depart']));
                                }
                                return $carry + $diffHours;
                            }, 0)/24
                        );
                    }
                }
            }
            if(($droit % 1)>= 0.5 )$droit = round($droit) + 0.5;
            else $droit = round($droit);
        }
        return response()->json(compact('employe', 'conges', 'droit' ))->getContent();
    }

    public function do_traite(Request $request, $id){
        $paie = Paie::find($id);
        $old_paie = clone $paie;
        if ($request->user()->role == "resp_rh" && $paie->status == "demande") {
            $validator = $this->validatePaie($request);
            if ($validator->fails()) {
                return response(['error'=> $validator->errors()->first()]);
            }
            $paie->status = "traite";
            $paie->updated_at = new \DateTime();
            $paie->update($request->all());
            if($paie->save()){
                HistoriqueController::update_paie($request, $old_paie, "Paie en cours de traitement");
                return response(["success" => "Paie en cours de traitement", "id" => $paie->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function edit(Request $request, $id){
        $paie = Paie::find($id);
        $old_paie = clone $paie;
        if ($request->user()->role == "resp_rh" && in_array($paie->status, ['demande','traite'])) {
            $validator = $this->validatePaie($request);
            if($validator->fails()) 
                return response(['error' =>$validator->errors()->first()]);
            $paie->update($request->all());
            $paie->updated_at = new \DateTime();
            if($paie->save()){
                HistoriqueController::update_paie($request, $old_paie, "Paie modifié");
                return response(["success" => "Paie modifié", "id" => $paie->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function getEmploye(Request $request){
        $deduction = DB::select("SELECT SUM(montant) as montant 
            FROM deductions WHERE employe_id = $request->employe_id 
            AND date_paie = '".$request->date_paie."' and status = 'done'")[0];

        $congePaye = DB::select("SELECT * FROM absences 
            WHERE (type_absence = 'conge' or type_absence = 'permission') 
            AND date_paie = '" . $request->date_paie . "' and employe_id = " . $request->employe_id . " and status = 'done'");

        $prime = DB::select("SELECT SUM(montant) as 'montant' 
            FROM primes WHERE employe_id = $request->employe_id 
            AND date_paie = '" . $request->date_paie . "' and status = 'done'")[0];

        $avance = DB::select("SELECT sum(montant) as 'montant', avc.type_avance_id, type_avc.name 
            FROM avances avc 
            LEFT JOIN type_avances type_avc on type_avc.id = avc.type_avance_id 
            WHERE employe_id = $request->employe_id and date_paie = '$request->date_paie' and `status` = 'done'
            GROUP BY avc.type_avance_id, type_avc.name ");

        $partVariable = DB::select("SELECT sum(montant) as 'montant' from critere_mensuels cm 
            LEFT JOIN part_variables pv ON pv.id = cm.part_variable_id
            WHERE 
                (pv.status = 'done' 
                -- or pv.status = 'validation'
                ) and pv.date_paie = ? and pv.employe_id = ?"
            ,[$request->date_paie,$request->employe_id])[0];

        $congePaye = json_decode(json_encode($congePaye), true);
        return compact("deduction", "congePaye", "prime", "avance", "partVariable");
    }

    public function editAndDone(Request $request, $id){
        $paie = Paie::find($id);
        $old_paie = clone $paie;
        if ($request->user()->role == "resp_rh") {
            $validator = $this->validatePaie($request);
            if($validator->fails()) 
                return response(['error' =>$validator->errors()->first()]);
            $paie->update($request->all());
            $paie->updated_at = new \DateTime();
            $paie->status = "done";
            if($paie->save()){
                HistoriqueController::update_paie($request, $old_paie, "Paie terminé");
                return response(["success" => "Paie terminé", "id" => $paie->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $paie = Paie::find($id);
        $old_paie = clone $paie;
        if($request->user()->id == $paie->user_id && $paie->status == "draft"){
            $validator = $this->validatePaie($request);
            if($validator->fails()) 
                return response(['error' => $validator->errors()->first()]);
            $paie->status = "demande";
            $paie->update($request->all());
            $paie->updated_at = new \DateTime();
            if ($paie->save()) {
                HistoriqueController::update_paie($request, $old_paie, 'Renvoie de la paie');
                return response(["success" => "Renvoie de la demande réussi", "id" => $paie->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done(Request $request, $id){
        $paie = Paie::find($id);
        if ($request->user()->role == "resp_rh" && $paie && in_array($paie->status, ['traite', 'demande'])) {
            $paie->status = "done";
            $paie->updated_at = new \DateTime();
            $paie->note_id = HistoriqueController::action_paie($request, 'Paie terminée', $id);
            if ($paie->save()) {
                return response(["success" => "Paie terminée", "id" => $paie->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done_multiple(Request $request){
        if ($request->user()->role == 'resp_rh') {
            Paie::whereIn('id', $request->ids_paie)->update(['status' => 'done', "updated_at" => new \DateTime()]);
            for ($i=0; $i < count($request->ids_paie); $i++) {
                $dataHistorique[] =
                [
                    "objet" => 'Paie terminé',
                    "note" => 'Terminaison groupée',
                    "user" => $request->user()->name . ' < ' . $request->user()->email . '>',
                    "user_id" => $request->user()->id,
                    "paie_id" => $request->ids_paie[$i],
                    "employe_id" => $request->ids_emp[$i],
                    "created_at" => new \DateTime(),
                ];
            }
            Historique::insert($dataHistorique);
            return response(["success" => "Terminaison groupé avec succés"]);
        }
        return response(["error" => "EACCES"]);
    }
    
    protected function prorata($diffHCHT, $div_hcht, $value){
        if($diffHCHT >= 0 || $diffHCHT == -12){
            return $value;
        }else  return $value * $div_hcht;
    }

    public function getHeureTravaille(Request $request)
    {
        $datePaie = $request->date_paie;
        list($year, $month) = explode('-', $datePaie);
        $datePaie = $year . "-" . str_pad($month, 2, '0', STR_PAD_LEFT);

        $monthYearBefore = "";
        if ($month == 1) {
            $monthYearBefore = $year - 1 . "-12";
        } else {
            $monthYearBefore = $year . "-" . str_pad($month - 1, 2, '0', STR_PAD_LEFT);
        }
        
        $pointages = DB::select("SELECT COUNT(id)*12 as 'heures' FROM pointages 
            WHERE pointages.employe_id= $request->employe_id AND (soft_delete = 0 or soft_delete is null)
            AND (date_pointage BETWEEN ('" . $monthYearBefore ."-20 00:00:00') AND ('".$datePaie ."-19 23:59:00'))")[0];
        $pointages_reclam = $this->getReclamation($request);
        $heures = $pointages->heures + $pointages_reclam;
        $id = $request->employe_id;
        return response(compact('heures', 'datePaie','id'));
    }
    
    protected function calculAbsencePaye($conge){
        $congePaye = array_reduce($conge, function($carry, $c){
            $diffHours = Carbon::parse($c['retour'])->diffInHours(Carbon::parse($c['depart']));
            return $carry + $diffHours;
        },0)/24;
        $cgPrisArray = array_filter($conge, function ($item) {
            return $item['type_absence'] == 'conge';
        });
        $congePris = array_reduce($cgPrisArray, function ($carry, $c) {
            $diffHours = Carbon::parse($c['retour'])->diffInHours(Carbon::parse($c['depart']));
            return $carry + $diffHours;
        }, 0) / 24;
        return response()->json(compact('congePaye', 'congePris'))->getContent();
    }

    public function PvValidationToDone($date_paie, $employe_id, Request $request){
        if ($request->user()->role == "resp_rh") {

            $partVariable = PartVariable::where("employe_id", $employe_id)
                ->where("date_paie", $date_paie)
                ->where("status", "validation")
                ->update(["status" => "done", "updated_at" => new \DateTime()]);
            
            if ($partVariable > 0) {
                $historiquePartVariable = DB::select("SELECT id FROM part_variables WHERE date_paie = ? and employe_id = ?",[$date_paie, $employe_id]);
                $groupIds = array_map(function ($resultItem) {
                    return (string)$resultItem->id;
                }, $historiquePartVariable);
                for ($i = 0; $i < count($groupIds); $i++) {
                    $dataHistorique[] = [
                        "objet" => 'Part Variable terminé',
                        "note" => "Terminaison automatique",
                        "user" => $request->user()->name . ' < ' . $request->user()->email . '>',
                        "user_id" => $request->user()->id,
                        "part_variable_id" => $groupIds[$i],
                        "created_at" => new \DateTime(),
                    ];
                }
                Historique::insert($dataHistorique);

                return response(["success" => $partVariable ." Terminé", "nb" => $partVariable]);
            }
            
            // return response(["success" => "Aucune part variable en cours de validation", "nb" => $partVariable]);
            
        }
        return response(["error" => "EACCES"]);
    }

    public static function RecalculePaie(Request $request){
        if ($request->user()->role == "resp_rh") {
            $paie = Paie::find($request->paie_id);

            $avance = DB::select("SELECT sum(montant) as 'montant', avc.type_avance_id, type_avc.name 
                FROM avances avc 
                LEFT JOIN type_avances type_avc on type_avc.id = avc.type_avance_id 
                WHERE employe_id = $request->employe_id and date_paie = '$request->date_paie' and `status` = 'done'
                GROUP BY avc.type_avance_id, type_avc.name ");

            $deduction = DB::select("SELECT SUM(montant) as montant 
                FROM deductions WHERE employe_id = $request->employe_id 
                AND date_paie = '" . $request->date_paie . "' and status = 'done'")[0];

            $prime = DB::select("SELECT SUM(montant) as 'montant' 
                FROM primes WHERE employe_id = $request->employe_id 
                AND date_paie = '" . $request->date_paie . "' and status = 'done'")[0];

            $partVariable = DB::select("SELECT sum(montant) as 'montant' from critere_mensuels cm 
                LEFT JOIN part_variables pv ON pv.id = cm.part_variable_id
                WHERE (pv.status = 'done' or pv.status = 'validation') and pv.date_paie = ? and pv.employe_id = ?",
                [$request->date_paie, $request->employe_id]
            )[0];
            $conge = DB::select("SELECT * FROM absences 
                WHERE (type_absence = 'conge' or type_absence = 'permission') 
                AND date_paie = '" . $request->date_paie . "' and employe_id = " . $request->employe_id . " and status = 'done'");

            $congePaye = array_reduce($conge, function ($carry, $c) {
                $diffHours = Carbon::parse($c->retour)->diffInHours(Carbon::parse($c->depart));
                return $carry + $diffHours;
            }, 0) / 24;
            $cgPrisArray = array_filter($conge, function ($item) {
                return $item->type_absence == 'conge';
            });
            $congePris = array_reduce($cgPrisArray, function ($carry, $c) {
                $diffHours = Carbon::parse($c->retour)->diffInHours(Carbon::parse($c->depart));
                return $carry + $diffHours;
            }, 0) / 24;

            $avance15 = 0;
            $avc_embauche = 0;
            $avc_speciale = 0;
            foreach ($avance as $avc) {
                if ($avc->name == "avance15") $avance15 = $avc->montant;
                if ($avc->name == "avance_embauche") $avc_embauche = $avc->montant;
                if ($avc->name == "avance_speciale") $avc_speciale = $avc->montant;
            }

            $netAPayer = $paie->net_a_payer + $paie->avance_15e + $paie->avance_special + $paie->avance_speciale_embauche - $avance15 - $avc_embauche - $avc_speciale;
            if ($deduction->montant != $paie->autre_deduction) {
                $netAPayer = $netAPayer + $paie->autre_deduction - $deduction->montant ;
                $paie->autre_deduction = $deduction->montant;
            }
            if ($prime->montant != $paie->prime) {
                $netAPayer = $netAPayer - $paie->prime + $prime->montant;
                $paie->prime = $prime;
            }
            if ($paie->part_variable != $partVariable->montant) {
                $netAPayer = $netAPayer - $paie->part_variable + $partVariable->montant;
                $paie->part_variable = $partVariable->montant;
            }
            $newCalcMasseSalariale = 0;
            if ($congePaye != $paie->s_conge) {
                // $paie->s_conge = $congePaye;
                $paie->conge_reste = $paie->conge_reste + $paie->conge_pris - $congePris;
                $paie->conge_pris = $congePris;
                $cPaye = $paie->sal_base / 30 * $congePaye;
                $oldCpaye = $paie->sal_base / 30 * $paie->s_conge;
                $newSalBrut = $paie->salaire_brut - $oldCpaye + $cPaye;
                $salfa = $newSalBrut / 100;
                $cnaps = $newSalBrut / 100;
                $netImp = $newSalBrut - $salfa - $cnaps;
                $tranche0 = 350000;
                $tranche1 = 400000;
                $tranche2 = 500000;
                $tranche3 = 600000;
                $irsa_ = 0;
                if ($netImp <= $tranche1) {
                    $irsa_ = ($netImp - $tranche0) * 0.05; //5%
                } else if ($netImp <= $tranche2) {
                    $irsa_ = ($tranche1 - $tranche0) * 0.05; //5%
                    $irsa_ = $irsa_ + ($netImp - $tranche1) * 0.1; //10%
                } else if ($netImp <= $tranche3) {
                    $irsa_ = ($tranche1 - $tranche0) * 0.05; //5%
                    $irsa_ = $irsa_ + ($tranche2 - $tranche1) * 0.1; //10%
                    $irsa_ = $irsa_ + ($netImp - $tranche2) * 0.15; //15%
                } else if ($netImp > $tranche3) {
                    $irsa_ = ($tranche1 - $tranche0) * 0.05; //5%
                    $irsa_ = $irsa_ + ($tranche2 - $tranche1) * 0.1; //10%
                    $irsa_ = $irsa_ + ($tranche3 - $tranche2) * 0.15; //15%
                    $irsa_ = $irsa_ + ($netImp - $tranche3) * 0.2; //20%
                }
                if ($irsa_ <= 3000) {
                    $irsa_ = 3000;
                }
                $netAPayer = $paie->net_a_payer + $paie->irsa - $irsa_ - $paie->net_imposable + $netImp;
                $newCalcMasseSalariale = $irsa_ + $salfa + $cnaps - $paie->salfa - $paie->cnaps - $paie->irsa ;
                $paie->salaire_brut = $newSalBrut;
            }
            $salfaPatronal = ($netAPayer + $avance15) * 5 / 100;
            $cnapsPat = ($netAPayer + $avance15) * 13 / 100;
            $masseSalariale = $netAPayer + $salfaPatronal + $cnapsPat + $paie->irsa_pat + $paie->salfa + $paie->cnaps + $paie->irsa + $avance15 + $newCalcMasseSalariale;
            $paie->avance_15e = $avance15;
            $paie->avance_speciale_embauche = $avc_embauche;
            $paie->avance_special = $avc_speciale;
            $paie->masse_salariale = $masseSalariale;
            $paie->net_a_payer = $netAPayer;
            $paie->salfa_pat = $salfaPatronal;
            $paie->cnaps_pat = $cnapsPat;
            if ($congePaye != $paie->s_conge) {
                $paie->s_conge = $congePaye ?? $paie->s_conge;
                $paie->salfa = $salfa ?? $paie->salfa;
                $paie->cnaps = $cnaps ?? $paie->cnaps;
                $paie->irsa = $irsa_ ?? $paie->irsa;
                $paie->net_imposable = $netImp ?? $paie->net_imposable;
            }
            $paie->updated_at = new \DateTime();
            $paie->save();
        }
    }

    public function generate_paie(Request $request, $id=null){
        if ($request->user()->role == 'resp_rh'|| $request->user()->id == '191') {
            $datePaie = $request->date_paie;
            list($year, $month) = explode('-', $datePaie);
            $monthYearBefore = "";
            if ($month == 1) {
                $monthYearBefore = $year - 1 . "-12";
            } else {
                $monthYearBefore = $year . "-" . str_pad($month - 1, 2, '0', STR_PAD_LEFT);
            }
            $monthYear = $year . "-" . str_pad($month, 2, '0', STR_PAD_LEFT);

            if ($request->recalcul) {
                $employe = DB::select("SELECT emp.*, p.id as 'paie_id' FROM employes emp
                    LEFT JOIN paies p ON emp.id = p.employe_id
                    LEFT JOIN fonctions f on f.id = emp.fonction_id
                    where p.id is not null  AND p.date_paie = ? 
                    AND (emp.soft_delete = 0 or emp.soft_delete is null) and (f.sal_forfait is null or f.sal_forfait = 0 ) and emp.id = ?
                    LIMIT 1 ",[$request->date_paie, $id])[0];
                $old_paie = Paie::find($employe->paie_id);
                $request->merge(['employe_id' => $employe->id]);
                $heureT = $this->getHeureTravaille($request);
                $content = json_decode($heureT->getContent(), true);
                $employe->nb_heure_travaille = $content['heures'];
                $employe->rappel = $old_paie->rappel;
                $employe->all_fam_cnaps = $old_paie->all_fam_cnaps;
                $employe->remb_frais_fixe = $old_paie->remb_frais_fixe;
                
                $employe->retenue_formation = $old_paie->retenue_formation;

                $employe->heure_ferie = $old_paie->heure_ferie;
                $employe->hm_dim = $old_paie->hm_dim;
                $employe->h_maj = $old_paie->h_maj;

                $employe->prime_exceptionnelle = $old_paie->prime_exceptionnelle;
                $employe->prime_anc = $old_paie->prime_anc;
                $employe->prime_entret = $old_paie->prime_entret;
                $employe->prime_assid = $old_paie->prime_assid;
                $employe->prime_resp = $old_paie->prime_resp;
                $employe->idm_depl = $old_paie->idm_depl;
                $employe->prime_div = $old_paie->prime_div;
                $employe->perdiem = $old_paie->perdiem;

                $employe->nprv_preavis_deductible = $old_paie->nprv_preavis_deductible;
                $employe->nprv_preavis_payer = $old_paie->nprv_preavis_payer;
                $employe->nprv_licenciement = $old_paie->nprv_licenciement;
                $employe->preavis_moins = $old_paie->preavis_moins;

                $employe->mode_payement = $old_paie->mode_payement;
                if ($employe->mode_payement == "MOB") {
                    $employe->numero_tel = $old_paie->numero_tel;
                } else if ($employe->mode_payement == "VIR") {
                    $employe->banque = $old_paie->banque;
                };
                $employe->cin = $old_paie->cin;
                $recalcul = true;
            }
            else if($id){
                $employe = DB::select("SELECT DISTINCT emp.id, emp.*,(SELECT COUNT(id)*12 FROM pointages 
                    WHERE pointages.employe_id= emp.id 
                    AND (date_pointage BETWEEN ('$monthYearBefore-20 00:00:00') AND ('$monthYear-19 23:59:00'))) as 'nb_heure_travaille' 
                    from employes emp
                    LEFT JOIN paies p ON emp.id = p.employe_id
                    LEFT JOIN fonctions f on f.id = emp.fonction_id
                    where p.id is null AND (emp.soft_delete = 0 or emp.soft_delete is null) and (f.sal_forfait is null or f.sal_forfait = 0 )
                    AND emp.id = ?
                    LIMIT 0, 1", [$id]);
                if(count($employe) > 0){
                    $employe = $employe[0];
                }
                else{
                    return "Aucun employe trouvé";
                }
                $employe->mode_payement = $employe->mode_paiement;
                if ($employe->mode_paiement == "MOB") {
                    $employe->numero_tel = $employe->numero_mobile;
                } else if ($employe->mode_paiement == "VIR") {
                    $employe->banque = $employe->nom_paiement;
                };
                $employe->cin = $employe->cin_text;
            }
            if ($employe) {
                try {
                    $request->merge(["employe_id " => $employe->id]);
                    // $request->employe_id = $employe->id;
                    $empParams = $this->getEmploye($request);
                    $deduction = $empParams["deduction"]->montant ?? 0;
                    $prime = $empParams["prime"]->montant ?? 0;
                    $conge = $empParams["congePaye"];
                    $part_variable = $empParams["partVariable"];
                    $avance15 =0;
                    $avc_embauche = 0;
                    $avc_speciale =0;
                    foreach ($empParams["avance"] as $avc) {
                        if($avc->name == "avance15" ) $avance15 = $avc->montant;
                        if($avc->name == "avance_embauche" ) $avc_embauche = $avc->montant;
                        if($avc->name == "avance_speciale" ) $avc_speciale = $avc->montant;
                    }
                    $employe->avance15 = $avance15;
                    $employe->avc_speciale = $avc_speciale;
                    $employe->avc_embauche = $avc_embauche;
                    $nbConge = json_decode($this->calculAbsencePaye($conge), true);                
                    $employe->deduction = $deduction;
                    $employe->prime = $prime;
                    $employe->conge_paye = $nbConge['congePaye'];
                    $employe->conge_pris = $nbConge['congePris'];
                    $cgReste = json_decode($this->conge_done($employe->id), true);
                    $employe->conge_reste = $cgReste['droit'];
                    $employe->part_variable = $part_variable->montant;
                    $employe->date_paie = $request->date_paie;
                    if ($request->recalcul) {
                        return $this->calc_all($employe, $request, true );
                    }
                    else if ($id) {
                        return $this->calc_all($employe, $request );
                    }
                    else {
                        $store = $this->calc_all($employe, $request);
                        if($store){
                            // $pvDone = $this->PvValidationToDone($monthYear.'-20', $employe->id, $request)->getContent();
                            // $nbPartVariable = isset(json_decode($pvDone, true)["nb"]);
                            // if ($nbPartVariable > 0) 
                            //     return response(["success"=>"Paie creer avec " . $nbPartVariable. "part variable terminé"]);
                            return response( ["success"=>"Paie enregistré", "id_employe"=>$employe->id]);
                        }
                    }
                } catch (\Exception $e) {
                    return response(["error" => "Une erreur se produit", "error"=>$e->getMessage()]);
                }
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function get_only_calcul(Request $request){
        return response()->json($this->calc_all($request->all(), $request));
    }

    public function calc_all($paie, $user = null, $recalcul=false)
    {
        $paie = json_decode(json_encode($paie));
        $calc = [];
        $HS30 = 0;
        $heureContrat = 0;
        $heureTravaille = 0;
        $heureTravaille = $paie->nb_heure_travaille;
        if ($paie->nb_heure_contrat == null 
            || $paie->nb_heure_contrat == 0) {
            return ["error" => "Heure contrat non definie"];
        }
        else 
            $heureContrat = $paie->nb_heure_contrat;
        if ($paie->sal_base != null) {
            $salaireBase = $paie->sal_base;
        }
        else {
            return ["error" => "Salaire de base non definie"];
        }
        $diffHCHT = $heureTravaille - $heureContrat;
        $div_hcht = $heureTravaille / $heureContrat;
        $HSFR = 0;
        $salaireMensuel = $salaireBase * $heureTravaille / $heureContrat;
        
        if ($diffHCHT == -12) {
            $salaireMensuel = $salaireBase;
        }
        else if ($heureTravaille > $heureContrat) {
            $HSFR = $diffHCHT;
            $salaireMensuel = $salaireBase;
        }
        if ($HSFR > 33.6) {
            $HS30 = 33.6;
        }
        else {
            $HS30 = $HSFR;
        }
        $MH_S30 = ($salaireBase / $heureContrat) * $HS30 * 0.3;
        $HS50 = $HSFR - $HS30;
        $MH_S50 = ($salaireBase / $heureContrat) * $HS50 * 0.5;
        $hFerie = $paie->heure_ferie ?? 0;
        // $hFerie = 0;
        $MmajFerie = $salaireBase / $heureContrat * $hFerie;
        $hmDim = $paie->hm_dim ?? 0;
        // $hmDim = 0;
        $MMajdim = $salaireBase / $heureContrat * $hmDim * 0.4;
        $hMaj = $paie->h_maj ?? 0;
        // $hMaj = 0;
        $Mmaj = $hMaj / $heureContrat * $heureTravaille;
        $totMaj = $MH_S30 + $MH_S50 + $MMajdim + $Mmaj + $MmajFerie;
        
        $primeExceptionnelle = $paie->prime_exceptionnelle ?? 0;
        // $primeExceptionnelle = 0;
        $primeExceptionnelleProrata = $this->prorata($diffHCHT, $div_hcht, $primeExceptionnelle);
        $primeDiv = $paie->prim_div ?? 0;
        // $primeDiv = 0;
        $primeDivProrata = $this->prorata($diffHCHT, $div_hcht, $primeDiv);
        $indemniteDeplacement = $paie->idm_depl ?? 0;
        // $indemniteDeplacement = 0;
        $indemniteDeplacementProrata = $this->prorata($diffHCHT, $div_hcht, $indemniteDeplacement);
        $primeAssid = $paie->prime_assid ?? 0;
        // $primeAssid = 0;
        $primeAssidProrata = $this->prorata($diffHCHT, $div_hcht, $primeAssid);
        $primeResp = $paie->prime_resp ?? 0;
        // $primeResp = 0;
        $primeRespProrata = $this->prorata($diffHCHT, $div_hcht, $primeResp);
        $primeEntretien = $paie->prime_entret ?? 0;
        // $primeEntretien = 0;
        $primeEntretienProrata = $this->prorata($diffHCHT, $div_hcht, $primeEntretien);
        if ($paie->prime_anc == null)
            $primeAnciennete = 0;
        else $primeAnciennete = $paie->prime_anc;
        $primeAncienneteProrata = $this->prorata($diffHCHT, $div_hcht, $primeAnciennete);
        $totalProrataGrat = $primeExceptionnelleProrata + $primeDivProrata + $indemniteDeplacementProrata 
                            + $primeAssidProrata + $primeRespProrata + $primeEntretienProrata + $primeAncienneteProrata;
        $totalProrataGrat = $primeExceptionnelleProrata + $primeDivProrata + $indemniteDeplacementProrata
                            + $primeAssidProrata + $primeRespProrata + $primeEntretienProrata + $primeAncienneteProrata;

        $sConge = $paie->conge_paye;
        $cPaye = $salaireBase / 30 * $sConge;
        $NPrvDeductible = $paie->nprv_preavis_deductible ?? 0;
        $preavisDeductible = $salaireBase / 30 * $NPrvDeductible;
        
        $NPrvPayer = $paie->nprv_preavis_payer ?? 0;
        $preavisPayer = $salaireBase / 30 * $NPrvPayer;

        $NPrvLicenciement = $paie->nprv_licenciement ?? 0;
        $indemniteLicenciement = $salaireBase / 30 * $NPrvLicenciement;

        $rappel = $paie->rappel ?? 0;
        // $rappel = 0;
        $salaireBrut = $salaireMensuel + $totMaj + $totalProrataGrat + $cPaye + $rappel
                    - $preavisDeductible + $preavisPayer + $indemniteLicenciement;

        $salfa = $salaireBrut / 100;
        $cnaps = $salaireBrut / 100;
        $netImp = $salaireBrut - $salfa - $cnaps;
        $irsa = $this->calculIrsa($netImp);
        $perdiem = 0;
        if ($paie->perdiem != null) {
            $perdiem = $paie->perdiem;
        }
        $perdiemProrata = $this->prorata($diffHCHT, $div_hcht, $perdiem);
        $retenueFormation = $paie->retenue_formation ?? 0;
        // $retenueFormation = 0;
        $autreDeduction = $paie->deduction;
        $avanceSpecial = $paie->avc_speciale;
        $avance15e = $paie->avance15;
        $preavisMoins = $paie->preavis_moins ?? 0;
        // $preavisMoins = 0;
        $allFamCnaps = $paie->all_fam_cnaps ?? 0;
        // $allFamCnaps = 0;
        $rembFraisFixe = $paie->remb_frais_fixe ?? 0;
        // $rembFraisFixe = 0;
        $avanceSpecialeEmbauche = $paie->avc_embauche;
        $prime = $paie->prime;
        $partVariable = 0;
        if($paie->part_variable != null)  
            $partVariable = $paie->part_variable;

        $netAPayer = $netImp - $irsa - $retenueFormation - $autreDeduction - $avanceSpecial - $avance15e - $preavisMoins + $allFamCnaps
            + $rembFraisFixe - $avanceSpecialeEmbauche + $prime
            + $perdiemProrata + $partVariable;
        $salfaPatronal = ($netAPayer + $avance15e) * 5 / 100;
        $cnapsPat = ($netAPayer + $avance15e) * 13 / 100;
        $irsaPat = 0;
        $masseSalariale = $netAPayer + $salfaPatronal + $cnapsPat + $irsaPat + $salfa + $cnaps + $irsa + $avance15e;
        $calc["masse_salariale"] = round($masseSalariale, 2);
        $calc["net_a_payer"] = round($netAPayer, 2);
        $calc["salaire_brut"] = round($salaireBrut , 2);
        $calc["cnaps"] = round($cnaps, 2);
        $calc["cnaps_pat"] = round($cnapsPat, 2); 
        $calc["irsa"] = round($irsa ,2);
        $calc["cnaps"] = round($cnaps, 2);
        $calc["salfa_pat"] = round($salfaPatronal ,2);
        $calc["salfa"] = round($salfa, 2);
        $calc["irsa_pat"] = round($irsaPat, 2);
        $calc["nb_heure_travaille"] = $heureTravaille;
        $calc["nb_heure_contrat"] = $heureContrat;
        $calc["sal_base"] = $salaireBase;
        $calc["prime"] = $prime;
        $calc["prime_anc"] = $paie->prime_anc ?? 0;
        $calc["prime_assid"] = $paie->prime_assid ?? 0;
        $calc["prime_exceptionnelle"] = $paie->prime_exceptionnelle ?? 0;
        $calc["prime_entret"] = $paie->prime_entret ?? 0;
        $calc["prime_resp"] = $paie->prime_resp ?? 0;
        $calc["prime_div"] = $paie->prim_div?? 0;
        $calc["part_variable"] = $partVariable;
        $calc["net_imposable"] = round($netImp ,2);
        $calc["salaire_mensuel"] = round($salaireMensuel, 2);
        $calc["diff_HcHt"] = $diffHCHT;
        $calc["employe_id"] = $paie->id;
        $calc["autre_deduction"] = $paie->deduction;
        $calc['s_conge'] = $paie->conge_paye;
        $calc["perdiem"] = $perdiem;
        $calc["mode_payement"] = $paie->mode_payement ?? $paie->mode_paiement ?? null;
        $calc["numero_tel"] = $paie->numero_tel ?? null;
        $calc["banque"] = $paie->banque ?? null;
        $calc["numero_compte"] = $paie->numero_compte;
        $calc["code_banque"] = $paie->code_banque;
        $calc["code_guichet"] = $paie->code_guichet;
        $calc["rib"] = $paie->rib;
        $calc["nom"] = $paie->nom;
        $calc["date_paie"] = $paie->date_paie;
        $calc["numero_employe"] = $paie->numero_employe;
        $calc["num_emp_soit"] =$paie->num_emp_soit;
        $calc["numero_stagiaire"] = $paie->numero_stagiaire;
        $calc["societe_id"] = $paie->societe_id;
        $calc["agence_id"] = $paie->agence_id;
        $calc["site_id"] = $paie->site_id;
        $calc["real_site_id"] = $paie->real_site_id;
        $calc["date_embauche"] = $paie->date_embauche;
        $calc["idm_depl"] = $paie->idm_depl ?? 0;
        $calc["nb_heure_convenu"] = $paie->nb_heure_convenu ?? 0;
        $calc["fonction_id"] = $paie->fonction_id;
        $calc["sal_forfait"] = $paie->sal_forfait;
        $calc["categorie"] = $paie->categorie;
        $calc["date_confirmation"] = $paie->date_confirmation;
        $calc["conge_reste"] = $paie->conge_reste;
        $calc["avance_15e"] = $avance15e;
        $calc["avance_special"] = $avanceSpecial;
        $calc["avance_speciale_embauche"] = $avanceSpecialeEmbauche;
        $calc['mh_s30'] = $MH_S30;
        $calc['mh_s50'] = $MH_S50;
        $calc['hs50'] = $HS50;
        $calc['total_prorata_grat'] = $totalProrataGrat;
        $calc['preavis_deductible'] = $preavisDeductible;
        $calc['preavis_payer'] = $preavisPayer;
        $calc['idm_licenciement'] = $indemniteLicenciement;
        $calc['hs30'] = $HS30;
        if ($recalcul) {
            $edit = $this->edit($user->merge($calc), $paie->paie_id);
            if ($edit)
                return $edit;
        }
        else if($user && $user->get_data_only){
            return $calc;
        }
        else {
            $store=$this->storePaie($user->merge($calc));
            if ($store->original) 
                return $store;
        }
        // return false;
    }

    protected function calculIrsa($base_imp){
        $tranche0 = 350000;
        $tranche1 = 400000;
        $tranche2 = 500000;
        $tranche3 = 600000;
        $irsa_ = 0;
        if ($base_imp <= $tranche1) {
            $irsa_ = ($base_imp - $tranche0) * 0.05; //5%
        } else if ($base_imp <= $tranche2) {
            $irsa_ = ($tranche1 - $tranche0) * 0.05; //5%
            $irsa_ = $irsa_ + ($base_imp - $tranche1) * 0.1; //10%
        } else if ($base_imp <= $tranche3) {
            $irsa_ = ($tranche1 - $tranche0) * 0.05; //5%
            $irsa_ = $irsa_ + ($tranche2 - $tranche1) * 0.1; //10%
            $irsa_ = $irsa_ + ($base_imp - $tranche2) * 0.15; //15%
        } else if ($base_imp > $tranche3) {
            $irsa_ = ($tranche1 - $tranche0) * 0.05; //5%
            $irsa_ = $irsa_ + ($tranche2 - $tranche1) * 0.1; //10%
            $irsa_ = $irsa_ + ($tranche3 - $tranche2) * 0.15; //15%
            $irsa_ = $irsa_ + ($base_imp - $tranche3) * 0.2; //20%
        }
        if ($irsa_ <= 3000) {
            $irsa_ = 3000;
        }
        return $irsa_;
    }

}
