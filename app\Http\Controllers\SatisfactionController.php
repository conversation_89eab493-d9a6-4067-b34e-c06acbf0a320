<?php

namespace App\Http\Controllers;

use App\Models\PieceJointe;
use App\Models\Satisfaction;
use App\Models\Seen;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class SatisfactionController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }
    public static function search(Request $request){
        $searchArray = [];
        if($request->id) $searchArray[] = " stf.id = ".$request->id. " ";
        else{
            if($request->site_id){
                $searchArray[] = " stf.site_id = '".$request->site_id . "' ";
            }
            if ($request->created_at) {
                $mois_fr = [
                    'janvier' => 1, 'février' => 2, 'mars' => 3, 'avril' => 4, 'mai' => 5, 'juin' => 6,
                    'juillet' => 7, 'août' => 8, 'septembre' => 9, 'octobre' => 10, 'novembre' => 11, 'décembre' => 12
                ];
                if (Carbon::hasFormat($request->created_at, 'Y-m-d')) {
                    $searchArray[] =  " stf.created_at > '$request->created_at 00:00:00' and stf.created_at <= '$request->created_at 23:59:59' ";
                }
                if (isset($mois_fr[strtolower($request->created_at)])) {
                    $month = $mois_fr[strtolower($request->created_at)];
                    $searchArray[] = " MONTH(stf.created_at) = $month ";
                }
            }
            if ($request->unread) {
                $searchArray[] = " see.id is null and stf.user_id <> '". $request->user()->id . "'";
            }
            if ($request->user_id) {
                $searchArray[] = " stf.user_id = ".$request->user_id . " ";
            }
        }
        $query_where = "";
        $query_and = "";
        if ($request->offset || $request->offset > -1) {
            $orderBy = " order by stf.id DESC limit " . $request->offset . ", 30";
        } else {
            $orderBy = " order by stf.id DESC";
        }
        if (count($searchArray) > 0) {
            $query_where = " WHERE " . implode(" and", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . "";
        }
        $query_where = $query_where . " " . $orderBy;
        $query_and = $query_and . " " . $orderBy;
        return compact("query_where", "query_and");
    }

    public function index(Request $request){
        if (in_array($request->user()->role, ['access','validateur', 'resp_sup', 'resp_op', 'superviseur'])) {
            $sql = "SELECT stf.id, stf.comment, stf.site_id, stf.user_id, 
                u.name as 'user_nom', u.email as 'user_email', s.adresse as 'site_adresse', 
                s.nom as 'site_nom', stf.status, stat.color as 'status_color'
                FROM satisfactions stf
                LEFT JOIN sites s on s.idsite = stf.site_id
                LEFT JOIN users u on u.id = stf.user_id
                LEFT JOIN `status` stat on stat.name = stf.status";
            if ($request->unread) {
                $sql = $sql. " LEFT JOIN (SELECT s.id, s.satisfaction_id FROM seen s WHERE s.user_id = '" .$request->user()->id ."' ) see ON see.satisfaction_id = stf.id";
            }
            if (in_array($request->user()->role,['superviseur', 'resp_sup']))
                $sql = $sql . " WHERE stf.user_id = ". $request->user()->id . ($this->search($request))['query_and']; 
            if (in_array($request->user()->role,['resp_op'])) {
                $regions = RegionUsersController::getRegions($request);
                $sql = $sql . " WHERE s.group_pointage_id IN (".$regions.") ". ($this->search($request))['query_and'];
            }
            // else 
            //     $sql = $sql . ($this->search($request))['query_where'];
            $satisfactions = DB::select($sql, []);

            if (count($satisfactions) > 0) {
                $pieces = DB::select("SELECT id, satisfaction_id FROM piece_jointes WHERE satisfaction_id in (" . implode(",", array_column($satisfactions, "id")) . ")");
                $seen = DB::select("SELECT s.id, s.satisfaction_id FROM seen s WHERE s.user_id = ? and satisfaction_id in (" . implode(",", array_column($satisfactions, "id")) . ")", [$request->user()->id]);
                foreach ($satisfactions as $stf) {
                    $stf->nb_pj = 0;
                    foreach ($pieces as $pj) {
                        if ($stf->id == $pj->satisfaction_id)
                            $stf->nb_pj += 1;
                    }
                    $stf->seen = false;
                    foreach ($seen as $se) {
                        if ($stf->id == $se->satisfaction_id)
                            $stf->seen = true;
                    }
                }
            }
            return response()->json(compact('satisfactions'));
        }
        return response(["error"=>"EACCES"]);
    }

    public function show(Request $request, $id){
        $sql = "SELECT stf.id, stf.comment, stf.site_id, stf.user_id, stf.created_at,
            u.name as 'user_nom', u.email as 'user_email', s.adresse as 'site_adresse', see.id as 'seen',
            s.nom as 'site_nom', stf.status, stat.color as 'status_color', stat.description as 'status_description'
            FROM satisfactions stf
            LEFT JOIN sites s on s.idsite = stf.site_id
            LEFT JOIN users u on u.id = stf.user_id
            LEFT JOIN status stat on stat.name = stf.status
            LEFT JOIN (SELECT s.id, s.satisfaction_id FROM seen s WHERE s.user_id = ? ) see ON see.satisfaction_id = stf.id
            WHERE stf.id =?"; 
        if (in_array($request->user()->role, ["access", 'validateur', 'resp_sup', 'resp_op'])) {
            $satisfaction = DB::select($sql, [$request->user()->id, $id])[0];
        }
        else if(in_array($request->user()->role, ['superviseur'])){
            $satisfaction = DB::select($sql. " AND stf.user_id = ?", [$request->user()->id, $id, $request->user()->id])[0];
        }
        else
            return response(["error" => 'EACCES']);
        $pj = PieceJointe::select('id')->where('satisfaction_id', $id)->get();
        $satisfaction->nb_pj = count($pj);
        return response()->json(compact('satisfaction'));
    }

    protected function validateAndSetSatisfaction($request, $satisfaction){
        $satisfaction->site_id = $request->site_id;
        $satisfaction->comment = $request->comment;
        $validator = Validator::make($request->all(), [
            'site_id' => ["required", "integer"],
            'comment' => ["required"],
            'files' => ['required'],
        ],["comment.required" => "Le champ commentaire est obligatoire.", "files.required" => "La pièce jointe est obligatoire"]);
        if ($validator->fails()) {
            return ['error' => $validator->errors()->first()];
        }
        return ['error' => ''];
    }

    public function store(Request $request){
        if (in_array($request->user()->role, ['superviseur', 'resp_sup', 'resp_op', 'access'] )) {
            $satisfaction = new Satisfaction();
            $validator = $this->validateAndSetSatisfaction($request, $satisfaction);
            if ($validator['error']) {
                return response(['error' => $validator['error']]);
            }
            $satisfaction->status = 'demande';
            $satisfaction->user_id = $request->user()->id;
            $satisfaction->created_at = new \DateTime();
            if($satisfaction->save()){
                $nb_pj = 0;
                $all_pj_name = '';
                foreach ($request->file('files') as $key => $file) {
                    $pj_date = date("Y-m-d_His", time());
                    $file_ext = $file->extension();
                    $original_name = $file->getClientOriginalName();
                    $name_len = strlen($original_name);
                    if ($name_len > 15){
                        if (substr($original_name, -strlen('.' . $file_ext)) === ('.' . $file_ext)) {
                            $new_length = $name_len - strlen('.' . $file_ext);
                            $original_name = substr_replace($original_name, "", $new_length);
                        }
                        $original_name = substr($original_name, 0, 15) . '.' . $file_ext;
                    }
                    $file_name = 'satisfaction_'. $satisfaction->id. "_". $pj_date. "_" . $key . "." . $file_ext;
                    $file->storeAs('uploads', $file_name, 'public');
                    $piece_jointe = new PieceJointe();
                    $piece_jointe->path = $file_name;
                    $piece_jointe->nature = $original_name;
                    $piece_jointe->user_id = $request->user()->id;
                    $piece_jointe->created_at = new \DateTime();
                    $piece_jointe->satisfaction_id = $satisfaction->id;
                    $piece_jointe->save();
                    $all_pj_name = $all_pj_name . $original_name .'<' .$file_name .'>' . '\n'; 
                    $nb_pj += 1;
                }
                HistoriqueController::new_satisfaction($request, $satisfaction, $all_pj_name);
                return response(['success' => "Satisfaction enregisté avec " . $nb_pj . " pièce(s) jointe(s)", 'id' => $satisfaction->id]);
            }
        }
        return response(['error' => 'EACCES']);
    }

    // public function edit(Request $request, $id) {
    //     $satisfaction = Satisfaction::find($id);
    //     $old_satisfaction = clone $satisfaction;
    //     if ($request->user()) {
    //         $validator = $this->validateAndSetSatisfaction($request, $satisfaction);
    //         if ($validator['error']) {
    //             return response(['error' => $validator['error']]);
    //         }
    //         $satisfaction->status = 'demande';
    //         $satisfaction->user_id = $request->user()->id;
    //         $satisfaction->created_at = new \DateTime();
    //         $satisfaction->updated_at = new \DateTime();
    //         if($satisfaction->save()){
    //             HistoriqueController::update_satisfaction($request, $old_satisfaction,"Modification");
    //             return response(['success' => "successfully", 'id' => $satisfaction->id]);
    //         }
    //     }
    // }
    public function seen_satisfaction(Request $request, $id){
        $satisfaction = Satisfaction::find($id);
        // $old_satisfaction = clone $satisfaction;
        $auth = $request->user();
        if (($request->user()->id != $satisfaction->user_id)
            && in_array($request->user()->role, ["access", "validateur", "resp_op"])
        ) {
            $isSeen = Seen::where('user_id', $auth->id)->where("satisfaction_id", $id)->first();
            if ($satisfaction != null && $satisfaction->user_id != $auth->id
                && ($isSeen == null)
            ) {
                $seen = new Seen();
                $seen->user_id = $auth->id;
                $seen->satisfaction_id = $id;
                $seen->created_at = new \DateTime();
                if ($seen->save()) {
                    return response(['success' => "Satisfaction lu"]);
                }
            }
        }
        return response(['error' => "EACCES"]);
    }

    public function seen_all(Request $request){
        $auth = $request->user();
        if (count($request->ids) > 0 && in_array($request->user()->role, ["access", "validateur"])) {
            $satisfactions = DB::select("SELECT stf.id
            FROM satisfactions stf
            LEFT JOIN (SELECT s.id, s.satisfaction_id FROM seen s WHERE s.user_id = ? ) see ON see.satisfaction_id = stf.id
            where see.id is null and stf.id in ( " . implode(",", $request->ids) . ") " .
            "order by stf.created_at desc", [$request->user()->id]);
            $new_insert_array = array();
            foreach ($satisfactions as $stf) {
                $new_insert_array[] = array(
                    'user_id' => $auth->id,
                    'satisfaction_id' => $stf->id,
                    'created_at' => now()
                );
            }
            Seen::insert($new_insert_array);
            return response(["success" => "Tout les satisfactions sont lu"]);
        }
        return response(['error' => "EACCES"]);
    }

}
