<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Recrutement;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Http\Controllers\HistoriqueController;
use App\Http\Controllers\PieceJointeController;
use App\Models\PieceJointe;
use App\Models\Employe;

class RecrutementController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }
    
    protected function searchIndex(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = " r.id = " . $request->id . " ";
        else {
            if($request->actif)
                $searchArray[] = " (r.soft_delete is null or r.soft_delete = 0) ";
            if($request->archive)
                $searchArray[] = " (r.soft_delete = 1) ";
            if($request->created_at)
                $searchArray[] = " r.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "r.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->nom)
                $searchArray[] = " r.nom like '%" . $request->nom . "%' ";
            if($request->cin_text)
                $searchArray[] = " r.cin_text = '" . $request->cin_text . "' ";
            if($request->status_recrutement)
                $searchArray[] = " r.status = '" . $request->status_recrutement . "' ";
        }
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by r.created_at desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by r.created_at desc desc limit ". $request->offset . ", 30";
        return compact('query_where', 'query_and');
    }

    public function index(Request $request){
        $search = $this->searchIndex($request);
        $recrutements = DB::select("SELECT r.id, r.nom, r.numero_stagiaire, r.societe_id, r.soft_delete, r.cin, r.cv, r.photo, r.residence, r.plan_reperage, r.bulletin_n3, r.bonne_conduite, r.created_at, r.updated_at,
        r.contact, u.name as recruteur, u.email as recruteur_email, r.superviseur_id, r.site, r.status, r.francais, r.anglais, r.xp, r.date_naiss, r.adresse, r.latitude, r.longitude, r.telephone, r.email, r.taille
        FROM recrutements r
        LEFT JOIN users u ON r.user_id = u.id" . $search['query_where']);
        $ids = [];
        foreach($recrutements as $rec) {
            array_push($ids, $rec->numero_stagiaire);
        }
        if(is_array($ids) && count($ids) > 0) {
            $pointage = DB::select("SELECT * FROM pointage_formations WHERE numero_stagiaire IN (" . implode(',', $ids) . ")");
            foreach($recrutements as $rec) {
                if (!isset($rec->nb_pointage)) {
                    $rec->nb_pointage = 0;
                }
                foreach($pointage as $p) {
                    if ($rec->numero_stagiaire == $p->numero_stagiaire) {
                        $rec->nb_pointage += 1;
                    }
                }
            }
            $manque_vigilance = DB::select("SELECT * FROM manque_vigilances WHERE numero_stagiaire IN (" . implode(',', $ids) . ")");
            foreach($recrutements as $rec) {
                if (!isset($rec->nb_manque_vigilance)) {
                    $rec->nb_manque_vigilance = 0;
                }
                foreach($manque_vigilance as $p) {
                    if ($rec->numero_stagiaire == $p->numero_stagiaire) {
                        $rec->nb_manque_vigilance += 1;
                    }
                }
            }
        }
        return response(compact('recrutements'));
    }

    public function detail(Request $request, $id){
        $recrutement = DB::select("SELECT r.id, r.nom, r.numero_stagiaire, r.societe_id, r.cin_text, r.sexe, r.soft_delete, r.cin, r.cv, r.photo, r.residence, r.plan_reperage, r.bulletin_n3, r.bonne_conduite, r.created_at, r.updated_at,
        r.contact, u.`name` as recruteur, u.email as recruteur_email, r.superviseur_id, r.site, r.status, r.francais, r.anglais, r.xp, r.date_naiss, r.adresse, r.latitude, r.longitude, r.telephone, r.email, r.taille
        FROM recrutements r 
        LEFT JOIN users u ON r.user_id = u.id
        WHERE r.id = ?", [$id])[0];
        $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
            FROM piece_jointes pj
            WHERE pj.recrutement_id = ?", [$id]);
        $recrutement->nb_pj = count($piece_jointes);
        $recrutement->pj = $piece_jointes;
        $pointage = DB::select("SELECT count(id) as nombre FROM pointage_formations WHERE numero_stagiaire = ?", [$recrutement->numero_stagiaire])[0]->nombre;
        $manque_vigilance = DB::select("SELECT count(id) as nombre FROM manque_vigilances WHERE numero_stagiaire = ?", [$recrutement->numero_stagiaire])[0]->nombre;
        $recrutement->nb_pointage = $pointage;
        $recrutement->nb_manque_vigilance = $manque_vigilance;
        return response()->json($recrutement);
    }

    public static function updateRecrutement($recrutement_id, $employe_id) {
        $recrutement = Recrutement::find($recrutement_id);
        $recrutement->status = 'Recruté';
        $recrutement->updated_at = now();
        $recrutement->employe_id = $employe_id;
        $recrutement->save();
        return response()->json(["success" => "Recrutement mis à jour"]);
    }

    public function complement($id){
        $recrutement = DB::select("SELECT r.id, r.cin, r.cv, r.photo, r.residence, r.plan_reperage, r.bulletin_n3, r.bonne_conduite
            FROM recrutements r
            WHERE r.id = ?",[$id])[0];
        return response()->json($recrutement);
    }

    public function formation($numero_stagiaire) {
        $formations = DB::select("SELECT p.id, p.employe_id, p.date_pointage, s.nom as 'site' FROM pointage_formations p
            left join site_formations s on s.id = p.site_id
            WHERE numero_stagiaire = ?", [$numero_stagiaire]);
        $manques = DB::select('SELECT * FROM manque_vigilances WHERE numero_stagiaire = ?', [$numero_stagiaire]);
        foreach ($formations as $f) {
            foreach ($manques as $m) {
                if($f->date_pointage == $m->date_service){
                    $f->nb_manque = $m->nb_manque;
                    $f->manque = $m->manque;
                }
            }
        }
        return response()->json(compact('formations', 'manques'));
    }

    public function manque($numero_stagiaire) {
        $manques = DB::select('SELECT * FROM manque_vigilances WHERE numero_stagiaire = ?', [$numero_stagiaire]);
        return response()->json(compact('manques'));
    }

    public function store(Request $request){
        $recrutement = new Recrutement();
        $validator = Validator::make($request->all(), [
            'nom' => ['required', Rule::unique('recrutements')->where(function ($query) use ($request) {
                return $query->where('nom', 'like', $request->nom . '%');
            })],
            'cin_text' => ['required', 'regex:/(^[0-9]{3} [0-9]{3} [0-9]{3} [0-9]{3}$)/u'],
            'date_naiss' => ['nullable'],
            'sexe' => ['nullable'],
            'adresse' => ['nullable'],
            'latitude' => ['nullable', 'numeric'],
            'longitude' => ['nullable', 'numeric'],
            'telephone' => ['regex:/(^03[0-9]{1} [0-9]{2} [0-9]{3} [0-9]{2}$)/u','required'],
            'email' => ['nullable'],
            'francais' => ['nullable'],
            'anglais' => ['nullable'],
            'xp' => ['nullable'],
            'taille' => ['nullable'],
            'cin' => ['nullable', 'file', 'mimes:pdf,jpg,png,jpeg'],
            'cv' => ['nullable', 'file', 'mimes:pdf,jpg,png,jpeg'],
            'photo' => ['nullable', 'file', 'mimes:pdf,jpg,png,jpeg'],
            'residence' => ['nullable', 'file', 'mimes:pdf,jpg,png,jpeg'],
            'reperage' => ['nullable', 'file', 'mimes:pdf,jpg,png,jpeg'],
            'bulletin' => ['nullable', 'file', 'mimes:pdf,jpg,png,jpeg'],
            'bonne_conduite' => ['nullable', 'file', 'mimes:pdf,jpg,png,jpeg'],
        ]);

        if($validator->fails()) {
            return \response()->json(['error' => $validator->errors()->first()]);
        } else {
            $recrutement->nom = $request->nom;
            $max_numero_stagiare = DB::select("SELECT max(numero_stagiaire) as last_numero_stagiaire FROM recrutements")[0]->last_numero_stagiaire;
            $recrutement->numero_stagiaire = $max_numero_stagiare + 1;
            $recrutement->cin_text = $request->cin_text;
            $matchingEmploye = DB::select("SELECT id as employe_id from employes where cin_text = ?", [$request->cin_text]);
            if(count($matchingEmploye)) {
                $recrutement->status = "Recruté";
                $recrutement->employe_id = $matchingEmploye[0]->employe_id;
            }
            $recrutement->date_naiss = $request->date_naiss;
            $recrutement->sexe = $request->sexe;
            $recrutement->adresse = $request->adresse;
            $recrutement->latitude = $request->latitude;
            $recrutement->longitude = $request->longitude;
            $recrutement->telephone = $request->telephone;
            $recrutement->email = $request->email;
            $recrutement->francais = $request->francais;
            $recrutement->anglais = $request->anglais;
            $recrutement->xp = $request->xp;
            $recrutement->taille = $request->taille;
            $recrutement->cin = $request->cin ? 1 : 0;
            $recrutement->cv = $request->cv ? 1 : 0;
            $recrutement->photo = $request->photo ? 1 : 0;
            $recrutement->residence = $request->residence ? 1 : 0;
            $recrutement->plan_reperage = $request->reperage ? 1 : 0;
            $recrutement->bulletin_n3 = $request->bulletin ? 1 : 0;
            $recrutement->bonne_conduite = $request->bonne_conduite ? 1 : 0;
            $recrutement->created_at = now();
            $recrutement->updated_at = now();
            $recrutement->user_id = $request->user()->id;
            if($recrutement->save()) {
                $file_fields = [
                    'cin', 'cv', 'photo', 'residence', 'reperage', 'bulletin', 'bonne_conduite'
                ];

                foreach($file_fields as $field) {
                    if($request->hasFile($field)) {
                        $file = $request->file($field);
                        $pj_date = date("Y-m-d_His");
                        $file_ext = $file->extension();
                        $original_name = $file->getClientOriginalName();
                        $name_len = strlen($original_name);
    
                        if ($name_len > 15) {
                            if (substr($original_name, -strlen('.' . $file_ext)) === ('.' . $file_ext)) {
                                $new_length = $name_len - strlen('.' . $file_ext);
                                $original_name = substr_replace($original_name, "", $new_length);
                            }
                            $original_name = substr($original_name, 0, 15) . '.' . $file_ext;
                        }
    
                        $file_name = 'recrutement_' . $recrutement->id . "_" . $field . "_" . $pj_date . "." . $file_ext;
                        $file->storeAs('uploads', $file_name, 'public');
    
                        $piece_jointe = new PieceJointe();
                        $piece_jointe->path = $file_name;
                        $piece_jointe->nature = $field;
                        $piece_jointe->user_id = $request->user()->id;
                        $piece_jointe->created_at = now();
                        $piece_jointe->recrutement_id = $recrutement->id;
                        $piece_jointe->save();
                    }
                }
                if ($matchingEmploye) {
                    $employe = Employe::find($matchingEmploye[0]->employe_id);
                    $employe->recrutement_id = $recrutement->id;
                    $employe->save();
                }
                HistoriqueController::new_recrutement($request, $recrutement);
            }
            return response(["success" => "Recrutement ajouté", "id" => $recrutement->id]);
        }
    }

    public function update($id, Request $request){
        $recrutement = Recrutement::find($id);
        $old_recrutement = clone($recrutement);
        $validator = Validator::make($request->all(), [
            'nom' => ['required'],
            'cin_text' => ['required', 'regex:/(^[0-9]{3} [0-9]{3} [0-9]{3} [0-9]{3}$)/u'],
            'date_naiss' => ['nullable'],
            'sexe' => ['nullable'],
            'adresse' => ['nullable'],
            'latitude' => ['nullable'],
            'longitude' => ['nullable'],
            'telephone' => ['regex:/(^03[0-9]{1} [0-9]{2} [0-9]{3} [0-9]{2}$)/u','required'],
            'email' => ['nullable'],
            'francais' => ['nullable'],
            'anglais' => ['nullable'],
            'xp' => ['nullable'],
            'taille' => ['nullable'],
            'cin' => ['nullable'],
            'cv' => ['nullable'],
            'photo' => ['nullable'],
            'residence' => ['nullable'],
            'reperage' => ['nullable'],
            'bulletin' => ['nullable'],
            'bonne_conduite' => ['nullable'],
        ]);
        if($validator->fails()) {
            return \response()->json(['error' => $validator->errors()->first()]);
        } else {
            $recrutement->nom = $request->nom;
            $recrutement->cin_text = $request->cin_text;
            $recrutement->date_naiss = $request->date_naiss;
            $recrutement->sexe = $request->sexe;
            $recrutement->adresse = $request->adresse;
            $recrutement->latitude = $request->latitude ?: null;
            $recrutement->longitude = $request->longitude ?: null;
            $recrutement->telephone = $request->telephone;
            $recrutement->email = $request->email;
            $recrutement->francais = $request->francais;
            $recrutement->anglais = $request->anglais;
            $recrutement->xp = $request->xp;
            $recrutement->taille = $request->taille;
            $recrutement->cin = $request->cin ? 1 : 0;
            $recrutement->cv = $request->cv ? 1 : 0;
            $recrutement->photo = $request->photo ? 1 : 0;
            $recrutement->residence = $request->residence ? 1 : 0;
            $recrutement->plan_reperage = $request->reperage ? 1 : 0;
            $recrutement->bulletin_n3 = $request->bulletin ? 1 : 0;
            $recrutement->bonne_conduite = $request->bonne_conduite ? 1 : 0;
            HistoriqueController::update_recrutement($request, $old_recrutement, "Recrutement modifié");
            $recrutement->save();
            return response()->json(["success" => "Recrutement modifié"]);
        }
    }

    public function soft_delete($id, Request $request){
        $recrutement = Recrutement::find($id);
        if($recrutement && !$recrutement->soft_delete && in_array($request->user()->role, ['rh', "resp_rh"])
        && $request->date_archive && $request->observation){
            $recrutement->soft_delete = 1;
            $recrutement->updated_at = now();
            $recrutement->observation = $request->observation;
            $recrutement->date_archive = $request->date_archive;
            $recrutement->save();
            $request->note = "Date d'archivage: " . (new \DateTime($request->date_archive))->format('d-m-Y')
             . "\nObservation: " . $request->observation;
            HistoriqueController::action_recrutement($request, "Mise en archive", $id);
            return response()->json(["success" => "Recrutement archivé"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function restore($id, Request $request){
        $recrutement = Recrutement::find($id);
        if($recrutement && $recrutement->soft_delete && in_array($request->user()->role, ['rh', 'resp_rh'])){
            $updateOk = Recrutement::where('id', $id)->update([
                'soft_delete' => null]);
            HistoriqueController::action_recrutement($request, "Recrutement restauré", $id);
            return response()->json(["success" => "Recrutement restauré",]);
        }
        return response(["error" => "EACCES"]);
    }

    public function get_pj(Request $request, $id) {
        $ids = DB::select("SELECT id FROM piece_jointes WHERE recrutement_id = ?", [$id]);
        return response()->json(compact('ids'));
    }

    public function get_grille_competences() {
        $grilles = DB::select("SELECT id, valeur, description, pour FROM grille_competences");
        return response()->json(compact('grilles'));
    }
}
