import { useEffect, useState } from "react";
import useToken from "../../util/useToken";
import moment from 'moment';
import LoadingPage from "../../loading/LoadingPage";

export default function SanctionEmploye({id, onlyDone}) {
    const [showDoneSanction, toggleDoneSanction] = useState(false)
        const [isLoading, toggleLoading] = useState(false)
        const [sanctions, setSanctions] = useState([])

        console.log(id)
        useEffect(() => {
            let isMounted = true
            if(id){
                toggleLoading(true)
                let url = "/api/employe/sanction?employe_id=" + id
                if(showDoneSanction)
                    url = url + "?done=1"
                axios.get(url, useToken())
                .then((res) => {
                    if(isMounted) {
                        if(res.data.error)
                            console.error(res.data.error)
                        else
                            setSanctions(res.data)
                        toggleLoading(false)
                    }
                })
                .catch((e) => {
                    console.error(e)
                })
            }
            return () => { isMounted = false};
        }, [showDoneSanction]);
    
        return <> {
            isLoading ?
                <LoadingPage/>
            : <>
                <div className='tab-list-action'>
                    {
                        !onlyDone &&
                        <div className='action-container'>
                            {
                                showDoneSanction ?
                                    <span>
                                        <span onClick={() => toggleDoneSanction(false)}>Tous les sanctions</span>
                                    </span>
                                :
                                    <span>
                                        <span onClick={() => toggleDoneSanction(true)}>Sanction terminé</span>
                                    </span>
                            }
                        </div>
                    }
                </div>
                {
                    sanctions.length == 0 ?
                        <div className="story-container">
                            <span className='secondary'>Aucune sanction {(onlyDone || showDoneSanction) ? " terminé" : ""}</span>
                        </div>
                    : 
                    (onlyDone || showDoneSanction) ?
                        sanctions.map(sanction => <div key={sanction.id} className="story-container">
                            <h4 className='capitalize text'>
                                <span>{sanction.objet}</span>
                            </h4>
                            <p className='secondary'>
                                <span className='text'>Site : </span> { sanction.site }<br/>
                                <span className='text'>{sanction.date_pointage ? "Motif" : "Sans pointage"} : </span> 
                                { sanction.motif }<br/>
                                {
                                    sanction.date_pointage ?
                                    <>
                                        <span className='text'>Date du service : </span>
                                        { moment(sanction.date_pointage).format("DD MMM YYYY") } { (moment(sanction.date_pointage).format("HH:mm:ss") == "07:00:00" ? " JOUR" : " NUIT") }
                                    </>
                                    :
                                    <>
                                        <span className='text'>Créé le : </span>
                                        { moment(sanction.created_at).format("DD MMM YYYY à HH:mm") }
                                    </>
                                }
                            </p>
                        </div>)
                    :
                        sanctions.map(sanction => <div key={sanction.id} className="story-container">
                            <div className='badge-container'>
                                <span>
                                    <span className={'badge-outline badge-outline-' + sanction.status_color}>
                                        {sanction.status_description}
                                    </span>
                                </span>
                            </div>
                            <h4 className='capitalize text'>
                                <span>{sanction.site}</span>
                            </h4>
                            <p className='secondary'>
                                <span className='text'>{sanction.date_pointage ? "Motif" : "Sans pointage"} : </span> 
                                { sanction.motif }<br/>
                                {
                                    sanction.date_pointage ?
                                    <>
                                        <span className='text'>Date du service : </span>
                                        { moment(sanction.date_pointage).format("DD MMM YYYY") } { (moment(sanction.date_pointage).format("HH:mm:ss") == "07:00:00" ? " JOUR" : " NUIT") }
                                    </>
                                    :
                                    <>
                                        <span className='text'>Créé le : </span>
                                        { moment(sanction.created_at).format("DD MMM YYYY à HH:mm") }
                                    </>
                                }
                            </p>
                        </div>)
                }
            </>
        } </>
}