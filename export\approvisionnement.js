const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const { sendMail } = require("../auth")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == 'task')
const destination_vg = ["og<PERSON><PERSON><EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

const sqlSelectLastApprovisionnementExport = "SELECT value FROM params p WHERE p.key = 'last_approvisionnement_export'"

const sqlSelectStatus = "SELECT name, description FROM status order by ordre"

function sqlSelectApprovisionnement(dateString){
    const begin = moment(dateString).subtract(7, "day").format("YYYY-MM-DD") + " 06:00:00"
    const end = dateString + " 06:00:00"
    console.log(begin)
    console.log(end)
	return "SELECT appro.id, appro.reference, appro.objet, appro.total, appro.created_at, appro.user_id, appro.status, " +
        "s.designation as 'service', u.name as 'user_nom', u.email as 'user_email', appro.created_at " +
        "from approvisionnements appro " +
        "left join users u on u.id = appro.user_id " +
        "left join services s on s.id = u.service_id " +
        "where (appro.status not in ('done', 'draft') " + 
        "or (appro.status in ('done', 'draft') and appro.updated_at > '" + begin +"' and appro.updated_at <= '" + end +"')) " +
        "order by appro.created_at"
}

function sqlUpdateLastApprovisionnementExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_approvisionnement_export'"
}

function generateApprovisionnementExcelFile(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    data.forEach(stat => {
        const worksheet = workbook.addWorksheet(stat.description)
        worksheet.getColumn('A').width = 15
        worksheet.getColumn('B').width = 50
        worksheet.getColumn('C').width = 40
        worksheet.getColumn('D').width = 20
        worksheet.getColumn('E').width = 20
        worksheet.getColumn('F').width = 30
        worksheet.getCell('A1').value = stat.description + " (" + stat.approvisionnements.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:F1')

        let line = 3
        
        worksheet.getCell('A' + line).value = "Reference"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontBold
        worksheet.getCell('B' + line).value = "Objet"
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).font = fontBold
        worksheet.getCell('C' + line).value = "Demandeur"
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).font = fontBold
        worksheet.getCell('D' + line).value = "Service"
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).alignment = alignmentStyle
        worksheet.getCell('D' + line).font = fontBold
        worksheet.getCell('E' + line).value = "Montant payé"
        worksheet.getCell('E' + line).border = borderStyle
        worksheet.getCell('E' + line).font = fontBold
        worksheet.getCell('F' + line).value = "Créé le"
        worksheet.getCell('F' + line).border = borderStyle
        worksheet.getCell('F' + line).font = fontBold
        line++

        stat.approvisionnements.forEach(appro => {
            worksheet.getCell('A' + line).value = moment(appro.created_at).format("YYYY") + "/" + ("0000" + appro.reference).slice(-5)
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('A' + line).alignment = alignmentStyle
            worksheet.getCell('B' + line).value = appro.objet
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = appro.user_nom + ' <' + appro.user_email + '>'
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('D' + line).value = appro.service
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('D' + line).alignment = alignmentStyle
            worksheet.getCell('E' + line).value = appro.total ? (Number.parseInt(appro.total).toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ") + ' Ar') : ''
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('F' + line).value = appro.created_at ? moment(appro.created_at).format("DD MMM YYYY") : ""
            worksheet.getCell('F' + line).border = borderStyle
            line++
        })
    })
}

function doApprovisionnementExport(dateString){
	console.log("doApprovisionnementExport")
    pool.query(sqlSelectStatus, [], async (err, status) => {
        if(err)
            console.error(err)
        else {
            pool.query(sqlSelectApprovisionnement(dateString), [], async (err, approvisionnements) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb approvisionnement: " + approvisionnements.length)
                    const approvisionnementByStatus = []
                    status.map(stat => {
                        stat.approvisionnements = []
                        approvisionnements.map(appro => {
                            if(stat.name == appro.status)
                                stat.approvisionnements.push(appro)
                        })
                        if(stat.approvisionnements.length > 0){
                            approvisionnementByStatus.push(stat)
                        }
                    })
                    const workbookApprovisionnement = new Excel.Workbook()
                    const header = "Approvisionnement " + moment(dateString).format("DD MMMM YYYY")
                    generateApprovisionnementExcelFile(workbookApprovisionnement, header, approvisionnementByStatus)
                    const approvisionnementSiteBuffer = await workbookApprovisionnement.xlsx.writeBuffer()
                    sendMail(
                        pool,
                        isTask ? destination_vg : destination_test,
                        header, 
                        "Veuillez trouver ci-joint le rapport des demandes de approvisionnements  du " + moment(dateString).subtract(7, "days").format("DD MMMM YYYY") + " au " + moment(dateString).format("DD MMMM YYYY") + "<br/>"
                        + "<ul>"
                            + (approvisionnementByStatus.map(stat => "<li>" + stat.description + ": " + stat.approvisionnements.length + "</li>").join(""))
                        + "</ul>"
                        ,
                        [
                            {
                                filename: header + ".xlsx",
                                content: approvisionnementSiteBuffer
                            },
                        ],
                        (response) => {
                            if(response && isTask){
                                pool.query(sqlUpdateLastApprovisionnementExport(dateString), [], (e, r) =>{
                                    if(e)
                                        console.error(e)
                                    else
                                        console.log("update last diag export: " + r)
                                    process.exit(1)
                                })
                            }
                            else
                                process.exit(1)
                        },
                        isTask
                    )
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2])){
    console.log("send test...")
    doApprovisionnementExport(process.argv[2])
}
else if(isTask){
    if(moment().day() == 3 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
        let date_vigilance = moment().format("YYYY-MM-DD")
        pool.query(sqlSelectLastApprovisionnementExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && result[0].value == date_vigilance) {
                console.log("export list approvisionnement already done!")
                process.exit(1)
            }
            else {
                console.log("exporting ...")
                doApprovisionnementExport(date_vigilance)
            }
        })
    }
    else {
        console.log("Not wednesday, skip DA")
    }
}
else
    console.log("please specify command!")