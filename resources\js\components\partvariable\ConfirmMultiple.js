import React, { useEffect, useState } from 'react'
import Input<PERSON>onth<PERSON><PERSON> from '../input/InputMonthYear'
import axios from 'axios'
import InputUser from '../input/InputUser'
import useToken from '../util/useToken'
import matricule from '../util/matricule'
import showAmount from '../util/numberUtil'
import LoadingPage from '../loading/LoadingPage'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useLocation } from 'react-router-dom'
import moment from 'moment'

export default function ConfirmMultiple({ closeModal, updateData, setOffsetDate, userId }) {
    const [datePaie, setDatePaie] = useState()
    const [respPart, setRespPart] = useState(null)
    const [partVariables, setPartVariables] = useState([])
    const [doneButton, setDoneButton] = useState(false)
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const locationSearch = useLocation().search
    const [error, setError] = useState('')
    const defaultDatePv = (moment().isAfter(moment().set("date", 25)) ? moment().add(1, "month").set("date", 20) : moment().set("date", 20))

    const onSearch = (initial) => {
        let isMounted = true
        setError("")
        if (datePaie && respPart) {
            toggleLoading(true)
            const params = new URLSearchParams()
            params.set('date_paie', datePaie.year + '-' + datePaie.month)
            params.set('user_id', respPart.id)
            params.set('status', 'validation')
            if (initial)
                params.set('offset', 0)
            else 
                params.set('offset', partVariables.length)
            if (isMounted) {
                axios.get("/api/part_variable?" + params, useToken())
                .then((res) => {
                    const results = res.data.part_variables;
                    if (initial) 
                        setPartVariables(results)
                    else {
                        const list = partVariables.slice().concat(results);
                        setPartVariables(list);
                    }
                    if (results.length < 30) {
                        setDataLoaded(true)
                    }
                    toggleLoading(false)
                })
                .catch((e) => {
                    console.error(e)
                })
            }
        }
        else {
            setSearchButton(true)
        }
        return () => { isMounted = false };
    }

    useEffect(() => {
        axios.get("/api/user/show/" + userId, useToken())
        .then(res => {
            let isMounted = true
            if(isMounted){
                setRespPart(res.data)
            }
            return () => { isMounted = false };
        })
        .catch(err => {
            console.error(err)
        })
    }, [userId]);
    useEffect(() => {
        partVariables.length > 0 ? setDoneButton(true) : setDoneButton(false)
    }, [partVariables])
    
    useEffect(() => {
        if (datePaie && datePaie.year.trim() && datePaie.month.trim() && respPart) {
            onSearch(true)
        }
    }, [datePaie, respPart])
    
    const onConfirm = () => { 
        if (partVariables.length > 0) {
            setError("")
            const data = new FormData();
            data.append("date_paie", partVariables[0].date_paie)
            data.append("user_id", partVariables[0].user_id)
            axios.post("/api/part_variable/done_multiple", data, useToken())
            .then((res) => {
                if (res.data.success) {
                    const urlParams = new URLSearchParams(locationSearch)
                    urlParams.set('user_id', respPart.id)
                    setOffsetDate(res.data.offset_date)
                    closeModal()
                    updateData(true)
                }
                else if (res.data.error) {
                    setError(res.data.error)
                }
            })
            .catch((e) => {
                console.error(e) 
                setError("Erreur d'envoie, réessayez.")
            })
        }
    }
    const fetchMoreData = () => { 
        setTimeout(() => onSearch(), 300);
    }

    return (
        <div className='modal'>
            <div>
                <h2>Confirmation par lot</h2>
                <div>
                    <InputMonthYear
                        defaultDate={defaultDatePv}
                        setDefaultDate
                        label='Date paie'
                        value={datePaie}
                        onChange={setDatePaie}
                        required />
                </div>
                <div>
                    <InputUser label="Fait par :"
                        value={respPart}
                        onChange={setRespPart}
                        required/>
                </div>
                {(datePaie && datePaie.year.trim() && datePaie.month.trim() && respPart) &&
                    (isLoading ?
                        <LoadingPage />
                    :
                        partVariables.length == 0 ?
                            <h3 className="center secondary">Aucun données trouvé</h3>
                        :
                            <div id="scrollableList">
                                <InfiniteScroll
                                    dataLength={partVariables.length}
                                    next={fetchMoreData}
                                    hasMore={!allDataLoaded}
                                    loader={<LoadingPage />}
                                    scrollableTarget="scrollableList"
                                >
                                    <div className='list-container'>
                                        <ul>
                                            {partVariables.map(pv => {
                                                return (
                                                    <li key={pv.id}>
                                                        <span className='secondary'>
                                                            {"[ " + showAmount(pv.montant) + " ] - " + matricule(pv) + " " + pv.employe}
                                                        </span>
                                                    </li>
                                                )
                                            })}
                                        </ul>
                                    </div>
                                </InfiniteScroll>
                            </div>
                    )
                }
                {
                    error &&
                    <div className='container-error'>
                        {error}
                    </div>
                }
                <div className='form-button-container'>
                    {doneButton && <button className='btn btn-primary' onClick={()=> onConfirm()} >Confirmer</button>}
                    <button className='btn' onClick={() => closeModal()}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
