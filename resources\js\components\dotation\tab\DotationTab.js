import React, { useEffect, useState } from 'react';
import LoadingPage from '../../loading/LoadingPage';
import useToken from '../../util/useToken';
import moment from 'moment';
import { GiMonclerJacket, GiBilledCap, GiBelt, GiConverseShoe, GiLabCoat, GiWhistle} from "react-icons/gi";
import { PiShirtFoldedFill, PiPantsFill } from "react-icons/pi";
import { FaTshirt } from "react-icons/fa";

export default function DotationTab({auth, name, value, updateData}) {
    const [isLoading, toggleLoading] = useState(true);
    const [dotations, setDotations] = useState([]);

    useEffect(() => {
        let isMounted = true;
        if (name == "equipement_id") {
            axios.get("/api/mouvement_article/equipement/" + value, useToken())
            .then((res) => {
                if(isMounted) {
                    setDotations(res.data.ligne_equipement)
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e)
            })
            return () => { isMounted = false };
        } else if (name == "employe_id") {
            axios.get("/api/mouvement_article/employe/" + value, useToken())
            .then((res) => {
                if(isMounted) {
                    setDotations(res.data.ligne_equipement)
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e)
            })
            return () => { isMounted = false };
        }
    }, [])

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <div>
                    <div className='article-container'>
                        <span></span>

                    </div>
                    {
                        dotations.map((d) => (
                            <div key={d.id} className='article-container'>
                                <span>
                                    {d.articles.split(', ').includes('T-shirt') && <FaTshirt size={25} color='#8d6e63'/>}
                                    {d.articles.split(', ').includes('Chemise') && <PiShirtFoldedFill size={25} color='#8d6e63'/>}
                                    {d.articles.split(', ').includes('Casquette') && <GiBilledCap size={25} color='#8d6e63'/>}
                                    {d.articles.split(', ').includes('Sifflet') && <GiWhistle size={25} color='#8d6e63'/>}
                                    {d.articles.split(', ').includes('Pantalon') && <PiPantsFill size={25} color='#8d6e63'/>}
                                    {d.articles.split(', ').includes('Rangers') && <GiConverseShoe size={25} color='#8d6e63'/>}
                                    {d.articles.split(', ').includes('Blouson') && <GiMonclerJacket size={25} color='#8d6e63'/>}
                                    {d.articles.split(', ').includes('Impérmeable') && <GiLabCoat size={25} color='#8d6e63'/>}
                                    {d.articles.split(', ').includes('Ceinture') && <GiBelt size={25} color='#8d6e63'/>}
                                </span>
                                <span style={{ color: "#8d6e63" }}>{moment(d.date_mouvement).format("DD/MM/YY")}</span>
                            </div>
                        ))
                    }
                </div>
        }
    </>
}