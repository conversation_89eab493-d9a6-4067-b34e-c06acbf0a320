import axios from 'axios'
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useToken from '../util/useToken';

export default function StatusModal({onChange, closeModal, useLink, hasEmploye}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [status, setStatus] = useState(null)
    
    const handleSelectStatus = (stat) => {
        if(useLink) {
            let params = new URLSearchParams(location.search)
            params.set("status", stat.name)
            navigate(location.pathname + "?" + params)
        }
        onChange(stat)
        closeModal()
    }

    useEffect(() => {
        let isMounted = true
        axios.get('/api/status' + (hasEmploye ? "?has_employe=1" : ""), useToken())
        .then((res) => {
            if(isMounted) setStatus(res.data)
        })
        return () => {isMounted = false}
    }, [])

    return <div className='modal'>
        <div>
            <h2>Status</h2>
            {
                status &&
                <div className='list-container'>
                    {
                        <ul>
                            {
                                status.map(stat => {
                                    return <li key={stat.name} onClick={() => handleSelectStatus(stat)}>
                                        {stat.description}<br/>
                                    </li>
                                })
                            }
                        </ul>
                    }
                </div>
            }
            <div className='form-button-container'>
                <button onClick={closeModal}>Annuler</button>
            </div>
        </div>
    </div>
}