import React from 'react'
import { <PERSON> } from 'react-router-dom'
import {BsCheck} from 'react-icons/bs';

export default function Complement({employe, auth}) {
    return <>
        <div className='story-container'>
            <div className="complement-item">
                <span>
                    <span className={employe.cin == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                    CIN
                </span>
                {
                    ["resp_rh", "rh"].includes(auth.role) &&
                    (
                        employe.cin == 1 ? 
                            <Link to={"/pj/edit?type=employe&id=" + employe.id + "&piece=cin"}>Modifier</Link> 
                        :
                        <Link to={"/pj/add?type=employe&id=" + employe.id + "&piece=cin"}>Ajouter</Link>
                    )
                }
            </div>
            <div className="complement-item">
                <span>
                    <span className={employe.cv == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                    CV
                </span>
                {
                    ["resp_rh", "rh"].includes(auth.role) &&
                    (
                        employe.cv == 1 ? 
                            <Link to={"/pj/edit?type=employe&id=" + employe.id + "&piece=cv"}>Modifier</Link> 
                        :
                            <Link to={"/pj/add?type=employe&id=" + employe.id + "&piece=cv"}>Ajouter</Link>
                    )
                }
            </div>
            <div className="complement-item">
                <span>
                    <span className={employe.photo == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                    Photo
                </span>
                {["resp_rh", "rh"].includes(auth.role) &&
                    <>
                        {employe.photo == 1 ? <Link to={"/pj/edit?type=employe&id=" + employe.id + "&piece=photo"}>Modifier</Link> 
                        :
                        <Link to={"/pj/add?type=employe&id=" + employe.id + "&piece=photo"}>Ajouter</Link>
                        }
                    </>
                }
            </div>
            <div className="complement-item">
                <span>
                    <span className={employe.residence == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                    Residence
                </span>
                {["resp_rh", "rh"].includes(auth.role) &&
                    <>
                        {employe.residence == 1 ? <Link to={"/pj/edit?type=employe&id=" + employe.id + "&piece=residence"}>Modifier</Link> 
                        :
                        <Link to={"/pj/add?type=employe&id=" + employe.id + "&piece=residence"}>Ajouter</Link>
                        }
                    </>
                }
            </div>
            <div className="complement-item">
                <span>
                    <span className={employe.plan_reperage == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                    Plan de repérage
                </span>
                {["resp_rh", "rh"].includes(auth.role) &&
                    <>
                        {employe.plan_reperage == 1 ? <Link to={"/pj/edit?type=employe&id=" + employe.id + "&piece=plan_reperage"}>Modifier</Link> 
                        :
                        <Link to={"/pj/add?type=employe&id=" + employe.id + "&piece=plan_reperage"}>Ajouter</Link>
                        }
                    </>
                }   
            </div>
            <div className="complement-item">
                <span>
                    <span className={employe.bulletin_n3 == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                    Bulletin N°3
                </span>
                {["resp_rh", "rh"].includes(auth.role) &&
                    <>
                        {employe.bulletin_n3 == 1 ? <Link to={"/pj/edit?type=employe&id=" + employe.id + "&piece=bulletin_n3"}>Modifier</Link> 
                        :
                        <Link to={"/pj/add?type=employe&id=" + employe.id + "&piece=bulletin_n3"}>Ajouter</Link>
                        }
                    </>
                }     
            </div>
            <div className="complement-item">
                <span>
                    <span className={employe.bonne_conduite == 1 ? 'badge-outline' : 'badge-outline hide'}><BsCheck /></span>
                    Bonne conduite
                </span>
                {["resp_rh", "rh"].includes(auth.role) &&
                    <>
                        {employe.bonne_conduite == 1 ? <Link to={"/pj/edit?type=employe&id=" + employe.id + "&piece=bonne_conduite"}>Modifier</Link> 
                        :
                        <Link to={"/pj/add?type=employe&id=" + employe.id + "&piece=bonne_conduite"}>Ajouter</Link>
                        }
                    </>
                }                            
            </div>
        </div>
    </>
}