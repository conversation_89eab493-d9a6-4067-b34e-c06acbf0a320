<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Approvisionnement;
use App\Models\User;
use App\Http\Controllers\PieceJointeController;
use App\Models\PieceJointe;
use App\Models\DaItem;
use App\Models\Notification;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Storage;
use PDF;

class ApprovisionnementController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function show($id){
        $approvisionnement = DB::select("SELECT appro.id, appro.objet, appro.user_id, appro.status, appro.total, appro.created_at, appro.updated_at,
            stat.description as 'status_description', stat.color as 'status_color', appro.reference, 
            us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', 
            sv.designation as 'service'
            FROM approvisionnements appro
            LEFT JOIN users us on us.id = appro.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN `status` stat on stat.name = appro.status
            LEFT JOIN services sv on sv.id = us.service_id
            where appro.id = ?", [$id])[0];
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj 
            where approvisionnement_id = ?
            order by pj.created_at desc", [$id]);
        $approvisionnement->nb_pj = count($pieces);
        
        $da_items = DB::select("SELECT it.approvisionnement_id, it.quantite, it.prix, it.cout FROM da_items it 
            where it.approvisionnement_id = ?", [$id]);
        $total = 0;
        $has_all_price = true;
        foreach($da_items as $item){
            if($approvisionnement->id == $item->approvisionnement_id){
                $total = $total + ($item->quantite * $item->prix);
                if(!$item->prix) $has_all_price = false;
            }
        }
        if(!$approvisionnement->total && $has_all_price && $total)
            $approvisionnement->total = $total;
        return response()->json($approvisionnement);
    }
    public function article($id){
        $approvisionnement = DB::select("SELECT appro.id, appro.status, appro.total, appro.user_id
            FROM approvisionnements appro
            where appro.id = ?", [$id])[0];
        $da_items = DB::select("SELECT da.id, da.designation, da.unite, da.quantite, da.cout, da.prix, da.created_at, da.updated_at, da.price_only, da.done
            FROM da_items da
            WHERE da.approvisionnement_id = ?
            order by da.id desc", [$id]);
        return response()->json(compact('approvisionnement', 'da_items'));
    }

    public function show_item($id){
        $da_item = DB::select("SELECT da.id, da.designation, da.unite, da.quantite, da.prix, da.approvisionnement_id, da.created_at, da.updated_at, da.price_only
            FROM da_items da
            where da.id = ?", [$id])[0];
        return $da_item;
    }

    public function detail($id){
        $approvisionnement = DB::select("SELECT appro.id, appro.objet, appro.user_id, appro.status, appro.total, appro.created_at, appro.updated_at,
            stat.description as 'status_description', stat.color as 'status_color', appro.reference, 
            us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', 
            sv.designation as 'service'
            FROM approvisionnements appro
            LEFT JOIN users us on us.id = appro.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN `status` stat on stat.name = appro.status
            LEFT JOIN services sv on sv.id = us.service_id
            where appro.id = ?", [$id])[0];
        $da_items = DB::select("SELECT da.id, da.designation, da.unite, da.quantite, da.cout, da.prix, da.created_at, da.updated_at, da.price_only, da.done
            FROM da_items da
            WHERE da.approvisionnement_id = ?
            order by da.id desc", [$id]);
        $pieces = DB::select("SELECT pj.id, pj.nature, pj.path, pj.user_id, pj.created_at, pj.updated_at
            FROM piece_jointes pj 
            where approvisionnement_id = ?
            order by pj.created_at desc", [$id]);
        $historiques = DB::select("SELECT h.id, h.objet, h.note, h.detail, h.created_at, h.user
            FROM historiques h
            where h.approvisionnement_id = ?
            order by h.created_at desc
            limit 10", [$id]);
        return response()->json(compact('approvisionnement', 'da_items', 'pieces', 'historiques'));
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->reference)
            $searchArray[] = "appro.reference = '". $request->reference ."'";
        if($request->da_item)
            $searchArray[] = "item.designation like '%". $request->da_item ."%'";
        if($request->da_ref)
            $searchArray[] = "item.reference like '%". $request->da_ref ."%'";
        if($request->objet)
            $searchArray[] = "appro.objet like '%". $request->objet ."%'";
        if($request->status)
            $searchArray[] = "appro.status = '". $request->status ."'";
        if($request->created_at)
            $searchArray[] = " appro.created_at > '" . $request->created_at . " 00:00:00' and ". 
                "appro.created_at <= '" . $request->created_at . " 23:59:59' ";
        if($request->user_id)
            $searchArray[] = " appro.user_id = " . $request->user_id . " ";

        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by appro.id desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by appro.id desc limit ". $request->offset . ", 30";
        return compact('query_where', 'query_and');
    }

    public function index(Request $request){
        $search = $this->search($request);
        $auth = $request->user();
        if($search){
            if(in_array($request->user()->role, ['validateur','daf', 'compta', 'achat'])){
                if($request->da_item || $request->da_ref){
                    $approvisionnements = DB::select("SELECT appro.id, appro.reference, appro.user_id, appro.status, appro.created_at, appro.updated_at,
                        stat.description as 'status_description', stat.color as 'status_color', item.price_only, item.unite, item.quantite, item.prix, item.cout,
                        us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', item.designation as 'article', item.id as 'article_id',
                        sv.designation as 'service'
                        FROM da_items item
                        LEFT JOIN approvisionnements appro on appro.id = item.approvisionnement_id
                        LEFT JOIN users us on us.id = appro.user_id
                        LEFT JOIN users ur on ur.id = us.real_email_id
                        LEFT JOIN `status` stat on stat.name = appro.status
                        LEFT JOIN services sv on sv.id = us.service_id
                        " . $search['query_where'], []);
                }
                else {
                    $approvisionnements = DB::select("SELECT appro.id, appro.reference, appro.objet, appro.user_id, appro.status, appro.total, appro.created_at, appro.updated_at,
                        stat.description as 'status_description', stat.color as 'status_color', 
                        us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', 
                        sv.designation as 'service'
                        FROM approvisionnements appro
                        LEFT JOIN users us on us.id = appro.user_id
                        LEFT JOIN users ur on ur.id = us.real_email_id
                        LEFT JOIN `status` stat on stat.name = appro.status
                        LEFT JOIN services sv on sv.id = us.service_id
                        " . $search['query_where'], []);
                }
            }
            else {
                if($request->da_item || $request->da_ref){
                    $approvisionnements = DB::select("SELECT appro.id, appro.user_id, appro.status, appro.created_at, appro.updated_at, appro.reference, 
                        stat.description as 'status_description', stat.color as 'status_color', item.price_only, item.unite, item.quantite, item.prix, item.cout,
                        us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', item.designation as 'article', item.id as 'article_id',
                        sv.designation as 'service'
                        FROM da_items item
                        LEFT JOIN approvisionnements appro on appro.id = item.approvisionnement_id
                        LEFT JOIN users us on us.id = appro.user_id
                        LEFT JOIN users ur on ur.id = us.real_email_id
                        LEFT JOIN `status` stat on stat.name = appro.status
                        LEFT JOIN services sv on sv.id = us.service_id
                        where appro.user_id = ?
                        " . $search['query_and'], [$auth->id]);
                }
                else {
                    $approvisionnements = DB::select("SELECT appro.id, appro.objet, appro.user_id, appro.status, appro.total, appro.created_at, appro.updated_at,
                        stat.description as 'status_description', stat.color as 'status_color', appro.reference, 
                        us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', 
                        sv.designation as 'service'
                        FROM approvisionnements appro
                        LEFT JOIN users us on us.id = appro.user_id
                        LEFT JOIN users ur on ur.id = us.real_email_id
                        LEFT JOIN `status` stat on stat.name = appro.status
                        LEFT JOIN services sv on sv.id = us.service_id
                        where appro.user_id = ?
                        " . $search['query_and'], [$auth->id]);
                }
            }
            if(count($approvisionnements) > 0){
                $pieces = DB::select("SELECT id, approvisionnement_id FROM piece_jointes WHERE approvisionnement_id in (" . implode(",", array_column($approvisionnements, "id")) . ")");
                foreach ($approvisionnements as $appro) {
                    $appro->nb_pj = 0;
                    foreach ($pieces as $pj) {
                        if($appro->id == $pj->approvisionnement_id)
                            $appro->nb_pj += 1;
                    }
                }
            }
            if(!($request->da_item ||$request->da_ref)){
                $da_items = [];
                $ids = [];
                foreach($approvisionnements as $appro){
                    if(!$appro->total)
                        $ids[] = $appro->id;
                }
                if(count($ids) > 0){
                    $da_items = DB::select("SELECT it.approvisionnement_id, it.quantite, it.prix, it.cout FROM da_items it 
                        where it.approvisionnement_id in (". join(",", $ids) .")", []);
                    foreach($approvisionnements as $appro){
                        $total = 0;
                        $has_all_price = true;
                        foreach($da_items as $item){
                            if($appro->id == $item->approvisionnement_id){
                                $total = $total + ($item->quantite * $item->prix);
                                if(!$item->prix) $has_all_price = false;
                            }
                        }
                        if(!$appro->total && $has_all_price && $total)
                            $appro->total = $total;
                    }
                }
            }
        }
        else 
            return response(["error" => "EACCES"]);
        return response(compact('approvisionnements'));
    }

    protected function validateAndSetApprovisionnement($request, $approvisionnement){
        $validator = Validator::make($request->all(), [
            'objet' => 'required',
            //'items' => 'required',
            //'items.*.designation' => 'required',
            //'items.*.unite' => 'required',
            //'items.*.quantite' => 'required|integer',
            //'items.*.prix' => 'integer'
        ]);     
        return $validator;
    }

    protected function validateAndSetItem($request_item, $da_item){
        $da_item->designation = $request_item['designation'];
        if($request_item['price_only']){
            $validator = Validator::make($request_item, [
                'designation' => 'required',
                'prix' => 'required|numeric|gt:0'
            ]);  
            $da_item->price_only = 1;
            $da_item->quantite = 1;
            $da_item->unite = null;
            $da_item->prix = $request_item['prix'];
        }
        else {
            $validator = Validator::make($request_item, [
                'designation' => 'required',
                'unite' => 'required',
                'quantite' => 'required|numeric|gt:0',
                'prix' => 'numeric|nullable'
            ]);  
            $da_item->price_only = 0;
            $da_item->quantite = $request_item['quantite'];
            $da_item->unite = $request_item['unite'];
            $da_item->prix = $request_item['prix'];
        }
        return $validator;
    }

    public function store(Request $request){
        $auth = $request->user();
        $validator = Validator::make($request->all(), [
            'objet' => 'required|string',
        ]);
        if ($validator->fails())
            return response(['error' => $validator->errors()->first()]);

        $approvisionnement = new Approvisionnement();
        $approvisionnement->user_id = $request->user()->id;
        $approvisionnement->objet = $request->objet;
        if($request->user()-> id == 105)
            $approvisionnement->status = "demande";
        else
            $approvisionnement->status = "validation";
        $approvisionnement->created_at = new \DateTime();

        $items = [];
        foreach($request->items as $item){
            $da_item = new DaItem();
            $da_item->created_at = new \DateTime();
            $da_item->updated_at = new \DateTime();
            $validator = $this->validateAndSetItem($item, $da_item);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $items[] = $da_item;
        }
        if($approvisionnement->save()){
            foreach ($items as $item) {
                $item->approvisionnement_id = $approvisionnement->id;
                $item->save();
            }
            HistoriqueController::new_approvisionnement($request, $approvisionnement->id);
            if($auth->email == "<EMAIL>"){
                $emails = [];
                $emails[] = ['address' => '<EMAIL>', 'name' => 'Idealy'];
                MailController::approvisionnement($request, $approvisionnement->id, "DA-". $approvisionnement->id, $emails);
            }
            return response(["success" => "DA bien envoyée", "id" => $approvisionnement->id]);
        }
        return response(["error" => "Erreur d'envoi, réessayez"]);
    }

    public function store_item(Request $request, $approvisionnement_id){
        $auth = $request->user();
        $appro = Approvisionnement::find($approvisionnement_id);
        if($auth->id == $appro->user_id && $appro->status == "draft"){
            $da_item = new DaItem();
            $da_item->approvisionnement_id = $approvisionnement_id;
            $da_item->created_at = new \DateTime();
            $da_item->updated_at = new \DateTime();

            $validator = $this->validateAndSetItem($request->all(), $da_item);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            if($da_item->save()){
                HistoriqueController::new_approvisionnement_item($request, $approvisionnement_id);
                return response(["success" => "Article DA bien ajouté ", "item" => $da_item]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function reply_validation(Request $request, $id){
        $approvisionnement = Approvisionnement::find($id);
        if($request->user()->role == 'validateur' && $approvisionnement->status == 'validation'){
            if(!$request->note)
                $request->note = "ok";
            $approvisionnement->status = 'demande';
            $approvisionnement->updated_at = new \DateTime();

            $approvisionnement->note_id = HistoriqueController::action_approvisionnement($request, "DA validé", $id);
            if($approvisionnement->save()){
                $emails = [];
                $user = DB::select("SELECT us.email FROM users us WHERE us.id = ?", [$approvisionnement->user_id])[0];
                if($user->email == "<EMAIL>"){
                    $users = DB::select("SELECT id from users where email like '<EMAIL>' or id = ?", [$approvisionnement->user_id]);
                    foreach ($users as $u) {
                        Notification::create([
                            'historique_id' => $approvisionnement->note_id,
                            'receiver_id' => $u->id,
                            'user_id' => $request->user()->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
                else
                    Notification::create([
                        'historique_id' => $approvisionnement->note_id,
                        'receiver_id' => $approvisionnement->user_id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                return response(["success" => "Réponse de la demande envoyé", "id" => $approvisionnement->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function do_traite(Request $request, $id){
        $approvisionnement = Approvisionnement::find($id);
        $auth = $request->user();
        if(in_array($auth->role, ["achat","daf"]) && in_array($approvisionnement->status, ["demande"])){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            
            $approvisionnement->status = "traite";
            $approvisionnement->updated_at = new \DateTime();

            $approvisionnement->note_id = HistoriqueController::action_approvisionnement($request, "DA en cours de traitement", $id);
            if($approvisionnement->save()){
                return response(["success" => "DA bien envoyée", "id" => $approvisionnement->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function save_done(Request $request, $id){
        $approvisionnement = Approvisionnement::find($id);
        $auth = $request->user();
        if(
            in_array($auth->role, ["daf"]) && in_array($approvisionnement->status, ["demande", "traite"]) ||
            in_array($auth->role, ["compta"]) && in_array($approvisionnement->status, ["traite"])
        ){
            $validator = Validator::make($request->all(), [
                'total' => 'required|numeric|min:1',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
            $approvisionnement->total = $request->total;
            $approvisionnement->status = "done";
            $approvisionnement->updated_at = new \DateTime();

            $approvisionnement->note_id = HistoriqueController::action_approvisionnement($request, "DA terminé", $id);
            if($approvisionnement->save()){
                return response(["success" => "DA terminé avec succès", "id" => $approvisionnement->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function edit(Request $request, $id){
        $approvisionnement = Approvisionnement::find($id);
        $old_approvisionnement = clone $approvisionnement;
        $auth = $request->user();
        if($approvisionnement->user_id == $auth->id && $approvisionnement->status == "draft"){
            $validator = Validator::make($request->all(), [
                'objet' => 'required|string',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $approvisionnement->user_id = $request->user()->id;
            $approvisionnement->objet = $request->objet;
            $approvisionnement->status = "draft";
            $approvisionnement->updated_at = new \DateTime();            
            if($approvisionnement->save()){                              
                HistoriqueController::update_approvisionnement($request, $old_approvisionnement, "DA modifié");
                return response(["success" => "DA modifié", "id" => $approvisionnement->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function edit_item(Request $request, $id){
        $da_item = DaItem::find($id);
        $approvisionnement = Approvisionnement::find($da_item->approvisionnement_id);
        $old_da_item = clone $da_item;
        $auth = $request->user();
        if($approvisionnement->user_id == $auth->id && $approvisionnement->status == "draft"){
            $da_item->updated_at = new \DateTime();            
            $validator = $this->validateAndSetItem($request->all(), $da_item);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            if($da_item->save()){
                HistoriqueController::update_approvisionnement_item($request, $old_da_item, "DA modifié");
                return response(["success" => "Article modifié", "item" => $da_item]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function cancel_approvisionnement(Request $request, $id){
        $approvisionnement = Approvisionnement::find($id);
        if(
            ($request->user()->id == $approvisionnement->user_id && in_array($approvisionnement->status, ['validation'])) ||
            ($request->user()->role == 'validateur' && in_array($approvisionnement->status, ['validation', 'demande', 'traite']))
        ){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);

            $approvisionnement->status = 'draft';
            $approvisionnement->updated_at = new \DateTime();

            $approvisionnement->note_id = HistoriqueController::action_approvisionnement($request, "DA annulé", $id);
            if($approvisionnement->save()){
                if($request->user()->role == 'validateur'){
                    Notification::create([
                        'historique_id' => $approvisionnement->note_id,
                        'receiver_id' => $approvisionnement->user_id,
                        'user_id' => $request->user()->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                return response(["success" => "DA annulé", "id" => $approvisionnement->id]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function send_back(Request $request, $id){
        $approvisionnement = Approvisionnement::find($id);
        $items = DaItem::where('approvisionnement_id', $id)->get();
        $old_approvisionnement = clone $approvisionnement;
        if(count($items) > 0 && $request->user()->id == $approvisionnement->user_id && $approvisionnement->status == "draft"){
            $validator = Validator::make($request->all(), [
                'note' => 'required',
            ]);
            if ($validator->fails())
                return response(['error' => $validator->errors()->first()]);
  
            $approvisionnement->status = "validation";
            $approvisionnement->updated_at = new \DateTime();

            $approvisionnement->note_id = HistoriqueController::action_approvisionnement($request, "Renvoie de la demande", $approvisionnement->id);
            if($approvisionnement->save()){
                return response(["success" => "Renvoie de la demande", "id" => $approvisionnement->id]);
            }
            return response(["error" => "Erreur d'envoi, réessayez"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function last_approvisionnement(Request $request){
        $auth = $request->user();
        $approvisionnements = DB::select("SELECT appro.id, appro.objet, appro.user_id, appro.created_at, appro.updated_at,
            stat.description as 'status_description', stat.color as 'status_color', 
            us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email'
            FROM approvisionnements appro
            LEFT JOIN users us on us.id = appro.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN `status` stat on stat.name = appro.status
            where appro.user_id = ? and appro.status != 'draft'
            order by appro.created_at DESC LIMIT 3", [$auth->id]);
        return response($approvisionnements);
    }

    public function destroy_da_items(Request $request,$id){
        $auth = $request->user();
        $da_item = DaItem::find($id);
        $appro = Approvisionnement::find($da_item->approvisionnement_id);
        if($auth->id == $appro->user_id && in_array($appro->status, ['draft'])) { 
            $request['note'] = $da_item->designation;  
            if($da_item->delete()){
                HistoriqueController::action_approvisionnement($request, "Suppression de l'article", $appro->id);
                return response(["success" => "Suppression de l'article"]);
            }            
        }
        else 
            return response(["error" => "EACCES"]);
    }
}
