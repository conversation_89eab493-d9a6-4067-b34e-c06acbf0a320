.input-container,
.input-container-lg {
    padding: 10px 0px;
}

.input-container-lg>input {
    width: 100%;
    padding: 20px;
    font-size: 12pt;
    border: 1px #ccc solid;
}

.input-btn-item {
    width: 100%;
    padding: 20px 0;
    font-size: 14pt;
    border: none;
    /*border: 1px #ccc solid;*/
    text-align: center;
    background: #888888;
    color: #fff;
}

.input-container>label {
    display: inline-block;
    padding: 5px 0px;
    font-size: 12pt;
}

.input-date,
.input-container>input,
.input-container>textarea,
.input-container>select {
    width: 100%;
    padding: 10px;
    font-size: 12pt;
    border: 1px #ccc solid;
}

.input-date {
    z-index: 1;
}

.form-button-container {
    width: 100%;
    padding: 20px 0px;
    display: flex;
    justify-content: right;
}

.form-button-container>button {
    display: inline-block;
    font-size: 12pt;
    padding: 10px 15px;
    margin-left: 20px;
    width: 150px;
    border: none;
    background-color: #888;
    color: white;
}

.input-container-btn {
    padding: 50px 0px;
}

.input-container-btn>button {
    width: 100%;
    padding: 20px;
    text-align: center;
    background-color: #888;
    color: white;
    font-size: 14pt;
    border: none;
}

button.primary {
    background-color: #073570;
}

.container-error {
    margin: 20px 0px 10px 0px;
    color: #b71c1c;
}

.field-container {
    padding: 20px 0px 10px 0px;
}


/***INPUT SELECT***/

.select-container {
    width: 100%;
}

.select-container>.select-input {
    position: relative;
    user-select: none;
}

.select-input>.select-placeholder {
    padding: 10px;
    background: #fff;
    border: 1px #ccc solid;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-transform: capitalize;
    width: 100%;
}
.select-input>.select-placeholder-min {
    padding: 10px;
    background: #fff;
    border: 1px #ccc solid;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-transform: capitalize;
    width: 100%;
}

.select-input>.select-content {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: #fff;
    box-shadow: 0 2px 2px rgb(0 0 0 / 10%);
    z-index: 1;
    max-height: 150px;
    overflow-y: scroll;
}

.select-content>.select-option {
    padding: 15px;
    cursor: pointer;
    transition: all 0.2s;
    text-transform: capitalize;
}

.select-content>.select-option:hover {
    background: #ccc;
    color: #fff;
}

.select-placeholder>span>svg {
    transform: translateY(3px);
}

/* SELECT SEARCH */
.select-list{
    position: relative;
    padding: 5px;
    border: 1px #ccc solid;
    background-color: white;
    z-index: 2000;
    box-shadow: 3px 3px 5px rgba(0, 0, 0, .3);
}
.select-item {
    padding: 10px 5px;
    border-top: 1px #ccc solid;
}
.select-list>div:first-child{
    border-top: none;
}
.select-item:hover {
    background-color: #eee;
}
.input-select-relative{
    height: 0px;
}
.pj-file{
    padding: 10px 5px;
    border-top: 1px solid #eee;
}