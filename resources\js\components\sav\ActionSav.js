import React, { useState } from 'react';

import { Link } from 'react-router-dom';
import NoteModal from '../input/NoteModal';

export default function ActionSav({auth, sav, updateData}) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [currentAction, setAction] = useState(null)

    const handleCancelSav = (id) => {
        setAction({
            header: "Annuler la sav",
            request: "/api/sav/cancel_sav/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleCancelValidation = (id) => {
        setAction({
            header: "Annuler la demande de validation",
            request: "/api/sav/cancel_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleRequestValidation = (id) => {
        setAction({
            header: "Demande de validation",
            request: "/api/sav/request_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleReplyValidation = (id) => {
        setAction({
            header: "Réponse à la demande",
            request: "/api/sav/reply_validation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    
    const handleDone = (id) => {
        setAction({
            header: "Terminer le traitement de la sav",
            request: "/api/sav/save_done/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction} 
                updateData={() => updateData(true)} 
                closeModal={() => toggleNoteModal(false)}/> 
        }
        <div className='action-container'>
            {
                (["convocation", "traite"].includes(sav.status) && ["tech","electronique"].includes(auth.role)) && 
                <span onClick={() => handleDone(sav.id)}>Terminer</span>
            }
            {
                (["convocation"].includes(sav.status) && ["superviseur", "resp_sup", "resp_op", "tech", "electronique"].includes(auth.role)) &&
                <span>Imprimer</span>
            }
            {/*
                (['traite'].includes(sav.status) && ["tech"].includes(auth.role)) &&
                <span>
                    <Link to={"/sav/send_notification/" + sav.id}>Notifier par email</Link>
                </span>
            */}
            {
                (['traite'].includes(sav.status) && ["tech","electronique"].includes(auth.role)) &&
                <span onClick={() => handleRequestValidation(sav.id)}>Demander une validation</span>
            }
            {
                (["demande"].includes(sav.status) && ["tech","electronique"].includes(auth.role)) && 
                <span>
                    <Link to={"/sav/do_traite/" + sav.id}>Traiter</Link>
                </span>
            }
            {
                (["traite"].includes(sav.status) && ["tech","electronique"].includes(auth.role)) && 
                <span>
                    <Link to={"/sav/edit/" + sav.id}>Editer</Link>
                </span>
            }
            {
                (["validation"].includes(sav.status) && ["validateur"].includes(auth.role)) &&
                <span onClick={() => handleReplyValidation(sav.id)}>Répondre</span>
            }
            {
                (["validation"].includes(sav.status) && ["tech","electronique"].includes(auth.role)) &&
                <span onClick={() => handleCancelValidation(sav.id)}>Annuler la demande de validation</span>
            }
            {
                ((['demande', 'traite'].includes(sav.status) && ["tech","electronique"].includes(auth.role)) || 
                (['demande'].includes(sav.status) && auth.id == sav.user_id && ["superviseur","resp_sup","resp_op"].includes(auth.role))) &&
                <span onClick={() => handleCancelSav(sav.id)}>Annuler le SAV</span>
            }
            {
                (["draft"].includes(sav.status) && auth.id == sav.user_id) && 
                <span>
                    <Link to={"/sav/send_back/" + sav.id}>{["tech","electronique"].includes(auth.role) ? "Refaire le traitement" : "Renvoyer"}</Link>
                </span>
            }
        </div>
    </div>
}