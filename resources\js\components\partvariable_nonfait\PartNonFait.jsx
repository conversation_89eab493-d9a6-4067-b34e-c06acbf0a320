import React, { useEffect, useState } from 'react';
import LoadingPage from '../loading/LoadingPage';
import InfiniteScroll from 'react-infinite-scroll-component';
import matricule from '../util/matricule';
import showAmount from '../util/numberUtil';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import SearchBar from '../input/SearchBar';
import useToken from '../util/useToken';
export default function PartNonFait({auth, partNonFaits, setPartNonFaits, currentId, setCurrentId}) {
    const locationSearch = useLocation().search
    const hasUserId = (new URLSearchParams(locationSearch)).get("user_id")
    const hasNotFoundResp = (new URLSearchParams(locationSearch)).get("not_found_resp_part")
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const navigate = useNavigate();
    const searchItems = [
        {label: "Employé", name: 'employe_id', type:"number"},
        {label: 'Responsable', name: 'user_id', type:'number'},
    ]
    const updateData = (initial) => {
        let isMounted = true;
        const urlParams = new URLSearchParams(locationSearch)
        if(initial){
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            urlParams.set("offset", 0)
        }
        else
            urlParams.set("offset", partNonFaits.length)

        axios.get("/api/employe/get_pv_not_done?" + urlParams ,  useToken())
        .then((res) => {
            if(isMounted){
                if(res.data.error)
                    console.error(res.data.error)
                else {
                    if(initial) {
                        setPartNonFaits(res.data)
                    }
                    else {
                        const list = partNonFaits.slice().concat(res.data)
                        setPartNonFaits(list)
                    }
                    setDataLoaded(res.data.length < 30)
                }
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false };
    }

    useEffect(() => updateData(true), [locationSearch])

    const fetchMoreData = () => {
        setTimeout(() => {
            updateData()
        }, 300);
    };

    const linkUserWithParam = (id) => {
        const urlParams = new URLSearchParams(locationSearch)
        urlParams.set("user_id", id)
        return urlParams
    }

    useEffect(() => {
        if(partNonFaits && (!hasNotFoundResp && !hasUserId)) {
            if(partNonFaits.length == 1){
                let currentPv = partNonFaits[0]
                if(currentPv.responsable_nom){
                    navigate("/part-non-fait?" + linkUserWithParam(currentPv.user_id))
                }
                else {
                    navigate("/part-non-fait?not_found_resp_part=" + currentPv.resp_part_id)
                }
            }
        }
    }, [partNonFaits])
    
    return (
        <> 
            {
            isLoading ?
                <LoadingPage/>
            : 
                <div >
                    <div className="padding-container space-between">
                        <h2>Part variable non fait</h2>
                    </div>
                    <SearchBar hasEmploye listItems={searchItems} />
                    {
                        partNonFaits.length == 0 ? 
                            <h3 className='center secondary'>Aucun données trouvé</h3>
                        :
                            <div>
                                {
                                    (!(hasUserId || hasNotFoundResp) && ['resp_rh', 'validateur'].includes(auth.role)) ?
                                        <>
                                            <div className="line-container ">
                                                <div className='row-employe'>
                                                    <b className='line-cell-lg'>Nom</b>
                                                    <b className='line-cell-xs'>Nb</b>
                                                    <b>Email</b>
                                                </div>
                                            </div>
                                            {
                                                partNonFaits.map((pv, index) => (
                                                    <div className={'line-container ' + (!pv.responsable_nom && 'danger') } key={index}>
                                                        <Link className='link-no-style' to={(pv.responsable_nom) ? "/part-non-fait?" + linkUserWithParam(pv.user_id) : ("/part-non-fait?not_found_resp_part=" + pv.resp_part_id)}>
                                                            <div className='row-employe'>
                                                                <span className={'capitalize line-cell-lg '}>{ pv.responsable_nom ?? pv.responsable}</span>
                                                                <span className='line-cell-xs'>{pv.nb_pv}</span>
                                                                <span>{pv.responsable_email ?? "Utilisateur non defini"}</span>
                                                            </div>
                                                        </Link>
                                                    </div>
                                                ))
                                            }
                                        </>
                                    :
                                        <InfiniteScroll
                                            dataLength={partNonFaits.length}
                                            next={fetchMoreData}
                                            hasMore={!allDataLoaded}
                                            loader={<LoadingPage/>}
                                        >
                                            <div className='line-container'>
                                                <div className='row-employe'>
                                                    <b className='line-cell-xg'>Nom</b>
                                                    <b className='line-cell-sm'>Attribuable</b>
                                                </div>
                                            </div>
                                            {
                                                partNonFaits.map((pv, index) => (
                                                    <div className={`table line-container ${currentId && currentId == pv.employe_id ? 'selected' : ''}`} key={index}>
                                                        <div className='row-employe' onClick={() => {setCurrentId(pv.employe_id)}}>
                                                            <span className='line-cell-xg'>
                                                                {'['+ matricule(pv) +'] - ' + pv.nom}
                                                            </span>
                                                            <span className='line-cell-sm'>
                                                                {showAmount(pv.montant)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                ))
                                            }
                                        </InfiniteScroll>
                                }
                            </div>
                    }
                </div>
            }
        </>
    )
}