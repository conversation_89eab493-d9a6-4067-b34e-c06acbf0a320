import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import useToken from '../util/useToken';
import ActionApprovisionnement from './ActionApprovisionnement';
import Tab from '../layout/Tab';
import LoadingPage from '../loading/LoadingPage';
import moment from 'moment';
import ShowYearHeader from '../view/ShowYearHeader';

export default function ShowApprovisionnement({auth, currentId, setCurrentId, setCurrentItem, size}) {
    const params = useParams()
    const [isLoading, toggleLoading] = useState(true)
    const [appro, setAppro] = useState(null)
    const [defautUsers, setDefautUsers] = useState()

    const updateData = () => {
        let isMounted = true
        toggleLoading(true)
        axios.get('/api/approvisionnement/show/' + (currentId ? currentId : params.id), useToken())
        .then((res) => {
            if(isMounted){
                setAppro(res.data)
                const newUser = []
                if (auth.id != res.data.user_id)
                    newUser.unshift({id:res.data.user_id, address:res.data.user_email, name:res.data.user_nom})
                setDefautUsers(newUser)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
            toggleLoading(false)
        })
        return () => { isMounted = false };
    }

    useEffect(() => {
        if(setCurrentItem)
            setCurrentItem(appro)
    }, [appro]);

    useEffect(updateData, [currentId])

    return <> {
        isLoading ?
            <LoadingPage/>
        :
        <div>
            {
                appro &&
                <>
                    <ShowYearHeader label="DA" data={appro} closeDetail={() => setCurrentId()}/>
                    <div className="card-container">
                        <div className='badge-container'>
                            <span>
                                <span className={'badge-outline badge-outline-' + appro.status_color}>
                                    {appro.status_description}
                                </span> {
                                    appro.nb_pj > 0 &&
                                    <span className='badge-outline'>
                                        Pièce jointe : {appro.nb_pj}
                                    </span>
                                }
                            </span>
                        </div>
                        <h3>
                            {"DA-" + moment(appro.created_at).format("YYYY") + "/" + ("00000" + appro.reference).slice(-6) }
                        </h3>
                        <div>
                            Objet : <span className='text'>{appro.objet}</span>
                        </div>
                        <div>
                            Service : <span className='text'>{appro.service}</span>
                        </div>
                        <div>
                            Demandeur : <span className='text'> 
                                {appro.user_nom} {' <' + appro.user_email + '>'}
                            </span>
                        </div>
                        <div>
                            Le : <span className='text'> 
                                {moment(appro.created_at).format("DD MMMM YYYY")}
                            </span>
                        </div>
                        <div className='card-action'>
                            <ActionApprovisionnement auth={auth} approvisionnement={appro} updateData={updateData}/>
                        </div>
                    </div>
                    <Tab auth={auth} name="approvisionnement_id" value={appro.id} updateData={updateData} defautUsers={defautUsers}/>
                </>
            }
        </div>
    } </>
}