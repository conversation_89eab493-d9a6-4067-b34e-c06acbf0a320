import React from 'react';
import ExcelJS from 'exceljs';
import matricule from '../util/matricule';
import moment from 'moment/moment';

export default function PartExcel({ partVariables, datePaie, isGroupByUser, isGroupByFonction, isDetail, closeModal }) {
    const reformeData = () => {
        let dataCurrent = partVariables.map(part => {
            let matr = matricule(part);
            let societe = matr.match(/[a-zA-Z]+|\d+/g)[0];
            let num_matr = matr.match(/\d+/) ? matr.match(/\d+/)[0] : "Ndf";
            return {
                "Societe": societe,
                "matricule": num_matr,
                "NOM": part.employe,
                "FONCTION": part.fonction,
                "AGENCE": part.agence,
                "FAIT PAR": part.user_name,
                "MONTANT TOTAL" : part.montant_total,
                "Condition": part.criteres,
                "TITRE" : part.titre,
            };
        });

        dataCurrent.sort((a, b) => {
            if (isGroupByFonction) {
                let compareFonction = a['FONCTION'].localeCompare(b['FONCTION']);
                if (compareFonction !== 0) return compareFonction;
            }
            
            let compareUser = a['FAIT PAR'].localeCompare(b['FAIT PAR']);
            if (compareUser !== 0) return compareUser;
    
            let compareSociete = a.Societe.localeCompare(b.Societe);
            if (compareSociete !== 0) return compareSociete;

            let numA = isNaN(parseInt(a['matricule'])) ? 0 : parseInt(a['matricule']);
            let numB = isNaN(parseInt(b['matricule'])) ? 0 : parseInt(b['matricule']);
            return numA - numB;
        });
        return dataCurrent;
    };

    const createExcelFile = async () => {
        let dataPartVariables = reformeData()
        const workbook = new ExcelJS.Workbook();
        workbook.creator = "ADMIN";
        let worksheet = null;
        let headers = [
            "SOCIETE",
            "MATRICULE",
            "NOM",
            "FONCTION",
            "AGENCE",
            "RESPONSABLE",
            "MONTANT TOTAL"
        ];
        if(isDetail){
            headers=[
                "SOCIETE",
                "MATRICULE",
                "NOM",
                "FONCTION",
                "AGENCE",
                "RESPONSABLE",
                "MONTANT TOTAL",
                "CONDITION",
                "MONTANT",
            ]
        }

        let lastUser = null;
        let lastFonction = null;
        if (!isGroupByUser && !isGroupByFonction) {
            worksheet = workbook.addWorksheet(`Part_Variable_${(moment(datePaie).format("MMM YYYY")).toUpperCase()}`);
            worksheet.addRow(headers);
        }

        let rowIndex = 2;

        const getWorksheetByName = (name) => {
            let sheet = workbook.getWorksheet(name);
            if (!sheet) {
                sheet = workbook.addWorksheet(name);
                sheet.addRow(headers);
            }
            return sheet;
        };
        
        dataPartVariables.forEach(data => {
            let values = headers.map((header, index) => Object.values(data)[index]);
            if (isGroupByUser) {
                if (!lastUser || lastUser !== data['FAIT PAR']) {
                    worksheet = getWorksheetByName(data['FAIT PAR']);
                    lastUser = data['FAIT PAR'];
                    rowIndex = 2;
                }
            } else if (isGroupByFonction) {
                if (!lastFonction || lastFonction !== data['FONCTION']) {
                    worksheet = getWorksheetByName(data['FONCTION']);
                    lastFonction = data['FONCTION'];
                    rowIndex = 2;
                }
            } else {
                worksheet = workbook.getWorksheet(`Part_Variable_${(moment(datePaie).format("MMM YYYY")).toUpperCase()}`);
            }
            if(data['FONCTION'].toLowerCase() == 'autre') values[3] = data['TITRE']
            worksheet.addRow(values);
            if(isDetail){
                ['A', 'B', 'E', 'F'].forEach(col => {
                    worksheet.mergeCells(`${col}${rowIndex}:${col}${rowIndex + (data.Condition.length) - 1}`);
                })
            }
            if(isDetail){
                data.Condition.forEach((cond, i) => {
                    let cellI = worksheet.getCell(`I${rowIndex + i}`);
                    cellI.value = cond.montant;
                    cellI.alignment = { vertical: 'middle' };
    
                    let cellH = worksheet.getCell(`H${rowIndex + i}`);
                    cellH.value = cond.condition ? cond.condition : '';
                    cellH.alignment = { vertical: 'middle' };
                });
            }

            ['A', 'B', 'E', 'F'].forEach(col => {
                worksheet.getCell(`${col}${rowIndex}`).alignment = { horizontal: 'center', vertical: 'middle' };
            });
            worksheet.columns.forEach(column => {
                column.width = 15;
            });

            worksheet.eachRow(row => {
                row.eachCell(cell => {
                    cell.font = { size: 12 };
                    cell.border = {
                        top: { style: 'thin', color: { argb: 'FF000000' } },
                        left: { style: 'thin', color: { argb: 'FF000000' } },
                        bottom: { style: 'thin', color: { argb: 'FF000000' } },
                        right: { style: 'thin', color: { argb: 'FF000000' } },
                    };
                });
            });

            worksheet.getRow(1).font = { bold: true, size: 12 };
            ['C', 'E', 'F', 'G','H'].forEach((col, i) => {
                worksheet.getColumn(col).width = [40, 30, 20, 30, 70][i];
            });
            ['A', 'D'].forEach((col, i) => {
                worksheet.getColumn(col).width = [10, 30][i];
            });

            worksheet.views = [
                {
                    state: 'frozen',
                    xSplit: 1,
                    ySplit: 1,
                },
            ];
            rowIndex += isDetail ? data.Condition.length : 1;
        });

        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `PART_VARIABLE_${(moment(datePaie).format("MMM YYYY")).toUpperCase()}.xlsx`;
        a.click();
        closeModal();
    };

    return <button className="btn btn-primary" onClick={() =>createExcelFile()}>
        Exporter
    </button>
}
