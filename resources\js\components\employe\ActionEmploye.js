import React, { useState } from 'react';
import ChangeSiteModal from '../modal/ChangeSiteModal';
import { Link } from 'react-router-dom';
import ConfirmModal from '../modal/ConfirmModal';
import useToken from '../util/useToken';
import ArchiveEmployeModal from './ArchiveEmployeModal';
import BlacklistEmployeModal from './BlacklistEmployeModal';
import DoublonModal from './DoublonModal';

export default function ActionEmploye({auth, employe, updateData}) {
    const [showSiteModal, toggleSiteModal] = useState(false)
    const [showArchiveModal, toggleArchiveModal] = useState(false);
    const [showRestoreModal, toggleRestoreModal] = useState(false);
    const [showBlacklistModal, toggleBlacklistModal] = useState(false);
    const [showDoublonModal, toggleDoublonModal] = useState(false);

    const handleRestore = () => {
        toggleRestoreModal(false)
        axios.post("/api/employe/restore/" + employe.id, {}, useToken())
        .then(res => {
            updateData()
        })
    }

    return <div>
        {
            showSiteModal && 
            <ChangeSiteModal
                employe={employe}
                updateData={() => updateData(true)} 
                closeModal={() => toggleSiteModal(false)}/> 
        }
        {
            showArchiveModal &&
            <ArchiveEmployeModal
                employe={employe}
                updateData={() => updateData(true)}
                closeModal={() => toggleArchiveModal(false)}/>
        }
        {
            showRestoreModal &&
            <ConfirmModal msg="Restaurer l'employé ?"
                confirmAction={handleRestore}
                closeModal={() => toggleRestoreModal(false)}/>
        }
        {
            showBlacklistModal &&
            <BlacklistEmployeModal
                employe={employe}
                updateData={() => updateData(true)}
                closeModal={() => toggleBlacklistModal(false)}
            />
        }
        {
            showDoublonModal &&
            <DoublonModal
                employe={employe}
                updateData={() => updateData(true)}
                closeModal={() => toggleDoublonModal(false)}
            />
        }
        <div className='action-container'>
            {
                (["rh", "resp_rh", "admin"].includes(auth.role) && !employe.soft_delete) && 
                <span>
                    <Link to={"/employe/edit/" + employe.id}>Modifier</Link>
                </span>
            }
            {
                (["rh", "resp_rh", "resp_sup", "superviseur", "resp_op", "admin"].includes(auth.role) && !employe.soft_delete) && 
                <span onClick={() => {toggleSiteModal(true)}}>Changer de poste</span>
            }
            {
                (["rh", "resp_rh", "admin"].includes(auth.role) && !employe.soft_delete) && 
                <span onClick={() => {toggleArchiveModal(true)}}>Mettre en archive</span>
            }
            {
                (["rh", "resp_rh", "admin"].includes(auth.role) && (employe.soft_delete == 1 && employe.isBlacklist == 0)) && 
                <span onClick={() => {toggleRestoreModal(true)}}>Restaurer</span>
            }
            {
                (["rh", "resp_rh", "admin"].includes(auth.role) && employe.soft_delete == 1 && employe.isBlacklist == 0) &&
                <span onClick={() => {toggleBlacklistModal(true)}}>
                    Blacklister
                </span>
            }
            {
                (["admin"].includes(auth.role) && (employe.soft_delete == 0 || employe.soft_delete == null) && employe.isBlacklist == 0) &&
                <span onClick={() => {toggleDoublonModal(true)}}>
                    Doublons
                </span>
            }
        </div>
    </div>
}