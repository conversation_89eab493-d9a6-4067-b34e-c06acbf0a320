import React, { useState } from 'react';

import { Link } from 'react-router-dom';
import NoteModal from '../input/NoteModal';
import DoneReclamationModal from './DoneReclamationModal';

export default function ActionReclamation({auth, reclamation, updateData}) {
    const [showNoteModal, toggleNoteModal] = useState(false)
    const [currentAction, setAction] = useState(null)
    const [toDone, setToDone] = useState(false);
    const [showDoneModal, toggleDoneModal] = useState(false);

    const handleResendReclamation = (id) => {
        setAction({
            header: "Renvoyer la demande de reclamation",
            request: "/api/reclamation/send_back/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleCancelReclamation = (id) => {
        setAction({
            header: auth.role == 'validateur' ? "Annuler le reclamation" : "Annuler la demande de reclamation",
            request: "/api/reclamation/cancel_reclamation/" + id,
            required: true
        })
        toggleNoteModal(true)
    }

    const handleDoTraite = (id) => {
        setAction({
            header: "Traitement de la demande",
            request: "/api/reclamation/do_traite/" + id,
            required: true
        })
        toggleNoteModal(true)
    }
    
    // const handleDone = (id) => {
    //     setAction({
    //         header: "Terminer le traitement de la reclamation",
    //         request: "/api/reclamation/save_done/" + id,
    //         required: true
    //     })
    //     toggleNoteModal(true)
    //     setToDone(true);
    // }
    return <div>
        {
            showNoteModal && 
            <NoteModal 
                action={currentAction}
                updateData={() => updateData()}
                closeModal={() => toggleNoteModal(false)}
                toDone={toDone}
                setToDone={setToDone}
            /> 
            
        }
        {
            showDoneModal && 
            <DoneReclamationModal id={reclamation.id}
                updateData={() => updateData()}
                closeModal={() => toggleDoneModal(false)}
            />
        }
        <div className='action-container'>
            {
                (["demande"].includes(reclamation.status) && ["rh", "resp_rh"].includes(auth.role)) && 
                <span onClick={() => handleDoTraite(reclamation.id)}>Traiter</span>
            }
            {
                (["demande", "traite"].includes(reclamation.status) && ["rh", "resp_rh"].includes(auth.role)) && 
                <span onClick={() => toggleDoneModal(true)}>Terminer</span>
            }
            {
                (
                    ["draft"].includes(reclamation.status) && 
                    (auth.id == reclamation.user_id || ["rh", "resp_rh"].includes(auth.role))
                ) && 
                <span onClick={() => handleResendReclamation(reclamation.id)}>Renvoyer</span>
            }
            {/* {
                (
                    (["demande", "traite"].includes(reclamation.status) && ["rh", "resp_rh"].includes(auth.role))
                    || (["draft"].includes(reclamation.status) && auth.id == reclamation.user_id)
                ) && 
                <span>
                    <Link to={"/reclamation/edit/" + reclamation.id}>Modifier</Link>
                </span>
            } */}
            {
                (["validation"].includes(reclamation.status) && ["validateur"].includes(auth.role)) &&
                <span>
                    <Link to={"/reclamation/do_validation/" + reclamation.id}>Répondre</Link>
                </span>
            }
            {
                (['demande', 'traite', null].includes(reclamation.status) && ["rh", "resp_rh"].includes(auth.role)) &&
                <span onClick={() => handleCancelReclamation(reclamation.id)}>Annuler la reclamation</span>
            }
        </div>
    </div>
}