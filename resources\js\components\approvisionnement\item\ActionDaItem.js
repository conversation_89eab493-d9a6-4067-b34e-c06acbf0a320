import React, { useState } from 'react';

import NoteModal from '../../input/NoteModal';
import DoneDaItemModal from './DoneDaItemModal';

export default function ActionDaItem({article, updateData}) {
    const [showDoneModal, toggleDoneModal] = useState(false)
    const [currentAction, setAction] = useState(null)

    const handleDoneArticle = (id) => {
        setAction({
            header: "Article DA terminé",
            request: "/api/da_item/done/" + id,
            required: false
        })
        toggleDoneModal(true)
    }
    const handleCancelArticle = (id) => {
        setAction({
            header: "Article DA annulé",
            request: "/api/da_item/cancel/" + id,
            required: true
        })
        toggleDoneModal(true)
    }

    return <div>
        {
            showDoneModal && 
            <DoneDaItemModal 
                action={currentAction} 
                updateData={() => updateData()} 
                closeModal={() => toggleDoneModal(false)}/> 
        }
        <div className='action-container'>
            <span onClick={() => handleDoneArticle(article.id)}>Terminer</span>
            {/*<span onClick={() => handleCancelArticle(article.id)}>Annuler</span>*/}
        </div>
    </div>
}