<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\VisitePoste;
use App\Models\Site;
use Illuminate\Support\Facades\Validator;
use App\Models\Alarm;
use Carbon\Carbon;

class VisitePosteController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function show(Request $request, $id){
        $visite = DB::select("SELECT v.id, v.date_visite, v.start, v.end, v.compte_rendu, v.created_at, st.nom as 'site',
            v.user_id, us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', y.id as 'seen', y.send_email
            FROM visite_postes v
            LEFT JOIN sites st on st.idsite = v.site_id
            LEFT JOIN users us on us.id = v.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN (SELECT s.id, s.visite_poste_id, s.send_email FROM seen s WHERE s.user_id = ?) y ON y.visite_poste_id = v.id
            where v.id = ?", [$request->user()->id, $id])[0];
        $pieces = DB::select("SELECT pj.id FROM piece_jointes pj
            WHERE pj.visite_poste_id = ?
            order by pj.created_at desc", [$id]);
        $visite->nb_pj = count($pieces);
        return response()->json($visite);
    }

    public function detail(Request $request, $id){
        $visite = DB::select("SELECT v.id, v.date_visite, v.start, v.end, v.compte_rendu, v.created_at, st.nom as 'site',
            v.user_id, us.name as 'user_nom', coalesce(ur.email, us.email) as 'user_email', y.id as 'seen', y.send_email
            FROM visite_postes v
            LEFT JOIN sites st on st.idsite = v.site_id
            LEFT JOIN users us on us.id = v.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN (SELECT s.id, s.visite_poste_id, s.send_email FROM seen s WHERE s.user_id = ?) y ON y.visite_poste_id = v.id
            where v.id = ?", [$request->user()->id, $id])[0];
        $pieces = DB::select("SELECT pj.id, pj.nature, pj.path, pj.user_id, pj.created_at, pj.updated_at
            FROM piece_jointes pj
            WHERE pj.visite_poste_id = ?
            order by pj.created_at desc", [$id]);
        $historiques = DB::select("SELECT h.id, h.objet, h.note, h.detail, h.created_at, h.user
            FROM historiques h
            where h.visite_poste_id = ?
            order by h.created_at desc
            limit 10", [$id]);
        return response()->json(compact('visite', 'pieces', 'historiques'));
    }

    protected function search(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = " v.id = " . $request->id . " ";
        else  {
            if($request->unread)
                $searchArray[] = " y.id is null ";
            if($request->created_at)
                $searchArray[] = " v.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "v.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->user_id)
                $searchArray[] = " v.user_id = " . $request->user_id . " ";
            if($request->site_id)
                $searchArray[] = " v.site_id = " . $request->site_id . " ";
            if($request->date_visite)
                $searchArray[] = " v.date_visite = " . $request->date_visite . " ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by CASE WHEN v.user_id = ". $request->user()->id ." THEN 0 ELSE 1 END, v.id desc limit ". $request->offset . ", 10";
        $query_and = $query_and . " order by CASE WHEN v.user_id = ". $request->user()->id." THEN 0 ELSE 1 END, v.id desc limit ". $request->offset . ", 10";
        return compact('query_where', 'query_and');
    }

    public function index(Request $request){
        $search = $this->search($request);
        $visites = [];
        if($search){
            if($request->user()->role == 'superviseur'){
                $visites = DB::select("SELECT v.id, v.date_visite, v.start, v.end, v.compte_rendu, v.created_at, st.nom as 'site',
                    st.pointage, st.pointeuse, v.user_id, us.name as 'user_nom', us.email as 'user_email', y.id as 'seen', y.send_email
                    FROM visite_postes v
                    LEFT JOIN sites st on st.idsite = v.site_id
                    LEFT JOIN users us on us.id = v.user_id
                    LEFT JOIN (SELECT s.id, s.visite_poste_id, s.send_email FROM seen s WHERE s.user_id = ?) y ON y.visite_poste_id = v.id
                    where v.user_id = ? " . $search['query_and']
                    , [$request->user()->id, $request->user()->id]);
            }
            else if(in_array($request->user()->role, ['validateur', 'access', 'resp_sup', 'resp_op', 'daf'])){
                $visites = DB::select("SELECT v.id, v.date_visite, v.start, v.end, v.compte_rendu, v.created_at, st.nom as 'site',
                    st.pointage, st.pointeuse, v.user_id, us.name as 'user_nom', us.email as 'user_email', y.id as 'seen', y.send_email
                    FROM visite_postes v
                    LEFT JOIN sites st on st.idsite = v.site_id
                    LEFT JOIN users us on us.id = v.user_id
                    LEFT JOIN (SELECT s.id, s.visite_poste_id, s.send_email FROM seen s WHERE s.user_id = ?) y ON y.visite_poste_id = v.id
                    " . $search['query_where'] 
                    , [$request->user()->id]);
            }
            if(count($visites) > 0){
                $pieces = DB::select("SELECT id, visite_poste_id FROM piece_jointes WHERE visite_poste_id in (" . implode(",", array_column($visites, "id")) . ")");
                foreach ($visites as $item) {
                    $nb_pj = 0;
                    foreach ($pieces as $pj) {
                        if($item->id == $pj->visite_poste_id)
                            $nb_pj += 1;
                    }
                    $item->nb_pj = $nb_pj;
                }
            }
        }
        else 
            return response(["error" => "EACCES"]);
        return response(compact('visites'));
    }

    public function store(Request $request){
        $user = $request->user();
        if(in_array($user->role, ["superviseur", "resp_sup", "resp_op", "su"])){
            if($request->interim){
                $validator = Validator::make($request->all(), [
                    'site_id' => 'required',
                    'employe_id' => 'required',
                    'date_visite' => 'required',
                    'compte_rendu' => 'required',
                    'start' => 'nullable',
                    'end' => 'nullable',
                    // 'isBreackDown' => 'nullable',
                ]);
            } else {
                $request->employe_id = $user->employe_id;
                $validator = Validator::make($request->all(), [
                    'site_id' => 'required',
                    'date_visite' => 'required',
                    'compte_rendu' => 'required',
                    'start' => 'nullable',
                    'end' => 'nullable',
                    // 'isBreackDown' => 'nullable',
                ]);
            }

            if ($validator->fails()) {
                return response(['error' => $validator->errors()->first()]);
            }

            $site = Site::find($request->site_id);
            if (!$site) {
                return response(['error' => 'Site non trouvé.']);
            }

            if(
                (new \DateTime($request->date_visite)) < (new \DateTime())->sub(new \DateInterval('P7D'))
                || (new \DateTime($request->date_visite)) > (new \DateTime())->add(new \DateInterval("PT10M"))
                || (new \DateTime($request->date_visite))->format('H:i:s') == "00:00:00"
            ) {
                return response(['error' => "Date de visite incorrecte."]);
            }

            $visite = new VisitePoste();
            $visite->site_id = $request->site_id;
            $visite->employe_id = $request->employe_id;
            $visite->interim = $request->interim;
            $visite->date_visite = $request->date_visite;
            $visite->compte_rendu = $request->compte_rendu;
            $visite->start = $request->start;
            $visite->end = $request->end;
            $dtarrived = DB::select("SELECT dtarrived FROM test_periodiques WHERE site_id = ?", [$request->site_id]);
            $matched = 0;
            if ($dtarrived && count($dtarrived) > 0) {
                $dateVisite = Carbon::parse($request->date_visite);
                $startWindow = $dateVisite->copy()->subMinutes(90);
                $endWindow = $dateVisite->copy()->addMinutes(30);
                foreach ($dtarrived as $dt) {
                    $dtValue = Carbon::parse($dt->dtarrived);
                    if ($dtValue->between($startWindow, $endWindow)) {
                        $matched = 1;
                        break;
                    }
                }
            }
            if ($matched == 0) {
                $visite->isBreackDown = 1;
            } else {
                $visite->isBreackDown = 0;
            }

            $visite->user_id = $user->id;
            $visite->created_at = new \DateTime();
            $visite->updated_at = new \DateTime();
    
            if($visite->save()){
                $employe_id = $user->employe_id;
                if($employe_id) {
                    $begin = (\DateTime::createFromFormat("Y-m-d H:i:s", $request->date_visite))
                        ->sub(new \DateInterval('PT10M'));
                    $end = (\DateTime::createFromFormat("Y-m-d H:i:s", $request->date_visite))
                        ->add(new \DateInterval('PT10M'));
                    Alarm::where("employe_id", $employe_id)
                        ->where("date_visite", ">", $begin)->where("date_visite", "<=", $end)
                        ->whereNull("visite_poste_id")
                        ->update(["visite_poste_id" => $visite->id]);
                }
    
                Site::where('idsite', $visite->site_id)->update(['last_visite_id' => $visite->id]);
                HistoriqueController::new_visite_poste($request, $visite->id);
                return response(["success" => "Visite de poste bien enregistrée", "id" => $visite->id]);
            }
            return response("Erreur d'envoi, réessayez");
        }
        return response(["error" => "EACCES"]);
    }

    public function get_last_visite(Request $request) {
        if($request->employe_id)
            $employe_id = $request->employe_id;
        else
            $employe_id = $request->user()->employe_id;
        $site_id = $request->site_id;
        $last_visite = DB::select("SELECT date_visite as last_visite FROM visite_postes
            WHERE employe_id = ? and site_id = ?
            order by date_visite desc limit 1", [$employe_id, $site_id])[0]->last_visite;
        return response()->json($last_visite);
    }
}