import React, { } from 'react';
import { <PERSON> } from "react-router-dom";
import { AiOutlineMenu } from 'react-icons/ai';
import { IoMailOutline } from "react-icons/io5";
import { IoSettingsOutline } from "react-icons/io5";
import { IoMdNotificationsOutline } from 'react-icons/io';
import ModalEditRole from '../user/ModalEditRole';

export default function NavBar({auth, toggleSidebar, size}) {
    const [showRole, toggleRole] = React.useState(false)
    return (
        <nav id="navbar">
            {
                showRole &&
                <ModalEditRole auth={auth} closeModal={() => toggleRole(false)}/>
            }
            <div>
                <Link to="/"><img src="/img/blue_drx.svg" id='logo'/></Link> 
                {
                    size != "lg" &&
                    <AiOutlineMenu size={30} onClick={() => toggleSidebar(true)}/>
                }
            </div>
            <div className='right'>
                <div style={{padding: "10px"}}>
                    {auth.name} {[1, 2, 220, 221].includes(auth.id) && <IoSettingsOutline size={18} onClick={() => toggleRole(true)}/>} <br/>
                    <span className='secondary'>{auth.email}</span>
                </div>
                <div className={"notification-header " + (auth.note_message > 0 ? "new-notification" : "")}>
                    <Link reloadDocument to={"/message"} className='link-no-style'>
                        <IoMailOutline size="30"/>
                        {/*
                            auth.note_message > 0 && 
                            <span className='nb-notification'>{auth.note_message}</span>
                        */}
                    </Link>
                </div>
                <div className={"notification-header " + (auth.notification > 0 ? "new-notification" : "")}>
                    <Link reloadDocument to={"/notification"} className='link-no-style'>
                        <IoMdNotificationsOutline size={30}/>
                        {/*
                            auth.notification > 0 && 
                            <span className='nb-notification'>{auth.notification}</span>
                        */}
                    </Link>
                </div>
            </div>
        </nav>
    );
}