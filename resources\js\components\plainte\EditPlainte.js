import React, { useEffect, useState } from 'react';
import {useLocation, useParams} from 'react-router-dom'
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import Textarea from '../input/Textarea';
import InputAgence from '../input/InputAgence';
import DualContainer from '../container/DualContainer';
import InputSite from '../input/InputSite';
import removeDuplicateBreak from '../util/stringUtil';

export default function EditPlainte({title, action}) {
    const params = useParams()
    const locationSearch = useLocation().search
    const searchParams = new URLSearchParams(locationSearch)
    const [reference, setReference] = useState("")
    const [agence, setAgence] = useState(null)
    const [site, setSite] = useState(null);
    const [montant, setMontant] = useState(0)
    const [police, setPolice] = useState("");
    const [agent, setAgent] = useState("");
    const [fait, setFait] = useState("")
    const [suivi, setSuivi] = useState("");
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const faitStr = removeDuplicateBreak(fait)
        const suiviStr = removeDuplicateBreak(suivi)
        const data = {
            reference: reference,
            agence_id: agence ? agence.id : '',
            site_id: site ? site.id : '',
            montant: montant,
            police: police,
            agent: agent,
            fait: faitStr,
            suivi: suiviStr,
            fait_id: searchParams.get("fait_id"),
        }
        axios.post(action + (params.id ? "/" + params.id : ""), data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setNotification({success: "Une erreur est survenue."})
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
            setError("Erreur d'envoie, réessayez.")
        })
    }

    useEffect(() => {
        let isMounted = true
        const siteId = searchParams.get("site_id")
        /*if(siteId){
            axios.get('/api/site/show/' + siteId, useToken())
            .then((res) => {
                if(isMounted){
                    setSite(res.data)
                }
            })
        }*/
        if(params.id){
            axios.get('/api/plainte/detail/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    const plainte = res.data
                    if(plainte.agence)
                        setAgence(plainte.agence)
                    if(plainte.site)
                        setSite({
                            id: plainte.site.idsite,
                            nom: plainte.site.nom
                        })
                    if(plainte.reference)
                        setReference(plainte.reference)
                    if(plainte.montant)
                        setMontant(plainte.montant)
                    if(plainte.police)
                        setPolice(plainte.police)
                    if(plainte.agent)
                        setAgent(plainte.agent)
                    if(plainte.fait)
                        setFait(plainte.fait)
                    if(plainte.suivi)
                        setSuivi(plainte.suivi)
                }
            })
        }
        return () => { isMounted = false };
    }, [])

    return (
        <div id="content">
            {
                notification ? 
                    <Notification 
                        next={"/plainte" + (notification.id ? "?id=" + notification.id : "")} 
                        message={notification.success}/>
                :
                    <div>
                        <div className="title-container">
                            <h2>{title}</h2>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <InputSite
                                withoutDelete
                                required
                                value={site}
                                onChange={setSite}/>
                            <DualContainer>
                                <InputAgence
                                    required
                                    currentSelect={agence} 
                                    setCurrentSelect={setAgence}/>
                                <InputText 
                                    label="Référence"
                                    value={reference} 
                                    onChange={setReference}/>
                            </DualContainer>
                            <InputText 
                                required
                                label="Agent(s) concerné"
                                value={agent} 
                                onChange={setAgent}/>
                            <InputText
                                required
                                label="Rappelle des faits"
                                value={fait}
                                onChange={setFait}/>
                            <InputText 
                                required
                                label="Police/Gendarme compétent"
                                value={police} 
                                onChange={setPolice}/>
                            {
                                !params.id &&
                                <Textarea
                                    required
                                    label="Chose faite"
                                    value={suivi}
                                    onChange={setSuivi}/>
                            }
                            {
                                error &&
                                <div className='container-error'>
                                    {error}
                                </div>
                            }
                            <ButtonSubmit disabled={submitDisabled}/>
                        </form>
                    </div>
            }
        </div>
    )
}