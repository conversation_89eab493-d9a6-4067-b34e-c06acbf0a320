import React from 'react';

import moment from 'moment';

export default function LastSav({sav}) {
    return <div className="story-container">
        
        <h4 className='capitalize text'>
        <span className={'badge-outline badge-outline-' + sav.status_color}>
            {sav.status_description}
        </span>
        </h4>
        <p className='secondary'>
            <span className='text'>Motif : </span> 
            {sav.motif}<br/>
            <span className='text'>Demandé le: </span>
            { moment(sav.created_at).format("dddd DD MMM YYYY") } <br/>
            <span className='text'>Demandeur : </span> 
            {sav.user_nom}  {'<' + sav.user_email + '>'} <br/>
        </p>
    </div>
}