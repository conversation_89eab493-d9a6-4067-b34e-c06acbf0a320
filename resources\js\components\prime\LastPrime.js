import React from 'react';

import moment from 'moment';

export default function LastPrime({prime}) {
    return <div className="story-container">
        <h4 className='capitalize text'>
            <span>{/*prime.objet*/}</span>
        </h4>
        <p className='secondary'>
            <span className='text'>Motif : </span> 
            {prime.motif}<br/>
            <span className='text'>Date du service : </span>
            { moment(prime.date_pointage).format("DD MMM YYYY") } { (moment(prime.date_pointage).format("HH:mm:ss") == "07:00:00" ? " JOUR" : " NUIT") }
        </p>
    </div>
}