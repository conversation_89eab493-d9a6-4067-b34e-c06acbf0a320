import React, { useEffect, useState } from 'react'
import Textarea from '../input/Textarea'

export default function CommentModal({ closeModal, planning, handleAddComment }) {
    const [comment, setComment] = useState(planning.comment.comment_content)
    const [error, setError] = useState('')
    const onChangeComment = (value) => {
        if (value.length <= 200) {
            setComment(value)
        }
        else 
            setError('Excès de caractères')
    }
    useEffect(() => {
        if (comment.length < 200) {
            setError('')
        }
    }, [comment])
    
    return (
        <div className='modal'>
            <div>
                <Textarea label='Commentaire' value={comment} onChange={onChangeComment} />
                {
                    error &&
                    <div className='container-error'>
                        {error}
                    </div>
                }
                <div className='form-button-container'>
                    <button type='button'
                        className='btn btn-primary'
                        onClick={() => { handleAddComment(planning.id, comment), closeModal() }}
                    >
                        Enregistrer
                    </button>
                    <button type='button' onClick={() => closeModal()}>Annuler</button>
                </div>
            </div>
        </div>
    )
}
