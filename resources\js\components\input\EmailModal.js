import React, { useState } from 'react';

import Textarea from './Textarea';
import axios from 'axios';
import useToken from '../util/useToken';
import InputText from './InputText';

export default function EmailModal({objet, action, updateData, closeModal}) {
    const [contacts, setContacts] = useState([])
    const [searchContact, setSearchContact] = useState("")
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleOk = () => {
        disableSubmit(true)
        setError("")
        axios.post(action, {emails: contacts.map(c => ({address: c.email, name: c.name}))}, useToken())
        .then((res) => {
            if(res.data.error){
                setError(res.data.error)
                disableSubmit(false)
            }
            else if(res.data.success){
                closeModal()
                updateData()
            }
        })
        .catch((e) => {
            console.error(e)
            disableSubmit(false)
        })
    }

    return <div className='modal'>
        <div>
            <h3>Envoyer un email</h3>
            <div className='input-container'>
                <InputText label="Objet" value={objet} disabled/>
            </div>
            <div className='input-container'>
                <div className='destination-list'>
                    {
                        contacts.map(e => <span key={e.id} className="destination-item">
                            {e.name} <span className='text'>{e.email}</span> 
                        </span>)
                    }
                </div>
                <input />
            </div>
            <div className='input-container'>
                <Textarea value={email} label="Commentaire" onChange={(value) => setEmail(value)} required={action.required}/>
            </div>
            {
                error &&
                <div className='container-error'>
                    {error}
                </div>
            }
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Envoyer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>
}