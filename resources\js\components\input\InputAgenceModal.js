import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import InputAgence from './InputAgence';

export default function InputAgenceModal({closeModal}) {
    const navigate = useNavigate()
    const location = useLocation()
    const [modalOpen, toggleModal] = useState(false)
    const [selectedAgence, setSelectedAgence] = useState(null)

    const handleConfirmModal = (e) => {
        e.preventDefault()
        let params = new URLSearchParams(location.search)
        params.set("agence_id", selectedAgence.id)
        navigate(location.pathname + "?" + params)
    }

    const handleCloseModal = () => {
        toggleModal(false)
        if(selectedAgence) {
            setSelectedAgence(null)
        }
        else if(closeModal) 
            closeModal()
    }
    return <div>
        <div className='modal'>
            <div>
                <div style={{minHeight: 300, height: 300, maxHeight: 300}}>
                    <InputAgence required currentSelect={selectedAgence} setCurrentSelect={setSelectedAgence}/>
                </div>
                <div className='form-button-container'>
                    {
                        selectedAgence &&
                        <button onClick={handleConfirmModal} className='primary'>Confirmer</button>
                    }
                    <button onClick={handleCloseModal}>Annuler</button>
                </div>
            </div>
        </div>
    </div>;
}