drop trigger IF EXISTS before_update_absence;


DELIMITER |
CREATE TRIGGER before_update_absence
BEFORE UPDATE
ON absences FOR EACH ROW
BEGIN
    if(COALESCE(NEW.site_id, 0) != COALESCE(OLD.site_id, 0) 
          or COALESCE(NEW.motif, '') != COALESCE(OLD.motif, '') 
          or COALESCE(NEW.employe_id, 0) != COALESCE(OLD.employe_id, 0)
          or NEW.depart != OLD.depart 
          or NEW.retour != OLD.retour
          or NEW.status != OLD.status
          or COALESCE(NEW.superviseur_id, 0) != COALESCE(OLD.superviseur_id, 0)
    ) then
          begin
     	     set NEW.admin_updated_at = now();
          end;
     end if;
END
| DELIMITER ;