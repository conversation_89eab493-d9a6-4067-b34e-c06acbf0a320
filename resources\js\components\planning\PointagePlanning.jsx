import moment from 'moment'
import React, { useEffect, useState } from 'react'
import matricule from '../util/matricule'
import { upperCase } from 'lodash'
import { MdModeComment } from 'react-icons/md'

export default function PointagePlanning({ pointages, planning }) {
    const [groupPointages, setGroupPointages] = useState([])
    const [horaire, setHoraire] = useState({ 
        day: [
            planning.day_1 ?? 0,
            planning.day_2 ?? 0,
            planning.day_3 ?? 0,
            planning.day_4 ?? 0,
            planning.day_5 ?? 0,
            planning.day_6 ?? 0,
            planning.day_0 ?? 0,
            planning.day_ferie ?? 0
        ], 
        night: [
            planning.night_1 ?? 0,
            planning.night_2 ?? 0,
            planning.night_3 ?? 0,
            planning.night_4 ?? 0,
            planning.night_5 ?? 0,
            planning.night_6 ?? 0,
            planning.night_0 ?? 0,
            planning.night_ferie ?? 0
        ] })

        const reformatPointages = () => {
        if (pointages?.length > 0) {
            const pointagePlanning = []
            pointages.forEach(pointage => {
                const date = moment(pointage.date_pointage, 'YYYY-MM-DD HH:mm:ss');
                const existing = pointagePlanning.find((p) => p.date_pointage == date.format('YYYY-MM-DD HH:mm:ss'));
                if (existing) {
                    existing.employes.push({
                        id: pointage.agent_id,
                        matricule: matricule(pointage),
                        nom: pointage.nom,
                    })
                }
                else {
                    let curentPointage = {
                        id: pointage.id,
                        day: date.format('D'),
                        nom: date.format('ddd'),
                        date_pointage: date.format('YYYY-MM-DD HH:mm:ss'),
                        comment_content : pointage.comment_content,
                        employes: [{
                            id: pointage.agent_id,
                            matricule: matricule(pointage),
                            nom: pointage.nom,
                        }],
                    }
                    pointagePlanning.push(curentPointage)
                }
            })
            setGroupPointages(sortArray(pointagePlanning, 'date_pointage'))
        }
    }
    const sortArray = (array, key) => {
        return array.sort((a, b) => {
            if (a[key] < b[key]) return -1;
            if (a[key] > b[key]) return 1;
            return 0;
        });
    }

    const getEffectif = (currentPlanning) => {
        const dayIndex = moment(currentPlanning.date_pointage).isoWeekday() - 1
        const isDay = moment(currentPlanning.date_pointage).format("HH") == "06"
        const effectif = currentPlanning.employes.length
        const quota = isDay ? horaire.day[dayIndex] : horaire.night[dayIndex]
        if (effectif > quota) {
            return ('Surplus :' + (effectif - quota))
        }
        else if (effectif < quota) {
            return ('Manque :' + (quota - effectif))
        }
    }
    useEffect(() => reformatPointages(), [pointages])
    return (
        <div>
            {
                groupPointages?.map(p => 
                    <div key={p.id} className='line-container'>
                        <div className='space-between text'>
                            <h4 className='capitalize'>
                                {
                                    upperCase(moment(p.date_pointage).format("ddd DD MMM YYYY"))
                                    + " " + (moment(p.date_pointage).format("HH:mm:ss") == "18:00:00" ? " NUIT" : "JOUR")
                                }
                            </h4>
                            <div>
                                {
                                    getEffectif(p) &&
                                    <span className='badge-outline'>
                                        {getEffectif(p)}
                                    </span>
                                }
                                {
                                    p.comment_content &&
                                    <span style={{marginLeft:5}} title={p.comment_content}>
                                        <MdModeComment size={15} color='#073570'/>
                                    </span>
                                }
                            </div>

                        </div>
                        <div className="secondary">
                            {
                                p.employes.map(e => 
                                    <div key={e.id} style={{ padding : 5 }}>
                                        {e.id ? ("[" +e.matricule + "] " + e.nom) : ""}
                                    </div>
                                )
                            }
                            {/* {p.comment_content &&
                                <div style={{ borderLeft: "4px solid #888", marginLeft: 10, paddingLeft: 5, fontStyle: "italic" }}>
                                    {p.comment_content}
                                </div>
                            } */}
                        </div>
                    </div>
                )
            }
        </div>
    )
}
