import axios from 'axios'
import React, { useEffect, useState } from 'react';
import useToken from '../util/useToken';
import InputText from '../input/InputText';
import InputDate from '../input/InputDate';
import moment from 'moment';

export default function ArchiveEmployeModal({closeModal, updateData, employe}) {
    const [observation, setObservation] = useState("");
    const [dateSortie, setDateSortie] = useState(null);
    const [submitDisabled, setSubmitDisabled] = useState(true);

    const handleOk = () => {
        const data = {
            observation: observation,
            date_sortie: moment(dateSortie).format('YYYY-MM-DD')
        }
        axios.post("/api/employe/soft_delete/" + employe.id, data, useToken())
        .then(res => {
            updateData()
        })
    }
    
    useEffect(() => {
        setSubmitDisabled(!(observation && dateSortie))
    }, [observation, dateSortie]);
    
    return <div className='modal'>
        <div>
            <h2>Mise en archive</h2>
            <InputDate
                required
                label="Date de sortie"
                value={dateSortie} 
                onChange={setDateSortie}/>
            <InputText
                required
                type="text"
                label="Observation"
                value={observation}
                onChange={setObservation}/>
            <div className='form-button-container'>
                <button disabled={submitDisabled} className='btn-primary' onClick={handleOk}>Enregistrer</button>
                <button onClick={() => closeModal()}>Annuler</button>
            </div>
        </div>
    </div>;
}