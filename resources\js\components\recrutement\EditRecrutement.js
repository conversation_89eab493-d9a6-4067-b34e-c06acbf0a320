import React, { useEffect, useState } from 'react';
import {useParams, useLocation} from 'react-router-dom'
import axios from 'axios';
import moment from 'moment';
import "../layout/tab.css"

import Notification from '../notification/Notification';
import InputText from '../input/InputText';
import InputSelect from '../input/InputSelect';
import InputDate from '../input/InputDate';
import ButtonSubmit from '../input/ButtonSubmit';
import useToken from '../util/useToken';
import MapModal from '../modal/MapModal';
import InputMap from '../input/InputMap';
import DualContainer from '../container/DualContainer';
import InputSingleFile from '../input/InputSingleFile';

export default function EditRecrutement({auth, action, title}) {
        const params = useParams()
        const location = useLocation()
        const [notification, setNotification] = useState(null)
        const [disabledSubmit, setDisabledSubmit] = useState(false);
        const [error, setError] = useState("")
        const [nom, setNom] = useState("")
        const [cinText, setCinText] = useState("")
        const [dateNaiss, setDateNaiss] = useState()
        const [sexe, setSexe] = useState("")
        const [adresse, setAdresse] = useState("")
        const [coordonnees, setCoordonnees] = useState("")
        const [telephone, setTelephone] = useState("")
        const [email, setEmail] = useState("")
        const [taille, setTaille] = useState("")
        const [activeMenu, setActiveMenu] = useState("contact")
        const [francais, setFrancais] = useState("")
        const [anglais, setAnglais] = useState("")
        const [xp, setXp] = useState("")
        const sexes = ["Homme","Femme"]
        const [evaluation, setEvaluation] = useState([])
        const [experience, setExperience] = useState([])
        const [cin, setCin] = useState("")
        const [cv, setCv] = useState("")
        const [photo, setPhoto] = useState("")
        const [residence, setResidence] = useState("")
        const [reperage, setReperage] = useState("")
        const [bulletin, setBulletin] = useState("")
        const [bonneConduite, setBonneConduite] = useState("")
        const isDateNaiss = true
        const [showMapModal, toggleMapModal] = useState(false)

        useEffect(() => {
            getGrilles()
        }, [])

        useEffect(() => {
            let isMounted = true
            if(params.id){
                axios.get('/api/recrutement/show_detail/' + params.id, useToken())
                .then((res) => {
                    if(isMounted){
                        const recrutement = res.data
                        setNom(recrutement.nom)
                        setCinText(recrutement.cin_text)
                        setDateNaiss(recrutement.date_naiss ? new Date(recrutement.date_naiss) : null)
                        setSexe(sexes.find(sexe => sexe == recrutement.sexe))
                        setAdresse(recrutement.adresse)
                        let mergedCoordonnees
                        if (recrutement.latitude && recrutement.longitude) {
                            mergedCoordonnees = `${recrutement.latitude},${recrutement.longitude}`
                        } else {
                            mergedCoordonnees = null
                        }
                        setCoordonnees(mergedCoordonnees)
                        setTelephone(recrutement.telephone)
                        setEmail(recrutement.email ? recrutement.email : "")
                        setTaille(recrutement.taille)
                        setFrancais(recrutement.francais)
                        setAnglais(recrutement.anglais)
                        setXp(recrutement.xp)
                        setCin(recrutement.cin)
                        setCv(recrutement.cv)
                        setPhoto(recrutement.photo)
                        setResidence(recrutement.residence)
                        setReperage(recrutement.plan_reperage)
                        setBulletin(recrutement.bulletin_n3)
                        setBonneConduite(recrutement.bonne_conduite)
                    }
                })
            }
            return () => { isMounted = false };
        }, [])

        const handleSubmit = (e) => {
            e.preventDefault()
            setError("")
            setDisabledSubmit(true)
            const formData = new FormData();
            formData.append('nom', nom || "");
            formData.append('cin_text', cinText || "");
            formData.append('date_naiss', dateNaiss ? moment(dateNaiss).format('YYYY-MM-DD') : "");
            formData.append('sexe', sexe || "");
            formData.append('adresse', adresse || "");
            formData.append('latitude', coordonnees ? coordonnees.split(",")[0] : "");
            formData.append('longitude', coordonnees ? coordonnees.split(",")[1] : "");
            formData.append('telephone', telephone || "");
            formData.append('email', email || "");
            formData.append('francais', francais || "");
            formData.append('anglais', anglais || "");
            formData.append('xp', xp || "");
            formData.append('taille', taille || "");
            if (cin) formData.append('cin', cin);
            if (cv) formData.append('cv', cv);
            if (photo) formData.append('photo', photo);
            if (residence) formData.append('residence', residence);
            if (reperage) formData.append('reperage', reperage);
            if (bulletin) formData.append('bulletin', bulletin);
            if (bonneConduite) formData.append('bonne_conduite', bonneConduite);
            axios.post(action + (params.id ? params.id : ""), formData, useToken()) 
            .then((res) => {
                setDisabledSubmit(false)
                if (res.data.success) {
                    setNotification({
                        id: res.data.id,
                        message: "Recrutement bien enregistré"
                    })
                } else if (res.data.error == "EACCES") {
                    setError("Une erreur est survenue.")
                } else if (res.data.error) {
                    setError(res.data.error)
                }
            })
            .catch((e) => {
                console.error(e)
            })
        }

        const getGrilles = () => {
            axios.get('/api/recrutement/getGrille', useToken())
            .then((res) => {
                const grilles = res.data.grilles
                setEvaluation(grilles.filter(grille => grille.pour === "langue").map(grille => grille.description))
                setExperience(grilles.filter(grille => grille.pour === "gardiennage").map(grille => grille.description))
            })
            .catch((e) => {
                console.error(e)
            })
        }
        
        const handleNiveauFrancais = (evaluation) => {
            setFrancais(evaluation)
        }
        
        const handleNiveauAnglais = (evaluation) => {
            setAnglais(evaluation)
        }
        
        const handleExperience = (experience) => {
            setXp(experience)
        }
        
        const handleSexe = (sexes) => {
            setSexe(sexes)
        }

    return (
        <div id='content'>
            <div>
                {
                    notification ?
                        <Notification next={notification.id ? "/recrutement?id=" + notification.id : "/recrutement"} message={notification.message}/>
                    :
                    <form onSubmit={handleSubmit}>
                        <div className='title-container'>
                            <h2>{title}</h2>
                        </div>
                        <DualContainer>
                            <InputText
                                required
                                label="Nom"
                                value={nom} 
                                onChange={setNom}
                            />
                            <InputSelect 
                            // required
                                label="Sexe"
                                selected={sexe}
                                setSelected={handleSexe}
                                options={sexes}
                            />
                        </DualContainer>
                        <DualContainer>
                            <InputText
                                required
                                label="CIN"
                                value={cinText} 
                                onChange={setCinText}
                            />
                            <InputDate 
                                label="Date de Naissance"
                                // required
                                value={dateNaiss} 
                                onChange={setDateNaiss}
                                isDateNaiss={isDateNaiss}
                            />
                        </DualContainer>
                        <DualContainer>
                            <InputText
                                // required
                                label="Adresse"
                                value={adresse} 
                                onChange={setAdresse}
                            />
                            <InputMap
                                // required
                                label="Coordonnées"
                                value={coordonnees} 
                                onChange={setCoordonnees}
                            />
                        </DualContainer>
                        <div className='tab-container'>
                            <div className='tab-list'>
                                <div onClick={() => setActiveMenu('contact')} className={activeMenu == 'contact' ? 'active' : ''}>
                                    Contacts
                                </div>
                                <div onClick={() => setActiveMenu('compétences')} className={activeMenu == 'compétences' ? 'active' : ''}>
                                    Compétences
                                </div>
                                <div onClick={() => setActiveMenu('morphologie')} className={activeMenu == 'morphologie' ? 'active' : ''}>
                                    Morphologies
                                </div>
                                {
                                    !params.id &&
                                    <div onClick={() => setActiveMenu('documents')} className={activeMenu == 'documents' ? 'active' : ''}>
                                        Documents
                                    </div>
                                }
                            </div>
                            <div className='tab-content'>
                                {
                                    activeMenu == "contact" &&
                                    <div className='card-container'>
                                        <InputText
                                            required
                                            label="Téléphone"
                                            value={telephone} 
                                            onChange={setTelephone}
                                        />
                                        <InputText
                                            label="Email"
                                            value={email} 
                                            onChange={setEmail}
                                        />
                                    </div>
                                }
                                {
                                    activeMenu == "compétences" &&
                                    <div className='card-container'>
                                        <InputSelect 
                                            // required
                                            label="Français"
                                            selected={francais}
                                            setSelected={handleNiveauFrancais}
                                            options={evaluation}
                                        />
                                        <InputSelect 
                                            // required
                                            label="Anglais"
                                            selected={anglais}
                                            setSelected={handleNiveauAnglais}
                                            options={evaluation}
                                        />
                                        <InputSelect 
                                            // required
                                            label="XP en gardiennage"
                                            selected={xp}
                                            setSelected={handleExperience}
                                            options={experience}
                                        />
                                    </div>
                                }
                                {
                                    activeMenu == "morphologie" &&
                                    <div className='card-container'>
                                        <InputText
                                            // required
                                            type="number"
                                            label="Taille (cm)"
                                            value={taille} 
                                            onChange={setTaille}
                                        />
                                    </div>
                                }
                                {
                                    activeMenu == "documents" &&
                                    <div className='card-container'>
                                        <div className='space-between'>
                                            <span>CIN</span>
                                            <InputSingleFile file={cin} setFile={setCin}/>
                                        </div>
                                        <div className='space-between'>
                                            <span>CV</span>
                                            <InputSingleFile file={cv} setFile={setCv}/>
                                        </div>
                                        <div className='space-between'>
                                            <span>Photo</span>
                                            <InputSingleFile file={photo} setFile={setPhoto}/>
                                        </div>
                                        <div className='space-between'>
                                            <span>Résidence</span>
                                            <InputSingleFile file={residence} setFile={setResidence}/>
                                        </div>
                                        <div className='space-between'>
                                            <span>Plan de repérage</span>
                                            <InputSingleFile file={reperage} setFile={setReperage}/>
                                        </div>
                                        <div className='space-between'>
                                            <span>Bulletin N3</span>
                                            <InputSingleFile file={bulletin} setFile={setBulletin}/>
                                        </div>
                                        <div className='space-between'>
                                            <span>Bonne conduite</span>
                                            <InputSingleFile file={bonneConduite} setFile={setBonneConduite}/>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                        {
                            error &&
                            <div className='container-error'>
                                {error}
                            </div>
                        }
                        <ButtonSubmit label="Enregistrer"/>
                    </form>
                }
            </div>
            {
                showMapModal &&
                <MapModal
                    closeModal={() => toggleMapModal(false)}
                />
            }
        </div>
    )
}