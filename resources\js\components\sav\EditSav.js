import React, { useEffect, useState } from 'react';
import {useParams} from 'react-router-dom'
import axios from 'axios';

import ButtonSubmit from '../input/ButtonSubmit';
import InputText from '../input/InputText';
import Notification from '../notification/Notification';
import useToken from '../util/useToken';
import moment from 'moment';
import LastSav from './LastSav';
import Textarea from '../input/Textarea';
import InputUser from '../input/InputUser';
import InputSite from '../input/InputSite';
import InputDateTime from '../input/InputDateTime';
import LoadingScreen from '../loading/LoadingScreen';

export default function EditSav({auth, action}) {
    const params = useParams()
    const [currentType, setCurrentType] = useState()
    const [isLoading, toggleLoading] = useState(false)
    const [sav, setSav] = useState(null)
    const [site, setSite] = useState(null)
    const [lastSav, setLastSav] = useState(null)
    const [motif, setMotif] = useState("")
    const [mesure, setMersure] = useState("")
    const [dateSav, setDateSav] = useState(null)
    const [technicien, setTechnicien] = useState("")
    const [superviseur, setSuperviseur] = useState(null)
    const [notification, setNotification] = useState(null)
    const [error, setError] = useState("")
    const [submitDisabled, disableSubmit] = useState(false)

    const handleSubmit = (e) => {
        e.preventDefault()
        setError("")
        disableSubmit(true)
        const data = {
            type_sav: currentType ? currentType.name : '',
            site_id: site ? site.id : '',
            motif: motif,
            mesure: mesure,
            date_sav: dateSav ? moment(dateSav).format("YYYY-MM-DD HH:mm:ss") : '',
            technicien: technicien,
            superviseur_id: superviseur ? superviseur.id : '',
        }
        axios.post(action + (params.id ? "/" + params.id : ""), data, useToken())
        .then((res) => {
            disableSubmit(false)
            if(res.data.success)
                setNotification(res.data)
            else if(res.data.error == "EACCES")
                setError("Une erreur est survenue.")
            else if(res.data.error)
                setError(res.data.error)
        })
        .catch((e) => {
            console.error(e)
            setError("Erreur d'envoie, réessayez.")
            disableSubmit(false)
        })
    }

    useEffect(() => {
        let isMounted = true
        if(params.type){
            toggleLoading(true)
            axios.get('/api/type_sav/show/' + params.type, useToken())
            .then((res) => {
                if(isMounted){
                    setCurrentType(res.data)
                }
                toggleLoading(false)
            })
            .catch((e) => console.error(e))
        }
        return () => { isMounted = false };
    }, [params.type])
    
    //Last SAV
    useEffect(() => {
        let isMounted = true
        if(site){
            setLastSav(null)
            axios.get("/api/last_sav/" + site.id, useToken())
            .then((res) => {
                if(isMounted) setLastSav(res.data)
            })
            
        }
        return () => {isMounted = false}
    }, [site])

    useEffect(() => {
        let isMounted = true
        if(params.id){
            axios.get('/api/sav/show/' + params.id, useToken())
            .then((res) => {
                if(isMounted){
                    const sav = res.data
                    setSav(sav)
                    if(sav.motif) setMotif(sav.motif)
                    if(sav.type) setCurrentType(sav.type)
                    if(sav.site) setSite({
                        id: sav.site_id,
                        nom: sav.site
                    })
                    else 
                        setLastSav([])
                    if(sav.sup_nom) setSuperviseur({
                        id: sav.superviseur_id,
                        name: sav.sup_nom,
                        email: sav.sup_email,
                    })
                    if(sav.date_sav) setDateSav(moment(sav.date_sav).toDate())
                    if(sav.technicien) setTechnicien(sav.technicien)
                }
            })
        }
        else
            setLastSav([])
        return () => { isMounted = false };
    }, [])

    return (
        <div id="content">
            {
                isLoading ?
                    <LoadingScreen/>
                : notification ? 
                    <Notification next={notification.id ? "/sav/"+ currentType.name +"?id=" + notification.id : "/"} message={notification.success}/>
                :
                <div>
                    <div className="title-container">
                        <h2>
                            {
                                "SAV" + ((currentType && (currentType.name == 'tag' || currentType.name == 'biometrique')) ? " - " + currentType.description : '')
                            }
                        </h2>
                    </div>
                    {
                        lastSav === null ?
                            <LoadingScreen/>
                        : <div>
                            {
                                lastSav.length > 0 &&
                                <div className='header-container'>
                                    <h3>Dernières SAV sur le site</h3>
                                    <div>
                                        <span className='text'>
                                            {site.nom}
                                        </span>
                                    </div>
                                </div>
                            }
                            {
                                lastSav.map((sav,index) => (
                                    <LastSav key={index} sav={sav}/>
                                ))
                            }
                            {
                                lastSav.length > 0 &&
                                <div className="input-container-btn">
                                    <button onClick={() => setLastSav([])}>Continuer</button>
                                </div>
                            }
                            {
                                lastSav.length == 0 &&
                                <form onSubmit={handleSubmit}>
                                    {
                                        (sav && ['tech', 'electronique'].includes(auth.role) && auth.id != sav.user_id) &&
                                        <div>
                                            <div className="card-container">
                                                <h3>
                                                    {sav.site}
                                                </h3>
                                                <div>
                                                    Site: <span className='text'>{sav.site}</span>
                                                </div>
                                                <p>
                                                    Motif: <span className='text'>{sav.motif}</span>
                                                </p>
                                                <div>
                                                    Superviseur responsable: <span className='text'> 
                                                        {sav.user_nom} {' <' + sav.user_email + '>'}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    {
                                        (!params.id || (sav && auth.id == sav.user_id)) &&
                                        <div>
                                            <InputSite
                                                withoutDelete
                                                required
                                                value={site} 
                                                onChange={setSite}/>
                                            <Textarea
                                                required
                                                label="Motif"
                                                value={motif}
                                                onChange={setMotif}/>
                                            {
                                                auth.role == 'room' && 
                                                <Textarea
                                                    required
                                                    label="Mesure prise"
                                                    value={mesure}
                                                    onChange={setMersure}/>
                                            }
                                        </div>
                                    }
                                    {
                                        ['tech', 'electronique'].includes(auth.role) && 
                                        <div>
                                            <InputDateTime
                                                required
                                                label="Date prévu"
                                                value={dateSav}
                                                onChange={setDateSav}/>
                                            <InputText 
                                                required
                                                label="Technicien"
                                                value={technicien} 
                                                onChange={setTechnicien}/>
                                        </div>
                                    }
                                    {
                                        (['tech', 'electronique', 'room'].includes(auth.role) && (!sav || sav.superviseur_id != sav.user_id)) &&
                                        <InputUser
                                            role="superviseur"
                                            value={superviseur} 
                                            onChange={setSuperviseur}/>
                                    }
                                    {
                                        error &&
                                        <div className='container-error'>
                                            {error}
                                        </div>
                                    }
                                    <ButtonSubmit disabled={submitDisabled}/>
                                </form>
                            }
                        </div>
                    }
                </div>
            }
        </div>
    )
}