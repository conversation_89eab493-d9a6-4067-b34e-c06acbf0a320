import axios from 'axios';
import React, { useEffect, useState } from 'react'
import { useLocation, Link, useNavigate } from 'react-router-dom';
import useToken from '../util/useToken';
import LoadingPage from '../loading/LoadingPage';
import SearchBar from '../input/SearchBar';
import InfiniteScroll from 'react-infinite-scroll-component';
import moment from 'moment';
import { upperCase } from 'lodash';
import ModalExportPlanning from './ModalExportPlanning';
import StatusLabel from '../input/StatusLabel';
import './planning.css'
import './anomalie/anomalie.css';
import ModalChoiceAnomalie from './anomalie/ModalChoiceAnomalie';

export default function Planning({ auth, plannings, setPlannings, currentId ,setCurrentId }) {
    const [isLoading, toggleLoading] = useState(false)
    const [allDataLoaded, setDataLoaded] = useState(false)
    const [showExport, toggleShowExport] = useState(false)
    const [showTypeAnomalie, toggleShowTypeAnomalie] = useState(false)
    const [isPlanning, togglePlanning] = useState(true)
    const [typeAnomalie, setTypeAnomalie] = useState('anomalie_pointage_effectif')
    const locationSearch = useLocation().search;
    const hasSuperviseurId = (new URLSearchParams(locationSearch)).get("superviseur_id")
    const hasManagerId = (new URLSearchParams(locationSearch)).get("resp_sup_id")
    const isUnfinished = (new URLSearchParams(locationSearch)).get("un_finished")

const navigate = useNavigate();
    const [searchParams, setSearchParams] = useState(new URLSearchParams(window.location.search));
    
    let searchItems = [
        { label: "Reférence", name: "id", type: "number" },
        { label: 'Site', name: 'site_id', type: 'number' },
        { label: 'Date de planning', name: 'date_planning', type: 'dateMonth' },
        { label: 'Utilisateur', name: 'user_id', type: 'number' },
        { label: 'Non lu', name: 'unread', type: 'string' },
    ]

    if(["resp_op", "validateur"].includes(auth.role)){
        searchItems = searchItems.concat([
            { label: 'Manager', name:'resp_sup_id', type: 'number' },
        ])
    }
    if(["resp_sup", "resp_op"].includes(auth.role)){
        searchItems = searchItems.concat([
            { label: 'Non lu par le superviseur', name: 'unread_sup', type: 'string' }
        ])
    }

    let searchItemsAnomalieEffectif = [
        { label: 'Site', name: 'site_id', type: 'number' },
        { label: 'Type', name: 'type_anomalie', type: 'select' },
        { label: "Date de Service", name: "date_service", type:"date_service" },
    ]

    const updateData = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true);
            params.set("offset", 0)
        }
        else
            params.set("offset", plannings.length)
        axios.get('/api/planning?' + params, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.plannings) {
                        if (initial)
                            setPlannings(res.data.plannings)
                        else {
                            const list = plannings.slice().concat(res.data.plannings)
                            setPlannings(list)
                        }
                        setDataLoaded(res.data.plannings.length < 30)
                    }
                    toggleLoading(false);
                }
            })
            .catch((e) => {
                console.error(e)
            })
        return () => { isMounted = false; }
    }

    function getCurrentService() {
        const now = moment();
        let serviceDate;
        if (now.hour() >= 18)
            serviceDate = now.set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        else if (now.hour() >= 6)
            serviceDate = now.set({ hour: 6, minute: 0, second: 0, millisecond: 0 });
        else 
            serviceDate = now.subtract(1, 'days').set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        return serviceDate.format('YYYY-MM-DD HH:mm:ss');
    }

    const updateAnomalieEffectif = (initial) => {
        let isMounted = true;
        const params = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true)
            params.set("offset", 0)
        }
        else
            params.set("offset", plannings.length)
        // params.set("date_service", moment().format('YYYY-MM-DD') + ' 07:00:00')
        axios.get('/api/anomalie_he?' + params, useToken())
            .then((res) => {
                if (isMounted) {
                    if (initial) {
                        setPlannings(res.data.anomalies)
                    }
                    else{
                        const list = plannings.slice().concat(res.data.anomalies)
                        setPlannings(list)
                    }
                    setDataLoaded(res.data.anomalies.length < 30)
                    toggleLoading(false);
                }
            })
            .catch((e) => {
                console.error(e)
            })
        return () => { isMounted = false; }
    }

    const updateAnomaliePlanning = (initial) => {
        let isMounted = true;
        const urlParams = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            urlParams.set("offset", 0)
        }
        else
            urlParams.set("offset", plannings.length)
        axios.get("/api/anomalie?" + urlParams, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.error)
                        console.error(res.data.error)
                    else {
                        if (initial) {
                            setPlannings(res.data.anomalies)
                        }
                        else {
                            const list = plannings.slice().concat(res.data.anomalies)
                            setPlannings(list)
                        }
                        setDataLoaded(res.data.anomalies.length < 30)
                    }
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e)
            })
        return () => { isMounted = false };
    }
    
    const updateEfectifPlanning = (initial) => {
        let isMounted = true;
        const urlParams = new URLSearchParams(locationSearch);
        if (initial) {
            toggleLoading(true)
            setDataLoaded(true)
            setCurrentId(null)
            urlParams.set("offset", 0)
        }
        else
            urlParams.set("offset", plannings.length)
        axios.get("/api/anomalie_pl_eff?" + urlParams, useToken())
            .then((res) => {
                if (isMounted) {
                    if (res.data.error)
                        console.error(res.data.error)
                    else {
                        if (initial) {
                            setPlannings(res.data.anomalies)
                        }
                        else {
                            const list = plannings.slice().concat(res.data.anomalies)
                            setPlannings(list)
                        }
                        setDataLoaded(res.data.anomalies.length < 30)
                    }
                    toggleLoading(false)
                }
            })
            .catch((e) => {
                console.error(e)
            })
        return () => { isMounted = false };
    }

    useEffect(() => {
        handleGetData(true)
    }, [locationSearch])

    useEffect(() => {
        setCurrentId(null)
        const urlParams = new URLSearchParams(window.location.search);
        if (!isPlanning) {
            urlParams.set('type-anomalie', typeAnomalie);
            if(!urlParams.get('date_service'))
                urlParams.set('date_service', getCurrentService())
            navigate(`?${urlParams.toString()}`, { replace: true });
        }
        else if(isPlanning){
            let hasAnomalie = urlParams.get('type-anomalie')
            if(hasAnomalie){
                urlParams.delete('type-anomalie');
                navigate(`?${urlParams.toString()}`, { replace: true });
            }
        }
    }, [typeAnomalie, isPlanning]);

    const fetchMoreData = () => {
        setTimeout(() =>{
            handleGetData()
        }, 300)
    }

    const handleGetData = (initial) => {
        const urlParams = new URLSearchParams(window.location.search);
        if(isPlanning){
            let hasAnomalie = urlParams.get('type-anomalie')
            if(hasAnomalie){
                urlParams.delete('type-anomalie');
                navigate(`?${urlParams.toString()}`, { replace: true });
            }
            updateData(initial)
        }
        else{
            urlParams.set('type-anomalie', typeAnomalie);
            navigate(`?${urlParams.toString()}`, { replace: true });
            if(typeAnomalie == 'anomalie_pointage_effectif') updateAnomalieEffectif(initial)
            else if(typeAnomalie == 'anomalie_planning') updateAnomaliePlanning(initial)
            else updateEfectifPlanning(initial)
        }
        
    }

    const closeChoiceModal = () => {
        toggleShowTypeAnomalie(false)
        togglePlanning(false)
    }

    return (
        <div>
            {
                isLoading ?
                    <LoadingPage /> 
                :
                    <div>
                        <div className="padding-container space-between">
                            <h2>{isPlanning ? "Planning" : 
                                typeAnomalie == 'anomalie_pointage_effectif' ?"Anomalie Effectif-Pointage" 
                                : typeAnomalie == 'anomalie_planning' ? "Anomalie Planning-Pointage" 
                                : typeAnomalie == 'anomalie_planning_effectif' ? "Anomalie Planning-effectif" 
                                : ""}
                            </h2>
                            {auth.role != "access" &&
                                <Link className="btn btn-primary" to="/planning/add">Nouveau</Link>
                            }
                        </div>
                        {showExport && isPlanning && <ModalExportPlanning auth={auth} closeModal={() => toggleShowExport(false)} />}
                        <SearchBar listItems={isPlanning ? searchItems : searchItemsAnomalieEffectif} />
                        {
                            plannings.length == 0?
                                <h3 className='center secondary'>Aucun données trouvé</h3>
                            :
                                <div>
                                    <div className="action-container">
                                        {isPlanning && <span style={{ paddingLeft: 10 }} onClick={() => toggleShowExport(true)}>Exporter</span>}
                                        {
                                            !isPlanning  && <span onClick={() => togglePlanning(true)}>Planning</span>
                                        }
                                        <span onClick={() => toggleShowTypeAnomalie(true)}>Anomalie</span>
                                    </div>
                                        {isPlanning ? 
                                            <>
                                                {   
                                                    isUnfinished ?
                                                        <>
                                                            {
                                                                !hasManagerId ?
                                                                    <div>
                                                                        <div className="line-container">
                                                                            <div className="row-list">
                                                                                <b className="line-cell-md">Manager</b>
                                                                                <b className="line-cell-sm">Nb</b>
                                                                                <b>Email</b>
                                                                            </div>
                                                                        </div>
                                                                        {
                                                                            plannings.map((pln, index) => (
                                                                                <div className="line-container" key={index}>
                                                                                    <Link className='link-no-style' to={"/planning" + (locationSearch? (locationSearch + '&'):'?') +("resp_sup_id=" + pln.resp_sup_id) }>
                                                                                        <div className={"row-list " + (pln.resp_sup_id ? "" : "danger")}>
                                                                                            <span className="line-cell-md"> {pln.resp_sup_id ? pln.resp_sup : "Manager non defini"} </span>
                                                                                            <span className="line-cell-sm"> {pln.nb_planning} </span>
                                                                                            <span> {pln.resp_sup_email} </span>
                                                                                        </div>
                                                                                    </Link>
                                                                                </div>
                                                                            ))
                                                                        }
                                                                    </div>
                                                                : 
                                                                    <InfiniteScroll
                                                                        dataLength={plannings.length}
                                                                        next={fetchMoreData}
                                                                        hasMore={!allDataLoaded}
                                                                        loader={<LoadingPage />}
                                                                    >
                                                                        <div className="line-container">
                                                                            <div className="row-list">
                                                                                <b className="line-cell-sm">Date</b>
                                                                                <b className="line-cell-sm">H.Facturés</b>
                                                                                <b className="">Site</b>
                                                                            </div>
                                                                        </div>
                                                                        {
                                                                            plannings.map((pln, index) => {
                                                                                return (
                                                                                    <div onClick={() => setCurrentId(pln.id)}
                                                                                        className={`line-container ${currentId && currentId == pln.id ? "selected" : ""}`}
                                                                                        key={index}
                                                                                    >
                                                                                        <div className="row-list">
                                                                                            <span className="line-cell-sm">{upperCase(moment(pln.date_planning).format("MMM YYYY"))}</span>
                                                                                            <span className="line-cell-sm">{pln.total_hour}</span>
                                                                                            <span className="">{pln.site}</span>
                                                                                        </div>
                                                                                    </div>
                                                                                )
                                                                            })
                                                                        }
                                                                    </InfiniteScroll>
                                                            }
                                                        </>
                                                    :
                                                        <>
                                                            {
                                                                (hasManagerId || ['superviseur', 'resp_sup'].includes(auth.role)) ?
                                                                    <InfiniteScroll
                                                                        dataLength={plannings.length}
                                                                        next={fetchMoreData}
                                                                        hasMore={!allDataLoaded}
                                                                        loader={<LoadingPage />}
                                                                    >
                                                                        <div className="line-container">
                                                                            <div className="row-list">
                                                                                <b className="line-cell-sm">Date</b>
                                                                                <b className="status-line"><StatusLabel color="grey"/></b>
                                                                                <b className="line-cell-sm">H.Facturés</b>
                                                                                <b className="line-cell-sm">H. Totales</b>
                                                                                <b className="line-cell-md">Site</b>
                                                                            </div>
                                                                        </div>
                                                                        {
                                                                            plannings.map((pln, index) => {
                                                                                return (
                                                                                    <div onClick={() => setCurrentId(pln.id)}
                                                                                        className={`line-container ${currentId && currentId == pln.id ? "selected" : ""}`}
                                                                                        key={index}
                                                                                    >
                                                                                        <div className="row-list">
                                                                                            <span className="line-cell-sm">{upperCase(moment(pln.date_planning).format("MMM YYYY"))}</span>
                                                                                            <span className="status-line">
                                                                                                <StatusLabel color={pln.status_color} />
                                                                                            </span>
                                                                                            <span className="line-cell-sm">{pln.total_hour}</span>
                                                                                            <span className={`line-cell-sm ${((pln.number_ptg * 12) < (pln.total_hour - 12)) ? 'color-green' : ((pln.number_ptg * 12) > (pln.total_hour + 12)) ? 'color-pink' : ''}`}>
                                                                                                {pln.number_ptg * 12}
                                                                                            </span>
                                                                                            <span className="line-cell-lg">{pln.site}</span>
                                                                                        </div>
                                                                                    </div>
                                                                                )
                                                                            })
                                                                        }
                                                                    </InfiniteScroll>
                                                                :
                                                                    <>
                                                                        <div className="line-container">
                                                                            <div className="row-list">
                                                                                <b className="line-cell-md">Manager</b>
                                                                                <b className="line-cell-sm">Nb</b>
                                                                                <b>Email</b>
                                                                            </div>
                                                                        </div>
                                                                        {
                                                                            plannings.map((pln, index) => (
                                                                                <div className="line-container" key={index}>
                                                                                    <Link className='link-no-style' to={"/planning" + (locationSearch? (locationSearch + '&'):'?') +("resp_sup_id=" + pln.resp_sup_id) }>
                                                                                        <div className="row-list">
                                                                                            <span className="line-cell-md"> {pln.resp} </span>
                                                                                            <span className="line-cell-sm"> {pln.nb_planning} </span>
                                                                                            <span> {pln.resp_email} </span>
                                                                                        </div>
                                                                                    </Link>
                                                                                </div>
                                                                            ))
                                                                        }
                                                                    </>
                                                            }
                                                        </>
                                                }
                                            </>
                                        :
                                            <>
                                                <InfiniteScroll
                                                    dataLength={plannings.length}
                                                    next={fetchMoreData}
                                                    hasMore={!allDataLoaded}
                                                    loader={<LoadingPage />}
                                                >
                                                    <div className="line-container">
                                                        <div className="row-list">
                                                            <b className="line-cell-md">Site</b>
                                                            <b className="line-cell-sm">M/S</b>
                                                            <b className="">Manager</b>
                                                        </div>
                                                    </div>
                                                    { typeAnomalie == "anomalie_planning_effectif" ?
                                                        plannings.map((pln, index) => {
                                                            return (
                                                                <div onClick={() => setCurrentId(pln.idsite ?? pln.idsite_group)}
                                                                    className={`line-container ${currentId && currentId == pln.idsite ? "selected" : ""}`}
                                                                    key={index}
                                                                >
                                                                    <div className="row-list">
                                                                        <span className="line-cell-md">{pln.site}</span>
                                                                        <span className={'line-cell-sm ' + 
                                                                            (`${pln.current_effectif > pln.nb_ptg ? 'color-pink' :  pln.nb_ptg > pln.current_effectif ? 'color-purple' : ''}`)
                                                                        }>
                                                                            {(pln.current_effectif > pln.nb_ptg ? "Manque: " + (pln.current_effectif - pln.nb_ptg) 
                                                                                : pln.nb_ptg > pln.current_effectif ? "Surplus: " + (pln.nb_ptg - pln.current_effectif) : '')}
                                                                        </span>
                                                                        <span className="">
                                                                            {pln.resp_sup_id ? (pln.resp_sup + " <" + pln.resp_sup_email + ">") : ""}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            )
                                                        })
                                                    :
                                                        plannings.map((pln, index) => {
                                                            return (
                                                                <div onClick={() => setCurrentId(typeAnomalie == "anomalie_planning" ? pln.idsite_group : pln.idsite)}
                                                                    className={`line-container ${currentId && currentId == (typeAnomalie == "anomalie_planning" ? pln.idsite_group : pln.idsite) ? "selected" : ""}`}
                                                                    key={index}
                                                                >
                                                                    <div className="row-list">
                                                                        <span className="line-cell-md">{pln.site}</span>
                                                                        <span className={'line-cell-sm ' + 
                                                                            (typeAnomalie == 'anomalie_pointage_effectif' ? `${pln.current_effectif > pln.nb_ptg ? 'color-pink' :  pln.nb_ptg > pln.current_effectif ? 'color-purple' : ''}` 
                                                                                : `${pln.manque > 0 ? 'color-pink' : pln.surplus > 0 ? 'color-purple' : 'color-orange'}` )
                                                                        }>
                                                                            {typeAnomalie == 'anomalie_pointage_effectif' ? 
                                                                                (pln.current_effectif > pln.nb_ptg > 0 ? "Manque: " + (pln.current_effectif - pln.nb_ptg) 
                                                                                    : pln.nb_ptg > pln.current_effectif ? "Surplus: " + (pln.nb_ptg - pln.current_effectif) : '')
                                                                                : (pln.manque > 0 ? "Manque:" + pln.manque : pln.surplus > 0 ? "Surplus:" + pln.surplus : "Incoherence:" + pln.incoherence)}
                                                                        </span>
                                                                        <span className="">
                                                                            {pln.resp_sup_id ? (pln.resp_sup + " <" + pln.resp_sup_email + ">") : ""}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            )
                                                        })
                                                    }
                                                </InfiniteScroll>
                                            </>
                                        }
                                </div>
                                
                        }
                    </div>

            }
            {
                showTypeAnomalie && <ModalChoiceAnomalie closeModal={() => closeChoiceModal()} onChange={setTypeAnomalie}/>
            }
        </div>
    )
}
