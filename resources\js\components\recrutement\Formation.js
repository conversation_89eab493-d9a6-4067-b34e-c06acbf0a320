import React, { useEffect, useState } from 'react';
import moment from 'moment';
import LoadingPage from '../loading/LoadingPage';
import useToken from '../util/useToken';

export default function Formation({data}) {
    const [isLoading, toggleLoading] = useState(true)
    const [formations, setFormations] = useState([])

    useEffect(() => {
        let isMounted = true
        axios.get("/api/recrutement/formation/" + data.numero_stagiaire ,  useToken())
        .then((res) => {
            if(isMounted) {
                console.log(res.data.formations)
                setFormations(res.data.formations)
                toggleLoading(false)
            }
        })
        .catch((e) => {
            console.error(e)
        })
        return () => { isMounted = false};
    }, []);

    return <>
        {
            isLoading ?
                <LoadingPage/>
            :
                <div>
                    {
                        formations.map((f) => (
                            <div key={f.id} className='line-container'>
                                <div>
                                    {
                                        moment(f.date_pointage).format("DD MMM YYYY") 
                                        + " " + (moment(f.date_pointage).format("HH:mm:ss") == "07:00:00" ? "JOUR" : "NUIT")
                                    }
                                </div>
                                <div className='secondary'>
                                    {f.site}
                                </div>
                                <div className='secondary'>
                                    {
                                        f.manque &&
                                        f.manque.split(',').map(h => <><span key={h} className='badge-outline badge-outline-red'>{h}</span> {" "}</>)
                                    }
                                </div>
                            </div>
                        ))
                    }
                </div>
        }
    </>
}