import React from 'react';
import useToken from '../util/useToken';
import { Link } from 'react-router-dom';

export default function ActionFaitMarquant({auth, fait, updateData}) {

    const handleSeen = () => {
        axios.post('/api/seen/fait_marquant/' + fait.id , null, useToken())
        .then((res) => {
            if(res.data.success)
                updateData()
        })
        .catch((e) => {
            console.error(e)
        })
    }

    return <div>
        <div className='action-container'>
            {
                (auth.id != fait.user_id && !fait.seen) && 
                <span onClick={handleSeen}>Marquer comme lu</span>
            }
            {
                auth.role == "juridique" &&
                <span>
                    <Link className='link-no-style' 
                        to={"/plainte/add?fait_id=" + fait.id + "&site_id=" + fait.site_id} 
                        onClick={handleSeen}
                    >
                        Créer une plainte
                    </Link>
                </span>
                
            }
        </div>
    </div>
}