<?php

namespace App\Http\Controllers;

use App\Models\Employe;
use App\Models\Agence;
use App\Models\Fonction;
use App\Models\Immatriculation;
use App\Models\Service;
use App\Models\PieceJointe;
use App\Models\CriterePart;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Http\Controllers\HistoriqueController;
use App\Http\Controllers\RecrutementController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Util\PaieUtil;
use App\Models\CongePaye;
use App\Models\User;
use App\Modals\Reembauche;

class EmployeController extends Controller
{
    private static $attributeNames = array(
        'nom' => 'Nom',
        'societe_id' => 'Immatriculation',
        'fonction_id' => 'Fonction',
        'agence_id' => 'Agence',
        'date_embauche' => "Date d'embauche",
        'date_confirmation' => "Date de confirmation DGM",
        'date_conf_soit' => "Date de confirmation SOIT",
        'numero_employe' => "Num. employé DGM", 
        'num_emp_soit' => "Num. employé SOIT", 
        'numero_stagiaire' => "Num. stagiaire", 
        'nb_heure_contrat' => "Nb d'heure contrat",
        'sal_base' => "Salaire de base",
        'idm_depl' => "Indemnité de déplacement",
        'part_variable' => "Part variable",
        'perdiem' => "Perdiem",
        'service_id' => "Service",
        'titre' => "Titre",
        'categorie' => "Catégorie professionelle",
        'numero_compte' => 'Numéro de compte',
        'code_banque' => 'Code banque',
        'code_guichet' => 'Code guichet',
        'rib' => 'RIB',
        'numero_mobile' => 'Numéro mobile',
        'cin_text' => 'Numéro CIN',
        'responsable_id' => 'Responsable hiérarchique',
        // 'resp_user_id' => 'Responsable hiérarchique',
        'interim' => 'Visite par interim',
        'resp_part_id' => 'Responsable de part variable',
        'num_emp_saoi' => 'Num. employé SAOI',
        'date_conf_saoi' => 'Date de confirmation SAOI',
    );

    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
        app()->setLocale("fr");
    }

    public function getDayOrNightDate(){
        if(new \DateTime >= (new \DateTime)->setTime(06, 50, 0) &&
                new \DateTime < (new \DateTime)->setTime(17, 50, 0))
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if(new \DateTime < (new \DateTime)->setTime(06, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
    }

    public function auth(Request $request){
        return response()->json(Employe::find($request->user()->employe_id));
    }

    public function store(Request $request){
        if(in_array($request->user()->role, ['rh', "resp_rh", 'admin'])){
            $request->merge([
                'nom' => trim(preg_replace('!\s+!', ' ', $request->nom)),
            ]);
            $employe = new Employe();

            if ($request->doublon) {
                $validator = Validator::make($request->all(), [
                    'nom' => ['required'],
                    'societe_id' => ['required'],
                    'agence_id' => ['required'],
                    'fonction_id' => ['required'],
                    'sal_base' => ['required', 'integer'],
                    'categorie' => ['required'],
                    'numero_compte' => ['regex:/(^[0-9]{11}$)/u'],
                    'code_banque' => ['regex:/(^[0-9]{5}$)/u'],
                    'code_guichet' => ['regex:/(^[0-9]{5}$)/u'],
                    'rib' => ['regex:/(^[0-9]{2}$)/u'],
                    'numero_mobile' => ['regex:/(^03[0-9]{1} [0-9]{2} [0-9]{3} [0-9]{2}$)/u'],
                    'cin_text' => ['required', 'regex:/(^[0-9]{3} [0-9]{3} [0-9]{3} [0-9]{3}$)/u', Rule::unique('employes', 'cin_text')->ignore($employe->id)],
                ])->setAttributeNames(self::$attributeNames);
            } else {
                $validator = Validator::make($request->all(), [
                    'nom' => ['required', Rule::unique('employes')->where(function ($query) use ($request) {
                        return $query->where('nom', 'like', $request->nom . '%');
                    })],
                    'societe_id' => ['required'],
                    'agence_id' => ['required'],
                    'fonction_id' => ['required'],
                    'sal_base' => ['required', 'integer'],
                    'categorie' => ['required'],
                    'numero_compte' => ['regex:/(^[0-9]{11}$)/u'],
                    'code_banque' => ['regex:/(^[0-9]{5}$)/u'],
                    'code_guichet' => ['regex:/(^[0-9]{5}$)/u'],
                    'rib' => ['regex:/(^[0-9]{2}$)/u'],
                    'numero_mobile' => ['regex:/(^03[0-9]{1} [0-9]{2} [0-9]{3} [0-9]{2}$)/u'],
                    'cin_text' => ['required', 'regex:/(^[0-9]{3} [0-9]{3} [0-9]{3} [0-9]{3}$)/u', Rule::unique('employes', 'cin_text')->ignore($employe->id)],
                ])->setAttributeNames(self::$attributeNames);
            }
            
            
            if($validator->fails())
                return \response()->json(['error' => $validator->errors()->first()]);

            if (!$request->sal_forfait) {
                $validator = Validator::make($request->all(), [
                        'nb_heure_contrat' => ['required', 'integer'],
                    ])->setAttributeNames(self::$attributeNames);

                if ($validator->fails()) {
                    return \response()->json(['error' => $validator->errors()->first()]);
                }
            }

            if($request->part_variable == 1) {
                $validator = Validator::make($request->all(), [
                    'criteres' => ['required'],
                    'resp_part_id' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
                if($validator->fails())
                    return \response()->json(['error' => $validator->errors()->first()]);
            }

            if($request->interim == 1) {
                $validator = Validator::make($request->all(), [
                    'responsable_id' => ['required'],
                    // 'resp_user_id' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
                if($validator->fails())
                    return \response()->json(['error' => $validator->errors()->first()]);
            }

            if($request->fonction_id == 12) {
                $validator = Validator::make($request->all(), [
                    'service_id' => ['required'],
                    'titre' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
                if($validator->fails())
                    return \response()->json(['error' => $validator->errors()->first()]);
            }

            if($request->societe_id == 1){
                $validator = Validator::make($request->all(), [
                    'numero_employe' => ['required', 'integer', Rule::unique('employes')->where(function ($query) use ($request) {
                        return $query->where('societe_id', $request->societe_id)->where('numero_employe', $request->numero_employe);
                    })],
                    'numero_stagiaire' => ['integer'],
                    'num_emp_soit' => ['integer'],
                    'date_confirmation' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
            }
            else if($request->societe_id == 2){
                $validator = Validator::make($request->all(), [
                    'num_emp_soit' => ['required', 'integer', Rule::unique('employes')->where(function ($query) use ($request) {
                        return $query->where('societe_id', $request->societe_id)->where('num_emp_soit', $request->num_emp_soit);
                    })],
                    'numero_stagiaire' => ['integer'],
                    'numero_employe' => ['integer'],
                    'date_conf_soit' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
            }
            else if($request->societe_id == 3){
                $request->numero_employe = null;
                $request->num_emp_soit = null;
                $request->date_confirmation = null;
                $request->date_conf_saoi = null;
                $validator = Validator::make($request->all(), [
                    'numero_stagiaire' => ['required', 'integer', Rule::unique('employes')->where(function ($query) use ($request) {
                        return $query->where('societe_id', $request->societe_id)->where('numero_stagiaire', $request->numero_stagiaire);
                    })],
                    'date_embauche' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
            }
            else if($request->societe_id == 4){
                $request->numero_employe = null;
                $request->numero_stagiaire = null;
                $request->num_emp_soit = null;
                $request->date_embauche = null;
                $request->date_confirmation = null;
                $request->num_emp_saoi = null;
                $request->date_conf_saoi = null;
            }
            else if($request->societe_id == 5){
                $request->numero_employe = null;
                $request->numero_stagiaire = null;
                $request->num_emp_soit = null;
                $request->date_embauche = null;
                $request->date_confirmation = null;
                $request->num_emp_saoi = null;
                $request->date_conf_saoi = null;
            }
            else if ($request->societe_id == 6) {
                $validator = Validator::make($request->all(), [
                    'num_emp_saoi' => ['required', 'integer', Rule::unique('employes')->where(function ($query) use ($request) {
                        return $query->where('societe_id', $request->societe_id)->where('num_emp_saoi', $request->num_emp_saoi);
                    })],
                    'numero_stagiaire' => ['integer'],
                    'numero_employe' => ['integer'],
                    'date_conf_saoi' => ['required'],
                ])->setAttributeNames(self::$attributeNames);

            }
            if($request->responsable_id) {
                $responsable_id = $request->responsable_id;
                do {
                    $responsable = Employe::find($responsable_id);
                    if($responsable)
                        $responsable_id = $responsable->responsable_id;
                    else
                        $responsable_id = null;
                } while ($responsable_id != $request->responsable_id && $responsable_id != null);
                if($responsable_id == $request->responsable_id)
                    return \response()->json(['error' => "Erreur, hiérarchie en boucle"]);
            }

            // if ($request->resp_user_id) {
            //     $resp_user_id = $request->resp_user_id;
            //     do {
            //         $responsable = User::find($resp_user_id);
            //         if ($responsable->employe_id) {
            //             $emp_resp_user = Employe::find($responsable->employe_id);
            //             $resp_user_id = $emp_resp_user->resp_user_id;

            //         } else
            //         $resp_user_id = null;
            //     } while ($resp_user_id != $request->resp_user_id && $resp_user_id != null
            //     );
            //     if ($resp_user_id == $request->resp_user_id) {
            //         return \response()->json(['error' => "Erreur, hiérarchie en boucle"]);
            //     }
            // }

            if ($validator->fails()) {
                return \response()->json(['error' => $validator->errors()->first()]);
            }
            else {
                $employe->user_id = $request->authId;
                $employe->nom = $request->nom;
                if ($request->doublon) {
                    $employe->ignore_name = 1;
                }
                $employe->categorie = $request->categorie;
                $employe->fonction_id = $request->fonction_id;
                $employe->agence_id = $request->agence_id;
                $employe->responsable_id = $request->responsable_id;
                //$employe->resp_user_id = $request->responsable_id;
                // if ($request->user()->role == "resp_rh") {
                $employe->part_variable = $request->part_variable;
                $employe->resp_part_id = $request->resp_part_id;
                // }
                $employe->interim = $request->interim;
                $employe->sal_forfait = $request->sal_forfait;
                $employe->sal_base = $request->sal_base;
                $employe->nb_heure_contrat = $request->nb_heure_contrat;
                $employe->nb_heure_convenu = $request->nb_heure_convenu;
                $employe->societe_id = $request->societe_id;
                $employe->numero_employe = $request->numero_employe;
                $employe->num_emp_soit = $request->num_emp_soit;
                $employe->num_emp_saoi = $request->num_emp_saoi;
                $employe->numero_stagiaire = $request->numero_stagiaire;
                if($request->societe_id == 1){
                    $employe->date_embauche = $request->date_confirmation;
                    $employe->date_confirmation = $request->date_confirmation;
                }
                else if($request->societe_id == 2){
                    $employe->date_embauche = $request->date_conf_soit;
                    $employe->date_conf_soit = $request->date_conf_soit;
                }
                else if($request->societe_id == 3){
                    $employe->date_embauche = $request->date_embauche;
                }
                else if($request->societe_id == 6){
                    $employe->date_embauche = $request->date_conf_saoi;
                    $employe->date_conf_saoi = $request->date_conf_saoi;
                }
                if($request->fonction_id == 12) {
                    $employe->service_id = $request->service_id;
                    $employe->titre = $request->titre;
                }
                else {
                    $employe->service_id = 9;
                    $employe->titre = null;
                }
                $employe->mode_paiement = $request->mode_paiement;
                $employe->nom_paiement = $request->nom_paiement;
                $employe->numero_compte = $request->numero_compte;
                $employe->code_banque = $request->code_banque;
                $employe->code_guichet = $request->code_guichet;
                $employe->rib = $request->rib;
                $employe->numero_mobile = $request->numero_mobile;
                $employe->cin_text = $request->cin_text;

                $recrutement_id = DB::select("SELECT id FROM recrutements WHERE cin_text = ?", [$request->cin_text]);
                if (count($recrutement_id) > 0) {
                    $employe->recrutement_id = $recrutement_id[0]->id;
                }
                $employe->cin = $request->cin;
                $employe->cv = $request->cv;
                $employe->photo = $request->photo;
                $employe->residence = $request->residence;
                $employe->plan_reperage = $request->plan_reperage;
                $employe->bulletin_n3 = $request->bulletin_n3;
                $employe->bonne_conduite = $request->bonne_conduite;
                $employe->last_update = now();
                $employe->created_at = now();
                $employe->idm_depl = $request->idm_depl;
                $employe->perdiem = $request->perdiem;
                $employe->prime_anc = $request->prime_anc;           
                $employe->save();

                if($request->part_variable && in_array($request->user()->role, ["resp_rh", "rh"])){
                    $criteres = json_decode($request->criteres);
                    foreach ($criteres as $cr) {
                        if($cr->edition) {
                            if(!isset($cr->id)){
                                $critere = new CriterePart();
                                $critere->employe_id = $employe->id;
                                $critere->designation = $cr->designation;
                                $critere->montant = $cr->montant;
                                $critere->created_at = new \DateTime();
                                $critere->updated_at = new \DateTime();
                                if($critere->save()){
                                    HistoriqueController::new_critere_part($request, $critere);
                                };
                            }
                            else if($cr->soft_delete){
                                CriterePart::where('id', $cr->id)->update(['soft_delete' => 1]);
                                $critere = CriterePart::find($cr->id);
                                HistoriqueController::soft_delete_critere_part($request, $critere);
                            }
                            else{
                                $old_critere = CriterePart::find($cr->id);
                                CriterePart::where('id', $cr->id)->update(['designation' => $cr->designation, 'montant' => $cr->montant, 'updated_at' => new \DateTime()]);
                                $critere = CriterePart::find($cr->id);
                                HistoriqueController::update_critere_part($request, $old_critere, $critere, "Critère part variable modifié");
                            }
                        }
                    }
                }
                
                if (count($recrutement_id) > 0) {
                    RecrutementController::updateRecrutement($recrutement_id[0]->id, $employe->id);
                }

                if($request->isFromRecrutement) {
                    RecrutementController::updateRecrutement($request->recrutement_id, $employe->id);
                    $idsPj = json_decode($request->idsPj, true);
                    if(count($idsPj) > 0) {
                        foreach ($idsPj as $row) {
                            $pj_row = PieceJointe::find($row['id']);
                            if($pj_row) {
                                $pj_row->employe_id = $employe->id;
                                $pj_row->save();
                            }
                        } 
                    }
                }
                HistoriqueController::new_employe($request, $employe);
                return response(["success" => "Employé bien enregistré"]);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function update($id, Request $request){
        if(in_array($request->user()->role, ['rh', "resp_rh", 'admin'])){
            $request->merge([
                'nom' => trim(preg_replace('!\s+!', ' ', $request->nom)),
            ]);
            $employe = Employe::find($id);

            if(!in_array($request->societe_id, [1, 2, 3, 4, 5, 6]))
                $request->societe_id = null;
            if($employe->ignore_name)
                $validator = Validator::make($request->all(), [
                        'societe_id' => ['required'],
                        'agence_id' => ['required'],
                        'fonction_id' => ['required'],
                        'nb_heure_contrat' => ['required', 'integer'],
                        'sal_base' => ['required', 'integer'],
                        'categorie' => ['required'],
                    ])->setAttributeNames(self::$attributeNames);
            else
                $validator = Validator::make($request->all(), [
                        'nom' => ['required', Rule::unique('employes')->where(function ($query) use ($request, $id) {
                            return $query
                                ->where('nom', 'like', $request->nom . '%')
                                ->where('id', '<>', $id);
                        })],
                        'societe_id' => ['required'],
                        'agence_id' => ['required'],
                        'fonction_id' => ['required'],
                        'sal_base' => ['required', 'integer'],
                        'categorie' => ['required'],
                        'numero_compte' => ['regex:/(^[0-9]{11}$)/u'],
                        'code_banque' => ['regex:/(^[0-9]{5}$)/u'],
                        'code_guichet' => ['regex:/(^[0-9]{5}$)/u'],
                        'rib' => ['regex:/(^[0-9]{2}$)/u'],
                        'numero_mobile' => ['regex:/(^03[0-9]{1} [0-9]{2} [0-9]{3} [0-9]{2}$)/u'],
                        'cin_text' => ['required', 'regex:/(^[0-9]{3} [0-9]{3} [0-9]{3} [0-9]{3}$)/u', Rule::unique('employes', 'cin_text')->ignore($employe->id)],
                    ])->setAttributeNames(self::$attributeNames);

            if($validator->fails())
                return \response()->json(['error' => $validator->errors()->first()]);

            if (!$request->sal_forfait) {
                $validator = Validator::make($request->all(),[
                        'nb_heure_contrat' => ['required', 'integer'],
                    ])->setAttributeNames(self::$attributeNames);

                if ($validator->fails()){
                    return \response()->json(['error' => $validator->errors()->first()]);
                }
            }
            if($request->part_variable == 1) {
                $validator = Validator::make($request->all(), [
                    'criteres' => ['required'],
                    'resp_part_id' => ['required'],
                    // 'resp_part_user_id' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
                if($validator->fails())
                    return \response()->json(['error' => $validator->errors()->first()]); 
            }

            if($request->interim == 1) {
                $validator = Validator::make($request->all(), [
                    'responsable_id' => ['required'],
                    // 'resp_user_id' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
                if($validator->fails())
                    return \response()->json(['error' => $validator->errors()->first()]);
            }

            if($request->fonction_id == 12) {
                $validator = Validator::make($request->all(), [
                    'service_id' => ['required'],
                    'titre' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
                if($validator->fails())
                    return \response()->json(['error' => $validator->errors()->first()]);
            }

            if($request->societe_id == 1){
                $validator = Validator::make($request->all(), [
                    'numero_employe' => ['required', 'integer', Rule::unique('employes')->where(function ($query) use ($request, $id) {
                        return $query->where('societe_id', $request->societe_id)->where('numero_employe', $request->numero_employe)->where('id', '<>', $id);
                    })],
                    'numero_stagiaire' => ['integer'],
                    'num_emp_soit' => ['integer'],
                    'date_confirmation' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
            }
            else if($request->societe_id == 2){
                $validator = Validator::make($request->all(), [
                    'num_emp_soit' => ['required', 'integer', Rule::unique('employes')->where(function ($query) use ($request, $id) {
                        return $query->where('societe_id', $request->societe_id)->where('num_emp_soit', $request->num_emp_soit)->where('id', '<>', $id);
                    })],
                    'numero_stagiaire' => ['integer'],
                    'numero_employe' => ['integer'],
                    'date_conf_soit' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
            }
            else if($request->societe_id == 3){
                $request->numero_employe = null;
                $request->num_emp_soit = null;
                $request->date_confirmation = null;
                $request->date_conf_soit = null;
                $validator = Validator::make($request->all(), [
                    'numero_stagiaire' => ['required', 'integer', Rule::unique('employes')->where(function ($query) use ($request, $id) {
                        return $query->where('societe_id', $request->societe_id)->where('numero_stagiaire', $request->numero_stagiaire)->where('id', '<>', $id);
                    })],
                    'date_embauche' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
            }
            else if($request->societe_id == 4){
                $request->numero_employe = null;
                $request->numero_stagiaire = null;
                $request->num_emp_soit = null;
                $request->date_embauche = null;
                $request->date_confirmation = null;
                $request->date_conf_saoi = null;
            }
            else if($request->societe_id == 5){
                $request->numero_employe = null;
                $request->numero_stagiaire = null;
                $request->num_emp_soit = null;
                $request->num_emp_saoi = null;
                $request->date_embauche = null;
                $request->date_confirmation = null;
                $request->date_conf_saoi = null;
            }
            else if ($request->societe_id == 6) {
                $validator = Validator::make($request->all(), [
                    'num_emp_saoi' => ['required', 'integer', Rule::unique('employes')->where(function ($query) use ($request, $id) {
                        return $query->where('societe_id', $request->societe_id)->where('num_emp_saoi', $request->num_emp_saoi)->where('id', '<>', $id);
                    })],
                    'numero_stagiaire' => ['integer'],
                    'numero_employe' => ['integer'],
                    'date_conf_saoi' => ['required'],
                ])->setAttributeNames(self::$attributeNames);
                
            }
            if($request->responsable_id) {
                $responsable_id = $request->responsable_id;
                do {
                    $responsable = Employe::find($responsable_id);
                    if($responsable)
                        $responsable_id = $responsable->responsable_id;
                    else
                        $responsable_id = null;
                } while ($responsable_id != $request->responsable_id && $responsable_id != null);
                if($responsable_id == $request->responsable_id)
                    return \response()->json(['error' => "Erreur, hiérarchie en boucle"]);
            }
            // if ($request->resp_user_id) {
            //     $resp_user_id = $request->resp_user_id;
            //     do {
            //         $responsable = User::find($resp_user_id);
            //         if ($responsable->employe_id) {
            //             $emp_resp_user = Employe::find($responsable->employe_id);
            //             $resp_user_id = $emp_resp_user->resp_user_id;
            //             if ($emp_resp_user && $emp_resp_user->id == $id) {
            //                 $resp_user_id = $request->resp_user_id;
            //             }
            //         } else
            //             $resp_user_id = null;
            //     } while ($resp_user_id != $request->resp_user_id && $resp_user_id != null);
            //     if ($resp_user_id == $request->resp_user_id) {
            //         return \response()->json(['error' => "Erreur, hiérarchie en boucle"]);
            //     }
            // }
            if ($validator->fails()) {
                return \response()->json(['error' => $validator->errors()->first()]);
            }
            else {
                if(!$employe->ignore_name)
                    $employe->nom = $request->nom;
                if($request->fonction_id == 12) {
                    $employe->service_id = $request->service_id;
                    $employe->titre = $request->titre;
                }
                else {
                    $employe->service_id = 9;
                    $employe->titre = null;
                }
                HistoriqueController::update_employe($request, $employe, "Employé modifié");
                $employe->mode_paiement = $request->mode_paiement;
                $employe->nom_paiement = $request->nom_paiement;
                $employe->numero_compte = $request->numero_compte;
                $employe->code_banque = $request->code_banque;
                $employe->code_guichet = $request->code_guichet;
                $employe->rib = $request->rib;
                $employe->numero_mobile = $request->numero_mobile;
                $employe->cin_text = $request->cin_text;
                $employe->categorie = $request->categorie;
                $employe->fonction_id = $request->fonction_id;
                $employe->societe_id = $request->societe_id;
                $employe->agence_id = $request->agence_id;
                $employe->responsable_id = $request->responsable_id;
                // if ($request->user()->role == "resp_rh") {
                $employe->part_variable = $request->part_variable;
                $employe->resp_part_id = $request->resp_part_id;
                // }
                $employe->interim = $request->interim;
                $employe->sal_forfait = $request->sal_forfait;
                $employe->sal_base = $request->sal_base;
                $employe->nb_heure_contrat = $request->nb_heure_contrat;
                $employe->nb_heure_convenu = $request->nb_heure_convenu;
                $employe->numero_employe = $request->numero_employe;
                $employe->num_emp_soit = $request->num_emp_soit;
                $employe->numero_stagiaire = $request->numero_stagiaire;
                $employe->date_embauche = $request->date_embauche;
                $employe->date_confirmation = $request->date_confirmation;
                $employe->date_conf_soit = $request->date_conf_soit;
                $employe->cin = $request->cin;
                $employe->cv = $request->cv;
                $employe->photo = $request->photo;
                $employe->residence = $request->residence;
                $employe->plan_reperage = $request->plan_reperage;
                $employe->bulletin_n3 = $request->bulletin_n3;
                $employe->bonne_conduite = $request->bonne_conduite;
                $employe->last_update = now();
                $employe->idm_depl = $request->idm_depl;
                $employe->perdiem = $request->perdiem;
                $employe->prime_anc = $request->prime_anc;
                $employe->num_emp_saoi = $request->num_emp_saoi;
                $employe->date_conf_saoi = $request->date_conf_saoi;
                $employe->save();
                if (in_array($request->user()->role, ["resp_rh", "rh"])) {
                    if($request->part_variable){
                        $criteres = json_decode($request->criteres);
                        foreach ($criteres as $cr) {
                            if (isset($cr->edition)) {
                                if (!isset($cr->id)) {
                                    $critere = new CriterePart();
                                    $critere->employe_id = $employe->id;
                                    $critere->designation = $cr->designation;
                                    $critere->montant = $cr->montant;
                                    $critere->created_at = new \DateTime();
                                    $critere->updated_at = new \DateTime();
                                    if($critere->save()){
                                        HistoriqueController::new_critere_part($request, $critere);
                                    };
                                }
                                 else if(isset($cr->soft_delete)){
                                    CriterePart::where('id', $cr->id)->update(['soft_delete' => 1]);
                                    $critere = CriterePart::find($cr->id);
                                    HistoriqueController::soft_delete_critere_part($request, $critere);
                                }
                                else {
                                    $old_critere = CriterePart::find($cr->id);
                                    CriterePart::where('id', $cr->id)
                                        ->update(['designation' => $cr->designation, 'montant' => $cr->montant, 'updated_at' => new \DateTime()]);
                                    $critere = CriterePart::find($cr->id);
                                    HistoriqueController::update_critere_part($request, $old_critere, $critere, "Critère part variable modifié");
                                }
                            }
                        }
                    }
                     else 
                        CriterePart::where('employe_id', $employe->id)->update(['soft_delete' => 1]);
                }
                return response()->json(["success" => "Employé modifié"]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function change_real_site($employe_id, Request $request){
            return response()->json(Employe::where('id', $employe_id)->update(['real_site_id' => $request->site_id]));
        return response()->json(false);
    }
    public function last_sanction(Request $request, $id){
        $sanctions = DB::select("SELECT sanc.id, sanc.objet, sanc.motif, sanc.date_pointage, st.nom as 'site' 
            FROM sanctions sanc
            left join sites st on st.idsite = sanc.site_id
            WHERE sanc.status = 'done' and sanc.employe_id = ? 
            order by sanc.updated_at DESC LIMIT 5", [$id]);
        return response($sanctions);
    }
    public function last_prime(Request $request, $id){
        $primes = DB::select("SELECT pr.id, pr.objet, pr.motif, pr.date_pointage FROM primes pr
            WHERE pr.status = 'done' and pr.employe_id = ? 
            order by pr.date_pointage DESC LIMIT 3", [$id]);
        return response($primes);
    }
    public function conge(Request $request, $id){
        $employe = Employe::find($id);
        $conges = DB::select("SELECT depart, retour FROM absences ab 
            WHERE type_absence = 'conge' and ab.status = 'done' and ab.employe_id = ?", [$id]);
        // $congePayes = CongePaye::where('employe_id', $id)->sum('s_conge');
        return response()->json(compact('employe', 'conges'));
    }

    public function show($id){
        $employe = DB::select("SELECT e.id, f.has_hierarchie, e.nom, e.soft_delete, e.isBlacklist, societe_id, numero_stagiaire, numero_employe, num_emp_soit,
            num_emp_saoi, e.real_site_id, date_embauche, date_confirmation, date_conf_soit, date_conf_saoi, agence_id, fonction_id, service_id, titre, 
            e.real_site_id as 'site_id', s.nom as 'site', nb_heure_contrat, nb_heure_convenu, sal_base, idm_depl, e.part_variable, perdiem, prime_anc,
            cin, cv, photo, residence, plan_reperage, bulletin_n3, bonne_conduite, e.ignore_name,e.mode_paiement,e.nom_paiement, e.numero_mobile,
            e.code_banque,e.code_guichet,e.numero_compte, e.rib, e.resp_part_id, f.libelle as 'fonction', agc.nom as 'agence', e.sal_forfait,
            e.observation, e.date_sortie, e.cin_text, agc.nom as 'agence'
            FROM employes e
            LEFT JOIN fonctions f ON f.id = e.fonction_id
            LEFT JOIN agences agc ON agc.id = e.agence_id
            LEFT JOIN sites s ON s.idsite = e.real_site_id 
            WHERE e.id = ?",[$id])[0];
        $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
            FROM piece_jointes pj
            WHERE pj.employe_id = ?", [$id]);
        $resp_resp = null;
        if ($employe->resp_part_id) {
            $resp_resp = (Employe::select("resp_part_id")->where("id",$employe->resp_part_id)->first())->id;
        }
        $employe->resp_resp_id = $resp_resp;
        $employe->nb_pj = count($piece_jointes);
        if($employe->service_id)
            $employe->service = Service::find($employe->service_id);
        return response()->json($employe);
    }

    public function complement($id){
        $employe = DB::select("SELECT e.id, e.cin, e.cv, e.photo, e.residence, e.plan_reperage, e.bulletin_n3, e.bonne_conduite
            FROM employes e
            WHERE e.id = ?",[$id])[0];
        return response()->json($employe);
    }

    public function detail(Request $request, $id){
        $employe = DB::select("SELECT e.id, f.has_hierarchie, e.nom, e.soft_delete, e.isBlacklist, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit,
            e.num_emp_saoi, e.real_site_id, e.date_embauche, e.date_confirmation, e.date_conf_soit, e.date_conf_saoi, e.agence_id, e.fonction_id, e.service_id, e.titre, 
            e.real_site_id as 'site_id', s.nom as 'site', e.nb_heure_contrat, e.nb_heure_convenu, e.sal_base, e.idm_depl, e.part_variable, e.perdiem, e.prime_anc,
            e.cin, e.cv, e.photo, e.residence, e.plan_reperage, e.bulletin_n3, e.bonne_conduite, e.ignore_name, f.libelle as 'nom_fonction', e.sal_forfait,
            e.mode_paiement, e.nom_paiement, e.numero_mobile, e.numero_compte, e.code_banque, e.code_guichet, e.rib, e.cin_text,
            e.observation, e.date_sortie, e.categorie, e.responsable_id, e.interim, e.sal_forfait,
            e.resp_part_id, resp_part.nom as 'resp_part_variable', agc.nom as 'nom_agence', resp.nom as 'responsable'
            FROM employes e
            LEFT JOIN employes resp ON resp.id = e.responsable_id
            LEFT JOIN employes resp_part ON resp_part.id = e.resp_part_id
            LEFT JOIN fonctions f ON f.id = e.fonction_id
            LEFT JOIN agences agc ON agc.id = e.agence_id
            LEFT JOIN sites s ON s.idsite = e.real_site_id 
            WHERE e.id = ?",[$id])[0];
        $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path
            FROM piece_jointes pj
            WHERE pj.employe_id = ?", [$id]);
        $employe->nb_pj = count($piece_jointes);
        $employe->societe = Immatriculation::find($employe->societe_id);
        $employe->agence = Agence::find($employe->agence_id);
        $employe->fonction = Fonction::find($employe->fonction_id);
        if(in_array($request->user()->role ,['resp_rh', 'rh'])){
            $employe->criteres = CriterePart::where('employe_id', $id)->where(function ($q) {
                return $q->whereNull('soft_delete')->orWhere('soft_delete', 0);
            })->get();
        }
        if($employe->service_id)
            $employe->service = Service::find($employe->service_id);
        return response()->json($employe);
    }

    protected function searchIndex(Request $request){
        $searchArray = [];
        if($request->id)
            $searchArray[] = " e.id = " . $request->id . " ";
        else {
            if($request->actif)
                $searchArray[] = " (e.soft_delete is null or e.soft_delete = 0) ";
            if($request->archive)
                $searchArray[] = " (e.soft_delete = 1) ";
            if($request->sal_forfait)
                $searchArray[] = " (e.sal_forfait = 1) ";
            if($request->created_at)
                $searchArray[] = " e.created_at > '" . $request->created_at . " 00:00:00' and ". 
                    "e.created_at <= '" . $request->created_at . " 23:59:59' ";
            if($request->site_id)
                $searchArray[] = " e.site_id = " . $request->site_id . " ";
            if($request->nom)
                $searchArray[] = EmployeController::search_by_name('e.nom', $request->nom);
            if($request->cin_text)
                $searchArray[] = " e.cin_text = '" . $request->cin_text . "' ";
            if($request->matricule)
                $searchArray[] = " ( e.numero_stagiaire = " . $request->matricule . 
                    " or e.numero_employe = " . $request->matricule .
                    " or e.num_emp_soit = " . $request->matricule .
                    " or e.num_emp_saoi = " .$request->matricule .
                    ") ";
        }
        
        $query_where = "";
        $query_and = "";
        if(count($searchArray) > 0){
            $query_where = " WHERE " . implode(" and ", $searchArray) . " ";
            $query_and = " AND " . implode(" and ", $searchArray) . " " ;
        }
        $query_where = $query_where . " order by e.last_update desc limit ". $request->offset . ", 30";
        $query_and = $query_and . " order by e.last_update desc desc limit ". $request->offset . ", 30";
        return compact('query_where', 'query_and');
    }

    public function index(Request $request){
        $search = $this->searchIndex($request);
        $employes = DB::select("SELECT e.id, e.sal_forfait, e.nom, e.soft_delete, e.isBlacklist,  e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, 
            e.num_emp_saoi, e.real_site_id as 'site_id', s.nom as 'site', e.date_embauche, f.libelle as 'nom_fonction', agc.nom as 'nom_agence'
            FROM employes e
            LEFT JOIN fonctions f ON f.id = e.fonction_id
            LEFT JOIN agences agc ON agc.id = e.agence_id
            LEFT JOIN sites s ON s.idsite = e.real_site_id" . $search['query_where']
            , []);
        return response(compact('employes'));
    }
    
    public function sous_hierarchie(Request $request){
        $auth = $request->user();
        if ($auth->id == 9 || $auth->id == 10) {
            $employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                e.real_site_id as 'site_id', s.nom as 'site'
                FROM employes e
                LEFT JOIN sites s ON s.idsite = e.real_site_id
                LEFT JOIN fonctions f ON f.id = e.fonction_id
                WHERE (e.nom like ? or e.numero_stagiaire like ? or e.numero_employe like ? or e.num_emp_soit like ? or e.num_emp_saoi like ?)
                AND (e.soft_delete is null or e.soft_delete = 0)
                AND e.part_variable = 1 
                order by e.id desc LIMIT ?, 30", [
                '%' . $request->value . '%',
                $request->value,
                $request->value,
                $request->value,
                $request->value,
                $request->offset,
            ]);
        }
        else if ($auth->employe_id) {
            $ids = [$request->user()->employe_id];
            /*$resp_ids = array_column(DB::select("SELECT e.id FROM employes e WHERE e.resp_part_id in (" . implode(",", $ids) . ")", []), 'id');
            foreach ($resp_ids as $id){
                if(!in_array($id, $ids))
                    array_push($ids, $id);
            }
            $resp_ids = array_column(DB::select("SELECT e.id FROM employes e WHERE e.resp_part_id in (" . implode(",", $ids) . ")", []), 'id');
            foreach ($resp_ids as $id) {
                if(!in_array($id, $ids))
                    array_push($ids, $id);
            } */           
            $employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                e.real_site_id as 'site_id', s.nom as 'site'
                FROM employes e
                LEFT JOIN sites s ON s.idsite = e.real_site_id
                LEFT JOIN fonctions f ON f.id = e.fonction_id
                WHERE (e.nom like ? or e.numero_stagiaire like ? or e.numero_employe like ? or e.num_emp_soit like ? or e.num_emp_saoi like ?)
                AND (e.soft_delete is null or e.soft_delete = 0)
                AND e.part_variable = 1 AND e.resp_part_id = ?
                order by e.id desc LIMIT ?, 30", [
                    '%' . $request->value . '%',
                    $request->value,
                    $request->value,
                    $request->value,
                    $request->value,
                    $request->user()->employe_id,
                    $request->offset,
                ]);
        }
        else {
            return response(["error" => "Votre données employé n'est pas défini"]);
        }
        if($request->date_paie && count($employes) > 0) {
            $parts = DB::select("SELECT id, employe_id FROM part_variables 
                WHERE date_paie = ? AND employe_id in (". implode(',', array_column($employes, "id")) .")", [$request->date_paie]);
            foreach ($employes as $emp) {
                foreach ($parts as $pv) {
                    if($emp->id == $pv->employe_id)
                        $emp->danger = true;
                }
            }
        }
        return response(compact('employes'));
    }

    public static function search_by_name($column,$value){
        $search_term = '+' . implode(' +', explode(' ', $value));
        return "
            ( REPLACE($column, '  ', ' ') like REPLACE('%" . $value . "%' , '  ', ' ') 
                or (MATCH($column) AGAINST ( '$search_term' IN BOOLEAN MODE))
            )
        ";
    }

    public function sous_hierarchie_user(Request $request){
        $user_resp = User::find($request->employe_id);
        $employe_id = $user_resp->employe_id ?? null;
        if($request->user()->role != "validateur"){
            return response(["error" => "EACCES"]);
        }
        if ($employe_id) {
            $ids = [$employe_id];
            $resp_ids = array_column(DB::select("SELECT e.id FROM employes e WHERE e.resp_part_id in (" . implode(",", $ids) . ")", []), 'id');
            foreach ($resp_ids as $id) {
                if (!in_array($id, $ids))
                    array_push($ids, $id);
            }
            $resp_ids = array_column(DB::select("SELECT e.id FROM employes e WHERE e.resp_part_id in (" . implode(",", $ids) . ")", []), 'id');
            foreach ($resp_ids as $id) {
                if (!in_array($id, $ids))
                    array_push($ids, $id);
            }
            $condition_name = EmployeController::search_by_name('e.nom', $request->value);
            $employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                e.real_site_id as 'site_id', s.nom as 'site'
                FROM employes e
                LEFT JOIN sites s ON s.idsite = e.real_site_id
                LEFT JOIN fonctions f ON f.id = e.fonction_id
                WHERE ($condition_name or e.numero_stagiaire like ? or e.numero_employe like ? or e.num_emp_soit like ? or e.num_emp_saoi like ?)
                AND (e.soft_delete is null or e.soft_delete = 0)
                AND e.part_variable = 1 " . (count($ids) > 0 ? " AND e.resp_part_id in (" . implode(",", $ids) . ") " : "") .
                "order by e.id desc LIMIT ?, 30", [
                $request->value,
                $request->value,
                $request->value,
                $request->value,
                $request->offset,
            ]);
        } else {
            $employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
                e.real_site_id as 'site_id', s.nom as 'site'
                FROM employes e
                LEFT JOIN sites s ON s.idsite = e.real_site_id
                LEFT JOIN fonctions f ON f.id = e.fonction_id
                WHERE (e.nom like ? or e.numero_stagiaire like ? or e.numero_employe like ? or e.num_emp_soit like ? or e.num_emp_saoi like ?)
                AND (e.soft_delete is null or e.soft_delete = 0)
                AND e.part_variable = 1 " .
            "order by e.id desc LIMIT ?, 30", [
                '%' . $request->value . '%',
                $request->value,
                $request->value,
                $request->value,
                $request->value,
                $request->offset,
            ]);
        }
        
        if ($request->date_paie && count($employes) > 0) {
            $parts = DB::select("SELECT id, employe_id FROM part_variables 
                WHERE date_paie = ? AND employe_id in (" . implode(',', array_column($employes, "id")) . ")", [$request->date_paie]);
            foreach ($employes as $emp) {
                foreach ($parts as $pv) {
                    if ($emp->id == $pv->employe_id)
                        $emp->danger = true;
                }
            }
        }
        return response(compact('employes'));
    }

    public function get_employe_planning(Request $request){
        $auth = $request->user();
        $condition_name = EmployeController::search_by_name('e.nom', $request->value);
        $employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
            e.real_site_id as 'site_id', s.nom as 'site', e.soft_delete
            FROM employes e
            LEFT JOIN sites s ON s.idsite = e.real_site_id
            LEFT JOIN fonctions f ON f.id = e.fonction_id
            WHERE ($condition_name or e.numero_stagiaire like ? or e.numero_employe like ? or e.num_emp_soit like ? or e.num_emp_saoi like ?)
            order by e.id desc LIMIT ?, 30", [
            $request->value,
            $request->value,
            $request->value,
            $request->value,
            $request->offset
        ]);
        if(count($employes) > 0) {
            $plans = DB::select("SELECT ptg.id, ptg.agent_id, st.nom as 'site', st.idsite
                FROM planning_pointages ptg
                LEFT JOIN plannings pl ON pl.id = ptg.planning_id
                LEFT JOIN sites st ON st.idsite = pl.site_id
                WHERE ptg.date_pointage = ? AND ptg.agent_id in (". implode(',', array_column($employes, "id")) .")", [$request->date_pointage]);
            foreach ($employes as $emp) {
                foreach ($plans as $pl) {
                    if($emp->id == $pl->agent_id && $pl->idsite != $request->site_id){
                        $emp->danger = true;
                        $emp->site = $pl->site;
                    }
                }
            }
        }
        return response(compact('employes'));
    }

    public function interim(Request $request){
        $employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
            e.real_site_id as 'site_id', s.nom as 'site'
            FROM employes e
            LEFT JOIN sites s ON s.idsite = e.real_site_id
            WHERE e.interim = 1 and (e.nom like ? or e.numero_stagiaire like ? or e.numero_employe like ? or e.num_emp_soit like ? or e.num_emp_saoi like ?)
            AND (e.soft_delete is null or e.soft_delete = 0)
            AND e.responsable_id = ?
            order by e.id desc LIMIT ?, 30", [
                '%' . $request->value . '%',
                $request->value,
                $request->value,
                $request->value,
                $request->value,
                // $request->user()->id,
                $request->user()->employe_id,
                $request->offset,
            ]);
        return response(compact('employes'));
    }
    
    public function search(Request $request){
        $condition_name = EmployeController::search_by_name('e.nom', $request->value);
        $employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
            e.real_site_id as 'site_id', s.nom as 'site', f.libelle as 'fonction', e.soft_delete, e.titre
            FROM employes e
            LEFT JOIN sites s ON s.idsite = e.real_site_id
            LEFT JOIN fonctions f ON f.id = e.fonction_id
            WHERE ($condition_name or e.numero_stagiaire like ? or e.numero_employe like ? or e.num_emp_soit like ? or e.num_emp_saoi like ?)
            order by e.id desc LIMIT ?, 30", [
                $request->value,
                $request->value,
                $request->value,
                $request->value,
                $request->offset,
            ]);
        return response(compact('employes'));
    }

    public function search_without_delete(Request $request){
        $condition_name = EmployeController::search_by_name('e.nom', $request->value);
        $employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
            e.real_site_id as 'site_id', s.nom as 'site', f.libelle as 'fonction', e.soft_delete, e.titre
            FROM employes e
            LEFT JOIN sites s ON s.idsite = e.real_site_id
            LEFT JOIN fonctions f ON f.id = e.fonction_id
            WHERE ($condition_name or e.numero_stagiaire like ? or e.numero_employe like ? or e.num_emp_soit like ? or e.num_emp_saoi like ?)
            AND (e.soft_delete is NULL OR e.soft_delete = 0)
            order by e.id desc LIMIT ?, 30", [
                $request->value,
                $request->value,
                $request->value,
                $request->value,
                $request->offset,
            ]);
        return response(compact('employes'));
    }
    
    public function hierarchie_search(Request $request){
        $employes = DB::select("SELECT e.id, e.nom, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
            e.real_site_id as 'site_id', s.nom as 'site'
            FROM employes e
            LEFT JOIN fonctions f ON f.id = e.fonction_id
            LEFT JOIN sites s ON s.idsite = e.real_site_id
            WHERE (e.nom like ? or e.numero_stagiaire like ? or e.numero_employe like ? or e.num_emp_soit like ? or e.num_emp_saoi like ? )
            AND f.has_hierarchie = 1 AND (e.soft_delete is null or e.soft_delete = 0)
            order by e.id desc LIMIT ?, 30", [
                '%' . $request->value . '%',
                $request->value,
                $request->value,
                $request->value,
                $request->value,
                $request->offset,
            ]);
        return response(compact('employes'));
    }

    public function list_detail($id){
        $employe = DB::select("SELECT e.id, e.nom, e.soft_delete, e.isBlacklist societe_id, numero_stagiaire, numero_employe, num_emp_soit, num_emp_saoi,
            e.real_site_id as 'site_id', s.nom as 'site', date_embauche
            FROM employes e
            LEFT JOIN sites s ON s.idsite = e.real_site_id 
            WHERE e.id = ?",[$id])[0];
        $sanctions = DB::select("SELECT sanc.id, sanc.user_id, sanc.superviseur_id, sanc.site_id,
            sanc.status, sanc.created_at, sanc.objet, sanc.motif,
            stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site', 
            us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', coalesce(ur.email, sup.email) as 'sup_email'
            FROM sanctions sanc
            LEFT JOIN employes e on e.id = sanc.employe_id
            LEFT JOIN sites st on st.idsite = sanc.site_id
            LEFT JOIN users us on us.id = sanc.user_id
            LEFT JOIN users sup on sup.id = sanc.superviseur_id
            LEFT JOIN users ur on ur.id = sup.real_email_id
            LEFT JOIN `status` stat on stat.name = sanc.status
            WHERE sanc.employe_id = ?
            order by sanc.created_at DESC", [$id]);

        $primes = DB::select("SELECT pr.id, pr.user_id, pr.employe_id, pr.superviseur_id, pr.site_id, pr.pointage_id,
            pr.status, pr.created_at, pr.objet, pr.montant, pr.motif, pr.date_pointage,
            stat.description as 'status_description', stat.color as 'status_color',
            e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi,
            st.nom as 'site', us.name as 'user_nom', us.email as 'user_email', sup.name as 'sup_nom', sup.email as 'sup_email'
            FROM primes pr
            LEFT JOIN employes e on e.id = pr.employe_id
            LEFT JOIN sites st on st.idsite = pr.site_id
            LEFT JOIN users us on us.id = pr.user_id
            LEFT JOIN users sup on sup.id = pr.superviseur_id
            LEFT JOIN `status` stat on stat.name = pr.status
            WHERE pr.employe_id = ? 
            order by pr.created_at DESC", [$id]);

        $conges = DB::select("SELECT cg.id, cg.employe_id, cg.depart, cg.retour, cg.motif, cg.status, cg.created_at,
            cg.type_absence, cg.site_id, st.nom as 'site', us.name as 'user_nom', COALESCE(ur.email, us.email) as 'user_email',
            stat.description as 'status_description', stat.color as 'status_color', cg.user_id,
            e.nom as 'employe', e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi, e.date_embauche
            FROM absences cg
            LEFT JOIN employes e ON e.id = cg.employe_id
            LEFT JOIN sites st ON st.idsite = cg.site_id
            LEFT JOIN users us on us.id = cg.user_id
            LEFT JOIN users ur on ur.id = us.real_email_id
            LEFT JOIN `status` stat on stat.name = cg.status
            WHERE cg.type_absence = 'conge' and cg.employe_id = ?
            order by cg.created_at DESC", [$id]);
        $piece_jointes = DB::select("SELECT pj.id, pj.nature, pj.path, pj.user_id, pj.created_at, pj.updated_at
            FROM piece_jointes pj
            WHERE pj.employe_id = ?
            order by pj.created_at DESC", [$id]);
        return response()->json(compact('employe','sanctions','primes','conges','piece_jointes'));
    }

    public function soft_delete($id, Request $request){
        $employe = Employe::find($id);
        $absences = DB::select("SELECT a.id, a.employe_id FROM absences a WHERE status = 'done' and employe_id = ?
            and type_absence != 'mis_a_pied' and a.depart < now() and a.retour > now()", [$id]);
        if(count($absences) > 0)
            return response(["error" => "L'employé est en congé ou permission"]);
        if($employe && !$employe->soft_delete && in_array($request->user()->role, ['admin', 'rh', "resp_rh"])
        && $request->date_sortie && $request->date_sortie){
            $employe = Employe::find($id);
            $employe->soft_delete = 1;
            $employe->observation = $request->observation;
            $employe->date_sortie = $request->date_sortie;
            $employe->last_update = now();
            $employe->save();
            $request->note = "Date de sortie: " . (new \DateTime($request->date_sortie))->format('d-m-Y')
             . "\nObservation: " . $request->observation;
            HistoriqueController::action_employe($request, "Mise en archive", $id);
            return response()->json(["success" => "Employé archivé"]);
        }
        return response(["error" => "EACCES"]);
    }

    public function blacklist($id, Request $request){
        $employe = Employe::find($id);
        if($employe && $employe->soft_delete && in_array($request->user()->role, ['admin', 'rh', "resp_rh"])
        && $request->date && $request->observation){
            $employe = Employe::find($id);
            $employe->isBlacklist = 1;
            $employe->observation = $request->observation;
            $employe->date_sortie = $request->date;
            $employe->last_update = now();
            $employe->save();
            $request->note = "Date: " . (new \DateTime($request->date))->format('d-m-Y')
             . "\nObservation: " . $request->observation;
            HistoriqueController::action_employe($request, "Employé blacklister", $id);
            return response()->json(["success" => "Employé blacklister"]);
        }
        return response(["error" => "EACCES"]);
    }
    
    public function restore($id, Request $request){
        $employe = Employe::find($id);
        if($employe && $employe->soft_delete && $employe->isBlacklist == 0 && in_array($request->user()->role, ['admin', 'rh', 'resp_rh'])){
            $updateOk = Employe::where('id', $id)->update([
                'soft_delete' => null, 
                'last_update' => now()]);
            if($updateOk) {
                $employe = Employe::find($id);
                $reembauche = $employe->replicate();
                $reembauche->setTable('reembauches');
                $reembauche->created_at = now();
                $reembauche->save();
                HistoriqueController::action_employe($request, "Employé restauré", $id);
                return response()->json(["success" => "Employé restauré",]);
            }
        }
        return response(["error" => "EACCES"]);
    }
    
    public function notification(){
        $ids = array_column(DB::select("SELECT a.id, a.employe_id FROM absences a WHERE status = 'done' 
            and a.depart < now() and a.retour > now()"), "employe_id");
        $employes = DB::select("SELECT e.id, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.nom, e.last_date_pointage,
            (e.date_embauche is not null and TIMESTAMPDIFF(MONTH, e.date_embauche, now()) > 24) as confirmation,
            (e.last_date_pointage is null or TIMESTAMPDIFF(DAY, e.last_date_pointage, now()) > 45) as inactif
            FROM employes e
            WHERE (e.sal_forfait is null or e.sal_forfait = 0)
            and (e.soft_delete is null or e.soft_delete = 0)
            and (e.created_at is null or TIMESTAMPDIFF(DAY, e.created_at, now()) > 30)
            and (
                (
                    e.societe_id is not null and
                    e.date_embauche is not null and
                    e.numero_employe is null and 
                    e.num_emp_soit is null and 
                    TIMESTAMPDIFF(MONTH, e.date_embauche, now()) > 24
                )
                or
                (
                    e.last_date_pointage is null or 
                    TIMESTAMPDIFF(DAY, e.last_date_pointage, now()) > 30
                )
            ) " .
            (count($ids) > 0 ? " and e.id not in (" . join(", ", $ids) . ") " : "") . 
            " LIMIT 10");
        
        return response()->json(['now' => (new \DateTime())->format('Y-m-d'), 'employes' => $employes]);
    }
    
    public function site($id){
        $employes = DB::select("SELECT e.id, e.societe_id, e.numero_stagiaire, e.numero_employe, e.num_emp_soit, e.num_emp_saoi, e.nom FROM employes e 
            WHERE (e.sal_forfait = 0 or e.sal_forfait is null) and e.real_site_id = ?", [$id]);
        return response()->json(compact('employes'));
    }

    public function sanction(Request $request){
        if($request->done){
            $last_sanctions = DB::select("SELECT sanc.id, sanc.objet, sanc.motif, sanc.date_pointage, sanc.created_at, st.nom as 'site'
                FROM sanctions sanc
                left join sites st on st.idsite = sanc.site_id
                where sanc.status = 'done' && sanc.id != ? && sanc.employe_id = ?
                order by sanc.updated_at desc
                limit 10", [$request->employe_id, $request->employe_id]);
        }
        else {
            $last_sanctions = DB::select("SELECT sanc.id, sanc.status, sanc.objet, sanc.motif, sanc.date_pointage, sanc.created_at,
                stat.description as 'status_description', stat.color as 'status_color', st.nom as 'site'
                FROM sanctions sanc
                left join sites st on st.idsite = sanc.site_id
                LEFT JOIN `status` stat on stat.name = sanc.status
                where sanc.id != ? && sanc.employe_id = ?
                order by sanc.updated_at desc
                limit 10", [$request->employe_id, $request->employe_id]);
        }
        return response()->json($last_sanctions);
    }

    public function get_pv_not_done(Request $request){
        if(in_array($request->user()->role,['resp_rh', 'validateur'])){
            $now = new \DateTime();
            $date_paie = (new \DateTime())->setDate($now->format('Y'), $now->format('m'), 20);
            $dateLimit = (new \DateTime())->setDate($now->format('Y'), $now->format('m'), 23);
            $condition = '';
            if($request->employe_id)
                $condition = " AND emp.id = " . $request->employe_id . ' ';
         
            if($request->user_id || $request->not_found_resp_part){
                if($request->user_id)
                    $employe_id = User::find($request->user_id)->employe_id;
                elseif($request->not_found_resp_part)
                    $employe_id = $request->not_found_resp_part;
                //  !($now > $datePaie && $now < $dateLimit) ? 0 :
                $query = "SELECT emp.id as 'employe_id', emp.nom, emp.resp_part_id,
                    emp.societe_id, emp.numero_employe, emp.num_emp_soit, emp.numero_stagiaire,
                    emp.num_emp_saoi, resp.nom as 'responsable'
                    FROM employes emp
                    LEFT JOIN part_variables pv ON pv.employe_id = emp.id and pv.date_paie = ?
                    LEFT JOIN employes resp ON resp.id = emp.resp_part_id
                    WHERE (emp.soft_delete IS NULL OR emp.soft_delete = 0)
                    AND emp.part_variable = 1
                    AND emp.resp_part_id IS NOT NULL
                    AND pv.id IS NULL
                    AND (emp.soft_delete IS NULL OR emp.soft_delete = 0)
                    AND emp.resp_part_id = ". $employe_id . $condition;
                
                $pv_non_faits = DB::select( $query . " LIMIT ?, 30", [$date_paie->format('Y-m-d'), $request->offset]);
                if(count($pv_non_faits) > 0) {
                    $critere_part = DB::select("SELECT sum(montant) as 'montant', employe_id FROM critere_parts 
                        WHERE (soft_delete = 0 or soft_delete is null) 
                        AND employe_id in (". implode(",", array_column($pv_non_faits, "employe_id")) . ") group by employe_id", []);
                    foreach ($pv_non_faits as $pv) {
                        foreach ($critere_part as $cp) {
                            if ($cp->employe_id == $pv->employe_id) {
                                $pv->montant = $cp->montant;
                            }
                        }
                    }
                }
                return response()->json($pv_non_faits);
            }
       
            else{
                $query = "SELECT count(emp.id) as 'nb_pv', resp.nom as 'responsable', resp.id as 'resp_part_id',
                    u.name as 'responsable_nom', u.id as 'user_id', u.email as 'responsable_email'
                    FROM employes emp
                    LEFT JOIN part_variables pv ON pv.employe_id = emp.id and pv.date_paie = ?
                    LEFT JOIN employes resp ON resp.id = emp.resp_part_id
                    LEFT JOIN users u ON u.employe_id = emp.resp_part_id
                    WHERE (emp.soft_delete IS NULL OR emp.soft_delete = 0)
                    AND emp.part_variable = 1 
                    AND emp.resp_part_id IS NOT NULL 
                    AND pv.id IS NULL
                    AND (emp.soft_delete IS NULL OR emp.soft_delete = 0) " . $condition;
                
                $pv_non_faits = DB::select($query. " GROUP BY resp.id, u.name, u.id, u.email LIMIT ?, 30", [$date_paie->format('Y-m-d'), $request->offset]);
                return response()->json($pv_non_faits);
            }
        }
        return response(["error" => "EACCES"]);
    }

    public function show_pv_not_done(Request $request, $id){
        $now = new \DateTime();
        $datePaie = (new \DateTime())->setDate($now->format('Y'), $now->format('m'), 20);
        $pv_non_fait = DB::select("SELECT emp.id as 'employe_id', emp.nom as 'employe', emp.resp_part_id,
                emp.societe_id, emp.numero_employe, emp.num_emp_soit, emp.numero_stagiaire, emp.num_emp_saoi,
                resp.nom as 'responsable', f.libelle as 'fonction', agc.nom as 'agence', emp.titre
                FROM employes emp
                LEFT JOIN part_variables pv ON pv.employe_id = emp.id and pv.date_paie = ?
                LEFT JOIN employes resp ON resp.id = emp.resp_part_id
                LEFT JOIN fonctions f on f.id = emp.fonction_id
                LEFT JOIN agences agc on agc.id = emp.agence_id
                WHERE (emp.soft_delete IS NULL OR emp.soft_delete = 0)
                AND emp.part_variable = 1
                AND emp.resp_part_id IS NOT NULL
                AND pv.id IS NULL
                AND (emp.soft_delete IS NULL OR emp.soft_delete = 0)
                AND emp.id = ? ",
                [$datePaie->format('Y-m-d'), $id]
            )[0];
        if($pv_non_fait) {
            $critere_part = DB::select("SELECT employe_id, designation, montant FROM critere_parts 
                WHERE (soft_delete = 0 or soft_delete is null) 
                AND employe_id = ?", [$id]);
            $pv_non_fait->critere_parts = $critere_part;
        }
        return response()->json($pv_non_fait);
    }

    public function duplicatedName(Request $request, $id, $name) {
        $employes = Employe::where('nom', $name)
        ->where('id', '!=', $id)
        ->get();
        return response()->json($employes);
    }

    public function updateIgnoreName(Request $request) {
        if(is_array($request->doublons) && count($request->doublons) > 0) {
            foreach ($request->doublons as $doublon) {
                Employe::where('id', $doublon['id'])->update([
                    'ignore_name' => $doublon['ignore_name']
                ]);
            }
            return response()->json(['success' => 'ignore_name mis à jour']);
        } else {
            return response()->json(['error' => 'Aucun doublon à mettre à jour']);
        }
    }
}
