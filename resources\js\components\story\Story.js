import React from 'react';
import moment from 'moment';

export default function Story({trace}) {
    return <div className='story-container'>
        <div className='objet-story'>
            <h4>{trace.objet}</h4>
        </div>
        {
            trace.note && 
            <p className='story-note'>
                " {trace.note} "
            </p>
        }
        {
            trace.detail && 
            <p className='trace'>{trace.detail.split('\\n').map((d, index) => <span key={index}>{d}<br/></span>)}</p>
        }
        <div className='story-footer'>
            <span>{trace.user}</span>
            <span>{moment(trace.created_at).format("DD MMM YY à HH:mm")}</span>
        </div>
    </div>
}