const { sendReclamationNotification } = require('./send_reclamation_notification');

// Test function to verify the notification system
const testNotification = async () => {
    try {
        console.log('Testing reclamation notification system...');
        
        // You can replace this with an actual reclamation ID from your database
        const testReclamationId = process.argv[2];
        
        if (!testReclamationId) {
            console.error('Please provide a reclamation ID to test');
            console.error('Usage: node test_reclamation_notification.js <reclamation_id>');
            process.exit(1);
        }
        
        await sendReclamationNotification(testReclamationId);
        console.log('Test completed successfully!');
        
    } catch (error) {
        console.error('Test failed:', error);
        process.exit(1);
    }
};

// Run the test
testNotification();
