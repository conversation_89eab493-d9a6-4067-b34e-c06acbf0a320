import React from 'react';

class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        console.error(error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return <div id="content">
                <div>
                    <h1>Erreur</h1>
                    <p>Veuillez vider le cache.<br/>Si l'erreur persiste contacter les administrateurs : 032 12 379 97 / 032 12 379 98</p>
                    <div className='form-button-container'>
                        <button className='btn btn-primary' onClick={() => {
                            window.location.href = window.location.origin + window.location.pathname + '?no-cache=' + new Date().getTime();}}
                        >
                            Vider le cache
                        </button>
                    </div>
                </div>
            </div>
        }
        return this.props.children; 
    }
}

export default ErrorBoundary;