const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const {sendMail} = require("../auth")

moment.locale('fr')

const db_config = require("../auth").db_config_admin
const pool = mysql.createPool(db_config)

const isTask = (process.argv[2] == 'task')
const destination_vg = ["og<PERSON><PERSON><EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

const destination_test = ["<EMAIL>", "<EMAIL>"]

function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectLastSavExport = "SELECT value FROM params p WHERE p.key = 'last_sav_export'"

const sqlSelectStatus = "SELECT name, description FROM status order by ordre"

function sqlSelectSav(dateString){
    const begin = moment(dateString).subtract(7, "day").format("YYYY-MM-DD HH:mm:ss") + " 06:00:00"
    const end = dateString + " 06:00:00"
	return "SELECT sav.id, sav.site_id, sav.motif, sav.date_sav, sav.technicien, sav.created_at, sav.status, " +
        "s.nom as 'site', u.name as 'user_nom', u.email as 'user_email', h.note " +
        "from sav " +
        "left join sites s on s.idsite = sav.site_id " +
        "left join users u on u.id = sav.user_id " +
        "left join historiques h on h.id = sav.note_id " +
        "where (sav.status not in ('done', 'draft') " + 
        "or (sav.status in ('done', 'draft') and sav.updated_at > '" + begin +"' and sav.updated_at <= '" + end +"')) " +
        "order by sav.created_at"
}

function sqlUpdateLastSavExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_sav_export'"
}

function generateSavExcelFile(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
    const fontBold = {
        bold: true
    }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fontHeader = { size: 16, bold: true }

    data.forEach(stat => {
        const worksheet = workbook.addWorksheet(stat.description)
        worksheet.getColumn('A').width = 40
        worksheet.getColumn('B').width = 50
        if(stat.sav[0].date_sav){
            worksheet.getColumn('C').width = 20
            worksheet.getColumn('D').width = 20
            worksheet.getColumn('E').width = 40
            worksheet.getColumn('F').width = 20
            worksheet.getColumn('G').width = 50
        }
        else {
            worksheet.getColumn('C').width = 40
            worksheet.getColumn('D').width = 20
            worksheet.getColumn('E').width = 50
        }
        worksheet.getCell('A1').value = stat.description + " (" + stat.sav.length + ")"
        worksheet.getCell('A1').font = fontHeader
        worksheet.mergeCells('A1:G1')

        let line = 3
        
        worksheet.getCell('A' + line).value = "Site"
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).font = fontBold
        worksheet.getCell('B' + line).value = "Motif"
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).font = fontBold
        
        if(stat.sav[0].date_sav){
            worksheet.getCell('C' + line).value = "Date du SAV"
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('C' + line).alignment = alignmentStyle
            worksheet.getCell('C' + line).font = fontBold
            worksheet.getCell('D' + line).value = "Technicien"
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('D' + line).font = fontBold
            worksheet.getCell('E' + line).value = "Demandeur"
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('E' + line).font = fontBold
            worksheet.getCell('F' + line).value = "Créé"
            worksheet.getCell('F' + line).border = borderStyle
            worksheet.getCell('F' + line).font = fontBold
            worksheet.getCell('F' + line).alignment = alignmentStyle
            worksheet.getCell('G' + line).value = "Commentaire"
            worksheet.getCell('G' + line).border = borderStyle
            worksheet.getCell('G' + line).font = fontBold
        }
        else {
            worksheet.getCell('C' + line).value = "Demandeur"
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('C' + line).font = fontBold
            worksheet.getCell('D' + line).value = "Créé"
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('D' + line).font = fontBold
            worksheet.getCell('D' + line).alignment = alignmentStyle
            worksheet.getCell('E' + line).value = "Commentaire"
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('E' + line).font = fontBold
        }
        line++

        stat.sav.forEach(sav => {
            worksheet.getCell('A' + line).value = capitalizeFirstLetter(sav.site)
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = sav.motif
            worksheet.getCell('B' + line).border = borderStyle
            if(sav.date_sav){
                worksheet.getCell('C' + line).value = moment(sav.date_sav).format("DD-MM-YY HH:mm")
                worksheet.getCell('C' + line).border = borderStyle
                worksheet.getCell('C' + line).alignment = alignmentStyle
                worksheet.getCell('D' + line).value = sav.technicien
                worksheet.getCell('D' + line).border = borderStyle
                worksheet.getCell('E' + line).value = sav.user_nom + " <" + sav.user_email + ">"
                worksheet.getCell('E' + line).border = borderStyle
                worksheet.getCell('F' + line).value = moment(sav.created_at).format("DD-MM-YY HH:mm")
                worksheet.getCell('F' + line).border = borderStyle
                worksheet.getCell('F' + line).alignment = alignmentStyle
                worksheet.getCell('G' + line).value = sav.note
                worksheet.getCell('G' + line).border = borderStyle
            }
            else {
                worksheet.getCell('C' + line).value = sav.user_nom + " <" + sav.user_email + ">"
                worksheet.getCell('C' + line).border = borderStyle
                worksheet.getCell('D' + line).value = moment(sav.created_at).format("DD-MM-YY HH:mm")
                worksheet.getCell('D' + line).border = borderStyle
                worksheet.getCell('D' + line).alignment = alignmentStyle
                worksheet.getCell('E' + line).value = sav.note
                worksheet.getCell('E' + line).border = borderStyle
            }
            line++
        })
    })
}

function doSavExport(dateString){
	console.log("doSavExport")
    pool.query(sqlSelectStatus, [], async (err, status) => {
        if(err)
            console.error(err)
        else {
            pool.query(sqlSelectSav(dateString), [], async (err, sav) => {
                if(err)
                    console.error(err)
                else {
                    console.log("Nb sav: " + sav.length)
                    const savByStatus = []
                    status.map(stat => {
                        stat.sav = []
                        sav.map(sav => {
                            if(stat.name == sav.status)
                                stat.sav.push(sav)
                        })
                        if(stat.sav.length > 0){
                            savByStatus.push(stat)
                        }
                    })
                    const workbookSav = new Excel.Workbook()
                    const header = "SAV " + moment(dateString).format("DD MMMM YYYY")
                    generateSavExcelFile(workbookSav, header, savByStatus)
                    const savBuffer = await workbookSav.xlsx.writeBuffer()
                    sendMail(
                        pool,
                        isTask ? destination_vg : destination_test,
                        header, 
                        "Veuillez trouver ci-joint le rapport des demandes de SAV du " + moment(dateString).subtract(7, "days").format("DD MMMM YYYY") + " au " + moment(dateString).format("DD MMMM YYYY") + "<br/>"
                        + "<ul>"
                        + (savByStatus.map(stat => "<li>" + stat.description + ": " + stat.sav.length + "</li>").join(""))
                        + "</ul>"
                        ,
                        [
                            {
                                filename: header + ".xlsx",
                                content: savBuffer
                            },
                        ],
                        (response) => {
                            if(response && isTask){
                                pool.query(sqlUpdateLastSavExport(dateString), [], (e, r) =>{
                                    if(e)
                                        console.error(e)
                                    else
                                        console.log("update last diag export: " + r)
                                    process.exit(1)
                                })
                            }
                            else
                                process.exit(1)
                        },
                        isTask
                    )
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2])){
    console.log("send test...")
    doSavExport(process.argv[2])
}
else if(isTask){
    if(moment().day() == 3 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
        let date_vigilance = moment().format("YYYY-MM-DD")
        pool.query(sqlSelectLastSavExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result && result[0].value == date_vigilance) {
                console.log("export list sav already done!")
                process.exit(1)
            }
            else {
                console.log("exporting ...")
                doSavExport(date_vigilance)
            }
        })
    }
    else {
        console.log("Not wednesday, skip Sav")
    }
}
else
    console.log("please specify command!")