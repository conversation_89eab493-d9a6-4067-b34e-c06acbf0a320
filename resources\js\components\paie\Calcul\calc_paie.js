// calculPaie.js
import { calculIrsa } from "./calculIrsa";
import { prorata } from "./calculProrata";
export default function calcul_paie({
    heureTravaille, 
    heureContrat, 
    salBase, 
    maj, 
    conge, 
    nprv, 
    rajout, 
    deduction, 
    primeState, 
    preavis
}) {
    let divHcHt = heureTravaille / heureContrat;
    let diffHcHt = heureTravaille - heureContrat;
    let divSalBaseHc = salBase / heureContrat;
    let hsfr = 0;
    let newSalaireMensuel = 0;
    let newHs30 = 0;

    if (diffHcHt === -12) {
        newSalaireMensuel = salBase;
    } else if (heureTravaille > heureContrat) {
        hsfr = diffHcHt;
        newSalaireMensuel = salBase;
    } else {
        newSalaireMensuel = (salBase / heureContrat) * heureTravaille;
    }

    newHs30 = hsfr >= 33.6 ? 33.6 : hsfr;
    const newMh_s30 = divSalBaseHc * newHs30 * 0.3;
    const newHs50 = hsfr - newHs30;
    const newMh_s50 = divSalBaseHc * newHs50 * 0.5;

    const newMmaj_ferie = divSalBaseHc * maj.hFerie;
    const newMmaj_dim = divSalBaseHc * maj.hmDim * 0.4;
    const newMmaj = (maj.hMaj / heureContrat) * heureTravaille;

    let tot_maj = newMh_s30 + newMh_s50 + newMmaj_dim + newMmaj_ferie + newMmaj;

    const newCongePayer = (salBase / 30) * conge.soldeConge;
    const newPrDeductible = (salBase / 30) * nprv.nprvPreavisDeductible;
    const newPreavisPayer = (salBase / 30) * nprv.nprvPreavisPayer;
    const newIdmLicenciement = (salBase / 30) * nprv.nprvLicenciement;

    const newTotalProrataGrap =
        prorata(divHcHt, diffHcHt, primeState.primeExceptionnelle) +
        prorata(divHcHt, diffHcHt, primeState.primeDiv) +
        prorata(divHcHt, diffHcHt, primeState.idmDepl) +
        prorata(divHcHt, diffHcHt, primeState.primeAssid) +
        prorata(divHcHt, diffHcHt, primeState.primeResp) +
        prorata(divHcHt, diffHcHt, primeState.primeEntret) +
        prorata(divHcHt, diffHcHt, primeState.primeAnc);

    const newSalBrut =
        newSalaireMensuel +
        tot_maj +
        newTotalProrataGrap +
        rajout.rappel +
        newCongePayer -
        newPrDeductible +
        newPreavisPayer +
        newIdmLicenciement;

    const newCnaps = (newSalBrut / 100).toFixed(2);
    const newSalfa = (newSalBrut / 100).toFixed(2);

    const newNetImposable = (parseFloat(newSalBrut) - parseFloat(newSalfa) - parseFloat(newCnaps)).toFixed(2);
    const newIrsa = calculIrsa(newNetImposable);

    const newNetAPayer = (
        newNetImposable -
        newIrsa -
        deduction.retenueFormation -
        deduction.autreDeduction -
        deduction.avanceSpeciale -
        deduction.avance15 -
        preavis.preavisMoins +
        Number(rajout.allFamCnaps) +
        rajout.rembFraisFixe -
        deduction.avanceSpecialeEmbauche +
        primeState.prime +
        prorata(divHcHt, diffHcHt, primeState.perdiem) +
        primeState.partVariable
    ).toFixed(2);

    const newSalfaPatronale = ((Number(newNetAPayer) + Number(deduction.avance15)) * 0.05).toFixed(2);
    const newCnapsPatronale = ((Number(newNetAPayer) + Number(deduction.avance15)) * 0.13).toFixed(2);

    const newMasseSalariale =
        Number(newNetAPayer) +
        Number(newCnapsPatronale) +
        Number(newSalfaPatronale) +
        Number(newSalfa) +
        Number(newCnaps) +
        Number(newIrsa) +
        Number(deduction.avance15);

    return {
        salaireMensuel: newSalaireMensuel.toFixed(2),
        salaireBrut: newSalBrut.toFixed(2),
        netImposable: newNetImposable,
        netAPayer: newNetAPayer,
        masseSalariale: newMasseSalariale.toFixed(2),
        cnaps: newCnaps,
        salfa: newSalfa,
        irsa: newIrsa,
        salfaPatronale: newSalfaPatronale,
        cnapsPatronale: newCnapsPatronale,
        hs30: newHs30,
        hs50: newHs50,
        mh_s30: newMh_s30,
        mh_s50: newMh_s50,
        preavisDeductible: newPrDeductible.toFixed(2),
        preavisPayer: newPreavisPayer.toFixed(2),
        idmLicenciement: newIdmLicenciement.toFixed(2),
        congePayer: newCongePayer.toFixed(2),
        totalProrataGrap: newTotalProrataGrap.toFixed(2),
    };
}

// module.exports = calcul_paie;
