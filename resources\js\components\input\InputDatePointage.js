import React, { useState } from 'react';
import DatePicker, { registerLocale } from "react-datepicker";
import fr from "date-fns/locale/fr"; // the locale you want
registerLocale("fr", fr); // register it with the name you want

import "react-datepicker/dist/react-datepicker.css";
import moment from 'moment';

export default function InputDatePointage({ label, required, value, onChange, disabled, defaultHour, showHour=true }) {
    const [modalOpen, toggleModal] = useState(false)
    const [selectedDate, setDate] = useState(null)
    const [horaire, setHoraire] = useState(defaultHour ? defaultHour : "")

    const handleOk = (e) => {
        e.preventDefault()
        toggleModal(false)
        onChange(moment(selectedDate).format("YYYY-MM-DD") + " " + horaire)
    }
    const getCurrentDate = (value) => {
        const dateString = value ? (
            moment(value).format('ddd DD MMM YYYY')
            + (moment(value).format("HH:mm:ss") == "06:00:00" ? " JOUR" : " NUIT")
        ) : ''
        
        return dateString.charAt(0).toUpperCase() + dateString.slice(1)
    }
    return <div>
        <div className='input-container'>
            <label>{label} {required && <span className='danger'>*</span>}</label>
            <input value={getCurrentDate(value)} readOnly onClick={() => toggleModal(true)} type="text" disabled={disabled}/>
        </div>
        {
            modalOpen &&
            <div className='modal'>
                <div>
                    <h2>{label}</h2>
                    <div className='input-container'>
                        <label>Date <span className='danger'>*</span></label>
                        <DatePicker 
                            selected={selectedDate}
                            onChange={(date) => setDate(date)}
                            dateFormat="dd/MM/yyyy"
                            className='input-date'/>
                    </div>
                    {showHour &&
                        <div className='input-container'>
                            <label>Horaire <span className='danger'>*</span></label>
                            <select value={horaire} onChange={e => setHoraire(e.target.value)}>
                                <option></option>
                                <option value="06:00:00">Jour</option>
                                <option value="18:00:00">Nuit</option>
                            </select>
                        </div>
                    }
                    <div className='form-button-container'>
                        <button disabled={!(selectedDate && horaire)} className='btn-primary' onClick={handleOk}>OK</button>
                        <button onClick={() => toggleModal(false)}>Annuler</button>
                    </div>
                </div>
            </div>
        }
    </div>
}