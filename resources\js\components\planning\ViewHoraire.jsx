import React, { Component, useEffect, useState } from 'react';
import '../site/tab/horaire.css'
export default function ViewHoraire({horaires}){
    const header = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', '<PERSON>', 'Dim', '<PERSON><PERSON><PERSON><PERSON>'];
    const backgroundColor = ['indigo', 'orange', 'purple', 'green']
    const [numberWithColor, setNumberWithColor] = useState([])
    const hasAgent = horaires.day.some(hd => hd==1) || horaires.night.some(hn => hn==1);
    const [horaire, setHoraire] = useState({
            day: horaires?.day?.length > 0 ? horaires?.day : Array(8).fill(0),
            night: horaires?.night?.length > 0 ? horaires?.night : Array(8).fill(0)
    });

    const soustraireMultipleDe4 = (n) => {
        let multipleDe4 = Math.floor(n / 4) * 4;
        return n - multipleDe4;
    }

    const allUniqueNumbers = []

    const isInclude = (value) => {
        const tempNumberColor = []
        if(value > 0 && !allUniqueNumbers.includes(value)) {
            allUniqueNumbers.push(value)
            allUniqueNumbers.sort((a, b) => a - b);
        }
        if(allUniqueNumbers.length <= backgroundColor.length) {
            for(let i = 0; i < allUniqueNumbers.length; i++) {
                if(!tempNumberColor.find(n => n.value == allUniqueNumbers[i]))
                    tempNumberColor.push({color: backgroundColor[i], value: allUniqueNumbers[i]})
            }
            setNumberWithColor(tempNumberColor)
        }

        else if(allUniqueNumbers.length > backgroundColor.length) {
            for(let i = 0; i < allUniqueNumbers.length; i++) {
                if(!tempNumberColor.find(n => n.value == allUniqueNumbers[i])){
                    if(i < backgroundColor.length) {
                        tempNumberColor.push({color: backgroundColor[i], value: allUniqueNumbers[i]})
                    }
                    else {
                        tempNumberColor.push({color: backgroundColor[soustraireMultipleDe4(i)], value: allUniqueNumbers[i]})
                    }
                }

            }
            setNumberWithColor(tempNumberColor)
        }
    }

    useEffect(() => {
        horaire.day.map((h, i) => isInclude(h))
        horaire.night.map((h, i) => isInclude(h))
    }, [horaire])

    useEffect(() => {
        setHoraire({
            day: horaires?.day?.length > 0 ? horaires?.day : horaire.day,
            night: horaires?.night?.length > 0 ? horaires?.night : horaire.night
        })
    }, [horaires])

    return (
        <>
            <div className="card-container">
                
                <div className="calendar-header">
                    {header.map((hr) => (
                        <div key={hr} className="calendar-header-day">
                            {hr}
                        </div>
                    ))}
                </div>
                <div className="calendar-header">
                    {horaire.day.map((hr, index) => (
                        <div key={index} 
                            className={"horaire-content white"}
                            style={{ color: '#fafafa', fontWeight:'bold', fontSize:'1.7em'}}
                        >
                            {hr}
                        </div>
                    ))}
                </div>
                <div className="calendar-header">
                    {horaire.night.map((hr, index) => (
                        <div key={index} 
                            className={"horaire-content white"}
                            style={{ color: '#fafafa', fontWeight:'bold', fontSize:'1.7em'}}
                        >
                            {hr}
                        </div>
                    ))}
                </div>
            </div>
        </>
    )
}